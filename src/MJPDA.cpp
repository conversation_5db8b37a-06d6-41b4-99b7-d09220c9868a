#include <iostream>
#include <vector>
#include <cmath>
#include <ctime>
#include <cstdlib>

// 模拟参数
const int n = 3; // 目标数量
const int m_k = 5; // 有效回波个数
const double P_D = 0.8;
const double P_G = 0.7;
const double lambda_ = 0.2;

// 模拟协方差矩阵
std::vector<std::vector<double>> S = {
    {1, 0},
    {0, 1}
};

// 计算行列式
double determinant(const std::vector<std::vector<double>>& matrix) {
    return matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0];
}

// 随机生成在 [-1, 1] 范围内的新息（模拟，实际需根据具体情况计算）
std::vector<double> generateRandomInnovation() {
    return {static_cast<double>(rand()) / RAND_MAX * 2 - 1, static_cast<double>(rand()) / RAND_MAX * 2 - 1};
}

// 计算概率 P_j^t 和 P_0^t 的函数
void calculateProbabilities(std::vector<std::vector<double>>& w_matrix, std::vector<std::vector<double>>& P_j_t_matrix, std::vector<double>& P_0_t_matrix) {
    double b = lambda_ * std::sqrt(2 * M_PI * determinant(S)) * (1 - P_D * P_G) / P_D;
    std::vector<std::vector<double>> e_matrix(m_k, std::vector<double>(n));
    for (int j = 0; j < m_k; ++j) {
        for (int t = 0; t < n; ++t) {
            std::vector<double> v = generateRandomInnovation();
            double temp = 0.0;
            for (int i = 0; i < 2; ++i) {
                temp += v[i] * v[i] * S[i][i];
            }
            e_matrix[j][t] = std::exp(-0.5 * temp);
        }
    }
    for (int t = 0; t < n; ++t) {
        double e_sum = 0.0;
        for (int j = 0; j < m_k; ++j) {
            e_sum += e_matrix[j][t];
        }
        for (int j = 0; j < m_k; ++j) {
            P_j_t_matrix[j][t] = e_matrix[j][t] / (b + e_sum);
        }
        P_0_t_matrix[t] = b / (b + e_sum);
    }
}

// 确定公共回波集合和相关集合的函数
void determineCommonEchoes(std::vector<std::vector<double>>& w_matrix, std::vector<int>& Pub, std::vector<std::vector<int>>& T_j) {
    for (int j = 0; j < m_k; ++j) {
        int sum_over_t = 0;
        for (int t = 0; t < n; ++t) {
            sum_over_t += w_matrix[j][t];
        }
        if (sum_over_t > 1) {
            Pub.push_back(j);
        }
    }
    for (int j : Pub) {
        T_j[j].clear();
        for (int t = 0; t < n; ++t) {
            if (w_matrix[j][t] == 1) {
                T_j[j].push_back(t);
            }
        }
    }
}

// 对公共回波概率进行衰减和权值修正的函数
void attenuateAndCorrectProbabilities(std::vector<std::vector<double>>& P_j_t_matrix, std::vector<int>& Pub) {
    std::vector<std::vector<double>> P_j_t_prime_matrix = P_j_t_matrix;
    for (int j : Pub) {
        double sum_over_t_j = 0.0;
        for (int t_j = 0; t_j < n; ++t_j) {
            sum_over_t_j += P_j_t_matrix[j][t_j];
        }
        P_j_t_prime_matrix[j][0] = P_j_t_matrix[j][0] * P_j_t_matrix[j][0] / sum_over_t_j;
        for (int t = 1; t < n; ++t) {
            P_j_t_prime_matrix[j][t] = P_j_t_matrix[j][t] * P_j_t_matrix[j][t] / sum_over_t_j;
        }
    }
    std::vector<std::vector<double>> P_j_t_tilde_matrix = P_j_t_prime_matrix;
    for (int j : Pub) {
        double sum_over_j = 0.0;
        for (int j_prime = 0; j_prime < m_k; ++j_prime) {
            sum_over_j += P_j_t_prime_matrix[j_prime][0];
        }
        P_j_t_tilde_matrix[j][0] = P_j_t_prime_matrix[j][0] * P_j_t_prime_matrix[j][0] / sum_over_j;
        for (int t = 1; t < n; ++t) {
            P_j_t_tilde_matrix[j][t] = P_j_t_prime_matrix[j][t] * P_j_t_prime_matrix[j][t] / sum_over_j;
        }
    }
    P_j_t_matrix = P_j_t_tilde_matrix;
}

// 对修正后的概率进行归一化的函数
void normalizeProbabilities(std::vector<std::vector<double>>& P_j_t_matrix, std::vector<double>& beta_j_t, std::vector<int>& Pub) {
    for (int t = 0; t < n; ++t) {
        double sum_over_j = 0.0;
        for (int j = 0; j < m_k; ++j) {
            sum_over_j += P_j_t_matrix[j][t];
        }
        beta_j_t[t] = P_j_t_matrix[0][t] / sum_over_j;
        for (int j = 1; j < m_k; ++j) {
            beta_j_t[t] += P_j_t_matrix[j][t] / sum_over_j;
        }
    }
}

// 计算卡尔曼增益
std::vector<std::vector<double>> calculateKalmanGain(const std::vector<std::vector<double>>& P_j_t_matrix, const std::vector<std::vector<double>>& innovationCovariance) {
    std::vector<std::vector<double>> gain(n, std::vector<double>(m_k));
    for (int t = 0; t < n; ++t) {
        double temp1 = 0.0;
        for (int j = 0; j < m_k; ++j) {
            temp1 += P_j_t_matrix[j][t];
        }
        std::vector<double> temp2(2);
        for (int j = 0; j < m_k; ++j) {
            for (int i = 0; i < 2; ++i) {
                temp2[i] += P_j_t_matrix[j][t] * generateRandomInnovation()[i];
            }
        }
        for (int j = 0; j < m_k; ++j) {
            double temp3 = 0.0;
            for (int i = 0; i < 2; ++i) {
                temp3 += generateRandomInnovation()[i] * innovationCovariance[i][i];
            }
            for (int i = 0; i < 2; ++i) {
                gain[t][j] = P_j_t_matrix[j][t] * temp2[i] / (temp1 * temp3);
            }
        }
    }
    return gain;
}

int main() {
    srand(static_cast<unsigned int>(time(nullptr)));

    // 模拟确认矩阵 w_matrix
    std::vector<std::vector<double>> w_matrix(m_k, std::vector<double>(n));
    for (int j = 0; j < m_k; ++j) {
        for (int t = 0; t < n; ++t) {
            w_matrix[j][t] = static_cast<double>(rand() % 2);
        }
    }

    // P_j^t 和 P_0^t 的矩阵和向量
    std::vector<std::vector<double>> P_j_t_matrix(m_k, std::vector<double>(n));
    std::vector<double> P_0_t_matrix(n);

    // 公共回波集合
    std::vector<int> Pub;

    // T_j 的向量
    std::vector<std::vector<int>> T_j(m_k);

    // beta_j^t 的向量
    std::vector<double> beta_j_t(n);
    // 计算概率 P_j^t 和 P_0^t 的函数
    calculateProbabilities(w_matrix, P_j_t_matrix, P_0_t_matrix);
    // 确定公共回波集合和相关集合的函数
    determineCommonEchoes(w_matrix, Pub, T_j);
    // 对公共回波概率进行衰减和权值修正的函数
    attenuateAndCorrectProbabilities(P_j_t_matrix, Pub);
    // 对修正后的概率进行归一化的函数
    normalizeProbabilities(P_j_t_matrix, beta_j_t, Pub);

    // 假设的创新协方差矩阵
    std::vector<std::vector<double>> innovationCovariance = {
        {0.1, 0},
        {0, 0.1}
    };

    std::vector<std::vector<double>> kalmanGain = calculateKalmanGain(P_j_t_matrix, innovationCovariance);

    // 输出结果示例
    std::cout << "P_j^t Matrix:" << std::endl;
    for (int j = 0; j < m_k; ++j) {
        for (int t = 0; t < n; ++t) {
            std::cout << P_j_t_matrix[j][t] << " ";
        }
        std::cout << std::endl;
    }
    std::cout << "P_0^t Vector:" << std::endl;
    for (int t = 0; t < n; ++t) {
        std::cout << P_0_t_matrix[t] << " ";
    }
    std::cout << std::endl;
    std::cout << "Public Echoes: ";
    for (int j : Pub) {
        std::cout << j << " ";
    }
    std::cout << std::endl;
    std::cout << "Beta_j^t Vector:" << std::endl;
    for (int t = 0; t < n; ++t) {
        std::cout << beta_j_t[t] << " ";
    }
    std::cout << std::endl;
    std::cout << "Kalman Gain:" << std::endl;
    for (int t = 0; t < n; ++t) {
        for (int j = 0; j < m_k; ++j) {
            std::cout << kalmanGain[t][j] << " ";
        }
        std::cout << std::endl;
    }

    return 0;
}