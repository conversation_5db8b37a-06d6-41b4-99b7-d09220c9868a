/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-04-16 14:57:15
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-05-09 17:39:57
 * @FilePath     : fusionObjects.h
 * Copyright 2024 vanjee, All Rights Reserved. 
 * 2024-04-16 14:57:15
 */

#ifndef __FUSIONOBJECTS__H__
#define __FUSIONOBJECTS__H__

#include <iostream>

#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"


using namespace std;

class FusionObject
{
private:
    

    

public:
    static int m_objectCount;
    int m_id;
    float m_x;                    
    float m_y;                     
    float m_z;
    float m_length;
    float m_width;
    float m_height;
    // float m_longtitude             #经度
    // float m_latitude               #纬度
    // float m_altitude               #高度
    // float m_relspeedy              #纵轴相对速度
    // float m_relspeedx              #横轴相对速度
    // float m_rollRad                #横滚角 rad   
    // float m_pitchRad               #俯仰角 rad        
    float m_yawRad;                //航向角 rad
    // float m_pitchrate              #deg/s
    // float m_rollrate               #deg/s
    // float m_yawrate                #deg/s
    
    // uint8 m_m_m_classification         #类别
    // uint8 m_m_m_value                  #Cluster 版本用于速度来源-radar 
    // float m_confidence             #检测置信度
    // point3d[] points               #轮廓点数据 
    // uint8 m_m_drivingIntent           #驾驶意图：0-初始，1-切入
    // uint8 m_m_behavior_state          # FORWARD_STATE = 0, STOPPING_STATE = 1, BRANCH_LEFT_STATE = 2, BRANCH_RIGHT_STATE = 3, YIELDING_STATE = 4, ACCELERATING_STATE = 5, SLOWDOWN_STATE = 6
    // uint8 m_m_radarIndex              #相对速度来源
    // uint8 m_m_radarObjectID           #radar跟踪目标ID
    // float m_s                      #frenet坐标系的s
    // float m_l                      #frenet坐标系的l
    // float m_speeds                 #frenet坐标系的s方向速度
    // float m_speedl                 #frenet坐标系的l方向速度
    // uint8 m_m_m_objectDecision         #障碍物相关决策
    // objecthistory[] objectHistory  #历史轨迹信息
    // objectprediction[] objectPrediction #预测信息
    int m_sensorType;
    common_msgs::sensorobject m_lidarObject;
    common_msgs::sensorobject m_cameraObject;
    int m_matchedLidarID;
    int m_matchedCameraID;
    int m_unUpdateCount;

    FusionObject(/* args */);
    ~FusionObject();

    void setFusionObject(const common_msgs::sensorobject& object);
    void updateAssignedObjects(const common_msgs::sensorobject& object);
    void updateUnassignedObjects();
    int generateNewTrackId();
};



#endif /* __FUSIONOBJECTS__H__ */
