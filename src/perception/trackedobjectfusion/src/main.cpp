/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-04-12 08:46:49
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-06-14 09:17:50
 * @FilePath     : main.cpp
 * Copyright 2024 <PERSON><PERSON>, All Rights Reserved. 
 * 2024-04-12 08:46:49
 */
#include "fusionNode.h"
#include <glog/logging.h>
int main(int argc, char **argv)
{
    google::InitGoogleLogging(argv[0]);
    ros::init(argc, argv, "fusionNode");
    std::string versionInfo = "20240614-v1.2.1";
    ros::param::set("/version/fusionNode",versionInfo);
    // 设置日志输出目录
    FLAGS_log_dir = "./logs";
    // 标准错误输出
    FLAGS_logtostderr = true;
    LOG(INFO) << "fusionNode version: " + versionInfo;
    
    ros::NodeHandle nh;
    FusionNode fusionNode(nh);
    fusionNode.run();

    google::ShutdownGoogleLogging();
    return 0;
}