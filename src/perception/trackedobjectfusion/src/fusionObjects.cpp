/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-04-16 14:58:04
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-05-14 11:19:56
 * @FilePath     : fusionObjects.cpp
 * Copyright 2024 vanjee, All Rights Reserved. 
 * 2024-04-16 14:58:04
 */
#include "fusionObjects.h"

int FusionObject::m_objectCount = 1;
FusionObject::FusionObject(/* args */)
{
}

FusionObject::~FusionObject()
{
}

void FusionObject::setFusionObject(const common_msgs::sensorobject& object)
{
    m_id = generateNewTrackId();
    // m_lidarObject = object;
    // m_matchedLidarID = object.id;
    m_unUpdateCount = 0;
    m_x = object.x;
    m_y = object.y;
    m_z = object.z;
    m_length = object.length;
    m_width = object.width;
    m_height = object.height;
    m_yawRad = object.azimuth;
}

void FusionObject::updateAssignedObjects(const common_msgs::sensorobject& object){
  m_unUpdateCount = 0;
  m_x = object.x;
  m_y = object.y;
  m_z = object.z;
  m_length = object.length;
  m_width = object.width;
  m_height = object.height;
  m_yawRad = object.azimuth;


}

void FusionObject::updateUnassignedObjects(){
  ++m_unUpdateCount;
}


int FusionObject::generateNewTrackId() {
  int ret_track_id = static_cast<int>(m_objectCount);
  if (m_objectCount == std::numeric_limits<unsigned int>::max()) {
    m_objectCount = 1;
  } else {
    m_objectCount++;
  }
  return ret_track_id;
}