/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-04-11 17:10:53
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-05-14 11:14:18
 * @FilePath     : fusionNode.h
 * Copyright 2024 <PERSON><PERSON>, All Rights Reserved. 
 * 2024-04-11 17:10:53
 */

#include <iostream>
#include <map>
#include <set>
#include <glog/logging.h>
#include "ros/ros.h"
#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"
#include "../../commonlibrary/src/common.h"

#include "../../commonlibrary/src/sensorobjects/lidarobjects.h"
#include "../../commonlibrary/src/sensorobjects/cameraobjects.h"

#include "../../commonlibrary/src/computeAssignmentMatrix.h"
#include "../../commonlibrary/src/Hungarian.h"
#include "../../commonlibrary/src/visualize/sensorobjectsviewer.h"
#include "fusionObjects.h"

using namespace std;

class FusionNode
{
private:
    ComputeAssignmentMatrix m_cComputeAssignmentMatrix;
    HungarianAlgorithm m_cHungarianAlgorithm;
    Common m_cCommon;

    using c_cLidarObjects = SENSOROBJECTS::LidarObjects;
    using c_cCameraObjects = SENSOROBJECTS::CameraObjects<common_msgs::sensorobjects>;
    using c_cSenObjectsViewer = VISUALIZATION::SensorObjectsViewer;

    boost::shared_ptr<c_cLidarObjects> m_pLidarObjects;
    boost::shared_ptr<c_cCameraObjects> m_pCameraObjects;
    std::vector<boost::shared_ptr<FusionObject>> m_vpFusionObjects;
    boost::shared_ptr<c_cSenObjectsViewer> m_pcSenObjectsViewer;

    ros::NodeHandle m_nh;
    ros::Publisher m_pub_fusionTrackedObjects;
    vector<vector<int>> m_matchedPairs;
    set<int> m_unmatchedDetections;
    set<int> m_unmatchedTrajectories;
    double m_currentTimestamp;
    int m_sensorType;
    int m_fusionState;
    

public:
    enum FusionState{
        Init = 1,
        Lidar = 2,
        Camera = 3,
        LidarCamera = 4,
        CameraLidar = 5
    };


    FusionNode(ros::NodeHandle& nodeHandle);
    ~FusionNode();

    void run();
    void process();
    void transLidarObjects2FusionObjects(const common_msgs::sensorobjects& objects);
    void fuseLidarTrackedObjects(const common_msgs::sensorobjects& lidarObjects, const int& sensorType);
    void IDMatch(const common_msgs::sensorobjects& sensorObjects,
            std::vector<std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>>& vMatachedFusionSensorObjects,
            std::vector<boost::shared_ptr<FusionObject>>& vpUnmatchedFusionObjects,
            std::vector<common_msgs::sensorobject>& vUnmatchedDetections);
    void distanceMatch(const common_msgs::sensorobjects& sensorObjects,
        std::vector<std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>>& vMatachedFusionSensorObjects,
        std::vector<boost::shared_ptr<FusionObject>>& vpUnmatchedFusionObjects,
        std::vector<common_msgs::sensorobject>& vUnmatchedDetections);
    void UpdateAssignedTracks(std::vector<std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>> m_vMatachedFusionSensorObjects, const int& sensorType);
    void UpdateUnassignedTracks(std::vector<boost::shared_ptr<FusionObject>>& vpUnmatchedFusionObjects);
    void CreateNewTracks(const std::vector<common_msgs::sensorobject>& vUnmatchedDetections, const int& sensorType);
    void RemoveLostTrack();
    void fuseCameraTrackedObjects(const common_msgs::sensorobjects& cameraObjects);
    void transFusionObjects2LidarObjects(common_msgs::sensorobjects& objects, const int& sensorType);
    void objectsVisualizePublish();

};

