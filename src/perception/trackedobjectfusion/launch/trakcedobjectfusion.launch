<launch>
    <!--   launch-prefix="gdb -ex run -args"      -->
    <!--  launch-prefix="valgrind -log-file=/media/wanji/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/valReport -leak-check=full -show-reachable=yes -leak-resolution=low"  -->
    <!--          launch-prefix="valgrind &#45;&#45;tool=callgrind &#45;&#45;callgrind-out-file=/media/wanji/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/callgrind.out"-->
    <node name="trackedobjectfusion_node" pkg="trackedobjectfusion" type="trackedobjectfusion_node" output="screen" required="true"     
        launch-prefix="gdb -ex run -args"
    />

</launch>