Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /detection1
        - /fusion1
        - /tracking1
        - /v2i1
        - /collsionPath1
      Splitter Ratio: 0.8294117450714111
    Tree Height: 853
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Name: Time
    SyncMode: 0
    SyncSource: lidarpointsrviz
  - Class: rviz/Displays
    Help Height: 75
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /detection1
        - /fusion1
        - /tracking1
        - /tracking1/trk_bounding_boxes1
      Splitter Ratio: 0.5475504398345947
    Tree Height: 366
  - Class: rviz/Selection
    Name: Selection
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 70
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 100
      Reference Frame: <Fixed Frame>
      Value: true
    - Alpha: 1
      Class: rviz/Axes
      Enabled: true
      Length: 1.7999999523162842
      Name: Axes
      Radius: 0.5
      Reference Frame: <Fixed Frame>
      Show Trail: false
      Value: true
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /lidarobjectsrviz
          Name: lidarobjectsrviz
          Namespaces:
            basic_shapes: true
            clustering: true
          Queue Size: 100
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: lidarpointsrviz
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 1
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /lidarpointsrviz
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /image_detect_3dresults_rviz
          Name: image_detect_3dresults_rviz
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/Image
          Enabled: false
          Image Topic: /usbcamera0
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: usbcamera0
          Normalize Range: true
          Queue Size: 2
          Transport Hint: compressed
          Unreliable: false
          Value: false
        - Class: rviz/Image
          Enabled: false
          Image Topic: /wj_camera_0
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: wj_camera_0
          Normalize Range: true
          Queue Size: 2
          Transport Hint: compressed
          Unreliable: false
          Value: false
        - Class: rviz/Image
          Enabled: false
          Image Topic: /wj_camera_3
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: wj_camera_3
          Normalize Range: true
          Queue Size: 2
          Transport Hint: compressed
          Unreliable: false
          Value: false
        - Class: rviz/Image
          Enabled: false
          Image Topic: /image_detect_results
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: image_detect_results
          Normalize Range: true
          Queue Size: 2
          Transport Hint: compressed
          Unreliable: false
          Value: false
      Enabled: true
      Name: detection
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/lidarObjectBBX
          Name: lidarObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/radarObjectBBX
          Name: radarObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: sensorradarCloud
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 10
          Size (m): 0.5
          Style: Points
          Topic: /objectfusion/sensorradarCloud
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/fusionObjectBBX
          Name: fusionObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /objectfusion/objectfusionMarkerArray
          Name: /objectfusionMarkerArray
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: true
      Name: fusion
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/trackingObjectBBX
          Name: trk_bounding_boxes
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /objectTrack/trk_trajectory_points
          Name: trk_trajectory_points
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/cloudObjectsBBX
          Name: cloudObjectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /OBU_objectsBBX
          Name: OBU_objectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /fusiontracking/v2iFusionObjectBBX
          Name: v2iFusionObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /CAMERA/CAMERA_objectsBBX
          Name: CAMERA_objectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: true
      Enabled: true
      Name: tracking
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /v2ifusion/trackingObjectBBX
          Name: trackingObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /v2ifusion/obuObjectBBX
          Name: obuObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /v2ifusion/fusionObjectBBX
          Name: fusionObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: true
      Name: v2i
    - Class: rviz/Image
      Enabled: false
      Image Topic: /wj_camera_4
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Image
      Normalize Range: true
      Queue Size: 2
      Transport Hint: compressed
      Unreliable: false
      Value: false
    - Class: rviz/Image
      Enabled: false
      Image Topic: /wj_camera_4
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Image
      Normalize Range: true
      Queue Size: 2
      Transport Hint: compressed
      Unreliable: false
      Value: false
    - Class: rviz/Group
      Displays:
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: collsionPath0
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /collsionPath0
          Unreliable: false
          Value: true
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: collsionPath1
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /collsionPath1
          Unreliable: false
          Value: true
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: collsionPath2
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /collsionPath2
          Unreliable: false
          Value: true
      Enabled: true
      Name: collsionPath
    - Alpha: 1
      Buffer Length: 1
      Class: rviz/Path
      Color: 25; 255; 0
      Enabled: false
      Head Diameter: 0.30000001192092896
      Head Length: 0.20000000298023224
      Length: 0.30000001192092896
      Line Style: Lines
      Line Width: 0.029999999329447746
      Name: Path
      Offset:
        X: 0
        Y: 0
        Z: 0
      Pose Color: 255; 85; 255
      Pose Style: None
      Queue Size: 10
      Radius: 0.029999999329447746
      Shaft Diameter: 0.10000000149011612
      Shaft Length: 0.10000000149011612
      Topic: /collsionPath5
      Unreliable: false
      Value: false
    - Alpha: 1
      Buffer Length: 1
      Class: rviz/Path
      Color: 25; 255; 0
      Enabled: false
      Head Diameter: 0.30000001192092896
      Head Length: 0.20000000298023224
      Length: 0.30000001192092896
      Line Style: Lines
      Line Width: 0.029999999329447746
      Name: Path
      Offset:
        X: 0
        Y: 0
        Z: 0
      Pose Color: 255; 85; 255
      Pose Style: None
      Queue Size: 10
      Radius: 0.029999999329447746
      Shaft Diameter: 0.10000000149011612
      Shaft Length: 0.10000000149011612
      Topic: /collsionPath4
      Unreliable: false
      Value: false
    - Alpha: 1
      Buffer Length: 1
      Class: rviz/Path
      Color: 25; 255; 0
      Enabled: false
      Head Diameter: 0.30000001192092896
      Head Length: 0.20000000298023224
      Length: 0.30000001192092896
      Line Style: Lines
      Line Width: 0.029999999329447746
      Name: Path
      Offset:
        X: 0
        Y: 0
        Z: 0
      Pose Color: 255; 85; 255
      Pose Style: None
      Queue Size: 10
      Radius: 0.029999999329447746
      Shaft Diameter: 0.10000000149011612
      Shaft Length: 0.10000000149011612
      Topic: /collsionPath3
      Unreliable: false
      Value: false
    - Alpha: 1
      Buffer Length: 1
      Class: rviz/Path
      Color: 25; 255; 0
      Enabled: false
      Head Diameter: 0.30000001192092896
      Head Length: 0.20000000298023224
      Length: 0.30000001192092896
      Line Style: Lines
      Line Width: 0.029999999329447746
      Name: Path
      Offset:
        X: 0
        Y: 0
        Z: 0
      Pose Color: 255; 85; 255
      Pose Style: None
      Queue Size: 10
      Radius: 0.029999999329447746
      Shaft Diameter: 0.10000000149011612
      Shaft Length: 0.10000000149011612
      Topic: /collsionPath6
      Unreliable: false
      Value: false
    - Alpha: 1
      Buffer Length: 1
      Class: rviz/Path
      Color: 25; 255; 0
      Enabled: false
      Head Diameter: 0.30000001192092896
      Head Length: 0.20000000298023224
      Length: 0.30000001192092896
      Line Style: Lines
      Line Width: 0.029999999329447746
      Name: Path
      Offset:
        X: 0
        Y: 0
        Z: 0
      Pose Color: 255; 85; 255
      Pose Style: None
      Queue Size: 10
      Radius: 0.029999999329447746
      Shaft Diameter: 0.10000000149011612
      Shaft Length: 0.10000000149011612
      Topic: /collsionPath7
      Unreliable: false
      Value: false
    - Alpha: 1
      Buffer Length: 1
      Class: rviz/Path
      Color: 25; 255; 0
      Enabled: false
      Head Diameter: 0.30000001192092896
      Head Length: 0.20000000298023224
      Length: 0.30000001192092896
      Line Style: Lines
      Line Width: 0.029999999329447746
      Name: Path
      Offset:
        X: 0
        Y: 0
        Z: 0
      Pose Color: 255; 85; 255
      Pose Style: None
      Queue Size: 10
      Radius: 0.029999999329447746
      Shaft Diameter: 0.10000000149011612
      Shaft Length: 0.10000000149011612
      Topic: /collsionPath8
      Unreliable: false
      Value: false
    - Alpha: 1
      Buffer Length: 1
      Class: rviz/Path
      Color: 25; 255; 0
      Enabled: false
      Head Diameter: 0.30000001192092896
      Head Length: 0.20000000298023224
      Length: 0.30000001192092896
      Line Style: Lines
      Line Width: 0.029999999329447746
      Name: Path
      Offset:
        X: 0
        Y: 0
        Z: 0
      Pose Color: 255; 85; 255
      Pose Style: None
      Queue Size: 10
      Radius: 0.029999999329447746
      Shaft Diameter: 0.10000000149011612
      Shaft Length: 0.10000000149011612
      Topic: /collsionPath9
      Unreliable: false
      Value: false
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Default Light: true
    Fixed Frame: car
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Angle: -0.009998876601457596
      Class: rviz/TopDownOrtho
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Scale: 20.799381256103516
      Target Frame: <Fixed Frame>
      X: 0.35572052001953125
      Y: 6.607615947723389
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 965
  Hide Left Dock: false
  Hide Right Dock: true
  Image:
    collapsed: false
  QMainWindow State: 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
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 1070
  X: 850
  Y: 27
  image_detect_results:
    collapsed: false
  usbcamera0:
    collapsed: false
  wj_camera_0:
    collapsed: false
  wj_camera_3:
    collapsed: false
