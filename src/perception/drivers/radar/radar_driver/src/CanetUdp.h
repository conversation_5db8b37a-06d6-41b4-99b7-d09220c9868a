

/*
 * Copyright (c) 2021-2023 VANJEE Technology.
 * website: 
 * Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * 工具类：主要用于 udp 数据接收和发送。 
 * * *************************************************************************
 * */


#pragma once

#include <iostream>
#include <vector>
#include<string.h>

#include <stdio.h>
#include <errno.h>
#include <stdlib.h>
#include <ctype.h>
#include <stdio.h>  
#include <sys/types.h>

#include <errno.h>
#include <fcntl.h>

#include<unistd.h>
#include<arpa/inet.h>
#include <sys/socket.h>
#include<netinet/in.h>
#include <pthread.h>
typedef int SOCKET;
typedef unsigned char UINT8;
typedef unsigned int UINT32;

using namespace std;



#define NONBLOCK 0



class CanetUdp
{
public:
/*************************************************
Function:		// CanetUdp
Description: 		// 构造函数,初始化
Calls: 			// 无
Called By: 		// main.cpp中的main函数
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// char* hostIp (主机IP地址), ,int hostport(主机的端口号),char* canetIp(canet的IP地址),int canetport(canet的端口号)	
Output: 			// 无
Return: 			// 无
Others: 			// 无
Run frequency： //调用一次
*************************************************/
CanetUdp(string hostIp,int hostport,string canetIp,int canetport)
{
	memset(ReceBuf, 0, sizeof(char)*1024);
	memset(SendBuf, 0, sizeof(char)*13);

	NumofRecBuf = 0;

	//CanetUdp::prt=prt;
	sockaddr_in hostNett = CanetUdp::hostNet;
	sockaddr_in canett = CanetUdp::canet;

	//bzero(&hostNett,sizeof(struct sockaddr_in));
	hostNet.sin_family=AF_INET;
	inet_pton(AF_INET,hostIp.c_str(),&hostNet.sin_addr);     
	hostNet.sin_port=htons(hostport);


	//bzero(&canett,sizeof(struct sockaddr_in));
	canet.sin_family=AF_INET;
	inet_pton(AF_INET,canetIp.c_str(),&canet.sin_addr); 
	canet.sin_port=htons(canetport);
	printf("Parameters were set\n");

	cout << "CanetUdp is built." << endl;
}
/*************************************************
Function:		// ~CanetUdp
Description: 		// 析构函数,释放资源
Calls: 			// 无
Called By: 		// 无
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// 无	
Output: 			// 无
Return: 			// 无
Others: 			// 无
Run frequency： //调用一次
*************************************************/	 
~CanetUdp()
{
	cout << "CanetUdp is off." << endl;
	close(sockfd); 
}

public:
    SOCKET sockfd;
    struct  sockaddr_in hostNet;
    struct  sockaddr_in canet;

	UINT8 ReceBuf[1024]; // receive buffer
	UINT8 SendBuf[13];   // send buffer

public:
	int NumofRecBuf;


public:

/*************************************************
Function:		// socketBuild
Description: 		// 绑定UDP的IP 和 PORT
Calls: 			// 无
Called By: 		// Run
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// 无
Output: 			// 无
Return: 			// int  (固定返回值0)
Others: 			// 无
Run frequency： //调用一次
*************************************************/
int socketBuild(void)
{
	sockaddr_in hostNett = hostNet;

	CanetUdp::sockfd = socket(AF_INET,SOCK_DGRAM,IPPROTO_UDP);
	#if NONBLOCK
	SetNonBlocking(sockfd);
	#endif

	if(::bind(CanetUdp::sockfd,(struct sockaddr *)&hostNett,sizeof(struct sockaddr_in)) != 0)
	{
		printf("Bind fail!\n");

		exit(1);
	}
	printf("socket was built\n");


	return 0;
			
}
/*************************************************
Function:		// closeUdpCanet
Description: 		// 关闭socket
Calls: 			// 无
Called By: 		// 无
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// 无
Output: 			// 无
Return: 			// 无
Others: 			// 无
Run frequency： //调用一次
*************************************************/
void closeUdpCanet(void)
{
	close(sockfd);		
}
   //////////////////////////////CANET 收发 //////////////////////////
/*************************************************
Function:		// udpSendToCanet
Description: 		// UDP形式发送数据
Calls: 			// 无
Called By: 		// SendCarInfoKernel
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// (SendBuf待发送的字节数据,13个字节)
Output: 			// 无
Return: 			// int  如果数据发送成功返回0,否则返回-1
Others: 			// 无
Run frequency： //20hz
*************************************************/
int udpSendToCanet(void)
{
	sockaddr_in canett = canet;

	if (sendto(sockfd,(const char*)SendBuf,13,0,(struct sockaddr *)&canett,sizeof(struct sockaddr_in)) < 0)
	{    
		printf("Send Error\n");
		return -1;
	}
	return 0; 		
}
/*************************************************
Function:		// udpRecvFromCanet
Description: 		// UDP形式接收数据
Calls: 			// 无
Called By: 		// RecvCarInfoKernel
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// 无
Output: 			// (char*)ReceBuf
Return: 			// int  接收到数据的个数
Others: 			// 无
Run frequency： //200hz
*************************************************/
int udpRecvFromCanet(void)
{
	sockaddr_in canett = canet;
	socklen_t addrlen = sizeof(sockaddr_in);	
	NumofRecBuf = 0;
	NumofRecBuf =  recvfrom(sockfd, (char*)ReceBuf, 1024, 0, (struct sockaddr *)&canett, &addrlen);

	if (NumofRecBuf <= 0)
	{
	//printf("Receive Error\n");
	return -1;
	}
	// usleep(50000);
	return NumofRecBuf;		
}


	//////////////////////////////网络 收发 //////////////////////////
/*************************************************
Function:		// udpSendToCanet
Description: 		// UDP形式发送数据
Calls: 			// 无
Called By: 		// SendCarInfoKernel
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// (unsigned char *buf (待发送的字节数据), int len(字节个数))
Output: 			// 无
Return: 			// int  如果数据发送成功返回0,否则返回-1
Others: 			// 无
Run frequency： //20hz
*************************************************/
int udpSendToCanet(const char *buf, int len)
{
	sockaddr_in canett = canet;
    //UDP发送
	int length = sendto(sockfd,(const char*)buf,len,0,(struct sockaddr *)&canett,sizeof(struct sockaddr_in));

	if ( length < 0)
	{    
		//printf("Send Error\n");
		return -1;
	}
	else
	{
		// printf("send ok\n");
	}
	return length; 
}

int send_data(unsigned char buf[], int len)
{
	sockaddr_in canett = canet;

	int length = sendto(sockfd,(const char*)buf,len,0,(struct sockaddr *)&canett,sizeof(struct sockaddr_in));

	if ( length < 0)
	{    
		//printf("Send Error\n");
		return -1;
	}
	else
	{
		// printf("send ok\n");
	}
	return length; 
}


/*************************************************
Function:		// udpRecvFromCanet
Description: 		// UDP形式接收数据
Calls: 			// 无
Called By: 		// RecvCarInfoKernel
Table Accessed: 	// 无
Table Updated: 	// 无
Input: 			// unsigned char*buf 待收到的字节数组,int maxsize待接收的字节个数
Output: 			// 无
Return: 			// int  接收到数据的个数
Others: 			// 无
Run frequency： //200hz
*************************************************/
int udpRecvFromCanet(unsigned char*buf,int maxsize)
{
	sockaddr_in canett = canet;
	socklen_t addrlen = sizeof(sockaddr_in);	
	NumofRecBuf = 0;
	NumofRecBuf =  recvfrom(sockfd, buf, maxsize, 0, (struct sockaddr *)&canett, &addrlen);

	if (NumofRecBuf <= 0)
	{
	//printf("Receive Error\n");
	return -1;
	}
	// usleep(50000);
	return NumofRecBuf;
}

int receive_data(unsigned char*buf,int maxsize=1300)
{
	sockaddr_in canett = canet;
	socklen_t addrlen = sizeof(sockaddr_in);	
	NumofRecBuf = 0;
	//udp 接收
	NumofRecBuf =  recvfrom(sockfd, buf, maxsize, 0, (struct sockaddr *)&canett, &addrlen);

	if (NumofRecBuf <= 0)
	{
	//printf("Receive Error\n");
	return -1;
	}
	// usleep(50000);
	return NumofRecBuf;
}



/*************************************************
Function:		// SetNonBlocking
Description: 	// 设置非阻塞模式
Calls: 			// 无
Called By: 		// socketBuild
Table Accessed: // 无
Table Updated: 	// 无
Input: 			// int sockfd (句柄)
Output: 		// 无
Return: 		// 无
Others: 		// 无
Run frequency： //调用一次
*************************************************/
void SetNonBlocking(int sockfd) 
{
	int flag = fcntl(sockfd, F_GETFL, 0);
	if (flag < 0) {
		Perror("fcntl F_GETFL fail");
		return;
	}
	if (fcntl(sockfd, F_SETFL, flag | O_NONBLOCK) < 0) 
	{
		Perror("fcntl F_SETFL fail");
	}
}


void Perror(const char *s)
{
	perror(s);
	exit(EXIT_FAILURE);
}
	

};





