/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	radarparser.cpp
Description: 毫米波雷达数据解析程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/


    #include"radarparser.h"

	/*************************************************
	Function:       SensorRadar_ARS
	Description:    构造函数，主要用于数据初始化
	Input:          handle：ros句柄
	Output:         无
	Return:         无
	*************************************************/ 
    SensorRadar_ARS::SensorRadar_ARS()
	{
		nof_objects = 0;
		last_nof_objects = 0;
		last_objectid = 0;


		for (int k = 0; k < MaxTarNum; ++k)
        {
			RadarRecData[k].is_update = 0;
		}
	}
	/*************************************************
	Function:       setCaliInfo
	Description:    设置IP和端口号
	Input:          pCaliInfo：雷达标定信息指针
	Output:         无
	Return:         无
	*************************************************/
	void SensorRadar_ARS::SetCaliInfo(int DeltaAngle, int DeltaX, int DeltaY,int RadarType,int RadarUpdown)
	{
        this->m_RadarCaliInfo.DeltaAngle = DeltaAngle; //0.01
        this->m_RadarCaliInfo.DeltaX = DeltaX;         //
        this->m_RadarCaliInfo.DeltaY = DeltaY;         //cm
        this->m_RadarCaliInfo.RadarType = RadarType;	
		this->m_RadarCaliInfo.RadarUpdown = RadarUpdown;	



	}
	/*************************************************
	Function:       ParserCfgData
	Description:    解析接收的UDP包，得到CAN帧
	Input:          VCI_CAN_OBJ canRawdata
	Output:         无
	Return:         无
	*************************************************/
	int SensorRadar_ARS::ParserCfgData(VCI_CAN_OBJ *pCANMsg)
	{
		unsigned char *pData = pCANMsg->Data;
		int error = Err_NORMAL;
		//RadarState_NVMReadStatus 6  0x00:failed   0x01:successful
		//RadarState_NVMwriteStatus 7 0x00:failed   0x01:successful
		//RadarState_Voltage_Error 17 0x00:no error 0x01:vol error 
		//RadarState_OutputTypeCfg 42 0x00:none       0x01:send objects     0x02: send clusters
		//RadarState_SendQualityCfg 44 0x00:inactive   0x01:active
		//RadarState_SendExtInfoCfg 45 0x00:inactive   0x01:active
		//RadarState_MotionRxState 46 0x00:input ok 0x01:speed missing 0x02:yaw rate missing 0x03 all missing
		
		Byte b1,b2,b3,b4,b5,b6,b7,b8;
		b1.byte = pData[0];
		b2.byte = pData[1];
		b3.byte = pData[2];
		b4.byte = pData[3];
		b5.byte = pData[4];
		b6.byte = pData[5];
		b7.byte = pData[6];
		b8.byte = pData[7];
		
		if(b1.bit7==1 && b1.bit8==1 && b6.bit3==1 && b6.bit5==1 && b6.bit6==1)
		{
			error = Err_NORMAL;
			if(b6.bit7==0 && b6.bit8==0)
			{
				std::cout << "data is ok ~ " << std::endl;
			}
			else if(b6.bit7==1 && b6.bit8==0)
			{
				std::cout << "speed is missing ~ " << std::endl;
			}
			else if(b6.bit7==0 && b6.bit8==1)
			{
				std::cout << "yaw is missing ~ " << std::endl;
			}
			else if(b6.bit7==1 && b6.bit8==1)
			{
				std::cout << "speed & yaw is missing ~ " << std::endl;
			}
		}
		else
		{
			error = Err;
			//std::cout << "config is error, config again ~ " << std::endl;
		}
		return error;
	}
	/*************************************************
	Function:       ParserObjData
	Description:    解析接收的UDP包，得到CAN帧
	Input:          VCI_CAN_OBJ canRawdata
	Output:         无
	Return:         无
	*************************************************/
    bool SensorRadar_ARS::ParserObjData(VCI_CAN_OBJ *pCANMsg)
    {
        bool flag = false;
		int uiID = pCANMsg->ID;
		unsigned char *pData = pCANMsg->Data;
		short temp;
		ARSRecData data;
         //前向雷达占一路，侧向各占一路
		 if(m_RadarCaliInfo.RadarType == FRONTRADAR || m_RadarCaliInfo.RadarType == RIGHTBACKRADAR || m_RadarCaliInfo.RadarType == LEFTBACKRADAR)
		 {
		    if(m_RadarCaliInfo.RadarType == FRONTRADAR)
            {
				if(uiID==0x60a || uiID==0x61a) // 0x60a 障碍物数量信息  if(uiID == 0x601 || uiID == 0x611)
				{
					//也可以认为是一帧数据结束,我们可以在此认为一帧结束
					flag = true;//
					last_nof_objects = nof_objects;
					nof_objects = pData[0];

					//cout << "last num: " << last_nof_objects << " ,now num: " << nof_objects << endl;
				}
				else if(uiID==0x60b || uiID==0x61b) // 0x60b
				{
					data.obstacle_id = pData[0];
					Byte b1;
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					Byte b6;
					b1.byte=pData[1];
					b2.byte=pData[2];
					b3.byte=pData[3];
					b4.byte=pData[4];
					b5.byte=pData[5];
					b6.byte=pData[6];

					data.longitude_dist = OBJECT_DIST_LONG_MIN+OBJECT_DIST_RES*(pow(2,0)*b2.bit4+pow(2,1)*b2.bit5+pow(2,2)*b2.bit6+pow(2,3)*b2.bit7+pow(2,4)*b2.bit8+pow(2,5)*b1.bit1+pow(2,6)*b1.bit2+pow(2,7)*b1.bit3+pow(2,8)*b1.bit4+pow(2,9)*b1.bit5+pow(2,10)*b1.bit6+pow(2,11)*b1.bit7+pow(2,12)*b1.bit8);
					data.lateral_dist = OBJECT_DIST_LAT_MIN+OBJECT_DIST_RES*(pow(2,0)*b3.bit1+pow(2,1)*b3.bit2+pow(2,2)*b3.bit3+pow(2,3)*b3.bit4+pow(2,4)*b3.bit5+pow(2,5)*b3.bit6+pow(2,6)*b3.bit7+pow(2,7)*b3.bit8+pow(2,8)*b2.bit1+pow(2,9)*b2.bit2+pow(2,10)*b2.bit3);
					data.longitude_vel = OBJECT_VREL_LONG_MIN+OBJECT_VREL_RES*(pow(2,0)*b5.bit7+pow(2,1)*b5.bit8+pow(2,2)*b4.bit1+pow(2,3)*b4.bit2+pow(2,4)*b4.bit3+pow(2,5)*b4.bit4+pow(2,6)*b4.bit5+pow(2,7)*b4.bit6+pow(2,8)*b4.bit7+pow(2,9)*b4.bit8);
					data.lateral_vel = OBJECT_VREL_LAT_MIN+OBJECT_VREL_RES*(pow(2,0)*b6.bit6+pow(2,1)*b6.bit7+pow(2,2)*b6.bit8+pow(2,3)*b5.bit1+pow(2,4)*b5.bit2+pow(2,5)*b5.bit3+pow(2,6)*b5.bit4+pow(2,7)*b5.bit5+pow(2,8)*b5.bit6);
					data.rcs = OBJECT_RCS_MIN+OBJECT_RCS_RES*pData[7];
					//目标动态特性
					data.dynProp = pow(2,0)*b6.bit1 + pow(2,1) * b6.bit2 + pow(2,2) * b6.bit3;  


					RadarRecData[data.obstacle_id].longitude_dist = data.longitude_dist;
					RadarRecData[data.obstacle_id].lateral_dist = data.lateral_dist;
					RadarRecData[data.obstacle_id].longitude_vel = data.longitude_vel;
					RadarRecData[data.obstacle_id].lateral_vel = data.lateral_vel;
					RadarRecData[data.obstacle_id].rcs = data.rcs;
					RadarRecData[data.obstacle_id].obstacle_id = data.obstacle_id;
					
					RadarRecData[data.obstacle_id].is_update = 1;
					RadarRecData[data.obstacle_id].dynProp = data.dynProp;

				}
				else if(uiID==0x60c || uiID==0x61c) //0x60c
				{
					data.obstacle_id = pData[0];
					Byte b1;
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					Byte b6;
					b1.byte = pData[1];
					b2.byte = pData[2];
					b3.byte = pData[3];
					b4.byte = pData[4];
					b5.byte = pData[5];
					b6.byte = pData[6];

					if (b6.bit5 == 0) 
					{
						if (b6.bit4 == 0) 
						{
							if (b6.bit3 == 0) 
							{
								data.meas_state = 0;//0x0: Deleted Object
							}
							else 
							{
								data.meas_state = 1;//0x1: New Object is created 
							}
						}
						else 
						{
							if (b6.bit1 == 0) 
							{
								data.meas_state = 2;//0x2: Measured
							}
							else 
							{
								data.meas_state = 3;//0x3: Predicted
							}
						}
					}
					else 
					{
						if (b6.bit2 == 0) 
						{
							if (b6.bit1 == 0) 
							{
								data.meas_state = 4;//0x4: Deleted for merge
							}
							else 
							{
								data.meas_state = 5;//0x5: New from merge  
							}
						} 
					} 

					data.disLong_rms = pow(2,0) * b1.bit4 + pow(2,1) * b1.bit5 + pow(2,2) * b1.bit6 + pow(2,3) * b1.bit7 + pow(2,4) * b1.bit8;  //11
					data.vreLong_rms = pow(2,0) * b2.bit2 + pow(2,1) * b2.bit3 + pow(2,2) * b2.bit4 + pow(2,3) * b2.bit5 + pow(2,4) * b2.bit6;  //17
					data.disLat_rms =  pow(2,0) * b2.bit7 + pow(2,1) * b2.bit8 + pow(2,2) * b3.bit1 + pow(2,3) * b3.bit2 + pow(2,4) * b3.bit3;  //22
					data.vreLat_rms =  pow(2,0) * b3.bit5 + pow(2,1) * b3.bit6 + pow(2,2) * b3.bit7 + pow(2,3) * b3.bit8 + pow(2,4) * b4.bit1;   //28
					data.areLat_rms =  pow(2,0) * b4.bit3 + pow(2,1) * b4.bit4 + pow(2,2) * b4.bit5 + pow(2,3) * b4.bit6 + pow(2,4) * b4.bit7; //34
					data.areLon_rms =  pow(2,0) * b4.bit8 + pow(2,1) * b5.bit1 + pow(2,2) * b5.bit2 + pow(2,3) * b5.bit3 + pow(2,4) * b5.bit4;//39
					data.orientation_angle_rms = pow(2,0) * b5.bit6 + pow(2,1) * b5.bit7 + pow(2,2) * b5.bit8 + pow(2,3) * b6.bit1 + pow(2,4) * b6.bit2;//45
					data.probOfExist = pow(2,0) * b6.bit6 + pow(2,1) * b6.bit7 + pow(2,2) * b6.bit8 ;//53
				//if(RadarRecData[data.obstacle_id].is_update == 1) //已经更新,即使不是顺序过来的，也需要更新datas
					{
					RadarRecData[data.obstacle_id].meas_state = data.meas_state;
					RadarRecData[data.obstacle_id].disLong_rms = data.disLong_rms;
					RadarRecData[data.obstacle_id].vreLong_rms = data.vreLong_rms;
					RadarRecData[data.obstacle_id].disLat_rms = data.disLat_rms;
					RadarRecData[data.obstacle_id].vreLat_rms = data.vreLat_rms;
					RadarRecData[data.obstacle_id].areLat_rms = data.areLat_rms;
					RadarRecData[data.obstacle_id].areLon_rms = data.areLon_rms;
					RadarRecData[data.obstacle_id].orientation_angle_rms = data.orientation_angle_rms;
					RadarRecData[data.obstacle_id].probOfExist = data.probOfExist;
					}

				}
				else if(uiID==0x60d || uiID==0x61d) //0x60d
				{
					data.obstacle_id = pData[0];
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					b2.byte = pData[2];
					b3.byte = pData[3];
					b4.byte = pData[4];
					b5.byte = pData[5];

					if (b3.bit3 == 0) 
					{
						if (b3.bit2 == 0) 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = POINT;
							}
							else 
							{
								data.obstacle_class = CAR;  
							}
						}
						else 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = TRUCK;
							}
							else 
							{
								data.obstacle_class = PEDESTRIAN;
							}
						}
					}
					else 
					{
						if (b3.bit2 == 0) 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = MOTORCYCLE;
							}
							else 
							{
								data.obstacle_class = BICYCLE;  
							}
						}
						else 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = WIDE;
							}
							else 
							{
								data.obstacle_class = RESERVED;
							}
						}    
					} 
					data.orientation_angle = OBJECT_ORIENTATION_ANGEL_MIN+OBJECT_ORIENTATION_ANGEL_RES*(pow(2,0)*b5.bit7+pow(2,1)*b5.bit8+pow(2,2)*b4.bit1+pow(2,3)*b4.bit2+pow(2,4)*b4.bit3+pow(2,5)*b4.bit4+pow(2,6)*b4.bit5+pow(2,7)*b4.bit6+pow(2,8)*b4.bit7+pow(2,9)*b4.bit8);
					data.length = OBJECT_LENGTH_RES*pData[6];
					data.width = OBJECT_WIDTH_RES*pData[7];


					data.areLong = OBJECT_AREL_LONG_MIN+ OBJECT_AREL_RES * (pow(2,0) * b2.bit6 + pow(2,1) * b2.bit7 + pow(2,2) * b2.bit8 + pow(2,3) * b3.bit1 + pow(2,4) * b3.bit2\
					+ pow(2,5) * b3.bit3 + pow(2,6) * b3.bit4 + pow(2,7) * b3.bit5 + pow(2,8) * b3.bit6 + pow(2,9) * b3.bit7 + pow(2,10) * b3.bit8);//21
					data.areLat = OBJECT_AREL_LAT_MIN + OBJECT_AREL_RES * (pow(2,0) * b3.bit5 + pow(2,1) * b3.bit6 + pow(2,2) * b3.bit7 + pow(2,3) * b3.bit8 + pow(2,4) * b4.bit1\
					+ pow(2,5) * b4.bit2 + pow(2,6) * b4.bit3 + pow(2,7) * b4.bit4); //28
					
					RadarRecData[data.obstacle_id].orientation_angle = data.orientation_angle;
					RadarRecData[data.obstacle_id].length = data.length;
					RadarRecData[data.obstacle_id].width = data.width;
					RadarRecData[data.obstacle_id].obstacle_class = data.obstacle_class;
					RadarRecData[data.obstacle_id].areLong = data.areLong;
					RadarRecData[data.obstacle_id].areLat = data.areLat;

				}else if(uiID==0x60e)
				{
					data.obstacle_id = pData[0];
					data.collDetRegionBitField = pData[1];
					RadarRecData[data.obstacle_id].collDetRegionBitField = data.collDetRegionBitField;
					
				}
            }
			else if(m_RadarCaliInfo.RadarType == RIGHTBACKRADAR )
			{
				if(uiID==0x61a) // 0x60a 障碍物数量信息  if(uiID == 0x601 || uiID == 0x611)
				{
					//也可以认为是一帧数据结束,我们可以在此认为一帧结束
					flag = true;//
					last_nof_objects = nof_objects;
					nof_objects = pData[0];

					//cout << "last num: " << last_nof_objects << " ,now num: " << nof_objects << endl;
				}
				else if(uiID==0x61b) // 0x60b
				{
					data.obstacle_id = pData[0];
					Byte b1;
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					Byte b6;
					b1.byte=pData[1];
					b2.byte=pData[2];
					b3.byte=pData[3];
					b4.byte=pData[4];
					b5.byte=pData[5];
					b6.byte=pData[6];

					data.longitude_dist = OBJECT_DIST_LONG_MIN+OBJECT_DIST_RES*(pow(2,0)*b2.bit4+pow(2,1)*b2.bit5+pow(2,2)*b2.bit6+pow(2,3)*b2.bit7+pow(2,4)*b2.bit8+pow(2,5)*b1.bit1+pow(2,6)*b1.bit2+pow(2,7)*b1.bit3+pow(2,8)*b1.bit4+pow(2,9)*b1.bit5+pow(2,10)*b1.bit6+pow(2,11)*b1.bit7+pow(2,12)*b1.bit8);
					data.lateral_dist = OBJECT_DIST_LAT_MIN+OBJECT_DIST_RES*(pow(2,0)*b3.bit1+pow(2,1)*b3.bit2+pow(2,2)*b3.bit3+pow(2,3)*b3.bit4+pow(2,4)*b3.bit5+pow(2,5)*b3.bit6+pow(2,6)*b3.bit7+pow(2,7)*b3.bit8+pow(2,8)*b2.bit1+pow(2,9)*b2.bit2+pow(2,10)*b2.bit3);
					data.longitude_vel = OBJECT_VREL_LONG_MIN+OBJECT_VREL_RES*(pow(2,0)*b5.bit7+pow(2,1)*b5.bit8+pow(2,2)*b4.bit1+pow(2,3)*b4.bit2+pow(2,4)*b4.bit3+pow(2,5)*b4.bit4+pow(2,6)*b4.bit5+pow(2,7)*b4.bit6+pow(2,8)*b4.bit7+pow(2,9)*b4.bit8);
					data.lateral_vel = OBJECT_VREL_LAT_MIN+OBJECT_VREL_RES*(pow(2,0)*b6.bit6+pow(2,1)*b6.bit7+pow(2,2)*b6.bit8+pow(2,3)*b5.bit1+pow(2,4)*b5.bit2+pow(2,5)*b5.bit3+pow(2,6)*b5.bit4+pow(2,7)*b5.bit5+pow(2,8)*b5.bit6);
					data.rcs = OBJECT_RCS_MIN+OBJECT_RCS_RES*pData[7];
					//目标动态特性
					data.dynProp = pow(2,0)*b6.bit1 + pow(2,1) * b6.bit2 + pow(2,2) * b6.bit3;  
					
					RadarRecData[data.obstacle_id].longitude_dist = data.longitude_dist;
					RadarRecData[data.obstacle_id].lateral_dist = data.lateral_dist;
					RadarRecData[data.obstacle_id].longitude_vel = data.longitude_vel;
					RadarRecData[data.obstacle_id].lateral_vel = data.lateral_vel;
					RadarRecData[data.obstacle_id].rcs = data.rcs;
					RadarRecData[data.obstacle_id].obstacle_id = data.obstacle_id;
					
					RadarRecData[data.obstacle_id].is_update = 1;
					RadarRecData[data.obstacle_id].dynProp = data.dynProp;
				}
				else if(uiID==0x61c) //0x60c
				{
					data.obstacle_id = pData[0];
					Byte b1;
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					Byte b6;
					b1.byte = pData[1];
					b2.byte = pData[2];
					b3.byte = pData[3];
					b4.byte = pData[4];
					b5.byte = pData[5];
					b6.byte = pData[6];

					if (b6.bit5 == 0) 
					{
						if (b6.bit4 == 0) 
						{
							if (b6.bit3 == 0) 
							{
								data.meas_state = 0;//0x0: Deleted Object
							}
							else 
							{
								data.meas_state = 1;//0x1: New Object is created 
							}
						}
						else 
						{
							if (b6.bit1 == 0) 
							{
								data.meas_state = 2;//0x2: Measured
							}
							else 
							{
								data.meas_state = 3;//0x3: Predicted
							}
						}
					}
					else 
					{
						if (b6.bit2 == 0) 
						{
							if (b6.bit1 == 0) 
							{
								data.meas_state = 4;//0x4: Deleted for merge
							}
							else 
							{
								data.meas_state = 5;//0x5: New from merge  
							}
						} 
					} 

					data.disLong_rms = pow(2,0) * b1.bit4 + pow(2,1) * b1.bit5 + pow(2,2) * b1.bit6 + pow(2,3) * b1.bit7 + pow(2,4) * b1.bit8;  //11
					data.vreLong_rms = pow(2,0) * b2.bit2 + pow(2,1) * b2.bit3 + pow(2,2) * b2.bit4 + pow(2,3) * b2.bit5 + pow(2,4) * b2.bit6;  //17
					data.disLat_rms =  pow(2,0) * b2.bit7 + pow(2,1) * b2.bit8 + pow(2,2) * b3.bit1 + pow(2,3) * b3.bit2 + pow(2,4) * b3.bit3;  //22
					data.vreLat_rms =  pow(2,0) * b3.bit5 + pow(2,1) * b3.bit6 + pow(2,2) * b3.bit7 + pow(2,3) * b3.bit8 + pow(2,4) * b4.bit1;   //28
					data.areLat_rms =  pow(2,0) * b4.bit3 + pow(2,1) * b4.bit4 + pow(2,2) * b4.bit5 + pow(2,3) * b4.bit6 + pow(2,4) * b4.bit7; //34
					data.areLon_rms =  pow(2,0) * b4.bit8 + pow(2,1) * b5.bit1 + pow(2,2) * b5.bit2 + pow(2,3) * b5.bit3 + pow(2,4) * b5.bit4;//39
					data.orientation_angle_rms = pow(2,0) * b5.bit6 + pow(2,1) * b5.bit7 + pow(2,2) * b5.bit8 + pow(2,3) * b6.bit1 + pow(2,4) * b6.bit2;//45
					data.probOfExist = pow(2,0) * b6.bit6 + pow(2,1) * b6.bit7 + pow(2,2) * b6.bit8 ;//53
				//if(RadarRecData[data.obstacle_id].is_update == 1) //已经更新,即使不是顺序过来的，也需要更新datas
					{
					RadarRecData[data.obstacle_id].meas_state = data.meas_state;
					RadarRecData[data.obstacle_id].disLong_rms = data.disLong_rms;
					RadarRecData[data.obstacle_id].vreLong_rms = data.vreLong_rms;
					RadarRecData[data.obstacle_id].disLat_rms = data.disLat_rms;
					RadarRecData[data.obstacle_id].vreLat_rms = data.vreLat_rms;
					RadarRecData[data.obstacle_id].areLat_rms = data.areLat_rms;
					RadarRecData[data.obstacle_id].areLon_rms = data.areLon_rms;
					RadarRecData[data.obstacle_id].orientation_angle_rms = data.orientation_angle_rms;
					RadarRecData[data.obstacle_id].probOfExist = data.probOfExist;
					}

				}
				else if(uiID==0x61d) //0x60d
				{
					data.obstacle_id = pData[0];
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					b2.byte = pData[2];
					b3.byte = pData[3];
					b4.byte = pData[4];
					b5.byte = pData[5];

					if (b3.bit3 == 0) 
					{
						if (b3.bit2 == 0) 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = POINT;
							}
							else 
							{
								data.obstacle_class = CAR;  
							}
						}
						else 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = TRUCK;
							}
							else 
							{
								data.obstacle_class = PEDESTRIAN;
							}
						}
					}
					else 
					{
						if (b3.bit2 == 0) 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = MOTORCYCLE;
							}
							else 
							{
								data.obstacle_class = BICYCLE;  
							}
						}
						else 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = WIDE;
							}
							else 
							{
								data.obstacle_class = RESERVED;
							}
						}    
					} 
					data.orientation_angle = OBJECT_ORIENTATION_ANGEL_MIN+OBJECT_ORIENTATION_ANGEL_RES*(pow(2,0)*b5.bit7+pow(2,1)*b5.bit8+pow(2,2)*b4.bit1+pow(2,3)*b4.bit2+pow(2,4)*b4.bit3+pow(2,5)*b4.bit4+pow(2,6)*b4.bit5+pow(2,7)*b4.bit6+pow(2,8)*b4.bit7+pow(2,9)*b4.bit8);
					data.length = OBJECT_LENGTH_RES*pData[6];
					data.width = OBJECT_WIDTH_RES*pData[7];


					data.areLong = OBJECT_AREL_LONG_MIN+ OBJECT_AREL_RES * (pow(2,0) * b2.bit6 + pow(2,1) * b2.bit7 + pow(2,2) * b2.bit8 + pow(2,3) * b3.bit1 + pow(2,4) * b3.bit2\
					+ pow(2,5) * b3.bit3 + pow(2,6) * b3.bit4 + pow(2,7) * b3.bit5 + pow(2,8) * b3.bit6 + pow(2,9) * b3.bit7 + pow(2,10) * b3.bit8);//21
					data.areLat = OBJECT_AREL_LAT_MIN + OBJECT_AREL_RES * (pow(2,0) * b3.bit5 + pow(2,1) * b3.bit6 + pow(2,2) * b3.bit7 + pow(2,3) * b3.bit8 + pow(2,4) * b4.bit1\
					+ pow(2,5) * b4.bit2 + pow(2,6) * b4.bit3 + pow(2,7) * b4.bit4); //28

					RadarRecData[data.obstacle_id].orientation_angle = data.orientation_angle;
					RadarRecData[data.obstacle_id].length = data.length;
					RadarRecData[data.obstacle_id].width = data.width;
					RadarRecData[data.obstacle_id].obstacle_class = data.obstacle_class;

					RadarRecData[data.obstacle_id].areLong = data.areLong;
					RadarRecData[data.obstacle_id].areLat = data.areLat;
				}
            }
            else if(m_RadarCaliInfo.RadarType == LEFTBACKRADAR)
            {
 				if(uiID==0x60a) // 0x60a 障碍物数量信息  if(uiID == 0x601 || uiID == 0x611)
				{
					//也可以认为是一帧数据结束,我们可以在此认为一帧结束
					flag = true;//
					last_nof_objects = nof_objects;
					nof_objects = pData[0];

					//cout << "last num: " << last_nof_objects << " ,now num: " << nof_objects << endl;
				}
				else if(uiID==0x60b) // 0x60b
				{
					data.obstacle_id = pData[0];
					Byte b1;
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					Byte b6;
					b1.byte=pData[1];
					b2.byte=pData[2];
					b3.byte=pData[3];
					b4.byte=pData[4];
					b5.byte=pData[5];
					b6.byte=pData[6];
                    //根据协议进行解析
					data.longitude_dist = OBJECT_DIST_LONG_MIN+OBJECT_DIST_RES*(pow(2,0)*b2.bit4+pow(2,1)*b2.bit5+pow(2,2)*b2.bit6+pow(2,3)*b2.bit7+pow(2,4)*b2.bit8+pow(2,5)*b1.bit1+pow(2,6)*b1.bit2+pow(2,7)*b1.bit3+pow(2,8)*b1.bit4+pow(2,9)*b1.bit5+pow(2,10)*b1.bit6+pow(2,11)*b1.bit7+pow(2,12)*b1.bit8);
					data.lateral_dist = OBJECT_DIST_LAT_MIN+OBJECT_DIST_RES*(pow(2,0)*b3.bit1+pow(2,1)*b3.bit2+pow(2,2)*b3.bit3+pow(2,3)*b3.bit4+pow(2,4)*b3.bit5+pow(2,5)*b3.bit6+pow(2,6)*b3.bit7+pow(2,7)*b3.bit8+pow(2,8)*b2.bit1+pow(2,9)*b2.bit2+pow(2,10)*b2.bit3);
					data.longitude_vel = OBJECT_VREL_LONG_MIN+OBJECT_VREL_RES*(pow(2,0)*b5.bit7+pow(2,1)*b5.bit8+pow(2,2)*b4.bit1+pow(2,3)*b4.bit2+pow(2,4)*b4.bit3+pow(2,5)*b4.bit4+pow(2,6)*b4.bit5+pow(2,7)*b4.bit6+pow(2,8)*b4.bit7+pow(2,9)*b4.bit8);
					data.lateral_vel = OBJECT_VREL_LAT_MIN+OBJECT_VREL_RES*(pow(2,0)*b6.bit6+pow(2,1)*b6.bit7+pow(2,2)*b6.bit8+pow(2,3)*b5.bit1+pow(2,4)*b5.bit2+pow(2,5)*b5.bit3+pow(2,6)*b5.bit4+pow(2,7)*b5.bit5+pow(2,8)*b5.bit6);
					data.rcs = OBJECT_RCS_MIN+OBJECT_RCS_RES*pData[7];
					
					//目标动态特性
					data.dynProp = pow(2,0)*b6.bit1 + pow(2,1) * b6.bit2 + pow(2,2) * b6.bit3;  


					RadarRecData[data.obstacle_id].longitude_dist = data.longitude_dist;
					RadarRecData[data.obstacle_id].lateral_dist = data.lateral_dist;
					RadarRecData[data.obstacle_id].longitude_vel = data.longitude_vel;
					RadarRecData[data.obstacle_id].lateral_vel = data.lateral_vel;
					RadarRecData[data.obstacle_id].rcs = data.rcs;
					RadarRecData[data.obstacle_id].obstacle_id = data.obstacle_id;
					
					RadarRecData[data.obstacle_id].is_update = 1;
					RadarRecData[data.obstacle_id].dynProp = data.dynProp;
				}
				else if(uiID==0x60c) //0x60c
				{
					data.obstacle_id = pData[0];

					Byte b1;
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					Byte b6;
					b1.byte = pData[1];
					b2.byte = pData[2];
					b3.byte = pData[3];
					b4.byte = pData[4];
					b5.byte = pData[5];
					b6.byte = pData[6];

					if (b6.bit5 == 0) 
					{
						if (b6.bit4 == 0) 
						{
							if (b6.bit3 == 0) 
							{
								data.meas_state = 0;//0x0: Deleted Object
							}
							else 
							{
								data.meas_state = 1;//0x1: New Object is created 
							}
						}
						else 
						{
							if (b6.bit1 == 0) 
							{
								data.meas_state = 2;//0x2: Measured
							}
							else 
							{
								data.meas_state = 3;//0x3: Predicted
							}
						}
					}
					else 
					{
						if (b6.bit2 == 0) 
						{
							if (b6.bit1 == 0) 
							{
								data.meas_state = 4;//0x4: Deleted for merge
							}
							else 
							{
								data.meas_state = 5;//0x5: New from merge  
							}
						} 
					} 


					data.disLong_rms = pow(2,0) * b1.bit4 + pow(2,1) * b1.bit5 + pow(2,2) * b1.bit6 + pow(2,3) * b1.bit7 + pow(2,4) * b1.bit8;  //11
					data.vreLong_rms = pow(2,0) * b2.bit2 + pow(2,1) * b2.bit3 + pow(2,2) * b2.bit4 + pow(2,3) * b2.bit5 + pow(2,4) * b2.bit6;  //17
					data.disLat_rms =  pow(2,0) * b2.bit7 + pow(2,1) * b2.bit8 + pow(2,2) * b3.bit1 + pow(2,3) * b3.bit2 + pow(2,4) * b3.bit3;  //22
					data.vreLat_rms =  pow(2,0) * b3.bit5 + pow(2,1) * b3.bit6 + pow(2,2) * b3.bit7 + pow(2,3) * b3.bit8 + pow(2,4) * b4.bit1;   //28
					data.areLat_rms =  pow(2,0) * b4.bit3 + pow(2,1) * b4.bit4 + pow(2,2) * b4.bit5 + pow(2,3) * b4.bit6 + pow(2,4) * b4.bit7; //34
					data.areLon_rms =  pow(2,0) * b4.bit8 + pow(2,1) * b5.bit1 + pow(2,2) * b5.bit2 + pow(2,3) * b5.bit3 + pow(2,4) * b5.bit4;//39
					data.orientation_angle_rms = pow(2,0) * b5.bit6 + pow(2,1) * b5.bit7 + pow(2,2) * b5.bit8 + pow(2,3) * b6.bit1 + pow(2,4) * b6.bit2;//45
					data.probOfExist = pow(2,0) * b6.bit6 + pow(2,1) * b6.bit7 + pow(2,2) * b6.bit8 ;//53
				//if(RadarRecData[data.obstacle_id].is_update == 1) //已经更新,即使不是顺序过来的，也需要更新datas
					{
					RadarRecData[data.obstacle_id].meas_state = data.meas_state;
					RadarRecData[data.obstacle_id].disLong_rms = data.disLong_rms;
					RadarRecData[data.obstacle_id].vreLong_rms = data.vreLong_rms;
					RadarRecData[data.obstacle_id].disLat_rms = data.disLat_rms;
					RadarRecData[data.obstacle_id].vreLat_rms = data.vreLat_rms;
					RadarRecData[data.obstacle_id].areLat_rms = data.areLat_rms;
					RadarRecData[data.obstacle_id].areLon_rms = data.areLon_rms;
					RadarRecData[data.obstacle_id].orientation_angle_rms = data.orientation_angle_rms;
					RadarRecData[data.obstacle_id].probOfExist = data.probOfExist;
					}

				}
				else if(uiID==0x60d) //0x60d
				{
					data.obstacle_id = pData[0];
					Byte b2;
					Byte b3;
					Byte b4;
					Byte b5;
					b2.byte = pData[2];
					b3.byte = pData[3];
					b4.byte = pData[4];
					b5.byte = pData[5];

					if (b3.bit3 == 0) 
					{
						if (b3.bit2 == 0) 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = POINT;
							}
							else 
							{
								data.obstacle_class = CAR;  
							}
						}
						else 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = TRUCK;
							}
							else 
							{
								data.obstacle_class = PEDESTRIAN;
							}
						}
					}
					else 
					{
						if (b3.bit2 == 0) 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = MOTORCYCLE;
							}
							else 
							{
								data.obstacle_class = BICYCLE;  
							}
						}
						else 
						{
							if (b3.bit1 == 0) 
							{
								data.obstacle_class = WIDE;
							}
							else 
							{
								data.obstacle_class = RESERVED;
							}
						}    
					} 
					data.orientation_angle = OBJECT_ORIENTATION_ANGEL_MIN+OBJECT_ORIENTATION_ANGEL_RES*(pow(2,0)*b5.bit7+pow(2,1)*b5.bit8+pow(2,2)*b4.bit1+pow(2,3)*b4.bit2+pow(2,4)*b4.bit3+pow(2,5)*b4.bit4+pow(2,6)*b4.bit5+pow(2,7)*b4.bit6+pow(2,8)*b4.bit7+pow(2,9)*b4.bit8);
					data.length = OBJECT_LENGTH_RES*pData[6];
					data.width = OBJECT_WIDTH_RES*pData[7];


					data.areLong = OBJECT_AREL_LONG_MIN+ OBJECT_AREL_RES * (pow(2,0) * b2.bit6 + pow(2,1) * b2.bit7 + pow(2,2) * b2.bit8 + pow(2,3) * b3.bit1 + pow(2,4) * b3.bit2\
					+ pow(2,5) * b3.bit3 + pow(2,6) * b3.bit4 + pow(2,7) * b3.bit5 + pow(2,8) * b3.bit6 + pow(2,9) * b3.bit7 + pow(2,10) * b3.bit8);//21
					data.areLat = OBJECT_AREL_LAT_MIN + OBJECT_AREL_RES * (pow(2,0) * b3.bit5 + pow(2,1) * b3.bit6 + pow(2,2) * b3.bit7 + pow(2,3) * b3.bit8 + pow(2,4) * b4.bit1\
					+ pow(2,5) * b4.bit2 + pow(2,6) * b4.bit3 + pow(2,7) * b4.bit4); //28


					RadarRecData[data.obstacle_id].orientation_angle = data.orientation_angle;
					RadarRecData[data.obstacle_id].length = data.length;
					RadarRecData[data.obstacle_id].width = data.width;
					RadarRecData[data.obstacle_id].obstacle_class = data.obstacle_class;
					RadarRecData[data.obstacle_id].areLong = data.areLong;
					RadarRecData[data.obstacle_id].areLat = data.areLat;
				}
            }
		 }//end if frontradar

        if(flag) //接收结束
        {
            double PI = 3.1415926;
            for (int k = 0; k < MaxTarNum; ++k)
            {
                //add calibration
				if(RadarRecData[k].is_update == 1)
				{
					float heading = m_RadarCaliInfo.DeltaAngle/100.0; //DeltaAngle: 0.1度 顺时针为+
					float y = RadarRecData[k].longitude_dist; //m
					float x = -RadarRecData[k].lateral_dist;//left is +, right is -. 笛卡尔系

					//速度
					float speedy = RadarRecData[k].longitude_vel; //m
					float speedx = -RadarRecData[k].lateral_vel;					

					if(m_RadarCaliInfo.RadarType == LEFTBACKRADAR || m_RadarCaliInfo.RadarType == RIGHTBACKRADAR )
					{
                       y = -y;
					   speedy = -speedy;
					}

					//横摆角
					float xx = x*cos(heading/180.0*M_PI) + y*sin(heading/180.0*M_PI); //fu zuo
					float yy = -x*sin(heading/180.0*M_PI) + y*cos(heading/180.0*M_PI);

					//平移
					yy = yy + m_RadarCaliInfo.DeltaY/100.0; //
					xx = xx + m_RadarCaliInfo.DeltaX/100.0; // DeltaX : cm
				
					RadarRecData[k].longitude_dist = yy;

					float speedxx = speedx*cos(heading/180.0*M_PI) + speedy*sin(heading/180.0*M_PI); //fu zuo
					float speedyy = -speedx*sin(heading/180.0*M_PI) + speedy*cos(heading/180.0*M_PI);
                    RadarRecData[k].longitude_vel = speedyy;

                    //坐标转换
					if(m_RadarCaliInfo.RadarType == LEFTBACKRADAR || m_RadarCaliInfo.RadarType == RIGHTBACKRADAR )
					{
                         RadarRecData[k].lateral_dist = -xx;
						 RadarRecData[k].lateral_vel = -speedxx;
					}
					else 
					{
					   RadarRecData[k].lateral_dist = xx;
					   RadarRecData[k].lateral_vel = speedxx;//转成笛卡尔系
					}
				}
            }
        }
        return flag;
    }
