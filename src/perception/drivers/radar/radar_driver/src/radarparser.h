
/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	radarparser.h
Description: 毫米波雷达数据解析程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/

#pragma once
#include<iostream>
#include<vector>
#include<string.h>

#include "CanetUdp.h"

#include <unistd.h>
#include<iostream>
#include<vector>
#include<ros/ros.h>
#include<std_msgs/Float32MultiArray.h>

 #include "pthread.h"
 #include"radar_msgs/sensorradar.h"
 #include"radar_msgs/radarobject.h"
 #include"common_msgs/sensorgps.h"

 #include"const_vars.h"
 #include"data.h"


using namespace std;

//毫米波种类
enum RadarType
{
	  FRONTRADAR=0,
    LEFTFRONTRADAR=1,
	  RIGHTFRONTRADAR=2,
	  BACKRADAR=3,
    LEFTBACKRADAR=4,
    RIGHTBACKRADAR=5,
    RESERVE=6 
};

//毫米波赋值的障碍物种类
enum ClassType
{
	POINT=0,
	CAR=1,
	TRUCK=2,
	PEDESTRIAN=3,
	MOTORCYCLE=4,
	BICYCLE=5,
	WIDE=6,
	RESERVED=7
};

//////////////////////////////////////////////////////////////////
typedef union CHAR2UINT
{
	unsigned int i;
	unsigned char ch[4];
}CHAR2UINT;

 typedef union Char2Uint{
   unsigned short us;
   char ch[2];
 }Char2Uint;

 typedef union Char2Uchar{
   unsigned char uc;
   char ch;
 }Char2Uchar;

 typedef union Char2Int{
   int i;
   char ch[4];
 }Char2Int;

 typedef union Char2Ushort{
   unsigned short sh;
   unsigned char ch[2];
 }Char2Ushort;

 typedef union Char2Short{
   short sh;
   unsigned char ch[2];
 }Char2Short;



 //从CanetUdp那得到的CAN帧
typedef struct _CANMsg
{
  unsigned char head;//帧头
	unsigned ID;//ID of message  4 bytes
	unsigned char Data[8];//Data of message
} stCANMsg,VCI_CAN_OBJ;//CAN报文定义

//struct define
/************************************************************************/
/*                           ARS雷达相关变量                              */
/************************************************************************/

//向ARS雷达发送的CAN帧ID地址
#define	ARS_CONFIG_0_ID	0x4F0
#define	ARS_CONFIG_1_ID	0x4F1

#define ARS_MAX_DISTANCE (25000)//cm
#define MaxTarNum (256) //conti 雷达的目标检测最大数目

#define DELTA_RANGE (200)//cm
//#define DELTA_ANGLE (100)//*0.01deg
#define DELTA_RANGERATE (200)//cm/s

#define CYCLE_RX_ARS (50)//接收周期
#define CYCLE_TX_ARS (20)//发送周期

typedef struct _ARSRecData
{
	unsigned char obstacle_id; //障碍物ID
	float longitude_dist;//纵向坐标
	float lateral_dist;//横向坐标
	float longitude_vel;//纵向速度
	float lateral_vel;//横向速度
	float rcs;//反射强度
	float orientation_angle;//起始角
	float length;//长度
	float width;//宽度
	unsigned char obstacle_class;//种类
	unsigned char meas_state;//障碍物的状态
	unsigned char is_update;//数据是否更新
	//
	unsigned char disLong_rms;  // 纵向距离标准差  c  --
	unsigned char disLat_rms; //   横向距离标准差  c  --
	unsigned char vreLong_rms; //纵向相对速度标准差  c  --
	unsigned char vreLat_rms; //横向相对速度标准差  c  --
	unsigned char areLat_rms;  //横向相对加速度标准差  c --
	unsigned char areLon_rms;   //纵向相对加速度标准差  c --
	unsigned char orientation_angle_rms;  //方向角标准差  c --
	float  areLong;  // 目标的纵向相对加速度  d   --
	float  areLat;   // 目标的横向相对加速度  d  --
	unsigned char probOfExist; //测试状态   c   --
	unsigned char dynProp;   //目标的动态 特性b    -- ok
	unsigned char collDetRegionBitField;// 区域的位字段

} ARSRecData;


//雷达状态
typedef enum _Err_ARS
{
	Err_NORMAL = 0,
	Err = 1
} Err_ARS;

//原车及道路相关参数
#define LANE_WIDTH (375)//车道宽，单位cm
#define VEHICLE_WIDTH (180)//自车宽，单位cm

/************************************************************************/
/*                         雷达标定相关                                */
/************************************************************************/
typedef struct _RadarCaliInfo
{
	int DeltaX;//横向偏移
	int DeltaY;//纵向偏移
	int DeltaAngle;//角度偏移
	int RadarType;//雷达种类
	int RadarUpdown;//雷达是否镜像安装
} RadarCaliInfo;
//将车辆最前端的中心点设为原点，DeltaX，DeltaY，DeltaAngle分别表示雷达相对原点的\
                  纵向距离（向前为正）、横向距离（向右为正）、偏转角度（逆时针为正）。

    class SensorRadar_ARS
	{
	public:
		/*************************************************
		Function:       SensorRadar_ARS
		Description:    构造函数，主要用于数据初始化
		Input:          handle：ros句柄
		Output:         无
		Return:         无
		*************************************************/  
		SensorRadar_ARS();
		/*************************************************
		Function:       ~SensorRadar_ARS
		Description:    析构函数
		Input:          无
		Output:         无
		Return:         无
		*************************************************/ 	
		~SensorRadar_ARS(){}

		/*************************************************
		Function:       SetCaliInfo
		Description:    设置雷达标定位置
		Input:          pCaliInfo：雷达标定信息指针
		Output:         无
		Return:         无
		*************************************************/
        void SetCaliInfo(int DeltaAngle, int DeltaX, int DeltaY,int RadarType,int RadarUpdown=0);

		/*************************************************
		Function:       ParserCfgData
		Description:    解析接收的UDP包，得到CAN帧
		Input:          VCI_CAN_OBJ canRawdata
		Output:         无
		Return:         无
		*************************************************/
		int ParserCfgData(VCI_CAN_OBJ *pCANMsg);

		/*************************************************
		Function:       ParserData
		Description:    解析接收的UDP包，得到CAN帧
		Input:          VCI_CAN_OBJ canRawdata
		Output:         无
		Return:         无
		*************************************************/
		bool ParserObjData(VCI_CAN_OBJ *pCANMsg);
        
        //雷达解析出来的数据
		ARSRecData RadarRecData[MaxTarNum];//Received radar message
        
        //标定参数
		RadarCaliInfo m_RadarCaliInfo;

		int nof_objects;//障碍物数量
		int last_nof_objects;//上一次障碍物数量
		int last_objectid;//上一次障碍物ID
    };


    
