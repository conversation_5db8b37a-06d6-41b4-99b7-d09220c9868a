#ifndef __CANBUS_DEVICE_H__
#define __CANBUS_DEVICE_H__
#include <memory>
#include <fstream>
#include <iostream>
#include <sstream>
#include <queue>
#include <string.h>
#include <mutex>
#include <thread>
struct canData
{
    unsigned char* data;
    int len ;
    int msg_id;
    canData()
    {
        this->data = nullptr;
        this->len = 0;
        this->msg_id = 0;
    }
};



class CanbusDevice
{
public:
    explicit CanbusDevice(const char* dev_name);
    int Init();
    ~CanbusDevice();
    
public:
    int Read(unsigned char *data, int& len, int& msg_id);
    int Write(const unsigned char *data, int len, int msg_id);
    std::string GetDevName();
    std::queue<canData> CanDataGet();
    void recvDataTh();
private:
    std::string m_can_dev_name;
    int m_can_fd;
    std::mutex m_SendDdata_mutex;
    std::queue<struct canData> m_CanQue;
    bool m_start = false;
    int m_count = 0;
};


#endif