<package>

  <name>radar</name>
  <version>1.0.0</version>
  <description>
    Basic ROS support for the Radar.
  </description>
  <maintainer email="<EMAIL>">zhuxuekui</maintainer>
  <author> zhuxuekui</author>
  <license>BSD</license>
  <!--
  <review status="API reviewed" notes="2017-04-17"/>
  -->
  <url type="website">http://www.ros.org/wiki/ivradar</url>
  <url type="repository">https://github.com/ros-drivers/radar</url>
  <url type="bugtracker">https://github.com/ros-drivers/radar</url>

  <buildtool_depend>catkin</buildtool_depend>

  <run_depend>radar_driver</run_depend>
  <run_depend>radar_msgs</run_depend>
  <run_depend>radar_deal</run_depend>

  <export>
    <metapackage/>
  </export>

</package>
