cmake_minimum_required(VERSION 2.8.3)
project(autoalign)


find_package(catkin REQUIRED COMPONENTS
  cv_bridge
  image_transport
  roscpp
  rospy
  sensor_msgs
  std_msgs
  genmsg
  actionlib_msgs
  actionlib
  message_generation
  dynamic_reconfigure
   roslib
)

## Generate added messages and services with any dependencies listed here
#  generate_messages(
#    DEPENDENCIES
#       sensor_msgs
#       std_msgs
#       actionlib_msgs
#       std_msgs 
#  )


## Generate dynamic reconfigure parameters in the 'cfg' folder
 generate_dynamic_reconfigure_options(
#   cfg/DynReconf1.cfg
#   cfg/DynReconf2.cfg
    cfg/autoalign.cfg
#   nodeparams.cfg
 )


catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES usbcamera_pub
  CATKIN_DEPENDS cv_bridge image_transport roscpp rospy sensor_msgs std_msgs message_runtime
#  DEPENDS system_lib
)


include_directories(
  ${catkin_INCLUDE_DIRS}
  ${dynamic_reconfigure_PACKAGE_PATH}/cmake/cfgbuild.cmake
)


file(GLOB_RECURSE EXTRA_FILES */*)
add_custom_target(${PROJECT_NAME}_OTHER_FILES ALL WORKING_DIRECTORY ${PROJECT_SOURCE_DIR} SOURCES ${EXTRA_FILES})


add_executable(autoalign src/main.cpp src/mapcfg.cpp)
#add_dependencies(autoalign ${PROJECT_NAME}_gencfg)
add_dependencies(autoalign ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
target_link_libraries(autoalign ${catkin_LIBRARIES})


# Mark libraries for installation
# See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_libraries.html
install(TARGETS ${PROJECT_NAME}
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
        )

# Mark cpp header files for installation


# Mark other files for installation (e.g. launch and bag files, etc.)
install(DIRECTORY launch cfg
        DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
        )