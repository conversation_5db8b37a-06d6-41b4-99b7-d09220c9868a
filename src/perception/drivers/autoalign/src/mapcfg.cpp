
#include "mapcfg.h"
#include <iostream>
using namespace std;
mapcfg::mapcfg(ros::NodeHandle nh) {
    std::string s0 = "unknown";
    int s1 = 0;
    double s2 = 0.0;
    bool s3;
    ROS_INFO("Starting to spin...");
    // boost bind invoking object member function;
    dynamic_reconfigure::Server<config_> srv;
    dynamic_reconfigure::Server<config_>::CallbackType f;
    f = boost::bind(&mapcfg::callback, this, _1, _2);
    srv.setCallback(f);
    ros::Rate loop_rate(1);

    while (ros::ok()) 
    {
        // please pay attention to the useage of ROS_INFO and ROS_INFO_STREAM
        ros::spinOnce();
        loop_rate.sleep();
    }
}

mapcfg::~mapcfg() 
{
    
}



	/*************************************************
	Function:       callback
	Description:    回调函数，并赋值
	Input:          配置参数，等级
	Output:         无
	Return:         无
	*************************************************/  
void mapcfg::callback(config_ &config, uint32_t level) 
{

    ros::NodeHandle nh;
    m_config = config;

    nh.setParam("delta_yaw", m_config.delta_yaw);
    nh.setParam("delta_pitch", m_config.delta_pitch);
    nh.setParam("delta_roll", m_config.delta_roll);
    nh.setParam("delta_x", m_config.delta_x);
    nh.setParam("delta_y", m_config.delta_y);
    nh.setParam("delta_z", m_config.delta_z);
    nh.setParam("align_leftlidar", m_config.leftlidar);
    nh.setParam("align_midlidar", m_config.midlidar);
    nh.setParam("align_frontlidar", m_config.frontlidar);
    nh.setParam("align_backlidar", m_config.backlidar);  
    nh.setParam("align_rightlidar", m_config.rightlidar);
    nh.setParam("align_luxlidar", m_config.luxlidar);    

    nh.setParam("align_frontradar", m_config.frontradar);
    nh.setParam("align_leftfrontradar", m_config.leftfrontradar);
    nh.setParam("align_rightfrontradar", m_config.rightfrontradar);

    nh.setParam("align_backradar", m_config.backradar);
    nh.setParam("align_leftbackradar", m_config.leftbackradar);
    nh.setParam("align_rightbackradar", m_config.rightbackradar);
    nh.setParam("align_lidargps", m_config.lidargps);
    
}
