
#ifndef _CONFIG_H
#define _CONFIG_H

// c++ lib
#include <vector>
// ROS lib
#include "ros/ros.h"
#include "ros/time.h"
// other lib
#include <sstream>

#include "std_msgs/String.h"
#include <dynamic_reconfigure/server.h>
#include <autoalign/autoalignConfig.h>

using namespace std;
// change you configure class here
typedef autoalign::autoalignConfig config_;

class mapcfg {
    public:
        mapcfg(ros::NodeHandle nh);
        ~mapcfg();

	/*************************************************
	Function:       callback
	Description:    回调函数，并赋值
	Input:          配置参数，等级
	Output:         无
	Return:         无
	*************************************************/  
        void callback(config_ &config, uint32_t level);

    private:
        config_ m_config;
        ros::Subscriber sub_;
        ros::Publisher pub_;
};

#endif
