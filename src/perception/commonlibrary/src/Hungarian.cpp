///////////////////////////////////////////////////////////////////////////////
// Hungarian.cpp: Implementation file for Class HungarianAlgorithm.
// 
// This is a C++ wrapper with slight modification of a hungarian algorithm implementation by <PERSON>.
// The original implementation is a few mex-functions for use in MATLAB, found here:
// http://www.mathworks.com/matlabcentral/fileexchange/6543-functions-for-the-rectangular-assignment-problem
// 
// Both this code and the orignal code are published under the BSD license.
// by Cong Ma, 2016
// 

#include "Hungarian.h"
#include <stdlib.h>
#include <math.h>
#include <float.h>


HungarianAlgorithm::HungarianAlgorithm(){}
HungarianAlgorithm::~HungarianAlgorithm(){}


//********************************************************//
// A single function wrapper for solving assignment problem.
//********************************************************//
double HungarianAlgorithm::Solve(vector<vector<double>>& DistMatrix, vector<int>& Assignment)
{
	unsigned int nRows = DistMatrix.size();
	unsigned int nCols = DistMatrix[0].size();
	// if(nRows == 0 || nCols == 0){
	// 	std::cout << "hungarian debug: " <<nRows<<", " << nCols<<endl;
	// }
	// std::cout << "hungarian debug: row=" <<nRows<<", col=" << nCols<<endl;
	double *distMatrixIn = new double[nRows * nCols];//创建一个有nRows * nCols个元素的动态double数组，没有赋值，元素为随机数
	int *assignment = new int[nRows];//创建一个有nRows个元素的动态整型数组，没有赋值，元素为随机数
	double cost = 0.0;

	// Fill in the distMatrixIn. Mind the index is "i + nRows * j".
	// Here the cost matrix of size MxN is defined as a double precision array of N*M elements. 
	// In the solving functions matrices are seen to be saved MATLAB-internally in row-order.
	// (i.e. the matrix [1 2; 3 4] will be stored as a vector [1 3 2 4], NOT [1 2 3 4]).
	for (unsigned int i = 0; i < nRows; i++)
		for (unsigned int j = 0; j < nCols; j++)
			distMatrixIn[i + nRows * j] = DistMatrix[i][j];//损失矩阵：二维转一维
	// call solving function
	assignmentoptimal(assignment, &cost, distMatrixIn, nRows, nCols);
	Assignment.clear();
	for (unsigned int r = 0; r < nRows; r++)
		Assignment.push_back(assignment[r]);
	delete[] distMatrixIn;
	delete[] assignment;
	//std::cout << "hungarian debug:HungarianAlgorithm solved" << endl;
	return cost;
}


//********************************************************//
// Solve optimal solution for assignment problem using Munkres algorithm, also known as Hungarian Algorithm.
//匈牙利算法（指派算法）
//Munkres 算法是一种最优分配算法，可以看作是匈牙利算法的一个变种。它的不同之处在于处理过程中：
//1. 包含所有零的“最小行集”
//2. 独立零点的“最大集”
//https://blog.csdn.net/yiran103/article/details/103826367
//********************************************************//
void HungarianAlgorithm::assignmentoptimal(int *assignment, double *cost, double *distMatrixIn, int nOfRows, int nOfColumns)
{
	double *distMatrix, *distMatrixTemp, *distMatrixEnd, *columnEnd, value, minValue;
	bool *coveredColumns, *coveredRows, *starMatrix, *newStarMatrix, *primeMatrix;
	int nOfElements, minDim, row, col;

	/* initialization */
	*cost = 0;
	for (row = 0; row<nOfRows; row++)
		assignment[row] = -1;
	/* generate working copy of distance Matrix */
	/* check if all matrix elements are positive */
	nOfElements = nOfRows * nOfColumns;
	distMatrix = (double *)malloc(nOfElements * sizeof(double));
	distMatrixEnd = distMatrix + nOfElements;

	for (row = 0; row<nOfElements; row++)
	{
		value = distMatrixIn[row];
		if (value < 0)
			cerr << "All matrix elements have to be non-negative." << endl;
		distMatrix[row] = value;
	}


	/* memory allocation */
	coveredColumns = (bool *)calloc(nOfColumns, sizeof(bool));
	coveredRows = (bool *)calloc(nOfRows, sizeof(bool));
	starMatrix = (bool *)calloc(nOfElements, sizeof(bool));//记录当前的匹配
	primeMatrix = (bool *)calloc(nOfElements, sizeof(bool));
	newStarMatrix = (bool *)calloc(nOfElements, sizeof(bool)); /* used in step4 */

	/* preliminary steps 从数量少的方向开始，从每行、每列寻找距离最小值*/
	minDim = nOfRows <= nOfColumns?nOfRows:nOfColumns;
	if (nOfRows <= nOfColumns)
	{
	//	minDim = nOfRows;

		for (row = 0; row<nOfRows; row++)
		{
			/* find the smallest element in the row 从数量少的方向开始，从每行找出距离最小值*/
			distMatrixTemp = distMatrix + row;//distMatrixTemp一个访问元素的指针
			minValue = *distMatrixTemp;
			distMatrixTemp += nOfRows;
			while (distMatrixTemp < distMatrixEnd)
			{
				value = *distMatrixTemp;
				if (value < minValue)
					minValue = value;
				distMatrixTemp += nOfRows;
			}

			/* subtract the smallest element from each element of the row 从每行中减去最小值*/
			distMatrixTemp = distMatrix + row;
			while (distMatrixTemp < distMatrixEnd)
			{
				*distMatrixTemp -= minValue;
				distMatrixTemp += nOfRows;
			}
		}

		/* Steps 1 and 2a 记录处理过的位置：统计0所在的列，在当前行遇到未标星的0值，标星并记录所在列，剩下的元素不进行判断，直接处理下一行*/
		for (row = 0; row<nOfRows; row++)
			for (col = 0; col<nOfColumns; col++)
				if (fabs(distMatrix[row + nOfRows*col]) < DBL_EPSILON)//DBL_EPSILON所能识别的最小精度
					if (!coveredColumns[col])//如果距离矩阵中的零没有标性，则在starMatrix中标星，并记录标星所在列
					{
						starMatrix[row + nOfRows*col] = true;//记录一维坐标中0的索引，作用是？
						coveredColumns[col] = true;//记录二维数组中0只所在列索引
						break;
					}
	}
	else /* if(nOfRows > nOfColumns) */
	{
	//	minDim = nOfColumns;

		for (col = 0; col<nOfColumns; col++)
		{
			/* find the smallest element in the column 从数量少的方向开始，从每列找出距离最小值*/
			distMatrixTemp = distMatrix + nOfRows*col;
			columnEnd = distMatrixTemp + nOfRows;

			minValue = *distMatrixTemp++;
			while (distMatrixTemp < columnEnd)
			{
				value = *distMatrixTemp++;
				if (value < minValue)
					minValue = value;
			}

			/* subtract the smallest element from each element of the column 从每行中减去最小值*/
			distMatrixTemp = distMatrix + nOfRows*col;
			while (distMatrixTemp < columnEnd)
				*distMatrixTemp++ -= minValue;
		}

		/* Steps 1 and 2a 记录处理过的位置*/
		for (col = 0; col<nOfColumns; col++)
			for (row = 0; row<nOfRows; row++)
				if (fabs(distMatrix[row + nOfRows*col]) < DBL_EPSILON)
					if (!coveredRows[row])
					{
						starMatrix[row + nOfRows*col] = true;
						coveredColumns[col] = true;
						coveredRows[row] = true;
						break;
					}
		for (row = 0; row<nOfRows; row++)
			coveredRows[row] = false;

	}
	//std::cout << "hungarian debug:assignmentoptimal3 "<<endl;
	/* move to step 2b 统计覆盖列的数量*/
	step2b(assignment, distMatrix, starMatrix, newStarMatrix, primeMatrix, coveredColumns, coveredRows, nOfRows, nOfColumns, minDim);
	//std::cout << "hungarian debug:assignmentoptimal4 "<<endl;
	/* compute cost and remove invalid assignments  计算分配方案的成本*/
	computeassignmentcost(assignment, cost, distMatrixIn, nOfRows);
	//std::cout << "hungarian debug:assignmentoptimal finished5 "<<endl;
	/* free allocated memory */
	free(distMatrix);
	free(coveredColumns);
	free(coveredRows);
	free(starMatrix);
	free(primeMatrix);
	free(newStarMatrix);

	return;
}

/**********************
 * 如果starMatrix相应位置做了标记，则将匹配结果记录到assignment数组。
 * **********************************/
void HungarianAlgorithm::buildassignmentvector(int *assignment, bool *starMatrix, int nOfRows, int nOfColumns)
{
	int row, col;

	for (row = 0; row<nOfRows; row++)
		for (col = 0; col<nOfColumns; col++)
			if (starMatrix[row + nOfRows*col])
			{
#ifdef ONE_INDEXING
				assignment[row] = col + 1; /* MATLAB-Indexing */
#else
				assignment[row] = col;//行对应的列，即匹配结果此行匹配此列
#endif
				break;
			}
}

/********************************************************/
void HungarianAlgorithm::computeassignmentcost(int *assignment, double *cost, double *distMatrix, int nOfRows)
{
	int row, col;

	for (row = 0; row<nOfRows; row++)
	{
		col = assignment[row];
		if (col >= 0)
			*cost += distMatrix[row + nOfRows*col];
	}
}

/********************
 * 遍历每一列，如果元素中有加星的零则覆盖该列
 * ************************************/
void HungarianAlgorithm::step2a(int *assignment, double *distMatrix, bool *starMatrix, bool *newStarMatrix, bool *primeMatrix, bool *coveredColumns, bool *coveredRows, int nOfRows, int nOfColumns, int minDim)
{
	bool *starMatrixTemp, *columnEnd;
	int col;

	/* cover every column containing a starred zero  标星所在的列设置已覆盖*/
	for (col = 0; col<nOfColumns; col++)//遍历每一列
	{
		starMatrixTemp = starMatrix + nOfRows*col;
		columnEnd = starMatrixTemp + nOfRows;
		while (starMatrixTemp < columnEnd){//遍历每一行
			if (*starMatrixTemp++)
			{
				coveredColumns[col] = true;//如果标星的列没有设置覆盖，设置覆盖
				break;
			}
		}
	}

	/* move to step 3 */
	step2b(assignment, distMatrix, starMatrix, newStarMatrix, primeMatrix, coveredColumns, coveredRows, nOfRows, nOfColumns, minDim);
}

/********************
 * 统计覆盖列的数量
 * ************************************/
void HungarianAlgorithm::step2b(int *assignment, double *distMatrix, bool *starMatrix, bool *newStarMatrix, bool *primeMatrix, bool *coveredColumns, bool *coveredRows, int nOfRows, int nOfColumns, int minDim)
{
	int col, nOfCoveredColumns;

	/* count covered columns 统计标星所在列个数*/
	nOfCoveredColumns = 0;
	for (col = 0; col<nOfColumns; col++)
		if (coveredColumns[col])
			nOfCoveredColumns++;
	//std::cout << "hungarian debug:step2b-start "<<endl;
	//要用最少的直线把矩阵中的零都给覆盖，如果直线的数量等于行或列（行或列取值小的一个），那我们就可以得到一个最优方案
	if (nOfCoveredColumns == minDim)//直线数等于行或列（行或列取值小的一个）
	{
		/* algorithm finished 得到跟踪和检测匹配的结果，赋值匹配跟踪-检测索引到assignment*/
		buildassignmentvector(assignment, starMatrix, nOfRows, nOfColumns);
	}
	else
	{
		//std::cout << "hungarian debug:step2b-step3-start "<<endl;
		/* move to step 3 直线数小于列数*/
		step3(assignment, distMatrix, starMatrix, newStarMatrix, primeMatrix, coveredColumns, coveredRows, nOfRows, nOfColumns, minDim);
		//std::cout << "hungarian debug:step2b-step3-end "<<endl;
	}
	//std::cout << "hungarian debug:step2b2-end "<<endl;
}

/*************************
 * 遍历未覆盖列，寻找未覆盖的列中的0值，找不到进入step 5；找到了判断0值所在行是否标记为覆盖行，是覆盖行退出当前列，寻找下一列，若该行未标记覆盖，则进入step 4
 * ，
 * 找到一个未覆盖的零并将其加撇。如果在包含该加撇零的行中没有加星号的零，调用 HungarianAlgorithm::step4。
否则，覆盖该行并揭开包含加星号的零的列。以这种方式继续，直到没有剩余的零为止。
保存最小的未覆盖值，然后调用 HungarianAlgorithm::step5。
————————————————
版权声明：本文为CSDN博主「图波列夫」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://blog.csdn.net/yiran103/article/details/103826367
 *******************************/
void HungarianAlgorithm::step3(int *assignment, double *distMatrix, bool *starMatrix, bool *newStarMatrix, bool *primeMatrix, bool *coveredColumns, bool *coveredRows, int nOfRows, int nOfColumns, int minDim)
{
	bool zerosFound;
	int row, col, starCol;
	//std::cout << "hungarian debug:step3-2 "<<endl;
	zerosFound = true;
	while (zerosFound)
	{
		zerosFound = false;
		for (col = 0; col<nOfColumns; col++)//遍历每一列，每列都重新检查是否有未标星所在的列
			if (!coveredColumns[col]) //如果当前列未标星，则遍历每行，找到另一个值0的位置，设置primeMatrix对应位置为true
				//当前列因为其所在列的其他位置有0值（即所在列的其他位置找到匹配）
				for (row = 0; row<nOfRows; row++)
					if ((!coveredRows[row]) && (fabs(distMatrix[row + nOfRows*col]) < DBL_EPSILON)) // 开始如果行数小于列数，coveredRows为空，进入下边循环外step 5
					{
						/* prime zero */
						primeMatrix[row + nOfRows*col] = true;

						/* find starred zero in current row 在该行中有标星的0所在的列即寻找下一行，若这个0已标星，寻找下一行*/
						for (starCol = 0; starCol<nOfColumns; starCol++)
							if (starMatrix[row + nOfRows*starCol])
								break;

						if (starCol == nOfColumns) /* no starred zero found 到最后一列若没有找到，即这行没有标星的0*/
						{
							/* move to step 4 */
							//std::cout << "hungarian debug:step4-start "<<endl;
							step4(assignment, distMatrix, starMatrix, newStarMatrix, primeMatrix, coveredColumns, coveredRows, nOfRows, nOfColumns, minDim, row, col);
							return;
						}
						else
						{
							//当前列找到一个未标星的0，记录下所在行列即退出当前列，处理下一列
							//找到未标星所在行列，将所在行的覆盖行coveredRows置位，所在列复位，跳过之后的行，进入下一列处理
							coveredRows[row] = true;//将当前行和0值所在的列认为是候选的匹配
							coveredColumns[starCol] = false;//撤销当前的true，即不将其所在行和列作为合理的匹配对
							zerosFound = true;
							break;//找到未标星所在行列，将所在行的覆盖行coveredRows置位，所在列复位，跳过之后的行，进入下一列处理
						}
					}
	}
	//std::cout << "hungarian debug:step3-in "<<endl;
	/* move to step 5 当前未标星的列没有0值进入step 5 && */
	//std::cout << "hungarian debug:step5-start "<<endl;
	step5(assignment, distMatrix, starMatrix, newStarMatrix, primeMatrix, coveredColumns, coveredRows, nOfRows, nOfColumns, minDim);
	//std::cout << "hungarian debug:step3-out "<<endl;
}

/********************************************************/
void HungarianAlgorithm::step4(int *assignment, double *distMatrix, bool *starMatrix, bool *newStarMatrix, bool *primeMatrix, bool *coveredColumns, bool *coveredRows, int nOfRows, int nOfColumns, int minDim, int row, int col)
{
	int n, starRow, starCol, primeRow, primeCol;
	int nOfElements = nOfRows*nOfColumns;

	/* generate temporary copy of starMatrix */
	for (n = 0; n<nOfElements; n++)
		newStarMatrix[n] = starMatrix[n];

	/* star current zero  设置0值所在行列为true*/
	newStarMatrix[row + nOfRows*col] = true;

	/* find starred zero in current column 从startCol所在列开始找所在列中的第一个0所在的行，找到标星的退出*/
	starCol = col;
	for (starRow = 0; starRow<nOfRows; starRow++)
		if (starMatrix[starRow + nOfRows*starCol])
			break;

	while (starRow<nOfRows)//未找到所在列中的第一个0所在的行
	{
		/* unstar the starred zero 复位所在列中的第一个0所在的行*/
		newStarMatrix[starRow + nOfRows*starCol] = false;

		/* find primed zero in current row 找所在行中的第一个0所在的列*/
		primeRow = starRow;
		for (primeCol = 0; primeCol<nOfColumns; primeCol++)
			if (primeMatrix[primeRow + nOfRows*primeCol])
				break;

		/* star the primed zero 置位找到的替换的匹配位置：若第3列中第2行和第5行所交位置是0，找到第三列第二行中第一个0值的位置*/
		newStarMatrix[primeRow + nOfRows*primeCol] = true;

		/* find starred zero in current column */
		starCol = primeCol;
		for (starRow = 0; starRow<nOfRows; starRow++)
			if (starMatrix[starRow + nOfRows*starCol])
				break;
	}

	/* use temporary copy as new starMatrix */
	/* delete all primes, uncover all rows */
	for (n = 0; n<nOfElements; n++)
	{
		primeMatrix[n] = false;
		starMatrix[n] = newStarMatrix[n];//用newStarMatrix更新starMatrix，
	}
	for (n = 0; n<nOfRows; n++)//复位coveredRows，用于定位所在行
		coveredRows[n] = false;

	/* move to step 2a */
	step2a(assignment, distMatrix, starMatrix, newStarMatrix, primeMatrix, coveredColumns, coveredRows, nOfRows, nOfColumns, minDim);
}

/**********************
 * 计算：δ l = min u ∈ S , v ∉ T  {l(u)+l(v)−weight((u,v))}
找到所有未标星列中的最小值h，将所有未标星所在列都减去最小值，进入step3
————————————————
版权声明：本文为CSDN博主「图波列夫」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://blog.csdn.net/yiran103/article/details/103826367
 https://blog.csdn.net/u014754127/article/details/78086014
 * **********************************/
void HungarianAlgorithm::step5(int *assignment, double *distMatrix, bool *starMatrix, bool *newStarMatrix, bool *primeMatrix, bool *coveredColumns, bool *coveredRows, int nOfRows, int nOfColumns, int minDim)
{
	double h, value;
	int row, col;
	//std::cout << "hungarian debug:step5-in "<<endl;
	/* find smallest uncovered element h 找到所有未标星的列中的最小值*/
	h = DBL_MAX;
	for (row = 0; row<nOfRows; row++)
		if (!coveredRows[row])
			for (col = 0; col<nOfColumns; col++)
				if (!coveredColumns[col])
				{
					value = distMatrix[row + nOfRows*col];
					if (value < h)
						h = value;
				}
	//std::cout << "hungarian debug:step5-in2 "<<endl;
	/* add h to each covered row 在直线交叉处加上最小值*/
	for (row = 0; row<nOfRows; row++)
		if (coveredRows[row])//若距离矩阵行数小于列数，此处的coveredRows为空
			for (col = 0; col<nOfColumns; col++)
				distMatrix[row + nOfRows*col] += h;
	//std::cout << "hungarian debug:step5-in3 "<<endl;
	/* subtract h from each uncovered column 未标星所在列的每个元素减去最小值*/
	for (col = 0; col<nOfColumns; col++)
		if (!coveredColumns[col])
			for (row = 0; row<nOfRows; row++)
				distMatrix[row + nOfRows*col] -= h;
	//std::cout << "hungarian debug:step5-step3start "<<endl;
	/* move to step 3 */
	step3(assignment, distMatrix, starMatrix, newStarMatrix, primeMatrix, coveredColumns, coveredRows, nOfRows, nOfColumns, minDim);
}
