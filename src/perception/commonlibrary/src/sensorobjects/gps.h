/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-25 10:46:05
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-01-25 16:53:04
 * @FilePath     : gps.h
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-01-25 10:46:05
 */

#ifndef __GPS__H_
#define __GPS__H_
#include "objects.h"
#include "common_msgs/sensorgps.h"

namespace SENSOROBJECTS{
class SensorGPS: public SensorObjects<common_msgs::sensorgps>
{
private:
public:
    SensorGPS(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType);
    ~SensorGPS();
    // void preprocess(const long& curObjectFrameStamp, const int& synchroFlag);
    void run(const long& curObjectFrameStamp, const int& synchroFlag);
};




} // end namespace SENSOROBJECTS

#endif /* __GPS__H_ */