/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-01-10 14:27:03
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-01-11 12:05:44
 * @FilePath: /HongQi2/autodriving0928/src/perception/fusiontracking/src/objects.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __OBJECTS__
#define __OBJECTS__

#include <iostream>
#include <string>
#include <mutex>
#include <deque>
#include <map>

#include <eigen3/Eigen/Core>
#include <ros/ros.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>
#include <ros/callback_queue.h>

#include "common_msgs/sensorgps.h"
#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"

// #include "../../commonlibrary/src/common.h"
#include "../common.h"

using namespace std;
namespace SENSOROBJECTS{

template<typename T>
class SensorObjects{
    public:
        SensorObjects(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType);
        ~SensorObjects();
        std::string getSensorType();
		void setCallbackFunction(const boost::function<void(const T&)>& callbackFunction);
        void callbackFunction(const T& msg);
        virtual void timeSynchro(const long& curObjectFrameStamp, const int& synchroFlag);
		bool getCurrentData();
		virtual void sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source);

		Common m_common;
		ros::Publisher pub_ObjectBBX;
		std::mutex m_dataMutex;
		std::deque<T> m_SensorDataDeque;
		T m_curSensorData;

		ros::CallbackQueue m_callbackQueue;

    private:
		
        ros::NodeHandle m_nodeHandle;
        ros::Subscriber m_topicSubscriber;
        std::string m_topicName;
		int m_sensorType;
        
        
        

};




template<typename T>
SensorObjects<T>::SensorObjects(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType)
    :m_nodeHandle(nodeName), m_topicName(topicName), m_sensorType(sensorType){
    m_nodeHandle.setCallbackQueue(&m_callbackQueue);
	// 绑定回调函数
    this->setCallbackFunction(boost::bind(&SensorObjects<T>::callbackFunction, this, _1));
	
	// m_topicSubscriber = m_nodeHandle.subscribe<T>(
	// 	m_topicName, 10, [this](const boost::shared_ptr<const T>& msg) {
	// 		this->callbackFunction(*msg);
	// 	});
	std::string l_sensorName = "UNKNOWN";
	auto it  = COMMON::SensorTypeMap.find(m_sensorType);
	if(it != COMMON::SensorTypeMap.end())
		l_sensorName = it->second;
	pub_ObjectBBX = m_nodeHandle.advertise<visualization_msgs::MarkerArray>("/" + l_sensorName + "/" + l_sensorName + "_objectsBBX", 10);
}

template<typename T>
SensorObjects<T>::~SensorObjects(){};

template<typename T>
void SensorObjects<T>::setCallbackFunction(const boost::function<void(const T&)>& callbackFunction) {
    m_topicSubscriber = m_nodeHandle.subscribe<T>(m_topicName, 100, callbackFunction);
}

template <typename T>
void SensorObjects<T>::callbackFunction(const T& msg){
    std::lock_guard<std::mutex> lock(m_dataMutex);
    m_SensorDataDeque.push_back(msg);
	if(m_SensorDataDeque.size() > 100){
		m_SensorDataDeque.pop_front();
	}
	static long lastTimestamp, curTimestamp;
	static bool l_isFirstGetData = true;
	if(l_isFirstGetData){
		lastTimestamp = curTimestamp = msg.timestamp;
		l_isFirstGetData = false;
	}
	else{
		curTimestamp = msg.timestamp;
	}

	int timeDiff = curTimestamp - lastTimestamp;
	if(m_sensorType == COMMON::SensorType::OBU){
		std::cout<<std::setprecision(13) << "\nlastTimestamp: " << lastTimestamp << ", curTimestamp: " << curTimestamp << ", timeDiff(ms): " << timeDiff << std::endl;
		long rostime = ros::Time::now().toSec() * 1000;
		std::cout<<std::setprecision(13) << "\nros time: " << rostime << ",  ros time gap : " << rostime - curTimestamp << std::endl;
		for(int i = 0; i < m_SensorDataDeque.size(); i++){
			std::cout<<std::setprecision(13) << "\ni: "<< i<<" " << m_SensorDataDeque[i].timestamp * 0.001 << std::endl;
		}
		std::cout<<std::endl;
		std::cout << "OBU m_SensorDataDeque.size(): " << m_SensorDataDeque.size() << std::endl;
	}
	
	lastTimestamp = curTimestamp;
	if(timeDiff > 200)
		std::cout << FYEL("sensor lost data, please check: ") << "sensortype = " << getSensorType() << ", timeDiff(ms): " << timeDiff << std::endl;
	// cout<<"[v2i debug]SensorData size: "<< m_SensorDataDeque.size()<<endl;
}


/***
 * 20221027 lidar检测与传感器信息的时间同步 20220224 添加同步话题标志位
 * @tparam T 数据类型
 * @param curObjectFrameStamp 当前lidar检测时间
 * @param synchroFlag 同步标志位，1：lidar-gps，2：lidar-radar 3:lidar-obu
 */
template <typename T>
void SensorObjects<T>::timeSynchro(const long& curObjectFrameStamp, const int& synchroFlag){
    if(curObjectFrameStamp < 0){
        cout <<FYEL("WARN: curObjectFrameStamp < 0, please check") << endl;
    }

    if(synchroFlag < 0 || synchroFlag > 5){
        cout <<FYEL("WARN: undefined sensor type, please check") << endl;
    }


	std::lock_guard<std::mutex> dequeLock(m_dataMutex);
	if(m_SensorDataDeque.empty()) {
		// cout<<"\tmsgDeque empty,no need to syncchro\n";
		return;
	}
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	//找到容器中数据时间戳小于同步时间的最近索引
	int curMsgIndex = 0;
	int msgDequeSize = m_SensorDataDeque.size();
	for (int i = 0; i < msgDequeSize; ++i) {
		T curMsg = m_SensorDataDeque[i];
		if(curMsg.timestamp / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}
	
	
	if(curMsgIndex + 1 == m_SensorDataDeque.size()){//容器中数据时间戳都小于同步时间,只保留最后一个数据
		if(curMsgIndex > 0){//等于0的情况 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			while(curMsgIndex--){
				m_SensorDataDeque.pop_front();
			}
		}
	}
	else{
		if(curMsgIndex > 0){//小于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			if(abs(curFrameStamp - m_SensorDataDeque[curMsgIndex].timestamp / 1000.0) >//lidar前一帧
			   abs(curFrameStamp - m_SensorDataDeque[curMsgIndex + 1].timestamp / 1000.0)){//lidar后一帧
				curMsgIndex += 1;//取最近的一帧数据
			}
			while(curMsgIndex--){
				m_SensorDataDeque.pop_front();
			}
		}
	}
	float timeGap = curFrameStamp - m_SensorDataDeque.front().timestamp / 1000.0;
	static float minSynchroTime = FLT_MAX, maxSynchroTime = FLT_MIN;

    minSynchroTime = abs(minSynchroTime) < abs(timeGap) ? abs(minSynchroTime) : abs(timeGap);
    maxSynchroTime = abs(maxSynchroTime) > abs(timeGap) ? abs(maxSynchroTime) : abs(timeGap);
    cout<<std::setprecision(16)<<"\t时间差："<<timeGap<<", 同步传感器msg时间 = "<<m_SensorDataDeque.front().timestamp / 1000.0 <<", 被同步传感器（主）检测时间 ="
        <<curFrameStamp<<endl;
    cout<<std::setprecision(16)<<"\tabs(minSynchroTime)： "<<abs(minSynchroTime)<<", abs(maxSynchroTime) = "<<abs(maxSynchroTime) <<endl;
    //cout<<"\tgps Deque.size = "<<m_SensorDataDeque.size() <<endl;
	
}


/***
 * 20221027 lidar检测与传感器信息的时间同步 20220224 添加同步话题标志位
 * @tparam T 数据类型
 * @param msgDeque 需要同步的传感器（非主传感器）
 * @param curObjectFrameStamp 当前传感器时间时间
 * @param synchroFlag 同步标志位，1：lidar-gps，2：lidar-radar 3:lidar-obu
 */
template <typename M>
void timeSynchro(std::deque<M>& msgDeque,const long& curObjectFrameStamp, const int synchroFlag){
	std::mutex dequeMutex;
	std::lock_guard<std::mutex> dequeLock(dequeMutex);

	if(msgDeque.empty()) {
		// cout<<"\tmsgDeque empty,no need to syncchro\n";
		return;
	}
	cout <<"........objects msgDeque.size() = " << msgDeque.size() << endl;
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	int curMsgIndex = 0;
	for (int i = 0; i < msgDeque.size(); ++i) {
		M curMsg = msgDeque[i];
		if(curMsg.timestamp / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}

	// cout<<"......curMsgIndex = " << curMsgIndex <<"， msgDeque.size() = " << msgDeque.size() << endl;
	if(curMsgIndex + 1 == msgDeque.size()){ //20221014 针对GPS的时间戳都小于lidar时间戳的情况只保留最后一个GPS数据
		if(curMsgIndex > 0){//等于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	else{
		if(curMsgIndex > 0){//小于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			if(abs(curFrameStamp - msgDeque[curMsgIndex].timestamp / 1000.0) >//lidar前一帧
			   abs(curFrameStamp - msgDeque[curMsgIndex + 1].timestamp / 1000.0)){//lidar后一帧
				curMsgIndex += 1;//取最近的一帧数据 后一帧
			}
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	float timeGap = curFrameStamp - msgDeque.front().timestamp / 1000.0;
	static float minLidarGPSSynchroTime = FLT_MAX, maxLidarGPSSynchroTime = FLT_MIN,
			minLidarRadarSynchroTime = FLT_MAX, maxLidarRadarSynchroTime = FLT_MIN,
			minLidarOBUSynchroTime = FLT_MAX, maxLidarOBUSynchroTime = FLT_MIN,
			minLidarCloudObjectsSynchroTime = FLT_MAX, maxLidarCloudObjectsSynchroTime = FLT_MIN;
	if(synchroFlag == COMMON::SensorType::GPS){ // gps
		minLidarGPSSynchroTime = abs(minLidarGPSSynchroTime) < abs(timeGap) ? abs(minLidarGPSSynchroTime) : abs(timeGap);
		maxLidarGPSSynchroTime = abs(maxLidarGPSSynchroTime) > abs(timeGap) ? abs(maxLidarGPSSynchroTime) : abs(timeGap);
		cout<<std::setprecision(16)<<"\tlidar-gps时间差："<<timeGap<<", 当前容器msg时间 = "<<msgDeque.front().timestamp / 1000.0 <<", 当前传感器时间时间 ="
		    <<curFrameStamp<<endl;
		cout<<std::setprecision(16)<<"\tabs(minLidarGPSSynchroTime)： "<<abs(minLidarGPSSynchroTime)<<", abs(maxLidarGPSSynchroTime) = "<<abs(maxLidarGPSSynchroTime) <<endl;
		cout<<"\tgps Deque.size = "<<msgDeque.size() <<endl;
	}
	else if(synchroFlag == COMMON::SensorType::RADAR){ // radar
		minLidarRadarSynchroTime = abs(minLidarRadarSynchroTime) < abs(timeGap) ? abs(minLidarRadarSynchroTime) : abs(timeGap);
		maxLidarRadarSynchroTime = abs(maxLidarRadarSynchroTime) > abs(timeGap) ? abs(maxLidarRadarSynchroTime) : abs(timeGap);
		cout<<std::setprecision(16)<<"\tlidar-radar时间差："<<timeGap<<", 当前容器msg时间 = "<<msgDeque.front().timestamp / 1000.0 <<", 当前传感器时间时间 ="
		    <<curFrameStamp<<endl;
		cout<<std::setprecision(16)<<"\tabs(minLidarRadarSynchroTime)： "<<abs(minLidarRadarSynchroTime)<<", abs(maxLidarRadarSynchroTime) = "<<abs(maxLidarRadarSynchroTime) <<endl;
		cout<<"\tradar Deque.size = "<<msgDeque.size() <<endl;
	}
	else if (synchroFlag == COMMON::SensorType::OBU){ // obu
		// m_lidarOBUTimeGap = timeGap;
		minLidarOBUSynchroTime = abs(minLidarOBUSynchroTime) < abs(timeGap) ? abs(minLidarOBUSynchroTime) : abs(timeGap);
		maxLidarOBUSynchroTime = abs(maxLidarOBUSynchroTime) > abs(timeGap) ? abs(maxLidarOBUSynchroTime) : abs(timeGap);
		cout<<std::setprecision(16)<<"\tlidar-obu时间差："<<timeGap<<", 当前容器msg时间 = "<<msgDeque.front().timestamp / 1000.0 <<", 当前传感器时间时间 ="
		    <<curFrameStamp<<endl;
		cout<<std::setprecision(16)<<"\tabs(minLidarOBUSynchroTime)： "<<abs(minLidarOBUSynchroTime)<<", abs(maxLidarObusynchroTime) = "<<abs(maxLidarOBUSynchroTime) <<endl;
		cout<<"\tobu Deque.size = "<<msgDeque.size() <<endl;
	}
	else if (synchroFlag == COMMON::SensorType::CLOUDPLATFORM){ //
		minLidarCloudObjectsSynchroTime = abs(minLidarCloudObjectsSynchroTime) < abs(timeGap) ? abs(minLidarCloudObjectsSynchroTime) : abs(timeGap);
		maxLidarCloudObjectsSynchroTime = abs(maxLidarCloudObjectsSynchroTime) > abs(timeGap) ? abs(maxLidarCloudObjectsSynchroTime) : abs(timeGap);
		cout<<std::setprecision(16)<<"\tlidar-cloudSource时间差："<<timeGap<<", 当前容器msg时间 = "<<msgDeque.front().timestamp / 1000.0 <<", 当前传感器时间时间 ="
		    <<curFrameStamp<<endl;
		cout<<std::setprecision(16)<<"\tabs(minLidarCloudObjectsSynchroTime)： "<<abs(minLidarCloudObjectsSynchroTime)<<", abs(maxLidarCloudObjectsSynchroTime) = "<<abs(maxLidarCloudObjectsSynchroTime) <<endl;
		cout<<"\tcloudObject Deque.size = "<<msgDeque.size() <<endl;
	}
	else{
		cerr<<"synchroFlag not correct, please check: "<<synchroFlag <<endl;
	}


}



template <typename T>
bool SensorObjects<T>:: getCurrentData(){
	std::lock_guard<std::mutex> lock(m_dataMutex);
	if(!m_SensorDataDeque.empty()){
		m_curSensorData = m_SensorDataDeque.front(); //lidar原点FLU
		m_SensorDataDeque.pop_front();
		return true;
	}
	else{
		std::string l_sensorName = getSensorType();
		cout << FYEL("m_SensorDataDeque is empty, please check, sensortype = ") << l_sensorName << endl;
		return false;
	}
}

template <typename T>
std::string SensorObjects<T>::getSensorType(){
	std::string l_sensorName = "UNKNOWN";
	auto it  = COMMON::SensorTypeMap.find(m_sensorType);
	if(it != COMMON::SensorTypeMap.end())
		l_sensorName = it->second;
	return l_sensorName;
}

/***
 * 可视化目标msg，用于调试
 * @param msg_source 目标msg：carFrontRFU2CarFrontFLU
 */
template <typename T>
void SensorObjects<T>::sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source){
	std::string frameIDInfo = "car";

	visualization_msgs::MarkerArray objectsMarkerArray;

	visualization_msgs::Marker marker;
	marker.action=visualization_msgs::Marker::DELETEALL;
	objectsMarkerArray.markers.emplace_back(marker);

	for (int i=0; i<msg_source.obs.size(); i++){
		vector<float> color;
		if(int(msg_source.obs[i].classification) < class_color.size()){
			color = class_color[int(msg_source.obs[i].classification)];
		}
		else{
			color = {0.5, 0, 0};//僵尸车
		}

		visualization_msgs::Marker line_list_detect;
		double stamp = msg_source.timestamp / 1000.0;
		line_list_detect.header.frame_id  = frameIDInfo;
		//line_list_detect.header.stamp = ros::Time::now();
		line_list_detect.header.stamp = ros::Time().fromSec(stamp);//TODO  lidar时间戳 //ros::Time::now()
		line_list_detect.ns = "points_and_lines";
		line_list_detect.lifetime =ros::Duration();//0.1
		line_list_detect.action = visualization_msgs::Marker::ADD;
		line_list_detect.pose.orientation.w = 1.0;
		line_list_detect.id = i;
		line_list_detect.type = visualization_msgs::Marker::LINE_LIST;
		//line width
		line_list_detect.scale.x = 0.3;
		//line_list_detect.scale.y = 0.1;
		//line_list_detect.scale.z = 0.1;
		//color green
		line_list_detect.color.a = 1;//透明度
		if(m_sensorType == COMMON::SensorType::LIDARDETECTION || m_sensorType == COMMON::SensorType::V2ISHOW1){
			line_list_detect.color.r = 1;//color[0]; //检测颜色 淡红色
			line_list_detect.color.g = 0.5;//color[1];
			line_list_detect.color.b = 0.5;//color[2];
		}
		if(m_sensorType == COMMON::SensorType::LIDAR){
			line_list_detect.color.r = 1; //橙色
			line_list_detect.color.g = 0.5;
			line_list_detect.color.b = 0;
		}
		else if(m_sensorType == COMMON::SensorType::RADAR || m_sensorType == COMMON::SensorType::V2ISHOW2){
			line_list_detect.color.r = 0; //绿色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(m_sensorType == COMMON::SensorType::OBU || m_sensorType == COMMON::SensorType::V2ISHOW3){
			line_list_detect.color.r = 0; //蓝色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(m_sensorType == COMMON::SensorType::CLOUDPLATFORM || m_sensorType == COMMON::SensorType::V2ISHOW){
			line_list_detect.color.r = 1; //黄色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(m_sensorType == COMMON::SensorType::FUSION || m_sensorType == COMMON::SensorType::TRACKING){
			line_list_detect.color.r = 0; //青色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 1;
		}
		else if(m_sensorType == COMMON::SensorType::FUSIONAllOBJECTS){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(m_sensorType == COMMON::SensorType::V2IFUSION){
			line_list_detect.color.r = 0; //洋红色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0.5;
		}
		else if(m_sensorType == COMMON::SensorType::CAMERA){
			line_list_detect.color.r = 0; //绿色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else{

		}
		geometry_msgs::Point p0, p1, p2, p3;
		geometry_msgs::Point p4, p5, p6, p7;

		switch (m_sensorType) {
			case COMMON::SensorType::FUSION:
			case COMMON::SensorType::TRACKING:
			{
				p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::V2ISHOW1:
			{
				p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y + 1;		p0.z = msg_source.obs[i].points[0].z;
				p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y + 1;		p1.z = msg_source.obs[i].points[1].z;
				p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y + 1;		p2.z = msg_source.obs[i].points[2].z;
				p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y + 1;		p3.z = msg_source.obs[i].points[3].z;
				p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y + 1;		p4.z = msg_source.obs[i].points[4].z;
				p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y + 1;		p5.z = msg_source.obs[i].points[5].z;
				p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y + 1;		p6.z = msg_source.obs[i].points[6].z;
				p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y + 1;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::V2ISHOW2:
			{
				p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y + 1.5;		p0.z = msg_source.obs[i].points[0].z;
				p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y + 1.5;		p1.z = msg_source.obs[i].points[1].z;
				p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y + 1.5;		p2.z = msg_source.obs[i].points[2].z;
				p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y + 1.5;		p3.z = msg_source.obs[i].points[3].z;
				p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y + 1.5;		p4.z = msg_source.obs[i].points[4].z;
				p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y + 1.5;		p5.z = msg_source.obs[i].points[5].z;
				p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y + 1.5;		p6.z = msg_source.obs[i].points[6].z;
				p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y + 1.5;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::V2ISHOW3:
			{
				p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y -0.5;		p0.z = msg_source.obs[i].points[0].z;
				p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y -0.5;		p1.z = msg_source.obs[i].points[1].z;
				p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y -0.5;		p2.z = msg_source.obs[i].points[2].z;
				p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y -0.5;		p3.z = msg_source.obs[i].points[3].z;
				p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y -0.5;		p4.z = msg_source.obs[i].points[4].z;
				p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y -0.5;		p5.z = msg_source.obs[i].points[5].z;
				p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y -0.5;		p6.z = msg_source.obs[i].points[6].z;
				p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y -0.5;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			default:
			{
				p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
		}
		//bottom
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p1);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p2);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p3);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p0);
		//top
		line_list_detect.points.push_back(p4); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p5); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p6); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p7); line_list_detect.points.push_back(p4);
		//side
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p4);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p7);
		//direction
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p4);

		objectsMarkerArray.markers.push_back(line_list_detect);

		visualization_msgs::Marker text;
		text.header.frame_id = frameIDInfo;
		text.header.stamp = ros::Time().fromSec(stamp); //ros::Time::now()
		text.ns = "box";
		text.action = visualization_msgs::Marker::ADD;
		text.lifetime =ros::Duration();//0.1
		text.pose.orientation.w = 1;
		text.pose.position.x = msg_source.obs[i].x;
		text.pose.position.y = msg_source.obs[i].y;
		text.pose.position.z = msg_source.obs[i].z;
		text.id = i;
		text.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
		text.scale.z = 0.3;

		text.color.a = 1;//透明度
		text.color.r = 1;
		text.color.g = 1;
		text.color.b = 1;//字体为白色{1, 1, 1}

		//添加目标横纵向速度信息
		ostringstream str;
		str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << msg_source.obs[i].relspeedx;// + curCarSpeedVx_;
		std::string vxString = str.str();
		str.str("");//清空数据
		str <<  msg_source.obs[i].relspeedy;// + curCarSpeedVy_;
		std::string vyString = str.str();

		//添加跟踪类型
		//20221103 //0.初始目标 1.跟踪 2.lidar 3.lidar-radar-camera 4.融合中lidar-radar -20220908 20230111 简化
		// cout << "debug: value = " << (int)msg_source.obs[i].value <<  endl;
		int speedSourceValue = (int)msg_source.obs[i].value;
		std::string speedSource = speedSourceValue < v_speedSourceValue.size() ? v_speedSourceValue[speedSourceValue] : v_speedSourceValue[v_speedSourceValue.size() - 1];

		//添加目标运动状态
		//20220908 20230111 简化
		//std::string motionInfo = msg[i].motionInfo < v_motionInfo.size() ? v_motionInfo[msg[i].motionInfo] : v_motionInfo[v_motionInfo.size() - 1];
		std::string confidence = std::to_string(msg_source.obs[i].confidence);
		confidence = confidence.substr(0,4);

		string positionX = std::to_string(msg_source.obs[i].x);
		string positionY = std::to_string(msg_source.obs[i].y);
		// 显示：id
		// 第几次匹配成功-预测帧数
		// 类别-速度来源-运动信息
		// 横向速度-纵向速度
		// 置信度-驾驶意图-radarIndex-radarObjectID
		switch (m_sensorType) {
			case COMMON::SensorType::LIDAR:
			case COMMON::SensorType::LIDARDETECTION:
				text.text =
						"Lidar:\nX:" + positionX + "\nY:" + positionY
						+ "\nlabel:" + std::to_string(msg_source.obs[i].classification)
						+ "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
						+ "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
						+ "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::RADAR:
				text.text = "Radar:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::OBU:
				text.text = "OBU:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
				            + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::CLOUDPLATFORM:
				text.text = "Cloudplatform:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::FUSION:
			case COMMON::SensorType::TRACKING:
				text.text = "Fusion:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::FUSIONAllOBJECTS:
				text.text = "Fusion:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::V2IFUSION:
				text.text = "V2IFUSION000:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY;
				// + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				// + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				// + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::V2ISHOW1:
				text.text = "V2IFUSION111:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY;
				// + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				// + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				// + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::V2ISHOW2:
				text.text = "V2IFUSION222:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY;
				// + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				// + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				// + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::V2ISHOW3:
				text.text = "V2IFUSION333:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY;
				            // + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            // + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            // + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::CAMERA:
				text.text = "Camera:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY;
				            // + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            // + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            // + "\nconfidence:" + confidence;
				break;
			default:
				break;
		}


		objectsMarkerArray.markers.push_back(text);

	}

	pub_ObjectBBX.publish(objectsMarkerArray);
	objectsMarkerArray.markers.clear();

}


}//namespace SENSOROBJECTS

#endif