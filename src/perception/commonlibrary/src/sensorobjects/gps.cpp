/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-25 16:52:40
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-01-25 16:52:42
 * @FilePath     : gps.cpp
 * Copyright 2024 <PERSON>, All Rights Reserved. 
 * 2024-01-25 16:52:40
 */
#include "gps.h"

namespace SENSOROBJECTS{

SensorGPS::SensorGPS(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType)
:SensorObjects<common_msgs::sensorgps>(nodeName, topicName, sensorType)
{

}


SensorGPS::~SensorGPS(){

}


} // end namespace SENSOROBJEC