/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-04-08 18:37:30
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-05-29 16:15:17
 * @FilePath     : cameraobjects.h
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-04-08 18:37:30
 */
#include "objects.h"

namespace SENSOROBJECTS{

template<typename T>
class CameraObjects: public SensorObjects<T>
{
private:
    /* data */
public:
    CameraObjects(ros::NodeHandle& nodeHandle, const std::string& topicName, const int& sensorType);
    ~CameraObjects();

    void timeSynchro(std::deque<T>& msgDeque,const long& curObjectFrameStamp, const int synchroFlag);
    void preprocess();
	void timeSynchroBeforeCurrent(const long& curObjectFrameStamp, const int& synchroFlag);
};

template<typename T>
CameraObjects<T>::CameraObjects(ros::NodeHandle& nodeHandle, const std::string& topicName, const int& sensorType)
    : SensorObjects<T>(nodeHandle, topicName, sensorType)
{
}

template<typename T>
CameraObjects<T>::~CameraObjects()
{
}

template <typename T>
void CameraObjects<T>::timeSynchro(std::deque<T>& msgDeque,const long& curObjectFrameStamp, const int synchroFlag){
	std::mutex dequeMutex;
	std::lock_guard<std::mutex> dequeLock(dequeMutex);

	if(msgDeque.empty()) {
		// cout<<"\tmsgDeque empty,no need to syncchro\n";
		return;
	}
	cout <<"........camera msgDeque.size() = " << msgDeque.size() << endl;
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	int curMsgIndex = 0;
	for (int i = 0; i < msgDeque.size(); ++i) {
		T curMsg = msgDeque[i];
		if((curMsg.timestamp) / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}

	// cout<<"......curMsgIndex = " << curMsgIndex <<"， msgDeque.size() = " << msgDeque.size() << endl;
	if(curMsgIndex + 1 == msgDeque.size()){ //20221014 针对GPS的时间戳都小于lidar时间戳的情况只保留最后一个GPS数据
		if(curMsgIndex > 0){//等于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	else{
		if(curMsgIndex > 0){//小于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			if(abs(curFrameStamp - (msgDeque[curMsgIndex].timestamp) / 1000.0) >//lidar前一帧
			   abs(curFrameStamp - (msgDeque[curMsgIndex + 1].timestamp) / 1000.0)){//lidar后一帧
				curMsgIndex += 1;//取最近的一帧数据 后一帧
			}
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	float timeGap = curFrameStamp - (msgDeque.front().timestamp) / 1000.0;
	static float minLidarGPSSynchroTime = FLT_MAX, maxLidarGPSSynchroTime = FLT_MIN;
	
	minLidarGPSSynchroTime = abs(minLidarGPSSynchroTime) < abs(timeGap) ? abs(minLidarGPSSynchroTime) : abs(timeGap);
	maxLidarGPSSynchroTime = abs(maxLidarGPSSynchroTime) > abs(timeGap) ? abs(maxLidarGPSSynchroTime) : abs(timeGap);
	cout<<std::setprecision(16)<<"\tlidar-camera时间差："<<timeGap<<", 当前容器msg时间 = "<<(msgDeque.front().timestamp) / 1000.0 <<", 当前传感器时间时间 ="
		<<curFrameStamp<<endl;
	cout<<std::setprecision(16)<<"\tabs(minLidarCameraSynchroTime)： "<<abs(minLidarGPSSynchroTime)<<", abs(maxLidarCameraSynchroTime) = "<<abs(maxLidarGPSSynchroTime) <<endl;
	cout<<"\tcamera Deque.size = "<<msgDeque.size() <<endl;
	

}


template <typename T>
void CameraObjects<T>::preprocess(){
	vector<double> boxInfo(7);
	std::vector<common_msgs::sensorobject> l_vSensorobjectsToDelete;
	for(auto curSensorObjectIter = this->m_curSensorData.obs.begin(); curSensorObjectIter != this->m_curSensorData.obs.end(); ++curSensorObjectIter){
		float therateDegree = tanf(curSensorObjectIter->x / (float)(curSensorObjectIter->y + 1e-6)) * 180.0 / M_PI;
		bool l_isInFOV = abs(therateDegree) < 6.77; // 在范围外的目标位置不准
		if(!l_isInFOV){
			cout<<"x = "<<curSensorObjectIter->x<<", y = " <<curSensorObjectIter->y<<", therateDegree = " <<therateDegree<<endl;
			// common_msgs::sensorobject l_sensorobject = *curSensorObjectIter;
			l_vSensorobjectsToDelete.emplace_back(*curSensorObjectIter);
			continue;
		}

		float width = curSensorObjectIter->width, length = curSensorObjectIter->length;
		// curSensorObjectIter->width = length;
		// curSensorObjectIter->length = width;
		if(curSensorObjectIter->classification == COMMON::LidarDetectionClassification::Car){
			curSensorObjectIter->width = 1.8;//min(curSensorObjectIter->width, curSensorObjectIter->length);
			curSensorObjectIter->length = 4.5;//max(curSensorObjectIter->width, curSensorObjectIter->length);
		}
		else{
			curSensorObjectIter->width = min(curSensorObjectIter->width, curSensorObjectIter->length);
			curSensorObjectIter->length = max(curSensorObjectIter->width, curSensorObjectIter->length);
		}
		
		curSensorObjectIter->value = COMMON::SensorType::CAMERA;

		boxInfo = {curSensorObjectIter->x, curSensorObjectIter->y, curSensorObjectIter->z,
		                          curSensorObjectIter->length, curSensorObjectIter->width, curSensorObjectIter->height,
		                          curSensorObjectIter->azimuth};
		vector<vector<double>> eightCornerPoints = this->m_common.boxes_to_corners_3d(boxInfo);
		for(const auto& singleCornerPointVector : eightCornerPoints){
			common_msgs::point3d singleCornerPoint;
			singleCornerPoint.x = singleCornerPointVector[0];
			singleCornerPoint.y = singleCornerPointVector[1];
			singleCornerPoint.z = singleCornerPointVector[2];
			
			curSensorObjectIter->points.emplace_back(std::move(singleCornerPoint));
		}
	}

	cout <<FYEL("WARN: c------------------------------------------") << endl;
	// 删除相机可视范围内中心点不准区域的目标
	for(const auto& obj: l_vSensorobjectsToDelete){
		this->m_curSensorData.obs.erase(std::find(this->m_curSensorData.obs.begin(), this->m_curSensorData.obs.end(), obj));
	}

}

template <typename T>
void CameraObjects<T>::timeSynchroBeforeCurrent(const long& curObjectFrameStamp, const int& synchroFlag){
    if(curObjectFrameStamp < 0){
        cout <<FYEL("WARN: curObjectFrameStamp < 0, please check") << endl;
    }

    if(synchroFlag < 0 || synchroFlag > 19){
        cout <<FYEL("WARN: undefined sensor type, please check") << endl;
    }


	std::lock_guard<std::mutex> dequeLock(this->m_dataMutex);
	if(this->m_SensorDataDeque.empty()) {
		// cout<<"\tmsgDeque empty,no need to syncchro\n";
		return;
	}
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	//找到容器中数据时间戳小于同步时间的最近索引
	int curMsgIndex = 0;
	int msgDequeSize = this->m_SensorDataDeque.size();
	for (int i = 0; i < msgDequeSize; ++i) {
		common_msgs::sensorobjects curMsg = this->m_SensorDataDeque[i];
		if(curMsg.timestamp / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}

    while(curMsgIndex--){
        this->m_SensorDataDeque.pop_front();
    }
	
	float timeGap = curFrameStamp - this->m_SensorDataDeque.front().timestamp / 1000.0;
	static float minSynchroTime = FLT_MAX, maxSynchroTime = FLT_MIN;

    minSynchroTime = abs(minSynchroTime) < abs(timeGap) ? abs(minSynchroTime) : abs(timeGap);
    maxSynchroTime = abs(maxSynchroTime) > abs(timeGap) ? abs(maxSynchroTime) : abs(timeGap);
    cout<<std::setprecision(16)<<"\t时间差："<<timeGap<<", 同步传感器msg时间 = "<<this->m_SensorDataDeque.front().timestamp / 1000.0 <<", 被同步传感器（主）检测时间 ="
        <<curFrameStamp<<endl;
    cout<<std::setprecision(16)<<"\tabs(minSynchroTime)： "<<abs(minSynchroTime)<<", abs(maxSynchroTime) = "<<abs(maxSynchroTime) <<endl;
    //cout<<"\tgps Deque.size = "<<this->m_SensorDataDeque.size() <<endl;
	
}

}//end namespace SENSOROBJECTS

