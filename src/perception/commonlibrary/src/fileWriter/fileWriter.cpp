/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-10-18 10:22:40
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-10-18 14:23:35
 * @FilePath: /src/perception/commonlibrary/src/fileWriter/fileWriter.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "fileWriter.h"

FileWriter::FileWriter(const boost::shared_ptr<ConfigManager::ConfigManager> &pConfigManager):m_pConfigManager(pConfigManager){
     
}

FileWriter::~FileWriter(){
    m_file.close();
}

void FileWriter::createFile(const std::string& fileName){
    m_file.open(m_pConfigManager->m_saveTimeUseFilePath + "/" + fileName, ios::out | ios::app);
     if(!m_file.is_open()){
        std::cerr << "Failed to open file: " << std::endl;
    }
}

void FileWriter::writeHeader(const std::string& dataHeader){
    m_file << dataHeader;
}

void FileWriter::writeData(const std::string& data){
    m_file << data;
}
