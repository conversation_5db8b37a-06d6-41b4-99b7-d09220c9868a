/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-06-26 11:01:01
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-10-30 08:54:33
 * @FilePath     : logger.cpp
 * Copyright 2024 <PERSON><PERSON>, All Rights Reserved. 
 * 2024-06-26 11:01:01
 */
#include "logger.h"

Logger::Logger(const boost::shared_ptr<ConfigManager::ConfigManager>& configManager):m_pConfigManager(configManager)
{
    InitLogger();
}

Logger::~Logger(){
    UninitLogger();
}

void Logger::InitLogger(){
    if(spdlog::get("console") != nullptr){
        m_pLogger = spdlog::get("console");
        return;
    }

    spdlog::init_thread_pool(8192, 8);
    spdlog::flush_every(std::chrono::seconds(3));
    std::vector<spdlog::sink_ptr> sinkList;
    switch (m_pConfigManager->m_logOutputType)
    {
        case OutputType::File:
        {
            char* newFolderPath = createDirectoryWithDateAndGetPath(m_pConfigManager->m_logFilePath.c_str());
            if (newFolderPath) {
                printf("Created directory: %s\n", newFolderPath);
            }
            std::string currentTimeStr = getCurrentTimeString();
            std::string filePathName = m_pConfigManager->m_logFilePath + "/" + m_pConfigManager->m_logFileName;
            if (newFolderPath){
                filePathName = std::string(newFolderPath) + "/tracking_" + std::string(currentTimeStr) + ".log";
                std::replace_if(filePathName.begin(), filePathName.end(),
                        [](char c){ return c == ' ' || c == ':'; }, '-');
            }

            cout <<"directoryWithDate：" << newFolderPath << endl;
            cout <<"filePathName" << filePathName << endl;
            free(newFolderPath); // 释放返回的字符串指针

            auto rorateSink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(filePathName, 
                                                                                    1048576 * m_pConfigManager->m_logFileMaxSize, 
                                                                                    m_pConfigManager->m_logFileMaxNum);
            rorateSink->set_pattern("[%H:%M:%S] [%^%L%$] %v"); // [%s:%g:%#] [%!]
            rorateSink->set_level(spdlog::level::debug);
            sinkList.push_back(rorateSink);
        }break;
        case OutputType::Both:
        {
            char* newFolderPath = createDirectoryWithDateAndGetPath(m_pConfigManager->m_logFilePath.c_str());
            if (newFolderPath) {
                printf("Created directory: %s\n", newFolderPath);
            }
            std::string currentTimeStr = getCurrentTimeString();
            std::string filePathName = m_pConfigManager->m_logFilePath + "/" + m_pConfigManager->m_logFileName;
            if (newFolderPath){
                filePathName = std::string(newFolderPath) + "/tracking_" + std::string(currentTimeStr) + ".log";
                std::replace_if(filePathName.begin(), filePathName.end(),
                        [](char c){ return c == ' ' || c == ':'; }, '-');
            }

            cout <<"directoryWithDate：" << newFolderPath << endl;
            cout <<"filePathName" << filePathName << endl;
            free(newFolderPath); // 释放返回的字符串指针

            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            console_sink->set_pattern("[%H:%M:%S] [%^%L%$] %v"); // [%s:%g:%#] [%!]
            console_sink->set_level(spdlog::level::debug);
            sinkList.push_back(console_sink);
            
            auto rorateSink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(filePathName, 
                                                                                    1048576 * m_pConfigManager->m_logFileMaxSize, 
                                                                                    m_pConfigManager->m_logFileMaxNum);
            rorateSink->set_pattern("[%H:%M:%S] [%^%L%$] %v"); // [%s:%g:%#] [%!]
            rorateSink->set_level(spdlog::level::debug);
            sinkList.push_back(rorateSink);
        }break;
        case OutputType::Terminal:
        {  
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            console_sink->set_pattern("[%H:%M:%S] [%^%L%$] %v"); // [%s:%g:%#] [%!]
            console_sink->set_level(spdlog::level::debug);
            sinkList.push_back(console_sink);
        }
        default:
        {
            break;
        }
    }

    m_pLogger = std::make_shared<spdlog::logger>("console", begin(sinkList), end(sinkList));//同步日志
    // m_pLogger = std::make_shared<spdlog::async_logger>("console", begin(sinkList), end(sinkList), spdlog::thread_pool(),spdlog::async_overflow_policy::overrun_oldest); //异步日志
    m_pLogger->set_pattern("[%H:%M:%S] [%^%L%$] %v"); // [%s:%g:%#] [%!]
    m_pLogger->set_level(spdlog::level::debug);
    m_pLogger->flush_on(spdlog::level::warn);
    spdlog::register_logger(m_pLogger);
}

char* Logger::createDirectoryWithDateAndGetPath(const char* baseDir) {
    struct tm now;
    time_t t;

    time(&t);
    localtime_r(&t, &now); // 获取当前时间

    // 构造目录路径
    char dirPath[PATH_MAX+1];
    snprintf(dirPath, PATH_MAX, "%s/%04d-%02d-%02d", baseDir, now.tm_year + 1900, now.tm_mon + 1, now.tm_mday);

    // 检查目录是否存在，不存在则创建
    struct stat st;
    if (stat(dirPath, &st) == -1) {
        // 目录不存在
        if (mkdir(dirPath, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) == -1) {
            perror("Failed to create directory");
            return NULL; // 或者处理错误的其他方式
        }
    } else if (!S_ISDIR(st.st_mode)) {
        perror("Path exists but is not a directory");
        return NULL;
    }

    // 返回构造的绝对路径
    return strdup(dirPath); // 注意：需手动free返回的字符串以避免内存泄漏
}

std::string Logger::getCurrentTimeString() {
    time_t rawtime;
    struct tm * timeinfo;
    char buffer[PATH_MAX+1];

    // 获取当前时间
    time(&rawtime);
    // 将时间戳转换为本地时间
    timeinfo = localtime(&rawtime);

    // 使用strftime格式化时间到字符串
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);

    // 动态分配内存用于返回值，以避免使用局部变量的问题
    std::string timestamp(buffer);
    return timestamp;
}

void Logger::UninitLogger() {
  spdlog::drop_all();
}


std::shared_ptr<spdlog::logger>& Logger::getLogger(){
    m_pLogger = spdlog::get("console");
    return m_pLogger;
}