#ifndef SENSOROBJECTSVIEWER_H
#define SENSOROBJECTSVIEWER_H

#include <ros/ros.h>

#include <visualization_msgs/MarkerArray.h>
#include "common_msgs/sensorobjects.h"

#include "viewer.h"
#include "../common.h"

using namespace std;

namespace VISUALIZATION{

class SensorObjectsViewer: public Viewer<common_msgs::sensorobjects>
{
	public:
		Common m_cCommon;
		
		SensorObjectsViewer(ros::NodeHandle& nh, const std::string& pubTopicName, const int& showType);
		~SensorObjectsViewer();
		void visualize(const common_msgs::sensorobjects& objects);
	private:
};

} // namespace VISUALIZATION

#endif