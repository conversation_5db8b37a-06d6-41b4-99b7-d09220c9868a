/*
 * @Description: 
 * @Version: 2.0
 * @Autor: shuangquan han
 * @LastEditors: shuangquan han
 * @LastEditTime: 2022-11-11 14:16:37
 */
/********************************************************************************
* @author: shuangquan han
* @date: 2022/9/16 上午9:33
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_SENSORAXISTRANSFORMATION_H
#define SRC_SENSORAXISTRANSFORMATION_H
#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>

#include "../wgs84_utm.h"
using namespace std;

class SensorAxisTransformation{
public:
	//EIGEN_MAKE_ALIGNED_OPERATOR_NEW
	wgs84_utm m_wgs84_utm;
	
	SensorAxisTransformation();
	~SensorAxisTransformation(){}
	
	void setSelfCarEulerDegrees(const Eigen::Vector3d& selfCarEulerDegrees);
	Eigen::Vector3d getSelfCarEulerDegrees();
	void setSelfCarUTMPosition(const std::vector<double>& selfCarUTMPosition);
	
	void transINS570D_to_lidar80(const Eigen::Vector3d& inputPosition,Eigen::Vector3d& outputPosition);
	void utmAxis2CarrierAxis(const Eigen::Vector3d& positionUTM,const Eigen::Vector3d& eulerXYZDegree,Eigen::Vector3d& utmInCarrierAxis);
	void transformPoint(const Eigen::Vector3d& positionUTM,const Eigen::Vector3d& eulerXYZDegree,
						const Eigen::Vector3d& translation,const std::string& transformOrder, Eigen::Vector3d& outputPosition);
	void transformPoint(const Eigen::Vector3d& inputposition,
	                                              const Eigen::Vector3d& translation,const std::string& transformOrder,
	                                              Eigen::Vector3d& outputPosition);
	void transformPoint(const Eigen::Vector3f &inputposition, const Eigen::Quaternion<float> &quaternion,
						const Eigen::Vector3f &translation, Eigen::Vector3f &outputPosition);
						
	void transformPoint2utm(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
	                                                  const Eigen::Vector3d& translation,Eigen::Vector3d& outputPosition);
	void utm2NED(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
	                                         const Eigen::Vector3d& translation,
	                                         Eigen::Vector3d& outputPosition);
	Eigen::Vector3d utm2LidarAxis(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
	                                                        const Eigen::Vector3d& selfCarUTMPosition);
	Eigen::Vector3d lidar2UTMAxis(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
	                                                        const Eigen::Vector3d& selfCarUTMPosition);//20221024
	
	std::vector<double> bodyAxis2ENU(const Eigen::Vector3d& bodyAxisPosition,
	                                 const Eigen::Vector3d& selfCarHeadingEulerDegrees);
	std::vector<double> bodyAxis2ENU(const Eigen::Vector3d& bodyAxisPosition);
	
	std::vector<double> ENU2BodyAxis(const Eigen::Vector3d& ENUAxisPosition,
	                                                           const Eigen::Vector3d& selfCarHeadingEulerDegrees);
	std::vector<double> ENU2BodyAxis(const Eigen::Vector3d& ENUAxisPosition);
	
	std::vector<double> ENU2UTM(const Eigen::Vector3d& bodyAxisPosition,
	                                                      const Eigen::Vector3d& selfCarHeadingEulerDegrees,
	                                                      const std::vector<double>& selfCarUTMPosition);
	std::vector<double> ENU2UTM(const Eigen::Vector3d& bodyAxisPosition);
	
	void ENU2LLA(const Eigen::Vector3d& ENUPosition,
               const Eigen::Vector3d& selfCarHeadingEulerDegrees,
	             const std::vector<double>& selfCarUTMPosition,
	             int cityUTMCode, WGS84Corr& lla);
	
	void utm2CarBackRFU(const Eigen::Vector3d& inputUTMPosition,const Eigen::Vector3d& selfCarEulerXYZDegree,
	                                             const Eigen::Vector3d& selfCarUTMPosition, Eigen::Vector3d& objectPositionInCarBackFRU);
	
	std::vector<double> ENU2CarBackRFUAxis(const Eigen::Vector3d& curGPSEulerRPYAngleDegree, const Eigen::Vector3d& curGPSENUSpeed);
	std::vector<double> ENU2CarBackFLUAxis(const Eigen::Vector3d& curGPSEulerRPYAngleDegree, const Eigen::Vector3d& curGPSENUSpeed);
	std::vector<double> carBackRFUAxis2ENU(const Eigen::Vector3d& curGPSEulerRPYAngleDegree, const Eigen::Vector3d& carBackRFUSpeed);
	float CarBackRFUHeading2NorthClockwise(const float& objectHeadingRad, const float& carHeadingDegree);
	float NorthClockwise2CarBackRFU(const float& objectAngleDegreeInNorth_Clockwise, const float& carHeadingDegree);
	Eigen::Affine3f getTransformation(const double& anglex, const double& angley, const double& anglez, 
										const double& tranlationx, const double& tranlationy, const double& tranlationz);

private:
	Eigen::Vector3d m_selfCarEulerDegrees;
	std::vector<double> m_selfCarUTMPosition;
	Eigen::Matrix3d m_rotationFromZYX;
	Eigen::Matrix3d m_rotationFromZXY;
	Eigen::Matrix3d m_rotationFromZXY_speed;
};//EIGEN_ALIGN16


#endif //SRC_SENSORAXISTRANSFORMATION_H
