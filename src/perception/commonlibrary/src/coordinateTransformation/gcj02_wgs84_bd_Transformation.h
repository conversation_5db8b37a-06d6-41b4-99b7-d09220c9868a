/********************************************************************************
* @author: shuangquan han
* @date: 2022/5/23 下午3:07
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef RICHARDPROJECT_GCJ02_WGS84_BD_H
#define RICHARDPROJECT_GCJ02_WGS84_BD_H
#include <iostream>
#include <cmath>
#include "coordinateTransformation/wgs84_utm.h"
#include <iomanip>

namespace GCJ02_WGS84_BD_Transformation{
	
	class GCJ02_WGS84_BD_Transformation{
	public:
		/***
		 * 角度
		 * @param lon
		 * @param lat
		 * @return
		 */
		bool isInChina(double lon,double lat){
			return (72.004 <= lon <=137.8347 && 0.8293 <= lat <= 55.8271)?true: false;
		}
		
		double encrypt_transformLon(double x,double y){
			double ret = 300.0 + x +2*y+0.1* pow(x,2)+0.1*x*y+0.1* sqrt(fabs(x));
			ret += (20.0*sin(6*pi*x)+20.0*sin(2*pi*x))*2.0/3.0;
			ret += (20.0* sin(pi*x)+40*sin(pi*x/3.0))*2.0/3.0;
			ret += (150.0*sin(pi*x/12.0)+300*sin(pi*x/30.0))*2.0/3.0;
			return ret;
		}
		
		double encrypt_transformLat(double x,double y){
			double ret = -100 + 2.0 * x + 3.0 * y + 0.2 * pow(y,2) + 0.1 * x  * y + 0.2 *  sqrt(fabs(x));
			ret += (20.0* sin(6*pi*x)+20.0*sin(2*pi*x))*2.0/3.0;
			ret += (20.0* sin(pi*y)+40.0*sin(pi*y/3.0))*2.0/3.0;
			ret += (160.0*sin(pi*y/12.0)+320.0*sin(pi*y/30.0))*2.0/3.0;
			return ret;
		}
		
		/***
		 * 数据经纬度单位角度
		 * @param wgsLon
		 * @param wgsLat:degree
		 * @return
		 */
		std::pair<double,double> wgs2gcj(double wgsLonDegree,double wgsLatDegree){
			std::pair<double,double> encryptLonLat;
			if(!isInChina(wgsLonDegree,wgsLatDegree)){
				cerr<<"NOTE: lla is not in china.\n";
				return std::pair<double,double>{wgsLonDegree,wgsLatDegree};
			}
			
			double dLat = encrypt_transformLat(wgsLonDegree-105.0,wgsLatDegree-35.0);
			double dLon = encrypt_transformLon(wgsLonDegree-105.0,wgsLatDegree-35.0);
			double magic = sin(wgs84_utm::DegToRad(wgsLatDegree));
			magic = 1 - ee * pow(magic,2);
			double sqrtMagic = sqrt(magic);//W
			 dLat = (dLat*180.0)/((a*(1-ee))/(magic*sqrtMagic)*pi);//TODO  对一下 公式
			 dLon = (dLon*180.0)/(a/sqrtMagic*cos(wgs84_utm::DegToRad(wgsLatDegree))*pi);
			double gcjLat = wgsLatDegree + dLat;
			double gcjLon = wgsLonDegree + dLon;
			return std::pair<double,double>{gcjLon,gcjLat};
			
		}
		/***
		 * 输入输出均Degree
		 * @param gcjLon
		 * @param gcjLat
		 * @return
		 */
		std::pair<double,double> gcj2wgs(double gcjLonDegree,double gcjLatDegree){
			std::pair<double,double> g0 {gcjLonDegree,gcjLatDegree};
			std::pair<double,double> w0 =g0;
			std::pair<double,double> g1 = wgs2gcj(w0.first,w0.second);
			
			std::pair<double,double> w1 = std::pair<double,double>{w0.first-(g1.first-g0.first),w0.second-(g1.second-g0.second)};
			std::pair<double,double> delta = std::pair<double,double>{w1.first-w0.first,w1.second-w0.second};
			
			
			while(abs(delta.first)>=1e-6 || abs(delta.second)>=1e-6){//1e-9对应1mm
				w0 = w1;
				g1 = wgs2gcj(w0.first,w0.second);
				//w1 = w0-(g1-g0)
				w1 = std::pair<double,double>{w0.first-(g1.first-g0.first),w0.second-(g1.second-g0.second)};
				//delta = w1-w0
				delta = std::pair<double,double>{w1.first-w0.first,w1.second-w0.second};
			}
			//cout<<setprecision(12)<<"gcj02: "<<gcjLonDegree<<" "<<gcjLatDegree<<",\t wgs84: "<<w1.first<<" "<<w1.second<<endl;
			return w1;
		}
		
		std::pair<double,double> gcj2bd(double gcjLon,double   gcjLat){
			double z = sqrt(pow(gcjLon,2)+ pow(gcjLat,2)+0.00002* sin(gcjLat*pi*3000.0/180.0));
			double theta = atan2(gcjLat,gcjLon)+0.000003* cos(gcjLon*pi*3000.0/180.0);
			double bdLon = z*cos(theta)+0.0065;
			double bdLat = z*sin(theta)+0.006;
			return std::pair<double,double>{bdLon,bdLat};
		}
		
		std::pair<double,double> bd2gcj(double bdLon,double bdLat){
			double x= bdLon-0.0065;
			double y=bdLat-0.006;
			double z = sqrt(pow(x,2)+ pow(y,2)-0.00002*sin(y*pi*3000.0/180.0));
			double theta = atan2(y,x)-0.000003* cos(x*pi*3000.0*180.0);
			double gcjLon = z* cos(theta);
			double gcjLat = z* sin(theta);
			return std::pair<double,double>{gcjLon,gcjLat};
		}
		
		std::pair<double,double> wgs2bd(double wgsLonDegree,double wgsLatDegree){
			std::pair<double,double> gcj = wgs2gcj(wgsLonDegree,wgsLatDegree);
			return gcj2bd(gcj.first,gcj.second);
		}
		
		std::pair<double,double> bd2wgs(double bdLon,double bdLat){
			std::pair<double,double> gcj = bd2gcj(bdLon,bdLat);
			return gcj2wgs(gcj.first,gcj.second);
		}
		/***
		 * 验证wgs84  gcj  wgs84
		 * @param lon
		 * @param lat
		 */
		void runWgs2Gcj(double lon=0.0,double lat=0.0){
			double utmX = 439266.187195;
			double utmY = 4433296.873291;
			wgs84_utm::WGS84Corr lla;
			wgs84_utm::UTMXYToLatLon(utmX,utmY,50, false,lla);
		
			std::cout <<std::setprecision(12)<< "wgs84 log lat =" << wgs84_utm::RadToDeg(lla.log)<< " " << wgs84_utm::RadToDeg(lla.lat)<< std::endl;
			std::pair<double,double> gcjData =  wgs2gcj(wgs84_utm::RadToDeg(lla.log),wgs84_utm::RadToDeg(lla.lat));//return degree
			cout<<std::setprecision(12)<<"gcjData:\t"<<gcjData.first<<" "<<gcjData.second<<endl;
			
			std::pair<double,double> wgsDATA  = gcj2wgs(gcjData.first,gcjData.second);
			cout<<std::setprecision(12)<<"wgsDATA:\t"<<wgsDATA.first<<" "<<wgsDATA.second<<endl;
			
			//std::pair<double,double> wgsDATA2  = gcj2wgs(116.294074164497,40.0489721679);
			//cout<<std::setprecision(12)<<"gaode wgsDATA:\t"<<wgsDATA2.first<<" "<<wgsDATA2.second<<endl;
		}
		
		
	private:
		double a = 6378245.0;//长半轴
		double f = 1/298.3;
		double b = a * (1-f);
		double ee = 1 - pow(b,2)/ pow(a,2);//椭圆扁率
		
		//double ee = 0.00669342162296594323  # 扁率
		//double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
		//double pi = 3.1415926535897932384626;  # π
		//double a = 6378245.0 ; # 长半轴
		//double ee = 0.00669342162296594323;  # 扁率
		//
		double xPI = M_PI * 3000.0 / 180.0;
		double pi = M_PI;
	};
	

}//GCJ02_WGS84_BD_Transformation
#endif //RICHARDPROJECT_GCJ02_WGS84_BD_H
