/********************************************************************************
* @author: shuang<PERSON><PERSON> han
* @date: 2022/5/23 上午9:46
* @version: 1.0
* @description:
********************************************************************************/

//#pragma once

#include <iostream>
#include <cmath>
#include "wgs84_utm.h"

/* WGS-84 地球椭球模型常数 */
#define  sm_a  6378137.0   // 地球长半轴半径
#define  sm_b  6356752.314 // 地球短半轴半径
#define  sm_EccSquared  6.69437999013e-03
#define  UTMScaleFactor  0.9996 // UTM系数


	///* UTM坐标与WGS-84坐标系的相互转换
	///* 输入为坐标值，输出为结构体tagUTMCorr或tagWGS84Corr

	/*
	* DegToRad
	*
	* 角度转为弧度
	*
	*/
	double wgs84_utm::DegToRad(double deg)//inline
	{
		return (deg / 180.0 * M_PI);
	}
	
	/*
	* RadToDeg
	*
	* 弧度转为角度
	*
	*/
	inline double wgs84_utm::RadToDeg(double rad)
	{
		return (rad / M_PI * 180.0);
	}
	
	/*
	* ArcLengthOfMeridian
	*
	* 计算赤道（0°纬线）到输入纬度值的弧长
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	* GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*     phi - Latitude，输入的纬度值，单位：弧度
	*
	* Returns:
	*     该纬度到赤道（0°纬线）的椭球面弧长，单位：m
	*
	*/
	double wgs84_utm::ArcLengthOfMeridian(double phi)
	{
		double alpha, beta, gamma, delta, epsilon, n;
		double result;
		
		/* Precalculate n */
		n = (sm_a - sm_b) / (sm_a + sm_b);
		
		/* Precalculate alpha */
		alpha = ((sm_a + sm_b) / 2.0) * (1.0 + (pow(n, 2.0) / 4.0) + (pow(n, 4.0) / 64.0));
		
		/* Precalculate beta */
		beta = (-3.0 * n / 2.0) + (9.0 * pow(n, 3.0) / 16.0) + (-3.0 * pow(n, 5.0) / 32.0);
		
		/* Precalculate gamma */
		gamma = (15.0 * pow(n, 2.0) / 16.0) + (-15.0 * pow(n, 4.0) / 32.0);
		
		/* Precalculate delta */
		delta = (-35.0 * pow(n, 3.0) / 48.0) + (105.0 * pow(n, 5.0) / 256.0);
		
		/* Precalculate epsilon */
		epsilon = (315.0 * pow(n, 4.0) / 512.0);
		
		/* Now calculate the sum of the series and return */
		result = alpha * (phi + (beta * sin(2.0 * phi)) + (gamma * sin(4.0 * phi)) + (delta * sin(6.0 * phi)) + (epsilon * sin(8.0 * phi)));
		
		return result;
	}
	
	/*
	* UTMCentralMeridian
	*
	* 计算给定UTM区域（Zone）中央经线的经度。（弧度）
	* UTM坐标系按经度将全球分为60个投影带
	*
	* Inputs:
	*     zone - 指定UTM区域编号, 范围 [1,60].
	*
	* Returns:
	*   给定UTM区域的中心经度，以弧度为单位；
	*   若输入超过有效范围（<0 or >60），返回0.
	*
	*/
	inline double wgs84_utm::UTMCentralMeridian(int zone)
	{
		return DegToRad(-183.0 + (zone * 6.0));
	}
	
	/*
	* FootpointLatitude
	*
	* 计算将TM坐标（Transverse Mercator，横轴墨卡托投影坐标，注意与UTM不同）转换为WGS-84坐标时使用的脚点纬度。
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	*   GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*   y - TM的北向坐标，单位：m
	*
	* Returns:
	*   脚点纬度，单位：弧度
	*
	*/
	double wgs84_utm::FootpointLatitude(double y)
	{
		double y_, alpha_, beta_, gamma_, delta_, epsilon_, n;
		double result;
		
		/* Precalculate n (Eq. 10.18) */
		n = (sm_a - sm_b) / (sm_a + sm_b);
		
		/* Precalculate alpha_ (Eq. 10.22) */
		/* (Same as alpha in Eq. 10.17) */
		alpha_ = ((sm_a + sm_b) / 2.0) * (1 + (pow(n, 2.0) / 4) + (pow(n, 4.0) / 64));
		
		/* Precalculate y_ (Eq. 10.23) */
		y_ = y / alpha_;
		
		/* Precalculate beta_ (Eq. 10.22) */
		beta_ = (3.0 * n / 2.0) + (-27.0 * pow(n, 3.0) / 32.0) + (269.0 * pow(n, 5.0) / 512.0);
		
		/* Precalculate gamma_ (Eq. 10.22) */
		gamma_ = (21.0 * pow(n, 2.0) / 16.0) + (-55.0 * pow(n, 4.0) / 32.0);
		
		/* Precalculate delta_ (Eq. 10.22) */
		delta_ = (151.0 * pow(n, 3.0) / 96.0) + (-417.0 * pow(n, 5.0) / 128.0);
		
		/* Precalculate epsilon_ (Eq. 10.22) */
		epsilon_ = (1097.0 * pow(n, 4.0) / 512.0);
		
		/* Now calculate the sum of the series (Eq. 10.21) */
		result = y_ + (beta_ * sin(2.0 * y_)) + (gamma_ * sin(4.0 * y_)) + (delta_ * sin(6.0 * y_)) + (epsilon_ * sin(8.0 * y_));
		
		return result;
	}
	
	/*
	* MapLatLonToXY
	*
	* 将WGS-84坐标转为TM坐标（Transverse Mercator，横轴墨卡托投影坐标，注意与UTM不同，TM与UTM之间相差一个比例因子）
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	* GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*    phi - 该点的纬度，单位：弧度
	*    lambda - 该点经度，单位：弧度
	*    lambda0 - 该区域中央经线的经度，单位：弧度
	*
	* Outputs:
	*    xy - 该点的TM坐标，单位是m
	*
	* Returns:
	*    The function does not return a value.
	*
	*/
	void wgs84_utm::MapLatLonToXY(double phi, double lambda, double lambda0, UTMCoor &xy)
	{
		double N, nu2, ep2, t, t2, l;
		double l3coef, l4coef, l5coef, l6coef, l7coef, l8coef;
		double tmp;
		
		/* Precalculate ep2 */
		ep2 = (pow(sm_a, 2.0) - pow(sm_b, 2.0)) / pow(sm_b, 2.0);
		
		/* Precalculate nu2 */
		nu2 = ep2 * pow(cos(phi), 2.0);
		
		/* Precalculate N */
		N = pow(sm_a, 2.0) / (sm_b * sqrt(1 + nu2));
		
		/* Precalculate t */
		t = tan(phi);
		t2 = t * t;
		tmp = (t2 * t2 * t2) - pow(t, 6.0);
		
		/* Precalculate l */
		l = lambda - lambda0;
		
		/* Precalculate coefficients for l**n in the equations below
		so a normal human being can read the expressions for easting
		and northing
		-- l**1 and l**2 have coefficients of 1.0 */
		l3coef = 1.0 - t2 + nu2;
		
		l4coef = 5.0 - t2 + 9 * nu2 + 4.0 * (nu2 * nu2);
		
		l5coef = 5.0 - 18.0 * t2 + (t2 * t2) + 14.0 * nu2 - 58.0 * t2 * nu2;
		
		l6coef = 61.0 - 58.0 * t2 + (t2 * t2) + 270.0 * nu2 - 330.0 * t2 * nu2;
		
		l7coef = 61.0 - 479.0 * t2 + 179.0 * (t2 * t2) - (t2 * t2 * t2);
		
		l8coef = 1385.0 - 3111.0 * t2 + 543.0 * (t2 * t2) - (t2 * t2 * t2);
		
		/* Calculate easting (x) */
		xy.x = N * cos(phi) * l + (N / 6.0 * pow(cos(phi), 3.0) * l3coef * pow(l, 3.0)) + (N / 120.0 * pow(cos(phi), 5.0) * l5coef * pow(l, 5.0)) + (N / 5040.0 * pow(cos(phi), 7.0) * l7coef * pow(l, 7.0));
		
		/* Calculate northing (y) */
		xy.y = ArcLengthOfMeridian(phi) + (t / 2.0 * N * pow(cos(phi), 2.0) * pow(l, 2.0)) + (t / 24.0 * N * pow(cos(phi), 4.0) * l4coef * pow(l, 4.0)) + (t / 720.0 * N * pow(cos(phi), 6.0) * l6coef * pow(l, 6.0)) + (t / 40320.0 * N * pow(cos(phi), 8.0) * l8coef * pow(l, 8.0));
	}
	
	/*
	* MapXYToLatLon
	*
	* 将TM坐标（Transverse Mercator，横轴墨卡托投影坐标，注意与UTM不同，TM与UTM之间相差一个比例因子）中的x,y坐标转换为WGS-84坐标
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	*   GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*   x - 该点的TM坐标的x值，单位：m
	*   y - 该点的TM坐标的y值，单位：m
	*   lambda0 - 该区域中央经线的经度，单位：弧度
	*
	* Outputs:
	*   philambda - 该点的WGS-84坐标，单位：弧度
	*
	* Returns:
	*   The function does not return a value.
	*
	* Remarks:
	*   The local variables Nf, nuf2, tf, and tf2 serve the same purpose as
	*   N, nu2, t, and t2 in MapLatLonToXY, but they are computed with respect
	*   to the footpoint latitude phif.
	*
	*   x1frac, x2frac, x2poly, x3poly, etc. are to enhance readability and
	*   to optimize computations.
	*
	*/
	void wgs84_utm::MapXYToLatLon(double x, double y, double lambda0, WGS84Corr &philambda)
	{
		double phif, Nf, Nfpow, nuf2, ep2, tf, tf2, tf4, cf;
		double x1frac, x2frac, x3frac, x4frac, x5frac, x6frac, x7frac, x8frac;
		double x2poly, x3poly, x4poly, x5poly, x6poly, x7poly, x8poly;
		
		/* Get the value of phif, the footpoint latitude. */
		phif = FootpointLatitude(y);
		
		/* Precalculate ep2 */
		ep2 = (pow(sm_a, 2.0) - pow(sm_b, 2.0)) / pow(sm_b, 2.0);
		
		/* Precalculate cos (phif) */
		cf = cos(phif);
		
		/* Precalculate nuf2 */
		nuf2 = ep2 * pow(cf, 2.0);
		
		/* Precalculate Nf and initialize Nfpow */
		Nf = pow(sm_a, 2.0) / (sm_b * sqrt(1 + nuf2));
		Nfpow = Nf;
		
		/* Precalculate tf */
		tf = tan(phif);
		tf2 = tf * tf;
		tf4 = tf2 * tf2;
		
		/* Precalculate fractional coefficients for x**n in the equations
		below to simplify the expressions for latitude and longitude. */
		x1frac = 1.0 / (Nfpow * cf);
		
		Nfpow *= Nf; /* now equals Nf**2) */
		x2frac = tf / (2.0 * Nfpow);
		
		Nfpow *= Nf; /* now equals Nf**3) */
		x3frac = 1.0 / (6.0 * Nfpow * cf);
		
		Nfpow *= Nf; /* now equals Nf**4) */
		x4frac = tf / (24.0 * Nfpow);
		
		Nfpow *= Nf; /* now equals Nf**5) */
		x5frac = 1.0 / (120.0 * Nfpow * cf);
		
		Nfpow *= Nf; /* now equals Nf**6) */
		x6frac = tf / (720.0 * Nfpow);
		
		Nfpow *= Nf; /* now equals Nf**7) */
		x7frac = 1.0 / (5040.0 * Nfpow * cf);
		
		Nfpow *= Nf; /* now equals Nf**8) */
		x8frac = tf / (40320.0 * Nfpow);
		
		/* Precalculate polynomial coefficients for x**n.
		-- x**1 does not have a polynomial coefficient. */
		x2poly = -1.0 - nuf2;
		
		x3poly = -1.0 - 2 * tf2 - nuf2;
		
		x4poly = 5.0 + 3.0 * tf2 + 6.0 * nuf2 - 6.0 * tf2 * nuf2 - 3.0 * (nuf2 * nuf2) - 9.0 * tf2 * (nuf2 * nuf2);
		
		x5poly = 5.0 + 28.0 * tf2 + 24.0 * tf4 + 6.0 * nuf2 + 8.0 * tf2 * nuf2;
		
		x6poly = -61.0 - 90.0 * tf2 - 45.0 * tf4 - 107.0 * nuf2 + 162.0 * tf2 * nuf2;
		
		x7poly = -61.0 - 662.0 * tf2 - 1320.0 * tf4 - 720.0 * (tf4 * tf2);
		
		x8poly = 1385.0 + 3633.0 * tf2 + 4095.0 * tf4 + 1575 * (tf4 * tf2);
		
		/* Calculate latitude */
		philambda.lat = phif + x2frac * x2poly * (x * x) + x4frac * x4poly * pow(x, 4.0) + x6frac * x6poly * pow(x, 6.0) + x8frac * x8poly * pow(x, 8.0);
		
		/* Calculate longitude */
		philambda.log = lambda0 + x1frac * x + x3frac * x3poly * pow(x, 3.0) + x5frac * x5poly * pow(x, 5.0) + x7frac * x7poly * pow(x, 7.0);
	}
	
	/*
	* LatLonToUTMXY
	*
	* 将WGS-84坐标转换为UTM坐标（Universal Transverse Mercator，通用横轴墨卡托投影）中的x和y。
	*
	* Inputs:
	*   lat - 该点的纬度，单位：弧度
	*   lon - 该点的经度，单位：弧度
	*   zone - 该点所在的UTM区域（Zone），范围[1, 60]
	*   注意：***如果zone小于1或大于60，程序将根据lon的值确定合适的zone。
	*
	* Outputs:
	*   xy - 该点的UTM坐标
	*
	* Returns:
	*   void
	*
	*/
	void wgs84_utm::LatLonToUTMXY(double lat, double lon, UTMCoor &xy)
	{
		int zone = (int)((lon * 180.0/M_PI + 180.0) / 6.0) + 1;
		MapLatLonToXY(lat, lon, UTMCentralMeridian(zone), xy);
		
		/* Adjust easting and northing for UTM system. */
		xy.x = xy.x * UTMScaleFactor + 500000.0;
		xy.y = xy.y * UTMScaleFactor;
		if (xy.y < 0.0)
			xy.y += 10000000.0;
		
		//cout<<"\tutm wgs84： "<<std::setprecision(12)<< xy.x<<" "<<xy.y<<endl;
	}

	/***
	* 经纬度转utm坐标
	* @param longitude 经度 角度
	* @param latitude 维度 角度
	* @return utm坐标
	*/
	std::vector<double> wgs84_utm::llhDegree2UTM(const double& longitudeDegree,const double& latitudeDegree){
		UTMCoor utm;
		double lat = DegToRad(latitudeDegree);
		double lon = DegToRad(longitudeDegree);
		LatLonToUTMXY(lat,lon,utm);
		std::vector<double> utmXYOrder{utm.x,utm.y};
		return utmXYOrder;
	}

	/*************************************************
	Function:		// LatLonToLocalXY
	Description: 	// 经纬度转车辆坐标系局部xy
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 无
	Output: 		// 无
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void wgs84_utm::LatLonToLocalXY(const double& selfCarLonDegree, const double& selfCarLatDegree,
	                                const double& selfCarHeadingDegree,
	                                const double& objectLonDegree, const double& objectLatDegree,
	                                double& carBackRFU_x, double& carBackRFU_y)
	{
		double sin_lat1 = sin(selfCarLatDegree * M_PI / 180.0);     	// sin of start lat
		double cos_lat1 = cos(selfCarLatDegree * M_PI / 180.0); 	// sec of start lat
		double square_e = e_1 * e_1;
		double square_sin_lat1 = sin_lat1 * sin_lat1;
		
		double R_n = R_e / (sqrt(1 - square_e * square_sin_lat1));   				// 卯酉面等效曲率半径 (lon1, lat1)
		double R_m = (R_n * (1 - square_e)) / (1 - square_e * square_sin_lat1);   	// 子午面等效曲率半径 (lon1, lat1)
		
		double gx = (objectLonDegree - selfCarLonDegree) * M_PI / 180.0 * R_n * cos_lat1;
		double gy = (objectLatDegree - selfCarLatDegree) * M_PI / 180.0 * R_m;
		
		double temp_angle = selfCarHeadingDegree * M_PI / 180.0;
		carBackRFU_x = gx * cos(temp_angle) - gy * sin(temp_angle);
		carBackRFU_y = gx * sin(temp_angle) + gy * cos(temp_angle);
	}
	
	/*
	* UTMXYToLatLon
	*
	* 将UTM坐标系的x y转换为WGS-84坐标
	*
	* Inputs:
	*	x - 该点的UTM坐标x值，单位：m
	*	y - 该点的UTM坐标y值，单位：m
	*	zone - 该点所在的UTM区域（Zone）
	*	southhemi - True：该点在南半球
	*               false 北半球
	*
	* Outputs:
	*	latlon - 该点的WGS-84坐标  单位:弧度制
	*
	* Returns:
	*	The function does not return a value.
	*
	*/
	void wgs84_utm::UTMXYToLatLon(double x, double y, int zone, bool southhemi, WGS84Corr &latlon)
	{
		double cmeridian;
		
		x -= 500000.0;
		x /= UTMScaleFactor;
		
		/* If in southern hemisphere, adjust y accordingly. */
		if (southhemi)
			y -= 10000000.0;
		
		y /= UTMScaleFactor;
		
		cmeridian = UTMCentralMeridian(zone);
		MapXYToLatLon(x, y, cmeridian, latlon);
	}
	
	void wgs84_utm::runLatLonToUTMXY(double lon,double lat,tagUTMCorr& utm){
		
		LatLonToUTMXY(lat / 180.0 * M_PI, lon / 180.0 * M_PI, utm);
		
	}





/***
 * 根据自车的经纬度转到UTM坐标
 * @param gpsMsg
 * @return utm坐标
 */
std::vector<double> wgs84_utm::getUTMPosition(const double& lontitudeDegree, const double& latitudeDegree, const double& altitudeDegree){
	tagUTMCorr utm;
	LatLonToUTMXY(latitudeDegree / 180.0 * M_PI, lontitudeDegree / 180.0 * M_PI, utm);
	//std::cout << "INFO: utm.x utm.y =" << std::setprecision(18) << utm.x << " " << utm.y << std::endl;
	std::vector<double> elfCatUTMResult{utm.x,utm.y,altitudeDegree};
	return elfCatUTMResult;
}



/***
 * 获取UTM编号
 * @param lontitudeDegree
 * @return
 */
int wgs84_utm::getUTMCode(double lontitudeDegree) {
	int zone = (int) ((lontitudeDegree + 180.0) / 6.0) + 1;
	return zone;
}

	/*
	* utm2LLA
	*
	* 将UTM坐标系的x y转换为WGS-84坐标
	*
	* Inputs:
	*	x - 该点的UTM坐标x值，单位：m
	*	y - 该点的UTM坐标y值，单位：m
	*	zone - 该点所在的UTM区域（Zone）
	*	southhemi - True：该点在南半球
	*               false 北半球
	*
	* Outputs:
	*	v_lonlat - 该点的WGS-84坐标  单位:角度制
	*
	* Returns:
	*	The function does not return a value.
	*
	*/
void wgs84_utm::utm2LLADegree(double utm_x, double utm_y, int zone, bool southhemi, std::vector<double>& v_lonlat){
	WGS84Corr latlon;
	UTMXYToLatLon(utm_x, utm_y, zone, southhemi, latlon);
	v_lonlat.clear();
	v_lonlat.push_back(latlon.log * 180.0 / M_PI);
	v_lonlat.push_back(latlon.lat * 180.0 / M_PI);
}