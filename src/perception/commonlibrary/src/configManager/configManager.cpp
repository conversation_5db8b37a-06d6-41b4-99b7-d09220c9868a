/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-26 16:13:12
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-10-18 14:23:13
 * @FilePath     : configManager.cpp
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-01-26 16:13:12
 */
#include "configManager.h"
#include "../common.h"
#include "../logger/logger.h"

namespace ConfigManager{


ConfigManager::ConfigManager(const std::string yamlPath){
    try{
        m_configManager = YAML::LoadFile(yamlPath);
        std::cout << "read yaml config success." << std::endl;
        readParam();
    }
    catch (YAML::BadFile &e){
        std::cout << FRED("Error: load config paramater error!" )<< std::endl;
    }
}

ConfigManager::~ConfigManager()
{
}


void ConfigManager::readParam(){
    m_hdMapName = "HDMap-ruanjianyuan.png";
    m_hdMapLongitude = 116.279401146;
	m_hdMapLatitude = 40.053995788;
    m_cityUTMCode = 50;
    m_carNumber = 2;
    
    m_isUseHDMap = false;
    m_isUseRosBag = false;
	m_isSimulateMode = false;
	m_bdebug = 0;
	m_radarAsObjectDistance = 0;
	m_isSaveObjectInfoCSV = false;
	m_isUseRadarObjects = false;
	m_isEvaluateTracking = false;
	m_isUseRadarObjects = false;
    

	m_hdMapName = m_configManager["hdMapName"].as<std::string>();
    m_hdMapLongitude = m_configManager["hdMapLongitude"].as<double>();
    m_hdMapLatitude = m_configManager["hdMapLatitude"].as<double>();
    m_cityUTMCode = m_configManager["cityUTMCode"].as<int>();
    m_carNumber = m_configManager["carNumber"].as<int>();
    m_isUseHDMap = m_configManager["isUseHDMap"].as<bool>();

    m_isUseRosBag = m_configManager["isUseRosBag"].as<bool>();
    m_isSimulateMode = m_configManager["isSimulateMode"].as<bool>();
	m_bdebug = m_configManager["bdebug"].as<int>();
    m_radarAsObjectDistance = m_configManager["radarAsObjectDistance"].as<float>();
    m_front2BackDistance = m_configManager["front2BackDistance"].as<float>();
    m_isSaveObjectInfoCSV = m_configManager["isSaveObjectInfoCSV"].as<bool>();
    m_isEvaluateTracking = m_configManager["isUseRosBag"].as<bool>();
    m_isEvaluateTracking = m_configManager["isEvaluateTracking"].as<bool>();

    m_debugID = m_configManager["fusiontracking"]["debugID"].as<int>();
    m_isUseRadarObjects = m_configManager["fusiontracking"]["isUseRadarObjects"].as<bool>();
    m_isSaveTimeUseFile = m_configManager["fusiontracking"]["isSaveTimeUse"].as<bool>();
    m_saveTimeUseFilePath = m_configManager["fusiontracking"]["timeUseFilePath"].as<std::string>();
    m_logFilePath = m_configManager["log"]["filePath"].as<std::string>();
    m_logFileName = m_configManager["log"]["fileName"].as<std::string>();
    m_logOutputType = m_configManager["log"]["outputType"].as<int>();
    m_logFileMaxSize = m_configManager["log"]["fileMaxSize"].as<int>();
    m_logFileMaxNum = m_configManager["log"]["fileMaxNum"].as<int>();

    m_stationRange = m_configManager["stationRangeTest"].as<std::vector<std::vector<double>>>();

    if(carNumberMap.find(m_carNumber) != carNumberMap.end()){
		m_carName = carNumberMap.at(m_carNumber);
	}
	else{
		m_carName = carNumberMap.at(0);
	}

}

void ConfigManager::printParams(std::shared_ptr<spdlog::logger>& pLogger){
    pLogger->info("params................................");
    pLogger->info("carNumber:  {} ",m_carNumber);
    pLogger->info("carName:   {}",m_carName);
    pLogger->info("isUseRosBag:   {}",  m_isUseRosBag);
    pLogger->info("debugID:   {}",  m_debugID);
    pLogger->info("isUseRadarObjects:   {}",  m_isUseRadarObjects);
    pLogger->info("isEvaluateTracking:   {}",  m_isEvaluateTracking);
    pLogger->info("isUseHDMap:   {}",  m_isUseHDMap);
    pLogger->info("hdMapName:   {}", m_hdMapName);
    pLogger->info("hdMapLongitude:   {:.7f}",  m_hdMapLongitude);
    pLogger->info("hdMapLatitude:   {:.7f}",  m_hdMapLatitude);
    pLogger->info("cityUTMCode:   {}",  m_cityUTMCode);
    pLogger->info("isSimulateMode:   {}",  m_isSimulateMode);
    pLogger->info("bdebug:   {}",  m_bdebug);
    pLogger->info("radarAsObjectDistance:   {}",  m_radarAsObjectDistance);
    pLogger->info("front2BackDistance:   {}",  m_front2BackDistance);
    pLogger->info("isSaveObjectInfoCSV:   {}",  m_isSaveObjectInfoCSV);
    pLogger->info("logFilePath:   {}",  m_logFilePath);
    pLogger->info("logFileName:   {}",  m_logFileName);
    pLogger->info("logOutputType:   {}",  m_logOutputType);
    pLogger->info("logFileMaxSize:   {}",  m_logFileMaxSize);
    pLogger->info("logFileMaxNum:   {}",  m_logFileMaxNum);
    pLogger->info("params end................................\n");
}


}