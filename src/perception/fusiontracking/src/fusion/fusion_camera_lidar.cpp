//
// Created by wkx on 2022/3/29.
//
#include <fusiontracking/projection.h>

#include "fusiontracking/fusion_camera_lidar.h"
#include <ros/ros.h>
#include <pcl_conversions/pcl_conversions.h>

using namespace std;
/***
 * 验证lidar和camera融合效果
 * @param argc
 * @param argv
 * @return
 */
//void checkLidarObject2CameraObject(){
//	string path = "/home/<USER>/Documents/ros_dev/readerjson/datajson/data_picture_cloud/第二次数据/sequence/csv/20220331_1_000440.csv";
//	vector<vector<float>> lidar_objects;
//	vector<vector<float>> camera_objects;
//	lidar_objects = readcsv(path);//x y z ...
//
//	string path1 = "/home/<USER>/Documents/ros_dev/readerjson/datajson/data_picture_cloud/第二次数据/sequence/txt/20220331_1_000440.txt";
//	camera_objects = readttx(path1);// x y w l 类别 置信度
//
//	cloud_projection(lidar_objects, camera_objects);
//}


LidarProjection2Camera::LidarProjection2Camera(ros::NodeHandle& nh){
	
	subLidarPC_ = nh.subscribe("/velodyne_points",20,&LidarProjection2Camera::lidarCallBack,this);
	subCameraPicture_ = nh.subscribe("/usb_cam/image_raw/compressed",60,&LidarProjection2Camera::cameraCallBack,this);
	
	pub_lidarInPictureForRVIZ_ = nh.advertise<sensor_msgs::Image>("/lidarInPicture",60);
	
	camera_matrix_ = (cv::Mat_<double>(3,3)<<fx_,0.0,cx_,0.0,fy_,cy_,0.0,0.0,1.0);
	distortion_coeff_ = (cv::Mat_<double>(1,5)<<k1_,k2_,p1_,p2_,k3_);
	
	cv::Mat rotation_x = (cv::Mat_<double>(3,3)<<
	        1,0,0,
			0,cos(rollRadian_),-sin(rollRadian_),
			0,sin(rollRadian_),cos(rollRadian_)
			);
	
	cv::Mat rotation_y = (cv::Mat_<double>(3,3)<<
	        cos(pitchRadian_),0,sin(pitchRadian_),
			0,1,0,
			-sin(pitchRadian_),0, cos(pitchRadian_)
			);
	
	cv::Mat rotation_Z = (cv::Mat_<double>(3,3)<<
	        cos(yawRadian_),-sin(yawRadian_),0,
			sin(yawRadian_), cos(yawRadian_),0,
			0,0,1
			);
	
	cv::Mat rotation = rotation_Z * rotation_y * rotation_x;//旋转矩阵
	
	cv::Rodrigues(rotation,rotationVector_);//旋转矩阵到旋转向量的转换
	translationVector_ = (cv::Mat_<double>(3,1)<<drift_x_,drift_y_,drift_z_);//雷达到相机的平移
	
}

void LidarProjection2Camera::lidarCallBack(const sensor_msgs::PointCloud2 &pointCloudMsg) {
	std::mutex mutexLock;
	std::lock_guard<std::mutex> lidarLock(mutexLock);
	pointCloudDeque_.push_back(pointCloudMsg);
}

void LidarProjection2Camera::cameraCallBack(const sensor_msgs::CompressedImage &pictureMsg) {
	std::mutex mutexLock;
	std::lock_guard<std::mutex> cameraLock(mutexLock);
	pictureDeque_.push_back(pictureMsg);
}

void LidarProjection2Camera::calibrateLidar2Camera() {
	if(pointCloudDeque_.empty()) return;
	static int lidarDectionFrameCount = 1;
	cout << "第 "<<lidarDectionFrameCount++<<" 帧lidar...................... "  << endl;
	
	sensor_msgs::PointCloud2 curPointCloudMsg = pointCloudDeque_.front();
	pointCloudDeque_.pop_front();
	
	double curLidarStamp = curPointCloudMsg.header.stamp.toSec();
	
	//lidar camera 时间同步：保证camera容器内的第一帧是与lidar最近的一帧
	{
		std::mutex cameraMutex;
		std::lock_guard<std::mutex> cameraLock(cameraMutex);
		int curCameraIndex = 0;
		for (int i = 0; i < pictureDeque_.size(); ++i) {
			if(pictureDeque_[i].header.stamp.toSec() > curLidarStamp)
				break;
			curCameraIndex = i;
		}
		if(curCameraIndex > 0){//小于0 说明容器内第一帧camera数据就大于当前帧lidar数据，不处理
			if (abs(curLidarStamp - pictureDeque_[curCameraIndex].header.stamp.toSec()) >//lidar前一帧
			    abs(curLidarStamp - pictureDeque_[curCameraIndex + 1].header.stamp.toSec())){//lidar后一帧
				curCameraIndex += 1;//取最近的一帧数据
			}
			while (curCameraIndex--) {
				pictureDeque_.pop_front();
			}
		}
		
	}
	pcl::PointCloud<pcl::PointXYZI> curPointCloud;
	pcl::fromROSMsg(curPointCloudMsg,curPointCloud);
	
	
	if(pictureDeque_.empty()) return;
	std::vector<cv::Point3f> pts_3d;
	for(auto it = curPointCloud.points.begin(); it != curPointCloud.points.end();it++){
		if(abs(it->y) > yMax_ || it->x < xMin_ || it->y > xMax_ || it->z > zMax_)//限制前后、左右、上下点云 X前
			continue;
		pts_3d.emplace_back(cv::Point3f(it->x,it->y,it->z));
	}
	
	std::vector<cv::Point2f> pts_2d; //点云投影到图像后的像素值
	if(pts_3d.size()>0)
		cv::projectPoints(pts_3d, rotationVector_, translationVector_, camera_matrix_, distortion_coeff_, pts_2d);//opencv投影函数
	else {
		cout<<"error :投影失败\n";
		return;
	}
	
	//ros格式转到OpenCV格式
	sensor_msgs::CompressedImage curCompressedImageMsg = pictureDeque_.front();
	cv::Mat curImageMat;
	try {
		cv_bridge::CvImagePtr cvImagePtr = cv_bridge::toCvCopy(curCompressedImageMsg,sensor_msgs::image_encodings::BGR8);
		curImageMat = cvImagePtr->image;
	}
	catch (cv_bridge::Exception& e){
		std::cerr<<"Could not convert to bgr8\n";
		//return;
	}
	
	//将3D点云的投影2D框 和相机2D目标框画到图像上
	//3D点投影点云，3D点没有2D框，通过2D点云与相机框的重合 的投射看与2D框是否匹配成功。
	//cout<<"pts_2d size: "<<pts_2d.size()<<endl;
	double maxVal = 20.0;
	
	for(int i = 0; i < pts_2d.size(); i++){
		if(pts_2d[i].x > imageMaxX_ || pts_2d[i].x < 0 || pts_2d[i].y > imageMaxY_ || pts_2d[i].y < 0)
			continue;
		//double pointDistance = sqrt(pow(pts_3d[i].x,2) + pow(pts_3d[i].y,2) + pow(pts_3d[i].z,2));
		int red = min(255, (int)(255 * abs((pts_3d[i].x - maxVal) / maxVal)));
		int green = min(255, (int)(255 * (1 - abs((pts_3d[i].x - maxVal) / maxVal))));
		
		cv::circle(curImageMat,pts_2d[i],3,(0,green,red),-1);//-1填充
	}
	//发布画框图像
	sensor_msgs::ImagePtr cameraMsg = cv_bridge::CvImage(std_msgs::Header(),"bgr8",curImageMat).toImageMsg();
	cameraMsg->header = curCompressedImageMsg.header;
	pub_lidarInPictureForRVIZ_.publish(cameraMsg);
}


void LidarProjection2Camera::run() {
	ros::Rate rate(10);
	while(ros::ok()){
		ros::spinOnce();
		//lidar 处理
		calibrateLidar2Camera();
		//lidar2camera
		//图像保存
		
		rate.sleep();
	}
}




int main(int argc, char **argv)
{
	ROS_INFO("starting checkLidarObject2CameraObject node...\n");
	ros::init(argc,argv,"checkLidarObject2CameraObjectNode");
	ros::NodeHandle nh;
	
	LidarProjection2Camera lidarProjection2Camera(nh);
	lidarProjection2Camera.run();
	
	//checkLidarObject2CameraObject();

    cout<<"done!"<<endl;
    return 0;

}