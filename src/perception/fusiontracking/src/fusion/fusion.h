
#include <iostream>
#include <deque>
#include <ros/ros.h>
#include <sensor_msgs/Image.h>

#include <common_msgs/sensorobject1.h>
#include <common_msgs/sensorobjects1.h>
#include <common_msgs/sensorobjects.h>
#include <sensor_msgs/CompressedImage.h>
#include <point_cloud/sensorlidar.h>
#include <camera/sensorcamera.h>

#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>
#include <jsk_recognition_msgs/BoundingBoxArray.h>

#include <fusiontracking/projection.h>
#include "config.h"
#include "tictoc.h"

#include "common_msgs/fusionObject.h"
#include "common_msgs/fusionObjects.h"


using namespace std;

class FusionNode:public Config{
private:
	deque<common_msgs::sensorobjects1> lidar_objects_;//雷达目标队列
	deque<common_msgs::sensorobjects> radar_objects;//毫米波雷达目标队列
	deque<common_msgs::sensorobjects> camera_objects;//相机目标队列
	deque<sensor_msgs::CompressedImage> cameraPictureDeque;//相机压缩数据的存储
	
	double imageMaxX_ = 1920;
	double imageMaxY_ = 1080;
	
	//相机内参和畸变系数
	double fx_ = 1995.4000244140625, fy_ = 1995.0;
	double cx_ = 969.0999755859375, cy_ = 476.5, k3_ = 0;
	double k1_ = -0.557699978351593, k2_ = 0.3222000002861023;
	double p1_ = 0.0008273000130429864, p2_ = -0.0007362;
	
	cv::Mat camera_matrix_;//内参矩阵
	cv::Mat distortion_coeff_;//畸变系数
	
	//lidar-camera外参
	double rollRadian_ = 175.999743 / 180.0 * M_PI;
	double pitchRadian_ =  -87.997  / 180.0 * M_PI;
	double yawRadian_ = -84.278 / 180.0 * M_PI;
	
	double drift_x_ = 250.897732 / 1000.0;//单位毫米
	double drift_y_ =  -505.977407 / 1000.0;
	double drift_z_ = -489.323197 / 1000.0;
	
	double yMax_ = 6.0;
	double xMin_ = 2.0;
	double xMax_ = 30.0;//X 前
	double zMax_ = 2.5;
	
	//lidar-radar外参 TODO 重构projection.h文件 写入config中
	float radar2LidarDriftX_ = 1.0;
	float radar2LidarDriftY_ = -0.6;
	float radar2LidarDriftZ_ = 0.0;
	
	
public:
	
	ros::Subscriber lidar_objects_sub;
	ros::Subscriber radar_objects_sub;
	ros::Subscriber camera_objects_sub;
	ros::Subscriber subCameraPictureForCheck;//用于检查，将lidar目标检测与相机检测的匹配结果点云投影到图像上
	
	ros::Publisher pub_RadarObjectsPointCloud;
	ros::Publisher pub_FusionObjects;//发布融合后的目标-用于rviz显示
	ros::Publisher pub_objects_source;//发布融合后的目标
	ros::Publisher pub_FusionObjectsToTracker_;//发布融合后目标-用新的msgs传给跟踪20220902
	ros::Publisher pub_lidar_objs_source_show;
	
	ros::Publisher pub_LidarRadarFusionObjs_PCshow_;
	ros::Publisher pub_lidar_objs_PCshow;
	ros::Publisher pub_lidarFuseCameraObjectPointForRVIZ;
	ros::Publisher pub_lidarObjectInPictureForRVIZ;
	ros::Publisher pub_lidar_objs_BoxShow_;//发布点云检测目标的检测框
	
	cv::Mat rotationVector_;//旋转向量
	cv::Mat translationVector_;//雷达到相机的平移
	
	FusionNode(ros::NodeHandle& n){
		lidar_objects_sub = n.subscribe("/lidarobject", 200, &FusionNode::SubCallback_lidar_objects,this);//融合程序在lidar回调函数中实现
		radar_objects_sub = n.subscribe("/sensorradar", 200, &FusionNode::SubCallback_radar_objects,this);//只用于存储
		camera_objects_sub = n.subscribe("/cameraobject", 200, &FusionNode::SubCallback_camera_objects,this);//只用于存储
		subCameraPictureForCheck = n.subscribe("/usb_cam/image_raw/compressed",200,&FusionNode::cameraPictureCallBack,this);
		
		pub_RadarObjectsPointCloud = n.advertise<sensor_msgs::PointCloud2>("radarObjectPointsForRVIZ",10);//发布毫米波雷达检测目标的点云
		pub_FusionObjects =  n.advertise<common_msgs::sensorobjects1>("objects",10);//发布融合后目标 -用于rviz显示
		pub_objects_source =  n.advertise<common_msgs::sensorobjects>("objectfusion",10);//发布融合后的目标
		pub_FusionObjectsToTracker_ =  n.advertise<common_msgs::fusionObjects>("objectsFusionToTacker",10);//发布融合后目标-用新的msgs传给跟踪20220902
		
		//用于可视化调试
		pub_lidar_objs_source_show = n.advertise<visualization_msgs::MarkerArray>("fusionObjectBoxForRVIZ", 10);//发布融合后的目标框
		pub_LidarRadarFusionObjs_PCshow_ = n.advertise<sensor_msgs::PointCloud2>("fusionObjectPointsForRVIZ",10);//发布test cloud
		pub_lidar_objs_PCshow = n.advertise<sensor_msgs::PointCloud2>("lidarObjectPointsForRVIZ",10);//发布test cloud 需要旋转才能跟障碍物检测结果匹配
		pub_lidarFuseCameraObjectPointForRVIZ = n.advertise<sensor_msgs::PointCloud2>("lidarFuseCameraPointsForRVIZ",10);//发布lidar与camera融合目标，用于验证调试
		pub_lidarObjectInPictureForRVIZ = n.advertise<sensor_msgs::Image>("lidarObjectInPictureForRVIZ",10);
		pub_lidar_objs_BoxShow_ = n.advertise<jsk_recognition_msgs::BoundingBoxArray>("lidarDectionBox",10);
		
		
		camera_matrix_ = (cv::Mat_<double>(3,3)<<fx_,0.0,cx_,0.0,fy_,cy_,0.0,0.0,1.0);
		distortion_coeff_ = (cv::Mat_<double>(1,5)<<k1_,k2_,p1_,p2_,k3_);
		
		cv::Mat rotation_x = (cv::Mat_<double>(3,3)<<
		                                           1,0,0,
				0,cos(rollRadian_),-sin(rollRadian_),
				0,sin(rollRadian_),cos(rollRadian_)
		);
		
		cv::Mat rotation_y = (cv::Mat_<double>(3,3)<<
		                                           cos(pitchRadian_),0,sin(pitchRadian_),
				0,1,0,
				-sin(pitchRadian_),0, cos(pitchRadian_)
		);
		
		cv::Mat rotation_Z = (cv::Mat_<double>(3,3)<<
		                                           cos(yawRadian_),-sin(yawRadian_),0,
				sin(yawRadian_), cos(yawRadian_),0,
				0,0,1
		);
		
		cv::Mat rotation = rotation_Z * rotation_y * rotation_x;//旋转矩阵
		
		cv::Rodrigues(rotation,rotationVector_);//旋转矩阵到旋转向量的转换
		translationVector_ = (cv::Mat_<double>(3,1)<<drift_x_,drift_y_,drift_z_);//雷达到相机的平移
	}
	
	void SubCallback_lidar_objects(const point_cloud::sensorlidar::ConstPtr &msg_lidar);
	inline void SubCallback_radar_objects(const common_msgs::sensorobjects::ConstPtr &msg);
	void SubCallback_camera_objects(const camera::sensorcamera::ConstPtr &msg);
	inline void cameraPictureCallBack(const sensor_msgs::CompressedImage::Ptr cameraPictureMsg);
	void run();
	void fusionAllSensorResult();
	void fusionLidarAndRadar(common_msgs::sensorobjects1& lidar_objs);
	void fusionLidarAndCamera(common_msgs::sensorobjects1& lidar_objs);
	void visualization(const common_msgs::sensorobjects& lidar_objs_source);
	
	//void sensorSync();
	
	void publishFusionObjects(common_msgs::sensorobjects1& lidar_objs);
	
	bool objectProjection(common_msgs::sensorobjects1 &lidar_objects, common_msgs::sensorobjects &camera_objects,
	                         deque<sensor_msgs::CompressedImage>& cameraPictureDeque, sensor_msgs::ImagePtr& cameraMsg,
	                         pcl::PointCloud<pcl::PointXYZI>::Ptr lidarFuseCameraPointCloud);
	
	void publishFusionObjectsToTracker(common_msgs::sensorobjects1& lidar_objs);
	void visualization(const common_msgs::fusionObjects& lidar_objsToTracker);
};