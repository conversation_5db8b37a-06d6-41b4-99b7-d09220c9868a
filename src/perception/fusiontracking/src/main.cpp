/*
 * @Description: 
 * @Version: 2.0
 * @Autor: shuangquan han
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-25 09:48:54
 */
/********************************************************************************
* @author: shuangquan han
* @date: 2023/6/21 下午4:22
* @version: 1.0
* @description: 
********************************************************************************/

#include "fusiontracking.h"

int main(int argc, char **argv){
	ros::init(argc, argv, "fusiontracking");
	ros::param::set("/version/fusiontracking","2025-04-25-v4.5.0");
	
	ros::NodeHandle nh;
	FusionTracking node(nh);
	node.run();
	
	return 0;
}