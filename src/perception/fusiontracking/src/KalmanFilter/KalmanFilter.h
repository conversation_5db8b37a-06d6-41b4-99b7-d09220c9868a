/********************************************************************************
* @author: shuangquan han
* @date: 2022/8/31 下午1:34
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_KALMANFILTER_H
#define SRC_KALMANFILTER_H

#include <iostream>
#include <eigen3/Eigen/Dense>



class KalmanFilter{
public:
	KalmanFilter();//const int& stateSize = 0, const int& measureSize = 0
	~<PERSON><PERSON>Filter();
	<PERSON><PERSON><PERSON>ilter(const <PERSON><PERSON>Filter& other);
	<PERSON><PERSON>Filter& operator=(const Ka<PERSON>Filter& other);

	void Initialization(const Eigen::VectorXd& X_in);
	bool IsInitialized();
	void setF(const Eigen::MatrixXd& F_in);
	void setP(const Eigen::MatrixXd& P_in);
	void setQ(const Eigen::MatrixXd& Q_in);
	void setH(const Eigen::MatrixXd& H_in);
	void setR(const Eigen::MatrixXd& R_in);

	void setX(const double& positionX);
	void setY(const double& positionX);
	void setHeading(const double& headingRad);
	void setVx(const double& relativeVx);
	void setVy(const double& relativeVy);
	
	void prediction();
	void kfUpdate(const Eigen::VectorXd& z);
	void ekfUpdate(const Eigen::VectorXd& z);
	Eigen::VectorXd getX();
	Eigen::MatrixXd getP();
	Eigen::MatrixXd getQ();
	Eigen::MatrixXd getR();
	Eigen::MatrixXd getK();

private:
	bool isInitialized_;
	Eigen::VectorXd x_;
	Eigen::MatrixXd P_;
	Eigen::MatrixXd F_;
	Eigen::MatrixXd Q_;
	Eigen::MatrixXd H_;
	Eigen::MatrixXd R_;
	Eigen::MatrixXd K_;
};


#endif //SRC_KALMANFILTER_H
