/********************************************************************************
* @author: shuang<PERSON>n han
* @date: 2023/10/31 上午10:13
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_OBJECTTRACKING_H
#define SRC_OBJECTTRACKING_H

#include <iostream>
#include <vector>

#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"
#include "common_msgs/sensorgps.h"
#include "common_msgs/point3d.h"
#include "<PERSON><PERSON>Filter/KalmanFilter.h"
// #include "../../commonlibrary/src/KalmanFilter/KalmanFilter.h"

#include "../../commonlibrary/src/common.h"
#include "../../commonlibrary/src/coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h" //从FusionAndTrk/src目录开始算再去找头文件路径
#include "../../commonlibrary/src/configManager/configManager.h"
#include "../../commonlibrary/src/logger/logger.h"
#include "./MHT/scan_volume.h"
#include "./MHT/cv_target.h"
#include "./MHT/tracker/localHypothesis.h"
#include "./MHT/tracker/tracker.h"

using namespace std;

const double SPEED_THRESHOLD_SAME_DIRECTION = 2.0;
const double SPEED_THRESHOLD_OPPOSITE_DIRECTION = 1.0;

enum ClassSwitchType{
	ClassFixed = 0,
	Bicycle2Car = 1,
	Tricycle2Car = 2,
	Cone2Car = 3,
	Unknown2Car = 4
};

typedef struct ObjectPositionInfo{
	double timeStamp;
	std::vector<double> position;//存放经纬度 UTM
	double longtitude;//存放经纬度 UTM
	double latitude;//存放经纬度 UTM
	double altitude;//存放经纬度 UTM
	double rollDegree;// 度
	double pitchDegree;//
	double yawDegree;//正北方向顺时针0-360

	double absoluteSpeedX;//20221226 用于radar异常速度剔除 20230118 改为绝对速度
	double absoluteSpeedY;
	
	float xInLidarAxis; // 20230214 存放目标在当前帧lidar坐标系下的位置，用于目标速度补偿
	float yInLidarAxis;
	float zInLidarAxis;
	float objectHeadingDegree; // 20230214 存放目标航向角，用于目标速度补偿
	float length;
	float width;
	float classification;

}ObjectPositionInfo;//20220909 存放单帧目标时间戳和位置

typedef struct HistoryTrajectoryInfo{
	double timeStamp;
	std::vector<ObjectPositionInfo> historyTrajectory;
	std::vector<ObjectPositionInfo> predictedTrajectory;
	
}HistoryTrajectoryInfo;//20220907 添加历史轨迹

typedef struct StationObjectsInfo{
	bool m_isMatched;
	long m_id;
	int m_value;
	int m_classification;
	std::vector<double> m_centerLLA;
	StationObjectsInfo(){
		m_isMatched = false;
		m_id = 0;
		m_value = 0;
	}
}s_StationObjectsInfo;

typedef struct ClassDetectedTimes{
	int detectedTimes; //类别检测次数
	std::vector<float> m_lengths; //长
	std::vector<float> m_widths; //宽
	std::vector<float> m_heights; //宽
}s_ClassDetectedTimes;


class ObjectTracking{
public:
	static common_msgs::sensorgps m_selfCarGPS;
	static std::vector<double> m_selfCarUTMPosition; //三维
	static std::vector<double> m_selfCarSpeed; //三维
	static SensorAxisTransformation m_sensorAxisTransformation;
	static int m_kf_count;

	KalmanFilter m_kalmanFilter;
	
	wgs84_utm m_wgs84_utm;
	Common m_cCommon;



	common_msgs::sensorobject m_objectOriginal;
	int m_id;
	std::vector<double> m_centerPointUTM;
	std::vector<double> m_centerLLA;
	std::vector<float> m_boxSize;
	// std::vector<float> m_rorationAngelDegree;
	// std::vector<float> m_rorationAngelRateDegree;
	int m_classification;
	// bool m_isLockedClassification;
	float m_confidence;
	// std::vector<float> m_relVelocity;//相对速度
	std::vector<double> m_absVelocityUTM;//绝对速度
	int   m_value;
	// std::vector<float> m_cornerPoints;
	int  m_drivingIntent;//           #驾驶意图：0-初始，1-切入
	// int  m_behavior_state;//          # FORWARD_STATE = 0, STOPPING_STATE = 1, BRANCH_LEFT_STATE = 2, BRANCH_RIGHT_STATE = 3, YIELDING_STATE = 4, ACCELERATING_STATE = 5, SLOWDOWN_STATE = 6
	int  m_radarIndex;//              #相对速度来源
	int  m_radarObjectID;//           #radar跟踪目标ID
	HistoryTrajectoryInfo m_historyTrajectoryInfo;
	float m_headingRadInNorthClockwise;
	unsigned int m_historyCount;
	bool m_isHeadingInit;
	
	int m_time_since_update;//没有被更新的次数，预测帧数，update后置位0
	int m_hits;//更新次数(时间不连续) update中加1，
	int m_hit_streak;//连续跟踪次数 update中加1，
	int m_age;// 总共跟踪次数=预测+更新，预测中加1
	int m_max_age = 6;//超过max_age从跟踪列表中删除,预测帧数
	int m_motionInfo;//20220929 运动属性
	float m_motionHeadingRadian;//20220929 运动航向角
	int m_last_time_since_update;//20230113 上一帧预测帧数
	int m_updateCount;//20230214 第几次匹配成功
	std::vector<s_ClassDetectedTimes> m_classificationCount; //9种类别，s_ClassDetectedTimes存放
	vector<float> m_trackingBoxResult;//用于保存kalman预测或更新后目标
	vector<float> m_curPredictedInfo; // 20230206 每个目标添加当前预测信息

	s_StationObjectsInfo m_sStationObjectsInfo;
	int m_classSwitchType;
	
	
    ObjectTracking(const common_msgs::sensorobject& object);
	~ObjectTracking();
	static void setConfigManager(boost::shared_ptr<ConfigManager::ConfigManager>& pConfigManager);
	static void setLoggerManager(const std::shared_ptr<spdlog::logger>& pLogger);
	static void setStaticMembers(const common_msgs::sensorgps& selfCarGPS,
							   const std::vector<double>& selfCarUTMPosition,
							   const std::vector<double>& selfCarSpeed,
							   const SensorAxisTransformation& sensorAxisTransformer,
							   const double& curTimestamp);
	static int generateNewTrackId();

	void initializeKalmanFilter(const common_msgs::sensorobject& object);
	vector<float> predict(const double& timeStamp,const double& timeDiff);
	void setVelocityCorrectedBeforeUpdate(const common_msgs::sensorobject& object);
	void setAxisSpeed(double* absVelocityUTM_Axis, double curPredictedInfo_Axis, 
	  const double& sameDirectionThreshold, const double& oppositeDirectionThreshold,const int& velocityIndex);
	
	void setPositionCorrectedBeforeUpdate(const common_msgs::sensorobject& object);
	void update(const common_msgs::sensorobject& object, const double& timeStamp);
	template <typename T>
	T getMinPositiveOrNegative(T a, T b, T c);
	std::vector<float> get_state_finaly();
	vector<float> getCurPredictedInfo();
	ObjectPositionInfo packageObjectPositionInfo(const vector<float>& updatedStateInfo,
												const double& timeStamp,
												const common_msgs::sensorobject& obj);
	void positionSmooth(const int& trackingState);
	void velocitySmooth(Eigen::VectorXd& statement);
	void resetTrackingObjectInfo(ObjectPositionInfo& bottom, ObjectPositionInfo& penultimate,const int& trackingState);
	// void trackingObjectSmooth(const common_msgs::sensorobject& object);
	void fixSpeedAfterUpdate(Eigen::VectorXd& statementUpdated, const common_msgs::sensorobject& object, const double& fixedUTMSpeedVx,const double& fixedUTMSpeedVy);
	void headingSmooth(const common_msgs::sensorobject& object, const double& timeStamp);
	double findMedian(std::vector<double>& nums) {
		size_t size = nums.size();
		std::nth_element(nums.begin(), nums.begin() + size / 2, nums.end());

		if (size % 2 == 0) {
			std::nth_element(nums.begin(), nums.begin() + size / 2 - 1, nums.begin() + size / 2);
			return (nums[size / 2 - 1] + nums[size / 2]) / 2.0;
		} else {
			return nums[size / 2];
		}
	}

	void nonMotorVehivleHeadingSmooth(const common_msgs::sensorobject& object, const double& timeStamp);
	void clusterObjectsHeadingSmooth(const common_msgs::sensorobject& object, const double& timeStamp);
	float getCross(const common_msgs::point3d& cornerPoint1, const common_msgs::point3d& cornerPoint2,
                            const common_msgs::point3d& point);
	void findOptimalYaw(common_msgs::sensorobject& sensorObject);
	std::vector<double> getPositionInCarbackRFU();
	std::vector<double> getSpeedInCarbackRFU();
	std::vector<double> getSpeedInCarbackRFU(const Eigen::Vector3d& speedInUTM) const;

private:
	static double m_curTimestamp;
	static double m_lastTimestamp;
	
    int m_stateNum; //状态量维度
	int m_measureNum;//观测量维度 20220905
	static const int m_measureNum_8 = 8;//观测量维度8 20220905
	static const int m_measureNum_6 = 6;//观测量维度6 20220905
	Eigen::VectorXd m_measurement;
};


#endif //SRC_OBJECTTRACKING_H
