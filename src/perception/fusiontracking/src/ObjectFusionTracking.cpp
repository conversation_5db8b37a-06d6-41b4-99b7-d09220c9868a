/********************************************************************************
* @author: shuangquan han
* @date: 2023/7/26 上午10:46
* @version: 1.0
* @description: 
********************************************************************************/


#include "ObjectFusionTracking.h"

std::map<COMMON::LidarDetectionClassification, float> thresholdMap = {
		{COMMON::LidarDetectionClassification::Pedestrian, 2.5},
		{COMMON::LidarDetectionClassification::Bicycle, 4},
		{COMMON::LidarDetectionClassification::Tricycle, 4}
};

/**
 * @description: 初始化量测信息：检测概率、杂波系数等
 * @return {*}
 */
CartesianVolume ObjectFusionTracking::generateVolume(){
    double init_lambda = 0.05;
    Eigen::MatrixXd ranges = Eigen::MatrixXd::Zero(2,2);
    // ranges << -250.0, 250.0,-250.0, 250.0;
	ranges << -1.0, 100.0,-20.0, 20.0;
    CartesianVolume volume(
        ranges, // 生成量测边界
        0.9,	// P_D 检测概率
        1,		// clutter_lambda 生成杂波的泊松分布的系数为clutter_lambda=1.0
        init_lambda	// 目标生成时的泊松分布的系数为init_lambda=1.0
    );
    return volume;
}

std::map<uint32_t, int> ObjectFusionTracking::m_obuLidarTrackedObjectIDMap;

ObjectFusionTracking::ObjectFusionTracking():m_volume(generateVolume()), m_targetmodel(std::make_shared<Density>(), 0){
	
	m_lidarObjects.obs.clear();
	m_radarObjects.obs.clear();
	m_radarParticipants.obs.clear();
	m_cloudObjects.obs.clear();
	m_obupantsMsg.pants.clear();
	m_obuObjectMsg.obs.clear();
	m_obuObjects.obs.clear();
	m_trackedObjects.obs.clear();
	m_trackedObjects8CornerForRVIZ.obs.clear();
	m_v2ifusionObjects.obs.clear();
	m_v2ifusionObjectsRVIZ.obs.clear();
	m_v2ifusionObjectsPAD.obs.clear();
	m_matchedRadarobjects.obs.clear();
	m_unmatchedRadarobjects.obs.clear();
	
	m_lidarOBUSyncTimeStep = 0;
	m_folderPath = "";
	
	m_isInitial = false;
	m_isStartTracking = false;
	m_curFrameStamp = 0;
	m_lastFrameStamp = 0;
	m_timeDiff = 0;
	m_selfCarSpeed.reserve(3);
	m_selfCarUTMPosition.reserve(3);

	// MHT
	gweights_.push_back(log(1.0));
	
	m_pStationObjects = boost::make_shared<c_StationObjects>(m_nh, "/obu/source", COMMON::SensorType::OBU);
	m_pSensorGPS = boost::make_shared<c_SensorGPS>(m_nh, "/sensorgps", COMMON::SensorType::GPS);
	pub_track_results = m_nh.advertise<common_msgs::sensorobjects>("/objectTrack/track_results", 1);//发布跟踪结果RFU用于决策
	pub_track_results8CornerForRVIZ = m_nh.advertise<common_msgs::sensorobjects>("/objectTrack/track_results8CornerForRVIZ", 1);//发布跟踪结果FLU用于显示
	pub_track_clusterPointcloud = m_nh.advertise<sensor_msgs::PointCloud2>("/objectTrack/track_clusterPointcloud", 1);

	pub_v2iFusionObject = m_nh.advertise<common_msgs::sensorobjects>("/v2ifusion/fusionObject", 1); // /objectTrack/v2iFusionObject
	pub_v2iFusionObjectRVIZ = m_nh.advertise<common_msgs::sensorobjects>("/v2ifusion/fusionObjectRVIZ", 1);
	pub_v2iFusionObjectPAD = m_nh.advertise<common_msgs::sensorobjects>("/v2ifusion/fusionObjectPAD", 1);

	pub_matchedRadarObjects = m_nh.advertise<sensor_msgs::PointCloud2>("/fusion/matchedRadarObjectsPC", 1);
	pub_unmatchedRadarObjects = m_nh.advertise<sensor_msgs::PointCloud2>("/fusion/matchedRadarObjectsPC", 1);

	pub_MHTTrackedObjectsBBX = m_nh.advertise<visualization_msgs::MarkerArray>("/objectTrack/MHTTrackedObjectsBBX", 1);
}


ObjectFusionTracking::~ObjectFusionTracking(){
	m_pStationObjects = nullptr;
	m_pSensorGPS = nullptr;
	m_pConfigManager = nullptr;
	m_pLogger = nullptr;
}

void ObjectFusionTracking::setNodeHandle(const ros::NodeHandle& nodeHandle) {
	m_nh = nodeHandle;
}

void ObjectFusionTracking::setConfigManager(boost::shared_ptr<ConfigManager::ConfigManager>& pConfigManager){
	m_pConfigManager = pConfigManager;
	ObjectTracking::setConfigManager(pConfigManager);
}

void ObjectFusionTracking::setLoggerManager(const std::shared_ptr<spdlog::logger>& pLogger){
	m_pLogger = pLogger;
	ObjectTracking::setLoggerManager(pLogger);
}

void ObjectFusionTracking::setLidarObjects(const common_msgs::sensorobjects& objects){
	m_lidarObjects.obs.clear();
	m_lidarObjects = objects;
	
	static bool isLidarObjectReceived = false;
	if(!isLidarObjectReceived){
		m_lastFrameStamp = objects.timestamp;
		isLidarObjectReceived = true;
	}
	m_curFrameStamp = objects.timestamp;
	m_timeDiff =  (m_curFrameStamp - m_lastFrameStamp)/1000.0;//整数转秒，带两位小数点
	m_lastFrameStamp = m_curFrameStamp;
}

void ObjectFusionTracking::setRadarObjects(const common_msgs::sensorobjects& objects) {
	m_radarObjects.obs.clear();
	m_radarObjects = objects;
}

void ObjectFusionTracking::serRadarParticipants(const common_msgs::sensorobjects& objects) {
	m_radarParticipants.obs.clear();
	m_radarParticipants = objects;
}

void ObjectFusionTracking::setCloudObjects(const common_msgs::sensorobjects& objects){
	m_cloudObjects.obs.clear();
	m_cloudObjects = objects;
}
void ObjectFusionTracking::setOBUObjects(const common_msgs::sensorobjects& objects){
	m_obuObjectMsg.obs.clear();
	m_obuObjectMsg = objects;
}
void ObjectFusionTracking::setGPS(const common_msgs::sensorgps& gps, const std::vector<double>& selfCarSpeed){
	m_gps = gps;
	m_pLogger->info("ENU speed: m_gps.speedE: {:.2f}, speedN: {:.2f}, speedU: {:.2f}", m_gps.speedE,m_gps.speedN, -m_gps.speedD);
	
	m_selfCarUTMPosition.clear();
	m_selfCarUTMPosition = m_wgs84Utm.getUTMPosition(m_gps.lon, m_gps.lat, m_gps.alt);
	static bool isCaculateUTMCode = false;
	if(isCaculateUTMCode){
		m_pConfigManager->m_cityUTMCode = m_wgs84Utm.getUTMCode(m_gps.lon);
		isCaculateUTMCode = true;
	}
	m_pLogger->info("citycode: {}",  m_pConfigManager->m_cityUTMCode);
	m_pLogger->info("lon: {:.7f}, lat: {:.7f}",  gps.lon, gps.lat);
	m_pLogger->info("m_selfCarUTMPosition: {:.2f}, {:.2f}, {:.2f}",  m_selfCarUTMPosition[0], m_selfCarUTMPosition[1],  m_selfCarUTMPosition[2]);
	
	//第一辆车发出NED,看做是ENU顺序,三轴角度和速度都看作是ENU
	m_selfCarSpeed = selfCarSpeed;

	//设置m_sensorAxisTransformer公共数据
	Eigen::Vector3d selfCarEulerDegrees{m_gps.pitch, m_gps.roll, m_gps.heading};
	m_sensorAxisTransformer.setSelfCarEulerDegrees(selfCarEulerDegrees);
	m_sensorAxisTransformer.setSelfCarUTMPosition(m_selfCarUTMPosition);
	ObjectTracking::setStaticMembers(m_gps, m_selfCarUTMPosition, m_selfCarSpeed, m_sensorAxisTransformer, m_curFrameStamp / 1000.0);
	
	m_pLogger->info("curCarSpeed右前上: {:.3f}, curCarSpeedVx_ = {:.3f}, curCarSpeedVy_ = {:.3f}", gps.velocity,m_selfCarSpeed[0], m_selfCarSpeed[1]);
	m_pLogger->info("curCarYawRate(deg/s)= {:.3f}, heading(deg) = {:.3f}",  m_gps.yawrate,m_gps.heading);
}


void ObjectFusionTracking::objectFusion(){
	m_currentROSTime = ros::Time::now();
	std::sort(m_lidarObjects.obs.begin(), m_lidarObjects.obs.end(), std::bind(&ObjectFusionTracking::objectSort, this, std::placeholders::_1, std::placeholders::_2));
	std::sort(m_obuObjectMsg.obs.begin(), m_obuObjectMsg.obs.end(), std::bind(&ObjectFusionTracking::objectSort, this, std::placeholders::_1, std::placeholders::_2));
	std::sort(m_radarObjects.obs.begin(), m_radarObjects.obs.end(), std::bind(&ObjectFusionTracking::objectSort, this, std::placeholders::_1, std::placeholders::_2));
	if(m_lidarObjects.obs.empty())
		return;
	m_pLogger->info("m_lidarObjects size = {}, m_radarObjects size = {}", m_lidarObjects.obs.size(),m_radarObjects.obs.size());

	unsigned int lidarObjectNum = m_lidarObjects.obs.size();
	unsigned int obuObjectNum = m_obupantsMsg.pants.size();
	std::vector<std::vector<double>> associateMatrix(lidarObjectNum, std::vector<double>(obuObjectNum, -1));
	int lidarOBUFusionMethod = 0;

	if(!m_obupantsMsg.pants.empty()){
		if((int)(m_obupantsMsg.pants[0].roadlist.size() == 1)){
			m_pLogger->info("INFO: one predicted trajectorys  ");
		}
		else if((int)(m_obupantsMsg.pants[0].roadlist.size() == 0)){ //没有预测轨迹使用当前的lidar扫描结果
			m_pLogger->info("WARN: no predicted trajectorys ");
		}
		else{
			m_pLogger->info("WARN: OBU object has mulity predicted trajectory");
		}
	}


	for(int i = 0; i < lidarObjectNum; ++i) {
		common_msgs::sensorobject& singleLidarObject = m_lidarObjects.obs[i];
		lidarRadarFusion(singleLidarObject, m_radarObjects);
		if(!m_obupantsMsg.pants.empty())
			continue;

		std::vector<float> lidarObjectBox{singleLidarObject.x, singleLidarObject.y, 1,
		                                  singleLidarObject.length, singleLidarObject.width, 1,
		                                  singleLidarObject.azimuth};
		for(int j = 0; j < obuObjectNum; ++j){
			common_msgs::obupant obupantsObject = m_obupantsMsg.pants[j];
			std::vector<float> obuObjectBox;
			if((int)(obupantsObject.roadlist.size() == 1) && (int)(obupantsObject.roadlist[0].oburoadpoint.size()) != 0){ //一条预测轨迹的数量 && 轨迹点数量不为0（有这种情况）

				std::vector<double> objectPositionInCarBackFRU(3, -1);
				//if(m_lidarOBUTimeGap >= 1e6){ // 基站检测晚于当前lidar检测时间
				if(m_lidarOBUSyncTimeStep > (int)(obupantsObject.roadlist[0].oburoadpoint.size())){//一条轨迹
					printf("WARN: predicted object trajectory is earlyer\n");
					printf("INFO: m_lidarOBUSyncTimeStep = %u, predicted trajectory size = %u\n", m_lidarOBUSyncTimeStep,
					       (int)(obupantsObject.roadlist[0].oburoadpoint.size()));
					m_lidarOBUSyncTimeStep = (int)(obupantsObject.roadlist[0].oburoadpoint.size());
				}

				//cout<<FGRN("use OBU predicted trajectory, predicted time = ") << m_lidarOBUSyncTimeStep * 0.1 << endl;
				common_msgs::oburoadpoint obuObjectPredictedLLA = obupantsObject.roadlist[0].oburoadpoint[m_lidarOBUSyncTimeStep-1];

				float obuObjectAngleDegree = obuObjectPredictedLLA.heading;
				float obuObjectAngleDegreeInLidarRFU_Clockwise = (obuObjectAngleDegree - m_gps.heading);
				obuObjectAngleDegreeInLidarRFU_Clockwise = obuObjectAngleDegreeInLidarRFU_Clockwise < 0? obuObjectAngleDegreeInLidarRFU_Clockwise + 360:obuObjectAngleDegreeInLidarRFU_Clockwise;
				obuObjectAngleDegreeInLidarRFU_Clockwise = obuObjectAngleDegreeInLidarRFU_Clockwise > 360 ? obuObjectAngleDegreeInLidarRFU_Clockwise - 360:obuObjectAngleDegreeInLidarRFU_Clockwise;

				m_wgs84Utm.LatLonToLocalXY(m_gps.lon, m_gps.lat, m_gps.heading, obuObjectPredictedLLA.lon, obuObjectPredictedLLA.lat, objectPositionInCarBackFRU[0], objectPositionInCarBackFRU[1]);
				//cout<<FGRN("curobuObject m_lidarOBUSyncTimeStep = ") << m_lidarOBUSyncTimeStep<<", x = " << objectPositionInCarBackFRU[0] <<", y = " << objectPositionInCarBackFRU[1]<< endl;

				//使用预测的轨迹点、速度、加速度、航向角替换当前帧的信息
				obupantsObject.pos_lon = obuObjectPredictedLLA.lon;
				obupantsObject.pos_lat = obuObjectPredictedLLA.lat;
				obupantsObject.speed = obuObjectPredictedLLA.speed;
				obupantsObject.heading = obuObjectAngleDegreeInLidarRFU_Clockwise;
				obupantsObject.accel = obuObjectPredictedLLA.accel;

				m_obuObjectMsg.obs[j].x = objectPositionInCarBackFRU[0];
				m_obuObjectMsg.obs[j].y = objectPositionInCarBackFRU[1];
				//m_obuObjectMsg.obs[j].z = objectPositionInCarBackFRU[2];
				m_obuObjectMsg.obs[j].azimuth = obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;//deg2rad

				//cout<<FBLU("obu object heading degree = ") << obupantsObject.heading // //阿里盎司 rad
				//    <<", gps heading degree = " << m_gps.heading //deg
				//    <<", m_obuObjectMsg.obs[j].heading = " << m_obuObjectMsg.obs[j].azimuth * 180.0 / M_PI
				// <<",obuObjectPredictedLLA.heading = " << obuObjectPredictedLLA.heading
				//    << endl;

				// TODO 验证角度与相对速度: need lidar axis angle
				m_obuObjectMsg.obs[j].relspeedx = obupantsObject.speed * sin(obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0);
				m_obuObjectMsg.obs[j].relspeedy = obupantsObject.speed * cos(obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0);

				obuObjectBox = {objectPositionInCarBackFRU[0], objectPositionInCarBackFRU[1], 1,
				                obupantsObject.length, obupantsObject.width,1, obupantsObject.heading * M_PI / 180.0};
				//}
				//else{ //基站检测时间戳早于车端：使用基站扫描时刻目标，不使用预测目标
				//	cout<<FGRN("WARN: m_lidarOBUTimeGap < 0 ")<< endl;
				//	common_msgs::sensorobject obuObject = m_obuObjectMsg.obs[j];
				//	obuObjectBox = {obuObject.x, obuObject.y, 1,
				//	                                obuObject.length, obuObject.width,1, obuObject.azimuth};
				//}
			}
			else if((int)(obupantsObject.roadlist.size() == 0)){ //没有预测轨迹使用当前的lidar扫描结果
				//cout<<FGRN("WARN: no predicted trajectorys ")<< endl;
				common_msgs::sensorobject obuObject = m_obuObjectMsg.obs[j];
				obuObjectBox = {obuObject.x, obuObject.y, 1,
				                obuObject.length, obuObject.width,1, obuObject.azimuth};
			}
			else{ // 多条预测轨迹
				//cout<<FBLU("WARN: OBU object has mulity predicted trajectory ")<< endl;
				common_msgs::sensorobject obuObject = m_obuObjectMsg.obs[j];
				obuObjectBox = {obuObject.x, obuObject.y, 1,
				                obuObject.length, obuObject.width,1, obuObject.azimuth};
			}
			//cout<<FBLU("obu object label = ") << obuObject.classification
			//    <<", x = " << obuObject.x<<", y = " << obuObject.y
			//    <<", length = " << obuObject.length<<", width = " << obuObject.width
			//    <<", azimuth = " << obuObject.azimuth * 180.0 / M_PI
			//    << endl;
			m_computeAssignmentMatrix.setAssociateObject(lidarObjectBox, obuObjectBox);
			if(lidarOBUFusionMethod == 0){ // IOU
				associateMatrix[i][j] = m_computeAssignmentMatrix.getBoxIOUDistance();
				// cout<<FRED("iou = ") << 1-associateMatrix[i][j] << endl;
			}
			else{ // distance
				associateMatrix[i][j] = m_computeAssignmentMatrix.getLocationDistance();
				// cout<<FRED("distance = ") << associateMatrix[i][j] << endl;
			}
			m_computeAssignmentMatrix.resetDistance();
		}
	}

	vector<int> assignment(lidarObjectNum, -1);
	m_hungarianAlgorithm.Solve(associateMatrix, assignment);


	vector<int> lidarObjectIndexUnmatched, obuObjectIndexUnmatched;//lidar-OBU 未匹配的lidar目标使用lidar-radar匹配的情况，匹配的目标将OBU目标转为lidar目标
	vector<std::vector<int>> lidarOBUObjectIndexsMatched;

	if(lidarOBUFusionMethod == 0) { // IOU
		float matchTreshold = 0.1;
		for(int i = 0; i < assignment.size(); i++){
			if(assignment[i] == -1){
				continue;
			}
			else if(1 - associateMatrix[i][assignment[i]] > matchTreshold){
				std::vector<int> lidarOBUObjectIndexMatched = {i, assignment[i]};
				lidarOBUObjectIndexsMatched.emplace_back(lidarOBUObjectIndexMatched);
			}
			else{
				lidarObjectIndexUnmatched.emplace_back(i);
				obuObjectIndexUnmatched.emplace_back(assignment[i]);
			}
		}

		// cout<<"lidarOBUObjectIndexsMatched pair:\n";
		// for (int i = 0; i < lidarOBUObjectIndexsMatched.size(); ++i) {
		// 		cout <<lidarOBUObjectIndexsMatched[i][0] <<", " <<lidarOBUObjectIndexsMatched[i][1]<<
		// 		", iou = " << 1 - associateMatrix[lidarOBUObjectIndexsMatched[i][0]][lidarOBUObjectIndexsMatched[i][1]] <<endl;
		// }
		//
	}
	else { // distance
		float distanceThreshold = 10;//设置距离匹配阈值
		for(int i = 0; i < assignment.size(); i++){
			if(assignment[i] == -1){
				continue;
			}
			else if(associateMatrix[i][assignment[i]] < distanceThreshold){
				std::vector<int> lidarOBUObjectIndexMatched = {i, assignment[i]};
				lidarOBUObjectIndexsMatched.emplace_back(lidarOBUObjectIndexMatched);
			}
			else{
				lidarObjectIndexUnmatched.emplace_back(i);
				obuObjectIndexUnmatched.emplace_back(assignment[i]);
			}
		}

		// cout<<"lidarOBUObjectIndexsMatched pair:\n";
		// for (int i = 0; i < lidarOBUObjectIndexsMatched.size(); ++i) {
		// 		cout <<lidarOBUObjectIndexsMatched[i][0] <<", " <<lidarOBUObjectIndexsMatched[i][1]<<
		// 		", iou = " << 1 - associateMatrix[lidarOBUObjectIndexsMatched[i][0]][lidarOBUObjectIndexsMatched[i][1]] <<endl;
		// }
		//
	}

	//cout << "debug2: m_lidarObjects size = " << m_lidarObjects.obs.size() << endl;
	//cout << "debug: lidar-obu: fusionObject size = " << lidarOBUObjectIndexsMatched.size() << endl;
	for (int i = 0; i < lidarOBUObjectIndexsMatched.size(); ++i) {
		std::vector<int> lidarOBUObjectIndexMatched = lidarOBUObjectIndexsMatched[i];
		int objectValue = (int)m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].value;
		{
			//common_msgs::fusiontrackingobject fusionObject;
			if(objectValue == COMMON::SensorType::LIDAR){
				//fusionObject.objectsource = static_cast<uint8_t>(COMMON::SensorType::LIDAR_OBU);
				m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].value = static_cast<uint8_t>(COMMON::SensorType::LIDAR_OBU);
			}
			else if(objectValue == COMMON::SensorType::LIDAR_RADAR){
				//fusionObject.objectsource = static_cast<uint8_t>(COMMON::SensorType::LIDAR_RADAR_OBU);
				m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].value = static_cast<uint8_t>(COMMON::SensorType::LIDAR_RADAR_OBU);
			}
			//fusionObject.lidartimestamp = m_lidarObjects.timestamp;
			//fusionObject.lidargpstime = m_lidarObjects.gpstime;
			//fusionObject.lidarObject = m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]];
			//
			//fusionObject.obutimestamp = m_obuObjectMsg.timestamp;
			//fusionObject.obugpstime = m_obuObjectMsg.gpstime;
			//fusionObject.obuObject = m_obuObjectMsg.obs[lidarOBUObjectIndexMatched[1]];
			//
			//fusionObjects.obs.emplace_back(fusionObject);
		}

		if(objectValue == COMMON::SensorType::LIDAR){
			m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].value = static_cast<uint8_t>(COMMON::SensorType::LIDAR_OBU);
		}
		else if(objectValue == COMMON::SensorType::LIDAR_RADAR){
			m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].value = static_cast<uint8_t>(COMMON::SensorType::LIDAR_RADAR_OBU);
		}
		//cout<<"fusion IOU = " << assignment[lidarOBUObjectIndexMatched[0]] << endl;
		//cout << "lidar x = " << m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].x
		//	<< ", y = " << m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].y
		//	<< ", value = " << (int)m_lidarObjects.obs[lidarOBUObjectIndexMatched[0]].value<< endl;
		//cout << "obu x = " << m_obuObjectMsg.obs[lidarOBUObjectIndexMatched[1]].x
		//     << ", y = " << m_obuObjectMsg.obs[lidarOBUObjectIndexMatched[1]].y<< endl;
	}

	//cout << "debug: obuObject size = " << obuObjectNum <<", unmatch obuobject size = "<< obuObjectIndexUnmatched.size() << endl;
	int findOBUObjectSize = 0;
	for(int i = 0; i < obuObjectNum; ++i) {

		bool isFindMatchOBUObjectIndex = false;
		for (int j = 0; j < lidarOBUObjectIndexsMatched.size(); ++j) {
			//cout << "debug: obuObjectmatch index = " << lidarOBUObjectIndexsMatched[j][1] <<", i = "<< i << endl;
			if(i == lidarOBUObjectIndexsMatched[j][1]){
				findOBUObjectSize++;
				isFindMatchOBUObjectIndex = true;
				break;
			}
		}
		if(isFindMatchOBUObjectIndex){
			continue;
		}
		else{
			//cout << "1............................\n";
			////去除自车目标
			if(abs(m_obuObjectMsg.obs[i].x) < 1.5 && m_obuObjectMsg.obs[i].y > -2.6 && m_obuObjectMsg.obs[i].y < 1.0)
				continue;

			{
				//common_msgs::fusiontrackingobject fusionObject;
				//fusionObject.objectsource = static_cast<uint8_t>(COMMON::SensorType::OBU);
				//fusionObject.obutimestamp = m_obuObjectMsg.timestamp;
				//fusionObject.obugpstime = m_obuObjectMsg.gpstime;
				//fusionObject.obuObject = m_obuObjectMsg.obs[i];
				//fusionObjects.obs.emplace_back(fusionObject);
			}
			//OBU object转为lidar object，存入 lidar objects TODO
			common_msgs::sensorobject obuObject = m_obuObjectMsg.obs[i];
			obuObject.relspeedx = 0;
			obuObject.relspeedy = 0;
			obuObject.value = COMMON::SensorType::OBU;
			m_lidarObjects.obs.emplace_back(obuObject);

			//cout << "obu x = " << m_obuObjectMsg.obs[i].x
			//     << ", y = " << m_obuObjectMsg.obs[i].y<< ", length = " << m_obuObjectMsg.obs[i].length
			//                 << ", width = " << m_obuObjectMsg.obs[i].width<< endl;
		}
	}
	// cout << "debug: findOBUObjectSize = " << findOBUObjectSize << endl;
	// cout << "debug: all fusionObject size = " << m_lidarObjects.obs.size() << endl;

	// 	检测框小，在基站检测框内无法匹配问题：使用距离或者点在框内
}

/***
 * 对检测的目标根据置信度降序排序
 * @param obj1 检测目标
 * @param obj2 检测目标
 * @return 前者置信度高于后者
 */
bool ObjectFusionTracking::objectSort(const common_msgs::sensorobject& obj1, const common_msgs::sensorobject& obj2){
	return obj1.confidence > obj2.confidence; // 按置信度降序排
}

void ObjectFusionTracking::lidarRadarFusion(common_msgs::sensorobject& singleLidarObject, const common_msgs::sensorobjects& radarobjects){
	float headingAnticlockwise =  2 * M_PI - singleLidarObject.azimuth;
	vector<double> boxInfo = {singleLidarObject.x,singleLidarObject.y, singleLidarObject.z,
							singleLidarObject.length, singleLidarObject.width, singleLidarObject.height, headingAnticlockwise};
	vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
	common_msgs::point3d cornerPoint0, cornerPoint1, cornerPoint2, cornerPoint3;// 底面四个点
	cornerPoint0.x = eightCornerPoints[0][0];		cornerPoint0.y = eightCornerPoints[0][1];		cornerPoint0.z = eightCornerPoints[0][2];
	cornerPoint1.x = eightCornerPoints[1][0];		cornerPoint1.y = eightCornerPoints[1][1];		cornerPoint1.z = eightCornerPoints[1][2];
	cornerPoint2.x = eightCornerPoints[2][0];		cornerPoint2.y = eightCornerPoints[2][1];		cornerPoint2.z = eightCornerPoints[2][2];
	cornerPoint3.x = eightCornerPoints[3][0];		cornerPoint3.y = eightCornerPoints[3][1];		cornerPoint3.z = eightCornerPoints[3][2];

	// 找到lidar二维框内的所有radar目标
	std::vector<std::vector<int>> candidateClosedRadarObjectIndexs(6, std::vector<int>()); // 6个radar的vector，存放在容器中，最多255个目标

	static int classificationMAX = INT_MIN;
	static int classificationMIN = INT_MAX;
	std::vector<int> matchedRadarObjectIndex;

	int radarObjectsSize = radarobjects.obs.size();
	for(int j = 0; j < radarObjectsSize; ++j){
		common_msgs::sensorobject singleRadarObject = radarobjects.obs[j];
		classificationMAX = singleRadarObject.classification < classificationMAX ? classificationMAX : singleRadarObject.classification;
		classificationMIN = singleRadarObject.classification < classificationMIN ? singleRadarObject.classification: classificationMIN;

		// bool isPointInBox = ((getCross(cornerPoint0, cornerPoint1,singleRadarObject) * getCross(cornerPoint2, cornerPoint3,singleRadarObject)) >= 0) &&
		//                     ((getCross(cornerPoint1, cornerPoint2,singleRadarObject) * getCross(cornerPoint3, cornerPoint0,singleRadarObject)) >= 0);
		bool isPointInBox = IsPointInBox(cornerPoint0, cornerPoint1,cornerPoint2, cornerPoint3,singleRadarObject);

		if(isPointInBox){
			//cout<<FYEL("singleRadarObject.classification = ") << static_cast<int>(singleRadarObject.classification) << ", j = " << j << endl;
			candidateClosedRadarObjectIndexs[singleRadarObject.radarIndex].emplace_back(j); // radar的sensorobject中的classfication存放的是radar编号
		}
	}
	//cout<<"classificationMAX = " << classificationMAX << ", classificationMIN = " << classificationMIN << endl;


	// 找到每个radar距当前lidar目标最近的radar点索引及距离
	std::vector<std::pair<int, float>> closedRadarObjectIndexAndDistances; // 每个radar的最近的索引及距离
	for (int j = 0; j < candidateClosedRadarObjectIndexs.size(); ++j) {
		float minDistance = FLT_MAX;
		int closedRadarObjectIndex = -1;
		std::vector<int> singleRadarIndex = candidateClosedRadarObjectIndexs[j]; // 前向radar的框内所有点索引
		float ldiarRadarDistance = FLT_MAX;
		for (int k = 0; k < singleRadarIndex.size(); ++k) {
			int rawRadarIndex = singleRadarIndex[k];// 前向radar的框内一个点索引
			common_msgs::sensorobject singleRadarObject = radarobjects.obs[rawRadarIndex];
			ldiarRadarDistance = abs(sqrt(pow(singleLidarObject.x - singleRadarObject.x, 2) + pow(singleLidarObject.y - singleRadarObject.y, 2)));
			if(ldiarRadarDistance < minDistance){
				minDistance = ldiarRadarDistance;
				closedRadarObjectIndex = rawRadarIndex;
			}
		}
		closedRadarObjectIndexAndDistances.emplace_back(std::make_pair(closedRadarObjectIndex,minDistance)); // 最近目标的索引以及距离
	}

	int closedRadarObjectIndex = -1;
	float minDistance = FLT_MAX;
	for (int j = 0; j < closedRadarObjectIndexAndDistances.size(); ++j) {
		std::pair<int,float> singleradarIndexAndDistance = closedRadarObjectIndexAndDistances[j];
		if(j == 0 && singleradarIndexAndDistance.first != -1 && singleradarIndexAndDistance.second < 500){ // 前向radar有匹配点，优先使用
			closedRadarObjectIndex = singleradarIndexAndDistance.first;
			break;
		}
		else{
			if(singleradarIndexAndDistance.second < minDistance){ // 前向radar没有，使用剩下的最近的radar目标
				minDistance = singleradarIndexAndDistance.second;
				closedRadarObjectIndex = singleradarIndexAndDistance.first;
			}
		}
	}
	if(closedRadarObjectIndex != -1){
		matchedRadarObjectIndex.emplace_back(closedRadarObjectIndex);
		singleLidarObject.id = radarobjects.obs[closedRadarObjectIndex].id; // TODO 确认是同一个radar的同一个目标
		singleLidarObject.relspeedy = radarobjects.obs[closedRadarObjectIndex].relspeedy; //也可以通过路径上的两点计算得到
		singleLidarObject.relspeedx = radarobjects.obs[closedRadarObjectIndex].relspeedx;
		singleLidarObject.value = COMMON::SensorType::LIDAR_RADAR;
		singleLidarObject.radarIndex = radarobjects.obs[closedRadarObjectIndex].radarIndex; // 存放目标的相对速度来源
		singleLidarObject.radarObjectID = radarobjects.obs[closedRadarObjectIndex].radarObjectID; // 存放目标的相对速度来源
		//cout<<"debug: distance 1----lidar: x=" <<singleLidarObject.x<<", y=" <<singleLidarObject.y<<"\n\tradar id="
		//	<<radarobjects.obs[closedRadarObjectIndex].id<<", radarObjectID="
		//		<<static_cast<int>(radarobjects.obs[closedRadarObjectIndex].radarObjectID)<<endl;
		m_matchedRadarobjects.obs.push_back(radarobjects.obs[closedRadarObjectIndex]);
	}
	else{
		//找最近距离的radar目标
		float minindex = -1;
		float mindis = 10000;
		float lx = singleLidarObject.x;
		float ly = singleLidarObject.y;
		int radarObjectsSize = radarobjects.obs.size();
		for(int j = 0;j < radarObjectsSize; ++j)
		{
			float rx = radarobjects.obs[j].x;
			float ry = radarobjects.obs[j].y;
			float rs = radarobjects.obs[j].relspeedy;//m/s
			float dis = (rx-lx)*(rx-lx) + (ry-ly)*(ry-ly);
			float latdis = fabs(rx-lx);
			float londis = fabs(ry-ly);
			if (londis < DELTA_DIS && latdis < DELTA_LAT && dis < mindis){
				mindis = dis;
				minindex = j;
			}
		}
		if(minindex != -1)
		{
			matchedRadarObjectIndex.emplace_back(minindex);
			singleLidarObject.id = radarobjects.obs[minindex].id; // TODO 确认是同一个radar的同一个目标
			singleLidarObject.relspeedy = radarobjects.obs[minindex].relspeedy; //也可以通过路径上的两点计算得到
			singleLidarObject.relspeedx = radarobjects.obs[minindex].relspeedx;
			singleLidarObject.value = COMMON::SensorType::LIDAR_RADAR;
			singleLidarObject.radarIndex = radarobjects.obs[minindex].radarIndex; // 存放目标的相对速度来源
			singleLidarObject.radarObjectID = radarobjects.obs[minindex].radarObjectID; // 存放目标的相对速度来源
			//cout<<"debug: distance ----lidar: x=" <<singleLidarObject.x<<", y=" <<singleLidarObject.y<<"\n\tradar id="
			//	<<radarobjects.obs[minindex].id<<", radarObjectID="
			//	<<static_cast<int>(radarobjects.obs[minindex].radarObjectID)<<endl;
			m_matchedRadarobjects.obs.push_back(radarobjects.obs[minindex]);
		}
	}

	// Generate std::vector<int> variable allRadarObjectIndex, the content of the variable is from 0 to radarobjects.obs.size()
	std::vector<int> allRadarObjectIndex(radarobjects.obs.size());
	std::iota(allRadarObjectIndex.begin(), allRadarObjectIndex.end(), 0); // Fill with 0, 1, ..., radarobjects.obs.size()

	m_unmatchedRadarObjectIndex.clear();
	// Find the index in the variable that is not in std::vector<int> matchedRadarObjectIndex and store it in std::vector<int> m_unmatchedRadarObjectIndex
	std::copy_if(allRadarObjectIndex.begin(), allRadarObjectIndex.end(), std::back_inserter(m_unmatchedRadarObjectIndex), [&](int i){
		return std::find(matchedRadarObjectIndex.begin(), matchedRadarObjectIndex.end(), i) == matchedRadarObjectIndex.end();
	});
}

void ObjectFusionTracking::getProcessedRadarObjects(common_msgs::sensorobjects& matchedRadarobjects, common_msgs::sensorobjects& unmatchedRadarobjects){
	matchedRadarobjects = m_matchedRadarobjects;
	unmatchedRadarobjects = m_unmatchedRadarobjects;

	if(m_pConfigManager->m_isUseRosBag){
		showProcessedRadarObjects(m_matchedRadarobjects, pub_matchedRadarObjects);
		// showProcessedRadarObjects(m_unmatchedRadarobjects, pub_unmatchedRadarObjects);//未填充未匹配radar数据
	}

	m_matchedRadarobjects.obs.clear();
	m_unmatchedRadarobjects.obs.clear();
}

// 显示lidar-radar匹配及未匹配的radar对象
void ObjectFusionTracking::showProcessedRadarObjects(const common_msgs::sensorobjects& radarobjects, ros::Publisher& pubRadarObjects){
	pcl::PointCloud<pcl::PointXYZI> radarCloud;
	int pointsSize = radarobjects.obs.size();
	radarCloud.points.reserve(pointsSize);
	for (int i = 0; i < pointsSize; ++i) {
		pcl::PointXYZI radarPoint;
		radarPoint.x = radarobjects.obs[i].x;
		radarPoint.y = radarobjects.obs[i].y;
		radarPoint.z = radarobjects.obs[i].z;
		radarPoint.intensity = radarobjects.obs[i].id;
		radarCloud.points.push_back(radarPoint);
	}
	sensor_msgs::PointCloud2 radarMsg;
	pcl::toROSMsg(radarCloud, radarMsg);
	radarMsg.header.frame_id = "car";
	radarMsg.header.stamp = ros::Time(radarobjects.timestamp / 1000.0);//ros::Time::now();
	radarMsg.height = 1;
	radarMsg.width = pointsSize; // 点云中的点数
	radarMsg.is_dense = false;
    radarMsg.is_bigendian = false;
	// 确保 data 字段已正确初始化
    if (radarMsg.data.size() < radarMsg.row_step * radarMsg.height) {
        radarMsg.data.resize(radarMsg.row_step * radarMsg.height);
    }
	pubRadarObjects.publish(radarMsg);
}

/***
 * 计算向量叉积（用于判断点是否在矩形内）
 * @param cornerPoint1 （矩形）第一个点
 * @param cornerPoint2 （矩形）第二个点
 * T: common_msgs::sensorobject
 * @param object 毫米波目标（用到检测的点坐标）
 * @return 叉积值
 */
template <typename T>
float ObjectFusionTracking::getCross(const common_msgs::point3d& cornerPoint1, const common_msgs::point3d& cornerPoint2,
                             const T& object){
	return (cornerPoint2.x - cornerPoint1.x) * (object.y - cornerPoint1.y) - (object.x - cornerPoint1.x) * (cornerPoint2.y - cornerPoint1.y);
}
template <typename T>
bool ObjectFusionTracking::IsPointInBox(const common_msgs::point3d& cornerPoint0, const common_msgs::point3d& cornerPoint1,
                  const common_msgs::point3d& cornerPoint2, const common_msgs::point3d& cornerPoint3,
                 const T& object) {
    return (getCross(cornerPoint0, cornerPoint1, object) * getCross(cornerPoint2, cornerPoint3, object)) >= 0 &&
           (getCross(cornerPoint1, cornerPoint2, object) * getCross(cornerPoint3, cornerPoint0, object)) >= 0;
}

void ObjectFusionTracking::getLidarOBUTimeStep(){
	m_lidarOBUSyncTimeStep = std::round((m_lidarObjects.timestamp / 1000.0 - m_obupantsMsg.timestamp / 1000.0) * 10);
	m_lidarOBUSyncTimeStep = m_lidarOBUSyncTimeStep < 0 ? 0 : m_lidarOBUSyncTimeStep;
	m_pLogger->info("use current: roadSide time = {:.3f}, currentROSTime = {:.3f}, m_lidarOBUSyncTimeStep =  {:.3f},, m_lidarMsg.timestamp = {:.3f}",
					m_obupantsMsg.timestamp / 1000.0,
					m_currentROSTime.toSec(),
					m_lidarOBUSyncTimeStep,
					m_lidarObjects.timestamp / 1000.0);
}

void ObjectFusionTracking::objectsTracking() {
	if(0){
		if (!m_isInitial){
			for (auto &object: m_lidarObjects.obs) {
			if(object.confidence < 0.4)
				continue;
				ObjectTracking objectTracking = ObjectTracking(object);
				m_trackingObjectsVector.push_back(objectTracking);
			}
			m_isInitial = true;
			return;
		}
	}
	else{
		if(!m_isInitial || !m_isStartTracking){
			float time = 0.1;
			trackingInit(time);
			return;
		}
	}


	if(m_pConfigManager->m_isUseRadarObjects){
		m_pLogger->info("添加radar objects前: m_lidarObjects size = {}, m_radarParticipants.obs.size = {}",
					m_lidarObjects.obs.size(),
					m_radarParticipants.obs.size());
		for (const auto& radarParticipant: m_radarParticipants.obs) {
			m_lidarObjects.obs.push_back(radarParticipant);
		}
		m_pLogger->info("添加radar objects前: m_lidarObjects size = {}, m_radarParticipants.obs.size = {}",
					m_lidarObjects.obs.size(),
					m_radarParticipants.obs.size());
	}

	int trackingObjectsVectorSize = m_trackingObjectsVector.size();
	if(trackingObjectsVectorSize <= 0){
		m_pLogger->info("跟踪目标列表为空，新建跟踪列表");
		for (const auto &detectionObject: m_lidarObjects.obs) { //未找到匹配的目标作为新出现的目标
			if(detectionObject.confidence < 0.4)
				continue;
			ObjectTracking objectTracking = ObjectTracking(detectionObject);//timeDiff 使用默认0.1s
			m_pLogger->info("建立跟踪器id：{}, 类别 = {}, 置信度 = {:.2f}", objectTracking.m_id, detectionObject.classification, detectionObject.confidence);
			m_trackingObjectsVector.push_back(objectTracking);
		}
		return;
	}

	trackingObjectsVectorSize = m_trackingObjectsVector.size();
	for (int i = 0; i < trackingObjectsVectorSize; i++) {
		auto &singleTrackedObject = m_trackingObjectsVector[i];
		vector<float> curPredictedInfo = singleTrackedObject.predict(m_curFrameStamp / 1000.0, m_timeDiff);
	}

	m_pLogger->info("第1次数据关联.......................");
	long predictEndTime = ros::Time::now().toSec()*1000;
	float iouThreshold = 0.05;
	objectAssociation(iouThreshold);
	long firstMatchEndTime =  ros::Time::now().toSec()*1000;
	double firstMatchTimeUse = (double)(firstMatchEndTime - predictEndTime);
	m_pLogger->info("[-----------------firstMatchTimeUse use time(ms)] = {}", firstMatchTimeUse);
	objectUpdate(iouThreshold, 1);

	m_pLogger->info("第2次数据关联.......................");
	secondObjectAssociation(); // 20230207
	float distanceThreshold = 8;//设置距离匹配阈值
	objectUpdate(distanceThreshold, 2);

	m_pLogger->info("generate tracking object size = {}", m_unmatchedDetections.size());

	if(0){
		for (const auto &detectionIndex: m_unmatchedDetections) { //未找到匹配的目标作为新出现的目标
			if(m_lidarObjects.obs[detectionIndex].confidence < 0.4){
				// printf("置信度低，不建立跟踪器id：  类别 = %d, 置信度 = %f, x = %f, y = %f\n",
				// 	m_lidarObjects.obs[detectionIndex].classification, m_lidarObjects.obs[detectionIndex].confidence
				// 	, m_lidarObjects.obs[detectionIndex].x, m_lidarObjects.obs[detectionIndex].y);
				continue;
			}
			ObjectTracking objectTracking = ObjectTracking(m_lidarObjects.obs[detectionIndex]);//timeDiff 使用默认0.1s
			m_pLogger->debug("建立跟踪器id：{}, 类别 = {}, 置信度 = {:.2f}, x = {:.2f}, y = {:.2f}",
							objectTracking.m_id, m_lidarObjects.obs[detectionIndex].classification, m_lidarObjects.obs[detectionIndex].confidence,
							m_lidarObjects.obs[detectionIndex].x, m_lidarObjects.obs[detectionIndex].y);
			m_trackingObjectsVector.push_back(objectTracking);
		}
	}
	else{
		// 当前帧未匹配的检测目标与上一帧未匹配的检测目标匹配
		// 匹配成功，当前帧未匹配的检测目标建立跟踪目标存入跟踪容器，未匹配检测目标删除
		common_msgs::sensorobjects curUnmatchedDetections;
		for (const auto &detectionIndex: m_unmatchedDetections){
			curUnmatchedDetections.obs.emplace_back(m_lidarObjects.obs[detectionIndex]);
		}
		float time = 0.1;
		manageTracks(curUnmatchedDetections, time);//进行匹配初始化跟踪器
	}

	// TODO 合并跟踪目标，更新中心点及长宽高、航向角
	mergeTrackedObjects();

	//// 更新8角点
	// update8Corners();

	///删除目标
	removeInvalidObjects();

	/// 保存障碍物信息到CSV
	if(m_pConfigManager->m_isSaveObjectInfoCSV){
		saveObjectTrackedInfo();
	}

	if(m_pConfigManager->m_isUseRosBag){
		m_pLogger->info("all tracked objects: size = {}", m_trackingObjectsVector.size());
		std::ostringstream oss;
		for(const auto &objectTracking : m_trackingObjectsVector){
			oss << " " << objectTracking.m_id;
		}
		m_pLogger->info("{} ", oss.str());
	}
}

void ObjectFusionTracking::trackingInit(float& time){
	if (!m_isInitial){
		m_preDetectionObjects.obs.clear();
		m_preDetectionObjects.obs = m_lidarObjects.obs;
		m_isInitial = true;
	}
	else if(m_isInitial && !m_isStartTracking){
		manageTracks(m_lidarObjects, time);//进行匹配初始化跟踪器

		if(!m_trackingObjectsVector.empty()){
			m_isStartTracking = true;
		}
		else{
			m_preDetectionObjects.obs = m_lidarObjects.obs;
		}
	}
}

void ObjectFusionTracking::manageTracks(const common_msgs::sensorobjects& secondSensorObjects, float& time){
	const int preDetectionSize = m_preDetectionObjects.obs.size();
	const int curDetectionSize = secondSensorObjects.obs.size();

	if(preDetectionSize == 0){
		m_preDetectionObjects.obs = secondSensorObjects.obs;
	}
	else if(curDetectionSize == 0){
		m_preDetectionObjects.obs.clear();
	}
	else{
		m_computeAssignmentMatrix.setAssignmentMatrix(preDetectionSize,curDetectionSize);
		vector<float> detectedBox1(7);
		vector<float> predictBox(7);
		std::vector<vector<float>> detectedObjectsBox;
		detectedObjectsBox.reserve(curDetectionSize);

		for(unsigned int i = 0; i < curDetectionSize; ++i){
			const auto& singleDetectedObject = secondSensorObjects.obs[i];
			detectedBox1 = {singleDetectedObject.x,  singleDetectedObject.y, 1,
							singleDetectedObject.length,  singleDetectedObject.width, 1,
							singleDetectedObject.azimuth};
			detectedObjectsBox.emplace_back(detectedBox1);
		}

		for (size_t i = 0; i < preDetectionSize; ++i){
			auto& singleTrackedObject = m_preDetectionObjects.obs[i];
			// x y l w h Heading
			predictBox = { singleTrackedObject.x,  singleTrackedObject.y, 1,
							singleTrackedObject.length,  singleTrackedObject.height, 1,
							singleTrackedObject.azimuth};
			m_computeAssignmentMatrix.setPredictedObject(predictBox);

			for(int j = 0; j < curDetectionSize; ++j){
				m_computeAssignmentMatrix.setDetectedObject(detectedObjectsBox[j]);
				m_computeAssignmentMatrix.m_assignmentMatrix[i][j] = m_computeAssignmentMatrix.getBoxIOUDistance();
			}
		}

		m_hungarianAlgorithm.Solve(m_computeAssignmentMatrix.m_assignmentMatrix, m_computeAssignmentMatrix.m_assignmentPairIndex);
		// 找出匹配目标和未匹配目标
		m_matchedPairs.clear();
		float iouThreshold = 0.1;
		vector<std::vector<int>> twoFrameObjectIndexsMatched;
		vector<int> l_vUnmatchedPreObjectsIndex;
		vector<int> l_vUnmatchedCurObjectsIndex;
		for (unsigned int i = 0; i < preDetectionSize; ++i)	{
			if (m_computeAssignmentMatrix.m_assignmentPairIndex[i] == -1){
				l_vUnmatchedPreObjectsIndex.emplace_back(i);
				continue;
			}

			int curIndex = m_computeAssignmentMatrix.m_assignmentPairIndex[i];
			common_msgs::sensorobject curSensorobject = secondSensorObjects.obs[curIndex];
			float preDetectionRadNormalized =  m_common.normalizeAngle(m_preDetectionObjects.obs[i].azimuth);
			float curDetectionRadNormalized =  m_common.normalizeAngle(curSensorobject.azimuth);
			float x = curSensorobject.x;
			float y = curSensorobject.y;
			float curDistance = sqrt(x * x + y * y);

			if (1 - m_computeAssignmentMatrix.m_assignmentMatrix[i][curIndex] < iouThreshold)
			{
				l_vUnmatchedPreObjectsIndex.emplace_back(i);
				l_vUnmatchedCurObjectsIndex.emplace_back(curIndex);
			}
			else if(cos(preDetectionRadNormalized - curDetectionRadNormalized) < 0.99 && curDistance > 10){
				// 角度相差太大不建立跟踪目标
				l_vUnmatchedPreObjectsIndex.emplace_back(i);
				l_vUnmatchedCurObjectsIndex.emplace_back(curIndex);
			}
			else {
				std::vector<int> twoFrameObjectIndexMatched = {i, curIndex};
				twoFrameObjectIndexsMatched.emplace_back(twoFrameObjectIndexMatched);
			}
		}
		// 匹配目标创建跟踪目标
		for(size_t i = 0; i < twoFrameObjectIndexsMatched.size(); ++i){
			ObjectTracking objectTracking = ObjectTracking(secondSensorObjects.obs[twoFrameObjectIndexsMatched[i][1]]);
			m_trackingObjectsVector.push_back(objectTracking);
			m_pLogger->debug("manageTracks建立跟踪器-匹配成功,id：{}, 类别 = {}, 置信度 = {:.2f}, x = {:.2f}, y = {:.2f}, value = {}, 跟踪value = {}",
							objectTracking.m_id, secondSensorObjects.obs[twoFrameObjectIndexsMatched[i][1]].classification,
							secondSensorObjects.obs[twoFrameObjectIndexsMatched[i][1]].confidence,
							secondSensorObjects.obs[twoFrameObjectIndexsMatched[i][1]].x,
							secondSensorObjects.obs[twoFrameObjectIndexsMatched[i][1]].y,
							secondSensorObjects.obs[twoFrameObjectIndexsMatched[i][1]].value,
							objectTracking.m_value);
		}

		// 未匹配目标存入m_preDetectionObjects
		m_preDetectionObjects.obs.clear();
		for (size_t i = 0; i < l_vUnmatchedCurObjectsIndex.size(); ++i){
			common_msgs::sensorobject singleSensorObject = secondSensorObjects.obs[l_vUnmatchedCurObjectsIndex[i]];
			if(m_common.isMotorVehicle(singleSensorObject.classification)){
				m_preDetectionObjects.obs.emplace_back(singleSensorObject);
			}
			else{
				ObjectTracking objectTracking = ObjectTracking(singleSensorObject);
				m_trackingObjectsVector.push_back(objectTracking);
				m_pLogger->debug("manageTracks建立跟踪器-未匹配机动车、非机动车、聚类,id：{}, 类别 = {}, 置信度 = {:.2f}, x = {:.2f}, y = {:.2f}",
								objectTracking.m_id, singleSensorObject.classification,
								singleSensorObject.confidence,
								singleSensorObject.x,
								singleSensorObject.y);
			}

		}

		m_pLogger->debug("pre-cur距离匹配：pre目标个数 = {}, 匹配的目标个数 = {}, 不匹配fusion个数 = {}, cur目标个数 = {}, 不匹配cur个数 = {}",
					preDetectionSize, twoFrameObjectIndexsMatched.size(), l_vUnmatchedPreObjectsIndex.size(), curDetectionSize, l_vUnmatchedCurObjectsIndex.size());

		// assert(preDetectionSize == twoFrameObjectIndexsMatched.size() + l_vUnmatchedPreObjectsIndex.size());
		// assert(curDetectionSize == twoFrameObjectIndexsMatched.size() + l_vUnmatchedCurObjectsIndex.size());
	}
}

/***
 * 20230207 第一次数据关联-IOU匹配
 */
void ObjectFusionTracking::objectAssociation(const float& iouThreshold){
	unsigned int trkNum = m_trackingObjectsVector.size();
	unsigned int detNum = m_lidarObjects.obs.size();
	if(trkNum == 0){
		m_matchedPairs.clear();
		m_unmatchedDetections.clear();
		m_unmatchedTrajectories.clear();
		for (int i = 0; i < detNum; ++i) {
			m_unmatchedDetections.insert(i);
		}
		m_pLogger->warn("trkNum == 0");
		return;
	}

	if(detNum == 0){
		m_matchedPairs.clear();
		m_unmatchedDetections.clear();
		m_unmatchedTrajectories.clear();
		for (unsigned int i = 0; i < trkNum; ++i){
			m_unmatchedTrajectories.insert(i);
		}
		m_pLogger->warn("detNum == 0");
		return;
	}

	m_iouMatrix.clear();
	m_iouMatrix.resize(trkNum, vector<double>(detNum, -1));

	vector<float> curPredictedInfo(8);
	vector<float> predictBox(7);
	vector<float> detectedBox1(7);
	std::vector<double> centerPointUTM(3);
	Eigen::Vector3d ENUPosition;
	float objectAngleDegreeInNorth_Clockwise;
	float headingRadInNorthClockwise;
	// 提前计算每个检测的UTM坐标及对地速度
	std::vector<vector<float>> detectedObjectsBox;
	detectedObjectsBox.reserve(detNum);

	for(unsigned int i = 0; i < detNum; ++i){
		const auto& singleDetectedObject = m_lidarObjects.obs[i];
		ENUPosition << singleDetectedObject.x, singleDetectedObject.y,singleDetectedObject.z;
		centerPointUTM = m_sensorAxisTransformer.ENU2UTM(ENUPosition);
		objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformer.CarBackRFUHeading2NorthClockwise(singleDetectedObject.azimuth * 180 / M_PI, m_gps.heading);
		headingRadInNorthClockwise = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
		detectedBox1 = {centerPointUTM[0],  centerPointUTM[1], 1,
						singleDetectedObject.length,  singleDetectedObject.width, 1,
						headingRadInNorthClockwise};
		detectedObjectsBox.emplace_back(detectedBox1);
	}

	long precessEndTime =  ros::Time::now().toSec()*1000;
	for (unsigned int i = 0; i < trkNum; ++i)
	{
		auto& singleTrackedObject = m_trackingObjectsVector[i];
		curPredictedInfo = singleTrackedObject.getCurPredictedInfo();
		// x y l w h Heading
		predictBox = { curPredictedInfo[0],  curPredictedInfo[1], 1,
						curPredictedInfo[2],  curPredictedInfo[3], 1,
						curPredictedInfo[5]};
		// cout<<FRED("predictBox11 object: ")
		// 	<<", id = "<< m_trackingObjectsVector[i].m_id
		//    <<", x = " << predictBox[0]<<", y = " << predictBox[1]<<", z = " << predictBox[2]
		//    <<", length = " << predictBox[3]<<", width = " << predictBox[4]<<", height = " << predictBox[5]
		//    <<", azimuth = " <<predictBox[6] * 180 / M_PI
		//    << endl;
		m_computeAssignmentMatrix.setPredictedObject(predictBox);

		for (unsigned int j = 0; j < detNum; ++j){
			m_computeAssignmentMatrix.setDetectedObject(detectedObjectsBox[j]);
			long ioustartTime = ros::Time::now().toSec()*1000;
			//m_iouMatrix[i][j] = m_computeAssignmentMatrixPtr->getWeightingDistance();//获取加权值
			m_iouMatrix[i][j] = m_computeAssignmentMatrix.getBoxIOUDistance();//只获取IOU的值
			//m_iouMatrix[i][j] = m_computeAssignmentMatrixPtr->getLocationDistance();//只获取中心点距离的值
			//cout<<FRED("iou = ") << 1-m_iouMatrix[i][j] << endl;
			//m_computeAssignmentMatrix.resetDistance();

			//if(m_lidarObjects.obs[j].value == COMMON::SensorType::RADAR){
			//	cout << FGRN("detectedBox12 object: ")
			//	     << "x = " << detectedBox1[0] << ", y = " << detectedBox1[1] << ", z = " << detectedBox1[2]
			//		 << ", ENU: x = " << ENUPosition[0] << ", y = " << ENUPosition[1] << ", z = " << ENUPosition[2]
			//	     << ", length = " << detectedBox1[3] << ", width = " << detectedBox1[4] << ", height = "
			//	     << detectedBox1[5]
			//	     << ", azimuth = " << detectedBox1[6] * 180 / M_PI
			//	     << ", iou = " << 1-m_iouMatrix[i][j]
			//		 << endl;
			//}

			long IOUEndTime =  ros::Time::now().toSec()*1000;
			double iouTimeUse = (double)(IOUEndTime - ioustartTime);
			if(iouTimeUse >= 1)
				m_pLogger->debug("---------------iouTimeUse(ms): {}", iouTimeUse);
		}
		//cout<<FRED(".................................") << endl;
	}
	long matchEndTime =  ros::Time::now().toSec()*1000;
	double matchTimeUse = (double)(matchEndTime - precessEndTime);
	m_pLogger->debug("matchTimeUse(ms): {}, tracking size: {}, detection size: {}", matchTimeUse, trkNum, detNum);

	m_assignment.clear();
	m_pLogger->debug("start first solve");
	m_hungarianAlgorithm.Solve(m_iouMatrix, m_assignment);//得到匹配对assignment：i是跟踪索引，m_assignment[i]是索引i跟踪对应的检测索引
	m_pLogger->debug("end first solve");
	m_unmatchedTrajectories.clear();
	m_unmatchedDetections.clear();
	m_allItems.clear();
	m_matchedItems.clear();
	m_pLogger->info("检测数量： = {}, 跟踪数量：= {}", detNum, trkNum);

	m_matchedPairs.clear();

	if (detNum > trkNum) //	there are unmatched detections 检测目标数大于跟踪目标数（新出现的、误剔除或者长时间检测不到误删除的）
	{
		for (unsigned int n = 0; n < detNum; n++) {
			m_allItems.insert(n);//目标数量多的全部存入allItems
		}
		for (unsigned int i = 0; i < trkNum; ++i) {
			m_matchedItems.insert(m_assignment[i]);//目标数量少的全部存入matchedItems
		}
		//	获取未匹配检测结果
		//	set_difference() 算法可以创建两个集合的差集，得到allItems中未与matchedItems匹配的目标ID存入unmatchedDetections
		set_difference(m_allItems.begin(), m_allItems.end(), m_matchedItems.begin(), m_matchedItems.end(),
		               insert_iterator<set<int>>(m_unmatchedDetections, m_unmatchedDetections.begin()));

	}
	else if (detNum < trkNum) // there are unmatched trajectory/predictions 跟踪目标数大于检测目标数，将未匹配的目标划为未跟踪轨迹
	{//包含新出现的检测目标、之前检测的目标、
		for (unsigned int i = 0; i < trkNum; ++i) {
			if (m_assignment[i] == -1){// unassigned label will be set as -1 in the m_assignment algorithm
				m_unmatchedTrajectories.insert(i);
			}
		}
	}

	// filter out matched with low IOU 低于IOU阈值的划为未匹配，更新匹配对
	m_matchedPairs.clear();
	for (unsigned int i = 0; i < trkNum; ++i)
	{

		if (m_assignment[i] == -1){// pass over invalid values
			continue;
		}
		if (1 - m_iouMatrix[i][m_assignment[i]] < iouThreshold
            || (isMotorVehicle(m_trackingObjectsVector[i].m_classification) && !isMotorVehicle(m_lidarObjects.obs[m_assignment[i]].classification)))	// TODO 误匹配限制&& trackers[i].classfication != detData[m_assignment[i]].label
		{
//            if(abs(m_trackingObjectsVector[i].m_trackingBoxResult[2] - 2 * m_lidarObjects.obs[m_assignment[i]].length)> 0
//               && abs(m_trackingObjectsVector[i].m_trackingBoxResult[3] - 2 * m_lidarObjects.obs[m_assignment[i]].width) > 0){
//
//            }


		m_unmatchedTrajectories.insert(i);
		m_unmatchedDetections.insert(m_assignment[i]);

		}
		else {
			m_matchedPairs.push_back(std::vector<int>{i, m_assignment[i]});//存入跟踪ID、检测ID。//TODO 匹配成功验证，
		}
	}

	if(m_pConfigManager->m_isUseRosBag){
		m_pLogger->info("第1次匹配-IOU匹配ID： id:");
		std::ostringstream oss;
		for (unsigned int i = 0; i < m_matchedPairs.size(); i++){
			oss<<""<<m_trackingObjectsVector[m_matchedPairs[i][0]].m_id<<", "
			;

			// cout<<"id = "<<m_trackingObjectsVector[m_matchedPairs[i][0]].m_id
			// 	<<", iou = " << 1 - m_iouMatrix[m_matchedPairs[i][0]][m_matchedPairs[i][1]]
			// <<", detection x = "<< m_lidarObjects.obs[m_assignment[i]].x
			// <<", detection y = " << m_lidarObjects.obs[m_assignment[i]].y
			// << endl;
		}
		m_pLogger->info("{}", oss.str());

		m_pLogger->info("第1次匹配-不成功匹配ID： id:");
		oss.str("");
		for(auto it = m_unmatchedTrajectories.begin();it!=m_unmatchedTrajectories.end();it++) {
			oss<<"id = "<<m_trackingObjectsVector[*it].m_id <<", ";
			// cout<<"id = "<<m_trackingObjectsVector[*it].m_id
			// 	<<", iou = " << 1 - m_iouMatrix[*it][m_assignment[*it]] << endl;
		}
		m_pLogger->info("{}", oss.str());

		m_pLogger->info("第1次匹配：原始融合检测目标个数= {}, 匹配的目标个数= {}, 不匹配检测个数= {}",
						detNum, m_matchedPairs.size(),m_unmatchedDetections.size());
		// assert(detNum == m_matchedPairs.size() +m_unmatchedDetections.size());
		m_pLogger->info("第1次匹配：跟踪目标个数 = {}, 匹配的目标个数= {}, 未匹配跟踪目标个数= {}",
						trkNum, m_matchedPairs.size(),m_unmatchedTrajectories.size());
		// assert(trkNum == m_matchedPairs.size() +m_unmatchedTrajectories.size());
	}
	m_pLogger->info("第1次匹配-finished: \n\t");
}

/***
 * 20230207 第二次数据关联：距离关联
 */
void ObjectFusionTracking::secondObjectAssociation(){
	v_unmatchedDetectionsIndex_.clear();
	for(set<int>::iterator it = m_unmatchedDetections.begin();it!=m_unmatchedDetections.end();it++) {
		v_unmatchedDetectionsIndex_.emplace_back(*it);
	}

	v_unmatchedTrajectoriesIndex_.clear();
	for(set<int>::iterator it = m_unmatchedTrajectories.begin();it!=m_unmatchedTrajectories.end();it++) {
		v_unmatchedTrajectoriesIndex_.emplace_back(*it);
	}

	m_assignment.clear();
	m_iouMatrix.clear();//TODO 更改iouMatrix名称为距离matrix或统一使用associateMatrix
	m_iouMatrix.resize(v_unmatchedTrajectoriesIndex_.size(), vector<double>(v_unmatchedDetectionsIndex_.size(), -1));

	vector<float> curPredictedInfo(8);
	vector<float> predictBox(7);
	vector<float> detectedBox1(7);
	std::vector<double> centerPointUTM(3);
	std::vector<float> matchParams{3,6};// 运动静止分情况设置椭圆门限阈值
	m_computeAssignmentMatrix.setMatchParams(matchParams);

	int unmatchedTrajectoriesIndex_Size = v_unmatchedTrajectoriesIndex_.size();
	int unmatchedDetectionsIndex_Size = v_unmatchedDetectionsIndex_.size();
	float matchMaxDistance = 3;

	// 提前计算每个检测的UTM坐标及对地速度
	std::vector<vector<float>> unmatchedDetectedObjectsBox;
	unmatchedDetectedObjectsBox.reserve(unmatchedDetectionsIndex_Size);
	Eigen::Vector3d ENUPosition;
	std::vector<std::vector<double>> unmatchedDetectedObjectsCenterPointUTM;
	unmatchedDetectedObjectsCenterPointUTM.reserve(unmatchedDetectionsIndex_Size);
	std::vector<double> unmatchedDetectedObjectsHeadingRadInNorthClockwise;
	unmatchedDetectedObjectsHeadingRadInNorthClockwise.reserve(unmatchedDetectionsIndex_Size);

	for(unsigned int i = 0; i < unmatchedDetectionsIndex_Size; ++i){
		int umdIndex = v_unmatchedDetectionsIndex_[i];
		auto& singleDetectedObject = m_lidarObjects.obs[umdIndex];

		ENUPosition << singleDetectedObject.x, singleDetectedObject.y,singleDetectedObject.z;
		centerPointUTM = m_sensorAxisTransformer.ENU2UTM(ENUPosition);
		unmatchedDetectedObjectsCenterPointUTM.emplace_back(centerPointUTM);

		//关联矩阵使用中心点距离
		float objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformer.CarBackRFUHeading2NorthClockwise(singleDetectedObject.azimuth * 180 / M_PI, m_gps.heading);
		float headingRadInNorthClockwise = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
		unmatchedDetectedObjectsHeadingRadInNorthClockwise.emplace_back(headingRadInNorthClockwise);
		detectedBox1 = {centerPointUTM[0],  centerPointUTM[1], 1,
						singleDetectedObject.length,  singleDetectedObject.width, 1,
						headingRadInNorthClockwise};
		unmatchedDetectedObjectsBox.emplace_back(detectedBox1);
	}

	std::vector<double>  objectPositionInCarBackFRU(3);
	bool isUseEuclideanDistanceMethod = true;
	for (int i = 0; i < unmatchedTrajectoriesIndex_Size; ++i) {
		int umtIndex = v_unmatchedTrajectoriesIndex_[i];
		auto& singleTrackedObject = m_trackingObjectsVector[umtIndex];
		curPredictedInfo = singleTrackedObject.getCurPredictedInfo();
		predictBox = { curPredictedInfo[0],  curPredictedInfo[1], 1,
		                             curPredictedInfo[2],  curPredictedInfo[3], 1,
		                             curPredictedInfo[5]};
		float speed = sqrt(curPredictedInfo[6] * curPredictedInfo[6] + curPredictedInfo[7] * curPredictedInfo[7]);

		if(isMotorVehicle(singleTrackedObject.m_classification)){
			matchMaxDistance = max(singleTrackedObject.m_trackingBoxResult[2], singleTrackedObject.m_trackingBoxResult[3]);
		}
		bool isTrackedObjectMotorVehicle = isMotorVehicle(singleTrackedObject.m_classification);
		// if (singleTrackedObject.m_id == m_pConfigManager->m_debugID)
		// 	cout<<FRED("predictBox11 object: ")
		// 	<<", id = "<< m_trackingObjectsVector[umtIndex].m_id
		// 	<<", x = " << predictBox[0]<<", y = " << predictBox[1]
		// 	//<<", length = " << predictBox[3]<<", width = " << predictBox[4]<<", height = " << predictBox[5]
		// 	<<", azimuth = " <<predictBox[6] * 180 / M_PI
		// 	<<", matchMaxDistance = " <<matchMaxDistance
		// 	<<", isTrackedObjectMotorVehicle = " <<isTrackedObjectMotorVehicle
		// 	<<", speed = " <<speed
		// 	<<", curPredictedInfo[6] = " <<curPredictedInfo[6]
		// 	<<", curPredictedInfo[7] = " <<curPredictedInfo[7]
		// 	<< endl;
		m_computeAssignmentMatrix.setPredictedObject(predictBox);
		for (int j = 0; j < unmatchedDetectionsIndex_Size; ++j) {
			int umdIndex = v_unmatchedDetectionsIndex_[j];
			auto& singleDetectedObject = m_lidarObjects.obs[umdIndex];
			m_computeAssignmentMatrix.setDetectedObject(unmatchedDetectedObjectsBox[j]);

			if(isUseEuclideanDistanceMethod)
			{
				float associationDistance = m_computeAssignmentMatrix.getLocationDistance();
				bool isDetectedObjectNonMotorVehicle = m_common.isNonMotorVehicle(singleDetectedObject.classification);
				bool isDetectedObjectUnknown = m_common.isClusterObject(singleDetectedObject.classification);

				float positionAngle = atan2f(unmatchedDetectedObjectsCenterPointUTM[j][0]- curPredictedInfo[0], unmatchedDetectedObjectsCenterPointUTM[j][1]- curPredictedInfo[1] + 1e-6);
				positionAngle = m_common.normalizeAngle(positionAngle);

				// 自车carbackRFU下的角度偏差
				Eigen::Vector3d inputUTMPosition{unmatchedDetectedObjectsCenterPointUTM[j][0]- curPredictedInfo[0],
												unmatchedDetectedObjectsCenterPointUTM[j][1]- curPredictedInfo[1],
												0};
				objectPositionInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(inputUTMPosition);
				float distanceCarbackRFU = sqrt(objectPositionInCarBackFRU[0] * objectPositionInCarBackFRU[0] + objectPositionInCarBackFRU[1] * objectPositionInCarBackFRU[1]);


				float tempHeadingRadInNorthCLockwise = unmatchedDetectedObjectsHeadingRadInNorthClockwise[j];
				float acc = distanceCarbackRFU / (0.1 * 0.1);

				if(associationDistance > matchMaxDistance){//大于距离阈值不匹配
					m_iouMatrix[i][j] = FLT_MAX;
				}
				else if(speed > 2 && positionAngle > M_PI / 2.0){ // 速度大于2m/s，且预测位置与检测位置的角度大于90度，认为反向的检测目标不是匹配目标
					m_iouMatrix[i][j] = FLT_MAX;
				}
				else if(isTrackedObjectMotorVehicle && isDetectedObjectNonMotorVehicle){//机动车与非机动车不匹配
					m_iouMatrix[i][j] = FLT_MAX;
				}
				else if(isTrackedObjectMotorVehicle && isDetectedObjectUnknown){ //  && distanceCarbackRFU > curPredictedInfo[2]
					m_iouMatrix[i][j] = FLT_MAX;
				} // 机动车与聚类，距离超过长不匹配
				else{
					if (singleTrackedObject.m_id == m_pConfigManager->m_debugID){
						m_pLogger->debug("第二次匹配 跟踪id = {}, 跟踪角度 = {:.2f}, 检测id = {}, 类别 = {}， positionAngle = {:.2f}, det x = {:.2f}, det y = {:.2f}, associationDistance = {:.2f}，acc = {:.2f}",
								singleTrackedObject.m_id,  curPredictedInfo[5] * 180.0 / M_PI, singleDetectedObject.id,
								(int)singleDetectedObject.classification, positionAngle * 180 / M_PI,
								singleDetectedObject.x, singleDetectedObject.y,
								associationDistance,acc);
					}
					m_iouMatrix[i][j] = associationDistance;
				}
			}
			else{
				m_iouMatrix[i][j] = m_computeAssignmentMatrix.getEllipseLocationDistance();//只获取中心点椭圆门限距离的值
			}
		}
	}
	m_assignment.clear();
	m_pLogger->debug("start second solve");
	m_hungarianAlgorithm.Solve(m_iouMatrix, m_assignment);//得到匹配对assignment
	m_pLogger->debug("end second solve");
	//显示指派
	set<int> l_unmatchedTrajectories;
	set<int> l_unmatchedDetections;

	m_matchedPairs.clear();
	for(int i = 0; i < v_unmatchedTrajectoriesIndex_.size(); ++i){
		if(m_assignment[i] == -1){
			l_unmatchedTrajectories.insert(v_unmatchedTrajectoriesIndex_[i]);
			continue;
		}

		bool l_isMatch = true;
		if(isUseEuclideanDistanceMethod){
			if(m_iouMatrix[i][m_assignment[i]] > matchMaxDistance){
				l_unmatchedDetections.insert(v_unmatchedDetectionsIndex_[m_assignment[i]]);
				l_unmatchedTrajectories.insert(v_unmatchedTrajectoriesIndex_[i]);
				m_assignment[i] = -1;
				l_isMatch = false;
			}
		}
		else{
			if(m_iouMatrix[i][m_assignment[i]] > 1){
				l_unmatchedDetections.insert(v_unmatchedDetectionsIndex_[m_assignment[i]]);
				l_unmatchedTrajectories.insert(v_unmatchedTrajectoriesIndex_[i]);
				m_assignment[i] = -1;
				l_isMatch = false;
			}
		}

		if(l_isMatch){
			m_matchedPairs.push_back(std::vector<int>{v_unmatchedTrajectoriesIndex_[i], v_unmatchedDetectionsIndex_[m_assignment[i]]});//存入跟踪ID、检测ID。
		}
	}

	// 找到未匹配的检测:既不在未匹配容器中也不在未匹配的容器中的目标添加到未匹配容器中
	for (int i = 0; i < unmatchedDetectionsIndex_Size; ++i){
		int umdIndex = v_unmatchedDetectionsIndex_[i];
		auto it = l_unmatchedDetections.find(umdIndex);
		if(it == l_unmatchedDetections.end()){
			auto it2 = std::find_if(m_matchedPairs.begin(), m_matchedPairs.end(), [&](const std::vector<int>& pair){
				return pair[1] == umdIndex;
			});
			if(it2 == m_matchedPairs.end()){
				l_unmatchedDetections.insert(umdIndex);
			}
		}
	}


	m_unmatchedTrajectories.clear();
	m_unmatchedTrajectories = l_unmatchedTrajectories;
	m_unmatchedDetections.clear();
	m_unmatchedDetections = l_unmatchedDetections;

	if(m_pConfigManager->m_isUseRosBag){
		m_pLogger->info("第2次匹配-不成功匹配ID： id:");
		std::ostringstream oss;
		for(set<int>::iterator it = m_unmatchedTrajectories.begin();it!=m_unmatchedTrajectories.end();it++) {
			oss << m_trackingObjectsVector[*it].m_id << ", ";
		}
		m_pLogger->info("{}",oss.str());

		m_pLogger->info("第2次匹配-距离匹配ID： id:");
		oss.str("");
		for (unsigned int i = 0; i < m_matchedPairs.size(); i++){
			oss<<""<<m_trackingObjectsVector[m_matchedPairs[i][0]].m_id<<", ";
		}
		m_pLogger->info("{}",oss.str());

		m_pLogger->info("第2次匹配：检测目标个数= {}, 匹配的目标个数= {}, 不匹配检测个数= {}",
						unmatchedDetectionsIndex_Size, m_matchedPairs.size(),m_unmatchedDetections.size());
		//assert(unmatchedDetectionsIndex_Size == m_matchedPairs.size() +m_unmatchedDetections.size());
		m_pLogger->info("第2次匹配：跟踪目标个数= {}, 匹配的目标个数= {}, 未匹配跟踪目标个数= {}",
						unmatchedTrajectoriesIndex_Size, m_matchedPairs.size(),m_unmatchedTrajectories.size());
		//assert(unmatchedTrajectoriesIndex_Size == m_matchedPairs.size() +m_unmatchedTrajectories.size());

		// for (int i = 0; i < m_iouMatrix.size(); ++i) {
		// 	for (int j = 0; j < m_iouMatrix[i].size(); ++j) {
		// 		cout<< m_iouMatrix[i][j] <<",";
		// 	}
		// 	cout<<endl;
		// }
		// cout<<endl;
	}

	m_pLogger->info("第2次匹配-距离匹配ID finished");
}

/***
 * 20230208 目标状态更新
 * @param m_lidarObjects 当前帧检测（融合）结果

 * @param relevancyThreshold 关联项阈值
 * @param updateCount 关联更新次数
 */
void ObjectFusionTracking::objectUpdate(const float& relevancyThreshold, const unsigned int& updateCount){
	float threshold = relevancyThreshold;
	int assignmentSize = m_assignment.size();
	for (int i = 0; i < assignmentSize; ++i) {
		if(m_assignment[i] == -1)
				continue; // 跳过错误匹配对

		int trackerIndex = 0;
		int detectionIndex = 0;

		bool updateFlag = false;
		if(updateCount == 1){ // IOU

			auto it = std::find_if(m_matchedPairs.begin(), m_matchedPairs.end(),[&](const std::vector<int>& pair){
				return pair[0] == i;
			});
			if(it != m_matchedPairs.end()){
				trackerIndex = i;
				detectionIndex = m_assignment[i];
				updateFlag = (m_assignment[i] != -1) && (1 - m_iouMatrix[i][m_assignment[i]] > threshold);
			}
			else{
				trackerIndex = i;
				// cout<<"WARN: not match pair , id = " << m_trackingObjectsVector[trackerIndex].m_id << endl;
			}
		}
		else if(updateCount == 2){ //Distance
			trackerIndex = v_unmatchedTrajectoriesIndex_[i];
			detectionIndex = v_unmatchedDetectionsIndex_[m_assignment[i]];
			updateFlag = (m_assignment[i] != -1) && (m_iouMatrix[i][m_assignment[i]] < threshold);
			// updateFlag = (m_assignment[i] != -1);
		}
		else{
			m_pLogger->warn("not given updateCount = {}", updateCount);
		}

		if (updateFlag){// pass over invalid values
			//类别修正
			updateClassification(trackerIndex, detectionIndex, updateCount);

			m_trackingObjectsVector[trackerIndex].m_confidence = m_lidarObjects.obs[detectionIndex].confidence;

			if(1) {//lidar-obu融合上固定融合标志
				int fusionTypeValue = static_cast<int>(m_lidarObjects.obs[detectionIndex].value);
				int trackingTypeValue = m_trackingObjectsVector[trackerIndex].m_value;
				//跟踪目标已经融合上了（融合标志不是LIDAR_RADAR_OBU、LIDAR_OBU类型，对融合标志更新）m_isInStationRange &&
				// if (trackingTypeValue != COMMON::SensorType::LIDAR_RADAR_OBU
				//     && trackingTypeValue != COMMON::SensorType::LIDAR_OBU) {//lidar-obu融合上不更新融合标志位
				// 	m_trackingObjectsVector[trackerIndex].m_value = m_lidarObjects.obs[detectionIndex].value;
				// }

				std::vector<double> v_lonlat;
				m_wgs84Utm.utm2LLADegree(m_trackingObjectsVector[trackerIndex].m_trackingBoxResult[0], m_trackingObjectsVector[trackerIndex].m_trackingBoxResult[1],
										m_pConfigManager->m_cityUTMCode, false, v_lonlat);
				bool l_isInStationRange = isInStationRange(v_lonlat[1], v_lonlat[0], 150);

				switch (trackingTypeValue)
				{
					case COMMON::SensorType::LIDAR_OBU:
					case COMMON::SensorType::LIDAR_RADAR_OBU:
						{
							if(l_isInStationRange){
								if(fusionTypeValue == COMMON::SensorType::LIDAR){
									if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
										m_pLogger->debug("update 1 跟踪类型={},更改类型 = {}",trackingTypeValue,  COMMON::SensorType::LIDAR_OBU);
									}
									m_trackingObjectsVector[trackerIndex].m_value = COMMON::SensorType::LIDAR_OBU;
								}
								else if(fusionTypeValue == COMMON::SensorType::LIDAR_RADAR){
									// 基站范围内不变
									if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
										m_pLogger->debug("update 2 跟踪类型={},更改类型 = {}",trackingTypeValue,  COMMON::SensorType::LIDAR_RADAR_OBU);
									}
									m_trackingObjectsVector[trackerIndex].m_value = COMMON::SensorType::LIDAR_RADAR_OBU;
								}
								else{
									if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
										m_pLogger->debug("update 3 跟踪类型={},更改类型 = {}",trackingTypeValue,  COMMON::SensorType::LIDAR_RADAR_OBU);
									}
								}

							}
							else{
								if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
									m_pLogger->debug("update 4 跟踪类型={},更改类型 = {}, lon = {:.7f}, lat = {:.7f}",
													trackingTypeValue, fusionTypeValue, v_lonlat[0], v_lonlat[1]);
								}
								m_trackingObjectsVector[trackerIndex].m_value = fusionTypeValue;
								m_trackingObjectsVector[trackerIndex].m_sStationObjectsInfo.m_isMatched = false;
							}
						}
						break;
					case COMMON::SensorType::LIDAR:
						{
							if(l_isInStationRange && m_trackingObjectsVector[trackerIndex].m_sStationObjectsInfo.m_isMatched){
								if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
										m_pLogger->debug("update 5 跟踪类型={},更改类型 = {}",trackingTypeValue,  COMMON::SensorType::LIDAR_OBU);
									}
								m_trackingObjectsVector[trackerIndex].m_value = COMMON::SensorType::LIDAR_OBU;
							}
							else{
								if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
									m_pLogger->debug("update 6 跟踪类型={},更改类型 = {}, l_isInStationRange = {}, m_isMatched = {}",
													trackingTypeValue, m_lidarObjects.obs[detectionIndex].value, l_isInStationRange, m_trackingObjectsVector[trackerIndex].m_sStationObjectsInfo.m_isMatched);
								}
								m_trackingObjectsVector[trackerIndex].m_value = m_lidarObjects.obs[detectionIndex].value;
							}
						}
						break;
					case COMMON::SensorType::LIDAR_RADAR:
						{
							if(l_isInStationRange && m_trackingObjectsVector[trackerIndex].m_sStationObjectsInfo.m_isMatched){
								if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
									m_pLogger->debug("update 7 跟踪类型={},更改类型 = {}",trackingTypeValue, COMMON::SensorType::LIDAR_RADAR_OBU);
								}
								m_trackingObjectsVector[trackerIndex].m_value = COMMON::SensorType::LIDAR_RADAR_OBU;
							}
							else{
								if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
									m_pLogger->debug("update 8 跟踪类型={},更改类型 = {}",trackingTypeValue, m_lidarObjects.obs[detectionIndex].value);
								}
								m_trackingObjectsVector[trackerIndex].m_value = m_lidarObjects.obs[detectionIndex].value;
							}
						}
						break;
					default:
						{
							if(m_trackingObjectsVector[trackerIndex].m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("update 9 跟踪类型={},更改类型 = {}",trackingTypeValue, m_lidarObjects.obs[detectionIndex].value);
							}
							m_trackingObjectsVector[trackerIndex].m_value = m_lidarObjects.obs[detectionIndex].value;
							break;
						}
				}
			}
			else{//正常更新融合标志
				m_trackingObjectsVector[trackerIndex].m_value = m_lidarObjects.obs[detectionIndex].value;
			}

			m_trackingObjectsVector[trackerIndex].m_updateCount = updateCount;//20230214 记录第几次匹配成功
			m_trackingObjectsVector[trackerIndex].m_radarIndex = m_lidarObjects.obs[detectionIndex].radarIndex; //m_lidarObjects.obs[detectionIndex].m_radarIndex;
			m_trackingObjectsVector[trackerIndex].m_radarObjectID = m_lidarObjects.obs[detectionIndex].radarObjectID; //m_lidarObjects.obs[detectionIndex].m_radarObjectID

			//更新
			m_trackingObjectsVector[trackerIndex].update(m_lidarObjects.obs[detectionIndex], m_curFrameStamp/1000.0);


			float distance = updateCount == 1 ? 1 - m_iouMatrix[i][m_assignment[i]] : m_iouMatrix[i][m_assignment[i]];

			if(updateCount == 2 || updateCount == 3){ //Distance
				m_unmatchedDetections.erase(detectionIndex);// 从未匹配检测和未匹配跟踪中移除匹配的ID
				m_unmatchedTrajectories.erase(trackerIndex);
			}

		}
		else{
			// cout<<"第 "<<updateCount<<" 次 不 更新id = "<< m_trackingObjectsVector[trackerIndex].m_id
			// 	<< ", iou distance = " << 1 - m_iouMatrix[i][m_assignment[i]]
			// 	<<", 距离匹配阈值 = "<<threshold
			// 	<<", 类别 = "<<m_trackingObjectsVector[trackerIndex].m_classification
			// 	<<", x = "<<m_trackingObjectsVector[trackerIndex].m_objectOriginal.x
			// 	<<", y = "<<m_trackingObjectsVector[trackerIndex].m_objectOriginal.y<<endl;
		}
		//cout<<"m_assignment.size() = " << m_assignment.size() << ", tracker size = " << m_trackingObjectsVector.size()
		//    << ", trackerIndex = "<< trackerIndex << endl;
		// cout<<"更新次数："<< updateCount <<", 更新 id = "<< m_trackingObjectsVector[trackerIndex].m_id <<", 匹配阈值 = "
		//     <<threshold<<", 运动状态 = "<<m_trackingObjectsVector[trackerIndex].m_motionInfo<<endl;
		//  cout<<"\n..........................\n";
	}
	// printf("更新次数： %d finish\n", updateCount);
}


void ObjectFusionTracking::mergeTrackedObjects(){

	for(size_t i=0; i<m_trackingObjectsVector.size(); ++i){
		ObjectTracking& trackedObject1 = m_trackingObjectsVector[i];
		for(size_t j=i+1; j<m_trackingObjectsVector.size(); ++j){
			ObjectTracking& trackedObject2 = m_trackingObjectsVector[j];

			bool isFilterByLengthWidth = abs(trackedObject1.m_boxSize[0] - trackedObject2.m_boxSize[1]) > 1
										|| abs(trackedObject1.m_boxSize[1] - trackedObject2.m_boxSize[0]) > 1;
			bool isFilterByPosition = abs(trackedObject2.m_objectOriginal.x) > 4;

			// 4种情况：cluster-detecttion(跳过)，cluster-cluster， detection-detection（跳过）， detection-cluster
			if((trackedObject1.m_classification == 7 && trackedObject2.m_classification != 7)
				|| (trackedObject1.m_classification != 7 && trackedObject2.m_classification != 7)
				|| (!isFilterByLengthWidth) || isFilterByPosition
			){
				continue;
			}

			bool removeTarget = false;
			mergeObjects(trackedObject1, trackedObject2, removeTarget);
			// 根据跟踪时间确定合并方向
			if (removeTarget) {

				float deltaX = trackedObject1.m_objectOriginal.x - trackedObject2.m_objectOriginal.x;
				float deltaY = trackedObject1.m_objectOriginal.y - trackedObject2.m_objectOriginal.y;
				float distance = sqrt(deltaX * deltaX + deltaY * deltaY);
				// 如果用IOU，添加距离限制
				// if( distance > 4)
				// 	continue;

				m_pLogger->info("merge tracked objects: [reserve id] = {}, x = {:.2f}, y = {:.2f}, class = {}, length = {:.2f}, width= {:.2f}",
								trackedObject1.m_id,trackedObject1.m_objectOriginal.x,trackedObject1.m_objectOriginal.y,trackedObject2.m_classification,
								trackedObject1.m_objectOriginal.length, trackedObject1.m_objectOriginal.width);
				m_pLogger->info("[delete id] = {}, x = {:.2f}, y = {:.2f}, class = {}, length = {:.2f}, width= {:.2f}, distance = {:.2f}",
								trackedObject2.m_id,trackedObject2.m_objectOriginal.x,trackedObject2.m_objectOriginal.y,trackedObject2.m_classification,
								trackedObject2.m_objectOriginal.length, trackedObject2.m_objectOriginal.width,
								distance);
				mergedObjectsUpdate(trackedObject1, trackedObject2);
				m_trackingObjectsVector.erase(m_trackingObjectsVector.begin() + j);
				--j;
			}
			else{
				// cout<<FYEL("merge tracked objects: ") << "reserve id = " << trackedObject1.m_id
				// 	<< ", x = " << trackedObject1.m_objectOriginal.x
				// 	<< ", y = " << trackedObject1.m_objectOriginal.y
				// 	<< ", m_age = " << trackedObject1.m_age
				// 	<< ", m_hit_streak = " << trackedObject1.m_hit_streak
				// 	<< ", m_time_since_update = " << trackedObject1.m_time_since_update
				// 	<< ", m_classification = " << trackedObject1.m_classification
				// 	<< ", length = " << trackedObject1.m_trackingBoxResult[2]
				// 	<< ", width = " << trackedObject1.m_trackingBoxResult[3]
				// 	<< ", heigh = " << trackedObject1.m_trackingBoxResult[4]
				// 	<< ", headingdegree = " << trackedObject1.m_trackingBoxResult[5]
				// 	<<", delete id = " << trackedObject2.m_id
				// 	<< ", x = " << trackedObject2.m_objectOriginal.x
				// 	<< ", y = " << trackedObject2.m_objectOriginal.y
				// 	<< ", m_age = " << trackedObject2.m_age
				// 	<< ", m_hit_streak = " << trackedObject2.m_hit_streak
				// 	<< ", m_time_since_update = " << trackedObject2.m_time_since_update
				// 	<< ", m_classification = " << trackedObject2.m_classification
				// 	<< ", length = " << trackedObject2.m_trackingBoxResult[2]
				// 	<< ", width = " << trackedObject2.m_trackingBoxResult[3]
				// 	<< ", heigh = " << trackedObject2.m_trackingBoxResult[4]
				// 	<< ", headingdegree = " << trackedObject2.m_trackingBoxResult[5]
				// 	<< endl;
			}

		}
	}
}

void ObjectFusionTracking::mergedObjectsUpdate(ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2){
	for(size_t i=0; i<3; i++){
		trackedObject1.m_centerPointUTM[i] =  (trackedObject1.m_centerPointUTM[i] + trackedObject2.m_centerPointUTM[i]) / 2.0;
	}
	// 相对速度、绝对UTM速度（新建目标不准，不更新速度）

	for(size_t i=0; i<trackedObject2.m_objectOriginal.points.size(); ++i){
		trackedObject1.m_objectOriginal.points.push_back(trackedObject2.m_objectOriginal.points[i]);
	}
	// TODO 更新长宽
}

void ObjectFusionTracking::mergeObjects(const ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2, bool& removeTarget){
	mergeTrackedObjectByPoints(trackedObject1, trackedObject2, removeTarget);

	if (removeTarget) {
		m_pLogger->info("merge tracked objects: reserve id = {}, 点数大于10", trackedObject1.m_id);
		return;
	}
	else {
		// 如果目标是聚类，考虑是否有IOU，IOU超过阈值，认为是同一个目标，移除，否则保留
		// TODO trackedObject1检测，trackedObject2聚类：2合并到1；trackedObject1聚类，trackedObject2检测：1合并到2；
		mergeTrackedObjectByIOU(trackedObject1, trackedObject2, removeTarget);
	}
	// if(trackedObject1.m_classification == COMMON::LidarDetectionClassification::Unknown
	// 	|| trackedObject2.m_classification == COMMON::LidarDetectionClassification::Unknown){

	// 	}
	// else{
	// 	removeTarget = false;
	// }
}

void ObjectFusionTracking::mergeTrackedObjectByPoints(const ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2, bool& removeTarget){
	// 检查目标2的聚类点是否大部分在目标1的矩形框内
	common_msgs::point3d cornerPoint0, cornerPoint1, cornerPoint2, cornerPoint3;

	Eigen::Vector3d inputUTMPosition{trackedObject1.m_trackingBoxResult[0] - m_selfCarUTMPosition[0],
										 trackedObject1.m_trackingBoxResult[1] - m_selfCarUTMPosition[1],
		                                  trackedObject1.m_objectOriginal.z};
	std::vector<double> objectPositionInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(inputUTMPosition);

	float headingClockwise = 2.0 * M_PI -  trackedObject1.m_trackingBoxResult[5];
	vector<double> boxInfo = {objectPositionInCarBackFRU[0], objectPositionInCarBackFRU[1], objectPositionInCarBackFRU[2],
				trackedObject1.m_trackingBoxResult[2], trackedObject1.m_trackingBoxResult[3], trackedObject1.m_trackingBoxResult[4],
			           headingClockwise};
	vector<vector<double>> pointsVector = m_common.boxes_to_corners_3d(boxInfo);

	cornerPoint0.x = pointsVector[0][0]; cornerPoint0.y = pointsVector[0][1]; cornerPoint0.z = pointsVector[0][2];
	cornerPoint1.x = pointsVector[1][0]; cornerPoint1.y = pointsVector[1][1]; cornerPoint1.z = pointsVector[1][2];
	cornerPoint2.x = pointsVector[2][0]; cornerPoint2.y = pointsVector[2][1]; cornerPoint2.z = pointsVector[2][2];
	cornerPoint3.x = pointsVector[3][0]; cornerPoint3.y = pointsVector[3][1]; cornerPoint3.z = pointsVector[3][2];

	// int pointsNumberInBox = 0;
	// for(const common_msgs::point3d& point : trackedObject2.m_objectOriginal.points){
	// 	bool isPointInBox = ((getCross(cornerPoint0, cornerPoint1,point) * getCross(cornerPoint2, cornerPoint3,point)) >= 0) &&
	// 					((getCross(cornerPoint1, cornerPoint2,point) * getCross(cornerPoint3, cornerPoint0,point)) >= 0);
	// 	if(isPointInBox){
	// 		++pointsNumberInBox;
	// 	}
	// }
	// float pointsRatio = pointsNumberInBox / (float)(trackedObject2.m_objectOriginal.points.size() + 1e-5);


	auto pointsNumberInBox = std::count_if(trackedObject2.m_objectOriginal.points.begin(), trackedObject2.m_objectOriginal.points.end(),
		[&](const common_msgs::point3d& point){
			return IsPointInBox(cornerPoint0, cornerPoint1, cornerPoint2, cornerPoint3, point);
		});


	float pointsRatio = static_cast<double>(pointsNumberInBox) / (float)(trackedObject2.m_objectOriginal.points.size() + 1e-5);


	if (pointsNumberInBox > 10 || pointsRatio > 0.3) {
		// cout<<FGRN("merge tracked objects: ") << "reserve id = " << trackedObject1.m_id
		// 			<< ", 点数大于10 " << endl;
		removeTarget = true;
	}
	else{
		// cout<<FGRN("merge tracked objects: ") << "reserve id = " << trackedObject1.m_id
		// 			<< ", 点数 = " << pointsNumberInBox << ", all size = " << trackedObject2.m_objectOriginal.points.size()
		// 			<< ", ratio = " << pointsRatio << endl;
		removeTarget = false;
	}
}

void ObjectFusionTracking::mergeTrackedObjectByIOU(const ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2, bool& removeTarget){
	vector<float> tracked1ObjectInfo = {trackedObject1.m_trackingBoxResult[0], trackedObject1.m_trackingBoxResult[1], 1,
										   trackedObject1.m_trackingBoxResult[2], trackedObject1.m_trackingBoxResult[3],1,
										    trackedObject1.m_trackingBoxResult[5]};
	vector<float> tracked2ObjectInfo = {trackedObject2.m_trackingBoxResult[0], trackedObject2.m_trackingBoxResult[1], 1,
										   trackedObject2.m_trackingBoxResult[2], trackedObject2.m_trackingBoxResult[3],1,
										    trackedObject2.m_trackingBoxResult[5]};
	m_computeAssignmentMatrix.setPredictedObject(tracked1ObjectInfo);
	m_computeAssignmentMatrix.setDetectedObject(tracked2ObjectInfo);
	float iou = 1 - m_computeAssignmentMatrix.getBoxIOUDistance();

	if(iou > 1){//TODO 设置阈值为1暂时不用
		m_pLogger->info("merge tracked objects: reserve id = {}, iou 大于0.1, iou = {}", trackedObject1.m_id, iou);
		removeTarget = true;
	}
	else{
		// cout<<FGRN("merge tracked objects: ") << "reserve id = " << trackedObject1.m_id
		// 			<< ", iou = " << iou << endl;
		removeTarget = false;
	}
}

void ObjectFusionTracking::updateClassification(const int& trackerIndex, const int& detectionIndex, const int& updateCount){
	ObjectTracking& trackingObject = m_trackingObjectsVector[trackerIndex];
	common_msgs::sensorobject& detectionObject = m_lidarObjects.obs[detectionIndex];

	if(detectionObject.classification == COMMON::LidarDetectionClassification::Zombiecar){
		trackingObject.m_classificationCount[8].detectedTimes++;
		trackingObject.m_classificationCount[8].m_lengths.emplace_back(detectionObject.length);
		trackingObject.m_classificationCount[8].m_widths.emplace_back(detectionObject.width);
		trackingObject.m_classificationCount[8].m_heights.emplace_back(detectionObject.height);
	}
	else{
		trackingObject.m_classificationCount[detectionObject.classification].detectedTimes++;
		trackingObject.m_classificationCount[detectionObject.classification].m_lengths.emplace_back(detectionObject.length);
		trackingObject.m_classificationCount[detectionObject.classification].m_widths.emplace_back(detectionObject.width);
		trackingObject.m_classificationCount[detectionObject.classification].m_heights.emplace_back(detectionObject.height);
	}

	auto maxCountPtr = std::max_element(trackingObject.m_classificationCount.begin(),trackingObject.m_classificationCount.end(),
		[](const s_ClassDetectedTimes& firstClassDetectedInfo, const s_ClassDetectedTimes& secondClassDetectedInfo){
			return firstClassDetectedInfo.detectedTimes < secondClassDetectedInfo.detectedTimes;
	});
	int maxCountClassification = std::distance(trackingObject.m_classificationCount.begin(), maxCountPtr);
	if(maxCountClassification == 8){
		maxCountClassification = COMMON::LidarDetectionClassification::Zombiecar;
	}

	if(trackingObject.m_id == m_pConfigManager->m_debugID){
		m_pLogger->debug("类别更新：更新前id = {}, m_hits = {}, 跟踪类别 = {}, 检测类别 = {}, 最大类别 = {}, 跟踪长 = {:.2f}, 跟踪宽 = {:.2f}",
			trackingObject.m_id ,trackingObject.m_hits,
			trackingObject.m_classification,
			(int)detectionObject.classification,
			maxCountClassification,trackingObject.m_trackingBoxResult[2], trackingObject.m_trackingBoxResult[3]);
	}

	// 僵尸车固定类别，其他可以改变类别
	// TODO 添加类别锁定状态
	if(!trackingObject.m_sStationObjectsInfo.m_isMatched){ //与OBU匹配上的类别不变
		// cout<<FGRN("非与OBU匹配目标：")<< trackingObject.m_sStationObjectsInfo.m_isMatched << endl;
		if(trackingObject.m_hits > 5){
			// 长宽取最多次数的类别中修剪平均值
			float trimmedMeanLength = m_common.getTrimmedMean(maxCountPtr->m_lengths, 0.1); // 剔除10%的异常值
			float trimmedMeanWidth = m_common.getTrimmedMean(maxCountPtr->m_widths, 0.1); // 剔除10%的异常值
			float trimmedMeanHeight = m_common.getTrimmedMean(maxCountPtr->m_heights, 0.1); // 剔除10%的异常值
			trackingObject.m_trackingBoxResult[2] = trimmedMeanLength;
			trackingObject.m_trackingBoxResult[3] = trimmedMeanWidth;
			trackingObject.m_trackingBoxResult[4] = trimmedMeanHeight;

			if(trackingObject.m_classification != maxCountClassification){
				if((trackingObject.m_classification == COMMON::LidarDetectionClassification::Bicycle || trackingObject.m_classification == COMMON::LidarDetectionClassification::Tricycle)
					&& maxCountClassification == COMMON::LidarDetectionClassification::Car){
					if(trackingObject.m_id == m_pConfigManager->m_debugID){
						std::string printStr = "类别更新：更新为最多类别id = {}, m_hits = {}, 跟踪类别 = {}, 检测类别 = {}, 最大类别 = {}, \
												跟踪长 = {:.2f}, 跟踪宽 = {:.2f},类别转换类型 = {}, 最大类别次数 = {}";
						m_pLogger->debug(printStr.c_str(),
							trackingObject.m_id ,trackingObject.m_hits,
							trackingObject.m_classification,
							(int)detectionObject.classification,
							maxCountClassification,trackingObject.m_trackingBoxResult[2],
							trackingObject.m_trackingBoxResult[3],trackingObject.m_classSwitchType,
							maxCountPtr->detectedTimes);
					}
					trackingObject.m_classSwitchType = ClassSwitchType::Bicycle2Car; // 设置类别转换类型
					trackingObject.m_classification = maxCountClassification;

				}
				else if(trackingObject.m_classification == COMMON::LidarDetectionClassification::Cone
					&& maxCountClassification == COMMON::LidarDetectionClassification::Car){ // && maxCountPtr->detectedTimes > 4
						if(trackingObject.m_id == m_pConfigManager->m_debugID){
							std::string printStr = "类别更新：更新为最多类别id = {}, m_hits = {}, 跟踪类别 = {}, 检测类别 = {}, 最大类别 = {}, \
													跟踪长 = {:.2f}, 跟踪宽 = {:.2f},类别转换类型 = {}, 最大类别次数 = {}";
							m_pLogger->debug(printStr.c_str(),
								trackingObject.m_id ,trackingObject.m_hits,
								trackingObject.m_classification,
								(int)detectionObject.classification,
								maxCountClassification,trackingObject.m_trackingBoxResult[2],
								trackingObject.m_trackingBoxResult[3],trackingObject.m_classSwitchType,
								maxCountPtr->detectedTimes);
						}
						trackingObject.m_classSwitchType = ClassSwitchType::Cone2Car; // 设置类别转换类型
						trackingObject.m_classification = maxCountClassification;
				}
				else{
					if(trackingObject.m_id == m_pConfigManager->m_debugID){
							std::string printStr = "类别更新：更新-----更新为最多类别id = {}, m_hits = {}, 跟踪类别 = {}, 检测类别 = {}, 最大类别 = {}, \
													跟踪长 = {:.2f}, 跟踪宽 = {:.2f},类别转换类型 = {}, 最大类别次数 = {}";
							m_pLogger->debug(printStr.c_str(),
								trackingObject.m_id ,trackingObject.m_hits,
								trackingObject.m_classification,
								(int)detectionObject.classification,
								maxCountClassification,trackingObject.m_trackingBoxResult[2],
								trackingObject.m_trackingBoxResult[3],trackingObject.m_classSwitchType,
								maxCountPtr->detectedTimes);
						}
					trackingObject.m_classification = maxCountClassification;
				}

			}
			else{
				trackingObject.m_classSwitchType = ClassSwitchType::ClassFixed;
				if(trackingObject.m_id == m_pConfigManager->m_debugID){
					m_pLogger->debug("类别更新：类别不变 id = {}, m_hits = {}, 跟踪类别 = {}, 检测类别 = {}, 最大类别 = {}, 跟踪长 = {:.2f}, 跟踪宽 = {:.2f}",
						trackingObject.m_id ,trackingObject.m_hits,
						trackingObject.m_classification,
						(int)detectionObject.classification,
						maxCountClassification,trackingObject.m_trackingBoxResult[2], trackingObject.m_trackingBoxResult[3]);
				}
			}
		}
		else{
			// 1.跟踪、检测都是聚类，不更新；2.跟踪是聚类，检测不是，使用检测类别；3.跟踪不是聚类，检测是聚类，不更新类别；4.跟踪不是聚类，检测不是聚类，(前几帧)不更新类别；
			if(trackingObject.m_classification == COMMON::LidarDetectionClassification::Unknown &&
				detectionObject.classification != COMMON::LidarDetectionClassification::Unknown){
				if(trackingObject.m_id == m_pConfigManager->m_debugID){
					m_pLogger->debug("类别更新：更新次数少，跟踪为聚类，检测部位聚类，更新为检测类别,检测类别 = {}",(int)detectionObject.classification);
				}
				trackingObject.m_classification = detectionObject.classification;
			}
		}

	}
	else{
		// 长宽取最多次数的类别中修剪平均值
		float trimmedMeanLength = m_common.getTrimmedMean(maxCountPtr->m_lengths, 0.1); // 剔除10%的异常值
		float trimmedMeanWidth = m_common.getTrimmedMean(maxCountPtr->m_widths, 0.1); // 剔除10%的异常值
		float trimmedMeanHeight = m_common.getTrimmedMean(maxCountPtr->m_heights, 0.1); // 剔除10%的异常值
		trackingObject.m_trackingBoxResult[2] = trimmedMeanLength;
		trackingObject.m_trackingBoxResult[3] = trimmedMeanWidth;
		trackingObject.m_trackingBoxResult[4] = trimmedMeanHeight;
		if(trackingObject.m_id == m_pConfigManager->m_debugID){
			m_pLogger->debug("classification修正-与基站更新：更新顺序： updateCount = {}, 更新 id = {},, 最大类别 = {}, 跟踪类别 = {}, 检测类别 = {}",
							updateCount, trackingObject.m_id, maxCountClassification, trackingObject.m_classification,
							static_cast<int>(detectionObject.classification));
		}
		// cout<<FGRN("classification修正-与基站更新：更新顺序：")<< updateCount <<", 更新 id = "<< trackingObject.m_id
		// 		<<", 最大类别 = " <<maxCountClassification
		// 		<<", 跟踪类别 = "<<trackingObject.m_classification
		// 		<<", 检测类别 = "<<static_cast<int>(detectionObject.classification)<< endl;
	}

	// 自行车固定：跟踪是行人或者聚类，检测是自行车，设定是自行车
	// if((trackingObject.m_classification == COMMON::LidarDetectionClassification::Pedestrian
	// 	|| trackingObject.m_classification == COMMON::LidarDetectionClassification::Unknown)
	// 	&& detectionObject.classification == COMMON::LidarDetectionClassification::Bicycle){

	// 		m_pLogger->info("classification修正：更新为自行车: updateCount = {}, 更新 id = {}, , 最大类别 = {}, 跟踪类别 = {}, 检测类别= {}, 跟踪航向角={:.2f},更新为检测航向角 ={:.2f}",
	// 						updateCount,trackingObject.m_id,maxCountClassification,
	// 						trackingObject.m_classification,
	// 						static_cast<int>(detectionObject.classification),
	// 						trackingObject.m_trackingBoxResult[5] * 180 / M_PI,
	// 						detectionObject.azimuth * 180 / M_PI);

	// 		trackingObject.m_classification = detectionObject.classification;
	// 		trackingObject.m_trackingBoxResult[2] = detectionObject.length;
	// 		trackingObject.m_trackingBoxResult[3] = detectionObject.width;
	// 		trackingObject.m_trackingBoxResult[4] = detectionObject.height;

	// 		// float objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformer.CarBackRFUHeading2NorthClockwise(detectionObject.azimuth * 180 / M_PI, m_gps.heading);
	// 		// trackingObject.m_trackingBoxResult[5] = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
	// 		trackingObject.m_classificationCount[COMMON::LidarDetectionClassification::Bicycle].detectedTimes++;
	// }
	// else if(trackingObject.m_classification == COMMON::LidarDetectionClassification::Bicycle
	// 	&& detectionObject.classification == COMMON::LidarDetectionClassification::Pedestrian){
	// 		m_pLogger->info("classification修正：自行车不更新: updateCount = {}, 更新 id = {}, , 最大类别 = {}, 跟踪类别 = {}, 检测类别= {}, 跟踪航向角={:.2f},更新为检测航向角 ={:.2f}",
	// 						updateCount,trackingObject.m_id,maxCountClassification,
	// 						trackingObject.m_classification,
	// 						static_cast<int>(detectionObject.classification));

	// 		// 跟踪是自行车，检测是行人，设定是自行车
	// 		trackingObject.m_classification = COMMON::LidarDetectionClassification::Bicycle;
	// 		trackingObject.m_classificationCount[COMMON::LidarDetectionClassification::Bicycle].detectedTimes++;
	// 		detectionObject.length = trackingObject.m_trackingBoxResult[2];
	// 		detectionObject.width = trackingObject.m_trackingBoxResult[3];
	// 		detectionObject.height = trackingObject.m_trackingBoxResult[4];
	// }

	// if(trackingObject.m_id == m_pConfigManager->m_debugID){
	// 	cout<<UNDL("类别更新分析：") <<", id = "<< trackingObject.m_id
	// 	<<", m_hits = "<< trackingObject.m_hits
	// 	<<", 最大类别 = " <<maxCountClassification
	// 	<<", 跟踪类别 = "<<trackingObject.m_classification
	// 	<<", 跟踪length = "<<trackingObject.m_trackingBoxResult[2]
	// 	<<", 跟踪width = " <<trackingObject.m_trackingBoxResult[3]
	// 	<<", 检测length = "<<detectionObject.length
	// 	<<", 检测width = " <<detectionObject.width
	// 	<< endl;
	// }
}

void ObjectFusionTracking::update8Corners(){
	vector<double> boxInfo(7, 0.0);
	vector<vector<double>> pointsVector(8, vector<double>(3, 0.0));
	Eigen::Vector3d selfCarEulerDegrees{m_gps.pitch, m_gps.roll, m_gps.heading};
	Eigen::Vector3d selfCarUTMPosition{m_selfCarUTMPosition[0], m_selfCarUTMPosition[1],  m_selfCarUTMPosition[2]};
	for (auto &objectTracking: m_trackingObjectsVector) {
		// 聚类目标不更新8角点
		if(objectTracking.m_classification == COMMON::LidarDetectionClassification::Unknown)
			continue;
		if(objectTracking.m_time_since_update == 0){
			float headingClockwise = 2.0 * M_PI - objectTracking.m_trackingBoxResult[5]; // headingAnticlockwise = 2 * M_PI - objectTracking.m_trackingBoxResult[5]显示 -- 跟画框有关:画框需要逆时针，保存CSV需要顺时针，发送需要顺时针
			boxInfo = {objectTracking.m_trackingBoxResult[0], objectTracking.m_trackingBoxResult[1],
			           objectTracking.m_objectOriginal.z,
			           objectTracking.m_trackingBoxResult[2], objectTracking.m_trackingBoxResult[3],
			           objectTracking.m_trackingBoxResult[4],
			           headingClockwise};
		}
		else{
			float headingClockwise = 2.0 * M_PI - objectTracking.m_curPredictedInfo[5]; // headingAnticlockwise = 2 * M_PI - objectTracking.m_trackingBoxResult[5]显示 -- 跟画框有关:画框需要逆时针，保存CSV需要顺时针，发送需要顺时针
			boxInfo = {objectTracking.m_curPredictedInfo[0], objectTracking.m_curPredictedInfo[1],
			           objectTracking.m_objectOriginal.z,
			           objectTracking.m_curPredictedInfo[2], objectTracking.m_curPredictedInfo[3],
			           objectTracking.m_curPredictedInfo[4],
			           headingClockwise};
		}

		pointsVector = m_common.boxes_to_corners_3d(boxInfo); // UTM 坐标

		std::vector<common_msgs::point3d> cornerPoints = objectTracking.m_objectOriginal.points;//carbackRFU坐标
		int cornerPointsSize = cornerPoints.size();
		std::vector<common_msgs::point3d> polygonPoints;
		// 提取多边形点
		for (int i = 8; i < cornerPointsSize; ++i) {
			polygonPoints.push_back(cornerPoints[i]);//存入carbackRFU坐标
		}
		objectTracking.m_objectOriginal.points.clear();
		// 更新后八角点转到ENU坐标存入原始点
		for (const auto &pointVector: pointsVector) {
			Eigen::Vector3d inputUTMPosition{pointVector[0], pointVector[1], pointVector[2]};
			Eigen::Vector3d objectInCarBackFRU;
			m_sensorAxisTransformer.utm2CarBackRFU(inputUTMPosition,selfCarEulerDegrees,
											selfCarUTMPosition, objectInCarBackFRU);
			common_msgs::point3d cornerPoint;
			cornerPoint.x = objectInCarBackFRU[0];
			cornerPoint.y = objectInCarBackFRU[1];
			cornerPoint.z = objectInCarBackFRU[2];
			objectTracking.m_objectOriginal.points.emplace_back(cornerPoint);
		}
		// 八角点后存入多边形点
		objectTracking.m_objectOriginal.points.insert(objectTracking.m_objectOriginal.points.end(),polygonPoints.begin(),polygonPoints.end());
	}
}


void ObjectFusionTracking::removeInvalidObjects() {
	// Eigen::Vector3d selfCarEulerXYZDegree{0, 0, m_gps.heading};
	// Eigen::Vector3d selfCarUTMPosition{m_selfCarUTMPosition[0], m_selfCarUTMPosition[1], m_selfCarUTMPosition[2]};

	// for(auto& it : m_trackingObjectsVector){
	// 	float speed = sqrt(it.m_absVelocityUTM[0] * it.m_absVelocityUTM[0] + it.m_absVelocityUTM[1] * it.m_absVelocityUTM[1]);
	// 	cout<<"id = "<<it.m_id<<", age = " << it.m_age <<", hits = "<<it.m_hits<<", max_age = "<<it.m_max_age<<", m_hit_streak = "<<it.m_hit_streak<<endl;
	// 	if(m_common.isMotorVehicle(it.m_classification)){
	// 		if(it.m_hits > 5 && speed > 2){
	// 			it.m_max_age = 8;
	// 		}
	// 		else{
	// 			it.m_max_age = 6;
	// 		}
	// 	}
	// 	else{
	// 		if(it.m_hit_streak > 8 && speed > 2){
	// 			it.m_max_age = 8;
	// 		}
	// 		else{
	// 			it.m_max_age = 6;
	// 		}
	// 	}
	// 	cout<<"AFTER id = "<<it.m_id<<", age = " << it.m_age <<", hits = "<<it.m_hits<<", max_age = "<<it.m_max_age<<", m_hit_streak = "<<it.m_hit_streak<<endl;
	// }

	m_trackingObjectsVector.erase(
			std::remove_if(
					m_trackingObjectsVector.begin(),
					m_trackingObjectsVector.end(),
					[&](const ObjectTracking& object) {
						bool isRemove = object.m_time_since_update > object.m_max_age;
						if(isRemove && m_pConfigManager->m_isUseRosBag){
							m_pLogger->info("remove object id = {}", object.m_id);
						}
						return isRemove;
					}
			),
			m_trackingObjectsVector.end()
	);
}

void ObjectFusionTracking::saveObjectTrackedInfo(){
	std::string savePathName = m_folderPath + "/" + std::to_string(m_curFrameStamp / 1000.0) + ".csv";
	fstream saveFile(savePathName, ios::out | ios::app);
	for (auto &curFrameTracker: m_trackingObjectsVector) {
		//id,timestamp,x,y,w,l,h,角度-H,vx,vy, absvx,absvy,raw_vx,raw_vy,raw_absvx,raw_absvy,速度来源,radarIndex,radarObjectID
		// 自车横向速度, 自车纵向速度, 轨迹速度(now 0), 转向角速度
		if(m_pConfigManager->m_isEvaluateTracking){// 跟踪结果评估
			// id 类型 X-cm Y-cm Z-cm 速度-cm/s 运动方向-RFU-Y正顺360 长-cm 宽-cm 高-cm 是否遮挡 遮挡程度 置信度 信息来源 异常属性
			double angleRad = m_common.normalizeAngle(curFrameTracker.m_trackingBoxResult[5]);
			curFrameTracker.m_trackingBoxResult[5] = angleRad;

			//跟画框有关:画框需要逆时针，保存CSV需要顺时针，发送需要顺时针：
			// 自车(RFU-Y正顺0-360)路口左弯，左侧等待直行车辆的航向角顺时针增大，画框的点应该逆时针增大才能对应
			int labelClass = transformDetectionClass2LabelClass(curFrameTracker.m_classification);
			saveFile << setprecision(12) << curFrameTracker.m_id << "," << curFrameTracker.m_classification << ","
						<< curFrameTracker.m_trackingBoxResult[0] * 100 << "," << curFrameTracker.m_trackingBoxResult[1] * 100 << ","
						<< curFrameTracker.m_objectOriginal.z * 100 << ","
						<< sqrt(pow(curFrameTracker.m_trackingBoxResult[6] + m_selfCarSpeed[0], 2) + pow(curFrameTracker.m_trackingBoxResult[7] + m_selfCarSpeed[1], 2)) << ","
						<< curFrameTracker.m_trackingBoxResult[5] * 180 / M_PI << "," // 正北顺时针
						<< curFrameTracker.m_trackingBoxResult[2] * 100 << "," << curFrameTracker.m_trackingBoxResult[3] * 100 << ","
						<< curFrameTracker.m_trackingBoxResult[4] * 100 << ","
						<< "0" << "," << "0" << ","
						<< curFrameTracker.m_confidence << ", " << curFrameTracker.m_value << ","
						<< "0"
						<< endl;
		}
		else{ //用于调试优化
			saveFile << setprecision(12) << curFrameTracker.m_id << "," << std::to_string(m_curFrameStamp / 1000.0) << ","
						<< std::fixed << setprecision(2)
						<< curFrameTracker.m_trackingBoxResult[0] << "," << curFrameTracker.m_trackingBoxResult[1] << ","
						<< curFrameTracker.m_trackingBoxResult[2] << "," << curFrameTracker.m_trackingBoxResult[3] << ","
						<< curFrameTracker.m_trackingBoxResult[4] << "," << curFrameTracker.m_trackingBoxResult[5] * 180 / M_PI << "," // 顺时针
						<< curFrameTracker.m_trackingBoxResult[6] << "," << curFrameTracker.m_trackingBoxResult[7] << ","
						<< curFrameTracker.m_trackingBoxResult[6] + m_selfCarSpeed[0] << "," << curFrameTracker.m_trackingBoxResult[7] + m_selfCarSpeed[1] << ","
						<< curFrameTracker.m_objectOriginal.relspeedx << ", " << curFrameTracker.m_objectOriginal.relspeedy << ","
						<< curFrameTracker.m_objectOriginal.relspeedx + m_selfCarSpeed[0] << ", " << curFrameTracker.m_objectOriginal.relspeedy + m_selfCarSpeed[1] << ","
						<< curFrameTracker.m_value << "," << curFrameTracker.m_radarIndex << "," << curFrameTracker.m_radarObjectID << ","
						<< m_selfCarSpeed[0] << "," << m_selfCarSpeed[1] << ","
						<< "0," << m_gps.yawrate
						<< endl;
		}
	}
	saveFile.close();
}

int ObjectFusionTracking::transformDetectionClass2LabelClass(const int& objectClass){
	switch (objectClass){
		case 0: return COMMON::LidarDetectionClassification::Pedestrian; break;
		case 1: return COMMON::LidarDetectionClassification::Unknown; break;
		case 2: return COMMON::LidarDetectionClassification::Bicycle; break;
		case 3: return COMMON::LidarDetectionClassification::Tricycle; break;
		case 4: return COMMON::LidarDetectionClassification::Car; break;
		case 5: return COMMON::LidarDetectionClassification::Bus; break;
		case 6: return COMMON::LidarDetectionClassification::Truck; break;
		case 7: return COMMON::LidarDetectionClassification::Cone; break;
		default: return COMMON::LidarDetectionClassification::Unknown; break;
	}
}


void ObjectFusionTracking::publishTrackObject(){
	m_trackedObjects.obs.clear();
	m_trackedObjects.timestamp = m_lidarObjects.timestamp;
	m_trackedObjects.gpstime = m_lidarObjects.gpstime;
	//    m_trackedObjects.isvalid = msg_objs.isvalid;          //设置有效位，目前默认为1，都有效
	m_trackedObjects.isvalid = 1;

	// 发给rviz
	m_trackedObjects8CornerForRVIZ.isvalid = 1;
	m_trackedObjects8CornerForRVIZ.timestamp = m_lidarObjects.timestamp;
	m_pLogger->info("publishTrackObject timestamp = {:.3f}",m_lidarObjects.timestamp * 0.001);
	m_trackedObjects8CornerForRVIZ.gpstime = m_lidarObjects.gpstime;
	m_trackedObjects8CornerForRVIZ.obs.clear();
	int trackingObjectsVectorSize = m_trackingObjectsVector.size();
	std::vector<double> objectSpeedInCarBackFRU(3);
	std::vector<double>  objectPositionInCarBackFRU(3);
	vector<double> boxInfo(7);
	vector<vector<double>> pointsVector(8, vector<double>(3));

	// 聚类目标点云
	pcl::PointCloud<pcl::PointXYZI> clusterPointcloud;

	for(int i = 0; i < trackingObjectsVectorSize; i++)	{
		const ObjectTracking& objectTracking = m_trackingObjectsVector[i];
		common_msgs::sensorobject obj;
		obj.id = static_cast<uint32_t>(objectTracking.m_id);

		//没有初始化，即第一帧，显示全部，初始化后跟踪次数小于4帧，不发送不显示

		if(m_common.isNonMotorVehicle(objectTracking.m_classification) && objectTracking.m_hits < 2){
			if(objectTracking.m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("[publish nonMotorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
								objectTracking.m_id,
								objectTracking.m_classification,
								objectTracking.m_hit_streak,
								objectTracking.m_hits,
								objectTracking.m_age);
			continue;
		}
		else if(m_common.isMotorVehicle(objectTracking.m_classification) && objectTracking.m_hits < 5
			&& objectTracking.m_classification != COMMON::LidarDetectionClassification::Unknown){
			if(objectTracking.m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("[publish jump motorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
								objectTracking.m_id,
								objectTracking.m_classification,
								objectTracking.m_hit_streak,
								objectTracking.m_hits,
								objectTracking.m_age);
			continue;
		}
		else if(m_common.isClusterObject(objectTracking.m_classification) && objectTracking.m_age < 10){
			if(objectTracking.m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("[publish jump unknown] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
								objectTracking.m_id,
								objectTracking.m_classification,
								objectTracking.m_hit_streak,
								objectTracking.m_hits,
								objectTracking.m_age);
			continue;
		}

		Eigen::Vector3d inputUTMPosition{objectTracking.m_trackingBoxResult[0] - m_selfCarUTMPosition[0],
										 objectTracking.m_trackingBoxResult[1] - m_selfCarUTMPosition[1],
		                                  objectTracking.m_objectOriginal.z};
		objectPositionInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(inputUTMPosition);

		obj.x = objectPositionInCarBackFRU[0];
		obj.y = objectPositionInCarBackFRU[1];
		obj.z = objectPositionInCarBackFRU[2];
		WGS84Corr lla;//填充经纬度
		m_wgs84Utm.UTMXYToLatLon(objectTracking.m_trackingBoxResult[0], objectTracking.m_trackingBoxResult[1],
								 m_pConfigManager->m_cityUTMCode, false, lla);

		//计算8角点
		float objectAngleDegreeInCarBackRFU_Clockwise = m_sensorAxisTransformer.NorthClockwise2CarBackRFU(objectTracking.m_trackingBoxResult[5] * 180 / M_PI, m_gps.heading);
		float objectAngleRadInCarBackRFU_Clockwise = objectAngleDegreeInCarBackRFU_Clockwise * M_PI / 180.0;

		float headingClockwise = 2.0 * M_PI - objectAngleRadInCarBackRFU_Clockwise; // 显示 -- 跟画框有关:画框需要逆时针，保存CSV需要顺时针，发送需要顺时针
		boxInfo = {obj.x, obj.y, obj.z,
				  objectTracking.m_trackingBoxResult[2], objectTracking.m_trackingBoxResult[3],
		           objectTracking.m_trackingBoxResult[4],
		           headingClockwise};
		pointsVector = m_common.boxes_to_corners_3d(boxInfo);
		obj.points.clear();
		for(const auto& pointVector:pointsVector){
			common_msgs::point3d  cornerPoint;
			cornerPoint.x = pointVector[0];
			cornerPoint.y = pointVector[1];
			cornerPoint.z = pointVector[2];
			obj.points.emplace_back(std::move(cornerPoint));
		}

		obj.longtitude = lla.log / M_PI * 180;
		obj.latitude = lla.lat / M_PI * 180;


		Eigen::Vector3d UTMRelativeSpeed{objectTracking.m_trackingBoxResult[6] - m_gps.speedE,
									  objectTracking.m_trackingBoxResult[7] - m_gps.speedN,
		                                0};

		objectSpeedInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(UTMRelativeSpeed);

		obj.relspeedx = objectSpeedInCarBackFRU[0];//UTM速度转到ENU速度
		obj.relspeedy = objectSpeedInCarBackFRU[1];

		obj.length = objectTracking.m_trackingBoxResult[2];
		obj.width = objectTracking.m_trackingBoxResult[3];
		obj.height = objectTracking.m_trackingBoxResult[4];
		obj.azimuth = objectAngleRadInCarBackRFU_Clockwise;
		obj.classification = objectTracking.m_classification;

		bool l_isInStationRange = isInStationRange(obj.latitude, obj.longtitude, 150);
		if(!l_isInStationRange){
			switch (objectTracking.m_value)
			{
				case COMMON::SensorType::LIDAR_OBU:
					obj.value = COMMON::SensorType::LIDAR;
					break;
				case COMMON::SensorType::LIDAR_RADAR_OBU:
					obj.value = COMMON::SensorType::LIDAR_RADAR;
					break;
				default:
					obj.value = COMMON::SensorType::LIDAR;
					break;
			}
		}
		else{
			// 在基站范围内：
			obj.value = objectTracking.m_value;//融合情况 value存融合的情况
		}

		obj.confidence = objectTracking.m_confidence; //置信度

		//TODO
		// {
		// common_msgs::objecthistory objecthistory;
		// obj.objectHistory = objectTracking.m_historyTrajectoryInfo.predictedTrajectory;
		// obj.objectPrediction = objectTracking.m_historyTrajectoryInfo.historyTrajectory;
		// }

		obj.drivingIntent = objectTracking.m_drivingIntent;
		obj.radarIndex = objectTracking.m_radarIndex;
		obj.radarObjectID = objectTracking.m_radarObjectID;

		if(objectTracking.m_id == m_pConfigManager->m_debugID){
			Eigen::Vector3d speedInUTM{objectTracking.m_trackingBoxResult[6], objectTracking.m_trackingBoxResult[7], 0};
			std::vector<double> objectSpeedInCarBackRFU = objectTracking.getSpeedInCarbackRFU(speedInUTM);
			m_pLogger->info("跟踪信息： id = {}, m_value = {}, UTMx = {:.2f}, UTMy= {:.2f}, m_trackingBoxResult[6]={:.2f},m_trackingBoxResult[7] ={:.2f}, objectSpeedInCarBackRFU vx = {:.2f}, , objectSpeedInCarBackRFU vy = {:.2f}",
							objectTracking.m_id,static_cast<int>(objectTracking.m_value) ,objectTracking.m_trackingBoxResult[0],
							objectTracking.m_trackingBoxResult[1],objectTracking.m_trackingBoxResult[6],objectTracking.m_trackingBoxResult[7],
							objectSpeedInCarBackRFU[0], objectSpeedInCarBackRFU[1]);
			m_pLogger->info("obj.relspeedx = {:.2f}, obj.relspeedy= {:.2f}, m_selfCarSpeed[0]={:.2f},m_selfCarSpeed[1] ={:.2f}",
							obj.relspeedx,obj.relspeedy,m_selfCarSpeed[0],m_selfCarSpeed[1]);
			m_pLogger->debug("headingInNorth = {:.2f}", objectTracking.m_trackingBoxResult[5] * 180.0 / M_PI);
		}


		// 带着八角点，直接发给rviz
		m_trackedObjects8CornerForRVIZ.obs.push_back(obj);

		// 如果有多边形点，将多边形点加入到角点中，发给决策，如果没有就发送8角点
		std::vector<common_msgs::point3d> cornerPoints = objectTracking.m_objectOriginal.points;
		int cornerPointsSize = cornerPoints.size();

		bool isUseDetection8Corners = true;
		if(isUseDetection8Corners){
			obj.points.clear();
			for (int i = 0; i < cornerPointsSize; ++i) {
				obj.points.push_back(cornerPoints[i]);
			}
			m_trackedObjects.obs.push_back(obj);

		}
		else{
			// 跟踪目标不要检测的8角点
			if(objectTracking.m_classification == COMMON::LidarDetectionClassification::Unknown){
				// 聚类目标，计算8角点不要，只要聚类点
				obj.points.clear();
				for (int i = 0; i < cornerPointsSize; ++i) {
					obj.points.push_back(cornerPoints[i]);
				}
				m_trackedObjects.obs.push_back(obj);
			}
			else{
				if(cornerPointsSize > 8){
					obj.points.clear();
					for (int i = 8; i < cornerPointsSize; ++i) {
						obj.points.push_back(cornerPoints[i]);
					}
					m_trackedObjects.obs.push_back(obj);
				}
			}
		}

		// 聚类目标点云
		if(m_common.isClusterObject(objectTracking.m_classification)){
			for(size_t i = 0; i < objectTracking.m_objectOriginal.points.size(); ++i){
				common_msgs::point3d point = objectTracking.m_objectOriginal.points[i];
				pcl::PointXYZI clusterPoint;
				clusterPoint.x = point.x;
				clusterPoint.y = point.y;
				clusterPoint.z = point.z;
				clusterPoint.intensity = (float)(i % 255);
				clusterPointcloud.points.push_back(clusterPoint);
			}
		}
	}

	sensor_msgs::PointCloud2 clusterObjectsPointcloudMsg;
	pcl::toROSMsg(clusterPointcloud, clusterObjectsPointcloudMsg);
	clusterObjectsPointcloudMsg.header.frame_id = "car";

	// 加入虚拟目标
	for(const auto& cloudobject : m_cloudObjects.obs) {
		m_trackedObjects.obs.push_back(cloudobject);
		m_trackedObjects8CornerForRVIZ.obs.push_back(cloudobject);
	}
	pub_track_results.publish(m_trackedObjects);  //发布结果 "track_results",使用多边形点进行规划
	pub_track_results8CornerForRVIZ.publish(m_trackedObjects8CornerForRVIZ);  //发布结果 "track_results8CornerForRVIZ",使用8角点RVIZ显示

	pub_track_clusterPointcloud.publish(clusterObjectsPointcloudMsg);
	double curRosTimestamp = ros::Time::now().toSec();
	float timeGap = curRosTimestamp * 1000.0 - m_trackedObjects.timestamp;
	m_pLogger->info("publish current ros time = {:.3f}, object time : = {:.3f}, 时间差 = {:.3f}, 跟踪目标数量 = {}",
					curRosTimestamp, m_trackedObjects.timestamp / 1000.0, timeGap, m_trackingObjectsVector.size());
	m_pLogger->info("finished object published");
}


common_msgs::sensorobjects ObjectFusionTracking::getTrackedObjects(){
	return m_trackedObjects8CornerForRVIZ;
}

int ObjectFusionTracking::getTrackedObjectsSize(){
	return m_trackingObjectsVector.size();
}

bool ObjectFusionTracking::isMotorVehicle(const int& classification){
    switch (classification) {
        case COMMON::LidarDetectionClassification::Car:
        case COMMON::LidarDetectionClassification::Bus:
        case COMMON::LidarDetectionClassification::Truck:
        case COMMON::LidarDetectionClassification::Zombiecar:
            return true;
        default:
            return false;

    }
}


void ObjectFusionTracking::runV2IProcess(){
	m_pLogger->info("runV2IProcess");
	m_pStationObjects->m_callbackQueue.callAvailable();
	m_pSensorGPS->m_callbackQueue.callAvailable();
	common_msgs::sensorobjects l_curTrackingObjects = getTrackedObjects();
	// 保存同步的数据
	// {
	// 	m_folderPath = "/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/trkobjects/test";
	// 	static int lidarFrameCount = 0;
	// 	++lidarFrameCount;
	// 	// 保存车端数据:只保存发布的目标，不保存不稳定的目标
	// 	std::string savePathName = m_folderPath + "/" + std::to_string(lidarFrameCount) + "_carSide.csv";
	// 	fstream saveFile(savePathName, ios::out | ios::app);
	// 	m_pLogger->info("l_curTrackingObjects timestamp = {:.3f}",l_curTrackingObjects.timestamp * 0.001);
	// 	for(auto& curPubObject:l_curTrackingObjects.obs){
	// 		auto it = std::find_if(m_trackingObjectsVector.begin(), m_trackingObjectsVector.end(), [&curPubObject](const ObjectTracking& curTracker) {
	// 			return curTracker.m_id == curPubObject.id;
	// 		});
	// 		if(it != m_trackingObjectsVector.end()) {
	// 			float speedVx = it->m_trackingBoxResult[6] + m_selfCarSpeed[0];
	// 			float speedVy = it->m_trackingBoxResult[7] + m_selfCarSpeed[1];
	// 			float speed = sqrt(speedVx * speedVx + speedVy * speedVy);
	// 			// id	type	x_coordinate	y_coordinate	z_coordinate	speed	angle	length	width	height	confidence	source(车端为0)	time_stamp	longitude	latitude	frameid	send_timestamp
	// 			saveFile << setprecision(12) << curPubObject.id << "," << int(curPubObject.classification) << ","
	// 					<< curPubObject.x * 100 << "," << curPubObject.y * 100 << "," << curPubObject.z * 100 << ","
	// 					<< speed * 100 << "," << it->m_trackingBoxResult[5] * 180 / M_PI << ","
	// 					<< curPubObject.length * 100 << "," << curPubObject.width * 100 << "," << curPubObject.height * 100
	// 					<< "," << curPubObject.confidence << "," << 0 << "," << std::to_string(l_curTrackingObjects.timestamp) << ","
	// 					<< curPubObject.longtitude * 100000000 << "," << curPubObject.latitude * 100000000 << ","
	// 					<< lidarFrameCount << "," << std::to_string(l_curTrackingObjects.timestamp) << endl;

	// 		}
	// 	}
	// 	saveFile.close();
	// }

	m_pLogger->info("OBU deque size = {}", m_pStationObjects->m_SensorDataDeque.size());
	m_pStationObjects->timeSynchro(m_pStationObjects->m_SensorDataDeque, l_curTrackingObjects.timestamp, COMMON::SensorType::OBU);

	bool l_bGetCurrentData = m_pStationObjects->getCurrentData();
	// 未进入路端检测范围或者路端检测丢帧，发布车端检测目标
	// std::unique_lock<std::mutex> l_stationLock(m_pStationObjects->m_dataMutex);
	if(!l_bGetCurrentData){
		m_allCarObuObjectsSize = m_trackingObjectsVector.size(); // OBU为空，全部数量即跟踪数量

		// l_stationLock.unlock();
		long l_timestamp = m_lidarObjects.timestamp;
		// 发布v2i
		m_obuObjects.obs.clear(); //防止RVIZ暂留的旧数据
		m_pLogger->warn("station objects is empty, publish autodriving car perception information");
		l_curTrackingObjects.obs.clear();

		m_v2ifusionObjectsRVIZ.isvalid = 1;
		m_v2ifusionObjectsRVIZ.timestamp = l_curTrackingObjects.timestamp;
		m_v2ifusionObjectsRVIZ.gpstime = l_curTrackingObjects.gpstime;
		m_v2ifusionObjectsRVIZ.obs.clear();
		m_v2ifusionObjectsPAD.isvalid = 1;
		m_v2ifusionObjectsPAD.timestamp = l_curTrackingObjects.timestamp;
		m_v2ifusionObjectsPAD.gpstime = l_curTrackingObjects.gpstime;
		m_v2ifusionObjectsPAD.obs.clear();
		trackingObjects2SensorObjects(l_curTrackingObjects);
		m_v2ifusionObjects.obs.clear();
		m_v2ifusionObjects = l_curTrackingObjects;

		pub_v2iFusionObject.publish(m_v2ifusionObjects); //"objectfusion"
		pub_v2iFusionObjectRVIZ.publish(m_v2ifusionObjectsRVIZ);
		pub_v2iFusionObjectPAD.publish(m_v2ifusionObjectsPAD);
		double curRosTimestamp = ros::Time::now().toSec();
		float timeGap = curRosTimestamp * 1000.0 - l_curTrackingObjects.timestamp;
		m_pLogger->info("publish V2I-1 current ros time = {:.3f}, object time : = {:.3f}, 时间差 = {:.3f}, 跟踪目标数量 = {}",
						curRosTimestamp, l_curTrackingObjects.timestamp / 1000.0, timeGap, m_trackingObjectsVector.size());

		return;
	}

	long l_curOBUTimestamp;

	if(l_bGetCurrentData) {
		m_pStationObjects->m_curSensorData.timestamp += 100;//路端数据时间戳是一帧中第一包的时刻，车端数据时间戳是一帧中最后一包数据的时刻
		l_curOBUTimestamp = m_pStationObjects->m_curSensorData.timestamp;
		float l_v2iTimeGap = abs(m_trackedObjects.timestamp - l_curOBUTimestamp);

		if(l_v2iTimeGap > 300){
			m_pLogger->warn("station information is later than last lidar timestamp, publish autodriving car perception information");
			float timeGap = m_trackedObjects.timestamp - l_curOBUTimestamp;
			m_pLogger->warn("lidar-obu时间异常,时间差ms: {:.2f}",  timeGap);
			m_pLogger->warn("l_trackingObjectsTimestamp = {:.3f}", m_trackedObjects.timestamp / 1000.0);
			m_pLogger->warn("roadSide time = {:.3f}", l_curOBUTimestamp / 1000.0);
			m_pLogger->warn("m_pStationObjects->m_SensorDataDeque size = {}", m_pStationObjects->m_SensorDataDeque.size());

			if(!m_pStationObjects->m_SensorDataDeque.empty()){
				m_pStationObjects->m_SensorDataDeque.pop_front();
			}

			m_allCarObuObjectsSize = m_trackingObjectsVector.size(); // lidar-obu时间匹配超时，认为OBU为空，全部数量即跟踪数量

			// 发布v2i
			m_obuObjects.obs.clear();//防止RVIZ暂留的旧数据
			l_curTrackingObjects.obs.clear();
			m_v2ifusionObjectsRVIZ.isvalid = 1;
			m_v2ifusionObjectsRVIZ.timestamp = l_curTrackingObjects.timestamp;
			m_v2ifusionObjectsRVIZ.gpstime = l_curTrackingObjects.gpstime;
			m_v2ifusionObjectsRVIZ.obs.clear();
			m_v2ifusionObjectsPAD.isvalid = 1;
			m_v2ifusionObjectsPAD.timestamp = l_curTrackingObjects.timestamp;
			m_v2ifusionObjectsPAD.gpstime = l_curTrackingObjects.gpstime;
			m_v2ifusionObjectsPAD.obs.clear();

			trackingObjects2SensorObjects(l_curTrackingObjects);
			m_v2ifusionObjects.obs.clear();
			m_v2ifusionObjects = l_curTrackingObjects;

			pub_v2iFusionObject.publish(m_v2ifusionObjects); //"objectfusion"
			pub_v2iFusionObjectRVIZ.publish(m_v2ifusionObjectsRVIZ);
			pub_v2iFusionObjectPAD.publish(m_v2ifusionObjectsPAD);
			double curRosTimestamp = ros::Time::now().toSec();
			float timeGapObjects2ROS = curRosTimestamp * 1000.0 - l_curTrackingObjects.timestamp;
			m_pLogger->info("publish V2I-2 current ros time = {:.3f}, object time : = {:.3f}, 时间差 = {:.3f}, 跟踪目标数量 = {}",
							curRosTimestamp, l_curTrackingObjects.timestamp / 1000.0, timeGapObjects2ROS, m_trackingObjectsVector.size());
			return;
		}
		else{
			m_pLogger->info("currentROSTime: {:.2f}, l_trackingObjectsTimestamp = {:.3f}, roadSide time = {:.3f}",
							m_currentROSTime.toSec() ,m_trackedObjects.timestamp / 1000.0,l_curOBUTimestamp / 1000.0);
			// cout << FGRN("时间差ms: ") << m_trackedObjects.timestamp - l_curOBUTimestamp << endl;
			m_pLogger->info("时间差ms: {}",(m_trackedObjects.timestamp - l_curOBUTimestamp));
			float timeGap = m_trackedObjects.timestamp - l_curOBUTimestamp;
			m_pLogger->info("lidar-obu时间正常,时间差ms: {:.2f}",timeGap);
		}
	}

	// 路端结果为主

	m_pLogger->info("obu-gps time sync:");
	// std::lock_guard<std::mutex> l_gpsLock(m_pSensorGPS->m_dataMutex);
	SENSOROBJECTS::timeSynchro(m_pSensorGPS->m_SensorDataDeque, l_curOBUTimestamp, COMMON::SensorType::GPS);
	m_pSensorGPS->getCurrentData();


	m_pStationObjects->transOBUObject2CarBackRFU(m_obuObjects, m_pSensorGPS->m_curSensorData);
	m_pStationObjects->sensorobjectsBoxShow(m_obuObjects);


	if(l_curTrackingObjects.obs.empty()){
		m_pLogger->warn(" car perception objects is empty, publish station perception information");
		// 如果车端为空，使用路端，直接发布
		m_v2ifusionObjects.obs.clear();
		m_v2ifusionObjectsRVIZ.obs.clear();
		int eraseCount = 0;
		// 自车目标删除
		for(auto it = m_obuObjects.obs.begin(); it != m_obuObjects.obs.end();){
			if(abs(it->x) < 1.5 && it->y > -3.6 && it->y < 2
				&& (it->classification == COMMON::LidarDetectionClassification::Car || it->classification == COMMON::LidarDetectionClassification::Bus
					|| it->classification == COMMON::LidarDetectionClassification::Truck || it->classification == COMMON::LidarDetectionClassification::Zombiecar)){
				m_pLogger->info("erase OBU self car id = {}", it->id);
				m_obuObjects.obs.erase(it);
				++eraseCount;
				break;
			}
			else{
				++it;
			}
		}
		m_allCarObuObjectsSize = m_obuObjects.obs.size(); // 车端跟踪为空，全部数量即OBU目标数量

		m_v2ifusionObjects = m_obuObjects;
		m_v2ifusionObjectsRVIZ.isvalid = 1;
		m_v2ifusionObjectsRVIZ.timestamp = l_curTrackingObjects.timestamp;
		m_v2ifusionObjectsRVIZ.gpstime = l_curTrackingObjects.gpstime;
		m_v2ifusionObjectsRVIZ.obs.clear();
		m_v2ifusionObjectsRVIZ = m_obuObjects;
		m_v2ifusionObjectsPAD.isvalid = 1;
		m_v2ifusionObjectsPAD.timestamp = l_curTrackingObjects.timestamp;
		m_v2ifusionObjectsPAD.gpstime = l_curTrackingObjects.gpstime;
		m_v2ifusionObjectsPAD.obs.clear();
		m_v2ifusionObjectsPAD = m_obuObjects;

		pub_v2iFusionObject.publish(m_v2ifusionObjects); //"objectfusion"
		pub_v2iFusionObjectRVIZ.publish(m_v2ifusionObjectsRVIZ);
		pub_v2iFusionObjectPAD.publish(m_v2ifusionObjectsPAD);
		m_pLogger->info("l_curTrackingObjects.obs.empty return");
		double curRosTimestamp = ros::Time::now().toSec();
		float timeGap = curRosTimestamp * 1000.0 - l_curTrackingObjects.timestamp;
		m_pLogger->info("publish V2I-3 current ros time = {:.3f}, object time : = {:.3f}, 时间差 = {:.3f}",
						curRosTimestamp, l_curTrackingObjects.timestamp / 1000.0, timeGap);
		return;
	}

	// 融合使用当前的跟踪目标
	v2iObjectFusion(l_curTrackingObjects, m_obuObjects);
	// 保存同步的数据
	// {
	// 	m_folderPath = "/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/trkobjects/test";
	// 	static int lidarFrameCount = 0;
	// 	++lidarFrameCount;
	// 	// 保存车端数据:只保存发布的目标，不保存不稳定的目标
	// 	std::string savePathName = m_folderPath + "/" + std::to_string(lidarFrameCount) + "_carSide.csv";
	// 	fstream saveFile(savePathName, ios::out | ios::app);
	// 	m_pLogger->info("l_curTrackingObjects timestamp = {:.3f}",l_curTrackingObjects.timestamp * 0.001);
	// 	for(auto& curPubObject:l_curTrackingObjects.obs){
	// 		auto it = std::find_if(m_trackingObjectsVector.begin(), m_trackingObjectsVector.end(), [&curPubObject](const ObjectTracking& curTracker) {
	// 			return curTracker.m_id == curPubObject.id;
	// 		});
	// 		if(it != m_trackingObjectsVector.end()) {
	// 			float speedVx = it->m_trackingBoxResult[6] + m_selfCarSpeed[0];
	// 			float speedVy = it->m_trackingBoxResult[7] + m_selfCarSpeed[1];
	// 			float speed = sqrt(speedVx * speedVx + speedVy * speedVy);
	// 			// id	type	x_coordinate	y_coordinate	z_coordinate	speed	angle	length	width	height	confidence	source(车端为0)	time_stamp	longitude	latitude	frameid	send_timestamp
	// 			saveFile << setprecision(12) << curPubObject.id << "," << int(curPubObject.classification) << ","
	// 					<< curPubObject.x * 100 << "," << curPubObject.y * 100 << "," << curPubObject.z * 100 << ","
	// 					<< speed * 100 << "," << it->m_trackingBoxResult[5] * 180 / M_PI << ","
	// 					<< curPubObject.length * 100 << "," << curPubObject.width * 100 << "," << curPubObject.height * 100
	// 					<< "," << curPubObject.confidence << "," << 0 << "," << std::to_string(l_curTrackingObjects.timestamp) << ","
	// 					<< curPubObject.longtitude * 100000000 << "," << curPubObject.latitude * 100000000 << ","
	// 					<< lidarFrameCount << "," << std::to_string(l_curTrackingObjects.timestamp) << endl;

	// 		}
	// 	}
	// 	saveFile.close();
	// 	std::string savePathName2 = m_folderPath + "/" + std::to_string(lidarFrameCount) + "_roadSide.csv";
	// 	fstream saveFileRoad(savePathName2, ios::out | ios::app);
	// 	for(auto& curOBUObject:m_obuObjects.obs){
	// 		float headingDegree = curOBUObject.azimuth * 180.0 / M_PI + m_gps.heading;
	// 		if(headingDegree > 360){
	// 			headingDegree -= 360;
	// 		}
	// 		float speedVx = curOBUObject.relspeedx + m_selfCarSpeed[0];
	// 		float speedVy = curOBUObject.relspeedy + m_selfCarSpeed[1];
	// 		float speed = sqrt(speedVx * speedVx + speedVy * speedVy);
	// 		// id	type	x_coordinate	y_coordinate	z_coordinate	speed	angle	length	width	height	confidence	source(车端为0)	time_stamp	longitude	latitude	frameid	send_timestamp
	// 		saveFileRoad << setprecision(12) << curOBUObject.id << "," << int(curOBUObject.classification)
	// 				<< "," << curOBUObject.x * 100 << "," << curOBUObject.y * 100 << "," << curOBUObject.z * 100 << ","
	// 				<< speed * 100 << "," << headingDegree << ","
	// 				<< curOBUObject.length * 100 << "," << curOBUObject.width * 100 << "," << curOBUObject.height * 100
	// 				<< "," << curOBUObject.confidence << "," << 1 << "," << std::to_string(m_obuObjects.timestamp) << ","
	// 				<< curOBUObject.longtitude * 100000000 << "," << curOBUObject.latitude * 100000000 << ","
	// 				<< lidarFrameCount << "," << std::to_string(m_obuObjects.timestamp) << endl;
	// 	}
	// 	saveFileRoad.close();
	// }
}


void ObjectFusionTracking::v2iObjectFusion(const common_msgs::sensorobjects& curTrackingObjects, common_msgs::sensorobjects& obuobjects){
	unsigned int lidarObjectNum = m_trackingObjectsVector.size();
	unsigned int obuObjectNum = obuobjects.obs.size();
	std::vector<std::vector<double>> associateMatrix(lidarObjectNum, std::vector<double>(obuObjectNum, -1));
	int lidarOBUFusionMethod = 0;


	for(int i = 0; i < lidarObjectNum; ++i) {
		std::vector<float> lidarObjectBox{m_trackingObjectsVector[i].m_objectOriginal.x, m_trackingObjectsVector[i].m_objectOriginal.y, 1,
		                                  m_trackingObjectsVector[i].m_objectOriginal.length, m_trackingObjectsVector[i].m_objectOriginal.width, 1,
		                                  m_trackingObjectsVector[i].m_objectOriginal.azimuth};
		// cout << "id: " << m_trackingObjectsVector[i].m_id << ", x: "
		// 	<< m_trackingObjectsVector[i].m_objectOriginal.x << ", y: "
		// 	<< m_trackingObjectsVector[i].m_objectOriginal.y
		// 	<< endl;
		for(int j = 0; j < obuObjectNum; ++j){
			//没有预测轨迹使用当前的lidar扫描结果
			common_msgs::sensorobject obuObject = obuobjects.obs[j];
			std::vector<float> obuObjectBox = {obuObject.x, obuObject.y, 1,
							obuObject.length, obuObject.width,1, obuObject.azimuth};

			// cout << "id: " << obuObject.id << ", x: "
			// 	<< obuObject.x << ", y: "
			// 	<< obuObject.y
			// 	<< endl;

			m_computeAssignmentMatrix.setAssociateObject(lidarObjectBox, obuObjectBox);
			if(lidarOBUFusionMethod == 0){ // IOU
				associateMatrix[i][j] = m_computeAssignmentMatrix.getBoxIOUDistance();
				// cout<<FRED("iou = ") << 1-associateMatrix[i][j] << endl;
			}
			else{ // distance
				associateMatrix[i][j] = m_computeAssignmentMatrix.getLocationDistance();
				// cout<<FRED("distance = ") << associateMatrix[i][j] << endl;
			}
			m_computeAssignmentMatrix.resetDistance();
		}
	}
	vector<int> assignment(lidarObjectNum, -1);
	m_hungarianAlgorithm.Solve(associateMatrix, assignment);

	vector<int> lidarObjectIndexUnmatched, obuObjectIndexUnmatched;//lidar-OBU 未匹配的lidar目标使用lidar-radar匹配的情况，匹配的目标将OBU目标转为lidar目标
	vector<std::vector<int>> lidarOBUObjectIndexsMatched;

	if(lidarOBUFusionMethod == 0) { // IOU
		float matchTreshold = 0;//raw = 0.1
		for(int i = 0; i < assignment.size(); i++){
			if(assignment[i] == -1){
				lidarObjectIndexUnmatched.emplace_back(i);
				continue;
			}
			else if(1 - associateMatrix[i][assignment[i]] > matchTreshold
				&& m_trackingObjectsVector[i].m_classification != COMMON::LidarDetectionClassification::Unknown
			){
				// IOU大于阈值，且不是跟踪目标不是聚类目标
				std::vector<int> lidarOBUObjectIndexMatched = {i, assignment[i]};
				lidarOBUObjectIndexsMatched.emplace_back(lidarOBUObjectIndexMatched);
			}
			else{
				lidarObjectIndexUnmatched.emplace_back(i);
			}
		}
	}
	else { // distance
		float distanceThreshold = 15;//设置距离匹配阈值
		for(int i = 0; i < assignment.size(); i++){
			if(assignment[i] == -1){
				continue;
			}
			else if(associateMatrix[i][assignment[i]] < distanceThreshold){
				std::vector<int> lidarOBUObjectIndexMatched = {i, assignment[i]};
				lidarOBUObjectIndexsMatched.emplace_back(lidarOBUObjectIndexMatched);
			}
			else{
				lidarObjectIndexUnmatched.emplace_back(i);
			}
		}
	}


	for(int i = 0; i < obuObjectNum; ++i){
		auto it = std::find_if(lidarOBUObjectIndexsMatched.begin(),lidarOBUObjectIndexsMatched.end(),[&](const std::vector<int>& objectMatchIndex){
			if(objectMatchIndex[1] == i){
				return true;
			}
			return false;
		});

		if(it == lidarOBUObjectIndexsMatched.end()){
			obuObjectIndexUnmatched.emplace_back(i);
		}

	}

	int eraseCount = 0;
	for(auto it = obuObjectIndexUnmatched.begin(); it != obuObjectIndexUnmatched.end();){
		// 自车目标删除
		auto obuObject = obuobjects.obs[*it];
		if(abs(obuObject.x) < 1.5 && obuObject.y > -3.6 && obuObject.y < 2
			&& (obuObject.classification == COMMON::LidarDetectionClassification::Car || obuObject.classification == COMMON::LidarDetectionClassification::Bus
					|| obuObject.classification == COMMON::LidarDetectionClassification::Truck || obuObject.classification == COMMON::LidarDetectionClassification::Zombiecar)){
			m_pLogger->info("erase OBU self car id = {}", obuObject.id);
			obuObjectIndexUnmatched.erase(it);
			++eraseCount;
		}
		else{
			++it;
		}
	}

	m_pLogger->info("debug: lidar-obu: lidarObjectNum size = {}, matched trackingobjects size = {}, unmatched trackingobjects size = {}, eraseCount = {}",
					lidarObjectNum, lidarOBUObjectIndexsMatched.size(), lidarObjectIndexUnmatched.size(),eraseCount);
	//assert(lidarObjectNum == lidarOBUObjectIndexsMatched.size() + lidarObjectIndexUnmatched.size());
	m_pLogger->info("debug: obuObject size = {}, matched obuobject size = {}, unmatch obuobject size = {}, eraseCount size = {}",
					obuObjectNum, lidarOBUObjectIndexsMatched.size(), obuObjectIndexUnmatched.size(), eraseCount);
	m_allCarObuObjectsSize = lidarObjectNum + obuObjectIndexUnmatched.size(); //全部数量为跟踪数量+未匹配的OBU目标数量
	//assert(obuObjectNum == lidarOBUObjectIndexsMatched.size() + obuObjectIndexUnmatched.size() + eraseCount);

	// distanceMatch(obuobjects, lidarOBUObjectIndexsMatched, lidarObjectIndexUnmatched, obuObjectIndexUnmatched, eraseCount);

	// 匹配目标变更融合标志位
	m_pLogger->info("INFO: matched pair:");
	for (int i = 0; i < lidarOBUObjectIndexsMatched.size(); ++i) {
		std::vector<int> lidarOBUObjectIndexMatched = lidarOBUObjectIndexsMatched[i];
		// TODO 根据跟踪目标的更新次数和当前速度，与僵尸车目标速度判断是否匹配，删除僵尸车容器中的目标
		int objectValue = (int)m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_value;
		{
			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_isMatched = true;
			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_id = obuobjects.obs[lidarOBUObjectIndexMatched[1]].id;
			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_value =COMMON::SensorType::LIDAR_OBU;
			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_classification =obuobjects.obs[lidarOBUObjectIndexMatched[1]].classification;
			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_centerLLA.resize(3,0);
			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_centerLLA[0] =obuobjects.obs[lidarOBUObjectIndexMatched[1]].longtitude;
			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_centerLLA[1] =obuobjects.obs[lidarOBUObjectIndexMatched[1]].latitude;

			if(m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id == m_pConfigManager->m_debugID){
				m_pLogger->debug("v2iobjectfusion id = {}, 车端类别={}, 路端类型 = {}",
							m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id, m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_classification,
							obuobjects.obs[lidarOBUObjectIndexMatched[1]].classification);
			}

			std::vector<double> v_lonlat;
			m_wgs84Utm.utm2LLADegree(m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[0], m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[1],
									m_pConfigManager->m_cityUTMCode, false, v_lonlat);
			bool l_isInStationRange = isInStationRange(v_lonlat[1], v_lonlat[0], 150);

			if(l_isInStationRange){
				switch (objectValue){
					case COMMON::SensorType::LIDAR:
					case COMMON::SensorType::LIDAR_OBU:{
							if(m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("v2iobjectfusion 1 跟踪类型={},更改类型 = {}",objectValue, COMMON::SensorType::LIDAR_OBU);
							}
							m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_value = COMMON::SensorType::LIDAR_OBU;
						}
						break;
					case COMMON::SensorType::LIDAR_RADAR:
					case COMMON::SensorType::LIDAR_RADAR_OBU:{
							if(m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("v2iobjectfusion 2 跟踪类型={},更改类型 = {}",objectValue, COMMON::SensorType::LIDAR_RADAR_OBU);
							}
							m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_value = COMMON::SensorType::LIDAR_RADAR_OBU;
						}
						break;
					default:{
							if(m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("v2iobjectfusion 3 跟踪类型={},更改类型 = {}",objectValue, COMMON::SensorType::LIDAR_OBU);
							}
							m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_value = COMMON::SensorType::LIDAR_OBU;
						}
						break;
				}
			}



			m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_classification = m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_classification;

			if(m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_sStationObjectsInfo.m_classification == COMMON::LidarDetectionClassification::Zombiecar){
				m_pLogger->debug("v2i 跟踪匹配到僵尸车, tracking object id = {}, matched obuobject id = {}",
							m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id,obuobjects.obs[lidarOBUObjectIndexMatched[1]].id);
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_absVelocityUTM = std::vector<double>{0.0, 0.0, 0.0};
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[6] = 0.0;
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[7] = 0.0;
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_kalmanFilter.setVx(0.0);
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_kalmanFilter.setVy(0.0);
			}
			else{
				// 不做处理，对速度处理后影响车端跟踪速度结果
				// Eigen::Vector3d bodyAxisSpeed{obuobjects.obs[lidarOBUObjectIndexMatched[1]].relspeedx + m_selfCarSpeed[0],obuobjects.obs[lidarOBUObjectIndexMatched[1]].relspeedy + m_selfCarSpeed[1], 0.0};
				// double headingInNorthDegree = obuobjects.obs[lidarOBUObjectIndexMatched[1]].azimuth * 180.0 / M_PI + m_gps.heading;//
				// headingInNorthDegree = std::fmod(headingInNorthDegree * 180.0 / M_PI, 2.0 * M_PI) * 180.0 / M_PI;
				// Eigen::Vector3d objectHeadingEulerDegrees{0, 0, headingInNorthDegree};
				// std::vector<double> objectSpeedInENUAxis = m_sensorAxisTransformer.bodyAxis2ENU(bodyAxisSpeed);
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_absVelocityUTM = std::vector<double>{objectSpeedInENUAxis[0], objectSpeedInENUAxis[1], objectSpeedInENUAxis[2]};
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[6] = objectSpeedInENUAxis[0];
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[7] = objectSpeedInENUAxis[1];
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_kalmanFilter.setVx(objectSpeedInENUAxis[0]);
				// m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_kalmanFilter.setVy(objectSpeedInENUAxis[1]);

				// cout<<" tracking object id = " << m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id
				// 	<<", raw 横向速度m_trackingBoxResult = " << m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[6]
				// 	<<", raw 纵向速度m_trackingBoxResult = " << m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_trackingBoxResult[7]
				// 	<<", raw 横向速度m_absVelocityUTM = " << m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_absVelocityUTM[0]
				// 	<<", raw 纵向速度m_absVelocityUTM = " << m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_absVelocityUTM[1]
				// 	<<", 横向速度objectSpeedInENUAxis = " << objectSpeedInENUAxis[0]
				// 	<<", 纵向速度objectSpeedInENUAxis = " << objectSpeedInENUAxis[1]
				// 	<<", 横向速度bodyAxisSpeed = " << bodyAxisSpeed[0]
				// 	<<", 纵向速度bodyAxisSpeed = " << bodyAxisSpeed[1]
				// 	<<", 横向速度m_selfCarSpeed = " << m_selfCarSpeed[0]
				// 	<<", 纵向速度m_selfCarSpeed = " << m_selfCarSpeed[1]
				// 	<< endl;
			}

			if(m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("tracking object id = {}, matched obuobject id = {}",
								m_trackingObjectsVector[lidarOBUObjectIndexMatched[0]].m_id,obuobjects.obs[lidarOBUObjectIndexMatched[1]].id);
		}
		// cout << endl;
	}


	for(int i = 0; i < lidarObjectIndexUnmatched.size(); ++i){
		ObjectTracking& objectTracked = m_trackingObjectsVector[lidarObjectIndexUnmatched[i]];
		int trackingTypeValue = (int)objectTracked.m_value;
		// std::vector<double> v_lonlat;
		// m_wgs84Utm.utm2LLADegree(objectTracked.m_trackingBoxResult[0], objectTracked.m_trackingBoxResult[1],
		// 						m_pConfigManager->m_cityUTMCode, false, v_lonlat);
		// bool l_isInStationRange = isInStationRange(v_lonlat[1], v_lonlat[0], 100);
		if(objectTracked.m_sStationObjectsInfo.m_isMatched){ //l_isInStationRange &&
			// 在基站范围内
			switch (trackingTypeValue)
				{
					case COMMON::SensorType::LIDAR_OBU:
					case COMMON::SensorType::LIDAR_RADAR_OBU:
						{
							if(objectTracked.m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("v2iobjectfusion 4 跟踪类型={}",trackingTypeValue);
							}
						}
						break;
					case COMMON::SensorType::LIDAR:{
							if(objectTracked.m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("v2iobjectfusion 5 跟踪类型={},更改类型 = {}",trackingTypeValue, COMMON::SensorType::LIDAR_OBU);
							}
							objectTracked.m_value = COMMON::SensorType::LIDAR_OBU;
						}
						break;
					case COMMON::SensorType::LIDAR_RADAR:{
							if(objectTracked.m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("v2iobjectfusion 6 跟踪类型={},更改类型 = {}",trackingTypeValue, COMMON::SensorType::LIDAR_RADAR_OBU);
							}
							objectTracked.m_value = COMMON::SensorType::LIDAR_RADAR_OBU;
						}
						break;
					default:{
							if(objectTracked.m_id == m_pConfigManager->m_debugID){
								m_pLogger->debug("v2iobjectfusion 7 跟踪类型={},更改类型 = {}",trackingTypeValue, COMMON::SensorType::LIDAR_OBU);
							}
							objectTracked.m_value = COMMON::SensorType::LIDAR_OBU;
						}
						break;
				}

		}
		else{
			if(objectTracked.m_id == m_pConfigManager->m_debugID){
				m_pLogger->debug("v2iobjectfusion 8 跟踪类型={}",trackingTypeValue);
			}
		}
	}

	// 统计未匹配的OBU数据，加入到v2i的目标中，发送到PAD
	m_v2ifusionObjects.obs.clear();
	m_v2ifusionObjectsRVIZ.isvalid = 1;
	m_v2ifusionObjectsRVIZ.timestamp = curTrackingObjects.timestamp;
	m_v2ifusionObjectsRVIZ.gpstime = curTrackingObjects.gpstime;
	m_v2ifusionObjectsRVIZ.obs.clear();
	m_v2ifusionObjectsPAD.isvalid = 1;
	m_v2ifusionObjectsPAD.timestamp = curTrackingObjects.timestamp;
	m_v2ifusionObjectsPAD.gpstime = curTrackingObjects.gpstime;
	m_v2ifusionObjectsPAD.obs.clear();

	trackingObjects2SensorObjects(m_v2ifusionObjects);
	common_msgs::sensorobjects obuobjectsUnmatched;
	mergeLidarOBUObjects(obuobjects, obuObjectIndexUnmatched, obuobjectsUnmatched);

	m_pLogger->debug("m_v2ifusionObjects size ={}, m_v2ifusionObjectsRVIZ size ={}, m_v2ifusionObjectsPAD size ={},obuobjectsUnmatched size ={}",
					m_v2ifusionObjects.obs.size(),m_v2ifusionObjectsRVIZ.obs.size(),m_v2ifusionObjectsPAD.obs.size(),obuobjectsUnmatched.obs.size());

	m_v2ifusionObjects.obs.insert(m_v2ifusionObjects.obs.end(), obuobjectsUnmatched.obs.begin(),obuobjectsUnmatched.obs.end());
	m_v2ifusionObjectsRVIZ.obs.insert(m_v2ifusionObjectsRVIZ.obs.end(), obuobjectsUnmatched.obs.begin(),obuobjectsUnmatched.obs.end());
	m_v2ifusionObjectsPAD.obs.insert(m_v2ifusionObjectsPAD.obs.end(), obuobjectsUnmatched.obs.begin(),obuobjectsUnmatched.obs.end());

	pub_v2iFusionObject.publish(m_v2ifusionObjects); //"objectfusion"
	pub_v2iFusionObjectRVIZ.publish(m_v2ifusionObjectsRVIZ);
	pub_v2iFusionObjectPAD.publish(m_v2ifusionObjectsPAD);
	double curRosTimestamp = ros::Time::now().toSec();
	float timeGap = curRosTimestamp * 1000.0 - curTrackingObjects.timestamp;
	m_pLogger->info("publish V2I-4 current ros time = {:.3f}, object time : = {:.3f}, 时间差 = {:.3f}",
					curRosTimestamp, curTrackingObjects.timestamp / 1000.0, timeGap);
	// 	检测框小，在基站检测框内无法匹配问题：使用距离或者点在框内
}

void ObjectFusionTracking::distanceMatch(common_msgs::sensorobjects& obuobjects,
		vector<std::vector<int>>& lidarOBUObjectIndexsMatched,
        vector<int>& lidarObjectIndexUnmatched, vector<int>& obuObjectIndexUnmatched, int eraseCount)
{
    int l_lidarObjectNum = lidarObjectIndexUnmatched.size();
    int l_obuObjectNum = obuObjectIndexUnmatched.size();
	m_pLogger->info("distanceMatch: l_lidarObjectNum = {}，  sensorObjects size = {}", l_lidarObjectNum, l_obuObjectNum);

    if(l_lidarObjectNum == 0 || l_obuObjectNum == 0){
        return;
    }

    m_computeAssignmentMatrix.setAssignmentMatrix(l_lidarObjectNum,l_obuObjectNum);
    for (size_t i = 0; i < l_lidarObjectNum; i++)
    {
        int lidarObjectUnmatchedIndex = lidarObjectIndexUnmatched[i];
        std::vector<float> l_lidarObjectBox{m_trackingObjectsVector[lidarObjectUnmatchedIndex].m_objectOriginal.x, m_trackingObjectsVector[lidarObjectUnmatchedIndex].m_objectOriginal.y, 1,
		                                  m_trackingObjectsVector[lidarObjectUnmatchedIndex].m_objectOriginal.length, m_trackingObjectsVector[lidarObjectUnmatchedIndex].m_objectOriginal.width, 1,
		                                  m_trackingObjectsVector[lidarObjectUnmatchedIndex].m_objectOriginal.azimuth};
        m_computeAssignmentMatrix.setPredictedObject(l_lidarObjectBox);

        for(int j = 0; j < l_obuObjectNum; ++j){
			int obuObjectUnmatchedIndex = obuObjectIndexUnmatched[j];
			common_msgs::sensorobject obuObject = obuobjects.obs[obuObjectUnmatchedIndex];
			std::vector<float> l_obuObjectBox = {obuObject.x, obuObject.y, 1,
							obuObject.length, obuObject.width,1, obuObject.azimuth};

            m_computeAssignmentMatrix.setDetectedObject(l_obuObjectBox);
            m_computeAssignmentMatrix.m_assignmentMatrix[i][j] = m_computeAssignmentMatrix.getBoxIOUDistance();
        }
    }
    m_hungarianAlgorithm.Solve(m_computeAssignmentMatrix.m_assignmentMatrix, m_computeAssignmentMatrix.m_assignmentPairIndex);
    m_matchedPairs.clear();
    float iouThreshold = 0.1;
    vector<int> l_vUnmatchedLidarObjectsIndex;
    vector<int> l_vUnmatchedObuObjectsIndex;
	for (unsigned int i = 0; i < l_lidarObjectNum; ++i)
	{
		int lidarObjectUnmatchedIndex = lidarObjectIndexUnmatched[i];

		if (m_computeAssignmentMatrix.m_assignmentPairIndex[i] == -1){
            l_vUnmatchedLidarObjectsIndex.emplace_back(lidarObjectUnmatchedIndex);
			continue;
		}
		int obuObjectUnmatchedIndex = obuObjectIndexUnmatched[m_computeAssignmentMatrix.m_assignmentPairIndex[i]];
		if (1 - m_computeAssignmentMatrix.m_assignmentMatrix[i][m_computeAssignmentMatrix.m_assignmentPairIndex[i]] < iouThreshold)
		{
            l_vUnmatchedLidarObjectsIndex.emplace_back(lidarObjectUnmatchedIndex);
            l_vUnmatchedObuObjectsIndex.emplace_back(obuObjectUnmatchedIndex);
		}
		else {

			std::vector<int> lidarOBUObjectIndexMatched = {lidarObjectUnmatchedIndex, obuObjectUnmatchedIndex};
			lidarOBUObjectIndexsMatched.emplace_back(lidarOBUObjectIndexMatched);
		}
	}

    // 从未匹配的容器中找到匹配对，存入匹配容器
    lidarObjectIndexUnmatched = l_vUnmatchedLidarObjectsIndex;
    obuObjectIndexUnmatched = l_vUnmatchedObuObjectsIndex;
	m_pLogger->info("lidar-obu距离匹配：lidar目标个数 = {}, 匹配的目标个数 = {}, 不匹配fusion个数 = {}, obu目标个数 = {}, 不匹配检测个数 = {}, eraseCount = {}",
					l_lidarObjectNum, m_matchedPairs.size(), lidarObjectIndexUnmatched.size(), l_obuObjectNum, obuObjectIndexUnmatched.size(),eraseCount);

    //assert( l_lidarObjectNum == m_matchedPairs.size() + lidarObjectIndexUnmatched.size());
    //assert( l_obuObjectNum == m_matchedPairs.size() + obuObjectIndexUnmatched.size());
}

// 跟踪目标中加入OBU数据
void ObjectFusionTracking::mergeLidarOBUObjects(const common_msgs::sensorobjects& obuobjects,
							const std::vector<int>& obuObjectIndexUnmatched,
                          common_msgs::sensorobjects& mergedObjects)
{
	mergedObjects.isvalid = 1;
	mergedObjects.timestamp = m_trackedObjects.timestamp;
	mergedObjects.gpstime = m_trackedObjects.gpstime;
	mergedObjects.obs.clear();

	// 匹配标志：匹配上了，根据记录的匹配路端ID，不添加匹配的路端ID到v2ifusion中
	int beforeMatchedObuObjectsCount = 0;
	for (size_t i = 0; i < obuObjectIndexUnmatched.size(); i++)
	{
		long obuObjectIndexUnmatchedID = obuobjects.obs[obuObjectIndexUnmatched[i]].id;
		auto it = std::find_if(m_trackingObjectsVector.begin(),m_trackingObjectsVector.end(), [&](ObjectTracking& objectTracking){
			// cout << "debug: tracking id = " << objectTracking.m_sStationObjectsInfo.m_id
			// 	<< ", obuObjectIndexUnmatchedID id = " << obuObjectIndexUnmatchedID << endl;
			return objectTracking.m_sStationObjectsInfo.m_id == obuObjectIndexUnmatchedID;
		});

		if(it == m_trackingObjectsVector.end()){
			// cout << "debug: id = " << obuObjectIndexUnmatchedID <<", obuobject value = "<< std::to_string(obuobjects.obs[obuObjectIndexUnmatched[i]].value)
			// 	<< endl;

			common_msgs::sensorobject obuObject = obuobjects.obs[obuObjectIndexUnmatched[i]];
			obuObject.value = COMMON::SensorType::OBU;

			auto it = m_obuLidarTrackedObjectIDMap.find(obuObject.id);
			if(it != m_obuLidarTrackedObjectIDMap.end()){
				obuObject.id = it->second;//之前匹配上了，OBU的ID使用之前匹配的ID
			}
			else{
				// 之前没有匹配上，生成新的ID
				int obuTransdID = ObjectTracking::generateNewTrackId();
				m_obuLidarTrackedObjectIDMap.insert(std::pair<uint32_t,int>(static_cast<uint32_t>(obuObject.id), static_cast<int>(obuTransdID)));
				obuObject.id = obuTransdID;
			}

			// if(obuObject.y > -50 && obuObject.y < 65){
				mergedObjects.obs.push_back(obuObject);
			// }
		}
		else{
			// 之前匹配上，将匹配上的跟踪目标设置融合标志
			int index = std::distance(m_trackingObjectsVector.begin(),it);
			m_trackingObjectsVector[index].m_sStationObjectsInfo.m_isMatched = true;
			m_trackingObjectsVector[index].m_sStationObjectsInfo.m_value =COMMON::SensorType::LIDAR_OBU;

			switch (m_trackingObjectsVector[index].m_value)
			{
				case COMMON::SensorType::LIDAR:
					m_trackingObjectsVector[index].m_value = COMMON::SensorType::LIDAR_OBU;
					break;
				case COMMON::SensorType::LIDAR_RADAR:
					m_trackingObjectsVector[index].m_value = COMMON::SensorType::LIDAR_RADAR_OBU;
					break;
				case COMMON::SensorType::LIDAR_OBU:
					m_trackingObjectsVector[index].m_value = COMMON::SensorType::LIDAR_OBU;
					break;
				case COMMON::SensorType::LIDAR_RADAR_OBU:
					m_trackingObjectsVector[index].m_value = COMMON::SensorType::LIDAR_RADAR_OBU;
					break;
				default:
					m_trackingObjectsVector[index].m_value = COMMON::SensorType::LIDAR_OBU;
					break;
			}

			// m_trackingObjectsVector[index].m_value =COMMON::SensorType::LIDAR_OBU;
			m_trackingObjectsVector[index].m_sStationObjectsInfo.m_classification =obuobjects.obs[obuObjectIndexUnmatched[i]].classification;
			m_trackingObjectsVector[index].m_classification = m_trackingObjectsVector[index].m_sStationObjectsInfo.m_classification;

			++beforeMatchedObuObjectsCount;
		}
	}

	m_pLogger->info("debug: merge obuObject size = {}, obuObjectIndexUnmatched.size = {}, merged obuobject size = {}, beforeMatchedObuObjectsCount size = {}",
					obuobjects.obs.size() , obuObjectIndexUnmatched.size(), mergedObjects.obs.size(),beforeMatchedObuObjectsCount);


}

/**
 * @description: 跟踪目标转成sensorobjects格式，并设定各个话题目标发布条件
 * @param {sensorobjects&} sensorobjces 返回值
 * @return {*}
 */
void ObjectFusionTracking::trackingObjects2SensorObjects(common_msgs::sensorobjects& sensorobjces){
	std::vector<double> objectSpeedInCarBackFRU(3);
	std::vector<double>  objectPositionInCarBackFRU(3);
	vector<double> boxInfo(7);
	vector<vector<double>> pointsVector(8, vector<double>(3));

    for(int i = 0; i < m_trackingObjectsVector.size(); i++)	{
		const ObjectTracking& objectTracking = m_trackingObjectsVector[i];
		common_msgs::sensorobject obj;
		obj.id = static_cast<uint32_t>(objectTracking.m_id);

		// if(!m_common.isMotorVehicle(objectTracking.m_classification) && objectTracking.m_hits < 6 && objectTracking.m_age < 10)
		// 	continue;
		// else if(m_common.isMotorVehicle(objectTracking.m_classification) && objectTracking.m_hits < 8 && objectTracking.m_age < 10){
		// 	continue;
		// }

		if(m_common.isNonMotorVehicle(objectTracking.m_classification) && objectTracking.m_hit_streak < 2){
			if(objectTracking.m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("[v2i publish nonMotorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
								objectTracking.m_id, objectTracking.m_classification, objectTracking.m_hit_streak,
								objectTracking.m_hits, objectTracking.m_age);
			continue;
		}
		else if(m_common.isMotorVehicle(objectTracking.m_classification) && objectTracking.m_hits < 5
			&& objectTracking.m_classification != COMMON::LidarDetectionClassification::Unknown){
			if(objectTracking.m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("[v2i publish jump motorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
								objectTracking.m_id, objectTracking.m_classification, objectTracking.m_hit_streak,
								objectTracking.m_hits, objectTracking.m_age);
			continue;
		}
		else if(m_common.isClusterObject(objectTracking.m_classification) && objectTracking.m_age < 10){
			if(objectTracking.m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("[v2i publish jump unknown] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
									objectTracking.m_id, objectTracking.m_classification, objectTracking.m_hit_streak,
									objectTracking.m_hits, objectTracking.m_age);
			continue;
		}

		Eigen::Vector3d inputUTMPosition{objectTracking.m_trackingBoxResult[0] - m_selfCarUTMPosition[0],
										 objectTracking.m_trackingBoxResult[1] - m_selfCarUTMPosition[1],
		                                  objectTracking.m_objectOriginal.z};
		objectPositionInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(inputUTMPosition);

		obj.x = objectPositionInCarBackFRU[0];
		obj.y = objectPositionInCarBackFRU[1];
		obj.z = objectPositionInCarBackFRU[2];
		WGS84Corr lla;//填充经纬度
		if(objectTracking.m_sStationObjectsInfo.m_isMatched && objectTracking.m_sStationObjectsInfo.m_classification == COMMON::LidarDetectionClassification::Zombiecar){
			lla.log = objectTracking.m_sStationObjectsInfo.m_centerLLA[0] * M_PI / 180;
			lla.lat = objectTracking.m_sStationObjectsInfo.m_centerLLA[1] * M_PI / 180;
		}
		else{
			m_wgs84Utm.UTMXYToLatLon(objectTracking.m_trackingBoxResult[0], objectTracking.m_trackingBoxResult[1],
								 m_pConfigManager->m_cityUTMCode, false, lla);
		}


		//计算8角点
		float objectAngleDegreeInCarBackRFU_Clockwise = m_sensorAxisTransformer.NorthClockwise2CarBackRFU(objectTracking.m_trackingBoxResult[5] * 180 / M_PI, m_gps.heading);
		float objectAngleRadInCarBackRFU_Clockwise = objectAngleDegreeInCarBackRFU_Clockwise * M_PI / 180.0;

		double objectAngleRadInCarBackRFU_ClockwiseNormalized = m_common.normalizeAngle(objectAngleRadInCarBackRFU_Clockwise);
		objectAngleRadInCarBackRFU_Clockwise = objectAngleRadInCarBackRFU_ClockwiseNormalized;

		float headingClockwise = 2.0 * M_PI - objectAngleRadInCarBackRFU_Clockwise; // 显示 -- 跟画框有关:画框需要逆时针，保存CSV需要顺时针，发送需要顺时针
		boxInfo = {obj.x, obj.y, obj.z,
				  objectTracking.m_trackingBoxResult[2], objectTracking.m_trackingBoxResult[3],
		           objectTracking.m_trackingBoxResult[4],
		           headingClockwise};
		pointsVector = m_common.boxes_to_corners_3d(boxInfo);
		obj.points.clear();
		for(const auto& pointVector:pointsVector){
			common_msgs::point3d  cornerPoint;
			cornerPoint.x = pointVector[0];
			cornerPoint.y = pointVector[1];
			cornerPoint.z = pointVector[2];
			obj.points.emplace_back(std::move(cornerPoint));
		}

		obj.longtitude = lla.log / M_PI * 180;
		obj.latitude = lla.lat / M_PI * 180;


		Eigen::Vector3d UTMRelativeSpeed{objectTracking.m_trackingBoxResult[6] - m_gps.speedE,
									  objectTracking.m_trackingBoxResult[7] - m_gps.speedN,
		                                0};

		objectSpeedInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(UTMRelativeSpeed);

		obj.relspeedx = objectSpeedInCarBackFRU[0];//UTM速度转到ENU速度
		obj.relspeedy = objectSpeedInCarBackFRU[1];

		obj.length = objectTracking.m_trackingBoxResult[2];
		obj.width = objectTracking.m_trackingBoxResult[3];
		obj.height = objectTracking.m_trackingBoxResult[4];
		obj.azimuth = objectAngleRadInCarBackRFU_Clockwise;
		if(objectTracking.m_sStationObjectsInfo.m_isMatched){
			obj.classification = objectTracking.m_sStationObjectsInfo.m_classification;
		}
		else{
			obj.classification = objectTracking.m_classification;
		}


		// bool l_isInStationRange = isInStationRange(obj.latitude, obj.longtitude, 100);
		// if(l_isInStationRange){//objectTracking.m_sStationObjectsInfo.m_value == COMMON::SensorType::LIDAR_OBU  &&
		// 	obj.value = static_cast<uint8_t>(COMMON::SensorType::LIDAR_OBU);
		// }
		// else{
			obj.value = static_cast<uint8_t>(objectTracking.m_value);//融合情况 value存融合的情况
		// }

		obj.confidence = objectTracking.m_confidence; //置信度

		//TODO
		// {
		// common_msgs::objecthistory objecthistory;
		// obj.objectHistory = objectTracking.m_historyTrajectoryInfo.predictedTrajectory;
		// obj.objectPrediction = objectTracking.m_historyTrajectoryInfo.historyTrajectory;
		// }

		obj.drivingIntent = objectTracking.m_drivingIntent;
		obj.radarIndex = objectTracking.m_radarIndex;
		obj.radarObjectID = objectTracking.m_radarObjectID;



		common_msgs::sensorobject objRVIZ = obj;//用于发布到RVIZ，显示严格
		if(!m_common.isMotorVehicle(objectTracking.m_classification) && (objectTracking.m_hits > 8 || objectTracking.m_age > 10)){
			m_v2ifusionObjectsRVIZ.obs.push_back(objRVIZ);
		}
		else if(m_common.isMotorVehicle(objectTracking.m_classification) && (objectTracking.m_hits > 10 || objectTracking.m_age > 10)){
			m_v2ifusionObjectsRVIZ.obs.push_back(objRVIZ);
		}

		common_msgs::sensorobject objPAD = obj;//用于发布到pad，显示严格
		float distance = sqrt(obj.x * obj.x + obj.y * obj.y);
		if(distance > 50){
			// 不存入PAD显示
			if(objectTracking.m_value != COMMON::SensorType::LIDAR && objectTracking.m_value != COMMON::SensorType::LIDAR_RADAR){
				m_v2ifusionObjectsPAD.obs.push_back(objPAD);
			}
		}
		else if(distance > 25){
			if(!m_common.isMotorVehicle(objectTracking.m_classification) && (objectTracking.m_hits > 25 || objectTracking.m_age > 15)){
				if(objectTracking.m_id == m_pConfigManager->m_debugID)
					m_pLogger->info("[v2i-pad 大于50米 publish jump nomotorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
									objectTracking.m_id, objectTracking.m_classification, objectTracking.m_hit_streak,
									objectTracking.m_hits, objectTracking.m_age);
				m_v2ifusionObjectsPAD.obs.push_back(objPAD);
			}
			else if(m_common.isMotorVehicle(objectTracking.m_classification) && (objectTracking.m_hits > 20 || objectTracking.m_age > 15)){
				if(objectTracking.m_id == m_pConfigManager->m_debugID)
					m_pLogger->info("[v2i-pad 大于50米 publish jump motorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
									objectTracking.m_id, objectTracking.m_classification, objectTracking.m_hit_streak,
									objectTracking.m_hits, objectTracking.m_age);
				m_v2ifusionObjectsPAD.obs.push_back(objPAD);
			}
		}
		else{
			if(!m_common.isMotorVehicle(objectTracking.m_classification) && (objectTracking.m_hits > 15 || objectTracking.m_age > 15)){
				if(objectTracking.m_id == m_pConfigManager->m_debugID)
					m_pLogger->info("[v2i-pad 小于50米 publish jump nomotorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
									objectTracking.m_id, objectTracking.m_classification, objectTracking.m_hit_streak,
									objectTracking.m_hits, objectTracking.m_age);
				m_v2ifusionObjectsPAD.obs.push_back(objPAD);
			}
			else if(m_common.isMotorVehicle(objectTracking.m_classification) && (objectTracking.m_hits > 15 || objectTracking.m_age > 15)){
				if(objectTracking.m_id == m_pConfigManager->m_debugID)
					m_pLogger->info("[v2i-pad 小于50米 publish jump motorVehicle] id = {}, classification = {}, objectTracking.m_hit_streak = {}, objectTracking.m_hits = {},	objectTracking.m_age = {}",
									objectTracking.m_id, objectTracking.m_classification, objectTracking.m_hit_streak,
									objectTracking.m_hits, objectTracking.m_age);
				m_v2ifusionObjectsPAD.obs.push_back(objPAD);
			}
		}



		// 聚类目标填充点，否则使用8角点
		// 如果有多边形点，将多边形点加入到角点中，发给决策，如果没有就发送8角点
		std::vector<common_msgs::point3d> cornerPoints = objectTracking.m_objectOriginal.points;
		int cornerPointsSize = cornerPoints.size();

		bool isUseDetection8Corners = true;
		if(isUseDetection8Corners){
			obj.points.clear();
			for (int i = 0; i < cornerPointsSize; ++i) {
				obj.points.push_back(cornerPoints[i]);
			}
			sensorobjces.obs.push_back(obj);

		}
		else{
			// 跟踪目标不要检测的8角点
			if(objectTracking.m_classification == COMMON::LidarDetectionClassification::Unknown){
				// 聚类目标，计算8角点不要，只要聚类点
				obj.points.clear();
				for (int i = 0; i < cornerPointsSize; ++i) {
					obj.points.push_back(cornerPoints[i]);
				}
				sensorobjces.obs.push_back(obj);
			}
			else{
				if(cornerPointsSize > 8){
					obj.points.clear();
					for (int i = 8; i < cornerPointsSize; ++i) {
						obj.points.push_back(cornerPoints[i]);
					}
					sensorobjces.obs.push_back(obj);
				}
			}
		}
	}

	// 未匹配的OBU目标加入所有目标容器中
	// for(const auto& obuUnmatchedObject:sensorobjces.obs){
	// 	m_v2ifusionObjectsRVIZ.obs.emplace_back(obuUnmatchedObject);
	// }
}

common_msgs::sensorobjects ObjectFusionTracking::getV2IFusionObjectsRVIZ(){
	return m_v2ifusionObjectsRVIZ;
}

/**
 * @description: 获取所有目标数量：车端跟踪目标+OBU目标
 * @return {所有目标数量}
 */
int ObjectFusionTracking::getAllLidarOBUObjectsSize(){
	return m_allCarObuObjectsSize;
}

bool ObjectFusionTracking::isInStationRange(const double carLatitude, const double carLontitude,
					   const double distanceFromStation){
	bool l_isInStationRange = false;
	int stationSize = m_pConfigManager->m_stationRange.size();
	for (size_t i = 0; i < stationSize; i++){
		l_isInStationRange = l_isInStationRange || m_common.isInStationRange(carLatitude, carLontitude,
							m_pConfigManager->m_stationRange[i][1], m_pConfigManager->m_stationRange[i][0], distanceFromStation);
		if(l_isInStationRange)
			break;
	}
	return l_isInStationRange;
}




void ObjectFusionTracking::MHTTracking(){
	double t_now = m_curFrameStamp * 0.001;
	cout<<"t_now = " << t_now << endl;
	MHTProcess(m_lidarObjects.obs, m_volume, m_targetmodel, t_now, m_curFrameStamp / 1000.0, m_timeDiff);

}

/**
 * @description: 对航迹进行预测/跟踪
 * @param {detections&} 一帧中的检测
 * @param {Volume&} 扫描体积,量测点可能存在的范围和相关统计信息
 * @param {TargetPosition_CV2D&} targetmodel 目标模型
 * @param {double} t_now 当前时间
 * @return {*}
 */
void ObjectFusionTracking::MHTProcess(std::vector<common_msgs::sensorobject>& objectsMeasurements,
										const Volume& volume, TargetPosition_CV2D& targetmodel,
										double t_now,const double& timeStamp,const double& timeDiff){
	m_pLogger->info("into MHT process...");
	std::string detectionIDStr = "all ID: ";
	for(const auto& object: objectsMeasurements){
		detectionIDStr += std::to_string(object.id) + " ";
	}
	cout << detectionIDStr << endl;

	unordered_map<int, unordered_map<int, unordered_map<int, shared_ptr<LocalHypothesis>>>> track_updates;

	// 调用Track::predict()-> LocalHypothesis->predict() 进行预测
	#pragma omp parallel for collapse(2) // 合并两层循环
	for(const auto& [trackid, trackerPtr] : tracks_){
		unordered_map<int, shared_ptr<LocalHypothesis>>& lhyps = trackerPtr->lhyps_;
		for (auto& pair : lhyps) {//预测:对每个假设进行预测
			// cout<<"MHTProcess预测  trid = " << trackid << endl;
			if(trackid == 27){
				cout<<"MHTProcess预测前  x = " << pair.second->target_->_density->x().transpose() << endl;
				cout<<"MHTProcess预测前  P:\n" << pair.second->target_->_density->P()<< endl;
			}
			pair.second->predict(t_now); // 调用LocalHypothesis::predict()进行预测
			if(trackid == 27){
				cout<<"MHTProcess预测后 x = " << pair.second->target_->_density->x().transpose() << endl;
				cout<<"MHTProcess预测后 P:\n" << pair.second->target_->_density->P() << endl;
			}
		}
	}
	cout<<".............................................................." << endl;
	// 更新
	#pragma omp parallel for
	for(const auto& [trackid, trackerPtr] : tracks_){
		cout<<"MHTProcess更新  trid = " << trackid << endl;
		track_updates[trackid] = trackerPtr->update(objectsMeasurements, volume, t_now); // 调用Track::update
	}

	// 统计假设数量
	int lhpysize = 0;
	#pragma omp parallel for collapse(2) // 合并两层循环
	for (const auto& [trackid, lhyps] : track_updates) {
		for (const auto& [detectionid, lhyp] : lhyps) {
			++lhpysize;
		}
	}

	alllhpySize = lhpysize;
	cout<<"........................................................................................................" << endl;
	// cout<<"Tracker::track_updates size = " << track_updates.size() << endl;
	// cout<<"Tracker::objectsMeasurements size = " << objectsMeasurements.size() << endl;
	// cout<<"Tracker::alllhpySizee = " << alllhpySize << endl;
	// cout<<"Tracker::ghyps_.size() = " << ghyps_.size() << endl;
	cout<<"Tracker::lhpysize = " << lhpysize << endl;

    // 全局假设更新
   	update_global_hypotheses(track_updates, objectsMeasurements, targetmodel, volume, max_nof_hyps, hyp_weight_threshold, t_now);
    // 删除不在全局假设中的轨迹
    terminate_tracks();
    cout<<"finished terminate_tracks"<<endl;

	// 根据全局假设权重选择最优假设
	unordered_map<int,shared_ptr<Density>> estimations = estimates();
	// 目标轨迹合并。
	mergeCloseTargets(estimations);

	std::string idstr = "";
	for(const auto& [trackid, trackerPtr] : tracks_){
		idstr += std::to_string(trackid) + " ";
		// Eigen::VectorXd objectState = trackerPtr->lhyps_.second->target_.density_.x();
		// cout<<"trackid = " << trackid << ", x = " << objectState(0) << ", y = " << objectState(1) << endl;
	}
	// cout<<"track id: " << idstr << endl;

	// std::sort(estimations.begin(), estimations.end(), [](const auto& estimationA, const auto& estimationB){
	// 	return estimationA.first < estimationB.first;
	// });

	std::string objectInfoStr = "";
	#pragma omp parallel for collapse
	for(const auto& [trid,density] : estimations){
		objectInfoStr = "trid = " + std::to_string(trid) + ", label = " + class_name[density->xObject_.classification]
						+ ", x =  " + std::to_string(density->x()(0)) + ", y =  " + std::to_string(density->x()(1))
						+ ", yaw =  " + std::to_string(density->xObject_.azimuth * 180 / M_PI)
						+ ", vx =  " + std::to_string(density->x()(2)) + ", vy =  " + std::to_string(density->x()(3))
						+ "\n" + objectInfoStr;
	}
	cout << objectInfoStr << endl;
	// 显示MHT跟踪结果
	showTrackedObjectsInRviz(estimations);

}

/**
 * @description:
    1初始化：创建新的权重列表 new_weights 和新的假设列表 new_ghyps。
    2处理已有航迹：
        如果 tracks 不为空，遍历每个全局假设 ghyp 和其对应的权重 weight。
        计算成本矩阵 cost_matrix，如果 cost_matrix 包含航迹，则使用 Murty 算法计算最优分配。
        对于每个最优分配，更新 new_ghyp 和 new_weights。
    3处理无航迹情况：
        如果 tracks 为空，生成初始航迹;假设 init_ghyp 并计算其权重。
    4权重和假设处理：
        去除消失的航迹假设并对数归一化权重。
        剪枝假设并归一化权重。
        捕获假设并归一化权重。
    5更新航迹假设：
        更新每个航迹的假设，只保留与新全局假设匹配的部分。
    6更新全局假设和权重：
        将新的权重和假设赋值给类属性 gweights 和 ghyps。
 * @return {*}
 */

void ObjectFusionTracking::update_global_hypotheses(unordered_map<int, unordered_map<int, unordered_map<int, shared_ptr<LocalHypothesis>>>>& track_updates,
						vector<common_msgs::sensorobject>& Z, TargetPosition_CV2D& targetmodel, const Volume& volume, int max_nof_hyps,
						double weight_threshold, double t_now) {
	vector<double> new_weights;
	vector<unordered_map<int, int>> new_ghyps;

	// 预分配空间
	new_ghyps.reserve(ghyps_.size() * max_nof_hyps);
	new_weights.reserve(ghyps_.size() * max_nof_hyps);

	if (!tracks_.empty()) {
		cout<<"!tracks_.empty()"<<endl;
		cout <<"debug: ghyps_.size() = "<< ghyps_.size() <<", gweights_.size() = "<<gweights_.size() <<", track_updates.size() = "<<track_updates.size() <<endl;
		for (size_t i = 0; i < ghyps_.size(); ++i) {
			const unordered_map<int, int>& ghyp = ghyps_.at(i);
			const double weight = gweights_.at(i);

			CostMatrix cost_matrix(ghyp, track_updates);
			std::vector<int> track_ids = cost_matrix.tracks();
			if (!track_ids.empty()) {
				cout<<"更新全局假设：!track_ids.empty()"<<endl;
				int nof_best = ceil(exp(weight) * max_nof_hyps);
				cout <<"debug: weight = "<< weight <<", exp = "<<exp(weight)
					<<", max_nof_hyps = "<< max_nof_hyps  << ", exp(weight) * max_nof_hyps = " << exp(weight) * max_nof_hyps
					<<", nof_best = "<< nof_best <<", z.size = "<< Z.size()
					<<", track_updates.size = "<< track_updates.size()<<endl;
				bool hasSolutions = false;
				std::vector<std::tuple<double, std::unordered_map<int, int>, std::vector<int>>> solutionResult = cost_matrix.solutions(nof_best);
				for (const auto& [_, assignment, unassigned_detections] : solutionResult) {
					hasSolutions = true;
					int matchedlhpySize = 0;
					unordered_map<int, int> new_ghyp;
					#pragma omp parallel for
					for (const auto& [trid, lid] : ghyp) {
						// cout <<"trid = " << trid <<", lid = " << lid <<endl;
						if (assignment.find(trid) != assignment.end()) { // 若航迹id在分配索引列表中,更新tracks和new_ghyp
							std::unordered_map<int,std::shared_ptr<LocalHypothesis>> lhyps_from_gates = track_updates.at(trid).at(lid);
							// 处理：在门限内、不在门限内nullptr和未检测到量测
							// 检查并跳过 nullptr
							// TODO 所有的假设都有ID
							int detectionIndex = assignment.at(trid); // assignment行为轨迹，列为假设
							// std::shared_ptr<LocalHypothesis>& lhyp = lhyps_from_gates.at(detectionIndex); // TODO 由于Track：：update与python差异，python中MISS对应的NULL值改为了-1对应nullptr,-1排在了[0]索引的位置
							// // 跳过未匹配的感知结果
							// if(!lhyp){
							// 	// cout<<"update_global_hypotheses:objectsMeasurements未匹配检测 info: x = " << Z[detectionIndex].x << ", y = " << Z[detectionIndex].y << endl;
							// 	continue;
							// }
							// ++matchedlhpySize;
							// assert(lhyp != nullptr);
							// new_ghyp[trid] = lhyp->id();
							// tracks_[trid]->add(lhyp);

							if(detectionIndex != MISS) {
                                auto lhyp = lhyps_from_gates.at(detectionIndex);
                                if(lhyp) {
                                    new_ghyp[trid] = lhyp->id();
                                    tracks_[trid]->add(lhyp);
                                }
                            } else {
                                auto miss_lhyp = lhyps_from_gates.at(MISS);
                                new_ghyp[trid] = miss_lhyp->id();
                                tracks_[trid]->add(miss_lhyp);
                            }
						}
						else {
							// cout<<"trid not in assignment， tid = " << trid <<", lid = " << lid <<endl;
							new_ghyp[trid] = lid;
						}
					}
					// 处理未分配的量测，生成新的航迹假设
					cout<<"unassigned_detections size = "<< unassigned_detections.size() <<", matchedlhpySize = "<< matchedlhpySize
							<<", z.size = "<< Z.size() <<endl;
					assert(unassigned_detections.size() <=  Z.size());

					// if(unassigned_detections.empty()){
					// 	ROS_WARN("unassigned_detections.empty()");
					// 	new_weights.push_back(weight);
					// 	new_ghyps.push_back(new_ghyp);
					// }
					// else{

						vector<common_msgs::sensorobject> zUnassignedDetections;
						#pragma omp parallel for
						for(const auto& detectionIndex : unassigned_detections){
							// cout<<"detectionIndex = "<< detectionIndex <<", x  = "<< Z.at(detectionIndex).x <<", y  = "<< Z.at(detectionIndex).y <<endl;
							zUnassignedDetections.emplace_back(Z.at(detectionIndex));
						}

						pair<unordered_map<int, int>, double>  trackTreeResult = create_track_trees(zUnassignedDetections, volume, targetmodel, t_now);
						unordered_map<int, int>& init_ghyp = trackTreeResult.first;
						cout<<"未匹配目标初始全局假设：init_ghyp size = " << init_ghyp.size() <<endl;
						cout<<"未匹配目标初始全局假设： before new_ghyp size = " << new_ghyp.size() <<endl;
						new_ghyp.insert(init_ghyp.begin(), init_ghyp.end());
						cout<<"未匹配目标初始全局假设：new_ghyp size = " << new_ghyp.size() <<endl;
						// for(const auto& ghyp: new_ghyp){
						// 	cout<<"未匹配目标初始全局假设： tid = " << ghyp.first <<", lid = " << ghyp.second <<endl;
						// }
						// 得到相应的\Delta weight
						double weight_delta = _unnormalized_weight(new_ghyp);
						// 更新权重集合和假设集合
						new_weights.emplace_back(weight + weight_delta);
						new_ghyps.emplace_back(new_ghyp);
						cout<<"new_weights size111 = " << new_weights.size() <<", new_ghyps size = " << new_ghyps.size() <<endl;
						// for(const auto& weight: new_weights){
						// 	cout<<"未匹配目标更新全局假设： weight = " << weight <<", weight_delta = " << weight_delta <<endl;
						// }
					// }
					cout<<".............................................................." << endl;
				}
				// if(!hasSolutions){
				// 	ROS_WARN("do not has solutions");
				// 	// assert(hasSolutions = false);
				// 	new_weights.emplace_back(weight);
				// 	new_ghyps.emplace_back(ghyp);
				// }
			}
			else {
				cout<<"更新全局假设：track_ids.empty()"<<endl;
				new_weights.emplace_back(weight);
				new_ghyps.emplace_back(ghyp);
				cout<<"new_weights size222 = " << new_weights.size() <<", new_ghyps size = " << new_ghyps.size() <<endl;
				for(const auto& weight: new_weights){
					cout<<"更新全局假设track_ids.empty()： weight = " << weight <<endl;
				}
			}
		}
	}
	else {
		// 如果tracks为空,重新产生航迹和权值new_weight=>[double],newGhyps=>[{Track_ID:Hypo_ID}]
		cout<<"update_global_hypotheses::tracks_.empty()"<<endl;
		pair<unordered_map<int, int>, double>  trackTreeResult = create_track_trees(Z, volume, targetmodel, t_now);
		unordered_map<int, int>& init_ghyp = trackTreeResult.first;
		cout<<"init_ghyp size = " << init_ghyp.size() <<endl;
		for(auto& item : init_ghyp) {
			cout <<"跟踪ID = " << item.first <<  ": 假设ID = " << item.second << endl;
		}

		new_weights.emplace_back(_unnormalized_weight(init_ghyp)); // 所有目标的假设权重和
		new_ghyps.emplace_back(init_ghyp); // 所有目标的假设
		for(const auto& weight: new_weights){
			cout<<"初始化 weight = " << weight <<endl;
		}
	}

	assert(new_ghyps.size() == new_weights.size());
	cout<<"\n\nnew_weights size1 = " << new_weights.size() <<", new_ghyps size1 = " << new_ghyps.size()<<endl;
	// 去掉消失的航迹假设并对数归一化权重(去除dead_hypo)
	std::pair<vector<double>,vector<unordered_map<int, int>>> pruneDeadResult = prune_dead(new_weights, new_ghyps);
	new_weights = pruneDeadResult.first;
	new_ghyps = pruneDeadResult.second;
	// for(const auto& weight: new_weights){
	// 	cout<<"after prune_dead weight = " << weight <<endl;
	// }
	cout<<"new_weights size2 = " << new_weights.size() <<", new_ghyps size = " << new_ghyps.size() <<endl;
	// 去掉消失的航迹假设并对数归一化权重(去除dead_hypo)
	new_weights = normalize_log_sum(new_weights);
	// for(const auto& weight: new_weights){
	// 	cout<<"after normalize_log_sum weight = " << weight <<endl;
	// }

	assert(new_ghyps.size() == new_weights.size());
	// 假设剪枝并归一化权重(依照阈值去除)
	// cout << "\n\nnew_weights1: ";
	// for (const auto& weight : new_weights) {
	// 	cout << weight << " ";
	// }
	// cout << endl;
	cout << "weight_threshold1: " << weight_threshold <<", new_ghyps size = " << new_ghyps.size() << endl;

	// cout << "new_ghyps1: " << endl;
	// for (const auto& ghyp : new_ghyps) {
	// 	cout << "{ ";
	// 	for (const auto& [track_id, hypo_id] : ghyp) {
	// 		cout << track_id << ": " << hypo_id << ", ";
	// 	}
	// 	cout << "}" << endl;
	// }
	std::pair<vector<double>, vector<unordered_map<int, int>>> hypothesisPruneResultPair = hypothesis_prune(new_weights, new_ghyps, weight_threshold);
	new_weights = hypothesisPruneResultPair.first;
	new_ghyps = hypothesisPruneResultPair.second;
	cout << "new_weights2: ";
	for (const auto& weight : new_weights) {
		cout << weight << " ";
	}
	cout << endl;
	cout << "weight_threshold2: " << weight_threshold <<", new_ghyps size = " << new_ghyps.size() << endl;

	// cout << "new_ghyps2: " << endl;
	// for (const auto& ghyp : new_ghyps) {
	// 	cout << "{ ";
	// 	for (const auto& [track_id, hypo_id] : ghyp) {
	// 		cout << track_id << ": " << hypo_id << ", ";
	// 	}
	// 	cout << "}" << endl;
	// }

	cout<<"假设剪枝: new_weights size = " << new_weights.size() <<", new_ghyps size = " << new_ghyps.size()<<endl;
	new_weights = normalize_log_sum(new_weights);
	// 捕获假设并归一化权重(保留M个权重最大的航迹假设)
	std::pair<vector<double>, vector<unordered_map<int, int>>> hypothesisCapResult = hypothesis_cap(new_weights, new_ghyps, max_nof_hyps);
	new_weights = hypothesisCapResult.first;
	new_ghyps = hypothesisCapResult.second;
	cout<<"捕获假设: new_weights size = " << new_weights.size() <<", new_ghyps size = " << new_ghyps.size()<<endl;
	new_weights = normalize_log_sum(new_weights);
	// 1-scan-pruning:k时刻出现的不确定性在k+1时刻解决
	#pragma omp parallel for collapse(2) // 合并两层循环
	for (const auto& [trid, track] : tracks_) {
		vector<int> selected_lids;
		for (const auto& ghyp : new_ghyps) {
			if (ghyp.count(trid)) {
				selected_lids.emplace_back(ghyp.at(trid));
			}
		}
		track->select(selected_lids);
	}

	// TODO 移除重复全局假设
	gweights_ = new_weights;
	ghyps_ = new_ghyps;
	cout<<"after update: gweights_ size = " << gweights_.size() <<", ghyps_ size = " << ghyps_.size()<<endl;
}

/**
 * @description:
 * 初始化参数：计算杂波强度、新目标强度及LLR0值
遍历检测列表：为每个检测创建目标模型、假设对象和跟踪对象
存储跟踪信息：将跟踪ID与假设ID映射关系存入new_ghyp
计算初始成本：总成本为所有跟踪的LLR0负值之和
返回跟踪映射和总成本
 * @return {*}
 */
pair<unordered_map<int, int>, double> ObjectFusionTracking::create_track_trees(vector<common_msgs::sensorobject>& detections,
							const Volume& volume, TargetPosition_CV2D& targetmodel, double t_now) {
    double intensity_c = volume.clutter_intensity(); // 获取环境杂波密度
    double intensity_new = volume.initiation_intensity(); // 获取目标生成概率
    double llr0 = log(intensity_new + EPS) - log(intensity_c + EPS); // 计算目标存在的对数似然比

	// 每个检测生成一个目标，每个目标生成一个假设，每个假设生成一个跟踪目标, {跟踪ID：假设ID}
    unordered_map<int, int> new_ghyp;

	std::string generateIDs = "";
	#pragma omp parallel for
    for (const auto& z : detections) {
        double llhood = log(volume.P_D() + EPS) - log(intensity_c + EPS); // 计算似然值
        std::shared_ptr<TargetPosition_CV2D> target = targetmodel.from_one_detection(z, t_now); // 生成目标位置预测模型
        shared_ptr<LocalHypothesis> new_lhyp = make_shared<LocalHypothesis>(target, llr0, llhood); // 创建LocalHypothesis实例，参数包含目标模型、初始LLR0和似然值
        shared_ptr<Track> new_track = make_shared<Track>(new_lhyp); // 生成跟踪对象
        tracks_[new_track->id()] = new_track;
        new_ghyp[new_track->id()] = new_lhyp->id();

		std::string idstr = std::to_string(new_track->id()) + " ";
		generateIDs += idstr;
    }

	std::cout<<"create_track_trees: " << generateIDs << endl;

    double total_init_cost = new_ghyp.size() * -llr0; // 计算初始化总成本：总成本 = 跟踪数量 × (-LLR0)
    return pair<unordered_map<int, int>, double>(std::move(new_ghyp), total_init_cost);
}

double ObjectFusionTracking::_unnormalized_weight(const unordered_map<int, int>& ghyp) {
    double weight = 0.0;
	#pragma omp parallel for
    for (const auto& [trid, lid] : ghyp) {
        // cout<<"trid = "  << trid <<endl;
        weight += tracks_[trid]->log_likelihood(lid);
    }
    // cout<<"_unnormalized_weight: weight = "  << weight <<endl;
    return weight;
}

/*
* 用于从全局假设中移除无效的局部假设，并更新权重。具体功能如下：

获取所有跟踪对象的无效局部假设。
遍历输入的权重和全局假设，检查每个全局假设中的局部假设是否有效。
如果某个局部假设无效，则调整权重；否则保留该全局假设。
返回更新后的权重和全局假设。
* */
std::pair<vector<double>,vector<unordered_map<int, int>>> ObjectFusionTracking::prune_dead(const vector<double>& weights,
																		const vector<unordered_map<int, int>>& ghyps) {
	cout<<"ObjectFusionTracking::prune_dead  ghyps size = " << ghyps.size() <<", weights size = " << weights.size()<<endl;
    std::unordered_map<int, std::vector<int>> dead_lhpys; // {跟踪ID：Vector<无效局部假设ID>}
	#pragma omp parallel for
    for(const auto& [trid,track]: tracks_){
        dead_lhpys[trid] = track->dead_local_hyps();
		// cout<<"prune_dead: tid = " << trid <<", dead size = " << dead_lhpys[trid].size() <<endl;
    }

	cout<<"dead_lhpys size = " << dead_lhpys.size() <<endl;


    vector<double> pruned_weights;
    vector<unordered_map<int, int>> pruned_ghyps;

    for (size_t i = 0; i < weights.size(); ++i) {
        double weight = weights[i];
        const auto& ghyp = ghyps[i];
        double w_diff = 0.0;
        unordered_map<int, int> pruned_ghyp;

		// std::cout << "prune_dead: Processing weight = " << weight << std::endl;
		// std::cout << "prune_dead: ghyp size = " << ghyp.size() << std::endl;
		#pragma omp parallel for
        for (const auto& [trid, lid] : ghyp) {
			// cout<<"prune_dead: tid = " << trid <<", lid = " << lid <<endl;
            if(std::find(dead_lhpys[trid].begin(), dead_lhpys[trid].end(), lid) != dead_lhpys[trid].end()){
                w_diff += tracks_[trid]->log_likelihood(lid);
				// std::cout << "prune_dead: lid " << lid << " in dead_lhpys[" << trid << "], w_diff += " << tracks_[trid]->log_likelihood(lid) << std::endl;
            } else {
                pruned_ghyp[trid] = lid;
				// std::cout << "prune_dead: lid " << lid << " not in dead_lhpys[" << trid << "], adding to pruned_ghyp" << std::endl;
            }
        }

        if (!pruned_ghyp.empty()) {
            pruned_weights.emplace_back(weights[i] - w_diff);
            pruned_ghyps.emplace_back(pruned_ghyp);
			// std::cout << "prune_dead: Added pruned_ghyp: \n";
			// for (const auto& [trid, lid] : pruned_ghyp) {
			// 	std::cout << "trid: " << trid << ", lid: " << lid << " ";
			// }
			// std::cout << "with weight: " << weight - w_diff << std::endl;
        }
		// else {
		// 	std::cout << "prune_dead: pruned_ghyp is empty, skipping" << std::endl;
		// }
    }
	cout<<"prune_dead: pruned_weights size = " << pruned_weights.size() <<", pruned_ghyps size = " << pruned_ghyps.size()<<endl;
	cout<<"prune_dead: weights size = " << weights.size() <<", ghyps size = " << ghyps.size()<<endl;

    assert(pruned_ghyps.size() == pruned_weights.size());
    return std::make_pair(std::move(pruned_weights) ,std::move(pruned_ghyps));
}


std::pair<vector<double>, vector<unordered_map<int, int>>> ObjectFusionTracking::hypothesis_prune(const vector<double>& weights,
																		const vector<unordered_map<int, int>>& hypotheses, double threshold) {
    vector<double> pruned_weights;
    vector<unordered_map<int, int>> pruned_hypotheses;
	#pragma omp parallel for
    for (size_t i = 0; i < weights.size(); ++i) {
        if (weights[i] >= threshold) {
			cout<<"hypothesis_prune: i = " << i  <<", 保留权值： weights[i] = " << weights[i]
				<<", threshold = " << threshold <<endl;
            pruned_weights.emplace_back(weights[i]);
            pruned_hypotheses.emplace_back(hypotheses[i]);
        }
		else{
			cout<<"hypothesis_prune: i = " << i  <<", weights[i] = " << weights[i]
				<<", threshold = " << threshold <<endl;
		}
    }

    return std::make_pair(std::move(pruned_weights), std::move(pruned_hypotheses));
}


std::pair<vector<double>, vector<unordered_map<int, int>>> ObjectFusionTracking::hypothesis_cap(const vector<double>& weights,
																	const vector<unordered_map<int, int>>& hypotheses, int M) {
    if (weights.size() <= M) {
		cout<<"hypothesis_cap: weights.size() = " << weights.size()  <<", M = " << M <<endl;
        return std::make_pair(weights,hypotheses);
    }
    // TODO  排序是否改变原有索引
    vector<pair<size_t, double>> weight_indices;
	#pragma omp parallel for
    for (size_t i = 0; i < weights.size(); ++i) {
        weight_indices.emplace_back(i, weights[i]);
    }
	// 按权重降序排序
    std::sort(weight_indices.begin(), weight_indices.end(),
             [](const auto& a, const auto& b) { return a.second > b.second; });

	// 提取前 M 个最大权重及其对应的假设
    vector<double> pruned_weights;
    vector<unordered_map<int, int>> pruned_hypotheses;
	#pragma omp parallel for
    for (size_t i = 0; i < M; ++i) {
		auto idx = weight_indices[i].first;
		cout<<"hypothesis_cap: i = " << i  <<", 保留权值： weights[idx] = " << weights[idx] <<endl;
        pruned_weights.emplace_back(weights[idx]);
        pruned_hypotheses.emplace_back(hypotheses[idx]);

    }

    return std::make_pair(std::move(pruned_weights), std::move(pruned_hypotheses));
}

/*终止不在全局假设中的轨迹。具体步骤如下：

遍历全局假设，收集所有轨迹ID。
遍历所有轨迹，删除不在全局假设中的轨迹。
* */
void ObjectFusionTracking::terminate_tracks() {
    unordered_set<int> trids_in_ghyps;
	cout<<"ObjectFusionTracking::terminate_tracks, ghyps_.size() = " << ghyps_.size() << endl;
	#pragma omp parallel for collapse(2) // 合并两层循环
    for (const auto& ghyp : ghyps_) {
        for (const auto& [trid, _] : ghyp) {
			// cout<<"ObjectFusionTracking::terminate_tracks, trid = " << trid << endl;
            trids_in_ghyps.insert(trid);
        }
    }
	cout<<"ObjectFusionTracking::terminate_tracks, tracks_.size() = " << tracks_.size() << endl;
    for (auto it = tracks_.begin(); it != tracks_.end(); ) {
        if (trids_in_ghyps.count(it->first) == 0) {
			// cout<<"ObjectFusionTracking::terminate_tracks, erase trid = " << it->first << endl;
            it = tracks_.erase(it);
        } else {
            ++it;
        }
    }
}

/**
 * @description: 根据全局假设权重选择最优假设。
遍历假设中的跟踪项，筛选是否包含在确认列表（仅当only_confirmed为真时）。
收集符合条件的跟踪估计并返回结果
 * @param {bool} only_confirmed
 * @return {*}
 */
unordered_map<int,shared_ptr<Density>>  ObjectFusionTracking::estimates(bool only_confirmed) {
    cout<<"Tracker::estimates()"<<endl;
    unordered_map<int, shared_ptr<Density>>  result;
    if (!gweights_.empty()) {
		std::string weights_str = "gweights_: ";
		for(const auto& weight: gweights_){
			weights_str = weights_str + ", " + std::to_string(weight);
		}
		cout <<weights_str << endl;

        int index_max = max_element(gweights_.begin(), gweights_.end()) - gweights_.begin();
		cout <<"find index_max = " << index_max <<",weight = " << gweights_[index_max] << endl;
        const std::unordered_map<int, int>& best_global_hypothesis = ghyps_[index_max];
		#pragma omp parallel for collapse
        for (const auto& [trid, lid] : best_global_hypothesis) {
			// cout <<"trid = " << trid <<", lid = " << lid << endl;
            std::shared_ptr<Track>& track = tracks_[trid];
			bool include_estimate = !only_confirmed ||
									find(track->confirmed_local_hyps().begin(),
										track->confirmed_local_hyps().end(),
										lid) != track->confirmed_local_hyps().end();

			if (include_estimate) {
				result[trid] = track->estimate(lid);
			}
        }

    }

	// // 计算最近连续跟踪次数
    // int recent_hits = 0;
    // size_t start_idx = max(0, static_cast<int>(_hit_history.size()) - min_hits_for_confirmation);
    // for (size_t i = start_idx; i < _hit_history.size(); ++i) {
    //     if (_hit_history[i]) recent_hits++;
    // }
    return result;
}

void ObjectFusionTracking::mergeCloseTargets(unordered_map<int,shared_ptr<Density>>& estimations) {
	// 第一步：合并相近的跟踪目标（保留ID小的目标）
	m_pLogger->info("开始合并相近目标，当前目标数量: {}", estimations.size());

	// 创建一个映射，用于存储被合并的目标ID到保留目标ID的映射关系
	unordered_map<int, int> mergedToKeepTrackID;

    for (auto it1 = estimations.begin(); it1 != estimations.end(); ) {
        int trackID1 = it1->first;
        Eigen::VectorXd object1 = it1->second->x();
        double yaw1 = m_common.normalizeAngle(atan2(object1[2], object1[3]));

        auto it2 = std::next(it1);
        bool merged = false;

        while (it2 != estimations.end()) {
            int trackID2 = it2->first;
            Eigen::VectorXd object2 = it2->second->x();
			double yaw2 = m_common.normalizeAngle(atan2(object2[2], object2[3]));

            double distance = sqrt(pow(object1[0] - object2[0], 2) +
                                 pow(object1[1] - object2[1], 2));
			// 合并前检查运动方向一致性,角度差异过大时不合并
			double angleRadDiff1 = m_common.normalizeAngle(yaw1 - yaw2);
			double angleRadDiff2 = m_common.normalizeAngle(m_common.normalizeAngle(yaw1 + M_PI) - yaw2);
			bool isSimilarAngle1 = (angleRadDiff1 > M_PI/12.0) ? false : true;
			bool isSimilarAngle2 = (angleRadDiff2 > M_PI/12.0) ? false : true;
			bool isSimilarAngle = isSimilarAngle1 || isSimilarAngle2;

			// 计算合并阈值，使用目标长度或至少5米
			double length1 = static_cast<double>(it1->second->xObject_.length);
			double length2 = static_cast<double>(it2->second->xObject_.length);
			double mergeThreshold = std::max(5.0, std::max(length1, length2));

			// 检查目标类型是否相同
			bool isSameClass = (it1->second->xObject_.classification == it2->second->xObject_.classification);

			// 检查速度向量是否相似
			double vx1 = object1[2], vy1 = object1[3];
			double vx2 = object2[2], vy2 = object2[3];
			double speedDiff = sqrt(pow(vx1 - vx2, 2) + pow(vy1 - vy2, 2));
			bool isSimilarSpeed = (speedDiff < 5.0); // 速度差异小于5m/s


			// 记录合并条件的详细信息
			// m_pLogger->debug("目标对比: ID1={}, ID2={}, 距离={:.2f}, 阈值={:.2f}, 角度差={:.2f}度, 速度差={:.2f}, 类型相同={}",
			// 	trackID1, trackID2, distance, mergeThreshold, std::min(angleRadDiff1, angleRadDiff2) * 180 / M_PI,
			// 	speedDiff, isSameClass ? "是" : "否");

            // 更新合并条件，增加对航向角完全相同的特殊处理  && isSameClass mergeThreshold
            if ((distance < 2 && isSimilarAngle)) { // 如果距离小于阈值且角度差小于阈值且类别相同，则合并
                // 保留ID小的目标
                if (trackID1 < trackID2) {
					m_pLogger->info("合并目标: 保留ID={}, 删除ID={}, 距离={:.2f}, 阈值={:.2f}, 角度差={:.2f}度, 速度差={:.2f}, 类型相同={}",
						trackID1, trackID2, distance, mergeThreshold, std::min(angleRadDiff1, angleRadDiff2) * 180 / M_PI,
						speedDiff, isSameClass ? "是" : "否");

					// 记录被合并的目标ID映射到保留的目标ID
					mergedToKeepTrackID[trackID2] = trackID1;
                    it2 = estimations.erase(it2);
                } else {
					m_pLogger->info("合并目标: 保留ID={}, 删除ID={}, 距离={:.2f}, 阈值={:.2f}, 角度差={:.2f}度, 速度差={:.2f}, 类型相同={}",
						trackID2, trackID1, distance, mergeThreshold, std::min(angleRadDiff1, angleRadDiff2) * 180 / M_PI,
						speedDiff, isSameClass ? "是" : "否");

					// 记录被合并的目标ID映射到保留的目标ID
					mergedToKeepTrackID[trackID1] = trackID2;
                    it1 = estimations.erase(it1);
                    merged = true;
                    break;  // 跳出内层循环，继续外层循环
                }
            } else {
                ++it2;
            }
        }

        if (!merged && it2 == estimations.end()) {
            ++it1;  // 只有当没有合并发生时才递增it1
        }
    }

    // 第二步：处理全局假设中的多余假设
    if (!mergedToKeepTrackID.empty()) {
        m_pLogger->info("开始处理全局假设，当前假设数量: {}", ghyps_.size());

        // 遍历所有全局假设，更新假设中的目标ID
        for (auto& ghyp : ghyps_) {
            // 创建一个临时映射，用于存储需要在ghyp中更新的项
            unordered_map<int, int> updates;
            unordered_set<int> removals;

            // 检查每个全局假设中是否包含被合并的目标ID
            for (auto it = mergedToKeepTrackID.begin(); it != mergedToKeepTrackID.end(); ++it) {
                int mergedID = it->first;  // 被合并的ID
                int keepID = it->second;   // 保留的ID

                // 如果全局假设中同时包含了被合并的ID和保留的ID
                if (ghyp.find(mergedID) != ghyp.end() && ghyp.find(keepID) != ghyp.end()) {
                    // 标记要移除被合并的ID
                    m_pLogger->info("从全局假设中移除多余假设: 目标ID={}, 假设ID={}", mergedID, ghyp[mergedID]);
                    removals.insert(mergedID);
                }
                // 如果全局假设中只包含被合并的ID
                else if (ghyp.find(mergedID) != ghyp.end()) {
                    // 将被合并ID的假设转移给保留的ID
                    int hypothesisID = ghyp[mergedID];

                    // 如果保留的ID不在假设中，添加它
                    if (ghyp.find(keepID) == ghyp.end()) {
                        m_pLogger->info("将假设从目标ID={}转移到目标ID={}, 假设ID={}", mergedID, keepID, hypothesisID);
                        updates[keepID] = hypothesisID;
                    }

                    // 标记要移除被合并的ID
                    removals.insert(mergedID);
                }
            }

            // 应用更新
            for (const auto& update : updates) {
                ghyp[update.first] = update.second;
            }

            // 应用移除
            for (const auto& removal : removals) {
                ghyp.erase(removal);
            }
        }

        // 第三步：处理tracks_中的多余假设
        for (auto it = mergedToKeepTrackID.begin(); it != mergedToKeepTrackID.end(); ++it) {
            int mergedID = it->first;  // 被合并的ID
            int keepID = it->second;   // 保留的ID

            // 检查是否存在于tracks_中
            if (tracks_.find(mergedID) != tracks_.end() && tracks_.find(keepID) != tracks_.end()) {
                // 获取被合并目标的所有局部假设
                auto mergedTrack = tracks_[mergedID];
                auto keepTrack = tracks_[keepID];

                // 将被合并目标的局部假设复制到保留目标
                for (const auto& lhyp : mergedTrack->lhyps_) {
                    // 如果保留目标没有这个局部假设，则添加
                    if (keepTrack->lhyps_.find(lhyp.first) == keepTrack->lhyps_.end()) {
                        m_pLogger->info("将局部假设从目标ID={}复制到目标ID={}, 假设ID={}", mergedID, keepID, lhyp.first);
                        keepTrack->lhyps_[lhyp.first] = lhyp.second;
                    }
                }

                // 现在可以安全地删除被合并的目标
                m_pLogger->info("从tracks_中移除多余目标: ID={}", mergedID);
                tracks_.erase(mergedID);
            }
        }

        // 第四步：为每个跟踪目标保留最佳的假设，删除其他不必要的假设
        for (auto& trackPair : tracks_) {
            int trackID = trackPair.first;
            auto& track = trackPair.second;

            // 如果目标有多个假设
            if (track->lhyps_.size() > 1) {
                // 找到最佳假设（具有最高对数似然比的假设）
                int bestLhypID = -1;
                double bestLLR = -std::numeric_limits<double>::infinity();

                for (const auto& lhypPair : track->lhyps_) {
                    int lhypID = lhypPair.first;
                    auto& lhyp = lhypPair.second;

                    double llr = lhyp->log_likelihood_ratio();
                    if (llr > bestLLR) {
                        bestLLR = llr;
                        bestLhypID = lhypID;
                    }
                }

                // 如果找到了最佳假设
                if (bestLhypID >= 0) {
                    // 创建一个向量，包含要保留的假设ID
                    std::vector<int> keepLhypIDs = {bestLhypID};

                    // 记录要删除的假设数量
                    int deleteCount = track->lhyps_.size() - keepLhypIDs.size();
                    if (deleteCount > 0) {
                        m_pLogger->info("为目标ID={}保留最佳假设ID={}, 删除{}个其他假设", trackID, bestLhypID, deleteCount);

                        // 选择要保留的假设
                        track->select(keepLhypIDs);

                        // 更新全局假设，确保它们引用正确的局部假设
                        for (auto& ghyp : ghyps_) {
                            if (ghyp.find(trackID) != ghyp.end()) {
                                ghyp[trackID] = bestLhypID;
                            }
                        }
                    }
                }
            }
        }
    }

    m_pLogger->info("合并完成，剩余目标数量: {}", estimations.size());
}

/**
 * @description: 在rviz中显示跟踪目标
 * @param {unordered_map<int, shared_ptr<Density>>&} estimations 跟踪目标的估计
 * @param {string&} frameIDInfo 坐标系ID
 * @return {*}
 */
void ObjectFusionTracking::showTrackedObjectsInRviz(const unordered_map<int, shared_ptr<Density>>& estimations) {
    visualization_msgs::MarkerArray trackedObjectsMarkerArray;

    visualization_msgs::Marker marker;
    marker.action = visualization_msgs::Marker::DELETEALL;
    trackedObjectsMarkerArray.markers.emplace_back(marker);

    int id = 0;
	#pragma omp parallel for
    for (const auto& [trid, density] : estimations) {
        visualization_msgs::Marker line_list_detect;
        line_list_detect.header.frame_id = "car";
        line_list_detect.header.stamp = ros::Time::now();
        line_list_detect.ns = "tracked_objects";
        line_list_detect.lifetime = ros::Duration();
        line_list_detect.action = visualization_msgs::Marker::ADD;
        line_list_detect.pose.orientation.w = 1.0;
        line_list_detect.id = trid;
        line_list_detect.type = visualization_msgs::Marker::LINE_LIST;
        line_list_detect.scale.x = 0.1;
        line_list_detect.color.a = 1.0;
        line_list_detect.color.r = 1.0;
        line_list_detect.color.g = 0.0;
        line_list_detect.color.b = 0.0;

        double x = density->x()(0);
        double y = density->x()(1);
        double length = density->xObject_.length;
        double width = density->xObject_.width;
		double heading = density->x()(4);
		// 确保航向角在0~2π之间
        heading = fmod(heading + 2*M_PI, 2*M_PI);

		float headingAnticlockwise =  2 * M_PI - heading;
		vector<double> boxInfo = vector<double>{x,y, 1, length, width, 1, headingAnticlockwise};
		vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
		geometry_msgs::Point p0, p1, p2, p3, p4, p5, p6, p7;
		p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
		p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
		p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
		p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
		p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
		p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
		p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
		p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];

		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p1);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p2);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p3);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p0);

        trackedObjectsMarkerArray.markers.emplace_back(line_list_detect);
        visualization_msgs::Marker text_marker;
        text_marker.header.frame_id = "car";
        text_marker.header.stamp = ros::Time::now();
        text_marker.ns = "tracked_objects_text";
        text_marker.lifetime = ros::Duration();
        text_marker.action = visualization_msgs::Marker::ADD;
        text_marker.pose.orientation.w = 1.0;
        text_marker.id = trid + 1000; // 确保ID唯一
        text_marker.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
        text_marker.scale.z = 0.5; // 文本大小
        text_marker.color.a = 1.0;
        text_marker.color.r = 1.0;
        text_marker.color.g = 1.0;
        text_marker.color.b = 1.0;

        // 设置文本内容
        std::stringstream ss;
        ss << "ID: " << trid << "\n"
           << "x: " << x << "\n"
		   << "y: " << y << "\n"
           << "Class: " << std::to_string(density->xObject_.classification) << "\n"
           << "Heading: " << heading * 180 / M_PI << "\n"
		   << "vx: " << density->x()[2] << "\n"
		   << "vy: " << density->x()[3];
		//    << "speed: " << density->x()[2]+ m_selfCarSpeed[0] << ", " << density->x()[3] + m_selfCarSpeed[1];
        text_marker.text = ss.str();		

        // 设置文本位置为目标中心点
        text_marker.pose.position.x = x;
        text_marker.pose.position.y = y;
        text_marker.pose.position.z = 1.0; // 提高文本位置以便于可视化

        trackedObjectsMarkerArray.markers.emplace_back(text_marker);

		// ID 显示
		visualization_msgs::Marker IDMarker;
		IDMarker.header.frame_id = "car";
		IDMarker.header.stamp = ros::Time::now();
		IDMarker.ns = "ID";
		IDMarker.id = trid;
		IDMarker.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
		IDMarker.lifetime = ros::Duration();
		IDMarker.action = visualization_msgs::Marker::ADD;

		IDMarker.pose.orientation.w = 1;
		geometry_msgs::Point  p_temp;
		p_temp.x = (p4.x + p5.x)/2;
		p_temp.y = (p4.y + p5.y)/2;
		p_temp.z =  p4.z;
		IDMarker.pose.position.x = p_temp.x + 0.3*(p5.y-p4.y);
		IDMarker.pose.position.y = p_temp.y + 0.3*(p5.y-p4.y);
		IDMarker.pose.position.z = p_temp.z + 0.4*(p5.z-p4.z);

		IDMarker.scale.z = 2.4;
		IDMarker.color.a = 1;
		IDMarker.color.r = 1;
		IDMarker.color.g =1;
		IDMarker.color.b =0;
		IDMarker.text = std::to_string(trid);
		trackedObjectsMarkerArray.markers.emplace_back(IDMarker);
    }

    pub_MHTTrackedObjectsBBX.publish(trackedObjectsMarkerArray);
}
