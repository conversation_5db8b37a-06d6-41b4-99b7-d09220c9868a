/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 09:46:49
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-14 10:40:01
 * @FilePath: /src/perception/fusiontracking/src/MHT/tracker/localHypothesis.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __LOCAL_HYPOTHESIS_H__
#define __LOCAL_HYPOTHESIS_H__

#include <iostream>
#include <vector>
#include <set>
#include <algorithm>
#include <cmath>
#include <unordered_map>
#include <memory>

#include "./MHT/models/target.h"
#include "./MHT/utils/density.h"
#include "./MHT/cv_target.h"

using namespace std;

// LocalHypothesis 类
class LocalHypothesis {
public:
    static int counter;
    int lid_;
    shared_ptr<TargetPosition_CV2D> target_;

    LocalHypothesis(shared_ptr<TargetPosition_CV2D> target, double LLR, double log_likelihood, double LLR_max = -1);

    int id() const;

    shared_ptr<TargetPosition_CV2D> target() const ;

    shared_ptr<Density> density() const ;
    void predict(double t_now);

    double log_likelihood_ratio() const;

    double log_likelihood() const;

    bool is_dead() const;

    bool is_confirmed() const;

    static shared_ptr<LocalHypothesis> new_from_hit(const LocalHypothesis& self, const common_msgs::sensorobject& z, double hit_llhood, double t_hit);

    static shared_ptr<LocalHypothesis> new_from_miss(const LocalHypothesis& self, double miss_llhood, double t_now);

    string to_string() const;

private:
    
    double LLR_;
    double LLR_max_;
    double log_likelihood_;
    
};

 #endif