/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 11:05:05
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-15 17:11:27
 * @FilePath: /src/perception/fusiontracking/src/MHT/tracker/tracker.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * 
 */

#ifndef __TRACKER_H_
#define __TRACKER_H_

#include <iostream>
#include <vector>
#include <map>
#include <set>
#include <algorithm>
#include <cmath>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <string>
#include "localHypothesis.h"

#include "./MHT/scan_volume.h"
#include "./MHT/cv_target.h"
#include "./MHT/tracker/data_association.h"
#include "common_msgs/sensorobject.h"

using namespace std;



vector<double> normalize_log_sum(vector<double>& items);



// Track 类
class Track {
public:
    static int counter; // 轨迹计数器
    unordered_map<int, shared_ptr<LocalHypothesis>> lhyps_; // {假设ID：假设指针}

    Track(shared_ptr<LocalHypothesis> local_hypothesis);

    int id() const;

    shared_ptr<LocalHypothesis> operator()(int lhyp_id) const;

    void add(shared_ptr<LocalHypothesis> local_hypothesis);

    shared_ptr<Density> estimate(int lhyp_id = -1) const;

    vector<double> log_likelihood_ratio(int lhyp_id = -1) const;

    double log_likelihood(int lhyp_id) const;

    vector<int> dead_local_hyps() const;

    vector<int> confirmed_local_hyps() const;

    void select(const vector<int>& lhyp_ids) ;

    void predict(double t_now);

    unordered_map<int, unordered_map<int, shared_ptr<LocalHypothesis>>>  update(std::vector<common_msgs::sensorobject>& objectsMeasurements, 
                                                            const Volume& volume, double t_now);
private:
    int trid_; // 轨迹ID
};

#endif