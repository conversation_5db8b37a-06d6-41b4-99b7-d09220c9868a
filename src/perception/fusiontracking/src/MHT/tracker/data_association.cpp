/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-11 16:21:00
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-16 11:09:11
 * @FilePath: /src/perception/fusiontracking/src/MHT/tracker/data_association.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "data_association.h"
// #include "generation.h"

// 初始化一个代价矩阵（cost matrix），用于表示轨迹（tracks）与检测（detections）之间的关联代价
CostMatrix::CostMatrix(const std::unordered_map<int, int>& global_hypothesis, 
                        const std::unordered_map<int, std::unordered_map<int, std::unordered_map<int, std::shared_ptr<LocalHypothesis>>>>& track_updates){
    #pragma omp parallel for
    for (const auto& [trid, _] : track_updates) {
        // cout <<"debug: ghyps_.id() = "<< global_hypothesis.at(trid) <<", trid = "<< trid <<endl;
        if (global_hypothesis.find(trid) != global_hypothesis.end()) {
            _included_trids.push_back(trid);
        }
    }
    if (_included_trids.empty()) {
        cout<<"CostMatrix：_included_trids.empty(), return"<<endl;
        _matrix = Eigen::MatrixXd(0, 0);
        return;
    }

    int includedTridsSize = _included_trids.size();
    std::vector<std::vector<double> > c_track_detection;
    c_track_detection.reserve(includedTridsSize); // 预分配内存
    std::vector<double> miss_likelihood;
    miss_likelihood.reserve(includedTridsSize); // 预分配内存
    
    #pragma omp parallel for collapse(2) // 合并两层循环
    for (int trid : _included_trids) {
        const std::unordered_map<int, std::shared_ptr<LocalHypothesis>>& lhyps = track_updates.at(trid).at(global_hypothesis.at(trid));
        std::map<int, std::shared_ptr<LocalHypothesis>> lhyps_map(lhyps.begin(), lhyps.end());
        std::vector<double> likelihoods;
        likelihoods.reserve(lhyps_map.size()); // 预分配内存
        // #pragma omp parallel for
        for (const auto& [detection, lhyp] : lhyps_map) {
            if (detection != MISS) {
                likelihoods.push_back(lhyp ? lhyp->log_likelihood() : LOG_0);
            }
        }

        c_track_detection.emplace_back(std::move(likelihoods));
        miss_likelihood.emplace_back(lhyps_map.at(MISS)->log_likelihood());
    }

    // 初始化 _matrix
    int num_tracks = miss_likelihood.size();
    int num_localhyps = c_track_detection.empty() ? 0 : c_track_detection[0].size();//c_track_detection[0].size();
    _matrix = Eigen::MatrixXd(includedTridsSize, num_localhyps + num_tracks);
    _matrix.setConstant(LOG_0);

    #pragma omp parallel for collapse(2) // 合并两层循环
    for (int i = 0; i < includedTridsSize; ++i) {
        for (int j = 0; j < num_localhyps; ++j) {
            _matrix(i, j) = c_track_detection[i][j];//TODO c_track_detection[i][j]
        }
    }
    #pragma omp parallel for
    for (int i = 0; i < num_tracks; ++i) {
        _matrix(i, num_localhyps + i) = miss_likelihood[i]; // TODO  c_miss(i, i)
    }

    cout << "_matrix构造函数 rows = " << _matrix.rows() << ", cols = " <<  _matrix.cols() << endl;
    // cout << "_matrix构造函数 。。。。。。。。。。。。。。。。。\n" << _matrix << endl;
}

std::vector<int> CostMatrix::tracks() const {
    return _included_trids;
}

// std::vector<std::tuple<double, std::map<int, int>, std::vector<int>>> CostMatrix::solutions(int max_nof_solutions) const {
//     if (_matrix.size() == 0) {
//         cout<<"CostMatrix solutions _matrix.size() == 0, return"<<endl;
//         return {};
//     }

//     lap::Murty murty_solver(_matrix);

//     auto to_trid = [&](int t) -> int {
//         return _included_trids[t];
//     };

//     cout << "before n = " << _matrix.rows() << ", m_plus_n = " <<  _matrix.cols() << endl;
//     // auto results = murty(_matrix, max_nof_solutions);
//     // for (size_t i = 0; i < _matrix.rows(); i++)
//     // {
//     //     for (size_t j = 0; j < _matrix.cols(); j++)
//     //     {
//     //         cout << _matrix(i, j) <<", ";
//     //     }
//     //     cout<<endl;
//     // }
//     // cout<<endl;

//     std::vector<std::tuple<double, std::map<int, int>, std::vector<int>>> solutionResult;
//     for (int i = 0; i < max_nof_solutions; ++i) {
//         auto [is_ok, sum_cost, track_to_det] = murty_solver.draw_tuple();

//         if (!is_ok) {
//             break;
//         }

//         int n = _matrix.rows();;
//         int m_plus_n = _matrix.cols();

//         std::map<int, int> assignments;
//         for (int track_index = 0; track_index < track_to_det.rows(); ++track_index) {
//             int det_index = track_to_det[track_index];
//             assignments[to_trid(track_index)] = (det_index < m_plus_n - n) ? det_index : MISS;
//         }

//         std::vector<int> track_to_det_vector(track_to_det.data(), track_to_det.data() + track_to_det.size());
//         std::vector<int> unassigned_detections;
//         for (int det_index = 0; det_index < m_plus_n - n; ++det_index) {
//             if (std::find(track_to_det_vector.begin(), track_to_det_vector.end(), det_index) == track_to_det_vector.end()) {
//                 unassigned_detections.push_back(det_index);
//             }
//         }

//         solutionResult.emplace_back(sum_cost, assignments, unassigned_detections);
//     }

//     // for(const auto& [_, assignment, unassigned_detections]: solutionResult){
//     //     for(const auto& itmap: assignment){
//     //         cout <<"solution map: tid = " << itmap.first << ", lid = " << itmap.second << endl;
//     //     }
//     //     cout <<".........................."  << endl;
//     // }
//     cout <<"Finnish solution map。。。。。。。。。。"  << endl;
//     return solutionResult;
// }


std::vector<std::tuple<double, std::unordered_map<int, int>, std::vector<int>>> 
CostMatrix::solutions(int max_nof_solutions) const 
{
    if (_matrix.size() == 0) {
        std::cout << "CostMatrix solutions _matrix.size() == 0, return" << std::endl;
        return {};
    }

    lap::Murty murty_solver(_matrix);
    SolutionIterator it(murty_solver, _included_trids, max_nof_solutions, _matrix);
    
    std::vector<std::tuple<double, std::unordered_map<int, int>, std::vector<int>>> results;
    double sum_cost;
    std::unordered_map<int, int> assignments;
    std::vector<int> unassigned;
    cout << "solutions: begin while...\n";
    while (it.next(sum_cost, assignments, unassigned)) {
        results.emplace_back(sum_cost, assignments, unassigned);
        cout << "Added solution, current results size: " << results.size() << endl;
    }
    cout << "solutions: finish\n";
    cout.flush();  // 立即刷新日志
    return std::move(results);
}