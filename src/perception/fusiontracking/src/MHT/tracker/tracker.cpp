#include "tracker.h"
using namespace std;

int Track::counter = 1;
// 归一化函数
vector<double> normalize_log_sum(vector<double>& items) {
    double log_sum = 0;
    if (items.empty()) {
        return items;// 0 == std::nullopt
    } else if (items.size() == 1) {
        log_sum = items[0];
        return {items[0] - log_sum}; // 返回新数组，不修改输入
    } else {
        auto max_it = max_element(items.begin(), items.end());
        double max_log_w = *max_it;
        // Calculate log_sum
        double sum_exp = 0.0;
        #pragma omp parallel for
        for (auto it = items.begin(); it != items.end(); ++it) {
            if (it != max_it) {
                sum_exp += exp(*it - max_log_w);
            }
        }
        log_sum = max_log_w + log(1.0 + sum_exp);
        // Normalize items
        vector<double> normalized_items;
        #pragma omp parallel for
        for (double item : items) {
            normalized_items.emplace_back(item - log_sum);
        }

        return std::move(normalized_items);
    }
}


Track::Track(shared_ptr<LocalHypothesis> local_hypothesis)
    : lhyps_({{local_hypothesis->id(), local_hypothesis}}), trid_(counter++) {}

int Track::id() const { return trid_; }

shared_ptr<LocalHypothesis> Track::operator()(int lhyp_id) const {
    auto it = lhyps_.find(lhyp_id);
    return it != lhyps_.end() ? it->second : nullptr;
}

void Track::add(shared_ptr<LocalHypothesis> local_hypothesis) {
    lhyps_[local_hypothesis->id()] = local_hypothesis;
}

shared_ptr<Density> Track::estimate(int lhyp_id) const {
    // TODO 补充None的判断
    if(lhyp_id < 0){
        cerr <<"NOTE:::::::Track::estimate lhyp_id < 0"<< endl;
    }
    shared_ptr<Density> densities = lhyps_.at(lhyp_id)->density();
    return std::move(densities);
}

vector<double> Track::log_likelihood_ratio(int lhyp_id) const {
    if (lhyp_id < 0) {
        vector<double> llrs;
        #pragma omp parallel for
        for (const auto& pair : lhyps_) {
            llrs.emplace_back(pair.second->log_likelihood_ratio());
        }
        return std::move(llrs);
    } else {
        return {lhyps_.at(lhyp_id)->log_likelihood_ratio()};
    }
}

double Track::log_likelihood(int lhyp_id) const {
    return lhyps_.at(lhyp_id)->log_likelihood();
}

vector<int> Track::dead_local_hyps() const {
    vector<int> dead_ids;
    #pragma omp parallel for
    for (const auto& [lid, lhyp] : lhyps_) {
        if (lhyp->is_dead()) {
            dead_ids.emplace_back(lid);
        }
    }
    // cout <<"Track::dead_local_hyps() dead hyps size = " << dead_ids.size() << endl;
    return std::move(dead_ids);
}

/**
 * @description: 遍历Track的lhyps_容器，收集所有已确认的LocalHypothesis的ID并返回。
 * * 具体流程：1. 遍历lhyps_中每个键值对 
 * * 2. 检查LocalHypothesis对象是否被确认 
 * * 3. 若确认则将对应ID存入结果列表 
 * * 4. 返回最终ID列表
 * @return {*}
 */
vector<int> Track::confirmed_local_hyps() const {
    vector<int> confirmed_ids;
    #pragma omp parallel for
    for (const auto& pair : lhyps_) {
        if (pair.second->is_confirmed()) { // 调用 LocalHypothesis::is_confirmed()
            confirmed_ids.emplace_back(pair.first);
        }
    }
    return std::move(confirmed_ids);
}

void Track::select(const vector<int>& lhyp_ids) {
    unordered_map<int, shared_ptr<LocalHypothesis>> new_lhyps;
    #pragma omp parallel for
    for (auto& [lid, lhpy] : lhyps_) {
        if(std::find(lhyp_ids.begin(), lhyp_ids.end(), lid) != lhyp_ids.end()) {
            new_lhyps[lid] = lhpy;
        }
    }
    lhyps_ = std::move(new_lhyps);
}
/**
 * @description: 调用LocalHypothesis::predict()进行预测
 * @param {double} t_now
 * @return {*}
 */
// void Track::predict(double t_now) {
//     for (auto& pair : lhyps_) {
//         pair.second->predict(t_now);
//     }
// }
/**
 * @description: 更新目标跟踪中的局部假设,并返回更新后的局部假设
    1初始化：创建一个空字典 new_lhyps 用于存储新的局部假设。
    2遍历现有假设：遍历当前的局部假设 self._lhyps。
    3目标预测：获取每个假设对应的目标。
    4量测门控：通过目标的 gating 方法筛选出在椭圆门内的量测及其索引。
    5似然计算：计算在椭圆门内的量测的预测似然值。
    6似然更新：初始化一个全为 LOG_0 的数组 lhood，并更新在椭圆门内的量测的似然值。
    7生成新假设：根据量测是否在椭圆门内，生成新的局部假设并存储在 new_lhyps 中。
    8处理未检测到量测的情况：计算未检测到量测时的似然值，并生成相应的局部假设。
    9返回结果：返回更新后的局部假设字典 new_lhyps。
 * @param {Volume&} volume
 * @param {double} t_now
 * @return {*}
 */
unordered_map<int, unordered_map<int, shared_ptr<LocalHypothesis>>> Track::update(std::vector<common_msgs::sensorobject>& objectsMeasurements, 
                                                                const Volume& volume, double t_now) {
    // cout <<"Track::update start"<< endl;
    unordered_map<int, unordered_map<int, shared_ptr<LocalHypothesis>>> new_lhyps;
    new_lhyps.reserve(lhyps_.size() + 1);
    double P_G = 1.0; //未有量测时:\Delta L(k) = ln(1-P_D)
    bool isDebug = false;
    double miss_llhood = log(1.0 - volume.P_D() * P_G + EPS);
    double likelihood_ratio = log(volume.P_D() + EPS) - log(volume.clutter_intensity() + EPS);

    // 遍历当前的所有局部假设
    for (const auto& pair : lhyps_) {
        int lid = pair.first;
        const shared_ptr<LocalHypothesis>& lhyp = pair.second;
        const shared_ptr<TargetPosition_CV2D>&  target = lhyp->target();
        // 得到在椭圆门内的量测和量测索引
        vector<int> in_gate_indices = target->gating(objectsMeasurements); //TODO 检查in_gate_indices 与 objectsMeasurements对应性
        int z_in_gate_size = in_gate_indices.size();
        // cout<<"z_in_gate_size = " << z_in_gate_size << endl;
                
        // 计算在椭圆门内的量测的预测似然值
        vector<double> lh(z_in_gate_size);
        #pragma omp parallel for
        for (size_t i = 0; i < z_in_gate_size; ++i) {
            lh[i] = target->predicted_likelihood(objectsMeasurements[in_gate_indices[i]]);
            // cout<<"z_in_gate_size: i = " << i << ", in_gate_indices[i] = " << in_gate_indices[i] << ", lh[i] = " << lh[i]<< endl;
            // cout<<"z_in_gate_size: x = " << objectsMeasurements[in_gate_indices[i]].x 
            //     << ", y = " << objectsMeasurements[in_gate_indices[i]].y << endl;
        }
        
        if(isDebug){
            std::string inGateINdicesStr = "";
            #pragma omp parallel for
            for (size_t i = 0; i < in_gate_indices.size(); ++i) {
                std::string indicestr = std::to_string(in_gate_indices[i]) + " ";
                inGateINdicesStr += indicestr;
            }

            if(!in_gate_indices.empty()){
                cout<< "Track::update: in_gate_indices.size()= " << in_gate_indices.size() << endl;
                #pragma omp parallel for
                for (auto& index : in_gate_indices) {
                    cout<< "z_in_gate: 检测id = " << objectsMeasurements[index].id 
                            <<", x= " << objectsMeasurements[index].x 
                            <<", y= " << objectsMeasurements[index].y 
                            << ", 跟踪位置：" << target->_density->x().transpose()
                            << ", 跟踪类别：" << (int)target->_density->xObject_.classification << endl;
                }
                cout<< "Track::update: in_gate_indices.size()= " << in_gate_indices.size() << endl;
                cout <<"Track::update: lid= " << lid <<", inGateINdicesStr: " << inGateINdicesStr << endl;
            }
        }
        
        // 更新在椭圆门内的量测的似然值
        // 初始化似然数组：lhood向量初始化为大小为objectsMeasurements.size()，所有元素值为LOG_0（可能代表对数形式的零值，避免数值下溢）。
        // 遍历门限内索引：对通过门限筛选的索引列表in_gate_indices进行迭代（i为循环变量）。
        // 计算似然值：对每个索引in_gate_indices[i]，通过以下公式更新lhood值：
        // lh[i]（原始似然值，目标模型对测量z_in_gate[i]的预测似然） + log(volume.P_D() + EPS)（检测概率对数，EPS为防零小量）
        // volume.P_D()：目标检测概率，表示目标存在的可能性。z_in_gate[i].classification是检测置信度（如分类可靠性），应属于目标模型的预测似然（lh[i]）的一部分，而非直接替代检测概率；
        // volume.clutter_intensity()：杂波强度，表示误检噪声的概率。
        // 最终结果减去杂波强度的对数，实现似然值的归一化或对比。
        vector<double> lhood(objectsMeasurements.size(), LOG_0);
        #pragma omp parallel for
        for (size_t i = 0; i < in_gate_indices.size(); ++i) {
            // 最终似然值为目标存在且匹配测量的概率与杂波产生该测量的概率的对数比值，用于后续选择最优假设。
            lhood[in_gate_indices[i]] = lh[i] + likelihood_ratio;
        }

        // cout<<"Track::update...........\n";
        // cout <<"Track::update: lid= " << lid <<", lhyp id= " << lhyp->id()<<", objectsMeasurements.size()= " << objectsMeasurements.size() << endl;
        // 构造新的 local hypotheses
        //  根据量测是否在椭圆门内，生成新的局部假设并存储在 new_lhyps 中
        // new_lhyps[lid] = map<int, shared_ptr<LocalHypothesis>>();
        #pragma omp parallel for
        for (size_t j = 0; j < objectsMeasurements.size(); ++j) {
            if (find(in_gate_indices.begin(), in_gate_indices.end(), j) != in_gate_indices.end()) {
                new_lhyps[lid][j] = LocalHypothesis::new_from_hit(*lhyp, objectsMeasurements[j], lhood[j], t_now);
                // cout<<"match info: j = " << j << ", in_gate_indices[j] = " << in_gate_indices[j] << endl;
            }
            else {
                new_lhyps[lid][j] = nullptr; // 为每个检测索引j标记未命中的局部假设，表示该检测未被关联到当前目标。局部MISS（针对每个j）用于保留未关联检测的分支路径。
                // cout<<"objectsMeasurements未匹配检测1 info: x = " << objectsMeasurements[j].x << ", y = " << objectsMeasurements[j].y 
                //     <<", j = " << j << ", objectsMeasurements.size() = " << objectsMeasurements.size()<< endl;
            }
        }
        // 计算未检测到量测时的似然值，并生成相应的局部假设 创建全局MISS假设，表示目标未被检测到（即使存在其他检测），其概率基于未检测概率(1-P_D)。
        // 全局MISS（独立于j）表示目标完全未被检测到的情况，需单独计算概率权重。
        new_lhyps[lid][MISS] = LocalHypothesis::new_from_miss(*lhyp, miss_llhood, t_now);
        cout<<"................\n";
    }


    
    if(isDebug){
        cout<<"\n new_lhyps size = " << new_lhyps.size() << ", objectsMeasurements size = " << objectsMeasurements.size() << endl;
        #pragma omp parallel for collapse(2) // 合并两层循环
        for (auto& itmap : new_lhyps){
            unordered_map<int, shared_ptr<LocalHypothesis>> lhpymap = itmap.second;
            cout<<"\n lhpymap size = " << lhpymap.size() << ", objectsMeasurements size = " << objectsMeasurements.size()<< endl;
            for (auto& it : lhpymap){
                if(it.second){
                    cout <<"Track::update: lid= " << itmap.first  << endl;
                    cout <<", detectionIndex = " << it.first << endl;
                    cout <<", lid2 = " << it.second->lid_ << endl;
                }
                else{
                    cout<<"objectsMeasurements未匹配检测2 info: x = " << objectsMeasurements[it.first].x << ", y = " << objectsMeasurements[it.first].y << endl;
                }
                
            }
            cout<<"''''''''''''''\n";
        }
    }
    

    return std::move(new_lhyps);
}
