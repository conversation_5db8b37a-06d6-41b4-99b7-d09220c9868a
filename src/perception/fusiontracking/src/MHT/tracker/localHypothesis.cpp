/*
 * @Author: hanshuang<PERSON>n <EMAIL>
 * @Date: 2024-11-12 09:46:32
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-10-09 22:10:30
 * @FilePath: /src-MHT/src/perception/fusiontracking/src/MHT/tracker/localHypothesis.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#include "localHypothesis.h"


int LocalHypothesis::counter = 0;

LocalHypothesis::LocalHypothesis(shared_ptr<TargetPosition_CV2D> target, double LLR, double log_likelihood, double LLR_max)
    : target_(target), LLR_(LLR), LLR_max_(LLR_max < 0 ? LLR : LLR_max), log_likelihood_(log_likelihood), lid_(counter++) {
        // cout<<"创建lid_ " << lid_ << endl;
    }

int LocalHypothesis::id() const { 
    // cout<<" 返回lid_ " << lid_ << endl;
    return lid_; 
}

shared_ptr<TargetPosition_CV2D> LocalHypothesis::target() const { return target_; }

shared_ptr<Density> LocalHypothesis::density() const { return target_->density(); }
/**
 * @description: 调用Target::predict()进行预测
 * @param {double} t_now
 * @return {*}
 */
void LocalHypothesis::predict(double t_now) { 
    target_->predict(t_now); 
}

double LocalHypothesis::log_likelihood_ratio() const { return LLR_; }

double LocalHypothesis::log_likelihood() const { return log_likelihood_; }

bool LocalHypothesis::is_dead() const { return target_->is_dead(); }

bool LocalHypothesis::is_confirmed() const { return target_->is_confirmed(); }

/**
 * @description: 生成新假设
 * 根据传感器检测结果创建新的本地假设实例。
 * * 1. 复用原假设的目标对象并更新其命中状态；
 * * 2. 计算新的对数似然比；
 * * 3. 保留当前最大似然值；
 * * 4. 构造新假设对象返回。
 * 通过传感器数据生成新假设分支，而非修改原假设，以保持多假设跟踪（MHT）中假设的独立性。具体：
状态隔离：新假设独立保存目标状态副本，避免与其他分支相互干扰；
不可变设计：原假设对象（self）为const，确保其状态不可变，仅通过新对象传递更新；
分支扩展：MHT需要维护多条假设路径，新假设代表数据匹配后的独立分支，原有假设仍可参与后续其他分支计算。
 * @param {LocalHypothesis&} self
 * @param {VectorXd&} z
 * @param {double} hit_llhood
 * @param {double} t_hit
 * @return {*}
 */
shared_ptr<LocalHypothesis> LocalHypothesis::new_from_hit(const LocalHypothesis& self, const common_msgs::sensorobject& z, double hit_llhood, double t_hit) {
    auto target = self.target_;
    target->update_hit(z, t_hit);
    double llr = self.LLR_ + hit_llhood;
    // cout<<"LocalHypothesis::new_from_hit...........\n";
    return make_shared<LocalHypothesis>(target, llr, hit_llhood, max(self.LLR_max_, llr));
}

shared_ptr<LocalHypothesis> LocalHypothesis::new_from_miss(const LocalHypothesis& self, double miss_llhood, double t_now) {
    auto target = self.target_;
    target->update_miss(t_now);
    double llr = self.LLR_ + miss_llhood;
    // cout<<"LocalHypothesis::new_from_miss...........\n";
    return make_shared<LocalHypothesis>(target, llr, miss_llhood, max(self.LLR_max_, llr));
}

string LocalHypothesis::to_string() const {
    std::string idStr = std::to_string(id());
    return "<loc_hyp " + idStr + ": " + density()->to_string() + ">";
}


