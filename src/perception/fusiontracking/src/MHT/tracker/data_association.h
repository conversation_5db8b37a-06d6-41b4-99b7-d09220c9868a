/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-13 08:51:09
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-15 17:11:19
 * @FilePath: /src/perception/fusiontracking/src/MHT/tracker/data_association.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef _DATA_ASSOCIATION_H_
#define _DATA_ASSOCIATION_H_

#include <iostream>
#include <vector>
#include <map>
#include <set>
#include <algorithm>
#include <eigen3/Eigen/Dense>
#include <tuple>
#include <chrono>
#include <future>

#include "./MHT/tracker/localHypothesis.h"
#include "./MHT/murty/murty.hpp"


class SolutionIterator {
public:
    SolutionIterator(const lap::Murty& solver, 
                    const std::vector<int>& included_trids,
                    int max_solutions,
                    Eigen::MatrixXd matrix)
        : _solver(solver),
            _included_trids(included_trids),
            _max_solutions(max_solutions),
            _count(0),
            _matrix(matrix) {}
    
    bool next(double& sum_cost, 
                std::unordered_map<int, int>& assignments,
                std::vector<int>& unassigned_detections) 
    { 
        if (_count >= _max_solutions) return false;
        cout<< "_count = " << _count <<", _max_solutions = " << _max_solutions << endl;
        cout<< "start _solver.draw_tuple() "  << endl;
        
        // 启动异步任务
        auto future = std::async(std::launch::async, [this](){
            return _solver.draw_tuple();
        });
        // 等待结果，设置100ms超时
        const auto timeout = std::chrono::milliseconds(50);
        auto status = future.wait_for(timeout);
        if(status == std::future_status::timeout){
            future = {};  // 显式销毁 future 对象，确保线程立即销毁
            cout << "draw_tuple timeout after 100ms" << endl;
            return false;  // 超时返回
        }

        auto [is_ok, cost, track_to_det] = future.get();
        cout<< "end _solver.draw_tuple() "  << endl;
        if (!is_ok) return false;
        
        // 计算assignments
        const int n = _matrix.rows();
        const int m_plus_n = _matrix.cols();
        assignments.clear();
        #pragma omp parallel for
        for (int track_index = 0; track_index < track_to_det.rows(); ++track_index) {
            int det_index = track_to_det[track_index];
            assignments[_included_trids[track_index]] = (det_index < m_plus_n - n) ? det_index : MISS;
        }
        
        // 优化未分配检测计算
        unassigned_detections.clear();
        std::vector<bool> assigned_flags(m_plus_n - n, false);
        #pragma omp parallel for
        for (int track_index = 0; track_index < track_to_det.rows(); ++track_index) {
            int det_index = track_to_det[track_index];
            if (det_index < m_plus_n - n) {
                assigned_flags[det_index] = true;
            }
        }
        #pragma omp parallel for
        for (int i = 0; i < assigned_flags.size(); ++i) {
            if (!assigned_flags[i]) {
                unassigned_detections.push_back(i);
            }
        }
        
        sum_cost = cost;
        ++_count;
        return true;
    }

private:
    Eigen::MatrixXd _matrix;
    lap::Murty _solver;
    const std::vector<int>& _included_trids;
    const int _max_solutions;
    int _count;
    
};


class CostMatrix {
public:
    CostMatrix(const std::unordered_map<int, int>& global_hypothesis, const std::unordered_map<int, std::unordered_map<int, std::unordered_map<int, std::shared_ptr<LocalHypothesis>>>>& track_updates);

    std::vector<int> tracks() const ;
    std::vector<std::tuple<double, std::unordered_map<int, int>, std::vector<int>>> solutions(int max_nof_solutions = 5) const;
    friend std::ostream& operator<<(std::ostream& os, const CostMatrix& cm) {
        os << cm._matrix;
        return os;
    };

private:
    std::vector<int> _included_trids;
    Eigen::MatrixXd _matrix;
};

#endif
