/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 13:47:33
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-15 15:22:49
 * @FilePath: /src/perception/fusiontracking/src/MHT/cv_target.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __CV_TARGET_H__
#define __CV_TARGET_H__

#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>
#include <boost/math/distributions/chi_squared.hpp>

#include "../MHT/utils/density.h"
#include "../MHT/models/target.h"
#include "../MHT/models/motionmodel.h"
#include "../MHT/models/measmodel.h"
#include "common_msgs/sensorobject.h"

class TargetPosition_CV2D : public Target {
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
    ConstantVelocity2D _motion;
    alignas(16) ConstantVelocity _measure;
    double _t_now;

    TargetPosition_CV2D(const std::shared_ptr<Density>& density, double t_now);
    TargetPosition_CV2D(const TargetPosition_CV2D& other);
    TargetPosition_CV2D& operator=(const TargetPosition_CV2D& other);

    Eigen::MatrixXd _P0();

    std::shared_ptr<TargetPosition_CV2D> from_one_detection(const common_msgs::sensorobject& detection, double t_now);

    
    std::shared_ptr<ConstantVelocity2D> motion();

    std::shared_ptr<ConstantVelocity> measure();

    vector<int> gating(std::vector<common_msgs::sensorobject>& objectsMeasurements);

    double predicted_likelihood(common_msgs::sensorobject& detection);

    double max_coast_time();

private:
    double _gating_size2;
};

#endif