/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-04-01 16:27:51
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-24 08:58:42
 * @FilePath: /fusiontracking/src/MHT/models/motionmodel.cpp
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
#include <iostream>
#include <eigen3/Eigen/Dense>
#include <cmath>

#include "motionmodel.h"

using namespace std;
using namespace Eigen;


ConstantVelocity2D::ConstantVelocity2D(){
    sigma = 4.0;
}
ConstantVelocity2D::ConstantVelocity2D(double sigma) : sigma(sigma) {}

ConstantVelocity2D::ConstantVelocity2D(const ConstantVelocity2D& other) {
    this->sigma = other.sigma;
}




ConstantVelocity2D& ConstantVelocity2D::operator=(const ConstantVelocity2D& other){
    this->sigma = other.sigma;
    return *this;
}

int ConstantVelocity2D::dimension() const {
    return 5;
}

MatrixXd ConstantVelocity2D::F(const VectorXd& x, double dt) const {
    // 计算状态转移函数的雷可比矩阵（偏导数矩阵）
    // 状态向量: x = [x, y, vx, vy, θ]

    // 计算速度大小
    double v = std::sqrt(x(2)*x(2) + x(3)*x(3));

    // 当速度很小时，使用线性模型的F矩阵
    // 这样可以避免除法问题和不稳定性
    if (v < 1.0) {
        return (MatrixXd(5, 5) << 1, 0, dt, 0, 0,
                                0, 1, 0, dt, 0,
                                0, 0, 1, 0, 0,
                                0, 0, 0, 1, 0,
                                0, 0, 0, 0, 1).finished();
    }

    // 当速度足够大时，使用线性模型的F矩阵
    // 这样可以避免非线性模型带来的不稳定性
    return (MatrixXd(5, 5) << 1, 0, dt, 0, 0,
                            0, 1, 0, dt, 0,
                            0, 0, 1, 0, 0,
                            0, 0, 0, 1, 0,
                            0, 0, 0, 0, 1).finished();
}


MatrixXd ConstantVelocity2D::Q(double azimuthRad, double dt) const {
    // 为非线性运动模型设计过程噪声协方差矩阵
    // 状态向量: x = [x, y, vx, vy, θ]
    // 其中vx = v * cos(θ), vy = v * sin(θ)

    double dt2 = dt * dt;
    double dt3 = dt2 * dt;
    double dt4 = dt3 * dt;
    double squareSigma = sigma * sigma;

    // return (MatrixXd(5, 5) << dt4 / 4, 0, dt3 / 2, 0, 0,
    //                         0, dt4 / 4, 0, dt3 / 2, 0,
    //                         dt3 / 2, 0, dt2, 0, 0,
    //                         0, dt3 / 2, 0, dt2, 0,
    //                         0, 0, 0, 0,  10).finished() * squareSigma;


    // // // 获取当前状态 - 使用当前状态的航向角
    // // // 注意：在实际应用中，应该传入当前状态x并使用x(4)作为航向角
    // // // 这里为了保持接口一致性，使用默认值
    double theta = azimuthRad;  // 默认值
    double cos_theta = std::cos(theta);
    double sin_theta = std::sin(theta);

    // 位置噪声
    double pos_noise_x = dt4 / 4;
    double pos_noise_y = dt4 / 4;
    double pos_noise_xy = 0;  // 假设位置噪声在x和y方向不相关

    // 速度噪声
    double vel_noise_x = dt2;
    double vel_noise_y = dt2;
    double vel_noise_xy = 0;  // 假设速度噪声在x和y方向不相关

    // 位置-速度交叉项
    double pos_vel_noise_x = dt3 / 2;
    double pos_vel_noise_y = dt3 / 2;

    // 航向角噪声 - 使用非常小的噪声值
    double heading_noise = dt;  // 进一步减小航向角噪声

    // 速度与航向角的交叉项 - 移除交叉项影响
    double vel_heading_noise_x = 0.0;  // 移除vx与θ的关系
    double vel_heading_noise_y = 0.0;  // 移除vy与θ的关系

    // 构造Q矩阵
    MatrixXd Q(5, 5);
    Q << pos_noise_x, pos_noise_xy, pos_vel_noise_x, 0, 0,
         pos_noise_xy, pos_noise_y, 0, pos_vel_noise_y, 0,
         pos_vel_noise_x, 0, vel_noise_x, vel_noise_xy, vel_heading_noise_x,
         0, pos_vel_noise_y, vel_noise_xy, vel_noise_y, vel_heading_noise_y,
         0, 0, vel_heading_noise_x, vel_heading_noise_y, heading_noise;

    return Q * squareSigma;
}

VectorXd ConstantVelocity2D::f(const VectorXd& x, double dt) const {
    // 注意：这里的x(4)是在车辆后轴右前上坐标系（CarBackRFU）下的航向角（弧度）
    // 我们需要确保航向角始终在[0, 2π)范围内

    // 使用简单的线性模型，避免非线性模型带来的不稳定性
    double azimuth = fmod(x(4) + 2 * M_PI, 2 * M_PI);

    VectorXd predicted(5);
    predicted << x(0) + dt * x(2),           // x = x + vx*dt
                x(1) + dt * x(3),            // y = y + vy*dt
                x(2),                        // vx保持不变
                x(3),                        // vy保持不变
                azimuth;                     // 航向角保持不变
    return predicted;

    // KF
    // VectorXd predicted = F(x, dt) * x;
    // // 确保航向角在0~2π之间
    // predicted(4) = fmod(predicted(4) + 2*M_PI, 2*M_PI);
    // return predicted;

}


CoordinatedTurn2D::CoordinatedTurn2D(double sigma_vel, double sigma_angle_vel) {
    MatrixXd G(5, 2);
    G << 0, 0,
            0, 0,
            1, 0,
            0, 0,
            0, 1;

    MatrixXd S = Matrix2d::Zero();
    S(0, 0) = sigma_vel * sigma_vel;
    S(1, 1) = sigma_angle_vel * sigma_angle_vel;

    Q = G * S * G.transpose();
}

int CoordinatedTurn2D::dimension() const {
    return 5;
}

MatrixXd CoordinatedTurn2D::F(const Vector5d& x, double dt) const {
    return (MatrixXd(5, 5) << 1, 0, dt * cos(x(3)), -dt * x(2) * sin(x(3)), 0,
                                    0, 1, dt * sin(x(3)), dt * x(2) * cos(x(3)), 0,
                                    0, 0, 1, 0, 0,
                                    0, 0, 0, 1, dt,
                                    0, 0, 0, 0, 1).finished();
}

MatrixXd CoordinatedTurn2D::getQ(double dt) const {
    return Q;
}

Vector5d CoordinatedTurn2D::f(const Vector5d& x, double dt) const {
    Vector5d dx;
    dx << dt * x(2) * cos(x(3)),
            dt * x(2) * sin(x(3)),
            0,
            dt * x(4),
            0;
    return x + dx;
}
