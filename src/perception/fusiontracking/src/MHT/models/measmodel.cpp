/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-11 16:12:38
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-24 08:59:57
 * @FilePath: /fusiontracking/src/MHT/models/measmodel.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "measmodel.h"

using namespace std;
using namespace Eigen;


ConstantVelocity::ConstantVelocity(double sigma) : _sigma(sigma), dist(0, _sigma), generator(std::random_device{}()){
    // 初始化观测噪声协方差矩阵R
    _R = Eigen::Matrix<double,5,5>::Identity();
    float squareSigma = sigma * sigma;

    // 位置噪声
    _R(0, 0) = 0.1;  // x位置噪声squareSigma *
    _R(1, 1) = 0.1;  // y位置噪声

    // 速度噪声 - 设置很大的值，因为没有观测值
    _R(2, 2) = 100;  // vx没有观测值，设置很大的噪声
    _R(3, 3) = 100;  // vy没有观测值，设置很大的噪声

    // 航向角噪声
    _R(4, 4) = 0.1;  // 航向角的观测噪声
}

int ConstantVelocity::dimension() const {
    return 5;
}

MatrixXd ConstantVelocity::H(const VectorXd& x) const {
    // 观测矩阵H是观测函数h对状态x的雷可比矩阵
    // 状态向量: x = [x, y, vx, vy, θ]
    // 观测向量: z = [x, y, vx, vy, θ]
    // 但是观测量中的vx和vy目前没有输入，所以对应的行设为0

    // 修改观测矩阵，表示vx和vy没有观测值
    return (MatrixXd(5, 5) << 1, 0, 0, 0, 0,  // 观测x位置
                            0, 1, 0, 0, 0,  // 观测y位置
                            0, 0, 0, 0, 0,  // vx没有观测值
                            0, 0, 0, 0, 0,  // vy没有观测值
                            0, 0, 0, 0, 1).finished();  // 观测航向角
}

Matrix<double, 5, 5> ConstantVelocity::R() const {
    return _R;
}

Matrix<double, 5, 1> ConstantVelocity::h(const VectorXd& x) const {
    // EKF观测函数
    // 状态向量: x = [x, y, vx, vy, θ]
    // 观测向量: z = [x, y, vx, vy, θ]
    // 但是观测量中的vx和vy目前没有输入

    // 处理航向角的周期性，确保在[0, 2π)范围内
    double azimuth = fmod(x(4) + 2 * M_PI, 2 * M_PI);

    // 构造观测向量
    Matrix<double, 5, 1> measurement;
    measurement << x(0),    // x位置
                  x(1),    // y位置
                  0.0,     // vx没有观测值，设为0
                  0.0,     // vy没有观测值，设为0
                  azimuth;  // 航向角
    return measurement;

    // KF
    // Matrix<double, 5, 1> measurement = H(x) * x;
    // measurement(4) = fmod(measurement(4) + 2 * M_PI, 2 * M_PI);
    // return measurement;
}


/**
 * @description: 该函数生成带高斯噪声的测量值。
使用正态分布初始化噪声生成器，标准差为成员变量_sigma。
为4维向量的每个分量添加独立的随机噪声。
返回函数h(x)的计算结果与噪声向量的和。
 * @param {VectorXd&} x
 * @return {*}
 */


Matrix<double, 5, 1> ConstantVelocity::measure(const VectorXd& x) const {
    return h(x);
}
