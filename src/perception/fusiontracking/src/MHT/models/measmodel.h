/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 10:20:55
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-24 08:53:05
 * @FilePath: /fusiontracking/src/MHT/models/measmodel.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#ifndef __MEASMODEL_H__
#define __MEASMODEL_H__

#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>
#include <random>
#include <cmath>

using namespace std;
using namespace Eigen;

class alignas(16) ConstantVelocity {
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
    ConstantVelocity(double sigma = 1.0) ;
    // 拷贝构造函数
    ConstantVelocity(const ConstantVelocity& other) {
        _sigma = other._sigma;
        _R = other._R;
    }

    // 赋值运算符
    ConstantVelocity& operator=(const ConstantVelocity& other) {
        if (this != &other) {
            _sigma = other._sigma;
            _R = other._R;
        }
        return *this;
    }

    // 移动构造函数
    ConstantVelocity(ConstantVelocity&& other) noexcept {
        _sigma = other._sigma;
        _R = std::move(other._R);
    }

    // 移动赋值运算符
    ConstantVelocity& operator=(ConstantVelocity&& other) noexcept {
        if (this != &other) {
            _sigma = other._sigma;
            _R = std::move(other._R);
        }
        return *this;
    }

    int dimension() const;

    MatrixXd H(const VectorXd& x) const;
    Matrix<double, 5, 5> R() const;

    Matrix<double, 5, 1> h(const VectorXd& x) const ;

    Matrix<double, 5, 1> measure(const VectorXd& x) const;

private:
    alignas(16) Matrix<double, 5, 5> _R;
    double _sigma;
    mutable std::default_random_engine generator; // 使用 mutable 关键字
    mutable std::normal_distribution<double> dist; // 使用 mutable 关键字
};

#endif