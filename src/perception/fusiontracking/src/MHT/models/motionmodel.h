/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 10:06:38
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-25 10:01:02
 * @FilePath: /src/perception/fusiontracking/src/MHT/models/motionmodel.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __motionmodel_H__
#define __motionmodel_H__

#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>
#include <random>
#include <cmath>

using namespace std;
using namespace Eigen;

const double DT_DEFAULT = 0.1;

// 定义 5 维向量类型
typedef Eigen::Matrix<double, 5, 1> Vector5d;


class ConstantVelocity2D {
public:
    ConstantVelocity2D();
    ConstantVelocity2D(double sigma);
    ConstantVelocity2D(const ConstantVelocity2D& other);
    ConstantVelocity2D& operator=(const ConstantVelocity2D& other);

    int dimension() const;

    MatrixXd F(const VectorXd& x, double dt = DT_DEFAULT) const;

    MatrixXd Q(double azimuthRad, double dt = DT_DEFAULT) const ;

    VectorXd f(const VectorXd& x, double dt = DT_DEFAULT) const;

private:
    double sigma;
};

class CoordinatedTurn2D {
public:
    CoordinatedTurn2D(double sigma_vel, double sigma_angle_vel) ;

    int dimension() const;

    MatrixXd F(const Vector5d& x, double dt = DT_DEFAULT) const;

    MatrixXd getQ(double dt = DT_DEFAULT) const;

    Vector5d f(const Vector5d& x, double dt = DT_DEFAULT) const;

private:
    MatrixXd Q;
};

#endif