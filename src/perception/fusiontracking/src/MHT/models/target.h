/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 10:30:18
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-15 15:15:23
 * @FilePath: /src/perception/fusiontracking/src/MHT/models/target.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#ifndef __TARGET_H__
#define __TARGET_H__

#include <iostream>
#include <deque>
#include <vector>
#include <algorithm>
#include <memory>

#include "../../MHT/utils/density.h"
#include "../../MHT/scan_volume.h"
#include "../../MHT/utils/generation.h"



class Target {
public:
    std::shared_ptr<Density> _density;
    double _time;

    // 添加配置参数声明
    static const int min_hits_for_confirmation;
    static const double confirmation_hit_rate_threshold;
    static const double max_pos_uncertainty;
    static const double max_vel_uncertainty;
    static const int max_consecutive_misses;

public:
    Target(const std::shared_ptr<Density>& density, double t_now);
    Target(const Target& other);
    Target& operator=(const Target& other);

    void predict(double t_now);

    void update_hit(common_msgs::sensorobject detection, double t_now);

    void update_miss(double t_now) ;

    bool is_confirmed() const;

    bool is_dead();

    std::shared_ptr<Density> density() const;
    double time() const { return _time; }
    double time_hit() const { return _time_hit; }
    void setTimeHit(double value) { _time_hit = value; } 
    void setHitHistory(std::deque<bool> value) { _hit_history = value; } 
    std::deque<bool> getHitHistory() const { return _hit_history; }

    // 虚函数，需要在子类中实现
    std::shared_ptr<Target> from_one_detection(const std::shared_ptr<VectorXd>& detection, double t_now);

    virtual std::shared_ptr<ConstantVelocity2D> motion() = 0;

    virtual std::shared_ptr<ConstantVelocity> measure() = 0;

    virtual vector<int> gating(std::vector<common_msgs::sensorobject>& detections) = 0;

    virtual double predicted_likelihood(common_msgs::sensorobject& detection) = 0;

    virtual double max_coast_time() = 0;

private:
    
    double _time_hit;
    std::deque<bool> _hit_history;
};


#endif