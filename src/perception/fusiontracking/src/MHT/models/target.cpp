/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-11 16:18:45
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-15 10:55:29
 * @FilePath: /src/perception/fusiontracking/src/MHT/models/target.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "target.h"

// 添加配置参数
const int Target::min_hits_for_confirmation = 3;
const double Target::confirmation_hit_rate_threshold = 0.8;
const double Target::max_pos_uncertainty = 5.0;  // 米
const double Target::max_vel_uncertainty = 2.0;  // 米/秒
const int Target::max_consecutive_misses = 3;


Target::Target(const std::shared_ptr<Density>& density, double t_now)
    : _density(density), _time(t_now), _time_hit(t_now) {
    // cout<<"Target::Target" << endl;
    // cout << "X: " << endl << _density->x() << endl;
    // cout << "P: " << endl << _density->P() << endl;
    _hit_history.emplace_back(true);
}

Target::Target(const Target& other)
    : _density(other._density),
      _time(other._time),
      _time_hit(other._time_hit),
      _hit_history(other._hit_history)
{
}

Target& Target::operator=(const Target& other){
    if(this != &other){
        _density = other._density;
        _time = other._time;
        _time_hit = other._time_hit;
        _hit_history = other._hit_history;
    }
    return *this;
}

/**
 * @description: 调用Density::predict()进行预测
 * @param {double} t_now
 * @return {*}
 */
void Target::predict(double t_now) {
    _density->predict(*(this->motion()), t_now - _time);
    _time = t_now;
}

void Target::update_hit(common_msgs::sensorobject detection, double t_now) {
    _density->update(detection, *(this->measure()));
    _time_hit = t_now;
    _hit_history.emplace_back(true);
}

void Target::update_miss(double t_now) {
    _hit_history.emplace_back(false);
}

// TODO 发布可信目标的参数调整
/**
 * @description: 判断目标是否被确认。确认条件为：历史记录长度超过2次且至少两次检测命中（true值）。
 * * 注释部分展示了另一种更复杂的确认逻辑（需满足最近N帧击中率阈值、位置/速度不确定性阈值），但当前未启用。
 * @return {*}
 */
bool Target::is_confirmed() const {
    return _hit_history.size() > 3 && std::count(_hit_history.begin(), _hit_history.end(), true) >= 3;
    
    // 检查历史记录长度是否足够
    // if (_hit_history.size() < min_hits_for_confirmation) {
    //     return false;
    // }
    
    // // 计算最近N帧的击中率
    // int recent_hits = 0;
    // size_t start_idx = max(0, static_cast<int>(_hit_history.size()) - min_hits_for_confirmation);
    // for (size_t i = start_idx; i < _hit_history.size(); ++i) {
    //     if (_hit_history[i]) recent_hits++;
    // }
    
    // // // 计算总体击中率
    // double hit_rate = static_cast<double>(recent_hits) / min_hits_for_confirmation;
    
    // 检查状态估计的不确定性
    // Eigen::MatrixXd P = _density->P();
    // double pos_uncertainty = sqrt(P(0,0) + P(1,1));  // 位置不确定性
    // double vel_uncertainty = sqrt(P(2,2) + P(3,3));  // 速度不确定性
    
    // 综合判断：击中率高且状态估计稳定
    // return hit_rate >= confirmation_hit_rate_threshold && 
    //        pos_uncertainty < max_pos_uncertainty &&
    //        vel_uncertainty < max_vel_uncertainty;

    // return hit_rate >= confirmation_hit_rate_threshold;
}

/* 判断目标是否已死亡。逻辑如下：

计算当前时间和最后一次被击中的时间差是否超过最大滑行时间。
或者，检查命中历史记录中是否有多于两个记录且其中少于两个为真。
*/
// TODO 存活时间参数调整
bool Target::is_dead() {
    double timeout = (_time - _time_hit) > max_coast_time();
    bool isDead = timeout || (_hit_history.size() > 2 && std::count(_hit_history.begin(), _hit_history.end(), true) < 2);
    // cout<<"timeout: "<<timeout<<", _time: "<<_time<<", _time_hit: "<<_time_hit<<", max_coast_time(): "<<max_coast_time()
    //     <<", _hit_history.size(): "<<_hit_history.size() <<", isDead: "<<isDead<<endl;
    return isDead;

    // // 检查最大滑行时间
    // if (_time - _time_hit > max_coast_time()) { // max_coast_time
    //     return true;
    // }
    
    // // 检查连续丢失次数
    // int consecutive_misses = 0;
    // for (auto it = _hit_history.rbegin(); it != _hit_history.rend(); ++it) {
    //     if (!*it) {
    //         consecutive_misses++;
    //     } else {
    //         break;
    //     }
    // }
    // if (consecutive_misses > max_consecutive_misses) {
    //     return true;
    // }
    
    // // // 检查状态估计的不确定性
    // // Eigen::MatrixXd P = _density->P();
    // // double pos_uncertainty = sqrt(P(0,0) + P(1,1));
    // // double vel_uncertainty = sqrt(P(2,2) + P(3,3));
    
    // // // 如果不确定性过大，认为航迹已死亡
    // // if (pos_uncertainty > max_pos_uncertainty * 2 || 
    // //     vel_uncertainty > max_vel_uncertainty * 2) {
    // //     return true;
    // // }
    
    // // 检查总体击中率
    // int total_hits = count(_hit_history.begin(), _hit_history.end(), true);
    // double overall_hit_rate = static_cast<double>(total_hits) / _hit_history.size();
    
    // // 如果总体击中率过低且历史记录足够长，认为航迹已死亡
    // if (_hit_history.size() >= min_hits_for_confirmation * 2 && 
    //     overall_hit_rate < confirmation_hit_rate_threshold / 2) {
    //     return true;
    // }
    
    // return false;
}

std::shared_ptr<Density> Target::density() const {
    return _density;
}

// 虚函数，需要在子类中实现
std::shared_ptr<Target> Target::from_one_detection(const std::shared_ptr<VectorXd>& detection, double t_now) {
    throw std::runtime_error("Not implemented");
}


