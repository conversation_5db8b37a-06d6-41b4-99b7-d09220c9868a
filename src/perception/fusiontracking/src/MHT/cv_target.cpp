/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 13:47:28
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-23 16:03:29
 * @FilePath: /src/perception/fusiontracking/src/MHT/cv_target.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "cv_target.h"


TargetPosition_CV2D::TargetPosition_CV2D(const std::shared_ptr<Density>& density, double t_now): Target(density, t_now){
    // cout<<"TargetPosition_CV2D：：TargetPosition_CV2D：： "<<endl;
    _motion = ConstantVelocity2D(4); //创建二维匀速运动模型，标准差为0.01
    _measure = ConstantVelocity(2000);  // 创建二维匀速测量模型，标准差为0.1 调整新目标创建时的协方差矩阵
    // 创建n维卡方分布对象dist，设置置信水平0.99，计算卡方分布在置信度0.99下的值，并赋值给_gating_size2
    boost::math::chi_squared dist(_measure.dimension());
    double P_G = 0.80;
    _gating_size2 = boost::math::quantile(dist, P_G);
    _t_now = t_now;
}

TargetPosition_CV2D::TargetPosition_CV2D(const TargetPosition_CV2D& other): Target(other._density, other._t_now){
    _motion = other._motion;
    _measure = other._measure;
    _gating_size2 = other._gating_size2;
    _t_now = other._t_now;
}

TargetPosition_CV2D& TargetPosition_CV2D::operator=(const TargetPosition_CV2D& other){
    if (this != &other){
        _motion = other._motion;
        _measure = other._measure;
        _gating_size2 = other._gating_size2;

        _density = other._density;
        _time = other._time;
        this->setTimeHit(other.time_hit());
        this->setHitHistory(other.getHitHistory());
    }
    return *this;
    
}

Eigen::MatrixXd TargetPosition_CV2D::_P0() {
    Eigen::MatrixXd R = _measure.R();
    Eigen::MatrixXd P0(5, 5);
    P0 << R(0, 0), 0, 0, 0, 0,
            0, R(1, 1), 0, 0, 0,
            0, 0, R(2, 2), 0, 0,
            0, 0, 0, R(3, 3) , 0,
            0, 0, 0, 0, R(4, 4); // 调整新目标创建时的协方差矩阵
    return P0;
}

std::shared_ptr<TargetPosition_CV2D> TargetPosition_CV2D::from_one_detection(const common_msgs::sensorobject& detection, double t_now){
    Eigen::MatrixXd P0 = _P0();
    std::shared_ptr<Density> pDensity = std::make_shared<Density>(detection, P0);
    // std::shared_ptr<TargetPosition_CV2D> target = std::make_shared<TargetPosition_CV2D>(pDensity, t_now);
    std::shared_ptr<TargetPosition_CV2D> target(new TargetPosition_CV2D(pDensity, t_now));
    return target;
}

std::shared_ptr<ConstantVelocity2D> TargetPosition_CV2D::motion(){
    return std::make_shared<ConstantVelocity2D>(_motion);
}

std::shared_ptr<ConstantVelocity> TargetPosition_CV2D::measure() {
    return std::make_shared<ConstantVelocity>(_measure);
}

vector<int> TargetPosition_CV2D::gating(std::vector<common_msgs::sensorobject>& objectsMeasurements) {
    return _density->gating(objectsMeasurements, _measure, _gating_size2);
}

/**
 * @description: 预测似然概率
 * @param {VectorXd&} detection
 * @return {*}
 */
double TargetPosition_CV2D::predicted_likelihood(common_msgs::sensorobject& detection) {
    return _density->predicted_likelihood(detection, _measure);
}

double TargetPosition_CV2D::max_coast_time() {
    return 1.0; // TODO 丢失时间参数调整，确认单位，s ms
}

