/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 10:56:39
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-11 11:18:05
 * @FilePath: /src/perception/fusiontracking/src/MHT/scan_volume.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __SCAN_VOLUME_H__
#define __SCAN_VOLUME_H__

#include <iostream>
#include <vector>
#include <cassert>
#include <cmath>
#include <eigen3/Eigen/Dense>
#include <optional>
#include <unordered_map>

// #include "generation.h"


class Volume {
public:
    Volume(double P_D, double clutter_lambda, double init_lambda);
    Volume(const Volume& other);
    Volume& operator=(const Volume& other);

    double P_D() const ;

    // double _intensity(double lam) const ;

    double clutter_intensity(double lambda_c = -1.0) const ;

    double initiation_intensity(double lambda_init = -1.0) const ;

    // virtual double volume() const = 0;

protected:
    double _pd;
    double _lambda_c;
    double _lambda_init;
};

class CartesianVolume : public Volume {
public:
    CartesianVolume(const Eigen::MatrixXd& ranges, double P_D, double clutter_lambda, double init_lambda);
    CartesianVolume(const CartesianVolume& other);
    CartesianVolume& operator=(const CartesianVolume& other);
    // double volume() const;


private:
    Eigen::MatrixXd _ranges;
};

#endif