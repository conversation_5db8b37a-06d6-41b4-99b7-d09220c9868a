/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-21 13:04:39
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-11-21 13:21:33
 * @FilePath: /HO-MHT-c/src/hungarian/Hungarian.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef HUNGARIAN_H
#define HUNGARIAN_H

#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>
#include <limits>
#include <stdexcept>

using namespace std;
using namespace Eigen;
#include <eigen3/Eigen/Dense>
#include <utility>

// Forward declarations
class Hungary;
typedef void (*StepFunction)(Hungary& state);

class Hungary {
private:
    Eigen::MatrixXd C;
    Eigen::Array<bool, Eigen::Dynamic, 1> row_uncovered;
    Eigen::Array<bool, Eigen::Dynamic, 1> col_uncovered;
    int Z0_r;
    int Z0_c;
    Eigen::MatrixXi path;
    Eigen::MatrixXi marked;

    void clear_covers();

public:
    Hungary(const Eigen::MatrixXd& cost_matrix);

    friend std::pair<Eigen::VectorXi, Eigen::VectorXi> linear_sum_assignment(const Eigen::MatrixXd& cost_matrix);
    friend void step1(Hungary& state);
    friend void step3(Hungary& state);
    friend void step4(Hungary& state);
    friend void step5(Hungary& state);
    friend void step6(Hungary& state);
};

std::pair<Eigen::VectorXi, Eigen::VectorXi> linear_sum_assignment(const Eigen::MatrixXd& cost_matrix);

#endif // HUNGARIAN_H

