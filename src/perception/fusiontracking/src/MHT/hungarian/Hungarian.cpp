#include "Hungarian.h"
#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>
#include <limits>
#include <stdexcept>

using namespace std;
using namespace Eigen;

#include <limits>
#include <stdexcept>
#include <vector>

Hungary::Hungary(const Eigen::MatrixXd& cost_matrix) {
    C = cost_matrix;
    int n = C.rows();
    int m = C.cols();

    row_uncovered = Eigen::Array<bool, Eigen::Dynamic, 1>::Ones(n);
    col_uncovered = Eigen::Array<bool, Eigen::Dynamic, 1>::Ones(m);
    Z0_r = 0;
    Z0_c = 0;
    path = Eigen::MatrixXi::Zero(n + m, 2);
    marked = Eigen::MatrixXi::Zero(n, m);
}

void Hungary::clear_covers() {
    row_uncovered.setConstant(true);
    col_uncovered.setConstant(true);
}
void step1(Hungary& state) {
    // Step 1: For each row of the matrix, find the smallest element and
    // subtract it from every element in its row.
    for (int i = 0; i < state.C.rows(); ++i) {
        double min_val = state.C.row(i).minCoeff();
        state.C.row(i).array() -= min_val;
    }

    // Step 2: Find a zero (Z) in the resulting matrix. If there is no
    // starred zero in its row or column, star Z.
    state.clear_covers();
    for (int i = 0; i < state.C.rows(); ++i) {
        for (int j = 0; j < state.C.cols(); ++j) {
            if (std::abs(state.C(i, j)) < 1e-10 &&
                state.col_uncovered(j) &&
                state.row_uncovered(i)) {
                state.marked(i, j) = 1;
                state.col_uncovered(j) = false;
                state.row_uncovered(i) = false;
            }
        }
    }
}

void step3(Hungary& state) {
    state.col_uncovered.setConstant(true);

    for (int j = 0; j < state.C.cols(); ++j) {
        for (int i = 0; i < state.C.rows(); ++i) {
            if (state.marked(i, j) == 1) {
                state.col_uncovered(j) = false;
            }
        }
    }

    int count = (state.col_uncovered.array() == false).count();
    if (count >= state.C.rows()) {
        return;
    }
}

void step4(Hungary& state) {
    const double ZERO_THRESHOLD = 1e-10;
    while (true) {
        // Find an uncovered zero
        int row = -1, col = -1;
        bool done = false;

        for (int i = 0; i < state.C.rows() && !done; ++i) {
            for (int j = 0; j < state.C.cols() && !done; ++j) {
                if (std::abs(state.C(i, j)) < ZERO_THRESHOLD &&
                    state.row_uncovered(i) &&
                    state.col_uncovered(j)) {
                    row = i;
                    col = j;
                    done = true;
                }
            }
        }

        if (row == -1) {
            step6(state);
            continue;
        }

        state.marked(row, col) = 2;

        // Find starred zero in the row
        int star_col = -1;
        for (int j = 0; j < state.C.cols(); ++j) {
            if (state.marked(row, j) == 1) {
                star_col = j;
                break;
            }
        }

        if (star_col == -1) {
            state.Z0_r = row;
            state.Z0_c = col;
            step5(state);
            return;
        }

        state.row_uncovered(row) = false;
        state.col_uncovered(star_col) = true;
    }
}

void step5(Hungary& state) {
    int count = 0;
    state.path(count, 0) = state.Z0_r;
    state.path(count, 1) = state.Z0_c;

    while (true) {
        // Find starred zero in column
        int row = -1;
        for (int i = 0; i < state.C.rows(); ++i) {
            if (state.marked(i, state.path(count, 1)) == 1) {
                row = i;
                break;
            }
        }

        if (row == -1) break;

        count++;
        state.path(count, 0) = row;
        state.path(count, 1) = state.path(count - 1, 1);

        // Find primed zero in row
        int col = -1;
        for (int j = 0; j < state.C.cols(); ++j) {
            if (state.marked(row, j) == 2) {
                col = j;
                break;
            }
        }

        count++;
        state.path(count, 0) = state.path(count - 1, 0);
        state.path(count, 1) = col;
    }

    // Convert path
    for (int i = 0; i <= count; ++i) {
        if (state.marked(state.path(i, 0), state.path(i, 1)) == 1)
            state.marked(state.path(i, 0), state.path(i, 1)) = 0;
        else
            state.marked(state.path(i, 0), state.path(i, 1)) = 1;
    }

    state.clear_covers();

    // Erase all prime markings
    for (int i = 0; i < state.marked.rows(); ++i) {
        for (int j = 0; j < state.marked.cols(); ++j) {
            if (state.marked(i, j) == 2) {
                state.marked(i, j) = 0;
            }
        }
    }
}

void step6(Hungary& state) {
    double minval = std::numeric_limits<double>::max();

    for (int i = 0; i < state.C.rows(); ++i) {
        if (!state.row_uncovered(i)) continue;
        for (int j = 0; j < state.C.cols(); ++j) {
            if (state.col_uncovered(j) && state.C(i, j) < minval) {
                minval = state.C(i, j);
            }
        }
    }

    for (int i = 0; i < state.C.rows(); ++i) {
        if (!state.row_uncovered(i)) {
            state.C.row(i).array() += minval;
        }
    }

    for (int j = 0; j < state.C.cols(); ++j) {
        if (state.col_uncovered(j)) {
            state.C.col(j).array() -= minval;
        }
    }
}

std::pair<Eigen::VectorXi, Eigen::VectorXi> linear_sum_assignment(const Eigen::MatrixXd& cost_matrix) {
    if (cost_matrix.size() == 0) {
        throw std::invalid_argument("cost matrix is empty");
    }

    // Check if matrix needs to be transposed
    bool transposed = false;
    Eigen::MatrixXd working_matrix = cost_matrix;
    if (working_matrix.cols() < working_matrix.rows()) {
        working_matrix.transposeInPlace();
        transposed = true;
    }

    Hungary state(working_matrix);

    step1(state);
    step3(state);

    while (true) {
        int stars = 0;
        for (int i = 0; i < state.marked.rows(); ++i) {
            for (int j = 0; j < state.marked.cols(); ++j) {
                if (state.marked(i, j) == 1) stars++;
            }
        }

        if (stars >= state.C.rows()) break;

        step4(state);
        step3(state);
    }

    // Store indices in a vector of pairs
    std::vector<std::pair<int, int>> indices;
    for (int i = 0; i < state.marked.rows(); ++i) {
        for (int j = 0; j < state.marked.cols(); ++j) {
            if (state.marked(i, j) == 1) {
                indices.push_back({i, j});
            }
        }
    }

    // Sort indices by row index
    std::sort(indices.begin(), indices.end());

    // Create result vectors
    Eigen::VectorXi row_ind(indices.size());
    Eigen::VectorXi col_ind(indices.size());

    for (size_t i = 0; i < indices.size(); ++i) {
        row_ind(i) = indices[i].first;
        col_ind(i) = indices[i].second;
    }

    if (transposed) {
        return {col_ind, row_ind};
    }
    return {row_ind, col_ind};
}