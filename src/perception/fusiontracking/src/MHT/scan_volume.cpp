/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-21 17:38:18
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-11 11:23:23
 * @FilePath: /src/perception/fusiontracking/src/MHT/scan_volume.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#include "scan_volume.h"

/**
 * @description: 
 * @param {double} P_D 检测概率 [0,1)
 * @param {double} clutter_lambda 生成杂波的泊松分布的系数为clutter_lambda=1.0
 * @param {double} init_lambda 目标生成时的泊松分布的系数为init_lambda=1.0
 * @return {*}
 */
Volume::Volume(double P_D, double clutter_lambda, double init_lambda)
    : _pd(P_D), _lambda_c(clutter_lambda), _lambda_init(init_lambda) {}

Volume::Volume(const Volume& other){
    _pd = other._pd;
    _lambda_c = other._lambda_c;
    _lambda_init = other._lambda_init;
}

Volume& Volume::operator=(const Volume& other){
    if(this != &other){
        _pd = other._pd;
        _lambda_c = other._lambda_c;
        _lambda_init = other._lambda_init;
    }
    return *this;
}

double Volume::P_D() const {
    return _pd;
}

// double Volume::_intensity(double lam) const {
//     return lam ;/// volume();
// }

/**
 * @description: 根据输入参数 lambda_c 计算并返回杂波强度值，具体逻辑如下：
如果 lambda_c 小于 0，则使用对象自身的 _lambda_c 值作为输入。
否则，直接使用传入的 lambda_c 值。
调用 _intensity 方法计算并返回杂波强度值。
 * @param {double} lambda_c
 * @return {*}
 */
double Volume::clutter_intensity(double lambda_c) const {
    double lambda = lambda_c < 0 ? this->_lambda_c : lambda_c;
    // std::cout <<"lambda: " << lambda <<", _lambda_c: " << _lambda_c <<", lambda_c: " << lambda_c << std::endl;
    return lambda; // _intensity(lambda);
}

/**
 * @description: 根据输入参数 lambda_init 计算并返回初始化强度值。具体逻辑如下：
如果 lambda_init 小于 0，则使用对象自身的 _lambda_init 值。
否则，使用传入的 lambda_init 值。
调用 _intensity 方法，传入上述选择的值，并返回结果。
 * @param {double} lambda_init
 * @return {*}
 */
double Volume::initiation_intensity(double lambda_init) const {
    return lambda_init < 0 ? this->_lambda_init : lambda_init; // _intensity(lambda_init < 0 ? this->_lambda_init : lambda_init);
}

/**
 * @description: 
 * @param {MatrixXd&} ranges 生成量测边界
 * @param {double} P_D 检测概率
 * @param {double} clutter_lambda 生成杂波的泊松分布的系数为clutter_lambda=1.0
 * @param {double} init_lambda 目标生成时的泊松分布的系数为init_lambda=1.0
 * @return {*}
 */
CartesianVolume::CartesianVolume(const Eigen::MatrixXd& ranges, double P_D, double clutter_lambda, double init_lambda)
    : Volume(P_D, clutter_lambda, init_lambda), _ranges(ranges) {
    assert(ranges.cols() == 2);
    assert((ranges.col(0).array() <= ranges.col(1).array()).all());
}

CartesianVolume::CartesianVolume(const CartesianVolume& other): Volume(other){
    _ranges = other._ranges;
}
CartesianVolume& CartesianVolume::operator=(const CartesianVolume& other){
    if(this != &other){
        _ranges = other._ranges;
    }
    return *this;
}

/**
 * @description: 计算一个笛卡尔空间的体积，具体功能如下：
_ranges.col(1) 表示范围矩阵的第二列（通常是最大值）。
_ranges.col(0) 表示范围矩阵的第一列（通常是最小值）。
计算两列对应元素的差值，得到每个维度的长度。
对这些长度求积，得到总体积。
 * @return {*}
 */
// double CartesianVolume::volume() const{
//     double volume = (_ranges.col(1) - _ranges.col(0)).prod();
//     // std::cout <<"_ranges.col(1): " << _ranges.col(1).transpose() 
//     //             <<"\n_ranges.col(0): " << _ranges.col(0).transpose()
//     //             <<", volume: " << volume << std::endl;
//     return volume;
// }

