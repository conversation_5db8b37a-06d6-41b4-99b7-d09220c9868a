/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-11 16:45:32
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-03-10 10:17:33
 * @FilePath: /HO-MHT-c/src/utils/generation.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __GENERAION_H__
#define __GENERAION_H__


#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>
#include <random>
#include <cassert>
#include <algorithm>
#include <unordered_map>


#include "../../MHT/models/motionmodel.h"
#include "../../MHT/models/measmodel.h"
#include "common_msgs/sensorobject.h"
using namespace std;
using Eigen::MatrixXd;
using Eigen::VectorXd;

// 定义常量
const double EPS = 1e-10;
const double LARGE = 1e10;
const double LOG_0 = 1e6;
const int MISS = -1;


double mahalanobis2(const common_msgs::sensorobject& x, const VectorXd& mu, const MatrixXd& inv_sigma) ;
double mahalanobis2(const VectorXd& x, const VectorXd& mu, const MatrixXd& inv_sigma) ;
double euclideanDistance(const VectorXd& x, const VectorXd& mu);
double euclideanDistance(const common_msgs::sensorobject& x, const VectorXd& mu);


#endif