/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-11 16:45:32
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-11 15:34:54
 * @FilePath: /src/perception/fusiontracking/src/MHT/utils/generation.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */


#include "generation.h"

double mahalanobis2(const common_msgs::sensorobject& x, const VectorXd& mu, const MatrixXd& inv_sigma) {
    VectorXd xmatrix = Eigen::VectorXd::Zero(2);
    xmatrix << x.x, x.y;
    Vector2d mumatrix(mu(0), mu(1));
    VectorXd d = xmatrix - mumatrix;
    Eigen::Matrix2d inv_sigma2 = inv_sigma.block<2,2>(0,0);
    return d.transpose() * inv_sigma2 * d;
}

double mahalanobis2(const VectorXd& x, const VectorXd& mu, const MatrixXd& inv_sigma) {
    VectorXd xmatrix = Eigen::VectorXd::Zero(2);
    xmatrix << x(0), x(1);
    Vector2d mumatrix(mu(0), mu(1));
    VectorXd d = xmatrix - mumatrix;
    Eigen::Matrix2d inv_sigma2 = inv_sigma.block<2,2>(0,0);
    return d.transpose() * inv_sigma2 * d;   
}

inline double euclideanDistance(const common_msgs::sensorobject& x, const VectorXd& mu) {
    double distance = sqrt((x.x - mu(0))* (x.x - mu(0))+ (x.y - mu(1)) * (x.y - mu(1)));
    return std::move(distance);
}

double euclideanDistance(const VectorXd& x, const VectorXd& mu) {
    double distance = sqrt((x(0) - mu(0))* (x(0) - mu(0))+ (x(1) - mu(1)) * (x(1) - mu(1)));
    return std::move(distance);
}
