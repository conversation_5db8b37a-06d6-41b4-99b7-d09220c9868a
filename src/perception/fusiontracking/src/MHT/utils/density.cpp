
#include "density.h"

const double Density::log2pi_ = log(2 * M_PI);
Density::Density() //: x_(VectorXd()), P_(MatrixXd()) 
{
    // std::cout << "density Default constructor called" << std::endl;
}
Density::Density(const common_msgs::sensorobject& x, const MatrixXd& P) : P_(P) {
    // cout<<"Density1 P_ : "<<P_<<endl;
    xObject_ = x;
    Eigen::VectorXd xVector = Eigen::VectorXd::Zero(5);
    xVector << x.x, x.y, x.relspeedx, x.relspeedy, x.azimuth; // TODO 使用绝对速度
    x_ = xVector;
    // std::cout << "density Parameterized constructor called" << std::endl;
    // std::cout << "Passed x: " << x << std::endl;
    // std::cout << "Passed P: " << P << std::endl;
    // cout<<"Density x_ : "<<x_<<endl;
    // cout<<"Density P_ : "<<P_<<endl;
}

VectorXd Density::x() const {
    return x_;
}
MatrixXd Density::P() const { return P_; }

void Density::set_x(const Eigen::VectorXd& x) {
    x_ = x;
}
void Density::set_P(const MatrixXd& P) { P_ = P; }

string Density::to_string() const {
    stringstream ss;
    ss << x_.transpose().format(Eigen::IOFormat(Eigen::StreamPrecision, 0, ", ", ", ", "[", "]"));
    return "<density x=" + ss.str() + ">";
}

bool Density::operator==(const Density& other) const {
    Eigen::VectorXd otherVector = Eigen::VectorXd::Zero(5);
    otherVector << other.x_(0), other.x_(1), other.x_(2), other.x_(3), other.x_(4);
    return x_.isApprox(otherVector) && P_.isApprox(other.P_);
}

/**
 * @description: 计算多元正态分布的概率密度函数对数
 * * 计算协方差矩阵P的行列式对数
求P的逆矩阵
调用mahalanobis2计算马氏距离平方
按多元正态分布对数概率密度公式返回结果
 * @param {sensorobject&} x
 * @return {*}
 */
double Density::ln_mvnpdf(const common_msgs::sensorobject& x) const {
    // double ln_det_sigma = log(P_.determinant());
    // MatrixXd inv_sigma = P_.inverse();
    // return -0.5 * (ln_det_sigma + mahalanobis2(x, x_, inv_sigma) + x_.size() * log2pi_);

    // 改用LDLT分解
    Eigen::LDLT<Eigen::MatrixXd> ldlt(P_);
    if (ldlt.info() != Eigen::Success) {
        return -std::numeric_limits<double>::infinity();
    }

    // 行列式对数 = Σln(D的对角元素)
    double ln_det_sigma = ldlt.vectorD().array().log().sum();

    // 马氏距离计算优化
    double azimuthRadGap = x.azimuth - x_(4);
    if(azimuthRadGap < 0) azimuthRadGap += 2 * M_PI;
    VectorXd delta(5);
    delta << x.x - x_(0), x.y - x_(1),
            x.relspeedx - x_(2), x.relspeedy - x_(3), azimuthRadGap;
    double mahalanobis = (ldlt.transpositionsP().transpose() * delta)
                        .dot(ldlt.vectorD().cwiseInverse().asDiagonal()
                             * (ldlt.matrixL().solve(delta)));

    return -0.5 * (ln_det_sigma + mahalanobis + x_.size() * log2pi_);
}

// 使用马氏距离判断是否在匹配范围内
// tuple<vector<common_msgs::sensorobject>, vector<int>> Density::gating(std::vector<common_msgs::sensorobject>& objectsMeasurements,
//             const ConstantVelocity& measure,
//             double size2, const MatrixXd* inv_S, bool bool_index){
//     MatrixXd S_inv = inv_S ? *inv_S : innovation(measure).inverse();

//     VectorXd zbar = measure.h(x_);
//     auto is_inside = [&](const VectorXd& z) { return mahalanobis2(z, zbar, S_inv) < size2; };

//     // if (bool_index) {
//     //     vector<bool> in_gate(objectsMeasurements.size());
//     //     for (size_t i = 0; i < objectsMeasurements.size(); ++i) {
//     //         VectorXd z = Vector2d{objectsMeasurements[i].x, objectsMeasurements[i].y};
//     //         in_gate[i] = is_inside(z);
//     //     }
//     //     // 将 bool 向量转换为 int 向量
//     //     vector<int> indices;
//     //     for (bool b : in_gate) {
//     //         indices.push_back(b ? 1 : 0);
//     //     }
//     //     return std::make_tuple(objectsMeasurements, indices);
//     // } else {
//         vector<common_msgs::sensorobject> in_gate;
//         vector<int> indices;
//         for (size_t i = 0; i < objectsMeasurements.size(); ++i) {
//             VectorXd z = Vector2d{objectsMeasurements[i].x, objectsMeasurements[i].y};
//             if (is_inside(z)) {
//                 in_gate.push_back(objectsMeasurements[i]);
//                 indices.push_back(i);
//             }
//         }
//         return std::make_tuple(in_gate, indices);
//     // }
// }

// 使用欧氏距离判断是否在匹配范围内
vector<int> Density::gating(std::vector<common_msgs::sensorobject>& objectsMeasurements,
    const ConstantVelocity& measure,
    double size2, const MatrixXd* inv_S, bool bool_index){
    // cout <<"x_:"<<x_.transpose()<<endl;
    Matrix<double, 5, 1> zbar = measure.h(x_);
    // cout <<"zbar:"<<zbar.transpose()<<endl;
    Eigen::Vector2d z;

    vector<int> indices;
    #pragma omp parallel for
    for (size_t i = 0; i < objectsMeasurements.size(); ++i) {
        const common_msgs::sensorobject& object = objectsMeasurements[i];
        z << object.x, object.y;
        bool isClosed = euclideanDistance(z, zbar) < 2; // hsq xObject_.length / 2.0;//
        bool isSameClass1 = object.classification == xObject_.classification;
        bool isSameClass2 = (object.classification == 2 || object.classification == 6) &&
                                (xObject_.classification == 2 || xObject_.classification == 6);
        bool isSameClass = isSameClass1 || isSameClass2;
        bool isInGating = isClosed && isSameClass;

        if (isInGating) {
            indices.emplace_back(i);
        }
    }
    return std::move(indices);
}

/**
 * @description: 预测对数似然\Lambda(x_{k|k_1})=P(zk|xkk_1)=N(zkk_1,Sk)=>ln f(z,zkk_1,S)
 * 计算预测测量值：通过measure.h(x_)预测状态x的测量值zbar
构造预测观测对象：将预测值zbar的前两个元素存入zbarObject的x/y字段
选择协方差矩阵：若S指针有效则直接使用，否则调用innovation计算
创建概率密度对象：用预测值和协方差构建正态分布对象
计算对数似然：返回真实测量值z在预测分布中的对数概率密度值
 * @param {VectorXd&} z 观测值
 * @param {ConstantVelocity&} measure 观测模型
 * @param {MatrixXd*} S
 * @return {*}
 */
double Density::predicted_likelihood(common_msgs::sensorobject& z, const ConstantVelocity& measure, const MatrixXd* S){
    common_msgs::sensorobject zbarObject = z;
    VectorXd zbar = measure.h(x_);
    zbarObject.x = zbar[0];
    zbarObject.y = zbar[1];
    MatrixXd S_ = S ? *S : innovation(measure);
    Density d(zbarObject, S_);
    return d.ln_mvnpdf(z); // 计算多元正态分布的概率密度函数对数
}
/**
 * @description: 调用模型进行预测
 * @param {ConstantVelocity2D&} motion
 * @param {double} dt
 * @return {*}
 */
void Density::predict(const ConstantVelocity2D& motion, double dt) {
    // cout<<"x_ = " << x_.transpose() << ", dt = " << dt << endl;
    MatrixXd F = motion.F(x_, dt);
    VectorXd x = motion.f(x_, dt);
    x(4) = fmod(x(4) + 2 * M_PI, 2 * M_PI);
    MatrixXd P = F * P_ * F.transpose() + motion.Q(x(4), dt);
    set_x(x);
    set_P(P);
    // cout<<"x = " << x.transpose() << endl;
    // cout<<"P = " << P.transpose() << dt << endl;
}

// 将航向角规范化到[0, 2π)范围内
 double Density::normalizeHeading(double heading) const {
    // 先将航向角规范化到[0, 2π)范围
    heading = fmod(heading + 2*M_PI, 2*M_PI);

    // 处理数值精度问题，避免很小的负值
    if (heading < 0) {
        heading += 2*M_PI;
    }

    return heading;
}

// 计算两个航向角之间的最小差值，考虑周期性
 double Density::computeHeadingDifference(double heading1, double heading2) const {
    // 先将两个航向角规范化
    heading1 = normalizeHeading(heading1);
    heading2 = normalizeHeading(heading2);

    // 计算直接差值
    double diff = heading1 - heading2;

    // 处理周期性，确保差值在[-π, π]范围内
    if (diff > M_PI) {
        diff -= 2*M_PI;
    } else if (diff < -M_PI) {
        diff += 2*M_PI;
    }

    return diff;
}

// 判断测量的航向角是否为异常值
 bool Density::isHeadingOutlier(double measured_heading) const {
    // 如果历史缓存为空，则不判断为异常值
    if (heading_history_.empty()) {
        return false;
    }

    // 规范化测量航向角
    measured_heading = normalizeHeading(measured_heading);

    // 方案1：使用最近N个航向角的中位数作为参考
    const int N = std::min(15, static_cast<int>(heading_history_.size()));
    std::vector<double> recent_headings(heading_history_.end() - N, heading_history_.end());
    
    // 计算中位数航向角
    std::sort(recent_headings.begin(), recent_headings.end());
    double median_heading = recent_headings[N/2];
    
    // 计算与中位数的差值
    double diff = computeHeadingDifference(measured_heading, median_heading);
    // 如果差值超过阈值，则判断为异常值
    return diff > M_PI / 9.0;

    // 方案2：检查与最近几个航向角的差值，如果大部分都超过阈值才判定为异常
    // int outlier_count = 0;
    // for (const double& hist_heading : recent_headings) {
    //     if (computeHeadingDifference(measured_heading, hist_heading) > M_PI / 3.0) {
    //         outlier_count++;
    //     }
    // }
    // return outlier_count > N * 0.6;  // 如果超过60%的历史数据都显示为异常，则判定为异常
}

// 获取平滑后的航向角
 double Density::getSmoothedHeading(double measured_heading) {
    // 规范化测量航向角
    measured_heading = normalizeHeading(measured_heading);

    // 如果历史缓存为空，则直接返回测量航向角
    if (heading_history_.empty()) {
        return measured_heading;
    }

    // 使用加权平均计算平滑航向角
    double sum_sin = 0.0;
    double sum_cos = 0.0;

    // 计算加权的sin和cos和
    for (size_t i = 0; i < heading_history_.size(); ++i) {
        cout<<"heading_history_[i] = " << heading_history_[i] * 180.0 / M_PI << endl;
        sum_sin += sin(heading_history_[i]);
        sum_cos += cos(heading_history_[i]);
    }

    sum_sin /= (float)heading_history_.size();
    sum_cos /= (float)heading_history_.size();


    // 计算平均航向角
    double avg_heading = atan2(sum_sin,  sum_cos);

    // 规范化到[0, 2π)范围
    return normalizeHeading(avg_heading);
}

void Density::update(const common_msgs::sensorobject& z, const ConstantVelocity& measure, const MatrixXd* inv_S) {
    xObject_ = z;

    // 1. 构造观测向量
    Eigen::Matrix<double,5,1> zVector;
    // 注意：观测量中的vx和vy目前没有输入，设置为0
    // 实际上，这些值不会影响更新，因为观测矩阵H中对应的行为0
    zVector << z.x, z.y, 0.0, 0.0, z.azimuth;

    // 2. 预测值
    VectorXd predicted = measure.h(x_);

    // 3. 航向角处理 - 重要修改：确保在正确的坐标系下处理
    // 注意：z.azimuth和x_(4)都是在车辆后轴右前上坐标系（CarBackRFU）下的航向角（弧度）
    // 因此可以直接在车辆坐标系下进行比较和处理

    // 规范化测量和预测航向角
    double measured_heading = normalizeHeading(z.azimuth);  // 车辆坐标系下的测量航向角
    double predicted_heading = normalizeHeading(predicted(4));  // 车辆坐标系下的预测航向角

    // 获取平滑后的航向角
    double smoothed_heading = getSmoothedHeading(measured_heading);
    cout<<"smoothed_heading = " << smoothed_heading * 180.0 / M_PI 
        << ", measured_heading = " << measured_heading * 180.0 / M_PI
        << ", predicted_heading = " << predicted_heading * 180.0 / M_PI<< endl;

    // 计算平滑航向角与预测航向角的差值
    double heading_diff = computeHeadingDifference(measured_heading, predicted_heading);

    // 4. 构造创新向量
    VectorXd innovation = zVector - predicted;

    // 使用平滑航向角替代原始测量航向角
    innovation(4) = heading_diff;

    // 5. EKF更新
    
    // 避免与局部变量innovation混淆，使用this->innovation调用成员函数
    const MatrixXd& S_inv = inv_S ? *inv_S : this->innovation(measure).inverse();
    const MatrixXd H = measure.H(x_);
    // 4. 卡尔曼增益计算（优化运算顺序）
    MatrixXd PHt = P_ * H.transpose();
    MatrixXd K = PHt * S_inv;

    // 6. 状态更新
    x_.noalias() += K * innovation;

    // 7. 确保航向角在[0, 2π)范围内
    x_(4) = normalizeHeading(x_(4));
    // 添加额外的范围检查
    assert(x_(4) >= 0 && x_(4) < 2*M_PI && "Heading angle out of range!");

    // 6. 协方差更新（Joseph form优化）
    const MatrixXd IKH = MatrixXd::Identity(P_.rows(), P_.cols()) - K * H;
    const MatrixXd KRKT = K * measure.R() * K.transpose().eval(); // eval避免aliasing
    P_ = IKH * P_ * IKH.transpose() + KRKT;

    // 9. 强制对称（数值稳定性）
    P_ = 0.5 * (P_ + P_.transpose().eval());


    // 判断是否为异常值
    bool is_outlier = isHeadingOutlier(x_(4));
    // 如果历史缓存为空或者不是异常值，则添加到历史缓存中
    if (heading_history_.empty() || !is_outlier) {
        // 如果缓存已满，则移除最早的数据
        if (heading_history_.size() >= HEADING_HISTORY_SIZE) {
            heading_history_.erase(heading_history_.begin());
        }

        // 添加新的航向角
        heading_history_.push_back(x_(4));
    }
}

/**
 * @description: 计算测量函数H矩阵
判断状态协方差矩阵P是否为空
若P为空则直接对测量噪声R对称化后返回
否则计算HPH^T + R的协方差矩阵并强制对称化后返回
 * @param {ConstantVelocity&} measure
 * @return {*}
 * */
MatrixXd Density::innovation(const ConstantVelocity& measure) const {
    // cout<<"x_: row " << x_.rows() <<" * " << x_.cols()  << endl;
    // cout<<"x_: \n" << x_ << endl;
    MatrixXd H = measure.H(x_);
    // cout<<"H: \n" << H << endl;
    // cout<<"P_: \n" << this->P() << endl; // TODO　为空
    // cout<<"R: \n" << measure.R() << endl;
    if(this->P().rows() == 0){
        MatrixXd S = measure.R();
        S = 0.5 * (S + S.transpose());
        return S;
    }
    else{
        MatrixXd S = H * P_ * H.transpose() + measure.R();
        S = 0.5 * (S + S.transpose());
        return S;
    }
}

void Density::print() const {
        std::cout << "Density x_ : " << x_ << std::endl;
        std::cout << "Density P_ : " << P_ << std::endl;
    }
