/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-11-12 09:54:25
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-04-25 09:58:34
 * @FilePath: /src/perception/fusiontracking/src/MHT/utils/density.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#ifndef _DENSITY_H_
#define _DENSITY_H_

#include <iostream>
#include <vector>
#include <string>
#include <memory>

#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Core>
#include <eigen3/Eigen/Geometry>
#include <eigen3/Eigen/Cholesky>

#include "../../MHT/models/motionmodel.h"
#include "../../MHT/models/measmodel.h"
#include "generation.h" //src/utils/
#include "common_msgs/sensorobject.h"

using namespace std;
using namespace Eigen;

class Density {
public:
    common_msgs::sensorobject xObject_;
public:
    // 添加默认构造函数
    Density();
    Density(const common_msgs::sensorobject& x, const MatrixXd& P);

    VectorXd x() const;
    MatrixXd P() const;

    void set_x(const Eigen::VectorXd& x);
    void set_P(const MatrixXd& P);

    string to_string() const;

    bool operator==(const Density& other) const ;

    double ln_mvnpdf(const common_msgs::sensorobject& x) const;

    vector<int> gating(vector<common_msgs::sensorobject>& Z, const ConstantVelocity& measure,
            double size2, const MatrixXd* inv_S = nullptr, bool bool_index = false);

    double predicted_likelihood(common_msgs::sensorobject& z, const ConstantVelocity& measure, const MatrixXd* S = nullptr);

    void predict(const ConstantVelocity2D& motion, double dt);

    void update(const common_msgs::sensorobject& z, const ConstantVelocity& measure, const MatrixXd* inv_S = nullptr) ;
    void print() const ;

private:
    MatrixXd innovation(const ConstantVelocity& measure) const;

private:
    Eigen::VectorXd x_;
    MatrixXd P_;
    static const double log2pi_;

    // 航向角平滑处理相关参数
    static const int HEADING_HISTORY_SIZE = 25;  // 航向角历史缓存大小
    std::vector<double> heading_history_;  // 航向角历史值缓存

    // 航向角处理相关方法
    double normalizeHeading(double heading) const;
    double computeHeadingDifference(double heading1, double heading2) const;
    double getSmoothedHeading(double measured_heading);
    bool isHeadingOutlier(double measured_heading) const;
};

#endif
