/********************************************************************************
* @author: shuang<PERSON>n han
* @date: 2023/7/26 上午10:46
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_OBJECT_H
#define SRC_OBJECT_H
#include <iostream>
#include <deque>
#include <set>
#include <functional>
#include <stdlib.h>
#include <fstream>
#include <numeric>
#include <algorithm>
#include <filesystem>
#include <boost/filesystem.hpp>
#include <future>

#include "../../commonlibrary/src/common.h"
#include "../../commonlibrary/src/coordinateTransformation/wgs84_utm.h"
#include "../../commonlibrary/src/computeAssignmentMatrix.h"
#include "../../commonlibrary/src/Hungarian.h"
#include "../../commonlibrary/src/configManager/configManager.h"
#include "../../commonlibrary/src/sensorobjects/stationobjects.h"
#include "../../commonlibrary/src/sensorobjects/gps.h"

#include <ros/ros.h>
#include "ObjectTracking.h"
#include <ros/package.h>
#include <sensor_msgs/PointCloud2.h>

#include <pcl/point_cloud.h>
#include <pcl_ros/point_cloud.h>
#include <pcl/point_types.h>

#include "common_msgs/sensorgps.h"
#include "common_msgs/point3d.h"
#include "common_msgs/oburoadpoint.h"
#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"
#include "common_msgs/cloudpant.h"
#include "common_msgs/cloudpants.h"
#include "common_msgs/obupant.h"
#include "common_msgs/obupants.h"
#include "common_msgs/fusiontrackingobject.h"
#include "common_msgs/fusiontrackingobjects.h"

#include "./MHT/scan_volume.h"

using namespace std;

#define DELTA_DIS (3.0) //纵坐标阈值      m raw 6.0 20221124 1.0
#define DELTA_LAT (2.0) //横坐标阈值      m raw1.0 20221124 0.5

struct ObjectTrackingStruct
{
	long timestamp; // timestamp
	std::vector<ObjectTracking>* m_trackingObjectsFrame; // tracking
};


class ObjectFusionTracking {
public:
	Common m_common;
	wgs84_utm m_wgs84Utm;
	ComputeAssignmentMatrix m_computeAssignmentMatrix;
	HungarianAlgorithm m_hungarianAlgorithm;
	SensorAxisTransformation m_sensorAxisTransformer;
	boost::shared_ptr<ConfigManager::ConfigManager> m_pConfigManager;
	std::shared_ptr<spdlog::logger> m_pLogger;
	static std::map<uint32_t, int> m_obuLidarTrackedObjectIDMap;
	
	ObjectFusionTracking();
	~ObjectFusionTracking();
	void setNodeHandle(const ros::NodeHandle& nodeHandle);
	void setConfigManager(boost::shared_ptr<ConfigManager::ConfigManager>& pConfigManager);
	void setLoggerManager(const std::shared_ptr<spdlog::logger>& pLogger);
	void setLidarObjects(const common_msgs::sensorobjects& objects);
	void setRadarObjects(const common_msgs::sensorobjects& objects);
	void serRadarParticipants(const common_msgs::sensorobjects& objects);
	void setCloudObjects(const common_msgs::sensorobjects& objects);
	void setOBUObjects(const common_msgs::sensorobjects& objects);
	void setGPS(const common_msgs::sensorgps& gps, const std::vector<double>& selfCarSpeed);
	
	void objectFusion();
	void getProcessedRadarObjects(common_msgs::sensorobjects& matchedRadarobjects, common_msgs::sensorobjects& unmatchedRadarobjects);
	void showProcessedRadarObjects(const common_msgs::sensorobjects& radarobjects, ros::Publisher& pubRadarObjects);
	bool objectSort(const common_msgs::sensorobject& obj1, const common_msgs::sensorobject& obj2);
	void lidarRadarFusion(common_msgs::sensorobject& singleLidarObject, const common_msgs::sensorobjects& radarobjects);
	template <typename T>
	bool IsPointInBox(const common_msgs::point3d& cornerPoint0, const common_msgs::point3d& cornerPoint1,
                  const common_msgs::point3d& cornerPoint2, const common_msgs::point3d& cornerPoint3,
                  const T& object);
	template <typename T>
	float getCross(const common_msgs::point3d& cornerPoint1, const common_msgs::point3d& cornerPoint2,
                             const T& object);
	void getLidarOBUTimeStep();
	void objectsTracking();
	void trackingInit(float& time);
	void manageTracks(const common_msgs::sensorobjects& secondSensorObjects, float& time);
	void objectAssociation(const float& iouThreshold);
	void secondObjectAssociation();
	void objectUpdate(const float& relevancyThreshold, const unsigned int& updateCount);
	void mergeTrackedObjects();
	void mergeObjects(const ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2, bool& removeTarget);
	void mergeTrackedObjectByPoints(const ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2, bool& removeTarget);
	void mergeTrackedObjectByIOU(const ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2, bool& removeTarget);
	void mergedObjectsUpdate(ObjectTracking& trackedObject1, const ObjectTracking& trackedObject2);
	void updateClassification(const int& trackerIndex, const int& detectionIndex, const int& updateCount);
	void update8Corners();
	void removeInvalidObjects();
	void saveObjectTrackedInfo();
	int transformDetectionClass2LabelClass(const int& objectClass);
	void publishTrackObject();
	common_msgs::sensorobjects getTrackedObjects();
	int getTrackedObjectsSize();
    bool isMotorVehicle(const int& classification);

	void runV2IProcess();
	void fixFusionInfo(common_msgs::sensorobjects& curTrackingObjects);
	void v2iObjectFusion(const common_msgs::sensorobjects& curTrackingObjects, common_msgs::sensorobjects& obuobjects);
	void distanceMatch(common_msgs::sensorobjects& obuobjects,
		vector<std::vector<int>>& lidarOBUObjectIndexsMatched,
        vector<int>& lidarObjectIndexUnmatched, vector<int>& obuObjectIndexUnmatched, int eraseCount);
	void mergeLidarOBUObjects(const common_msgs::sensorobjects& obuobjects,
							const std::vector<int>& obuObjectIndexUnmatched,
                          common_msgs::sensorobjects& mergedObjects);
	void trackingObjects2SensorObjects(common_msgs::sensorobjects& sensorobjces);
	common_msgs::sensorobjects getV2IFusionObjectsRVIZ();
	int getAllLidarOBUObjectsSize();
	bool isInStationRange(const double carLatitude, const double carLontitude, 
					   const double distanceFromStation);
	// MHT
	unordered_map<int, shared_ptr<Track>> tracks_; // {跟踪ID：跟踪目标Track}
	vector<unordered_map<int, int>> ghyps_; // Vetor({跟踪ID：假设ID-LocalHypothesis})
    vector<double> gweights_;
	int alllhpySize = 0;
	CartesianVolume m_volume;
	TargetPosition_CV2D m_targetmodel;
	
	CartesianVolume generateVolume();
	void MHTTracking();
	void MHTProcess(std::vector<common_msgs::sensorobject>& objectsMeasurements, const Volume& volume, TargetPosition_CV2D& targetmodel, double t_now,const double& timeStamp,const double& timeDiff);
	void update_global_hypotheses(unordered_map<int, unordered_map<int, unordered_map<int, shared_ptr<LocalHypothesis>>>>& track_updates, 
		vector<common_msgs::sensorobject>& Z, TargetPosition_CV2D& targetmodel, const Volume& volume, int max_nof_hyps, double weight_threshold, double t_now);
	pair<unordered_map<int, int>, double> create_track_trees(vector<common_msgs::sensorobject>& detections, 
			const Volume& volume, TargetPosition_CV2D& targetmodel, double t_now);
	std::pair<vector<double>,vector<unordered_map<int, int>>> prune_dead(const vector<double>& weights, const vector<unordered_map<int, int>>& ghyps);
	std::pair<vector<double>, vector<unordered_map<int, int>>> hypothesis_prune(const vector<double>& weights, const vector<unordered_map<int, int>>& hypotheses, double threshold);
	std::pair<vector<double>, vector<unordered_map<int, int>>> hypothesis_cap(const vector<double>& weights, const vector<unordered_map<int, int>>& hypotheses, int M);
	double _unnormalized_weight(const unordered_map<int, int>& ghyp);
	void terminate_tracks();
	void mergeCloseTargets(unordered_map<int,shared_ptr<Density>>& estimations);
	unordered_map<int,shared_ptr<Density>>  estimates(bool only_confirmed = true);
	void showTrackedObjectsInRviz(const unordered_map<int, shared_ptr<Density>>& estimations);

	// 自定义vector<pair>的哈希函数
	struct VectorHash {
		std::size_t operator()(const std::vector<std::pair<int, int>>& v) const {
			std::size_t seed = 0;
			for (const auto& p : v) {
				seed ^= std::hash<int>()(p.first) + 0x9e3779b9 + 
						(seed << 6) + (seed >> 2);
				seed ^= std::hash<int>()(p.second) + 0x9e3779b9 + 
						(seed << 6) + (seed >> 2);
			}
			return seed;
		}
	};
	// 自定义比较结构（按向量内容逐元素比较）
	struct VectorCompare {
		bool operator()(const std::vector<std::pair<int, int>>& a,
					const std::vector<std::pair<int, int>>& b) const {
			return a < b; // 默认按元素字典序比较
		}
	};
	
private:
	using c_StationObjects = SENSOROBJECTS::StationObjects<common_msgs::obupants>;
	boost::shared_ptr<c_StationObjects> m_pStationObjects;
	using c_SensorGPS = SENSOROBJECTS::SensorGPS;
	boost::shared_ptr<c_SensorGPS> m_pSensorGPS;

	ros::NodeHandle m_nh;
	ros::Publisher pub_track_results;
	ros::Publisher pub_track_results8CornerForRVIZ;
	ros::Publisher pub_track_clusterPointcloud;
	ros::Publisher pub_v2iFusionObject;
	ros::Publisher pub_v2iFusionObjectRVIZ;
	ros::Publisher pub_v2iFusionObjectPAD;
	ros::Publisher pub_matchedRadarObjects;
	ros::Publisher pub_unmatchedRadarObjects;
	ros::Publisher pub_MHTTrackedObjectsBBX;
	
	common_msgs::sensorobjects m_lidarObjects;      //lidar data
	common_msgs::sensorobjects m_radarObjects;      //radar data
	common_msgs::sensorobjects m_radarParticipants;
	common_msgs::sensorobjects m_cloudObjects; //cloudpants data
	common_msgs::obupants m_obupantsMsg;        //obu data
	common_msgs::sensorobjects m_obuObjectMsg; //cloudpants data
	common_msgs::sensorobjects m_obuObjects; //cloudpants data
	common_msgs::sensorgps    m_gps;       // gps data
	common_msgs::sensorobjects m_trackedObjects;    // 发给决策
	common_msgs::sensorobjects m_trackedObjects8CornerForRVIZ;
	common_msgs::sensorobjects m_v2ifusionObjects;
	common_msgs::sensorobjects m_v2ifusionObjectsRVIZ;
	common_msgs::sensorobjects m_v2ifusionObjectsPAD;
	common_msgs::sensorobjects m_matchedRadarobjects;
	common_msgs::sensorobjects m_unmatchedRadarobjects;

	common_msgs::sensorobjects m_preDetectionObjects;
	
	int m_lidarOBUSyncTimeStep;
	ros::Time m_currentROSTime;
	
	std::vector<int> m_unmatchedRadarObjectIndex;
	std::vector<ObjectTracking> m_trackingObjectsVector;
	std::vector<std::shared_ptr<ObjectTracking>> m_pTrackingObjectsVector;
	
	std::string m_folderPath;
	bool m_isInitial;
	bool m_isStartTracking;
	long m_curFrameStamp;
	long m_lastFrameStamp;
	double m_timeDiff;
	std::string m_timeStampPath;
	std::vector<double> m_selfCarSpeed; //三维
	std::vector<double> m_selfCarUTMPosition; //三维
	
	vector<vector<double>> m_iouMatrix;
	vector<int> m_assignment;
	set<int> m_unmatchedDetections;
	vector<int> v_unmatchedDetectionsIndex_;
	set<int> m_unmatchedTrajectories;
	vector<int> v_unmatchedTrajectoriesIndex_;
	set<int> m_allItems;
	set<int> m_matchedItems;
	vector<vector<int>> m_matchedPairs;

	// v2i
	std::deque<ObjectTrackingStruct> m_trackingObjectsFrameDeque;
	std::vector<std::vector<double>> m_zombie_position;
	std::vector<int> m_deletedLidarObjectID;
	bool m_isInStationRange = false;

	int m_allCarObuObjectsSize;

	// MHT
	int max_nof_hyps = 5; // 最多假设数量
	double hyp_weight_threshold = log(0.1); // raw 0.05 假设权重阈值-1.301029995664
	
	
	
	
};




#endif //SRC_OBJECT_H
