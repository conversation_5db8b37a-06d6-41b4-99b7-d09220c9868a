/********************************************************************************
* @author: shuang<PERSON><PERSON> han
* @date: 2023/6/21 下午5:48
* @version: 1.0
* @description: 
********************************************************************************/


#include "fusiontracking.h"

using namespace std;

/***
* 1.订阅相关话题，做数据缓存（radar分两个容器缓存，100米外用于障碍物检测，100米内用于障碍物融合）
 *2.进行坐标转换（独立类）
 *3.多传感器目标融合
 * 4.目标跟踪
 * */

FusionTracking::FusionTracking(ros::NodeHandle nodeHandle){
	m_nh = nodeHandle;

	m_gpstime = 0;
	m_curLidarStamp = 0;
	
	m_lidar2carBackRFU_X = 0;
	m_lidar2carBackRFU_Y = 0;
	m_lidar2carBackRFU_Z = 0;
	
	m_lidarFLU2carBackRFU_rollDegree = 0;
	m_lidarFLU2carBackRFU_pitchDegree = 0;
	m_lidarFLU2carBackRFU_yawDegree = 0;
	
	m_lidarRFU2carBackRFU_rollDegree = 0;
	m_lidarRFU2carBackRFU_pitchDegree = 0;
	m_lidarRFU2carBackRFU_yawDegree = 0;
	
	m_lidarRFU2carBackRFU_eulerXYZDegree = Eigen::Vector3d::Zero();
	m_lidarFLU2carBackRFU_eulerXYZDegree = Eigen::Vector3d::Zero();
	m_lidar2carBackRFU_translation = Eigen::Vector3d::Zero();
	
	memset(&m_lidarMsg, 0, sizeof(m_lidarMsg));
	memset(&m_radarMsg, 0, sizeof(m_radarMsg));
	
	m_lidarMsg.obs.clear();
	m_radarMsg.obs.clear();

	m_selfCarSpeed.reserve(3);
	
	std::string nodeName = ros::this_node::getName(); //获取节点名称
	int nameIndex = nodeName.find_first_not_of("/");
	std::string nodeNameNoChar = nodeName.substr(nameIndex); //得到"sensrorradar"
	std::string nodePath = ros::package::getPath(nodeNameNoChar);
	std::string packagePath = nodePath + "/launch/params.yaml";
	m_pConfigManager = boost::make_shared<ConfigManager::ConfigManager>(packagePath);

	m_pcLogger = std::make_shared<Logger>(m_pConfigManager);
	m_pLogger = m_pcLogger->getLogger();

	m_pConfigManager->printParams(m_pLogger);

	// 检查日志记录器是否为异步类型
    if (typeid(*m_pLogger) == typeid(spdlog::async_logger)) {
        m_pLogger->info("Asynchronous logging is enabled.");
    } else {
        m_pLogger->info("Asynchronous logging is not enabled.");
    }

	m_pLogger->info("nodeName: {}", nodeName);
	m_pLogger->info("nodePath: {}", nodePath);
	m_pLogger->info("packagePath: {}", packagePath);

	m_nh.param(m_pConfigManager->m_carName + "/lidar2carBackRFU_X",m_lidar2carBackRFU_X,m_lidar2carBackRFU_X); //
	m_nh.param(m_pConfigManager->m_carName + "/lidar2carBackRFU_Y",m_lidar2carBackRFU_Y,m_lidar2carBackRFU_Y); //
	m_nh.param(m_pConfigManager->m_carName + "/lidar2carBackRFU_Z",m_lidar2carBackRFU_Z,m_lidar2carBackRFU_Z); //
	m_nh.param(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_rollDegree",m_lidarFLU2carBackRFU_rollDegree,m_lidarFLU2carBackRFU_rollDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_pitchDegree",m_lidarFLU2carBackRFU_pitchDegree,m_lidarFLU2carBackRFU_pitchDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_yawDegree",m_lidarFLU2carBackRFU_yawDegree,m_lidarFLU2carBackRFU_yawDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_rollDegree",m_lidarRFU2carBackRFU_rollDegree,m_lidarRFU2carBackRFU_rollDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_pitchDegree",m_lidarRFU2carBackRFU_pitchDegree,m_lidarRFU2carBackRFU_pitchDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_yawDegree",m_lidarRFU2carBackRFU_yawDegree,m_lidarRFU2carBackRFU_yawDegree); //
	

	m_pLogger->info("carName: {}", m_pConfigManager->m_carName);
	m_pLogger->info("Copyright©2021-2023 VANJEE Technology. All rights reserved");
	if(m_pConfigManager->m_isUseHDMap){
		m_hdMapPicture = cv::imread(nodePath + "/data/" + m_pConfigManager->m_hdMapName,0);
		if(m_hdMapPicture.empty()){
			m_pLogger->warn("hdmap is not found or hdmap is corrupted, please check!!");
		}
		else{
			m_pLogger->info("m_hdMapPicture.size = {} * {}", m_hdMapPicture.cols,m_hdMapPicture.rows);
		}
		
	}
	
	m_lidarRFU2carBackRFU_eulerXYZDegree << m_lidarRFU2carBackRFU_rollDegree, m_lidarRFU2carBackRFU_pitchDegree, m_lidarRFU2carBackRFU_yawDegree;
	m_lidarFLU2carBackRFU_eulerXYZDegree << m_lidarFLU2carBackRFU_rollDegree, m_lidarFLU2carBackRFU_pitchDegree, m_lidarFLU2carBackRFU_yawDegree;
	m_lidar2carBackRFU_translation << m_lidar2carBackRFU_X, m_lidar2carBackRFU_Y, m_lidar2carBackRFU_Z;
	
	m_objectFusionTracking.setNodeHandle(m_nh);
	m_objectFusionTracking.setConfigManager(m_pConfigManager);
	m_objectFusionTracking.setLoggerManager(m_pLogger);

	if(m_pConfigManager->m_isSaveTimeUseFile){
		m_pTimeUseFileWriter = boost::make_shared<FileWriter>(m_pConfigManager);
		std::string timestampStr = std::to_string(long(ros::Time::now().toSec() * 1000)) + ".csv";
		m_pTimeUseFileWriter->createFile(timestampStr);
		std::string dataHeader = "frameCount,trackedObjectSize,trackingTimeUse,allLidarOBUObjectSize,allLidarOBUTimeUse,alllhpySize\n";
		m_pTimeUseFileWriter->writeHeader(dataHeader);
	}
	

	//订阅话题
	sub_lidar = m_nh.subscribe("sensorlidar2",100,&FusionTracking::SubCallback_lidar,this);
	sub_radar = m_nh.subscribe("sensorradar",100, &FusionTracking::SubCallback_radar,this);
	sub_gps = m_nh.subscribe("sensorgps", 200, &FusionTracking::SubCallback_gps,this);
	sub_slamgps = m_nh.subscribe("common_tcp/slamgps",200, &FusionTracking::SubCallback_slamgps, this);
	sub_behaviordecision = m_nh.subscribe("/behaviordecision",100, &FusionTracking::SubCallback_behaviordecision, this);
	sub_cloudobjects = m_nh.subscribe("/cloud/cloudobjects", 200, &FusionTracking::SubCallback_cloudpants,this);
	
	//发布话题
	pub_radarCloudRaw = m_nh.advertise<sensor_msgs::PointCloud2>("/objectfusion/sensorradarCloud", 100);
	pub_lidarObjectBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/lidarObjectBBX",100);
	pub_radarObjectBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/radarObjectBBX",100);
	pub_cloudObjectsBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/cloudObjectsBBX", 100);
	pub_fusionObjectBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/fusionObjectBBX",100);
	pub_trackingObjectBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/trackingObjectBBX",100);
	pub_v2iFusionObjectBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/v2iFusionObjectBBX",100);
	pub_fusiontrackingElapsedtime = m_nh.advertise<common_msgs::elapsedtime>(nodeName + "/elapsedtime",100);

	m_pcMatchedRadarObjectsViewer = boost::make_shared<c_cSenObjectsViewer>(m_nh, nodeName + "/matchedRadarObjectsBBX", VISUALIZATION::ShowType::Radar_MatchedLidar);
	m_pcUnmatchedRadarObjectsViewer = boost::make_shared<c_cSenObjectsViewer>(m_nh, nodeName + "/unmatchedRadarObjectsBBX", VISUALIZATION::ShowType::Radar_UnMatchedLidar);
}

FusionTracking::~FusionTracking(){
	m_pcMatchedRadarObjectsViewer = nullptr;
	m_pcUnmatchedRadarObjectsViewer = nullptr;
	m_pConfigManager = nullptr;
	m_pcLogger = nullptr;
	m_pLogger = nullptr;
};

void FusionTracking::run() {
	ros::Rate rate(30);
	
	while(ros::ok()) {
		ros::spinOnce();
		if(!m_pConfigManager->m_isUseRosBag){
			fusiontracking();
		}
		rate.sleep();
	}

}

void FusionTracking::fusiontracking() {
	if(m_pConfigManager->m_isSimulateMode)
		return;

	//// 以lidar为主传感器,必须以lidar数据开始，如果第一帧是radar或者其他传感器退出
	m_lidarMsg.obs.clear();
	std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
	if(!m_lidarMsgDeque_.empty()) {
		// std::lock_guard<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsg = m_lidarMsgDeque_.front();
		m_lidarMsgDeque_.pop_front();
		m_pLogger->info("m_lidarMsgDeque_ size: {}", m_lidarMsgDeque_.size());
	}
	else{
		m_pLogger->warn("m_lidarMsgDeque_ is empty");
		return;
	}
	lidarLock.unlock();

	static int lidarFrameCount = 0;
	m_pLogger->info("\n\n第 {} 帧lidar融合跟踪过程...................................", lidarFrameCount++);
	m_radarVector.clear();//20221128

	long curStartStamp = ros::Time::now().toSec() * 1000;
	float timeGap = curStartStamp - m_lidarMsg.timestamp;
	m_pLogger->info("curStartROSTimestamp = {:.3f}, m_lidarMsg timestamp = {:.3f}, timeGap(ms) = {:.3f}", curStartStamp / 1000.0, m_lidarMsg.timestamp / 1000.0, timeGap);
	if(!m_lidarMsgDeque_.empty()){
		m_pLogger->info("m_lidarMsgDeque_.front() timestamp = {:.3f}", m_lidarMsgDeque_.front().timestamp / 1000.0);
	}
	else{
		m_pLogger->info("m_lidarMsgDeque_.empty()");
	}
	m_curLidarStamp = m_lidarMsg.timestamp;

	long getLidarEndTime =  ros::Time::now().toSec()*1000;
	double getLidarTimeUse = (double)(getLidarEndTime - curStartStamp);
	m_pLogger->info("[getLidarTimeUse use time(ms)] = {}", getLidarTimeUse);

	//// lidar-GPS-radar-cloudObject时间同步
	sensorTimeSynchro();

	// GPS进入失效区域，且SLAM定位数据不为空，使用SLAM定位数据
	std::unique_lock<std::mutex> slamgpsLock(m_slamgpsMutex);
	if(m_gpsMsg.status == 0 && !m_slamgpsMsgDeque_.empty()){
		m_gpsMsg.lon = m_slamgpsMsg.lon;
		m_gpsMsg.lat = m_slamgpsMsg.lat;
		m_gpsMsg.heading = m_slamgpsMsg.heading;
	}
	slamgpsLock.unlock();
	
	m_selfCarSpeed.clear();
	//第一辆车发出NED,看做是ENU顺序,三轴角度和速度都看作是ENU
	m_selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading},
	                                                            Eigen::Vector3d{m_gpsMsg.speedE, m_gpsMsg.speedN, -m_gpsMsg.speedD});


	
	//lidar目标长宽对调，value值为1
	std::vector<int> bicycleIndex;
	std::vector<int> clucterObjectDeleteIndex;
	std::vector<int> objectOutsideHDMapIndex;
	int lidarObjectsSize = m_lidarMsg.obs.size();
	m_pLogger->info("lidar obs raw size = {}", lidarObjectsSize);
	#pragma omp parallel for
	for(int i = 0; i < lidarObjectsSize; i++){
		common_msgs::sensorobject& lidarObject = m_lidarMsg.obs[i];
		float width = lidarObject.width, length = lidarObject.length;
		lidarObject.width = length;
		lidarObject.length = width;
		lidarObject.value = COMMON::SensorType::LIDAR;
		lidarObject.radarIndex = UINT8_MAX; // radarIndex 未融合的目标设置索引为255
		lidarObject.radarObjectID = UINT8_MAX;

		double angleRad = m_common.normalizeAngle(lidarObject.azimuth);
		lidarObject.azimuth = angleRad;

		// 类别错误修正
		if(lidarObject.classification == COMMON::LidarDetectionClassification::Cone && 
			lidarObject.length > 1 && lidarObject.width > 1){
				m_pLogger->info("lidar obs id = {}, classification = {}, change to car", lidarObject.id, (int)lidarObject.classification);
				lidarObject.classification = COMMON::LidarDetectionClassification::Car;
		}

		// 记录ID
		if(m_pConfigManager->m_isUseHDMap && !isObjectInHDMap(lidarObject)){
			objectOutsideHDMapIndex.emplace_back(i);
		}
		else if(lidarObject.confidence < 0.30){// 4.9 v2i红绿灯场景调整到8 //记录需要删除的自行车和行人
			bicycleIndex.emplace_back(i);
		}
		else if(lidarObject.classification == COMMON::LidarDetectionClassification::Unknown){
			lidarObject.confidence = 0.42;
			
			auto xMinMax = std::minmax_element(lidarObject.points.begin(), lidarObject.points.end(),[](const common_msgs::point3d& point1,const common_msgs::point3d& point2){
				return point1.x < point2.x;
			});
			float xMinMaxDistance = (xMinMax.second)->x - (xMinMax.first)->x;
			auto yMinMax = std::minmax_element(lidarObject.points.begin(), lidarObject.points.end(),[](const common_msgs::point3d& point1,const common_msgs::point3d& point2){
				return point1.y < point2.y;
			});
			float yMinMaxDistance = (yMinMax.second)->y - (yMinMax.first)->y;
			float widthTemp = lidarObject.width, lengthTemp = lidarObject.length;
			lidarObject.width = min(widthTemp, lengthTemp);
			lidarObject.length = max(widthTemp, lengthTemp);
			lidarObject.width = min(lidarObject.width, min(xMinMaxDistance, yMinMaxDistance));
			lidarObject.length = min(lidarObject.length, max(xMinMaxDistance, yMinMaxDistance));
			// assert(lidarObject.width <= lidarObject.length);

			// 找到最优的航向角（CarBackRFU下）
			findOptimalYaw(lidarObject);

			// 存储异常的聚类目标
			// bool isClusterObjectDelete = isClusterObjectsDelete(lidarObject);
			// if(isClusterObjectDelete)
				clucterObjectDeleteIndex.emplace_back(i);
		}

		// TODO 暂时只保留Bus
		// if(lidarObject.classification != COMMON::LidarDetectionClassification::Bus){
		// 	bicycleIndex.emplace_back(i);
		// }

	}
	// 删除高精地图外的聚类目标
	std::reverse(objectOutsideHDMapIndex.begin(),objectOutsideHDMapIndex.end());
	for (int index: objectOutsideHDMapIndex) {
		m_lidarMsg.obs.erase (m_lidarMsg.obs.begin () + index);
	}

	// 删除异常的聚类目标
	std::reverse(clucterObjectDeleteIndex.begin(),clucterObjectDeleteIndex.end());
	for (int index: clucterObjectDeleteIndex) {
		m_lidarMsg.obs.erase (m_lidarMsg.obs.begin () + index);
	}
	
	//路口场景不滤除车旁边的自行车
	// if(m_behaviordecisionMsg.drivebehavior != 4 && m_behaviordecisionMsg.drivebehavior != 5 &&
	//    m_behaviordecisionMsg.drivebehavior != 9 && m_behaviordecisionMsg.drivebehavior != 11){
		std::reverse(bicycleIndex.begin(),bicycleIndex.end());
		if(bicycleIndex.size() < m_lidarMsg.obs.size()) {
			for (int index: bicycleIndex) {
				m_lidarMsg.obs.erase (m_lidarMsg.obs.begin () + index);
			}
		}
		else{
			m_pLogger->warn("bicycleIndex.size() > lidarObjectsSize, bicycleIndex.size = {}, lidarObjectsSize = {}", 
							bicycleIndex.size(), lidarObjectsSize);
		}
	// }
	// else{
	// 	cout<<FGRN("is in intersection") << endl;
	// }
	
	m_pLogger->info("lidar obs filtered size = {}", m_lidarMsg.obs.size());
	sensorobjectsBoxShow(m_lidarMsg, COMMON::SensorType::LIDAR);
	
	if(m_pConfigManager->m_carNumber == 1){
		transRadar_carFrontRFU2CarBackRFU(m_radarMsg);//20221129 radar后轴右前上转到车前保险杠右前上（ 纵向-3.6 ） ，速度右前上 hongqi1:carFrontRFU
	}
	// radar生成8角点：radar目标分为两部分，一部分用于lidar-obu融合，一部分用于独立目标
	m_pLogger->info("radar obs raw size = {}", m_radarMsg.obs.size());
	common_msgs::sensorobjects radarParticipants;
	radarParticipants.isvalid = m_radarMsg.isvalid;
	radarParticipants.timestamp = m_radarMsg.timestamp;
	radarParticipants.gpstime = m_radarMsg.gpstime;
	int radarObjectsCount = 0;
	vector<double> boxInfo(7);
	// 使用并行化处理每个对象，如果环境支持
    #pragma omp parallel for
	for(auto radarObjectIter = m_radarMsg.obs.begin(); radarObjectIter != m_radarMsg.obs.end();){
		radarObjectIter->z = 1;
		radarObjectIter->height = 1;
		
		double angleRad = m_common.normalizeAngle(radarObjectIter->azimuth);
		radarObjectIter->azimuth = angleRad;

		float headingAnticlockwise = radarObjectIter->azimuth; //TODO: radar100米以上目标作为单独目标由于暂时没有航向角,这里设置为0，
		if(radarObjectIter->length == 0 || radarObjectIter->width == 0){
			radarObjectIter->length = 4;
			radarObjectIter->width = 2.5;
		}
		boxInfo = {radarObjectIter->x, radarObjectIter->y, radarObjectIter->z,
		                          radarObjectIter->length, radarObjectIter->width, radarObjectIter->height,
		                          headingAnticlockwise};
		vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
		for(const auto& singleCornerPointVector : eightCornerPoints){
			common_msgs::point3d singleCornerPoint;
			singleCornerPoint.x = singleCornerPointVector[0];
			singleCornerPoint.y = singleCornerPointVector[1];
			singleCornerPoint.z = singleCornerPointVector[2];
			
			radarObjectIter->points.emplace_back(std::move(singleCornerPoint));
		}
		
		if(m_pConfigManager->m_isUseRadarObjects && abs(radarObjectIter->y) > m_pConfigManager->m_radarAsObjectDistance && abs(radarObjectIter->x) < 10){//
			// radar检测目标作为交通参与者
			++radarObjectsCount;
			radarParticipants.obs.emplace_back(std::move(*radarObjectIter));
			radarObjectIter = m_radarMsg.obs.erase(radarObjectIter);
		}
		else{
			++radarObjectIter;
		}
	}
	m_pLogger->info("radarParticipants size = {}, radarObjectsCount = {}", radarParticipants.obs.size(), radarObjectsCount);
	sensorobjectsBoxShow(m_radarMsg, COMMON::SensorType::RADAR);

	// cloudObjects预处理
	cloudpantsPreprocess(m_cloudpantsMsg, m_cloudObjectMsg);
	sensorobjectsBoxShow(m_cloudObjectMsg, COMMON::SensorType::CLOUDPLATFORM);
	
	////目标融合
	m_gpstime = m_lidarMsg.timestamp;

	m_objectFusionTracking.setLidarObjects(m_lidarMsg);
	m_objectFusionTracking.setRadarObjects(m_radarMsg);
	m_objectFusionTracking.serRadarParticipants(radarParticipants);
	m_objectFusionTracking.setCloudObjects(m_cloudObjectMsg);
	m_objectFusionTracking.setGPS(m_gpsMsg, m_selfCarSpeed);
	
	long preprocessEndTime =  ros::Time::now().toSec()*1000;
	m_pLogger->info("开始融合.......");
	m_objectFusionTracking.objectFusion();
	sensorobjectsBoxShow(m_lidarMsg, COMMON::SensorType::FUSION);
	long fusionEndTime =  ros::Time::now().toSec()*1000;
	double fusionTimeUse = (double)(fusionEndTime - preprocessEndTime);
	double fusionTimeUse2 = (double)(fusionEndTime - curStartStamp);
	m_pLogger->info("[fusion use time(ms)] = {}, fusionTimeUse2(ms) = {}", fusionTimeUse, fusionTimeUse2);

	if(m_pConfigManager->m_isUseRosBag){
		common_msgs::sensorobjects matchedRadarobjects, unmatchedRadarobjects;
		m_objectFusionTracking.getProcessedRadarObjects(matchedRadarobjects, unmatchedRadarobjects);
		m_pLogger->info("lidar-radar show: matchedRadarobjects size = {}, unmatchedRadarobjects size = {}", matchedRadarobjects.obs.size(), unmatchedRadarobjects.obs.size());
		m_pcMatchedRadarObjectsViewer->visualize(matchedRadarobjects);
		m_pcUnmatchedRadarObjectsViewer->visualize(unmatchedRadarobjects);
	}
	
	m_pLogger->info("开始跟踪.......");
	// m_objectFusionTracking.objectsTracking();
  	m_objectFusionTracking.MHTTracking();
	
	long trackingEndTime =  ros::Time::now().toSec()*1000;
	double trackingTimeUse = (double)(trackingEndTime - fusionEndTime);
	double fusiontrackingTimeUse1 = (double)(trackingEndTime - curStartStamp);
	m_pLogger->info("[trackingTimeUse use time(ms)] = {}, fusion_tracking use time(ms) = {}", trackingTimeUse, fusiontrackingTimeUse1);

	int detectionObjectSize = m_lidarMsg.obs.size();
	int alllhpySize = m_objectFusionTracking.alllhpySize; // 所有假设的size
	
	// m_objectFusionTracking.publishTrackObject();
	// common_msgs::sensorobjects trackedOBjects = m_objectFusionTracking.getTrackedObjects();
	// sensorobjectsBoxShow(trackedOBjects, COMMON::SensorType::TRACKING);
	
	// m_objectFusionTracking.runV2IProcess();
	// const common_msgs::sensorobjects l_v2iFusionObjects = m_objectFusionTracking.getV2IFusionObjectsRVIZ();
	// sensorobjectsBoxShow(l_v2iFusionObjects, COMMON::SensorType::V2IFUSION);

	// float objectSize = m_objectFusionTracking.getTrackedObjectsSize();
	// long fusiontrackingEndTime =  ros::Time::now().toSec()*1000;
	// double fusiontrackingTimeUse = (double)(fusiontrackingEndTime - curStartStamp);
	// m_pLogger->info("fusiontracking use time: fusiontrackingTimeUse(ms) = {}, 跟踪目标数量 = {}, mean sort time = {:.3f}", 
	// 				fusiontrackingTimeUse, objectSize, fusiontrackingTimeUse / objectSize);
	// m_pLogger->info("fusiontracking start time:  {:.3f}, end time = {:.3f}", curStartStamp / 1000.0, fusiontrackingEndTime / 1000.0);
	// common_msgs::elapsedtime fusiontrackingElapsedtimeMsg;
	// fusiontrackingElapsedtimeMsg.time = fusiontrackingTimeUse;
	// pub_fusiontrackingElapsedtime.publish(fusiontrackingElapsedtimeMsg);
	
	static double maxFusionTrackingTimeUse = 0;
	if(fusiontrackingTimeUse1 > maxFusionTrackingTimeUse){
		maxFusionTrackingTimeUse = fusiontrackingTimeUse1;
		m_pLogger->warn("get max time: : maxFusionTrackingTimeUse(ms) = {}", maxFusionTrackingTimeUse);
	}
	static int maxTrackingObjectsSize = 0;
	if(maxTrackingObjectsSize < detectionObjectSize){
		maxTrackingObjectsSize = detectionObjectSize;
	}

	if(m_pConfigManager->m_isSaveTimeUseFile){
		int allLidarObuObjectsSize = m_objectFusionTracking.getAllLidarOBUObjectsSize();
		std::string fileData = std::to_string(lidarFrameCount) + "," + std::to_string(detectionObjectSize) + "," + std::to_string(trackingTimeUse) 
								+ "," + std::to_string(allLidarObuObjectsSize) + "," + std::to_string(fusiontrackingTimeUse1)
								+ "," + std::to_string(alllhpySize) + "\n";
		m_pTimeUseFileWriter->writeData(fileData);
	}
	
	
	m_pLogger->info("max fusiontracking use time(ms) = {}, maxTrackingObjectsSize = {}", 
					maxFusionTrackingTimeUse, maxTrackingObjectsSize);
}
/***
 * 订阅lidar检测话题
 * @param msg lidar检测目标
 */
void FusionTracking::SubCallback_lidar(const common_msgs::sensorobjects::ConstPtr &msg){
	static double preStamp = -1;
	// m_pLogger->info("Entering callback, preStamp = {:.3f}, current msg timestamp = {:.3f}", preStamp, msg->timestamp / 1000.0);
	if(preStamp < 0){
		preStamp = msg->timestamp;
		m_pLogger->info("callback first msg timestamp = {:.3f}", msg->timestamp / 1000.0);
	}
	else{
		float timeGap = msg->timestamp - preStamp;
		if(timeGap >= 200){
			m_pLogger->warn("检测丢帧: callback cur timestamp = {:.3f}, pre timestamp = {:.3f}, timeGap(ms) = {:.3f}",msg->timestamp / 1000.0, preStamp / 1000.0, timeGap);
		}
		preStamp = msg->timestamp;
	}
	if(m_pConfigManager->m_isUseRosBag){
		double curtimestamp = ros::Time::now().toSec() * 1000.0;
		float timeGap = curtimestamp - msg->timestamp;
		m_pLogger->info("sensorlidar2 callback current ros time = {:.3f}, msg timestamp = {:.3f}, timeGap(ms) = {:.3f}",curtimestamp/ 1000.0, msg->timestamp / 1000.0, timeGap);
		std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsgDeque_.emplace_back(*msg);
		lidarLock.unlock();
		fusiontracking();
	}
	else{
		double curtimestamp = ros::Time::now().toSec() * 1000.0;
		float timeGap = curtimestamp - msg->timestamp;
		m_pLogger->info("sensorlidar2 callback current ros time = {:.3f}, msg timestamp = {:.3f}, timeGap(ms) = {:.3f}",curtimestamp / 1000.0, msg->timestamp / 1000.0, timeGap);
		// std::lock_guard<std::mutex> lidarLock(m_lidarMutex);
		std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsgDeque_.emplace_back(*msg);
		lidarLock.unlock();
	}
}


/***
 * 订阅radar检测话题
 * @param msg radar检测目标
 */
void FusionTracking::SubCallback_radar(const common_msgs::sensorobjects::ConstPtr &msg){

	// std::lock_guard<std::mutex> radarLock(m_radarMutex);
	std::unique_lock<std::mutex> radarLock(m_radarMutex);
	m_radarMsgDeque_.emplace_back(*msg);
	if(m_radarMsgDeque_.size() > 100)
		m_radarMsgDeque_.pop_front();
	radarLock.unlock();
}

void FusionTracking::SubCallback_cloudpants(const common_msgs::cloudpants::ConstPtr &msg){
	std::unique_lock<std::mutex> cloudpantsLock(m_cloudpantsMutex);
	if(!m_pConfigManager->m_isSimulateMode){// 实车测试
		m_cloudpantsMsgDeque_.emplace_back(*msg);
		if(m_cloudpantsMsgDeque_.size() > 100)
			m_cloudpantsMsgDeque_.pop_front();
	}
	else{// 仿真模式
		// cloudObjectPants 时间同步
		{
		  m_pLogger->info("virtualObject-gps time sync: ");
		  std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
		  timeSynchro(m_gpsMsgDeque_, (*msg).timestamp, COMMON::SensorType::GPS);    //20221027 lidar检测与GPS信息的时间同步
		  if(!m_gpsMsgDeque_.empty()){
			  m_gpsMsg = m_gpsMsgDeque_.front();//20221027 lidar检测与GPS信息的时间同步
		  }
		  else{
			  m_pLogger->warn("WARNING: gps msg is empty, please check! ");
		  }
		  gpsLock.unlock();
		}
		m_selfCarSpeed.clear();
		m_pLogger->info("Car heading(deg) = {}", m_gpsMsg.heading);
		//第一辆车发出NED,看做是ENU顺序,三轴角度和速度都看作是ENU
		m_selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading},
																	Eigen::Vector3d{m_gpsMsg.speedE, m_gpsMsg.speedN, -m_gpsMsg.speedD});

		m_gpstime = m_gpsMsg.timestamp;//ros::Time::now().toSec() * 1000;
		m_pLogger->info("use virtual object");
		// cloudObjects预处理
		cloudpantsPreprocess(*msg, m_cloudObjectMsg);
		sensorobjectsBoxShow(m_cloudObjectMsg, COMMON::SensorType::CLOUDPLATFORM);

		m_objectFusionTracking.setLidarObjects(m_cloudObjectMsg);// only use cloud timestamp
		m_objectFusionTracking.setCloudObjects(m_cloudObjectMsg);
		m_objectFusionTracking.setGPS(m_gpsMsg, m_selfCarSpeed);

		m_objectFusionTracking.publishTrackObject();
	}
	cloudpantsLock.unlock();
}

/***
 * 订阅gps话题
 * @param msg gps话题
 */
void FusionTracking::SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg){
	// std::lock_guard<std::mutex> gpsLock(m_gpsMutex);
	std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
	m_gpsMsgDeque_.emplace_back(*msg);
	if(m_gpsMsgDeque_.size() > 100)
		m_gpsMsgDeque_.pop_front();
	gpsLock.unlock();
}

void FusionTracking::SubCallback_slamgps(const common_msgs::sensorgps::ConstPtr &msg){
	// std::lock_guard<std::mutex> slamgpsLock(m_slamgpsMutex);
	std::unique_lock<std::mutex> slamgpsLock(m_slamgpsMutex);
    m_slamgpsMsgDeque_.emplace_back(*msg);
	if(m_slamgpsMsgDeque_.size() > 100)
		m_slamgpsMsgDeque_.pop_front();
	slamgpsLock.unlock();
}

void FusionTracking::SubCallback_behaviordecision(const common_msgs::decisionbehavior::ConstPtr& behaviordecisionMsg){
	// std::lock_guard<std::mutex> behaviordecisionLock(m_behaviordecisionMutex);
	std::unique_lock<std::mutex> behaviordecisionLock(m_behaviordecisionMutex);
	m_behaviordecisionMsg = *behaviordecisionMsg;
	behaviordecisionLock.unlock();
}

void FusionTracking::sensorTimeSynchro(){
	{
		m_pLogger->info("lidar-radar time sync:");
		// std::lock_guard<std::mutex> radarLock(m_radarMutex);
		std::unique_lock<std::mutex> radarLock(m_radarMutex);
		timeSynchro(m_radarMsgDeque_,m_lidarMsg.timestamp, COMMON::SensorType::RADAR);//20221027 lidar检测与radar信息的时间同步
		m_radarMsg.obs.clear();
		if(!m_radarMsgDeque_.empty()){
			m_radarMsg = m_radarMsgDeque_.front();
		}
		else{
			m_pLogger->warn("m_radarMsgDeque_ is empty.");
		}
		radarLock.unlock();
	}

	{
		m_pLogger->info("lidar-gps time sync:");
		// std::lock_guard<std::mutex> gpsLock(m_gpsMutex);
		std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
		timeSynchro(m_gpsMsgDeque_, m_lidarMsg.timestamp, COMMON::SensorType::GPS);    //20221027 lidar检测与GPS信息的时间同步
		if(!m_gpsMsgDeque_.empty()){
			m_gpsMsg = m_gpsMsgDeque_.front();//20221027 lidar检测与GPS信息的时间同步
		}
		else{
			m_pLogger->warn("m_gpsMsgDeque_ is empty.");
		}
		gpsLock.unlock();
	}

	{
		m_pLogger->info("lidar-slamgps time sync:");
		// std::lock_guard<std::mutex> slamgpsLock(m_slamgpsMutex);
		std::unique_lock<std::mutex> slamgpsLock(m_slamgpsMutex);
		timeSynchro(m_slamgpsMsgDeque_, m_lidarMsg.timestamp, COMMON::SensorType::GPS);    //20221027 lidar检测与GPS信息的时间同步
		if(!m_slamgpsMsgDeque_.empty()){
			m_slamgpsMsg = m_slamgpsMsgDeque_.front();//20221027 lidar检测与GPS信息的时间同步
			m_pLogger->info("slam Car heading(deg) = {:.3d}", m_slamgpsMsg.heading);
		}
		else{
			m_pLogger->warn("m_slamgpsMsgDeque_ is empty.");
		}
		slamgpsLock.unlock();

	}

	{
		m_pLogger->info("lidar-cloudpants time sync:");
		// std::lock_guard<std::mutex> cloudpantsLock(m_cloudpantsMutex);
		std::unique_lock<std::mutex> cloudpantsLock(m_cloudpantsMutex);
		m_cloudpantsMsg.pants.clear();
		timeSynchro(m_cloudpantsMsgDeque_, m_lidarMsg.timestamp, COMMON::SensorType::CLOUDPLATFORM);
		if(!m_cloudpantsMsgDeque_.empty()){
			m_cloudpantsMsg = m_cloudpantsMsgDeque_.front();
			m_cloudpantsMsgDeque_.pop_front();
			m_pLogger->info("cloudpants freameID = {}", m_cloudpantsMsg.frameId);
		}
		else{
			m_pLogger->warn("m_cloudpantsMsgDeque_ is empty.");
		}
		cloudpantsLock.unlock();
	}
}


/***
 * 20221027 lidar检测与传感器信息的时间同步 20220224 添加同步话题标志位
 * @tparam T 数据类型
 * @param msgDeque 需要同步的传感器（非主传感器）
 * @param curObjectFrameStamp 当前lidar检测时间
 * @param synchroFlag 同步标志位，1：lidar-gps，2：lidar-radar 3:lidar-obu
 */
template <typename T>
void FusionTracking::timeSynchro(std::deque<T>& msgDeque,const int64& curObjectFrameStamp, const int synchroFlag){
	std::mutex dequeMutex;
	std::unique_lock<std::mutex> dequeLock(dequeMutex);

	if(msgDeque.empty()) {
		m_pLogger->warn("msgDeque empty,no need to syncchro.");
		return;
	}
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	//找到容器中数据时间戳小于同步时间的最近索引
	int curMsgIndex = 0;
	int msgDequeSize = msgDeque.size();
	for (int i = 0; i < msgDequeSize; ++i) {
		T curMsg = msgDeque[i];
		if(curMsg.timestamp / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}
	
	
	if(curMsgIndex + 1 == msgDeque.size()){//容器中数据时间戳都小于同步时间,只保留最后一个数据
		double lastMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex].timestamp / 1000.0 - curFrameStamp );
		if(curMsgIndex > 0){//等于0的情况 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	else{
		if(curMsgIndex + 1 <= msgDeque.size()){
			double lastMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex].timestamp / 1000.0 - curFrameStamp );
			double curMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex + 1].timestamp / 1000.0 - curFrameStamp);
			if(abs(curFrameStamp - msgDeque[curMsgIndex].timestamp / 1000.0) >//lidar前一帧
			   abs(curFrameStamp - msgDeque[curMsgIndex + 1].timestamp / 1000.0)){//lidar后一帧
				curMsgIndex += 1;//取最近的一帧数据
			}
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	float timeGap = curFrameStamp * 1000 - msgDeque.front().timestamp;
	static float minTimeGap = FLT_MAX, maxTimeGap = FLT_MIN;
    if (abs(timeGap) < abs(minTimeGap)) {
        minTimeGap = abs(timeGap);
    }
    if (abs(timeGap) > abs(maxTimeGap)) {
        maxTimeGap = abs(timeGap);
    }
   
	std::string l_sensorName = "UNKNOWN";
	auto it  = COMMON::SensorTypeMap.find(synchroFlag);
	if(it != COMMON::SensorTypeMap.end())
		l_sensorName = it->second;
	dequeLock.unlock();
	m_pLogger->info("lidar-{} 时间差(ms): {:.3f}, 当前msg时间 = {:.3f}, 当前lidar检测时间 = {:.3f}", 
					l_sensorName, timeGap, msgDeque.front().timestamp / 1000.0, curFrameStamp);
	m_pLogger->info("Min time gap: {:.3f}, Max time gap: {:.3f}", minTimeGap, maxTimeGap);
}


/***
 * 可视化目标msg，用于调试
 * @param msg_source 目标msg：carFrontRFU2CarFrontFLU
 */
void FusionTracking::sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source, const int& sensorType){
	std::string frameIDInfo = "car";

	visualization_msgs::MarkerArray objectsMarkerArray;

	visualization_msgs::Marker marker;
	marker.action=visualization_msgs::Marker::DELETEALL;
	objectsMarkerArray.markers.emplace_back(marker);

	int msgSourceObsSize = msg_source.obs.size();
	int classColorSize = class_color.size();
	int speedSourceValueSize = v_speedSourceValue.size();
	vector<float> color{0.5, 0, 0};
	geometry_msgs::Point p0, p1, p2, p3;
	geometry_msgs::Point p4, p5, p6, p7;
	#pragma omp parallel for
	for (int i=0; i<msgSourceObsSize; i++){
		const auto& obs = msg_source.obs[i];
		int classification = int(obs.classification);

		// 距离目标不显示框
		if(classification == COMMON::LidarDetectionClassification::Unknown && m_pConfigManager->m_isUseRosBag == false)
			continue;

		if(classification < classColorSize){
			color = class_color[classification];
		}

		visualization_msgs::Marker line_list_detect;
		double stamp = msg_source.timestamp / 1000.0;
		line_list_detect.header.frame_id  = frameIDInfo;
		//line_list_detect.header.stamp = ros::Time::now();
		line_list_detect.header.stamp = ros::Time().fromSec(stamp);//TODO  lidar时间戳 //ros::Time::now()
		line_list_detect.ns = "points_and_lines";
		line_list_detect.lifetime =ros::Duration();//0.1
		line_list_detect.action = visualization_msgs::Marker::ADD;
		line_list_detect.pose.orientation.w = 1.0;
		line_list_detect.id = i;
		line_list_detect.type = visualization_msgs::Marker::LINE_LIST;
		//line width
		line_list_detect.scale.x = 0.1;
		//line_list_detect.scale.y = 0.1;
		//line_list_detect.scale.z = 0.1;
		//color green
		line_list_detect.color.a = 1;//透明度
		if(sensorType == COMMON::SensorType::LIDAR){
			line_list_detect.color.a = 1;//透明度
			line_list_detect.color.r = color[0]; //检测颜色
			line_list_detect.color.g = color[1];
			line_list_detect.color.b = color[2];
		}
		else if(sensorType == COMMON::SensorType::RADAR){
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::OBU){
			line_list_detect.color.r = 0; //蓝色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::CLOUDPLATFORM){
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(sensorType == COMMON::SensorType::FUSION){
			line_list_detect.color.a = 1;//透明度
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(sensorType == COMMON::SensorType::FUSIONAllOBJECTS){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::TRACKING){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0.5;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::V2IFUSION){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0.5;
			line_list_detect.color.b = 1;
		}
		else{

		}


		int obj_class = int(msg_source.obs[i].classification);
		
		switch (sensorType) {
			case COMMON::SensorType::FUSION:
			{
				float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				vector<double> boxInfo = {obs.x,obs.y, obs.z,
										obs.length, obs.width, obs.height, headingAnticlockwise};
				vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				break;

				// const auto& points = obs.points;
				// p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				// p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				// p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				// p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				// p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				// p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				// p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				// p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;


				// p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				// p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				// p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				// p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				// p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				// p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				// p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				// p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::TRACKING:
			{
				// float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				// vector<double> boxInfo = {obs.x,obs.y, obs.z,
				// 						obs.length, obs.width, obs.height, headingAnticlockwise};
				// vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				// p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				// p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				// p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				// p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				// p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				// p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				// p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				// p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				// break;

				const auto& points = obs.points;
				p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;

				// p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				// p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				// p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				// p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				// p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				// p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				// p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				// p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::LIDAR:
			{
				float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				vector<double> boxInfo = {obs.x,obs.y, obs.z,
										obs.length, obs.width, obs.height, headingAnticlockwise};
				vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				break;
			}
			default:
			{
				// float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				// vector<double> boxInfo = {obs.x,obs.y, obs.z,
				// 						obs.length, obs.width, obs.height, headingAnticlockwise};
				// vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				// p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				// p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				// p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				// p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				// p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				// p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				// p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				// p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				// break;

				const auto& points = obs.points;
				p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;
				break;
			}
		}

		//bottom
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p1);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p2);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p3);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p0);
		//top
		line_list_detect.points.push_back(p4); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p5); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p6); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p7); line_list_detect.points.push_back(p4);
		//side
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p4);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p7);
		//direction
		// if(sensorType == COMMON::SensorType::TRACKING){
		// 	// 方向箭头
		// 	geometry_msgs::Point headingArrow1, headingArrow2, headingArrow3, headingArrow4;
		// 	headingArrow1.x = (p4.x + p5.x)/2;
		// 	headingArrow1.y = (p4.y + p5.y)/2;
		// 	headingArrow1.z =  p4.z;
			
		// 	headingArrow2.x = (p4.x*3 + p7.x)/4;
        //     headingArrow2.y = (p4.y*3 + p7.y)/4;
        //     headingArrow2.z =p4.z;
        //     line_list_detect.points.push_back(headingArrow1);
        //     line_list_detect.points.push_back(headingArrow2);

		// 	headingArrow3.x = (p5.x*3 + p6.x)/4;
        //     headingArrow3.y = (p5.y*3 + p6.y)/4;
        //     headingArrow3.z =p6.z;
        //     line_list_detect.points.push_back(headingArrow1);
        //     line_list_detect.points.push_back(headingArrow3);

        //     headingArrow4.x = (p5.x + p4.x+ p7.x + p6.x)/4;
        //     headingArrow4.y = (p5.y + p4.y + p7.y + p6.y)/4;
        //     headingArrow4.z =p4.z;
        //     line_list_detect.points.push_back(headingArrow1);
        //     line_list_detect.points.push_back(headingArrow4);
		// }
		


		// radar不画框，其他画框
		if(sensorType != COMMON::SensorType::RADAR){
			objectsMarkerArray.markers.push_back(line_list_detect);
		}
		visualization_msgs::Marker text;
		text.header.frame_id = frameIDInfo;
		text.header.stamp = ros::Time().fromSec(stamp); //ros::Time::now()
		text.ns = "box";
		text.action = visualization_msgs::Marker::ADD;
		text.lifetime =ros::Duration();//0.1
		text.pose.orientation.w = 1;
		text.pose.position.x = msg_source.obs[i].x;
		text.pose.position.y = msg_source.obs[i].y;
		text.pose.position.z = msg_source.obs[i].z;
		text.id = i;
		text.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
		text.scale.z = 0.3;

		text.color.a = 1;//透明度
		text.color.r = 1;
		text.color.g = 1;
		text.color.b = 1;

		//添加目标横纵向速度信息
		std::string vxString = "", vyString = "";
		if(sensorType == COMMON::SensorType::RADAR){
			ostringstream str;
			str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << msg_source.obs[i].relspeedx;
			vxString = str.str();
			str.str("");//清空数据
			str <<  msg_source.obs[i].relspeedy;
			vyString = str.str();
		}
		else{
			ostringstream str;
			str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << msg_source.obs[i].relspeedx + m_selfCarSpeed[0];
			vxString = str.str();
			str.str("");//清空数据
			str <<  msg_source.obs[i].relspeedy + m_selfCarSpeed[1];
			vyString = str.str();
		}
		

		//添加跟踪类型
		//20221103 //0.初始目标 1.跟踪 2.lidar 3.lidar-radar-camera 4.融合中lidar-radar -20220908 20230111 简化
		int speedSourceValue = (int)msg_source.obs[i].value;
		std::string speedSource = speedSourceValue < speedSourceValueSize ? v_speedSourceValue[speedSourceValue] : v_speedSourceValue[speedSourceValueSize - 1];

		//添加目标运动状态
		//20220908 20230111 简化
		//std::string motionInfo = msg[i].motionInfo < v_motionInfo.size() ? v_motionInfo[msg[i].motionInfo] : v_motionInfo[v_motionInfo.size() - 1];
		std::string confidence = std::to_string(msg_source.obs[i].confidence);
		confidence = confidence.substr(0,4);
		string positionX = std::to_string(msg_source.obs[i].x);
		string positionY = std::to_string(msg_source.obs[i].y);
		// 显示：id
		// 第几次匹配成功-预测帧数
		// 类别-速度来源-运动信息
		// 横向速度-纵向速度
		// 置信度-驾驶意图-radarIndex-radarObjectID
		switch (sensorType) {
			case COMMON::SensorType::LIDAR:
				text.text =
						"Lidar:\nID:" +std::to_string(msg_source.obs[i].id)
						+ "\nX:" + positionX + "\nY:" + positionY
                        + "\nlabel:" + std::to_string(msg_source.obs[i].classification)
						+ "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                        + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
						+ "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::RADAR:
				text.text = "Radar:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::OBU:
				text.text = "OBU:\nID:" +std::to_string(msg_source.obs[i].id)
							+ "\nX:" + positionX + "\nY:" + positionY
							+ "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
							+ "\nlabel:" + std::to_string(msg_source.obs[i].classification)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
							+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
							+ "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
							+ "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::CLOUDPLATFORM:
				text.text = "Cloudplatform:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::FUSION:
				text.text = "Fusion:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
							+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::FUSIONAllOBJECTS:
				text.text = "FUSIONAllOBJECTS:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::TRACKING:
				text.text = "Tracking:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
                            //+ "-drivingIntent:" + std::to_string(msg_source.obs[i].drivingIntent)
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::V2IFUSION:
				text.text = "V2IFUSION:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
                            //+ "-drivingIntent:" + std::to_string(msg_source.obs[i].drivingIntent)
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			default:
				break;
		}
		objectsMarkerArray.markers.push_back(text);

		// ID 显示
		if(m_pConfigManager->m_isUseRosBag 
			&& (sensorType == COMMON::SensorType::TRACKING || sensorType == COMMON::SensorType::V2IFUSION)){
			visualization_msgs::Marker objectsInfoMarker;
			objectsInfoMarker.header.frame_id = frameIDInfo;
			objectsInfoMarker.header.stamp = ros::Time().fromSec(stamp);
			objectsInfoMarker.ns = "ID";
			objectsInfoMarker.id = i;
			objectsInfoMarker.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
			objectsInfoMarker.lifetime = ros::Duration();
			objectsInfoMarker.action = visualization_msgs::Marker::ADD;

			objectsInfoMarker.pose.orientation.w = 1;
			geometry_msgs::Point  p_temp;
			p_temp.x = (p4.x + p5.x)/2;
			p_temp.y = (p4.y + p5.y)/2;
			p_temp.z =  p4.z;
			objectsInfoMarker.pose.position.x = p_temp.x + 0.3*(p5.y-p4.y);
			objectsInfoMarker.pose.position.y = p_temp.y + 0.3*(p5.y-p4.y);
			objectsInfoMarker.pose.position.z = p_temp.z + 0.4*(p5.z-p4.z);

			objectsInfoMarker.scale.z = 2.4;
			objectsInfoMarker.color.a = 1;
			objectsInfoMarker.color.r = 1;
			objectsInfoMarker.color.g =1;
			objectsInfoMarker.color.b =0;
			objectsInfoMarker.text = std::to_string(msg_source.obs[i].id);
			objectsMarkerArray.markers.push_back(objectsInfoMarker);
		}
		
	}
	switch (sensorType) {
		case COMMON::SensorType::LIDAR:
			pub_lidarObjectBBX.publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::RADAR:
			pub_radarObjectBBX.publish(objectsMarkerArray); //
			break;
		case COMMON::SensorType::CLOUDPLATFORM:
			pub_cloudObjectsBBX.publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::FUSION:
			pub_fusionObjectBBX.publish(objectsMarkerArray); //
			break;
		case COMMON::SensorType::TRACKING:
			pub_trackingObjectBBX.publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::V2IFUSION:
			pub_v2iFusionObjectBBX.publish(objectsMarkerArray);
			break;
		default:
			break;
	}
	objectsMarkerArray.markers.clear();
	//发布radar中心点
	// 发布sensorradarCloud
	if(sensorType == COMMON::SensorType::RADAR){
		pcl::PointCloud<pcl::PointXYZI> radarCloud;
		radarCloud.points.reserve(msgSourceObsSize);
		// radarTopic.obs 车前保险杠RFU
		#pragma omp parallel for
		for (int i = 0; i < msgSourceObsSize; ++i) {
			pcl::PointXYZI radarPoint;
			radarPoint.x = msg_source.obs[i].x;
			radarPoint.y = msg_source.obs[i].y;
			radarPoint.z = msg_source.obs[i].z;
			radarPoint.intensity = msg_source.obs[i].radarIndex;//radar分类数据是存放的是目标属于哪个radar classification
			radarCloud.points.push_back(radarPoint);
		}
		sensor_msgs::PointCloud2 radarMsg;
		pcl::toROSMsg(radarCloud, radarMsg);
		radarMsg.header.frame_id = "car";
		radarMsg.header.stamp = ros::Time(msg_source.timestamp / 1000.0);//ros::Time::now();
		radarMsg.height = 1;
		radarMsg.width = msgSourceObsSize; // 点云中的点数
		pub_radarCloudRaw.publish(radarMsg);//sensorradar sensorradarCloud 车前保险杠RFU
	}
}


/***
 * 计算向量叉积（用于判断点是否在矩形内）
 * @param cornerPoint1 （矩形）第一个点
 * @param cornerPoint2 （矩形）第二个点
 * @param point 点（用到检测的点坐标）
 * @return 叉积值
 */
float FusionTracking::getCross(const common_msgs::point3d& cornerPoint1, const common_msgs::point3d& cornerPoint2,
                            const common_msgs::point3d& point){
	return (cornerPoint2.x - cornerPoint1.x) * (point.y - cornerPoint1.y) - (point.x - cornerPoint1.x) * (cornerPoint2.y - cornerPoint1.y);
}

// 计算聚类最优航向角
void FusionTracking::findOptimalYaw(common_msgs::sensorobject& lidarObject){
	float rawHeadingDegree = lidarObject.azimuth * 180.0 / M_PI;
	float bestHeadingDegree = 0;
	int maxPointsNumberInBox = 0;
	vector<double> boxInfo(7, 0.0);
	vector<vector<double>> pointsVector(8, vector<double>(3, 0.0));
	common_msgs::point3d cornerPoint0, cornerPoint1, cornerPoint2, cornerPoint3;

	for(float headingDegree = 0; headingDegree < 360; headingDegree += 20){
		float headingClockwise = 2.0 * M_PI - headingDegree * M_PI / 180.0;
		boxInfo = {lidarObject.x, lidarObject.y, lidarObject.z,
					lidarObject.length, lidarObject.width, lidarObject.height,
			           headingClockwise};
		pointsVector = m_common.boxes_to_corners_3d(boxInfo);

		cornerPoint0.x = pointsVector[0][0]; cornerPoint0.y = pointsVector[0][1]; cornerPoint0.z = pointsVector[0][2];
		cornerPoint1.x = pointsVector[1][0]; cornerPoint1.y = pointsVector[1][1]; cornerPoint1.z = pointsVector[1][2];
		cornerPoint2.x = pointsVector[2][0]; cornerPoint2.y = pointsVector[2][1]; cornerPoint2.z = pointsVector[2][2];
		cornerPoint3.x = pointsVector[3][0]; cornerPoint3.y = pointsVector[3][1]; cornerPoint3.z = pointsVector[3][2];

	
		int pointsNumberInBox = 0;
		for(const common_msgs::point3d& point : lidarObject.points){
			bool isPointInBox = ((getCross(cornerPoint0, cornerPoint1,point) * getCross(cornerPoint2, cornerPoint3,point)) >= 0) &&
		                    ((getCross(cornerPoint1, cornerPoint2,point) * getCross(cornerPoint3, cornerPoint0,point)) >= 0);
			if(isPointInBox){
				++pointsNumberInBox;
			}
		}

		if(pointsNumberInBox > maxPointsNumberInBox){
			maxPointsNumberInBox = pointsNumberInBox;
			bestHeadingDegree = headingDegree;
		}
	}

	if(cos((rawHeadingDegree - bestHeadingDegree) * M_PI / 180.0) > 0.9){
		// 计算的这个角度与检测的航向角相差不大，则使用检测的航向角
		// lidarObject.azimuth = bestHeadingDegree * M_PI / 180.0;
		// cout <<"findOptimalYaw: id = " << lidarObject.id << ", 计算的这个角度与检测的航向角相差不大,使用检测航向角"  << endl;
	}
	else if(cos((rawHeadingDegree - bestHeadingDegree - 180.0) * M_PI / 180.0) > 0.9){
		// 计算角度+180度与检测的航向角相差不大，则使用检测的航向角
		// lidarObject.azimuth = bestHeadingDegree * M_PI / 180.0;
		// cout <<"findOptimalYaw: id = " << lidarObject.id << ", 计算角度+180度与检测的航向角相差不大,使用检测航向角"  << endl;
	}
	else{
		// 计算角度与检测的航向角相差大，则使用计算的航向角
		lidarObject.azimuth = bestHeadingDegree * M_PI / 180.0;
		// cout <<"findOptimalYaw: id = " << lidarObject.id << ", 使用计算航向角"  << endl;
	}

	lidarObject.azimuth = bestHeadingDegree * M_PI / 180.0;
	// cout <<"findOptimalYaw: id = " << lidarObject.id << ", rawHeadingDegree = " << rawHeadingDegree 
	// 	<< ", bestHeadingDegree = " << bestHeadingDegree 
	// 	<<", maxPointsNumberInBox = " << maxPointsNumberInBox << endl;

}


bool FusionTracking::isClusterObjectsDelete(common_msgs::sensorobject& clusterObject){
	const static float carLengthMax = 5;
	if(clusterObject.length > carLengthMax || clusterObject.width > carLengthMax){
		return true;
	}
	else{
		return false;
	}
}

bool FusionTracking::isObjectInHDMap(common_msgs::sensorobject& lidarObject){
	float carx, cary;
	if(m_gpsMsg.lon == 0 || m_gpsMsg.lat == 0){
		return false;
	}
	
	LatLonToLocalXY(m_gpsMsg.lon,  m_gpsMsg.lat, carx, cary);//,自车的位置,经纬度,后边是图坐标
	bool isObjectInMap = InMap(lidarObject.x, lidarObject.y, carx, cary);
	return std::move(isObjectInMap);
}

void FusionTracking::LatLonToLocalXY(double lon_car, double lat_car, float& x, float& y)
{
	const double R_e = 6378137.0;       // 椭球体长半轴
    const double R_f = 6356752.314245;  // 椭球体短半轴
    const double e_1 = sqrt(pow(R_e, 2) - pow(R_f, 2)) / R_e;   // 第一偏心率
    

    double sin_lat1 = sin(m_pConfigManager->m_hdMapLatitude * M_PI / 180.0);     	// sin of start lat
    double cos_lat1 = cos(m_pConfigManager->m_hdMapLatitude * M_PI / 180.0); 	    // sec of start lat
    double square_e = e_1 * e_1;
    double square_sin_lat1 = sin_lat1 * sin_lat1;

    double R_n = R_e / (sqrt(1 - square_e * square_sin_lat1));   				// 卯酉面等效曲率半径 (lon1, lat1)
    double R_m = (R_n * (1.0 - square_e)) / (1.0 - square_e * square_sin_lat1);   	// 子午面等效曲率半径 (lon1, lat1)

    x = (lon_car - m_pConfigManager->m_hdMapLongitude) * M_PI / 180.0 * R_n * cos_lat1;
    y = (lat_car - m_pConfigManager->m_hdMapLatitude) * M_PI / 180.0 * R_m;

}

// 目标转换到图中
// 感知目标位置float x, float y, 车的图坐标 float x_car, float y_car, 航向float heading_car,   
// 输出坐标 float& x_obj, float& y_obj
void FusionTracking::transformToAbsoluteCoordinates(float x, float y, float x_car, float y_car, float heading_car,
												 float& x_obj, float& y_obj){
    x_obj = x_car + x*std::cos(heading_car) + y*std::sin(heading_car);
    y_obj = y_car - x*std::sin(heading_car) + y*std::cos(heading_car);

}

// 感知目标位置float x, float y
bool FusionTracking::InMap(float x ,float y, float carx, float cary){
	float x_obj;
	float y_obj;
	int x_img;
	int y_img;
	double heading = m_gpsMsg.heading / 180 * M_PI;
	transformToAbsoluteCoordinates(x,y,carx,cary, heading,  x_obj, y_obj);
	// to image
	y_obj = -y_obj;
	x_img = int(x_obj / 0.1 +0.5);
	y_img = int(y_obj / 0.1 +0.5);
	if(x_img < 0 || y_img < 0 || y_img > m_hdMapPicture.rows || x_img > m_hdMapPicture.cols
		){
		m_pLogger->info("x_img: {:.3f}, y_img: {:.3f}", x_img, y_img);
		return true;
	}
	
	try
	{
		int a = (int)m_hdMapPicture.at<uchar>(y_img,x_img);//y,x
		if(a>1){
			return true;
		}else{
			return false;
		}
	}
	catch(const std::exception& e)
	{
		m_pLogger->warn("InMap error: {}", e.what());
		return true;
	}
}


void FusionTracking::transRadar_carFrontRFU2CarBackRFU(common_msgs::sensorobjects& msg){
	int radarObsSize = msg.obs.size();
	#pragma omp parallel for
	for (size_t i = 0; i < radarObsSize; i++) {
		// common_msgs::sensorobject singleObject = msg->obs[i];
		msg.obs[i].y += m_pConfigManager->m_front2BackDistance;
		for (size_t j = 0; j < msg.obs[i].points.size(); j++)//8角点及多边形点
		{
			auto point = msg.obs[i].points[j];
			float pointTempX = point.x, pointTempY = point.y;
			msg.obs[i].points[j].y += m_pConfigManager->m_front2BackDistance;
		}
	}
}

void FusionTracking::cloudpantsPreprocess(const common_msgs::cloudpants& cloudpants, common_msgs::sensorobjects& cloudObjects){
	cloudObjects.obs.clear();
	cloudObjects.timestamp = cloudpants.timestamp;
	cloudObjects.gpstime = cloudpants.timestamp;
	cloudObjects.isvalid = 1;
	
	if(cloudpants.pants.empty()){
		m_pLogger->warn("cloudObjects is empty");
		return;
	}
	
	
	tagUTMCorr selfCarUTM;
	wgs84Utm.LatLonToUTMXY(m_gpsMsg.lat / 180.0 * M_PI, m_gpsMsg.lon / 180.0 * M_PI, selfCarUTM);
	Eigen::Vector3d selfCarEulerXYZDegree{m_gpsMsg.roll, m_gpsMsg.pitch, m_gpsMsg.heading};
	m_pLogger->info("m_gpsMsg.heading = {:.3f}", m_gpsMsg.heading);
	
	Eigen::Vector3d selfCarUTMAsTranslation{selfCarUTM.x, selfCarUTM.y, 0};
	m_pLogger->info("cloudpants object size = {}", cloudpants.pants.size());
	
	for (const auto& cloudpant:cloudpants.pants) {
		common_msgs::sensorobject cloudObject;
		//	1. 经纬度转UTM
		tagUTMCorr utm;
		wgs84Utm.LatLonToUTMXY(cloudpant.latitude / 180.0 * M_PI, cloudpant.longitude / 180.0 * M_PI, utm);
		Eigen::Vector3d objectPositionUTM{utm.x, utm.y, 0};
		
		//2.UTM转lidar RFU
		Eigen::Vector3d objectPositionInCarBackFRU;
		m_sensorAxisTransformer.utm2CarBackRFU(objectPositionUTM,selfCarEulerXYZDegree,selfCarUTMAsTranslation, objectPositionInCarBackFRU);
		
		cloudObject.id = atoi(cloudpant.Id.c_str());
		cloudObject.x = objectPositionInCarBackFRU[0];
		cloudObject.y = objectPositionInCarBackFRU[1];
		cloudObject.z = objectPositionInCarBackFRU[2];

		cloudObject.longtitude = cloudpant.longitude;
		cloudObject.latitude = cloudpant.latitude;
		cloudObject.altitude = 0;
		
		
		float cloudObjectAngleDegree = cloudpant.courseAngle * 180.0 / M_PI;
		float cloudObjectAngleDegreeInLidarRFU_Clockwise = (cloudObjectAngleDegree - m_gpsMsg.heading);
		cloudObjectAngleDegreeInLidarRFU_Clockwise = cloudObjectAngleDegreeInLidarRFU_Clockwise < 0? cloudObjectAngleDegreeInLidarRFU_Clockwise + 360:cloudObjectAngleDegreeInLidarRFU_Clockwise;
		cloudObjectAngleDegreeInLidarRFU_Clockwise = cloudObjectAngleDegreeInLidarRFU_Clockwise >= 360 ? cloudObjectAngleDegreeInLidarRFU_Clockwise - 360:cloudObjectAngleDegreeInLidarRFU_Clockwise;

		// TODO 验证角度与相对速度: need lidar axis angle
		cloudObject.relspeedx = cloudpant.speed * sin(cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - m_selfCarSpeed[0];
		cloudObject.relspeedy = cloudpant.speed * cos(cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - m_selfCarSpeed[1];
		cloudObject.azimuth = cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;
		cloudObject.width = cloudpant.width;
		cloudObject.length = cloudpant.length;
		cloudObject.height = cloudpant.height;
		cloudObject.classification = static_cast<unsigned char>(transCloudObjectType2CameraDetectionType(cloudpant.vehicletype));
		cloudObject.value = static_cast<uint8_t>(COMMON::SensorType::CLOUDPLATFORM);
		
		//生成8角点
		float headingAnticlockwise =  2 * M_PI - cloudObject.azimuth; //
		vector<double> boxInfo = {cloudObject.x,cloudObject.y, objectPositionInCarBackFRU[2],
		                         cloudpant.length, cloudpant.width, cloudpant.height, headingAnticlockwise};
		vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
		for(const auto& singleCornerPointVector : eightCornerPoints){
			common_msgs::point3d singleCornerPoint;
			singleCornerPoint.x = singleCornerPointVector[0];
			singleCornerPoint.y = singleCornerPointVector[1];
			singleCornerPoint.z = singleCornerPointVector[2];
			
			cloudObject.points.emplace_back(std::move(singleCornerPoint));
		}
		cloudObjects.obs.emplace_back(std::move(cloudObject));
	}
}


int FusionTracking::transCloudObjectType2CameraDetectionType(const int& cloudObjectType){
	int cameraClassfication = COMMON::LidarDetectionClassification::Unknown;
	switch(cloudObjectType){
		case 0:
		case 9:
			return COMMON::LidarDetectionClassification::Unknown;
		case 4:
			return  COMMON::LidarDetectionClassification::Pedestrian;
		case 5:
			return  COMMON::LidarDetectionClassification::Bicycle;
		case 6: // 摩托车认为是三轮车
			return  COMMON::LidarDetectionClassification::Tricycle;
		case 1:
		case 2:
		case 3:
		case 7:
		case 8:
		case 10:
		case 11:
		default:
			return  COMMON::LidarDetectionClassification::Car;
	}
}
