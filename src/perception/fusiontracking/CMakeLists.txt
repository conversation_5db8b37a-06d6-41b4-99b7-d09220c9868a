cmake_minimum_required(VERSION 2.8.3)
project(fusiontracking)

# set(CMAKE_BUILD_TYPE "Debug" )
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14 -march=native -O3")
## Compile as C++11, supported in ROS Kinetic and newer
#add_compile_options(-std=c++11)
#set(CMAKE_CXX_STANDARD 14)



find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
#  pcl_conversions
#  pcl_ros
  common_msgs
        nav_msgs
        geometry_msgs
        cv_bridge
        roslib
)


find_package(PkgConfig REQUIRED)
find_package( OpenCV REQUIRED )


if(NOT DEFINED CMAKE_SUPPRESS_DEVELOPER_WARNINGS)
    set(CMAKE_SUPPRESS_DEVELOPER_WARNINGS 1 CACHE INTERNAL "No dev warnings")
endif()
#set(PCL_DIR "/usr/lib/x86_64-linux-gnu/cmake/pcl")
# set(PCL_DIR /usr/share/pcl-1.9)
# find_package(PCL 1.9 REQUIRED COMPONENTS)

find_package(PCL REQUIRED COMPONENTS)

message(.........................)
message(${PCL_ROOT}/include/pcl-${PCL_VERSION_MAJOR}.${PCL_VERSION_MINOR})
include_directories(${PCL_INCLUDE_DIRS})

link_directories(${PCL_LIBRARY_DIRS})
add_definitions(${PCL_DEFINITIONS})
message(.........................)
message(${PCL_LIBRARY_DIRS})

catkin_package(
        # INCLUDE_DIRS include
        LIBRARIES ${PROJECT_NAME}
        catkin_depends roscpp rospy std_msgs

)

# 添加头文件
include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
  /usr/include/
        src
)

set(THIRD_LIB
        ${catkin_LIBRARIES}
        ${OpenCV_LIBS}
        ${PCL_LIBRARIES}
        )

#合并融合跟踪节点
include_directories(
        include
        ${catkin_INCLUDE_DIRS}
        ${OpenCV_INCLUDE_DIRS}
        /usr/include/
        src
)

file(GLOB_RECURSE FUSIONTRACKINGSORTFILE
        "src/KalmanFilter/KalmanFilter.cpp"
        "src/fusiontracking.cpp"
        "src/ObjectFusionTracking.cpp"
        "src/ObjectTracking.cpp"
        "src/MHT/scan_volume.cpp"
        "src/MHT/cv_target.cpp"

        "src/MHT/models/measmodel.cpp"
        "src/MHT/models/motionmodel.cpp"
        "src/MHT/models/target.cpp"

        "src/MHT/utils/density.cpp"
        "src/MHT/utils/generation.cpp"

        "src/MHT/tracker/localHypothesis.cpp"
        "src/MHT/tracker/data_association.cpp"
        "src/MHT/tracker/tracker.cpp"

        "src/MHT/hungarian/Hungarian.cpp"

        )
add_executable(fusiontracking src/main.cpp ${FUSIONTRACKINGSORTFILE})
target_include_directories(fusiontracking INTERFACE ${CMAKE_SOURCE_DIR}/perception/commonlibrary/src)
# target_include_directories(fusiontracking PRIVATE ${CMAKE_SOURCE_DIR}/perception/commonlibrary/src)
target_link_libraries(fusiontracking ${THIRD_LIB}
        ${CMAKE_SOURCE_DIR}/perception/commonlibrary/lib/libcommonlibrary.so)

# add_executable(da_test src/MHT/data_association_test.cpp ${FUSIONTRACKINGSORTFILE})

# Mark libraries for installation
# See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_libraries.html
install(TARGETS ${PROJECT_NAME}
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
        )

# Mark cpp header files for installation
install(DIRECTORY include
        DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
        FILES_MATCHING PATTERN "*.h"
        PATTERN ".svn" EXCLUDE
        )

# Mark other files for installation (e.g. launch and bag files, etc.)
install(DIRECTORY launch
        DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
        )
install(FILES coco.names fusiontracking.drawio rosgraph.png
        DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
        )