#common
isUseRosBag: true
#城市UTM坐标编号：49-西安，50-北京，51-苏州,
cityUTMCode: 50
# F09:1 Y160:2 EQM5:3
carNumber: 1
#是否使用高精地图滤除目标
isUseHDMap: false
#高精地图文件名-经纬度-北京软件园
hdMapName: HDMap-ruanjianyuan.png
hdMapLongitude: 116.279401146
hdMapLatitude: 40.053995788
#高精地图文件名-经纬度-北京软件园
# hdMapName: HDMap-guoqizhilian-paint.png
# m_hdMapLongitude: 116.486848891
# m_hdMapLatitude: 39.731528277

front2BackDistance: 4.63

#是否保存跟踪目标信息，分析目标跟踪情况和评估跟踪指标
isSaveObjectInfoCSV: false
#保存跟踪目标信息的目：false-分析目标跟踪情况, true评估跟踪指标，
isEvaluateTracking: false
isSaveLogInfoTXT: false
isUsePredictToShow: true
carCenter2CarBackDistance: 1.536

log:
  # log日志输出类型：0-终端，1-文件，2-终端和文件
  outputType: 0
  # 注意结尾不带/符号
  filePath: "/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/logFiles"
  fileName: "tracking.txt"
  # 日志文件最大值：MB
  fileMaxSize: 10
  # 日志数
  fileMaxNum: 100

gps:
  #精度丢失时间：秒
  accuracyLossTime: 3.0
  #isSimulateMode:是否是仿真模式,true:只使用虚拟目标仿真,false:在线运行
  isSimulateMode: false
  #sensorgps采图保存路径，true：home下/VanjeeAdMap/rawfiles/， false：perception下"/outputMapping/rawfiles/"
  isOnline: true


fusiontracking:
  #是否使用radar目标作为交通参与者
  isUseRadarObjects: false
  debugID: 0
  #是否保存跟踪耗时信息
  isSaveTimeUse: true
  timeUseFilePath: "/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/timeUse"

#融合
#false：实车模式，发送跟踪目标、云端目标，true：仿真模式，只发送云端数据
isSimulateMode: false
bdebug: 1

v2n:
  #false：仿真模式，只发送云端数据，true：实车模式，发送跟踪目标、云端目标
  isSimulateMode: false
  # 是否使用云端数据
  isUseV2N: false


#基站范围
stationRange:
  taocunjieLon: 120.62597705
  taocunjieLat: 31.42109701
  qinglonggangLon: 120.62682744
  qinglonggangLat: 31.42491582

  eightGateLon: 0 #116.2869813
  eightGateLat: 0 #40.0516963
  houchangcunLon: 116.290337
  houchangcunLat: 40.0528473
  nineStreetLon: 116.2932465
  nineStreetLat: 40.0471980
  sevenSteetLon: 116.2959062
  sevenSteetLat: 40.0429212

#[经度°，纬度°]: 桃村街-青龙港-八号门-后厂村-上地九街-上地七街
stationRangeTest: [
  [120.62597705,31.42109701],
  [120.62682744,31.42491582],
  [116.2869813,40.0516963],
  [116.290337,40.0528473],
  [116.2932465,40.0471980],
  [116.2959062,40.0429212]]


slamLocationRange:
  # position1_lon: 116.30413624996606
  # position1_lat: 40.0512319819988
  # position2_lon: 116.29502504254532
  # position2_lat: 40.04833656359975
  # position3_lon: 116.29532240571115
  # position3_lat: 40.047371396801736
  # position4_lon: 116.30481423798528
  # position4_lat: 40.05020312099467

  # position1_lon: 116.28986608892274
  # position1_lat: 40.047760691711744
  # position2_lon: 116.28945860204692
  # position2_lat: 40.04909534017443
  # position3_lon: 116.29050376675701
  # position3_lat: 40.049325989631654
  # position4_lon: 116.29088981858621
  # position4_lat: 40.047956497038655
  #靖江厂房
  position1_lon: 120.2306699
  position1_lat: 31.9642941
  position2_lon: 120.23077479999
  position2_lat: 31.9645719999
  position3_lon: 120.229617999
  position3_lat: 31.96487639999
  position4_lon: 120.229444899
  position4_lat: 31.9645981999
  

#跟踪
#毫米波雷达检测的目标作为障碍物的距离，大于阈值作为目标
radarAsObjectDistance: 100


#红旗2
hongqi2:
  lidar2carBackRFU_X: 0.302
  lidar2carBackRFU_Y: 1.690
  lidar2carBackRFU_Z: 1.231
  lidarFLU2carBackRFU_rollDegree: 0.871
  lidarFLU2carBackRFU_pitchDegree: -0.568
  lidarFLU2carBackRFU_yawDegree: 89.875

  LidarRFUAxis2CarBackRFU_rollDegree: 0.871
  LidarRFUAxis2CarBackRFU_pitchDegree: -0.568
  LidarRFUAxis2CarBackRFU_yawDegree: -0.125


#红旗1
hongqi1:
  lidar2carBackRFU_X: 0.302
  lidar2carBackRFU_Y: 1.690
  lidar2carBackRFU_Z: 1.231
  lidarFLU2carBackRFU_rollDegree: 0.871
  lidarFLU2carBackRFU_pitchDegree: -0.568
  lidarFLU2carBackRFU_yawDegree: 89.875

  LidarRFUAxis2CarBackRFU_rollDegree: 0.871
  LidarRFUAxis2CarBackRFU_pitchDegree: -0.568
  LidarRFUAxis2CarBackRFU_yawDegree: -0.125