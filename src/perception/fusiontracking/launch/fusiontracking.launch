<launch>
    <!-- <param name="/use_sim_time" value="true"/> -->
<!--    <include file="$(find sensorgps)/launch/sensorgps.launch"/>-->
    <rosparam file="$(find fusiontracking)/launch/params.yaml" command="load"/>
    <!--   launch-prefix="gdb -ex run -args"      -->
    <!--  launch-prefix="valgrind -tool=memcheck -s -log-file=/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/valReport.log -leak-check=full -show-reachable=yes -leak-resolution=low -track-origins=yes -undef-value-errors=yes"       -->
    <!--          launch-prefix="valgrind -tool=callgrind -dump-instr=yes -collect-jumps=yes -callgrind-out-file=/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/callgrindMHT.out" -->
    <!-- launch-prefix="valgrind -read-var-info=yes -tool=helgrind -log-file=/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/lock.txt" -->
    <!-- respawn="true" -->
    <node name="fusiontracking" pkg="fusiontracking" type="fusiontracking" output="screen" 
     
        

    />
    <node name="fusiontrackingrviz" pkg="rviz" type="rviz" args="-d $(find fusiontracking)/launch/fusiontracking.rviz"/>
</launch>