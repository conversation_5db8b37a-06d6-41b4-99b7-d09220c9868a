<launch>
    <rosparam file="$(find fusiontracking)/launch/params.yaml" command="load"/>
<!--    <arg name="isUseHongQi2" default="true"/>-->
    <!--是否对radar数据做验证，true：不进行坐标转换，false：进行坐标转换，即对radar进行旋转,转到INS坐标系-右前上    -->
    <param name="isRadarPosionSpeedVerify" value="false" type="bool"/>
<!--    <param name="isUseRosBag" value="true" type="bool"/>-->
<!--    <param name="carNumber" value="2" type="int"/>-->

    <!--毫米波雷达融合respawn="true"-->
    <node name="sensorradar"   pkg="sensorradar"   type="sensorradar"       output="screen" respawn="true" >
<!--        <param name="carNumber" value="1" type="int"/>-->
        <!--isShowRadarMarkerNumber是否显示指定radar信息，true:显示指定radar索引，false：不指定radar-->
        <param name="isShowRadarNumber" value="true" type="bool"/>
        <!--showRadarMarkerNumber显示radar索引的信息，0：front,1:leftFront,2:rightFront,3:back,4:leftBack,5:rightBack-->
        <param name="showRadarNumber" value="0" type="int"/>
        <!--   isSaveObjectInfoCSV:是否保存radar目标用于评估radar速度，true：保存，false：不保存     -->
        <param name="isSaveObjectInfoCSV" value="false" type="bool"/>

        <!--RADAR-INS外参-->
        <rosparam file="$(find sensorradar)/param/radar.yaml" command="load"/>
    </node>
   <!-- <node pkg="rviz" type="rviz" name="sensorradarrviz" args="-d $(find sensorradar)/launch/hsq_sensorradar.rviz" required="true"/> -->
</launch>