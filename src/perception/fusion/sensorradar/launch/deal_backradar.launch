<launch>

    <!--后雷达 -->
   <node name="radarback_driver"  pkg="radarars_driver"   type="radarars_driver"   output="screen" respawn="true">
        <param name="radardeltax" value="-30"  type="int"/>
        <!---460 cm-->
        <param name="radardeltay" value="-455"  type="int"/>
        <!--18000-->
        <param name="radardeltayaw" value="-17980"   type="int"/>
        
        <param name="radarcanetip" value="*************" type="string"/>
        <param name="radarcanetport" value="4004" type="int"/>
        <param name="radarpcip" value="*************" type="string"/>
        <param name="radarpcport" value="4004" type="int"/>
        <!--是否进行在线标定 0:否 1：是-->
        <param name="radarcali" value="0"   type="int"/> 

   </node>  
   <node name="radarback_deal"    pkg="radarars_deal"   type="radarars_deal"   output="screen" respawn="true"/>

   

   <!--毫米波雷达融合-->
   <node name="sensorradar"   pkg="sensorradar"   type="sensorradar"       output="screen" respawn="true"/> 

</launch>