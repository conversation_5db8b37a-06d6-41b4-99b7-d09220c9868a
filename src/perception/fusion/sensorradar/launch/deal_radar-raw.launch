<launch>
<!--    <param name="carNumber" value="1" type="int"/>-->
 <!--4650×1820×1510   44  70 | 22 75-->
    <rosparam file="$(find fusiontracking)/launch/params.yaml" command="load"/>

   <node name="radarfront_driver"  pkg="radar_driver"   type="radar_driver"   output="screen" respawn="true">
         <!--单位：cm -->
        <param name="radardeltax" value="37"  type="int"/>
        <param name="radardeltay" value="0"  type="int"/>
         <!--单位：0.01度 -->
        <param name="radardeltayaw" value="50"   type="int"/>

        <param name="radarcanetip" value="127.0.0.1" type="string"/>
        <param name="radarcanetport" value="7777" type="int"/>
        <param name="radarpcip" value="127.0.0.1" type="string"/>
        <param name="radarpcport" value="8888" type="int"/>

        <param name="radarcali" value="0"   type="int"/>
        <param name="radarupdown" value="0"   type="int"/>
        <param name="radardebug" value="1"   type="int"/>

   </node>
   <node name="radarfront_deal"    pkg="radar_deal"   type="radar_deal"   output="screen" respawn="true"/>


    <!--左后雷达 -->
   <node name="radarleftback_driver"  pkg="radar_driver"   type="radar_driver"   output="screen" respawn="true">
        <!--单位：cm -107-->
        <param name="radardeltax" value="95"  type="int"/>
        <!--单位：cm-->
        <param name="radardeltay" value="-470"  type="int"/>
        <!--单位：0.01度-->
        <param name="radardeltayaw" value="-2800"   type="int"/>
        <!--canet/pc ip(网络地址) 和 port(端口号)-->
        <param name="radarcanetip" value="127.0.0.1" type="string"/>
        <param name="radarcanetport" value="1111" type="int"/>
        <param name="radarpcip" value="127.0.0.1" type="string"/>
        <param name="radarpcport" value="2222" type="int"/>
        <!--是否进行在线标定 0:否 1：是-->
        <param name="radarcali" value="0"   type="int"/>
        <param name="radardebug" value="0"   type="int"/>


   </node>
      <node name="radarleftback_deal"    pkg="radar_deal"   type="radar_deal"   output="screen" respawn="true"/>


    <!--右后雷达 -->
   <node name="radarrightback_driver"  pkg="radar_driver"   type="radar_driver"   output="screen" respawn="true">
        <!--单位：cm 127-->
        <param name="radardeltax" value="-95"  type="int"/>
        <!--单位：cm -640-->
        <param name="radardeltay" value="-470"  type="int"/>
        <!--单位：0.01度 -->
        <param name="radardeltayaw" value="2900"   type="int"/>
        <!--canet/pc ip(网络地址) 和 port(端口号)-->
        <param name="radarcanetip" value="127.0.0.1" type="string"/>
        <param name="radarcanetport" value="3333" type="int"/>
        <param name="radarpcip" value="127.0.0.1" type="string"/>
        <param name="radarpcport" value="4444" type="int"/>
        <!--是否进行在线标定 0:否 1：是-->
        <param name="radarcali" value="0"   type="int"/>
        <param name="radardebug" value="0"   type="int"/>

   </node>
    <node name="radarrightback_deal"    pkg="radar_deal"   type="radar_deal"   output="screen" respawn="true"/>


<!--     <arg name="isUseHongQi2" default="true"/>-->

   <!--毫米波雷达融合-->
   <node name="sensorradar"   pkg="sensorradar"   type="sensorradar"       output="screen" respawn="true"/>
<!--        <param name="carNumber" value="1" type="int"/>-->
        <!--RADAR-INS外参-->
        <rosparam file="$(find sensorradar)/param/radar.yaml" command="load"/>


</launch>