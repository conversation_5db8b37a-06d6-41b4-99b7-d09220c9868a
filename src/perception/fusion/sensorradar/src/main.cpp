
/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	main.cpp
Description: 毫米波雷达融合程序，主函数。
              This node is used to receive radar data,  process radar data and publish fusion and driverable data.	
coordinate： 笛卡尔坐标系 横坐标为x,纵坐标为y              
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/ 
#include "sensorradar.h"

int main(int argc, char *argv[])
{
  ros::init(argc, argv, "sensorradar");
  ros::param::set("/version/sensorradar", "2024_06_12_3.2.2");
  ros::NodeHandle mh;
  SensorRadar node(mh);
  node.Run();
  return 0;
}





