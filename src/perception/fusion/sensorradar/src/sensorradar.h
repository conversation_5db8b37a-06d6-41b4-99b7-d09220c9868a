
/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	sensorradar.h
Description: 毫米波雷达融合程序，通过加权平均对数据进行滤波。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/


#ifndef NODE_SENSORRADAR_H
#define NODE_SENSORRADAR_H
#include <iostream>
#include <typeinfo>
#include <cxxabi.h>
#include <cstring>
#include <stdio.h>

//ros header
#include "ros/ros.h"
#include "ros/time.h"
#include "ros/package.h"
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>

//user header
// #include "sensorradar/sensorradar.h"
// #include "sensorradar/radarobject.h"
#include "radar_msgs/sensorradar.h"
#include "radar_msgs/radarobject.h"

#include "../../commonlibrary/src/common.h"
#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"
#include "common_msgs/sensorstatus.h"
#include "common_msgs/elapsedtime.h"
#include "ars40x_srr308_msgs/Object.h"//20221201
#include "ars40x_srr308_msgs/ObjectList.h"//20221201

//third part header
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl_ros/point_cloud.h>
#include <pcl/point_types.h>
#include <Eigen/Dense>

#define DELTA_DIS (2.5)//纵向距离阈值m
#define DELTA_LAT (1.5)//横向距离阈值m
#define DELTA_REL (2)//相对速度阈值  m/s

#define    MAXSIZE 25000
#define    M_PI    3.14159265358979323846

#define    CAR_LENGTH 4.7
#define    CAR_WIDE   1.8
#define    LANE_WIDE  2.8


/* FOREGROUND */
#define DEBUGHEAD "[sensorradar-->]"
#define RST  "\x1B[0m"
#define KRED  "\x1B[31m"
#define KGRN  "\x1B[32m"
#define KYEL  "\x1B[33m"
#define KBLU  "\x1B[34m"
#define KMAG  "\x1B[35m"
#define KCYN  "\x1B[36m"
#define KWHT  "\x1B[37m"

#define FRED(x) KRED x RST
#define FGRN(x) KGRN x RST
#define FYEL(x) KYEL x RST
#define FBLU(x) KBLU x RST
#define FMAG(x) KMAG x RST
#define FCYN(x) KCYN x RST
#define FWHT(x) KWHT x RST

#define BOLD(x) "\x1B[1m" x RST
#define UNDL(x) "\x1B[4m" x RST

using namespace std;

enum CARNUMBER{// 20221110
	Othercar = 0,
	HongQi1 = 1,
	HongQi2 = 2
};

enum RADARTYPE
{
	RADAR_FRONT=0,
	RADAR_LEFTFRONT=1,
	RADAR_RIGHTFRONT=2,
	RADAR_BACK=3,
	RADAR_LEFTBACK=4,
	RADAR_RIGHTBACK=5
};

const int RADARNUM = 6;

const long DELAYTIME = 40; // 40/20 = 2s

typedef pcl::PointCloud<pcl::PointXYZI> PointCloud;
typedef pcl::PointXYZI PointT;
/************************************************************************/
/*                      雷达融合相关变量                                */
/************************************************************************/

typedef struct _Data_Rec
{
	int   id;
	float Distance;             /***The Longitudinal Coordinate of the Object***/
	float LatPos;               /***The Horizontal Coordinate of the Object***/
	float RelSpd_Long;       	/***The Longitudinal Relative Velocity of the Object***/
	float RelSpd_Lat;        	/***The Horizontal Relative Velocity of the Object***/

	float width;       //m
	float length;      //m
	unsigned char classification;

	char  MoveFlag;           	/***The flag of the moving or static Object***/ 
	char  ValidLevel;         	/***The valid level of the Object***/
	char  RadarIndex;         	/***which radar of the Object owning***/
} Data_Rec;//直角坐标下的目标信息

typedef struct _TarFusion
{
	int NumOfValid;              /***The num of the valid Object***/
	Data_Rec TargetData[1536];   /***The information of the Object(256 * 6)***/
} TarFusion;//融合后的目标


class SensorRadar
{
	public:
	// 各个lidar话题
	std::string SUBTOPIC_RADARLEFTFRONT;
	std::string SUBTOPIC_RADARRIGHTFRONT;
	std::string SUBTOPIC_RADARLEFTBACK;
	std::string SUBTOPIC_RADARRIGHTBACK;
	std::string SUBTOPIC_RADARFRONT;
	std::string SUBTOPIC_RADARBACK;
	
	int m_carNumber;//20221110 区分车辆
	bool isRadarPosionSpeedVerify;//单独运行节点，显示radar目标速度设置为true，运行fusion设置为false
	bool m_isUseRosBag;//20221208 bag包调试用
	bool m_isShowRadarNumber;
	int m_showRadarNumber;//显示单个radar信息
	bool m_isSaveObjectInfoCSV; // 保存radar目标信息
	std::string radarObjectsSavedPath;
	fstream radarObjectInfoFile;
	
	
	/// radar-INS外参-20221111
	//#    i.前ARS-408毫米波-0
	float frontRotation_x;
	float frontRotation_y;
	float frontRotation_z;
	float frontRotation_w;
	float frontTranslation_x;
	float frontTranslation_y;
	float frontTranslation_z;
	Eigen::Quaternion<float> frontQuaternion;
	Eigen::Vector3f frontTranslation;
	

	//#    ii.左前SRR-308毫米波-1
	float leftFrontRotation_x;
	float leftFrontRotation_y;
	float leftFrontRotation_z;
	float leftFrontRotation_w;
	float leftFrontTranslation_x;
	float leftFrontTranslation_y;
	float leftFrontTranslation_z;
	Eigen::Quaternion<float> leftFrontQuaternion;
	Eigen::Vector3f leftFrontTranslation;

	//#    iii.右前SRR-308毫米波-2
	float rightFrontRotation_x;
	float rightFrontRotation_y;
	float rightFrontRotation_z;
	float rightFrontRotation_w;
	float rightFrontTranslation_x;
	float rightFrontTranslation_y;
	float rightFrontTranslation_z;
	Eigen::Quaternion<float> rightFrontQuaternion;
	Eigen::Vector3f rightFrontTranslation;
	
	//#    后SRR-308毫米波-3
	float backRotation_x;
	float backRotation_y;
	float backRotation_z;
	float backRotation_w;
	float backTranslation_x;
	float backTranslation_y;
	float backTranslation_z;
	Eigen::Quaternion<float> backQuaternion;
	Eigen::Vector3f backTranslation;

	//#    iv.左后SRR-308毫米波-4
	float leftBackRotation_x;
	float leftBackRotation_y;
	float leftBackRotation_z;
	float leftBackRotation_w;
	float leftBackTranslation_x;
	float leftBackTranslation_y;
	float leftBackTranslation_z;
	Eigen::Quaternion<float> leftBackQuaternion;
	Eigen::Vector3f leftBackTranslation;

	//#    v.右后SRR-308毫米波-5
	float rightBackRotation_x;
	float rightBackRotation_y;
	float rightBackRotation_z;
	float rightBackRotation_w;
	float rightBackTranslation_x;
	float rightBackTranslation_y;
	float rightBackTranslation_z;
	Eigen::Quaternion<float> rightBackQuaternion;
	Eigen::Vector3f rightBackTranslation;
	
	
	
    /*************************************************
	Function:       SensorRadar
	Description:    构造函数，主要用于数据初始化
	Input:          handle：ros句柄
	Output:         无
	Return:         无
	*************************************************/  	
	SensorRadar(ros::NodeHandle mh);
    /*************************************************
	Function:       ~SensorRadar
	Description:    析构函数
	Input:          无
	Output:         无
	Return:         无
	*************************************************/ 	
	~SensorRadar();

   /*************************************************
	Function:       Run
	Description:    主程序
	Input:          无
	Output:         无
	Return:         无
	*************************************************/  	
	void Run();

	private:
	/*************************************************
	Function:		// DataProcess
	Description: 	// 数据处理
	Calls: 			// PubMsg
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 多个毫米波雷达数据	
	Output: 		// 融合后毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void DataProcess();
	/*************************************************
	Function:		// SubCallback_radarfront
	Description: 	// Receive  Data from front radar node
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 前毫米波雷达数据	
	Output: 		// 前毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void SubCallback_radarfront(const ars40x_srr308_msgs::ObjectList &msg); //20221201 hongqi2
	void SubCallback_radarfrontCar1(const radar_msgs::sensorradar::ConstPtr &msg);
	/*************************************************
	Function:		// SubCallback_radarleftfront
	Description: 	// Receive  Data from left front radar node 
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 左前毫米波雷达数据	
	Output: 		// 左前毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void SubCallback_radarleftfront(const ars40x_srr308_msgs::ObjectList &msg);//20221111 hongqi2
	void SubCallback_radarleftfrontCar1(const radar_msgs::sensorradar::ConstPtr &msg);
	/*************************************************
	Function:		// SubCallback_radarrightfront
	Description: 	// Receive  Data from right front radar node 
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 右前毫米波雷达数据	
	Output: 		// 右前毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void SubCallback_radarrightfront(const ars40x_srr308_msgs::ObjectList &msg);
	void SubCallback_radarrightfrontCar1(const radar_msgs::sensorradar::ConstPtr &msg);
	/*************************************************
	Function:		// SubCallback_radarback
	Description: 	// Receive  Data from back radar node
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 后毫米波雷达数据	
	Output: 		// 后毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void SubCallback_radarback(const ars40x_srr308_msgs::ObjectList &msg);
	void SubCallback_radarbackCar1(const radar_msgs::sensorradar::ConstPtr &msg);
	/*************************************************
	Function:		// SubCallback_radarleftback
	Description: 	// Receive  Data from left back radar node
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 左后毫米波雷达数据	
	Output: 		// 左后毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void SubCallback_radarleftback(const ars40x_srr308_msgs::ObjectList &msg);
	void SubCallback_radarleftbackCar1(const radar_msgs::sensorradar::ConstPtr &msg);
	/*************************************************
	Function:		// SubCallback_radarrightback
	Description: 	// Receive  Data from right back radar node
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 右后毫米波雷达数据	
	Output: 		// 右后毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
	void SubCallback_radarrightback(const ars40x_srr308_msgs::ObjectList &msg);
	void SubCallback_radarrightbackCar1(const radar_msgs::sensorradar::ConstPtr &msg);
	/*************************************************
	Function:		// PubMsg
	Description: 	// the Function of Publishing radar Data
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 融合后毫米波雷达数据	
	Output: 		// 无
	Return: 		// 无
	Others: 		// 无
	*************************************************/
    void PubMsg();

	radar_msgs::sensorradar radarTypeTrans(const ars40x_srr308_msgs::ObjectList& ars40x_srr308_RadarObjects,
										const uint8_t& radarIndex);//20221201
	void loadCaliParam(ros::NodeHandle mh);//20221111
	void transformPoint(const Eigen::Vector3f& inputposition,const Eigen::Quaternion<float>& quaternion,
	                                 const Eigen::Vector3f& translation,Eigen::Vector3f& outputPosition);//20221111
	inline double DegToRad(double deg); //20221111
	void transformRadarPoint(radar_msgs::sensorradar& radarObjects, const uint8_t& radarIndex);
	void transformSpeed(const Eigen::Vector3f& inputposition,const Eigen::Quaternion<float>& quaternion,
                                 const Eigen::Vector3f& translation,Eigen::Vector3f& outputPosition);//20221129
	void showObjectInfo(const radar_msgs::sensorradar& radarObjects, 
								  const std::string& frameID);
	void showObjectMarker(const int& trackNum, const radar_msgs::radarobject& singleObject,
	                      const std::string& frameID,
	                      visualization_msgs::MarkerArray& objectfusionMarkerArray
	);
	void sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source);
	
	//各个雷达的数据
    radar_msgs::sensorradar radarMsg[RADARNUM];
    
	//融合后数据
	TarFusion stTarFusion;//一帧中5个radar目标融合后的数据

	int  bdebug;
	int  loopFrep;
	std::string                   		updatelog;
	std::vector<std::vector<float>> class_color = {{1.0, 0.0, 0},{0.0, 1.0, 0},{0.0, 0.0, 1.0},{1.0, 1.0, 0},{0, 1.0, 1.0},{0.5, 0.0, 1.0},{0, 0.5, 1},{0.5, 0.5, 0}};

    
	//发布融合后数据
	ros::Publisher      	   			pub_radar;
	ros::Publisher      	   			pub_show;
	ros::Publisher      	   			sensorradarMarkerArrayPub;//20221130
	ros::Publisher                      pub_sensorradarMarkerArrayInfo;
	//订阅各个雷达的topic
	ros::Subscriber                     sub_radarfront;
	ros::Subscriber       	   			sub_radarleftfront;
	ros::Subscriber       	   			sub_radarrightfront;
	ros::Subscriber       	   			sub_radarback;	
	ros::Subscriber       	   			sub_radarleftback;
	ros::Subscriber       	   			sub_radarrightback;
    
	//雷达超时的标志位
	long 								m_radarcnt[RADARNUM];
    
	
	//发布各个雷达的工作状态
	common_msgs::sensorstatus front_radar_status;
    ros::Publisher   pub_frontradarstatus;
	common_msgs::sensorstatus leftfront_radar_status;
    ros::Publisher   pub_leftfrontradarstatus;	
	common_msgs::sensorstatus rightfront_radar_status;
    ros::Publisher   pub_rightfrontradarstatus;	
    
	
	common_msgs::sensorstatus back_radar_status;
    ros::Publisher   pub_backradarstatus;
	common_msgs::sensorstatus leftback_radar_status;
    ros::Publisher   pub_leftbackradarstatus;	
	common_msgs::sensorstatus rightback_radar_status;
    ros::Publisher   pub_rightbackradarstatus;
	
	ros::Publisher pub_sensorradarElapsedtime;

};
#endif // NODE_EXAMPLE_TALKER_H
