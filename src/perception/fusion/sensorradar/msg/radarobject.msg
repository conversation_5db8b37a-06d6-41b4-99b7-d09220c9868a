uint8 id
float32 x
float32 y
float32 relspeedx
float32 relspeedy
float32 length
float32 width
uint8   classification
uint8   flag    #fusionFlag
int8 DistLong_rms  
uint8 DistLat_rms   
uint8 VrelLong_rms
uint8 VrelLat_rms
uint8 ArelLong_rms
uint8 ArelLat_rms
uint8 Orientation_rms
uint8 DynProp   #Object目标的动态特性，指示Object目标是移动的还是静止的,0~7 1 0x0: 运动的   0x1: 静止的  0x2: 来向的  0x3: 静止候选的 0x4: 未知的 0x5: 横穿静止的 0x6: 横穿运动的 0x7: （运动后）停止的
float32 RCS     #雷达散射截面积 RCS- 64.0 63.5 0.5 dBm²
uint8 MeasState #测量状态，指示Object目标是否有效，并已在新测量周期中由Cluster目标进行了确认0 7 1  0x0: 已删除 0x1: 新创建的 0x2: 测量的 0x3: 预测的 0x4: 已删除以进行合并聚类 0x5: 从合并聚类中新创建的
uint8 ProbOfExist   #存在可能性0 7 1  0x0: 无效 0x1: <25% 0x2: <50% 0x3: <75% 0x4: <90% 0x5: <99% 0x6: <99.9% 0x7: <=100%
float32 ArelLong    #Object目标的纵向相对加速度 - 10.00 10.47 0.01 m/s²
float32 ArelLat     #Object目标的横向相对加速度
uint8 Class
float32 OrientationAngel    #Object目标的方向角- 180.00 180.00 0.4 deg
float32 Length
float32 Width
uint8  CollDetRegionBitfield


# Header header
# uint8 obstacle_id
# float32 longitude_dist
# float32 lateral_dist
# float32 longitude_vel
# float32 lateral_vel
# float32 rcs
# float32 orientation_angle
# float32 length
# float32 width
# string obstacle_class
# uint8 meas_state
# uint8 flag

