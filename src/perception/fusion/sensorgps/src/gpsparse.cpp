
/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	gpsparse.cpp
Description: 组合导航解析程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2022.6.3
History: 	无
**************************************************************************/


#include "gpsparse.h"

#include<stdio.h>
#include<stdlib.h>
#include<fcntl.h>
#include<unistd.h>
#include<assert.h>
#include<termios.h>
#include<string.h>
#include<sys/time.h>
#include<time.h>
#include<sys/types.h>
#include<errno.h>
#include <ros/package.h>



static int ret;//返回的字符数
static int fd;//句柄
static unsigned char chrBuf[200];//串口缓冲区
static unsigned char chrCnt = 0;//串口计数

bool loopsave = false;  //保存的标志位
int last_roadattr = -1; //动态参数获取的上一次的 roadattr.

  /*************************************************
	Function:       gpsparse
	Description:    构造函数，主要是参数初始化
	Input:          nh: 句柄
	Output:         无
	Return:         无
	*************************************************/ 
gpsparse::gpsparse(ros::NodeHandle nh) {
	  baudrate = 115200;
	  serialport = "/dev/ttyUSB0"; //ttyUSB0 需要进行修改
	  runningmode = 0;
	  isSaveFile = 1;
	  loopFrep = 10;
	  m_mile = 0.0;
	  bdebug = 1;
	  updatelog = "null";
	  fp = NULL;
	  m_accuracyLossTime = 2;
	
	  m_canetip = "*************";
	  m_canetport = 4011;
	  m_pcip = "*************";
	  m_pcport = 4011;
	  m_serialcan = 0;
	  last_property_ = 0;
	  use_inspva = true;//20221128
	  use_inspvax = true;
	  use_imu = true;
	  isUseRosBag = false;
	  isSaveSelfCarInfo = false;
	  carNumber = 2;
	  isOnline = true;
	  lastRp.lat = 0;
	  lastRp.lon = 0;

    m_position1_lon = 0.0;
    m_position1_lat = 0.0;
    m_position2_lon = 0.0;
    m_position2_lat = 0.0;
    m_position3_lon = 0.0;
    m_position3_lat = 0.0;
    m_position4_lon = 0.0;
    m_position4_lat = 0.0;
	
	  std::string node_name = ros::this_node::getName(); //获取节点名称
	  nh.param(node_name+"/gpsserialcan",m_serialcan,m_serialcan);
	  nh.param(node_name+"/gpscanetip",m_canetip,m_canetip);
	  nh.param(node_name+"/gpscanetport",m_canetport,m_canetport);
	  nh.param(node_name+"/gpspcip",m_pcip,m_pcip);
	  nh.param(node_name+"/gpspcport",m_pcport,m_pcport);
	  nh.param(node_name+"/baudrate",baudrate,baudrate);
	
	  nh.param(node_name+"/useinspva",use_inspva,use_inspva);
	  nh.param(node_name+"/useinspvax",use_inspvax,use_inspvax);
	  nh.param(node_name+"/useimu",use_imu,use_imu);
	  nh.param("/isUseRosBag",isUseRosBag,isUseRosBag);
	  nh.param(node_name+"/isSaveSelfCarInfo",isSaveSelfCarInfo,isSaveSelfCarInfo);
	  nh.param("/carNumber",carNumber,carNumber);
	  nh.param("/gps/isOnline",isOnline,isOnline);
	  nh.param("/gps/accuracyLossTime",m_accuracyLossTime,m_accuracyLossTime);

    nh.param("/slamLocationRange/position1_lon",m_position1_lon,m_position1_lon);
    nh.param("/slamLocationRange/position1_lat",m_position1_lat,m_position1_lat);
    nh.param("/slamLocationRange/position2_lon",m_position2_lon,m_position2_lon);
    nh.param("/slamLocationRange/position2_lat",m_position2_lat,m_position2_lat);
    nh.param("/slamLocationRange/position3_lon",m_position3_lon,m_position3_lon);
    nh.param("/slamLocationRange/position3_lat",m_position3_lat,m_position3_lat);
    nh.param("/slamLocationRange/position4_lon",m_position4_lon,m_position4_lon);
    nh.param("/slamLocationRange/position4_lat",m_position4_lat,m_position4_lat);

    if(carNumber == 1){
      serialport = "/dev/ttyTHS1";
    }
    else if(carNumber == 2){
      serialport = "/dev/ttyUSB0";
    }
    else{
      serialport = "/dev/ttyUSB0";
    }

    m_slamPolygon = {
        {m_position1_lon, m_position1_lat},
        {m_position2_lon, m_position2_lat},
        {m_position3_lon, m_position3_lat},
        {m_position4_lon, m_position4_lat}
    };

	  
	  
	  //20221115 将txt文件输出设置为相对路径，方便代码移植。 这里的txt文件存储在最外层src文件夹下的outputMapping下的rawfiles、mapfiles中.
	  std::string path_txt = ros::package::getPath("sensorgps");//获取backage包的路径
	  cout << "sensorgps path: " << path_txt << endl;
	  int top_n = 2;//需要输出当前路径的前 top_n 级目录
	  for (int i = 0; i < top_n; ++i) {
		  int path_txt_last = path_txt.find_last_of("/\\");//反向查找, 遇到" "中任一字符均返回
		  std::string path_txt_1 = path_txt.substr(0, path_txt_last);
		  path_txt = path_txt_1;
	  }
	  //cout<<"name-top_n:" <<path_txt<<endl;
	
	  //if(username != ""){
		  if (isOnline){
			  uid_t uid = geteuid();
			  struct passwd *pw = getpwuid(uid);
			  std::string username = "";
			  std::string mapname = "";
			  if (pw){
				  username = pw->pw_name;
				  cout << "username: " << username << endl;
			  }
			  saveDir = "/home/" + username + "/VanjeeAdMap/rawfiles/";
			  saveTrajDir = "/home/" + username + "/VanjeeAdMap/mapfiles/";
		  }
		  else {
			  saveDir = path_txt + "/outputMapping/rawfiles/";          //相对路径
			  saveTrajDir = path_txt + "/outputMapping/mapfiles/";
		  }
	  // }

  std::string mkdirSaveDirCommand = "mkdir -p " + saveDir;
  std::system(mkdirSaveDirCommand.c_str());

  std::string mkdirSaveTrajDirCommand = "mkdir -p " + saveTrajDir;
  std::system(mkdirSaveTrajDirCommand.c_str());

	  
	cout<<"saveDir: "<<saveDir<<endl;
	cout<<"saveTrajDir: "<<saveTrajDir<<endl;



   
   if(isSaveSelfCarInfo){
	   std::string startTimeStamp = std::to_string(ros::Time::now().toSec());
	   std::string savePathName = path_txt + "/outputMapping/selfCarInfo/" + startTimeStamp + ".csv";
	   cout<<"selfCarInfo path_txt: " << path_txt << endl;
	   
	   saveSelfCarInofFile.open(savePathName, ios::out | ios::app);
	   if(!saveSelfCarInofFile.is_open()){
		   fstream tempFile(savePathName, ios::out | ios::app);
		   tempFile.close();
		   saveSelfCarInofFile.open(savePathName, ios::out | ios::trunc);
		   if(!saveSelfCarInofFile.is_open())
		        cerr<<"file open error: " << savePathName << endl;
	   }
	   else{
		   cout<<"selfCarInfo save PathName: " << savePathName << endl;
	   }
   }


    //paramters
  std::cout << FRED("Copyright©2021-2023 VANJEE Technology. All rights reserved ") << std::endl;
  std::cout << FYEL("*****sensorgps:parameters*******************") << std::endl;
  std::cout << FGRN("gps_baudrate: ") << baudrate << std::endl;
  std::cout << FGRN("gps_serialport: ") << serialport << std::endl;
  std::cout << FGRN("gps_runningmode:(0-dpcar, 1-simualtion) ") << runningmode << std::endl;
  std::cout << FGRN("gps_loopfrep: ") << loopFrep << std::endl;
  std::cout << FGRN("gps_issave: ") << isSaveFile << std::endl;
  std::cout << FGRN("gps_savepath: ") << savePath << std::endl;
  std::cout << FGRN("bdebug:") << bdebug << std::endl;
  std::cout << FGRN("updatelog: ") << updatelog << std::endl;
  std::cout << FGRN("use_inspva: ") << use_inspva << std::endl;
  std::cout << FGRN("use_inspvax: ") << use_inspvax << std::endl;
  std::cout << FGRN("use_imu: ") << use_imu << std::endl;
  std::cout << FGRN("isUseRosBag: ") << isUseRosBag << std::endl;
  std::cout << FGRN("isSaveSelfCarInfo: ") << isSaveSelfCarInfo << std::endl;
	std::cout << FGRN("carNumber: ") << carNumber << std::endl;
	std::cout << FGRN("isOnline: ") << isOnline << std::endl;
	std::cout << FGRN("m_accuracyLossTime: ") << m_accuracyLossTime << std::endl;

  std::cout << FGRN("m_position1_lon: ") << m_position1_lon << std::endl;
	std::cout << FGRN("m_position1_lat: ") << m_position1_lat << std::endl;

  std::cout << FYEL("*****sensorgps:parameters end***************") << std::endl;

  //data initialize
  memset(&rp, 0, sizeof(rp));
  memset(&addattr, 0, sizeof(addattr));
  memset(&last_inspvax, 0, sizeof(last_inspvax));//20221114

  //获取存在的目录文件
  struct dirent *ptr;
  DIR *dir;
  dir=opendir(saveTrajDir.c_str());
  cout<<"saveTrajDir =" <<saveTrajDir<<endl;
  while((ptr=readdir(dir))!=NULL)
  {
      string str=ptr->d_name;
      m_ExistFileDir.push_back(str);
  }
	
	sub_collectmap=nh.subscribe(SUBTOPIC_COLLECTMAP,2,&gpsparse::SubCallback_collectmap,this);
	sub_requestmap=nh.subscribe(SUBTOPIC_REQUESTMAP,2,&gpsparse::SubCallback_requestmap,this);
	sub_collectpoint=nh.subscribe(SUBTOPIC_COLLECTPOINT,2,&gpsparse::SubCallback_collectpoint,this);
	sub_actuator = nh.subscribe("actuator", 2, &gpsparse::subCallback_actuator,this);
	sub_inspvaMsg = nh.subscribe("/novatel/oem7/inspva", 2, &gpsparse::SubCallback_inspva, this); //20221110 订阅INSPVA
	sub_inspvaxMsg = nh.subscribe("/novatel/oem7/inspvax", 2, &gpsparse::SubCallback_inspvax, this); //20221110 订阅INSPVAX
	sub_corrimuMsg = nh.subscribe("/novatel/oem7/corrimu", 2, &gpsparse::SubCallback_corrimu, this); //20221128 订阅CORRIMU
	sub_imuMsg = nh.subscribe("/gps/imu", 2, &gpsparse::SubCallback_imu, this); //20221207订阅IMU

  //Create a publisher and name the topic.
  pub_gps = nh.advertise<common_msgs::sensorgps>("sensorgps", 1);
  pub_imu = nh.advertise<sensor_msgs::Imu>("/imu_gps", 1);
  srvSaveTraj = nh.advertiseService("/save_traj", &gpsparse::saveTrajHandler, this);
  pub_lonlatmap=nh.advertise<common_msgs::lonlatmappoints>(PUBTOPIC_LONLATMAP,20);
  pub_gpsstatus = nh.advertise<common_msgs::sensorstatus>("gps_status",1000);
  
}

/*************************************************
  Function:       ~gpsparse
  Description:    析构函数
  Input:          无
  Output:         无
  Return:         无
  *************************************************/
gpsparse::~gpsparse(){
	if(saveSelfCarInofFile.is_open())
		saveSelfCarInofFile.close();
}

  /*************************************************
	Function:       run
	Description:    主程序。
	Input:          无
	Output:         无
	Return:         无
	*************************************************/ 
void gpsparse::run()
{
	unsigned char r_buf[10000];
	bzero(r_buf,10000);
	if(carNumber == 1){
		if(m_serialcan == 0)
		{
		  fd = uart_open(fd,serialport.c_str());/*串口号/dev/ttySn,USB口号/dev/ttyUSBn */
		  if(fd == -1)
		  {
		      fprintf(stderr,"uart_open error\n");
		      exit(EXIT_FAILURE);
		  }

		  if(uart_set(fd,baudrate,8,'N',1) == -1)
		  {
		      fprintf(stderr,"uart set failed!\n");
		      exit(EXIT_FAILURE);
		  }
		}
		else
		{
		      //bind socket and receive_data
		    boost::asio::io_service io_service;
		    myCanetUdp = new Boost_UDP(io_service,m_pcip,m_pcport,m_canetip,m_canetport);
		    myCanetUdp->start_sock();
		}
	}
  ros::Rate rate(400); //loop at 10 hz until ros is shut down  //400
  long cntt= 0;
  while (ros::ok())
  {
    
   long timestamp = ros::Time::now().toSec()*1000;
   ros::spinOnce();
    if((timestamp-cntt)>1000)
    {
      ros::spinOnce();
      ros::NodeHandle n;
      if(timestamp-m_collectmap.timestamp > 3*1000)
      {
        m_collectmap.property=0;
      }
      if(m_collectmap.property==0)
      {
          addattr.roadattr=0;
          n.getParam("road_attr", addattr.roadattr);
          n.getParam("lane_attr",addattr.laneattr);
          n.getParam("speed_attr",addattr.speed);
          n.getParam("mergelane_attr",addattr.mergelanetype);
          n.getParam("sensorlanetype_attr",addattr.sensorlanetype);
          n.getParam("sideroadwidth",addattr.sideroadwidth);
          n.getParam("egolanewidth",addattr.egolanewidth);
          n.getParam("leftlanewidth",addattr.leftlanewidth);
          n.getParam("rightlanewidth",addattr.rightlanewidth);
          n.getParam("leftsearchdis",addattr.leftsearchdis);
          n.getParam("rightsearchdis",addattr.rightsearchdis);
      }
      else
      {
          addattr.roadattr=m_collectmap.property;
          addattr.laneattr=m_collectmap.laneattr;
          addattr.speed=m_collectmap.speed;
          addattr.mergelanetype=m_collectmap.mergelanetype;
          addattr.sensorlanetype=m_collectmap.sensorlanetype;
          addattr.sideroadwidth=m_collectmap.sideroadwidth;
          addattr.egolanewidth=m_collectmap.lanewidth;
          addattr.leftlanewidth=m_collectmap.leftlanewidth;
          addattr.rightlanewidth=m_collectmap.rightlanewidth;
          addattr.leftsearchdis=m_collectmap.leftsearchdis;
          addattr.rightsearchdis=m_collectmap.rightsearchdis;
		 
          addattr.laneswitch = m_collectmap.laneswitch;
          addattr.sidepass = m_collectmap.sidepass;
          addattr.lanenum = m_collectmap.lanenum;
          addattr.lanesite = m_collectmap.lanesite;
		  
          // cout<<"addattr.roadattr:"<<addattr.roadattr<<",addattr.laneattr:"<<addattr.laneattr<<endl;
      }
      cntt = timestamp;
    }
	
	if(carNumber == 1){
		// cout<<"m_serialcan:"<<m_serialcan<<endl;
		if(m_serialcan == 0)
		{
		  ret = recv_data(fd,r_buf,10000);
		  if(ret == -1)
		  {
		      exit(EXIT_FAILURE);
		  }
		  else if(ret == 0)
		  {
		    rate.sleep();
		    continue;
		  }
		  // std::cout << "ret: " << ret << std::endl;
		  for (int i=0;i<ret;i++)
		  {
		      ParseData(r_buf[i]);
		  }
		}
		else
		{
		  int ret = myCanetUdp->receive_data(r_buf); //ret表示的是数据长度的字节数
		  //std::cout << "ret: " << ret << std::endl;
		  long timestamp = ros::Time::now().toSec()*1000;

		  if ((ret>0) && (ret % 13 == 0))              //13　　接收13的倍数个字节的数据
		  {
		     ParserData(r_buf, ret);
		  }
		std::cout << "use time--------: " <<  ros::Time::now().toSec()*1000 - timestamp << std::endl;
		
		}
	}
	rate.sleep();
  }
  if(carNumber == 1){
	  if(m_serialcan == 0)
	  {
	    ret = uart_close(fd);
	    if(ret == -1)
	    {
	        fprintf(stderr,"uart_close error\n");
	        exit(EXIT_FAILURE);
	    }
	  }
	  else
	  {
	     delete myCanetUdp;
	  }

	    exit(EXIT_SUCCESS);
  }
}

/*************************************************
  Function:       ParseData
  Description:    解析串口数据。
  Input:          chr: 单个字符。
  Output:         无
  Return:         无
  *************************************************/
void gpsparse::ParseDataInspva()
{
    rp.lat = m_inspva.latitude;
    rp.lon = m_inspva.longitude;
    rp.alt = m_inspva.height;
    rp.speedE = m_inspva.east_velocity;
    rp.speedN = m_inspva.north_velocity;
    rp.speedD = -1.0 * m_inspva.up_velocity;
    rp.velocity = sqrt(pow(m_inspva.east_velocity, 2) + pow(m_inspva.north_velocity, 2));
    rp.roll = m_inspva.roll;//向右为正
    rp.pitch = m_inspva.pitch; //向上为正
    rp.heading = m_inspva.azimuth;
    if(rp.heading < 0){
        rp.heading += 360;//0~360
    }
    rp.gpsweek = m_inspva.nov_header.gps_week_number;//20221114
    rp.gpstime = m_inspva.nov_header.gps_week_milliseconds;

    rp.timestamp = m_inspva.header.stamp.toSec() * 1000.0;
    static int lostTimeCount = 0;

    if (use_inspvax){
        //20221116 添加INSPVAX信息
        // if (m_inspvax.header.stamp != last_inspvax.header.stamp){
            if (m_inspvax.ins_status.status == 3 && 
              (m_inspvax.pos_type.type == 56 ||
              m_inspvax.pos_type.type == 48||// L1固定解
              m_inspvax.pos_type.type == 49||// 宽巷固定解
              m_inspvax.pos_type.type == 50||// 窄巷固定解
              m_inspvax.pos_type.type == 17))// 伪距差分
            {
                rp.status = 4;//"4"表示GPS及IMU运转正常
                rp.rawstatus = 50;
                lostTimeCount = 0;
            }
            else if (m_inspvax.ins_status.status == 3){
                // 根据差分状态设置status
                switch (m_inspvax.pos_type.type)
                {
                  case 32:  // L1浮点解
                  case 33: // 消电离层浮点解
                  case 34: // 窄巷浮点解
                  case 54: // 惯导伪距差分解
                  case 55: // RTK浮点解
                    rp.status = 5;
                    lostTimeCount = 0;
                    break;
                  
                  default:
                    lostTimeCount++;
                    if(lostTimeCount % 50 == 0){
                    rp.status = 0;
                    lostTimeCount = 0;
                    }
                    break;
                }

                cout<<"GPS运转不正常 lostTimeCount = " << lostTimeCount <<endl;
                cout <<"\tins status = " << m_inspvax.ins_status.status << ", pos status = " << m_inspvax.pos_type.type << endl;
            }
            else if (m_inspvax.pos_type.type == 56){
                lostTimeCount++;
                if(lostTimeCount % 50 == 0){
                    rp.status = 0;
                    lostTimeCount = 0;
                }
                cout<<"IMU运转不正常lostTimeCount = " << lostTimeCount <<endl;
                cout <<"\tins status = " << m_inspvax.ins_status.status << ", pos status = " << m_inspvax.pos_type.type << endl;
            }
            else{
                lostTimeCount++;
                if(lostTimeCount % 50 == 0){
                    rp.status = 0;
                    lostTimeCount = 0;
                }
                cout<<"GPS及IMU运转均不正常lostTimeCount = " << lostTimeCount <<endl;
                cout <<"\tins status = " << m_inspvax.ins_status.status << ", pos status = " << m_inspvax.pos_type.type << endl;
            }
    }



    if (rp.lon > 150 || rp.lon < 100 || rp.lat > 50 || rp.lat < 20 )
    {
        rp.rawstatus = 0;
        rp.status = 0;
    }

    if (use_imu){
        Get_imuMsg(imuMsgDeque_, rp.timestamp);
        if (!imuMsgDeque_.empty()){
            m_imu = imuMsgDeque_.front();//获取距离插值时间最近的一帧数据
            rp.rollrate = m_imu.angular_velocity.y * 180.0 / M_PI; //20230104 统一到第一辆车：弧度每秒改度每秒
            rp.pitchrate = m_imu.angular_velocity.x * 180.0 / M_PI;
            rp.yawrate = m_imu.angular_velocity.z * 180.0 / M_PI;
            rp.accx = m_imu.linear_acceleration.x;//前左上
            rp.accy = m_imu.linear_acceleration.y;
            rp.accz = m_imu.linear_acceleration.z;
            imuMsgDeque_.pop_front();//删除使用过的数据
            rp.isvalid = 1;
        }
        else{
            rp.isvalid = 0; //TODO imu数据获取失败，队列中无数据
        }
    }

    Point2D p1 = BLH2XYZ(rp.lat, rp.lon, 0);
    Point2D p2 = BLH2XYZ(lastRp.lat, lastRp.lon, 0);
    bool isRecode = false;
    double dist = getDist2(p1, p2);
    if (dist >= 0.05 )
    {
        isRecode = true;
        if(dist < 0.5)
        {
            m_mile += getDist2(p1, p2);
        }
        lastRp = rp;
    }
    rp.mile = m_mile;
    publishMsg(rp); //parse finish

    if(addattr.roadattr != 0 && last_roadattr == 0) //上升沿，触发记录，重新产生新的文件
    {
        char buffer[80];
        std::time_t now = std::time(NULL);
        std::tm* pnow = std::localtime(&now);
        std::strftime(buffer, 80, "%Y%m%d_%H%M%S", pnow);
        if((int)m_collectmap.property>0)
        {
            savePath = saveTrajDir+"yuanqu"+boost::lexical_cast<string>((int)m_collectmap.zonename)+"/maping"+boost::lexical_cast<string>((int)m_collectmap.mapname)+".txt";
        }
        else
        {
            string tempPath(buffer);
            savePath = saveDir + tempPath + "_maping1.txt";
        }
         //cout<<"savePath:"<<savePath<<endl;
        safedeletefile(savePath);
        loopsave = true;
        isRecode = true;
    }
    else if(addattr.roadattr == 0 && last_roadattr != 0) //下降沿，停止记录。
    {
        loopsave = false;
    }

    last_roadattr = addattr.roadattr;
    if (isRecode && loopsave)
    {

        fp = fopen(savePath.c_str(), "a");

        char buffer[80];
        std::time_t now = std::time(NULL);
        std::tm* pnow = std::localtime(&now);
        std::strftime(buffer, 80, "%Y%m%d_%H%M%S", pnow);
        // std::string l_status = "( " + std::to_string(m_inspvax.ins_status.status)+" , "+ std::to_string(m_inspvax.pos_type.type) + " )";
        int l_status = m_inspvax.ins_status.status * 100 + m_inspvax.pos_type.type;
        // fprintf(fp, "%.8lf,%.8lf,%d,%d,%d,%.1lf,%d,%.2lf,%.2lf,%.8lf,%.8lf,%d,%d,%d,%s\n", rp.lon, rp.lat,
        //     addattr.roadattr,addattr.laneattr,m_actuator.turnLight,addattr.sideroadwidth,addattr.mergelanetype,addattr.leftsearchdis,addattr.rightsearchdis, rp.heading, rp.velocity, rp.gpstime,rp.status, rp.satenum, buffer);
        fprintf(fp, "%.8lf,%.8lf,%d,%d,%d,%d,%d,%d,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%d,%d,%d,%d,%d,%d,\n", rp.lon, rp.lat,
                addattr.roadattr,addattr.speed,addattr.laneattr,addattr.mergelanetype,addattr.sensorlanetype,m_actuator.turnLight,
                addattr.sideroadwidth,addattr.egolanewidth,addattr.leftlanewidth,addattr.rightlanewidth,addattr.leftsearchdis,
                addattr.rightsearchdis, rp.heading,l_status,rp.satenum,
                addattr.laneswitch, addattr.sidepass, addattr.lanenum, addattr.lanesite
                );
        fclose(fp);
    }

}

  /*************************************************
	Function:       ParserData
	Description:    can解析字符数组。
	Input:          data[]: 字符数组，num：字符数组个数。
	Output:         无
	Return:         无
	*************************************************/
void gpsparse::ParserData(unsigned char data[], int num) //buffer是数据缓存数组，num是数据接收的字节数．
{
  for (int i = 0; i < num / 13; ++i) //从第1３个字节以上
  {
    stCANMsg frame; //接收到的数据报文信息，包头＋ID + 数据
    CHAR2UINT ii;

    for (int j = 0; j < 4; ++j)
    {
      ii.ch[3 - j] = data[1 + j + i * 13];
    }
    unsigned int id = ii.i; //
    frame.ID = id;
    // if(!(frame.ID == 0xd0 || frame.ID == 0xd1 || frame.ID == 0xd4)) continue;

     for (int j = 0; j < 8; ++j)
      {
        frame.data[j] = data[5 + j + i * 13];
      }

      unsigned short tempInt = 0;
      unsigned int tempValue = 0;
      if (frame.ID==0x500) //接受ID为0x602的数据
      {
        tempInt = frame.data[1] + frame.data[0]*256;
        rp.accx = tempInt * 4.0/32768 - 4;//g
        tempInt = frame.data[3] + frame.data[2]*256;
        rp.accy = tempInt * 4.0/32768 - 4;//g
        tempInt = frame.data[5] + frame.data[4]*256;
        rp.accz = tempInt * 4.0/32768 - 4;//g
      }
      else if (frame.ID==0x501) //接受ID为0x201的数据
      {
        //x up+ , y右为+
        tempInt = frame.data[1] + frame.data[0]*256;
        rp.rollrate = tempInt * 0.0076293 -250;//deg/s
        tempInt = frame.data[3] + frame.data[2]*256;
        rp.pitchrate = tempInt * 0.0076293 -250;//deg/s
        tempInt = frame.data[5] + frame.data[4]*256;
        rp.yawrate = tempInt * 0.0076293 -250;//deg/s 
      }
      else if(frame.ID==0x502)
      {
        tempInt = frame.data[1] + frame.data[0]*256;
        rp.pitch = tempInt * 360.0/32768 - 360;

        tempInt = frame.data[3] + frame.data[2]*256;
        rp.roll = tempInt * 360.0/32768 - 360;
        
        tempInt = frame.data[5] + frame.data[4]*256;
        rp.heading = tempInt * 360.0/32768 - 360;

        if(rp.heading < 0)
        rp.heading += 360;
      }
      else if(frame.ID==0x503)
      {
        tempValue = frame.data[3] + frame.data[2]*256 + frame.data[1]*256*256 + frame.data[0]*256*256*256 ;//bca::charArray2Int(frame.data, 0, 4);
        rp.alt = tempValue * 0.001 - 10000;//
        
        tempValue = frame.data[7] + frame.data[6]*256 + frame.data[5]*256*256 + frame.data[4]*256*256*256 ;
        rp.gpstime = tempValue; 
      }
      else if (frame.ID==0x504) 
      {
        tempValue = frame.data[3] + frame.data[2]*256 + frame.data[1]*256*256 + frame.data[0]*256*256*256 ;//bca::charArray2Int(frame.data, 0, 4);
        rp.lat = tempValue * 0.0000001 - 180;//

        tempValue = frame.data[7] + frame.data[6]*256 + frame.data[5]*256*256 + frame.data[4]*256*256*256 ;
        rp.lon = tempValue * 0.0000001 - 180; 
      }
      else if(frame.ID==0x505)
      {
        tempInt = frame.data[1] + frame.data[0]*256;
        double speedx = tempInt * 100.0/32768 - 100;

        tempInt = frame.data[3] + frame.data[2]*256;
        double speedy = tempInt * 100.0/32768 - 100;

        tempInt = frame.data[5] + frame.data[4]*256;
        double speedz = tempInt * 100.0/32768 - 100;

        rp.velocity = sqrt(speedx*speedx + speedy*speedy);  


        if (rp.lon > 150 || rp.lon < 100 || rp.lat > 50 || rp.lat < 20 ) 
        {
          rp.rawstatus = 0;
          rp.status = 0;
        }
        Point2D p1 = BLH2XYZ(rp.lat, rp.lon, 0);
        Point2D p2 = BLH2XYZ(lastRp.lat, lastRp.lon, 0);
        bool isRecode = false;
        double dist = getDist2(p1, p2);
        if (dist >= 0.05 ) //&& rp.status == 4
        {
          isRecode = true;
          if(dist < 0.5)
          {
            m_mile += getDist2(p1, p2);
          }
          lastRp = rp;
        }
        rp.mile = m_mile;
        publishMsg(rp); //parse finish
   
      if(addattr.roadattr != 0 && last_roadattr == 0) //上升沿，触发记录，重新产生新的文件
      {
        char buffer[80];
        std::time_t now = std::time(NULL);
        std::tm* pnow = std::localtime(&now);
        std::strftime(buffer, 80, "%Y%m%d_%H%M%S", pnow);
        if((int)m_collectmap.property>0)
        {
            savePath = saveTrajDir+"yuanqu"+boost::lexical_cast<string>((int)m_collectmap.zonename)+"/maping"+boost::lexical_cast<string>((int)m_collectmap.mapname)+".txt";
        }
        else
        {
          string tempPath(buffer);
          savePath = saveDir + tempPath + "_maping1.txt";
        }
        // cout<<"savePath:"<<savePath<<endl;
        safedeletefile(savePath);
        loopsave = true;
        isRecode = true;
      }
      else if(addattr.roadattr == 0 && last_roadattr != 0) //下降沿，停止记录。
      {
        loopsave = false;
      }

      last_roadattr = addattr.roadattr;
      if (isRecode && loopsave) 
      {
        fp = fopen(savePath.c_str(), "a");
        char buffer[80];
        std::time_t now = std::time(NULL);
        std::tm* pnow = std::localtime(&now);
        std::strftime(buffer, 80, "%Y%m%d_%H%M%S", pnow);
        
        // fprintf(fp, "%.8lf,%.8lf,%d,%d,%d,%.1lf,%d,%.2lf,%.2lf,%.8lf,%.8lf,%d,%d,%d,%s\n", rp.lon, rp.lat,
        //     addattr.roadattr,addattr.laneattr,m_actuator.turnLight,addattr.sideroadwidth,addattr.mergelanetype,addattr.leftsearchdis,addattr.rightsearchdis, rp.heading, rp.velocity, rp.gpstime,rp.status, rp.satenum, buffer);
         fprintf(fp, "%.8lf,%.8lf,%d,%d,%d,%d,%d,%d,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%ld,%d,\n", rp.lon, rp.lat,
            addattr.roadattr,addattr.speed,addattr.laneattr,addattr.mergelanetype,addattr.sensorlanetype,m_actuator.turnLight,
            addattr.sideroadwidth,addattr.egolanewidth,addattr.leftlanewidth,addattr.rightlanewidth,addattr.leftsearchdis,addattr.rightsearchdis, rp.heading,rp.status,rp.satenum);
        fclose(fp);
      }

      }
      else if(frame.ID==0x506)
      {
        tempInt = frame.data[0];
       // cout<<"status: "<<tempInt<<endl;
        rp.rawstatus = tempInt;
        if(tempInt == 32 || tempInt == 33 || tempInt == 34)
        {
          rp.status = 5;
          
        }
        else if(tempInt == 48 || tempInt == 49 || tempInt == 50  || tempInt == 17)
        {
          rp.status = 4;
        }
        else
        {
          rp.status = 0;
        }
        
        if(tempInt == 0)
        {
            rp.status = 0;
        }

        tempInt = frame.data[1];
        rp.satenum = tempInt;
      }     
 
  }
}

  /*************************************************
	Function:       publishMsg
	Description:    发布组合导航数据
	Input:          data:解析后得到的组合导航数据
	Output:         无
	Return:         无
	*************************************************/ 
void gpsparse::publishMsg(sGpsPoint &data)
{
  common_msgs::sensorgps msg; //sensorgps::
  msg.lon = data.lon;
  msg.lat = data.lat;
  msg.alt = data.alt;
  msg.roadtype = data.roadtype;
  msg.lanetype = data.lanetype;  
  msg.heading = data.heading;   //红旗1 0~360 正北顺时针为正 degree
  msg.pitch = data.pitch; //红旗1 //-180~180 抬头为正
  msg.roll = data.roll;//红旗1 //-90~90 degree 右倾为正
  msg.pitchrate = data.pitchrate;//红旗1 deg/s [-250,250]
  msg.rollrate = data.rollrate;//红旗1 deg/s [-250,250]
	//发布结果统一
	//1.加速度统一到前右下
	//2.角速度统一到顺时针为正，车辆右转时输出角速度为正
	if(carNumber == 1){ //INS570D
		msg.accx = data.accx*10; // INS570D的g为单位的值改成m/s2的值 前右下
		msg.accy = data.accy*10;
		msg.accz = data.accz*10;
		
		msg.yawrate = data.yawrate;//红旗1 deg/s [-250,250] data.yawrate顺时针为正
	}
	else{//NPOS220S
		msg.accx = data.accx;//前左上转前右下
		msg.accy = -data.accy;
		msg.accz = -data.accz;
		
		msg.yawrate = -data.yawrate;//红旗2 data.yawrate顺时针为负
	}

  msg.velocity = data.velocity;
  msg.status = data.status;
  msg.rawstatus = data.rawstatus;
  
  msg.mile = data.mile;
  msg.satenum = data.satenum;
  msg.timestamp = isUseRosBag ? data.timestamp : ros::Time::now().toSec()*1000; // 20230119 true 调试lidar-inspva的运动补偿
  msg.isvalid = data.isvalid;
  msg.speedN = data.speedN; // X轴速度
  msg.speedE = data.speedE; // Y轴速度
  msg.speedD = data.speedD; // Z轴速度 TODO 红旗2赋值？ 红旗1NED

  long t_timestamp = bca::GPSTime2UTCTime(data.gpsweek,data.gpstime*1.0/1000,18);
  msg.gpstime = t_timestamp;

  // 经纬度判断是否在区域内 且GPS状态异常,在区域内设置状态为0
  Point curPoint = {data.lon, data.lat};
  if(m_common.isPointInPolygon(curPoint, m_slamPolygon)){ // data.rawstatus != 50
    msg.status = 0;
    std::cout << FRED("drving in area that the gps not useful") << std::endl;
  }else{
    // std::cout << FGRN("drving in area that the gps useful") << std::endl;
  }

  pub_gps.publish(msg);
  
  if(isSaveSelfCarInfo && saveSelfCarInofFile.is_open()){
	  static int gpsFrameCount = 0;
	  //帧序号,t0时间戳,t1时间戳,厂商编号,目标ID,类型,
	  // 经度,纬度,速度,航向角,长度,宽度,高度,
	  saveSelfCarInofFile << gpsFrameCount++ << ","
			   << std::fixed << setprecision(15)<< std::to_string(msg.timestamp / 1000.0) << ","
	           << msg.timestamp / 1000.0 << "," <<"0" << "," << "0" << "," << "car" << ","
			   << std::fixed << setprecision(10)
			   << msg.lon << "," << msg.lat << ","
			   << std::fixed << setprecision(5)
			   << sqrt(pow(msg.speedN,2) + pow(msg.speedE,2)) << ","
			   << msg.heading << "," //
	           << "4.5" << "," << "1.8" << "," << "2.0"
	           << endl;
  }

  if(data.status == 0){
    gps_status.state = 0;
  }
  else{
    gps_status.state = 1;
  }
  gps_status.timestamp = isUseRosBag ? data.timestamp : ros::Time::now().toSec()*1000; // 20230119
  pub_gpsstatus.publish(gps_status);


    sensor_msgs::Imu imumsg;
    Eigen::Vector3d ea0( msg.heading * M_PI/180.0,-msg.pitch* M_PI / 180.0,msg.roll * M_PI / 180.0);//yaw ,pitch ,roll.

    Eigen::Matrix3d R;
    R = Eigen::AngleAxisd(ea0[0], ::Eigen::Vector3d::UnitZ())
      * Eigen::AngleAxisd(ea0[1], ::Eigen::Vector3d::UnitY())
      * Eigen::AngleAxisd(ea0[2], ::Eigen::Vector3d::UnitX());
    Eigen::Quaterniond q;
    q = R;
    
    //std::cout<<" yaw: " << data.heading  << " ,pitch: "<<data.pitch << " ,roll: "<<data.roll<<std::endl;
    
    imumsg.orientation.w = (double)q.w();
    imumsg.orientation.x = (double)q.x();
    imumsg.orientation.y = (double)q.y();
    imumsg.orientation.z = (double)q.z();

    imumsg.header.stamp = ros::Time().fromSec(msg.gpstime/1000.0);
	
	if(carNumber == 1){
		imumsg.angular_velocity.x = msg.rollrate* M_PI / 180.0;//du/s
		imumsg.angular_velocity.y = -msg.pitchrate* M_PI / 180.0;
		imumsg.angular_velocity.z = -msg.yawrate* M_PI / 180.0;
		
		imumsg.linear_acceleration.x = msg.accx/10.0*9.8;//INS570D的g为单位的值改成npos m/s2的值
		imumsg.linear_acceleration.y = -msg.accy/10.0*9.8;
		imumsg.linear_acceleration.z = -msg.accz/10.0*9.8;
	}
	else{
		imumsg.angular_velocity.x = msg.rollrate* M_PI / 180.0;//rad/s
		imumsg.angular_velocity.y = msg.pitchrate* M_PI / 180.0;
		imumsg.angular_velocity.z = msg.yawrate* M_PI / 180.0;
		
		imumsg.linear_acceleration.x = msg.accx;
		imumsg.linear_acceleration.y = msg.accy;
		imumsg.linear_acceleration.z = msg.accz;
	}


    pub_imu.publish(imumsg);

}

void gpsparse::Get_imuMsg(std::deque<sensor_msgs::Imu> &MsgDeque, const long &GpsTime) {//20221207
    std::mutex imuMutex;
    std::lock_guard<std::mutex> gpsLock(imuMutex);
    if (MsgDeque.empty()){
        cout<<"/gps/imu msgDeque empty, no data."<<endl;
        return;
    }
    double curGPStime = (double)GpsTime;
    int curMsgIndex = 0;

    for (int i = 0; i < MsgDeque.size(); ++i){
        double dequetime = MsgDeque[i].header.stamp.toSec() * 1000.0;
        if (dequetime > curGPStime)
            break;
        curMsgIndex = i;
    }
    if (curMsgIndex + 1 == MsgDeque.size()){// 保留最后一个数据
        if (curMsgIndex > 0){
            while (curMsgIndex--){
                MsgDeque.pop_front();
            }
        }
    }
    else{
        double curMsg2GPStimeGap = abs(MsgDeque[curMsgIndex].header.stamp.toSec() * 1000.0 - curGPStime);
        double nextMsg2GPStimeGap = abs(MsgDeque[curMsgIndex + 1].header.stamp.toSec() * 1000.0 - curGPStime);
        if (curMsgIndex > 0){
            if (curMsg2GPStimeGap > nextMsg2GPStimeGap){
                curMsgIndex += 1;
            }
            while (curMsgIndex--){
                MsgDeque.pop_front();
            }
        }
    }
}

  /*************************************************
	Function:       getDist2
	Description:    得到两个点之间的距离
	Input:          p1: p1点， p2: p2点
	Output:         无
	Return:         距离
	*************************************************/  
double gpsparse::getDist2(Point2D p1, Point2D p2)
{
  return sqrt((p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y));
}

  /*************************************************
	Function:       GetL0InDegree
	Description:    得到时区所在的纬度。
	Input:          dLIn:纬度
	Output:         无
	Return:         纬度
	*************************************************/ 
double gpsparse::GetL0InDegree(double dLIn)
{
  double L = dLIn;//d.d
  double L_ddd_Style = L;
  double ZoneNumber = (int)((L_ddd_Style - 1.5) / 3.0) + 1;
  double L0 = ZoneNumber * 3.0;//degree
  return L0;
}

  /*************************************************
	Function:       BLH2XYZ
	Description:    wgs84坐标通过UTM投影转成大地坐标
	Input:          B:经度， L:纬度， H:航向
	Output:         无
	Return:         转换后的大地坐标
	*************************************************/  
Point2D gpsparse::BLH2XYZ(double B, double L, double H)
{
  double N, E, h;
  double L0 = GetL0InDegree(L);
  Point2D pt2d;
  double a = 6378245.0;
  double F = 298.257223563;
  double iPI = 0.0174532925199433;
  double f = 1 / F;
  double b = a * (1 - f);
  double ee = (a * a - b * b) / (a * a);
  double e2 = (a * a - b * b) / (b * b);
  double n = (a - b) / (a + b), n2 = (n * n), n3 = (n2 * n), n4 = (n2 * n2), n5 = (n4 * n);
  double al = (a + b) * (1 + n2 / 4 + n4 / 64) / 2;
  double bt = -3 * n / 2 + 9 * n3 / 16 - 3 * n5 / 32;
  double gm = 15 * n2 / 16 - 15 * n4 / 32;
  double dt = -35 * n3 / 48 + 105 * n5 / 256;
  double ep = 315 * n4 / 512;
  B = B * iPI;
  L = L * iPI;
  L0 = L0 * iPI;
  double l = L - L0, cl = (cos(B) * l), cl2 = (cl * cl), cl3 = (cl2 * cl), cl4 = (cl2 * cl2), cl5 = (cl4 * cl), cl6 = (cl5 * cl), cl7 = (cl6 * cl), cl8 = (cl4 * cl4);
  double lB = al * (B + bt * sin(2 * B) + gm * sin(4 * B) + dt * sin(6 * B) + ep * sin(8 * B));
  double t = tan(B), t2 = (t * t), t4 = (t2 * t2), t6 = (t4 * t2);
  double Nn = a / sqrt(1 - ee * sin(B) * sin(B));
  double yt = e2 * cos(B) * cos(B);
  N = lB;
  N += t * Nn * cl2 / 2;
  N += t * Nn * cl4 * (5 - t2 + 9 * yt + 4 * yt * yt) / 24;
  N += t * Nn * cl6 * (61 - 58 * t2 + t4 + 270 * yt - 330 * t2 * yt) / 720;
  N += t * Nn * cl8 * (1385 - 3111 * t2 + 543 * t4 - t6) / 40320;
  E = Nn * cl;
  E += Nn * cl3 * (1 - t2 + yt) / 6;
  E += Nn * cl5 * (5 - 18 * t2 + t4 + 14 * yt - 58 * t2 * yt) / 120;
  E += Nn * cl7 * (61 - 479 * t2 + 179 * t4 - t6) / 5040;
  E += 500000;
  N = 0.9999 * N;
  E = 0.9999 * (E - 500000.0) + 250000.0;//Get y
  pt2d.x = E;
  pt2d.y = N;
  h = H;
  return pt2d;
}


  /*************************************************
	Function:       ConvertDisToLngLat
	Description:    经纬度转换。
	Input:          dis:距离 ，lng:经度， lat:纬度， angle:航向
	Output:         无
	Return:         转换后的经纬度
	*************************************************/
Point2D gpsparse::ConvertDisToLngLat(double dis,double lng,double lat,double angle)
{
	Point2D p;
	memset(&p,0,sizeof(p));
	p.lon=lng+(dis*sin(angle*M_PI/180.0))/(111000*cos(lat*M_PI/180.0));
	//将距离转换成经度的计算公式
  p.lat = lat + (dis * cos(angle * M_PI / 180)) / 111000;//将距离转换成纬度的计算公式
	return p;
}

  /*************************************************
	Function:       uart_open
	Description:    打开串口。
	Input:          fd: 句柄，pathname: 串口名称
	Output:         无
	Return:         是否成功
	*************************************************/ 
int gpsparse::uart_open(int fd,const char *pathname)
{
    fd = open(pathname, O_RDWR|O_NOCTTY); 
    if (-1 == fd)
    { 
        perror("Can't Open Serial Port"); 
		return(-1); 
	} 
    else
		printf("open %s success!\n",pathname);
    if(isatty(STDIN_FILENO)==0) 
		printf("standard input is not a terminal device\n"); 
    else 
		printf("isatty success!\n"); 
    return fd; 
}

  /*************************************************
	Function:       uart_set
	Description:    设置串口。
	Input:          fd: 句柄，nspeed: 波特率，nBits: 位数，nEvent: 工作模式，nStop: 停止位
	Output:         无
	Return:         是否成功
	*************************************************/ 
int gpsparse::uart_set(int fd,int nSpeed, int nBits, char nEvent, int nStop)
{
     struct termios newtio,oldtio; 
     if  ( tcgetattr( fd,&oldtio)  !=  0) {  
      perror("SetupSerial 1");
	  printf("tcgetattr( fd,&oldtio) -> %d\n",tcgetattr( fd,&oldtio)); 
      return -1; 
     } 
     bzero( &newtio, sizeof( newtio ) ); 
     newtio.c_cflag  |=  CLOCAL | CREAD;  
     newtio.c_cflag &= ~CSIZE;  
     switch( nBits ) 
     { 
     case 7: 
      newtio.c_cflag |= CS7; 
      break; 
     case 8: 
      newtio.c_cflag |= CS8; 
      break; 
     } 
     switch( nEvent ) 
     { 
     case 'o':
     case 'O': 
      newtio.c_cflag |= PARENB; 
      newtio.c_cflag |= PARODD; 
      newtio.c_iflag |= (INPCK | ISTRIP); 
      break; 
     case 'e':
     case 'E': 
      newtio.c_iflag |= (INPCK | ISTRIP); 
      newtio.c_cflag |= PARENB; 
      newtio.c_cflag &= ~PARODD; 
      break;
     case 'n':
     case 'N': 
      newtio.c_cflag &= ~PARENB; 
      break;
     default:
      break;
     } 

     /*设置波特率*/ 

switch( nSpeed ) 
     { 
     case 2400: 
      cfsetispeed(&newtio, B2400); 
      cfsetospeed(&newtio, B2400); 
      break; 
     case 4800: 
      cfsetispeed(&newtio, B4800); 
      cfsetospeed(&newtio, B4800); 
      break; 
     case 9600: 
      cfsetispeed(&newtio, B9600); 
      cfsetospeed(&newtio, B9600); 
      break; 
     case 115200: 
      cfsetispeed(&newtio, B115200); 
      cfsetospeed(&newtio, B115200); 
      break; 
     case 230400: 
      cfsetispeed(&newtio, B230400); 
      cfsetospeed(&newtio, B230400); 
      break; 
     case 460800: 
      cfsetispeed(&newtio, B460800); 
      cfsetospeed(&newtio, B460800); 
      break; 
     default: 
      cfsetispeed(&newtio, B9600); 
      cfsetospeed(&newtio, B9600); 
     break; 
     } 
     if( nStop == 1 ) 
      newtio.c_cflag &=  ~CSTOPB; 
     else if ( nStop == 2 ) 
     newtio.c_cflag |=  CSTOPB; 
     newtio.c_cc[VTIME]  = 0; 
     newtio.c_cc[VMIN] = 0; 
     tcflush(fd,TCIFLUSH); 

if((tcsetattr(fd,TCSANOW,&newtio))!=0) 
     { 
      perror("com set error"); 
      return -1; 
     } 
     printf("set done!\n"); 
     return 0; 
}

  /*************************************************
	Function:       uart_close
	Description:    关闭串口。
	Input:          fd: 句柄。
	Output:         无
	Return:         是否成功
	*************************************************/ 
int gpsparse::uart_close(int fd)
{
    assert(fd);
    close(fd);

    return 0;
}
  /*************************************************
	Function:       send_data
	Description:    发送串口数据。
	Input:          fd: 句柄，send_buffer: 发送的字符数组，length: 发送的字符个数。
	Output:         无
	Return:         发送的字符个数
	*************************************************/  
int gpsparse::send_data(int  fd, unsigned char *send_buffer,int length)
{
	length=write(fd,send_buffer,length*sizeof(unsigned char));
	return length;
}


  /*************************************************
	Function:       recv_data
	Description:    接收串口数据。
	Input:          fd: 句柄，recv_buffer: 接收的字符数组，length: 接收的字符个数。
	Output:         无
	Return:         接收的字符个数。
	*************************************************/   
int gpsparse::recv_data(int fd, unsigned char* recv_buffer,int length)
{
	length=read(fd,recv_buffer,length);
	return length;
}

  /*************************************************
	Function:       ParseData
	Description:    串口-解析串口数据。
	Input:          chr: 单个字符。
	Output:         无
	Return:         无
	*************************************************/  
void gpsparse::ParseData(unsigned char chr)
{
		unsigned char i;
		unsigned char cTemp=0;
		chrBuf[chrCnt++]=chr;
		if (chrCnt<63) return;

		for(i=0;i<57;i++) cTemp^=chrBuf[i];

		if((chrBuf[0]==0xBD)&&(chrBuf[1]==0xDB)&&(chrBuf[2]==0x0B)&&(cTemp==chrBuf[57])) 
    {
      // printf("Error:%x %x %x\r\n",chrBuf[0],chrBuf[1],chrBuf[2]);
      int offset = 0; 
      int      tempValue=0;
      short    tempInt = 0;
      tempInt = bca::charArray2Int(chrBuf, 3-offset, 2);
      rp.roll = tempInt * 360.0/32768;//-90~90 degree 右倾为正

      tempInt = bca::charArray2Int(chrBuf, 5-offset, 2);
      rp.pitch = tempInt * 360.0/32768;//-180~180 抬头为正
      
      tempInt = bca::charArray2Int(chrBuf, 7-offset, 2);
      rp.heading = tempInt * 360.0/32768;//-180~180 正北顺时针为正 degree

      if(rp.heading < 0)
      rp.heading += 360;//0~360
      
      //x up+ , y右为+8
      tempInt = bca::charArray2Int(chrBuf, 9-offset, 2);
      rp.rollrate = tempInt * 300.0/32768;//deg/s [-250,250]
      tempInt = bca::charArray2Int(chrBuf, 11-offset, 2);
      rp.pitchrate = tempInt * 300.0/32768;//deg/s [-250,250]
      tempInt = bca::charArray2Int(chrBuf, 13-offset, 2);
      rp.yawrate = tempInt * 300.0/32768;//deg/s [-250,250]
      // printf("rate = %.4f\n",headingrate);

      tempInt = bca::charArray2Int(chrBuf, 15-offset, 2);
      rp.accx = tempInt * 12.0/32768;//g
      tempInt = bca::charArray2Int(chrBuf, 17-offset, 2);
      rp.accy = tempInt * 12.0/32768;//g
      tempInt = bca::charArray2Int(chrBuf, 19-offset, 2);
      rp.accz = tempInt * 12.0/32768;//g

      tempValue = bca::charArray2Int(chrBuf, 21-offset, 4);
      rp.lat = tempValue * 0.0000001;//lat

      tempValue = bca::charArray2Int(chrBuf, 25-offset, 4);
      rp.lon = tempValue * 0.0000001;

      tempValue = bca::charArray2Int(chrBuf, 29-offset, 4);
      rp.alt = tempValue * 0.001;

      tempInt = bca::charArray2Int(chrBuf, 33-offset, 2);
    
      double speedx = tempInt * 100.0/32768;
      rp.speedN = speedx; // X轴速度 20220926 m/s

      tempInt = bca::charArray2Int(chrBuf, 35 - offset, 2);
      double speedy = tempInt * 100.0/32768;
      rp.speedE = speedy; // Y轴速度 20220926
      rp.velocity = sqrt(speedx*speedx + speedy*speedy);
      
      
      tempInt = bca::charArray2Int(chrBuf, 37 - offset, 2);
      double speedz = tempInt * 100.0 / 32768;
      rp.speedD = speedz; // Z轴速度 20220926

      long longInt = bca::charArray2uInt(chrBuf, 52-offset, 4);
      rp.gpstime = (double)(longInt) /4 ;//001; ms

      long longweek = bca::charArray2uInt(chrBuf, 58-offset, 4);
      rp.gpsweek= longweek;//001; ms

      unsigned char type = chrBuf[56-offset];
      if(type == 32)
      {
       //目前我们认为gps都是有效的.        
        tempInt = bca::charArray2Int(chrBuf, 46-offset, 2);
        // cout<<"status: "<<tempInt<<endl;
        
        if(tempInt == 32 || tempInt == 33 || tempInt == 34)
        {
			//32L1浮动解：几十厘米到几米，33消电离层浮点解：比17高，几厘米几十厘米，34窄巷浮点解：比33高，几厘米几十厘米
          rp.status = 5;
        }
        else if(tempInt == 48 || tempInt == 49 || tempInt == 50  || tempInt == 17)
        {
			//17伪距差分定位：几米，48L1固定解：几厘米，49宽巷固定解：几厘米，50窄巷固定解：几毫米到几厘米
          rp.status = 4;
        }
        else
        {
          rp.status = 0;
        }
        
        if(tempInt == 0)
        {
           rp.status = 0;
        }
        rp.rawstatus = tempInt;

        tempInt = bca::charArray2Int(chrBuf, 48-offset, 2);
        rp.satenum = tempInt;
      }
      chrCnt=chrCnt-63;		
      for(int i = 0; i < chrCnt; ++i)
      {
        chrBuf[i] = chrBuf[i+63]; 
      }

      if (rp.lon > 150 || rp.lon < 100 || rp.lat > 50 || rp.lat < 20 ) 
      {
          rp.rawstatus = 0;
          rp.status = 0;
      }

      Point2D p1 = BLH2XYZ(rp.lat, rp.lon, 0);
      Point2D p2 = BLH2XYZ(lastRp.lat, lastRp.lon, 0);
      bool isRecode = false;
      double dist = getDist2(p1, p2);
      if (dist >= 0.05 ) 
      {
        isRecode = true;
        if(dist < 0.5)
        {
           m_mile += getDist2(p1, p2);
        }
        lastRp = rp;
      }
      rp.mile = m_mile;
      publishMsg(rp); //parse finish
      
    //  cout<<addattr.roadattr << ","<<last_roadattr<< ","<<dist<<",isRecode:"<<isRecode<<"savePath:"<<savePath<<endl;
      if(addattr.roadattr != 0 && last_roadattr == 0) //上升沿，触发记录，重新产生新的文件
      {
        char buffer[80];
        std::time_t now = std::time(NULL);
        std::tm* pnow = std::localtime(&now);
        std::strftime(buffer, 80, "%Y%m%d_%H%M%S", pnow);
        if((int)m_collectmap.property>0)
        {
            savePath = saveTrajDir+"yuanqu"+boost::lexical_cast<string>((int)m_collectmap.zonename)+"/maping"+boost::lexical_cast<string>((int)m_collectmap.mapname)+".txt";
        }
        else
        {
          string tempPath(buffer);
          savePath = saveDir + tempPath + "_maping1.txt";
        }
        // cout<<"savePath:"<<savePath<<endl;
        safedeletefile(savePath);
        loopsave = true;
        isRecode = true;
      }
      else if(addattr.roadattr == 0 && last_roadattr != 0) //下降沿，停止记录。
      {
        loopsave = false;
      }

      last_roadattr = addattr.roadattr;
      if (isRecode && loopsave) 
      {

        fp = fopen(savePath.c_str(), "a");
        
        char buffer[80];
        std::time_t now = std::time(NULL);
        std::tm* pnow = std::localtime(&now);
        std::strftime(buffer, 80, "%Y%m%d_%H%M%S", pnow);
        
      //  fprintf(fp, "%.8lf,%.8lf,%d,%d,%d,%d,%d,%d,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%d,%d\n", rp.lon, rp.lat,
      //       addattr.roadattr,addattr.speed,addattr.laneattr,addattr.mergelanetype,addattr.sensorlanetype,m_actuator.turnLight,addattr.sideroadwidth,addattr.egolanewidth,addattr.leftlanewidth,addattr.rightlanewidth,addattr.leftsearchdis,addattr.rightsearchdis, rp.heading,rp.gpstime,rp.satenum);

            fprintf(fp, "%.8lf,%.8lf,%d,%d,%d,%d,%d,%d,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%.2lf,%d,%d,%d,%d,%d,%d,\n", rp.lon, rp.lat,
                addattr.roadattr,addattr.speed,addattr.laneattr,addattr.mergelanetype,addattr.sensorlanetype,m_actuator.turnLight,
                addattr.sideroadwidth,addattr.egolanewidth,addattr.leftlanewidth,addattr.rightlanewidth,addattr.leftsearchdis,
                addattr.rightsearchdis, rp.heading,rp.gpstime,rp.satenum,
                addattr.laneswitch, addattr.sidepass, addattr.lanenum, addattr.lanesite
                );
        fclose(fp);
      }
    }
    else
    {
      for(int i = 0; i < chrCnt; ++i)
      {
        chrBuf[i] = chrBuf[i+1];
      }
      chrCnt--;
    }
    // for(int i = 0; i < 58; i++)
    // {
    //   printf("%x ",chrBuf[i]);
    // }
    // std::cout << std::endl;
}

  /*************************************************
	Function:       saveTrajHandler
	Description:    调用服务，保存地图。
	Input:          req: 请求信息，res：回应信息。
	Output:         无
	Return:         无
	*************************************************/     
bool gpsparse::saveTrajHandler(std_srvs::Empty::Request &req, std_srvs::Empty::Response &res)
{
    ROS_WARN("-------------------Save gps traj.---------------------");
    //保存文件  保存地图
    std::string fromfile = savePath;
    std::string tofile = saveTrajDir + "/maping1.txt";
    std::string savecmd = "cp " + fromfile + " " + tofile;
    ::system(savecmd.c_str());//delete files from directory
    ROS_WARN("-------------------Save gps traj end.---------------------");
}


  /*************************************************
	Function:       subCallback_actuator
	Description:    订阅actuator，订阅车辆信息。
	Input:          msg: 车辆信息
	Output:         无
	Return:         无
	*************************************************/     
void gpsparse::subCallback_actuator(const common_msgs::actuator::ConstPtr &msg)
{     
	 //cout<<"#################light:"<<endl;
	m_actuator = *msg;

  //cout<<"#################light:"<<m_actuator.turnLight<<endl;
}

  /*************************************************
	Function:       SubCallback_collectmap
	Description:    订阅collectmap，新建文件并记录地图
	Input:          msg: 
	Output:         无
	Return:         无
	*************************************************/ 
void gpsparse::SubCallback_collectmap(const common_msgs::collectmap::ConstPtr &msg)
{
  //   cout<<"collectmap --------------"<<endl;
    m_collectmap = *msg;
    m_collectmap.timestamp = ros::Time::now().toSec()*1000;
    int zone_name=(int)m_collectmap.zonename;
    if(zone_name>0)
    {
        bool notexist=true;
        string zone_name_str="yuanqu"+boost::lexical_cast<string>(zone_name);
        cout<<"zone_name_str:"<<zone_name_str<<endl;
        if(zone_name_str !="")
        {
          for(int i=0;i<m_ExistFileDir.size();++i)
          {
              if(zone_name_str == m_ExistFileDir[i])
              {
                  notexist=false;
                  break;
              }
          }
        }
        if(notexist)
        {
            filedirectoryname=zone_name_str;
            string saveTrajDir1=saveTrajDir+filedirectoryname+"/";
            GenerateNewDir(saveTrajDir1);
            m_ExistFileDir.push_back(filedirectoryname);
        }
        else
        {
            filedirectoryname=zone_name_str;
        }
    }
}
  /*************************************************
	Function:       SubCallback_collectpoint
	Description:    订阅collectpoint，回调APP数据进行泊车点、红绿灯点的采集
	Input:          msg: 泊车/红绿灯点
	Output:         无
	Return:         无
	*************************************************/  
void gpsparse::SubCallback_collectpoint(const common_msgs::collectpoint::ConstPtr &msg)
{
    collect_point_=*msg;
    int set_property=(int)collect_point_.property;
	  cout<<"-----set_property:"<<set_property<<endl;
    if(set_property>0 && set_property!=last_property_)
    {
        string zonename=saveTrajDir+"yuanqu"+boost::lexical_cast<string>((int)collect_point_.zonename)+"/";
        std::string paramfile=zonename+"param.yaml";
        std::cout<<"parafile:"<<paramfile<<std::endl;
        // 读取源文件，再根据app内容修改文件
        YamlConfigReader reader;
        Config config;
        reader.readFile(config,paramfile.c_str());
        int trafficnum,stopnum,apsnum,vapsnum;
        config.mapGetInt("apsnum",&apsnum);
        config.mapGetInt("vapsnum",&vapsnum);
        config.mapGetInt("stopnum",&stopnum);
        config.mapGetInt("trafficnum",&trafficnum);
        int aps_list_num=apsnum;//config.mapGetChild("aps").listLength();
        int vaps_list_num=vapsnum;//config.mapGetChild("vaps").listLength();
        int stop_list_num=stopnum;//config.mapGetChild("stop").listLength();
        int traffic_list_num=trafficnum;//config.mapGetChild("trafficlight").listLength();
        
        int set_index=(int)collect_point_.index;
        int set_time=(int)collect_point_.stoptime;
        int set_orientation=(int)collect_point_.orientation;
        string index=boost::lexical_cast<string>(set_index);
        switch(set_property) 
        {
          case 1:
          {
              //垂直泊车点
              if(set_index>vapsnum)
              {
                vapsnum=set_index;
              }
              if(set_index<=vaps_list_num)
              {
                  Config temp_aps_config= config.mapGetChild( "vaps");
                  Config apsmsg=temp_aps_config.listChildAt(set_index-1);
                  apsmsg.mapSetValue("lon",rp.lon);
                  apsmsg.mapSetValue("lat",rp.lat);
                  apsmsg.mapSetValue("heading",rp.heading);
                  apsmsg.mapSetValue("orientation",set_orientation);
              }
              else
              {
                if(vaps_list_num==0)
                {
                    Config temp_aps_config= config.mapMakeChild( "vaps");
                    Config apsmsg=temp_aps_config.listAppendNew();
                    apsmsg.mapSetValue("lon",rp.lon);
                    apsmsg.mapSetValue("lat",rp.lat);
                    apsmsg.mapSetValue("heading",rp.heading);
                    apsmsg.mapSetValue("orientation",set_orientation);
                }
                else
                {
                  Config temp_aps_config= config.mapGetChild( "vaps");
                  for(int i=0;i<set_index-vaps_list_num;i++)
                  {
                    if(i==set_index-vaps_list_num-1)
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",rp.lon);
                      apsmsg.mapSetValue("lat",rp.lat);
                      apsmsg.mapSetValue("heading",rp.heading);
                      apsmsg.mapSetValue("orientation",set_orientation);
                    }
                    else
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",0);
                      apsmsg.mapSetValue("lat",0);
                      apsmsg.mapSetValue("heading",0);
                      apsmsg.mapSetValue("orientation",0);
                    }
                  }
                }
              }
          }
          break;
          case 2:
          {
              //水平泊车点
              if(set_index>apsnum)
              {
                apsnum=set_index;
              }
              if(set_index<=aps_list_num)
              {
                  Config temp_aps_config= config.mapGetChild( "aps");
                  Config apsmsg=temp_aps_config.listChildAt(set_index-1);
                  apsmsg.mapSetValue("lon",rp.lon);
                  apsmsg.mapSetValue("lat",rp.lat);
                  apsmsg.mapSetValue("heading",rp.heading);
                  apsmsg.mapSetValue("orientation",set_orientation);
              }
              else
              {
                if(aps_list_num==0)
                {
                    Config temp_aps_config= config.mapMakeChild( "aps");
                    Config apsmsg=temp_aps_config.listAppendNew();
                    apsmsg.mapSetValue("lon",rp.lon);
                    apsmsg.mapSetValue("lat",rp.lat);
                    apsmsg.mapSetValue("heading",rp.heading);
                    apsmsg.mapSetValue("orientation",set_orientation);
                }
                else
                {
                  Config temp_aps_config= config.mapGetChild( "aps");
                  for(int i=0;i<set_index-aps_list_num;i++)
                  {
                    if(i==set_index-aps_list_num-1)
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",rp.lon);
                      apsmsg.mapSetValue("lat",rp.lat);
                      apsmsg.mapSetValue("heading",rp.heading);
                      apsmsg.mapSetValue("orientation",set_orientation);
                    }
                    else
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",0);
                      apsmsg.mapSetValue("lat",0);
                      apsmsg.mapSetValue("heading",0);
                      apsmsg.mapSetValue("orientation",0);
                    }
                  }
                }
              }
          }
          break;
          case 3:
          {
              //站点停车
              if(set_index>stopnum)
              {
                stopnum=set_index;
              }

              if(set_index<=stop_list_num)
              {
                  Config temp_aps_config= config.mapGetChild( "stop");
                  Config apsmsg=temp_aps_config.listChildAt(set_index-1);
                  apsmsg.mapSetValue("lon",rp.lon);
                  apsmsg.mapSetValue("lat",rp.lat);
                  apsmsg.mapSetValue("heading",rp.heading);
                  apsmsg.mapSetValue("property",1);
                  apsmsg.mapSetValue("stoptime",set_time);
              }
              else
              {
               if(stop_list_num==0)
                {
                    Config temp_aps_config= config.mapMakeChild( "stop");
                    Config apsmsg=temp_aps_config.listAppendNew();
                    apsmsg.mapSetValue("lon",rp.lon);
                    apsmsg.mapSetValue("lat",rp.lat);
                    apsmsg.mapSetValue("heading",rp.heading);
                    apsmsg.mapSetValue("property",1);
                    apsmsg.mapSetValue("stoptime",set_time);
                }
                else
                {
                  Config temp_aps_config= config.mapGetChild( "stop");
                  for(int i=0;i<set_index-stop_list_num;i++)
                  {
                    if(i==set_index-stop_list_num-1)
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",rp.lon);
                      apsmsg.mapSetValue("lat",rp.lat);
                      apsmsg.mapSetValue("heading",rp.heading);
                      apsmsg.mapSetValue("property",1);
                      apsmsg.mapSetValue("stoptime",set_time);
                    }
                    else
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",0);
                      apsmsg.mapSetValue("lat",0);
                      apsmsg.mapSetValue("heading",0);
                      apsmsg.mapSetValue("property",0);
                      apsmsg.mapSetValue("stoptime",0);
                    }
                  }
                }
              }

          }
          break;
          case 4:
          {
              //红绿灯点
              if(set_index>trafficnum)
              {
                trafficnum=set_index;
              }
              // string lon="trafficptlon"+index;
              // string lat="trafficptlat"+index;
              // string heading="trafficptheading"+index;
              // string property="trafficptdirection"+index;
              
              if(set_index<=traffic_list_num)
              {
                  Config temp_aps_config= config.mapGetChild( "traffic");
                  Config apsmsg=temp_aps_config.listChildAt(set_index-1);
                  apsmsg.mapSetValue("lon",rp.lon);
                  apsmsg.mapSetValue("lat",rp.lat);
                  apsmsg.mapSetValue("heading",rp.heading);
                  
              }
              else
              {
                if(traffic_list_num==0)
                {
                    Config temp_aps_config= config.mapMakeChild( "traffic");
                    Config apsmsg=temp_aps_config.listAppendNew();
                    apsmsg.mapSetValue("lon",rp.lon);
                    apsmsg.mapSetValue("lat",rp.lat);
                    apsmsg.mapSetValue("heading",rp.heading);
                }
                else
                {
                  Config temp_aps_config= config.mapGetChild( "traffic");
                  for(int i=0;i<set_index-traffic_list_num;i++)
                  {
                    if(i==set_index-traffic_list_num-1)
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",rp.lon);
                      apsmsg.mapSetValue("lat",rp.lat);
                      apsmsg.mapSetValue("heading",rp.heading);
                     
                    }
                    else
                    {
                      Config apsmsg=temp_aps_config.listAppendNew();
                      apsmsg.mapSetValue("lon",0);
                      apsmsg.mapSetValue("lat",0);
                      apsmsg.mapSetValue("heading",0);
                    }
                  }
                }
              } 
          }
          break;
        }        

        config.mapSetValue("apsnum",apsnum);
        config.mapSetValue("vapsnum",vapsnum);
        config.mapSetValue("stopnum",stopnum);
        config.mapSetValue("trafficnum",trafficnum);

        YamlConfigWriter writer;
        writer.writeFile(config,paramfile.c_str());
    }
    last_property_=set_property;
}

  /*************************************************
	Function:       SubCallback_requestmap
	Description:    订阅request map，接收APP数据请求，进行地图数据上传
	Input:          msg: 请求的地图
	Output:         无
	Return:         无
	*************************************************/ 
void gpsparse::SubCallback_requestmap(const common_msgs::requestmap::ConstPtr &msg)
{
    cout<<"requestmap:"<< (int)(msg->request)<<"**************************"<<endl;
    cout<<"--------------something"<<endl;
    m_requestmap.request = msg->request;
    m_requestmap.mapname=msg->mapname;
    // m_requestmap.filedirectoryname=msg->filedirectoryname;
    if(m_requestmap.request==1)
    {
       publishMap();
    }
    else if(m_requestmap.request==2)//指定地图
    {

    }
    else if(m_requestmap.request==3)
    {

    }
    else if(m_requestmap.request==4)
    {

    }
}

/*************************************************
  Function:       SubCallback_inspva
  Description:    订阅INSPVA
  Input:          msg:
  Output:         无
  Return:         无
  *************************************************/
void gpsparse::SubCallback_inspva(const novatel_oem7_msgs::INSPVA::ConstPtr &msg){//20221110
    //mutex inspvaMutex;
    //lock_guard<mutex> gpsLock(inspvaMutex);
    //inspvaMsgDeque_.push_back(*msg);
    m_inspva = *msg;
    //Process();
    ParseDataInspva();
}

/*************************************************
  Function:       SubCallback_inspvax
  Description:    订阅INSPVAX
  Input:          msg:
  Output:         无
  Return:         无
  *************************************************/
void gpsparse::SubCallback_inspvax(const novatel_oem7_msgs::INSPVAX::ConstPtr &msg){//20221114
    m_inspvax = *msg;
}

/*************************************************
    Function:       SubCallback_corrimu
    Description:    订阅CORRIMU，
    Input:          msg:
    Output:         无
    Return:         无
    *************************************************/
void gpsparse::SubCallback_corrimu(const novatel_oem7_msgs::CORRIMU::ConstPtr &msg){
    std::mutex corrimuMutex;
    std::lock_guard<std::mutex> gpsLock(corrimuMutex);
    corrimuMsgDeque.push_back(*msg);
}

void gpsparse::SubCallback_imu(const sensor_msgs::Imu::ConstPtr &msg){//20221207 订阅gps/imu
    std::mutex imuMutex;
    std::lock_guard<std::mutex> gpsLock(imuMutex);
    imuMsgDeque_.push_back(*msg);
}

  /*************************************************
	Function:       publishFileDir
	Description:    发布文件路径下的maping点集
	Input:          filedir:文件路径
	Output:         无
	Return:         无
	*************************************************/
void gpsparse::publishFileDir(string filedir)
{
    struct dirent *ptr;
    DIR *dir;
    dir=opendir(filedir.c_str());
    vector<string> files;
    // cout<<"文件列表："<<endl;
    while((ptr=readdir(dir))!=NULL)
    {
      string str=ptr->d_name;
      if( str.length()>4 && str.substr(str.length()-4,str.length()-1)== ".txt")
      {
          files.push_back(ptr->d_name);
      }
    }

    //将地图排序，如果有maping1.txt，则将此置为0.
    for(int i=0; i < files.size(); i++)
    {
      if(files[i] == "maping1.txt")
      {
        if(i==0) break;
        //交换内容
        string temp = files[0];
        files[0] = files[i];
        files[i] = temp;
        break;
      }
    }

    std::string str_name = "";
    for(int i=0;i<files.size();i++)
    {
       str_name = str_name + files[i].substr(0,files[i].length()-4);
       str_name = str_name + ",";
    }

   std_msgs::String msg_mapnames;
   msg_mapnames.data = str_name;
	//  pub_mapname.publish(msg_mapnames);

    vector<string> splitstr=split(filedir,"/");
    string filedirname=splitstr[splitstr.size()-1];//获取文件夹名称
    cout<<"filename:"<<filedirname<<endl;

    for(int i=0;i<files.size();i++)
    {
        ifstream infile; 
        infile.open((saveTrajDir+filedirname+"/"+files[i]).data());   //将文件流对象与文件连接起来 
        common_msgs::lonlatmappoints map;
        string s;
        int count=0;
        while(getline(infile,s))
        {
          count++;
          if(count%50==0)
          {
            common_msgs::lonlat point;
            vector<string> splitstr=split(s,",");
            point.lon=atof(splitstr[0].c_str());
            point.lat=atof(splitstr[1].c_str());
            point.heading=atof(splitstr[4].c_str());
            map.points.push_back(point);
          }
        }
        infile.close();             //关闭文件输入流 
        string oldname=files[i];
        string newname=filedirname+"/"+oldname.substr(0,oldname.length()-4);
        map.mapname=oldname.substr(0,oldname.length()-4);
        map.zonename=filedirname;//filedirname;
        // map.isvalid=1;
        map.timestamp=ros::Time::now().toSec()*1000;
        pub_lonlatmap.publish(map);
        cout <<"map.mapname:"<<map.mapname<<",map.zonename:"<<map.zonename << endl;
    }
}

  /*************************************************
	Function:       publishMap
	Description:    发布所有路径下的点集
	Input:          无
	Output:         无
	Return:         无
	*************************************************/  
void gpsparse::publishMap()
{
    struct dirent *ptr;
    DIR *dir;
    dir=opendir(saveTrajDir.c_str());
    vector<string> filedirectoryname1;
    while((ptr=readdir(dir))!=NULL)
    {
      string str=saveTrajDir+ptr->d_name;
      struct stat fileStat;
        if ((stat(str.c_str(), &fileStat) == 0) && S_ISDIR(fileStat.st_mode) && str.find("yuanqu")!=str.npos)
        {
            filedirectoryname1.push_back(str);
            cout<<"str:"<<str<<endl;
        }  
    }
    for(int i=0;i<filedirectoryname1.size();++i)
    {
        publishFileDir(filedirectoryname1[i]);
    }
}

  /*************************************************
	Function:       split
	Description:    将字符串以某个字符进行分割
	Input:          str:字符串 pattern：子字符串
	Output:         无
	Return:         字符串数组
	*************************************************/ 
vector<string> gpsparse::split(const string &str,const string &pattern)
{
    //const char* convert to char*
    char * strc = new char[strlen(str.c_str())+1];
    strcpy(strc, str.c_str());
    vector<string> resultVec;
    char* tmpStr = strtok(strc, pattern.c_str());
    while (tmpStr != NULL)
    {
        resultVec.push_back(string(tmpStr));
        tmpStr = strtok(NULL, pattern.c_str());
    }
    delete[] strc;
    return resultVec;
}
  /*************************************************
	Function:       CopyFile
	Description:    文件重命名
	Input:          sourcefile:源文件名 destfile：目的文件名
	Output:         无
	Return:         无
	*************************************************/ 
void gpsparse::CopyFile(string sourcefile,string destfile)
{
  rename(sourcefile.c_str(),destfile.c_str());
}

  /*************************************************
	Function:       GenerateNewDir
	Description:    生成新的文件
	Input:          path: 路径名称
	Output:         无
	Return:         无
	*************************************************/
void gpsparse::GenerateNewDir(string path)
{
     //新建文件夹
    mkdir(path.c_str(),S_IRUSR | S_IWUSR | S_IXUSR | S_IRWXG | S_IRWXO);
    //新建碰撞轨迹文件夹
    string collisionpath=path+"/collisionpath";
    mkdir(collisionpath.c_str(),S_IRUSR | S_IWUSR | S_IXUSR | S_IRWXG | S_IRWXO);
    //新建yaml

    std::string newstationfile=path+"/param.yaml";
    YamlConfigWriter writer;
    Config write_config;
    write_config.mapSetValue("apsnum",0);
    write_config.mapSetValue("vapsnum",0);
    write_config.mapSetValue("stopnum",0);
    write_config.mapSetValue("trafficnum",0);
    // write_config.mapSetValue("vlondis",9);
    // write_config.mapSetValue("vlatdis",-9);
    // write_config.mapSetValue("londis",15);
    // write_config.mapSetValue("latdis",-3.5);
    writer.writeFile(write_config,newstationfile.c_str());
  
}

  /*************************************************
	Function:       safedeletefile
	Description:    安全删除文件
	Input:          name: 文件名称
	Output:         无
	Return:         无
	*************************************************/
void gpsparse::safedeletefile(const std::string& name)
{
    bool exist=exists_test3(name);
    if(exist)
    {
        remove(name.c_str());
        cout<<"remove over"<<endl;
    }
    else
    {
        cout<<"is not exist"<<endl;
    }
}
