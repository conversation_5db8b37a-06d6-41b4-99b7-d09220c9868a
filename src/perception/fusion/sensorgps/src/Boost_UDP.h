﻿

/* 
* Copyright (c) 2015,北京万集科技有限公司
* All rights reserved. 
* 
* 文件名称：Boost_UDP.h 
* 文件标识：见软件框架协议
* 摘  要：UDP读写类
* 
* 当前版本：1.0
* 作  者：zhuxuekui
* 完成日期：2015年11月30日
* 
* 取代版本：1.0 
* 原作者  ：zhuxuekui
* 完成日期：2015年11月30日
*/

#ifndef BOOST_UDP_H
#define BOOST_UDP_H

#include "iostream"
#include "string.h"
#include "boost/algorithm/string.hpp"
#include "boost/regex.hpp"
#include "boost/asio.hpp"
#include "boost/thread.hpp"
#include "boost/lexical_cast.hpp"

using namespace boost;
using namespace std;
#define RECVSIZE 1024
class Boost_UDP
{
public:
  /*************************************************
	Function:       Boost_UDP
	Description:    构造函数，参数设定
	Input:          服务，pc的ip地址，pc的端口号，设备的ip地址，设备的端口号
	Output:         无
	Return:         无 
	*************************************************/
   Boost_UDP(boost::asio::io_service &io_service,string pcIP, int pcPort, string canetIP, int canetPort):udp_sock(io_service)
   {
       m_canetIP = canetIP;
	   m_canetPort = canetPort;
	   m_pcIP = pcIP;
	   m_pcPort = pcPort;
   }
  /*************************************************
	Function:       ~Boost_UDP
	Description:    析构函数
	Input:          无
	Output:         无
	Return:         无 
	*************************************************/
   ~Boost_UDP()
   {
	   udp_sock.close();
   }

  /*************************************************
	Function:       start_sock
	Description:    udp绑定
	Input:          无
	Output:         无
	Return:         无 
	*************************************************/ 
   void start_sock()
   {
	    boost::asio::ip::udp::endpoint local_add(boost::asio::ip::address_v4::from_string(m_pcIP),m_pcPort);
		udp_sock.open(local_add.protocol());
		udp_sock.bind(local_add);
   }

  /*************************************************
	Function:       receive_data
	Description:    udp接收
	Input:          str: 字符数组
	Output:         无
	Return:         字符数组个数 
	*************************************************/ 
   int receive_data(unsigned char buf[])
   {
	    boost::asio::ip::udp::endpoint send_endpoint(boost::asio::ip::address_v4::from_string(m_pcIP),m_pcPort);
	    int ret = udp_sock.receive_from(boost::asio::buffer(buf,RECVSIZE),send_endpoint);//堵塞模式
		return ret;
   }
  /*************************************************
	Function:       send_data
	Description:    udp发送
	Input:          str: 字符数组，len: 字符数组个数 
	Output:         无
	Return:         无
	*************************************************/    
   int send_data(unsigned char str[], int len)
   {
	   boost::asio::ip::udp::endpoint send_endpoint(boost::asio::ip::address_v4::from_string(m_canetIP),m_canetPort); 
	   int ret = udp_sock.send_to(boost::asio::buffer(str,len),send_endpoint);
	   return ret;
   }
public:

	string m_canetIP;
	int m_canetPort;
	string m_pcIP;
	int m_pcPort;

   boost::asio::ip::udp::socket udp_sock;
   mutable boost::mutex mutex;
   
};


#endif
