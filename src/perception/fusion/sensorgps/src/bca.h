

#ifndef BCA_H__
#define BCA_H__
//#define  BOOST_DISABLE_ASSERTS
#include <iostream>
#include <string>
#include <sstream>
#include <vector>
#include <boost//algorithm/string/split.hpp>
#include <boost//algorithm/string/classification.hpp>
#include <boost/regex.hpp>
#include <boost/lexical_cast.hpp>
#include <boost/assert.hpp>
#include <stdlib.h>
#include<stdio.h>
#include <boost/asio.hpp>
#include <boost/thread//thread.hpp>

using namespace boost; 
using namespace  std;



typedef struct 
{   /* time struct */ 
    time_t time;        /* time (s) expressed by standard time_t */ 
    double sec;         /* fraction of second under 1 s */ 
} gtime_t; 
 


class bca
{
public:
	explicit bca();
	virtual ~bca();
public:
  /*************************************************
	Function:       int2String
	Description:    Int转字符串
	Input:          obj:整型
	Output:         无
	Return:         字符串
	*************************************************/
	static string int2String(const int n);
  /*************************************************
	Function:       int2String_boost
	Description:    Int转字符串
	Input:          obj:整型
	Output:         无
	Return:         字符串
	*************************************************/	
	static string int2String_boost(int obj);

  /*************************************************
	Function:       string2Int_boost
	Description:    字符串转Int
	Input:          obj:字符串
	Output:         无
	Return:         Int 
	*************************************************/		
	static int	  string2Int_boost(string obj);

  /*************************************************
	Function:       string2Double_boost
	Description:    字符串转Double
	Input:          obj:字符串
	Output:         无
	Return:         Double 
	*************************************************/	
	static double string2Double_boost(string obj);

  /*************************************************
	Function:       string2Float_boost
	Description:    字符串转float
	Input:          obj:字符串
	Output:         无
	Return:         float 
	*************************************************/	
	static float string2Float_boost(string obj);

  /*************************************************
	Function:       charArray2Int
	Description:    字符数组转成Int
	Input:          buffer：字符数组， start：起始位，length：长度
	Output:         无
	Return:         Int 
	*************************************************/		
	static int charArray2Int(unsigned char buffer[], int start, int length);

  /*************************************************
	Function:       charArray2Double
	Description:    字符数组转成Double
	Input:          buffer：字符数组， start：起始位
	Output:         无
	Return:         Double 
	*************************************************/	
	static double charArray2Double(unsigned char buffer[], int start);

  /*************************************************
	Function:       charArray2Float
	Description:    字符数组转成float
	Input:          buffer：字符数组， start：起始位
	Output:         无
	Return:         float 
	*************************************************/	
	static float charArray2Float(unsigned char buffer[], int start);

  /*************************************************
	Function:       charArray2uInt
	Description:    字符数组转成uInt
	Input:          buffer：字符数组， start：起始位，length：长度
	Output:         无
	Return:         uInt 
	*************************************************/	
	static unsigned int charArray2uInt(unsigned char buffer[], int start, int length);

    
	//将周内秒转成UTC时间，参考： https://blog.csdn.net/c4679281314/article/details/115402050
  /*************************************************
	Function:       epoch2time
	Description:    将初始时间转成标准时
	Input:          ep: 起始时间
	Output:         无
	Return:         无 
	*************************************************/	
	static gtime_t epoch2time(const double *ep) ;

  /*************************************************
	Function:       gpst2time
	Description:    gps时间转成标准时。
	Input:          week:gps周 ， sec: gps秒
	Output:         无
	Return:         无 
	*************************************************/	
	static gtime_t gpst2time(int week, double sec) ;

  /*************************************************
	Function:       timeadd
	Description:    加入间隔秒
	Input:          t: gps时间，sec： 添加的时间
	Output:         无
	Return:         新构造的时间 
	*************************************************/	
	static gtime_t timeadd(gtime_t t, double sec) ;

  /*************************************************
	Function:       GPSTime2UTCTime
	Description:    gps时间转成utc时间
	Input:          week:gps周 ， sec: gps秒， leapsec: 间隔s，一般为固定值。
	Output:         无
	Return:         无 
	*************************************************/	
    static unsigned long  GPSTime2UTCTime(int week,double sec,double leapsec);
};

#endif 