<launch>
    <rosparam file="$(find fusiontracking)/launch/params.yaml" command="load"/>

   <node name="sensorgps"  pkg="sensorgps"   type="sensorgps"   output="screen" respawn="true">
    <!-- they are system set parameters, diffrent kind cars are diffrent paremeters --> 

   <param name="baudrate" value="230400"  type="int"/>
   <!-- 红旗1：/dev/ttyUSB0 红旗2：/dev/ttyTHS1 -->
   <!-- <param name="serialport" value="/dev/ttyUSB0"  type="string"/> -->
  
    <!--0:serail  1:can -->
    <param name="gpsserialcan" value="0"   type="int"/>
    <param name="gpscanetip" value="*************" type="string"/>
    <param name="gpscanetport" value="4011" type="int"/>
    <param name="gpspcip" value="*************" type="string"/>
    <param name="gpspcport" value="4011" type="int"/>

    <!--inspva  inspvax   gps/imu开关-->
    <param name="useinspva" value="true" type="bool"/>
    <param name="useinspvax" value="true" type="bool"/>
    <param name="useimu" value="true" type="bool"/>
    <!-- 是否保存自车信息(经纬度、速度、航向角)，用于评估自车定位精度，true：保存CSV，false：不保存      -->
    <param name="isSaveSelfCarInfo" value="false" type="bool"/>
   </node>

</launch>
