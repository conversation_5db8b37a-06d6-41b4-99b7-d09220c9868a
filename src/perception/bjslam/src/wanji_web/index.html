<!--
 * @Author: your name
 * @Date: 2021-05-12 13:33:51
 * @LastEditTime: 2023-06-08 09:29:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /wanji_ws/src/wanji_web/wanji.html
-->
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>WLR_720混合导航客户端</title>
    <link rel="stylesheet" type="text/css" href="./resources/easyui/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="./resources/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="./resources/easyui/themes/color.css">
    <script type="text/javascript" src="./resources/easyui/jquery.min.js"></script>
    <script type="text/javascript" src="./resources/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="./resources/ros/three.js"></script>
    <script type="text/javascript" src="./resources/ros/eventemitter2.min.js"></script>
    <script type="text/javascript" src="./resources/ros/roslib.js"></script>
    <script type="text/javascript" src="./resources/ros/ros3d.js"></script>
    <script type="text/javascript" src="./resources/wait.js"></script>
    <script type="text/javascript" src="./resources/valid.js"></script>
    <script type="text/javascript" src="./resources/func.js"></script>
    <script type="text/javascript" src="./resources/protocol.js"></script>
    <script type="text/javascript" type="text/javascript" src="./resources/qrcode/qrcode.js"></script>
    <script type="text/javascript" src="./resources//base-loading.js"></script>
</head>


<body id="winbody" class="easyui-layout">
    <!-- 顶部栏  $("input[name='auditRuleUserType']:checked").val()-->
    <div id="north" data-options="region:'north',border:false"
        style="height:65px;background:url(./resources/easyui/themes/icons/wjlogo.png) no-repeat 4px 5px;background-size:contain;padding:0px">
        <div style="margin-left:20%;margin-top:1px;padding:0px">
            <h2 style="padding:0px;">
                <table>
                    <tr>
                        <td>AGV位姿:&emsp;</td>
                        <td id="viewerPoseAGVX"></td>
                        <td>&emsp;</td>
                        <td id="viewerPoseAGVY"></td>
                        <td>&emsp;</td>
                        <td id="viewerPoseAGVA"></td>
                        <td>&emsp;&emsp;</td>
                        <td>Lidar位姿:&emsp;</td>
                        <td id="viewerPoseX"></td>
                        <td>&emsp;</td>
                        <td id="viewerPoseY"></td>
                        <td>&emsp;</td>
                        <td id="viewerPoseA"></td>
                        <td>&emsp;&emsp;</td>
                        <td id="viewerPoseS"></td>
                        <td>&emsp;&emsp;</td>
                        <td id="LidarState"></td>
                    </tr>
                </table>
            </h2>
        </div>
        <div id="unshow2" class="easyui-window"
            data-options="closable:true, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: false, border:'thin',cls:'c2'">
            <table>
                <tr>
                    <td>&emsp;</td>
                    <td id="viewerPoseAGVZ"></td>
                    <td>&emsp;</td>
                    <td id="viewerPoseZ"></td>
                </tr>
            </table>
        </div>
        <script>
            $("#LidarState").textbox({
                // left: '10',
                label: '雷达状态: ',
                labelWidth: 100,
                width: 200,
                height: 30,
                labelPosition: 'before',
                value: '关闭显示',
                readonly: true,
            });
            $("#viewerPoseS").textbox({
                left: '10',
                label: '位姿状态: ',
                labelWidth: 100,
                width: 200,
                height: 30,
                labelPosition: 'before',
                readonly: true,
            });
            $("#viewerPoseAGVX").numberbox({
                label: 'X:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });
            $("#viewerPoseAGVY").numberbox({
                label: 'Y:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });
            $("#viewerPoseAGVZ").numberbox({
                label: 'Z:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });
            $("#viewerPoseAGVA").numberbox({
                label: 'A:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });
            $("#viewerPoseX").numberbox({
                label: 'X:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });
            $("#viewerPoseY").numberbox({
                label: 'Y:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });
            $("#viewerPoseZ").numberbox({
                label: 'Z:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });
            $("#viewerPoseA").numberbox({
                label: 'A:',
                labelWidth: 30,
                width: 100,
                height: 30,
                labelPosition: 'before',
                readonly: 'true',
                precision: 3,
            });  
        </script>
    </div>
    <!-- 状态栏 -->
    <div data-options="region:'south',border:false" style="height:60px;padding:0px;">
        <table>
            <td width="1%"></td>
            <td width="9%">
                <h4 id="errcode"></h4>
            </td>
            <td width="80%"> </td>
            <td width="10%">
                <h4 id="wjversion"></h4>
            </td>
        </table>
    </div>

    <!-- 菜单栏 -->
    <div data-options="region:'west',collapsible:true, split:true,title:'菜单栏'" style="width:160px;padding:0px;">
        <div>
            <hr style="border:1px none #000" />
            <table>
                <tr>
                    <td style="width:70px;">启动Web</td>
                    <td id="openWeb"></td>
                </tr>
                <tr>
                    <td>实时位姿</td>
                    <td id="viewerPose"></td>
                </tr>
                <tr>
                    <td>实时雷达</td>
                    <td id="viewerLidar"></td>
                </tr>
                <tr>
                    <td>实时地图</td>
                    <td id="viewerMap"></td>
                </tr>
                <tr>
                    <td> <a id="SetSlamStateBtn" class="easyui-linkbutton" data-options="iconCls:'icon-lidar'"
                            href="javascript:void(0)" onclick="SetSlamState()">SLAM</a> </td>
                    <td id="ViewSlamState"></td>
                </tr>
            </table>
            <hr style="border:1px none #000" />
        </div>
        <div class="easyui-accordion" data-options="fit:false,border:false,multiple:true">
            <div title="模式配置" data-options="selected:true" style="padding:10px 5px 1px 5px;">
                <h4>--------&emsp;雷达模式&emsp;--------</h4>
                <hr style="border:1px none #000" />
                <form id="lidarModel-chose">
                    <div style="margin-bottom:10px">
                        <input class="easyui-radiobutton" id="lidarModel1" name="lidarModel" value="1" label="在线模式"
                            data-options="onChange:changeLidarModel">
                    </div>
                    <div style="margin-bottom:10px">
                        <input class="easyui-radiobutton" id="lidarModel0" name="lidarModel" value="0" label="离线模式"
                            data-options="onChange:changeLidarModel">
                    </div>
                </form>
                <h4>--------&emsp;工作模式&emsp;--------</h4>
                <form id="model-chose">
                    <div style="margin-bottom:10px">
                        <input class="easyui-radiobutton" id="model1" name="model" value="1" label="初始建图"
                            data-options="onChange:changeModel">
                    </div>
                    <div style="margin-bottom:10px">
                        <input class="easyui-radiobutton" id="model2" name="model" value="2" label="连续建图"
                            data-options="onChange:changeModel">
                    </div>
                    <div style="margin-bottom:10px">
                        <input class="easyui-radiobutton" id="model4" name="model" value="4" label="更新地图"
                            data-options="onChange:changeModel">
                    </div>
                    <div style="margin-bottom:10px">
                        <input class="easyui-radiobutton" id="model3" name="model" value="3" label="连续定位"
                            data-options="onChange:changeModel">
                    </div>

                    <div style="margin-bottom:10px">
                        <input class="easyui-radiobutton" id="model0" name="model" value="0" label="空闲模式"
                            data-options="onChange:changeModel">
                    </div>
                </form>
            </div>
            <div title="控制台" style="padding:10px 5px;">
                <table>
                    <!--表格 4行tr 2列 td-->
                    <tr>
                        <td>数据回放</td>
                        <td id="playPcap"></td>
                    </tr>
                    <tr>
                        <td>数据录制</td>
                        <td id="recordLidar"></td>
                    </tr>
                    <tr>
                        <td>录制时长</td>
                        <td id="recordDurT"></td>
                    </tr>
                    <tr>
                        <td>重置箭头</td>
                        <td> <a id="resetPoseUI" href="#" data-options="iconCls:'icon-restart2'">重置</a></td>
                    </tr>
                    <tr>
                        <td>保存地图</td>
                        <td> <a id="saveMap" href="#" data-options="iconCls:'icon-savemap'">保存</a></td>
                    </tr>
                    <tr>
                        <td>保存参数</td>
                        <td> <a id="saveSet" href="#" data-options="iconCls:'icon-read'">保存</a></td>
                    </tr>
                    <tr>
                        <td>恢复出厂</td>
                        <td> <a id="recoveryButton" href="#" data-options="iconCls:'icon-restart'">重置</a></td>
                    </tr>
                    <!--启动台开关解释 -->
                    <script>
                        $("#openWeb").switchbutton({
                            onText: "启动",
                            offText: "关闭",
                            checked: false,
                            onChange: function (checked) {
                                if (!checked) {
                                    //断开ROS
                                    closeRos();

                                    // defaultWeb();

                                    rvizViewer.stop();
                                    window.location.reload();

                                }
                                else {
                                    //连接ROS 其他仍旧屏蔽 等待主程序启动
                                    connectRos();
                                }
                            }
                        });

                        $("#viewerPose").switchbutton({
                            onText: "显示",
                            offText: "关闭",
                            onChange: function (checked) {
                                cViewPoseStatus = checked;
                                if (!checked) {
                                    viewPoseStatus((-1));
                                }
                            }
                        });

                        $("#viewerLidar").switchbutton({
                            onText: "显示",
                            offText: "关闭",
                            onChange: function (checked) {
                                console.log("viewLidar: ", checked, cViewLidarStatus);
                                if (!checked) {
                                    if (cViewLidarStatus) {
                                        // 设置关闭扫描点云
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_VIEWLIDAR);
                                        intTo8Byte(0, l_msg, 26);
                                        addTail_(l_msg);
                                        c_slam.MSG_SETCMD_VIEWLIDAR.start(l_msg);
                                    }
                                }
                                else {
                                    if (!cViewLidarStatus) {
                                        // 设置开启扫描点云
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_VIEWLIDAR);
                                        intTo8Byte(1, l_msg, 26);
                                        addTail_(l_msg);
                                        c_slam.MSG_SETCMD_VIEWLIDAR.start(l_msg);
                                    }
                                }
                                cViewLidarStatus = checked;
                            }
                        });
                        $("#viewerMap").switchbutton({
                            onText: "显示",
                            offText: "关闭",
                            onChange: function (checked) {
                                if (!checked) {
                                    if (cViewMapStatus) {
                                        // 设置关闭地图
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_VIEWMAP);
                                        intTo8Byte(0, l_msg, 26);
                                        addTail_(l_msg);
                                        c_slam.MSG_SETCMD_VIEWMAP.start(l_msg);
                                    }
                                }
                                else {
                                    if (!cViewMapStatus) {
                                        // 设置显示地图
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_VIEWMAP);
                                        intTo8Byte(1, l_msg, 26);
                                        addTail_(l_msg);
                                        c_slam.MSG_SETCMD_VIEWMAP.start(l_msg);
                                    }
                                }
                                cViewMapStatus = checked;
                            }
                        });

                        $("#ViewSlamState").switchbutton({
                            onText: "启动",
                            offText: "关闭",
                            onChange: function (checked) {
                                if (!checked) {
                                    clearStatus();
                                    clearBtnValue("#viewerLidar");
                                    clearBtnValue("#viewerMap");
                                    clearBtnValue("#viewerPose");
                                    clearBtnValue("#recordLidar");
                                    clearBtnValue("#playPcap");

                                    setBtnEnable("#viewerLidar", false);
                                    setBtnEnable("#viewerMap", false);
                                    setBtnEnable("#viewerPose", false);
                                    setBtnEnable("#playPcap", false);
                                    setSetEnable("#saveMap", false);
                                    setSetEnable("#saveSet", false);
                                    clearSlamModel();
                                    disableSlamModelChose();
                                }
                                else {
                                    setBtnEnable("#viewerLidar", true);
                                    setBtnEnable("#viewerMap", true);
                                    setBtnEnable("#viewerPose", true);
                                    setBtnEnable("#playPcap", true);
                                    setSetEnable("#saveMap", true);
                                    setSetEnable("#saveSet", true);
                                    enableSlamModelChose();

                                    QuerySlamMainInfo();
                                }
                                enableDriverModelChose(checked);
                            }
                        });

                        $("#playPcap").switchbutton({
                            onText: "启动",
                            offText: "关闭",
                            onChange: function (checked) {
                                if (!checked) {
                                    if (cPlayPcapStatus) {
                                        // 设置关闭播包
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_DRIVERCONTROL);
                                        intTo8Byte(0, l_msg, 26);
                                        addTail_(l_msg);
                                        c_master.MSG_SETCMD_DRIVERCONTROL.start(l_msg);
                                    }
                                }
                                else {
                                    if (!cPlayPcapStatus) {
                                        // 设置开启播包
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_DRIVERCONTROL);
                                        intTo8Byte(1, l_msg, 26);
                                        addTail_(l_msg);
                                        c_master.MSG_SETCMD_DRIVERCONTROL.start(l_msg);
                                    }
                                }
                                cPlayPcapStatus = checked;
                            }
                        });

                        $("#recordLidar").switchbutton({
                            onText: "录制",
                            offText: "关闭",
                            onChange: function (checked) {
                                if (!checked) {
                                    if (cRecordLidarStatus) {
                                        // 设置启动录制
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_RECORDDATA);
                                        intTo8Byte(0, l_msg, 26);
                                        addTail_(l_msg);
                                        c_master.MSG_SETCMD_RECORDDATA.start(l_msg);
                                        //停止录制 停止消息发送
                                        c_master.MSG_QUERYCMD_RECORDDATA.stop();
                                    }
                                }
                                else {
                                    if (!cRecordLidarStatus) {
                                        // 设置启动录制
                                        let l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_RECORDDATA);
                                        intTo8Byte(1, l_msg, 26);
                                        addTail_(l_msg);
                                        c_master.MSG_SETCMD_RECORDDATA.start(l_msg);

                                        //开始不间断查询录包状态 
                                        c_master.MSG_QUERYCMD_RECORDDATA.stop();
                                        l_msg = PROTArray.slice();
                                        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_RECORDDATA);
                                        addTail_(l_msg);
                                        c_master.MSG_QUERYCMD_RECORDDATA.start(l_msg);
                                    }
                                }
                                cRecordLidarStatus = checked;
                            }
                        });

                        $("#recordDurT").textbox({
                            width: 60,
                            labelPosition: 'before',
                            readonly: 'true'
                        });

                        $("#saveSet").linkbutton({
                            onClick: function () {
                                sendSaveParamCMD();
                            }
                        });

                        $("#saveMap").linkbutton({
                            onClick: function () {
                                var modelValue = $("input[name='model']:checked").val();
                                if (modelValue == 1 || modelValue == 2 || modelValue == 4) {
                                    $('#saveMapWarn').dialog('open');
                                    executeSaveMap();
                                }
                                else {
                                    $('#nosaveMapWarn').dialog('open');
                                    //console.log("非建图模式，无法保存地图");
                                }
                            }
                        });

                        $("#recoveryButton").linkbutton({
                            onClick: function () {
                                $('#recoveryWarn').dialog('open');
                            }
                        });

                        $("#resetPoseUI").linkbutton({
                            onClick: function () {
                                sendResetPoseUI();
                            }
                        });
                    </script>
                </table>
            </div>

            <div title="参数配置" style="padding:10px;">
                <a id="openSet4" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-lidar'"
                    style="width:100%" onclick="readSet4()">雷达参数配置</a>
                <hr style="height:1px;border:none;border-top:1px solid #ffffff;" />
                <a id="openSet5" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-param'"
                    style="width:100%" onclick="readSet5()">AGV参数配置</a>
                <hr style="height:1px;border:none;border-top:1px solid #ffffff;" />
                <a id="openSet9" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-sys'"
                    style="width:100%" onclick="readSet9()">系统参数配置</a>
                <hr style="height:1px;border:none;border-top:1px solid #ffffff;" />
                <a id="openSet7" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-net'"
                    style="width:100%" onclick="readNetParamTab()">网络参数配置</a>
                <hr style="height:1px;border:none;border-top:1px solid #ffffff;" />
                <a id="openSet6" href="javascript:void(0)" class="easyui-linkbutton"
                    data-options="iconCls:'icon-delete'" style="width:100%" onclick="readSet6()">数据文件清理</a>
                <hr style="height:1px;border:none;border-top:1px solid #ffffff;" />
                <a id="openSet10" href="javascript:void(0)" class="easyui-linkbutton"
                    data-options="iconCls:'icon-locate'" style="width:100%" onclick="readCalibTabParamTab()">标定参数配置</a>
            </div>
        </div>
    </div>

    <div id="unshow" class="easyui-window"
        data-options="closable:true, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: false, border:'thin',cls:'c2'">
        <table>
            <td>
                <div id="LidarZ0"></div>
            </td>
            <td>
                <div id="LidarAngleR0"></div>
            </td>
            <td>
                <div id="LidarAngleP0"></div>
            </td>
            <td>
                <div id="LidarZ1"></div>
            </td>
            <td>
                <div id="LidarAngleR1"></div>
            </td>
            <td>
                <div id="LidarAngleP1"></div>
            </td>

            <td>
                <div id="LidarZ2"></div>
            </td>
            <td>
                <div id="LidarAngleR2"></div>
            </td>
            <td>
                <div id="LidarAngleP2"></div>
            </td>

            <td>
                <div id="LidarZ3"></div>
            </td>
            <td>
                <div id="LidarAngleR3"></div>
            </td>
            <td>
                <div id="LidarAngleP3"></div>
            </td>
        </table>

    </div>
    <!-- 标定栏 -->
    <div id="CalibTab" class="easyui-window" title="标定参数配置"
        data-options="closable:true, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: false, border:'thin',cls:'c2'"
        style="width:790px;height:163px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <table>
                    <tr>
                        <th></th>
                        <th>名称</th>
                        <th>Lidar使能</th>
                        <th>粗标定</th>
                        <th>相对-X</th>
                        <th>相对-Y</th>
                        <th>相对-Yaw</th>
                    </tr>
                    <tr>
                        <th>0</th>
                        <td>
                            <div id="LidarName0"></div>
                        </td>
                        <th>
                            <div id="EnLider0"></div>
                        </th>
                        <td>
                            <a class="easyui-linkbutton" id="PoseSet0" href="javascript:void(0)"
                                onclick="PoseSet(0)">设定位姿</a>
                        </td>
                        <td>
                            <div id="LidarX0"></div>
                        </td>
                        <td>
                            <div id="LidarY0"></div>
                        </td>

                        <td>
                            <div id="LidarAngleY0"></div>
                        </td>
                        <td>
                            &ensp;&emsp;
                        </td>
                        <td>
                            <a class="easyui-linkbutton" id="openCalibBtn" data-options="iconCls:'icon-start'"
                                href="javascript:void(0)" onclick="openLidarCalibIndex()">自动标定</a>
                            <a class="easyui-linkbutton" id="closeCalibBtn" data-options="iconCls:'icon-stop'"
                                href="javascript:void(0)" onclick="closeLidarCalibIndex()">标定关闭</a>
                            <a class="easyui-linkbutton" id="applyCalibBtn1" href="javascript:void(0)"
                                onclick="applyLidarCalibResultInex(1)">标定应用</a>

                        </td>

                    </tr>

                    <tr>
                        <th>1</th>
                        <td>
                            <div id="LidarName1"></div>
                        </td>
                        <th>
                            <div id="EnLider1"></div>
                        </th>
                        <td>
                            <a class="easyui-linkbutton" id="PoseSet1" href="javascript:void(0)"
                                onclick="PoseSet(1)">设定位姿</a>
                        </td>
                        <td>
                            <div id="LidarX1"></div>
                        </td>
                        <td>
                            <div id="LidarY1"></div>
                        </td>
                        <td>
                            <div id="LidarAngleY1"></div>
                        </td>
                        <td>
                            &ensp;&emsp;
                        </td>
                        <td>
                            <div id="calibrationStateIndex1"></div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>



    <script>
        $(function () {
            $('#CalibTab').window({
                onBeforeClose: function () {
                    $('#CalibTab').window('close', true);
                    IsNorth = 0;
                }
            });
        });
        $("#calibrationStateIndex0").textbox({
            width: '330px',
            height: '30px',
            labelWidth: 70,
            value: '未启动',
            required: true,
            readonly: true,
        });
        $("#calibrationStateIndex1").textbox({
            width: '230px',
            height: '30px',
            labelWidth: 70,
            value: '未启动',
            required: true,
            readonly: true,
        });
        $("#LidarName0").textbox({
            width: '80px',
            readonly: true,
            height: 30,
            value: '---',
            required: true,
            missingMessage: "不可为空",
        });
        $("#LidarX0").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            value: '0.0',
            precision: 3,
        });
        $("#LidarY0").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            value: '0.0',
            precision: 3,
        });
        $("#LidarZ0").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            value: '0.0',
            precision: 3,
        });
        $("#LidarAngleR0").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            value: '0.0',
            precision: 3,
        });
        $("#LidarAngleP0").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            value: '0.0',
            precision: 3,
        });
        $("#LidarAngleY0").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            value: '0.0',
            precision: 3,
        });
        $("#EnLider0").switchbutton({
            width: '70px',
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {
                    if (cViewLidarPose0) {
                        setSetEnable("#PoseSet0",false);
                        // 设置关闭
                        let l_msg = PROTArray.slice();
                        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERENABLE);
                        l_subscript = strToAscii(c_ListCalibLidarName[0], l_msg, 26);
                        intTo8Byte(0, l_msg, l_subscript++);
                        addTail_(l_msg);
                        c_device.MSG_SETCMD_LASERENABLE.start(l_msg);
                    }
                }
                else {
                    if (!cViewLidarPose0) {
                        setSetEnable("#PoseSet0",true);
                        // 设置开启
                        let l_msg = PROTArray.slice();
                        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERENABLE);
                        l_subscript = strToAscii(c_ListCalibLidarName[0], l_msg, 26);
                        intTo8Byte(1, l_msg, l_subscript++);
                        addTail_(l_msg);
                        c_device.MSG_SETCMD_LASERENABLE.start(l_msg);
                    }
                }
                cViewLidarPose0 = checked;
            }
        });

        $("#LidarName1").textbox({
            width: '80px',
            readonly: true,
            height: 30,
            value: '---',
            required: true,
            missingMessage: "不可为空",
        });
        $("#LidarX1").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            required: true,
            precision: 3,
        });
        $("#LidarY1").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            required: true,
            precision: 3,
        });
        $("#LidarZ1").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            required: true,
            precision: 3,
        });
        $("#LidarAngleR1").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            required: true,
            precision: 3,
        });
        $("#LidarAngleP1").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            required: true,
            precision: 3,
        });
        $("#LidarAngleY1").numberbox({
            width: '80px',
            height: '30px',
            prompt: '(单位:m)',
            readonly: true,
            required: true,
            precision: 3,
        });
        $("#EnLider1").switchbutton({
            width: '70px',
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {
                    if (cViewLidarPose1) {
                        setSetEnable(("#PoseSet1"),false);
                        // 设置关闭
                        let l_msg = PROTArray.slice();
                        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERENABLE);
                        l_subscript = strToAscii(c_ListCalibLidarName[1], l_msg, 26);
                        intTo8Byte(0, l_msg, l_subscript++);
                        addTail_(l_msg);
                        c_device.MSG_SETCMD_LASERENABLE.start(l_msg);
                    }
                }
                else {
                    if (!cViewLidarPose1) {
                        setSetEnable("#PoseSet1",true);
                        // 设置开启
                        let l_msg = PROTArray.slice();
                        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERENABLE);
                        l_subscript = strToAscii(c_ListCalibLidarName[1], l_msg, 26);
                        intTo8Byte(1, l_msg, l_subscript++);
                        addTail_(l_msg);
                        c_device.MSG_SETCMD_LASERENABLE.start(l_msg);
                    }
                }
                cViewLidarPose1 = checked;
            }
        });
    </script>
    <!-- 主显示 background-color: black;-->
    <div id="center" data-options="region:'center'" style="width:auto;background:rgb(0, 0, 0);">
        <div id="rvizViewer"></div>
    </div>
    <!---未打开位姿开关警告--->
    <div id="NotOpenPoseWarn" class="easyui-dialog" title="未打开位姿开关警告" style="width:400px;height:100px;padding:10px"
        data-options="
                closed: true,
                closable: true,
				iconCls: 'icon-tip'">
        未打开实时位姿
    </div>
    <!---设定成功--->
    <div id="SetPoseSuc" class="easyui-dialog" title="设定位姿提醒" style="width:400px;height:100px;padding:10px"
        data-options="
                closed: true,
                closable: true,
				iconCls: 'icon-tip'">
        位姿设定成功
    </div>

    <div id="GetPoseWarn" class="easyui-dialog" title="获取位姿提醒" style="width:400px;height:100px;padding:10px"
        data-options="
                closed: true,
                closable: true,
				iconCls: 'icon-tip'">
        设定失败，定位位姿已刷新，请重新设定。
    </div>
    <!---重置参数警告--->
    <div id="recoveryWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-help',
				
                buttons: [
                {
					text:'确认',
					iconCls:'icon-ok',
					handler:function(){
                        $('#recoveryWarn').dialog('close');
                        executeRecovery();
                        
					}
                },
                {
                    text:'取消',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#recoveryWarn').dialog('close');
					}
				}]
			">
        是否恢复出厂设置？请确认！
    </div>
    <!---重启警告--->
    <div id="restartWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-help',
				
                buttons: [
                {
					text:'确认',
					iconCls:'icon-ok',
					handler:function(){

                        executeReStart();

					}
                },
                {
                    text:'取消',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#restartWarn').dialog('close');
					}
				}]
			">
        是否重启程序？请确认！
    </div>
    <!---正在保存地图警告--->
    <div id="saveMapWarn" class="easyui-dialog" title="地图保存提醒" style="width:400px;height:100px;padding:10px"
        data-options="
                closed: true,
                closable: false,
				iconCls: 'icon-tip'">
        保存地图中，请勿移动雷达！等待地图保存成功自动关闭弹窗
    </div>
    <!---保存地图成功提醒--->
    <div id="saveMapSucc" class="easyui-dialog" title="地图保存提醒" style="width:400px;height:100px;padding:10px"
        data-options="
                closed: true,
				iconCls: 'icon-tip'">
        保存地图成功！
    </div>

    <!---恢复出厂成功警告--->
    <div id="recoverySuccWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        恢复出厂完毕，请重启工控机！
    </div>

    <!---参数保存成功警告--->
    <div id="saveParamSuccWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        参数保存完毕！
    </div>

    <div id="startWorkMode" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        工作模式切换成功！
    </div>

    <div id="getStandbyWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        无效操作,请切换[空闲模式]！
    </div>
    <div id="getLocalbyWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        无效操作,请切换[定位模式]！
    </div>

    <!---SLAM启动成功警告--->
    <div id="startSlamSuccWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        SLAM启动成功！
    </div>

    <!---查询雷达名警告--->
    <div id="lidarNumWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        雷达个数超限！
    </div>

    <!---参数保存成功警告--->
    <div id="stopSlamSuccWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        SLAM已关闭，请手动打开！
    </div>

    <!---无法保存地图警告--->
    <div id="nosaveMapWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'确认',
					iconCls:'icon-ok',
					handler:function(){
                        $('#nosaveMapWarn').dialog('close');
					}
                }]
            ">
        当前模式非建图模式，无法保存地图！
    </div>
    <!---切换模式警告--->
    <div id="changeModelWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'确认',
					iconCls:'icon-ok',
					handler:function(){
                        $('#changeModelWarn').dialog('close');
                        executeChangeModel(0);
					}
                },
                {
                    text:'取消',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#changeModelWarn').dialog('close');
                        recoveryRawModel();
					}
				}]
            ">
        是否切换为空闲模式，请确认。
    </div>
    <!---提示保存参数警告--->
    <div id="saveParamWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'是',
					iconCls:'icon-ok',
					handler:function(){
                        $('#saveParamWarn').dialog('close');
                        sendSaveParamCMD();
					}
                },
                {
                    text:'否',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#saveParamWarn').dialog('close');
					}
				}]
            ">
        当前操作须保存参数，是否保存参数，请确认。
    </div>
    <!---提示重启SLAM警告--->
    <div id="resetSLAMWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'是',
					iconCls:'icon-ok',
					handler:function(){
                        $('#resetSLAMWarn').dialog('close');
                        $('#waitWarn').dialog('open');
                        sendResetSLAMCMD();
					}
                },
                {
                    text:'否',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#resetSLAMWarn').dialog('close');
                        $('#waitWarn').dialog('close');
					}
				}]
            ">
        当前操作重启SLAM生效，是否重启SLAM，请确认。
    </div>

    <!---强制重启SLAM警告--->
    <div id="forceResetSLAMWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'是',
					iconCls:'icon-ok',
					handler:function(){
                        $('#forceResetSLAMWarn').dialog('close');
                        $('#waitWarn').dialog('open');
                        sendResetSLAMCMD();
					}
                }]
            ">
        当前操作重启SLAM生效，请点击[是]重启SLAM。
    </div>

    <!---提示保存参数并重启SLAM警告--->
    <div id="saveParamResetSLAMWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                closable: false,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'是',
					iconCls:'icon-ok',
					handler:function(){
                        $('#saveParamResetSLAMWarn').dialog('close');
                        $('#waitWarn').dialog('open');
                        sendSaveParamCMD();
                        sendResetSLAMCMD();
					}
                },
                {
                    text:'否',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#saveParamResetSLAMWarn').dialog('close');
                        $('#waitWarn').dialog('close');
					}
				}]
            ">
        当前操作保存参数并重启SLAM后生效，是否保存并重启SLAM，请确认。
    </div>

    <!---切换雷达模式警告--->
    <div id="changeLidarModelWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'确认',
					iconCls:'icon-ok',
					handler:function(){
                        $('#changeLidarModelWarn').dialog('close');
                        executeChangeLidarModel();
					}
                },
                {
                    text:'取消',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#changeLidarModelWarn').dialog('close');
                        recoveryRawLidarModel();
					}
				}]
            ">
        是否切换雷达模式，请确认。
    </div>
    <!---录制操作异常提醒--->
    <div id="recordWarn" class="easyui-dialog" title="录制异常提醒" style="width:400px;height:100px;padding:10px"
        data-options="
                closed: true,
				iconCls: 'icon-tip'">
       录制数据操作异常，请检查!
    </div>

    <div id="waitWarn" class="easyui-dialog" title="等待窗口" style="width:400px;height:90px;padding:10px" data-options="
                closable: false,
                closed: true,
                iconCls: 'icon-tip',
            ">
        请耐心等待，动作执行完毕将自动关闭弹窗！
    </div>

    <div id="set0" class="easyui-window" title="离线建图参数"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:700px;height:450px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <h3>初始位姿:</h3>
                <div id="set0poseX"></div>
                <div id="set0poseY"></div>
                <div id="set0poseA"></div>

                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>地图设置:&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;场景选择:
                </h3>
                <table width="650">
                    <tr>
                        <td width="30%">
                            <div id="map0Name"></div>
                        </td>

                        <td width="23%">
                            <select id="map0Size" class="easyui-combobox" name="map0Size" label="地图分辨率:"
                                labelPosition="before" editable="false" style="width:100%;">
                                <option value="0.05">0.05</option>
                                <option value="0.1">0.1</option>
                                <option value="0.2">0.2</option>
                            </select>
                        </td>
                        <td width="14%"></td>
                        <td width="29%">
                            <select id="map0Model" class="easyui-combobox" name="map0Model" label="场景模式:"
                                labelPosition="before" editable="false" style="width:100%;">
                                <option value="0">室内场景</option>
                                <option value="1">室外场景</option>
                                <option value="2">复杂场景</option>
                            </select>
                        </td>
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>数据包选择:&emsp;<div id="read0Bag"></div>
                </h3>
                <table width="650">
                    <tr>
                        <input id="bag0NameList" class="easyui-combobox" name="bag0NameList" label="数据包列表:"
                            editable="false" labelPosition="before" data-options="validType:['unnormal'],missingMessage:'不可为空',required:true,limitToList:true,width:'220px',
                                            labelWidth:70,valueField:'id', textField:'text', panelHeight:'auto'">
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
            </div>
            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                    onclick="okSet0Value()" style="width:80px">确认</a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                    onclick="quitSet0Value()" style="width:80px">退出</a>
            </div>
        </div>

        <script>
            $("#set0poseX").numberbox({
                left: '10',
                label: '         X-',
                labelWidth: 80,
                labelPosition: 'before',
                prompt: 'X轴位置(单位:m)',
                required: true,

                missingMessage: "不可为空",
                precision: 3,//保留3位小数 
            });
            $("#set0poseY").numberbox({
                label: 'Y-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'Y轴位置(单位:m)',

                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#set0poseA").numberbox({
                label: 'A-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: '角度(单位:deg)',
                required: true,
                precision: 3,
                missingMessage: "不可为空",
                min: 0,
                max: 360,
            });
            $("#map0Name").textbox({
                left: '10',
                label: '地图名 ：',
                labelWidth: 80,
                labelPosition: 'before',
                prompt: '保存地图名称',
                validType: ['strname'],
                required: true,
                missingMessage: "不可为空"
            });
            $("#read0Bag").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {

                }

            });
        </script>
    </div>

    <div id="set1" class="easyui-window" title="初始建图参数"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:700px;height:285px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <h3>初始位姿:</h3>
                <div id="set1poseX"></div>&emsp;&emsp;&emsp;<div id="set1poseY"></div>&emsp;&emsp;&emsp;<div
                    id="set1poseZ"></div>&emsp;&emsp;&emsp;<div id="set1poseA"></div>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>地图设置:&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;模式选择:
                </h3>
                <table width="650">
                    <tr>
                        <td width="30%">
                            <div id="map1Name"></div>
                        </td>

                        <td width="23%">
                            <select id="map1Size" class="easyui-combobox" name="mapSize" label="地图分辨率:"
                                labelPosition="before" editable="false" style="width:100%;">
                                <option value="0.05">0.05</option>
                                <option value="0.1">0.1</option>
                                <option value="0.2">0.2</option>
                            </select>
                        </td>
                        <td width="14%"></td>
                        <td width="29%">
                            <select id="mapModel" class="easyui-combobox" name="mapSize" label="场景模式:"
                                labelPosition="before" editable="false" style="width:100%;">
                                <option value="0">室内场景</option>
                                <option value="1">室外场景</option>
                                <option value="2">复杂场景</option>
                            </select>
                        </td>
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
            </div>
            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                    onclick="okSet1Value()" style="width:80px">确认</a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                    onclick="quitSet1Value()" style="width:80px">取消</a>
            </div>
        </div>
        <script>
            $("#set1poseX").numberbox({
                width: 100,
                left: '10',
                label: 'X-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',

                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#set1poseY").numberbox({
                width: 100,
                label: 'Y-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"

            });
            $("#set1poseZ").numberbox({
                width: 100,
                label: 'Z-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"

            });
            $("#set1poseA").numberbox({
                width: 100,
                label: 'A-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:deg)',
                required: true,
                precision: 3,
                missingMessage: "不可为空",
                // min:0,
                // max:360,
            });
            $("#map1Name").textbox({
                left: '10',
                label: '地图名 ：',
                labelWidth: 80,
                labelPosition: 'before',
                prompt: '保存地图名称',
                validType: ['strname'],
                required: true,
                missingMessage: "不可为空"
            });
        </script>
    </div>

    <div id="set2" class="easyui-window" title="连续建图参数"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:700px;height:295px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <h3>当前位姿:&emsp;<div id="set2readPose"></div>
                </h3>
                <div id="set2poseX"></div>&emsp;&emsp;&emsp;<div id="set2poseY"></div>&emsp;&emsp;&emsp;<div
                    id="set2poseZ"></div>&emsp;&emsp;&emsp;<div id="set2poseA"></div>
                <!-- &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;模式选择:</h3> -->
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>地图设置:&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&ensp;模式选择:
                </h3>
                <table width="650">
                    <tr>
                        <td width="61%">
                            <input id="map2NameList" class="easyui-combobox" name="map2NameList" label="地图/分辨率:"
                                editable="false" labelPosition="before" data-options="validType:['unnormal'],required:true,missingMessage:'不可为空',
                                        valueField:'id', textField:'text', panelHeight:'auto'">
                            <div id="set2readMapList">获取地图列表</div>
                        </td>
                        <td width="10%"></td>
                        <td width="29%">
                            <select id="mapModel2" class="easyui-combobox" name="mapSize" label="场景模式:"
                                labelPosition="before" editable="false" style="width:100%;">
                                <option value="0">室内场景</option>
                                <option value="1">室外场景</option>
                                <option value="2">复杂场景</option>
                            </select>
                        </td>
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />

            </div>
            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                    onclick="okSet2Value()" style="width:80px">确认</a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                    onclick="quitSet2Value()" style="width:80px">取消</a>
            </div>
        </div>
        <script>

            $("#set2poseX").numberbox({
                width: 100,
                left: '10',
                label: 'X-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',

                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#set2poseY").numberbox({
                width: 100,
                label: 'Y-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"

            });
            $("#set2poseZ").numberbox({
                width: 100,
                label: 'Z-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"

            });
            $("#set2poseA").numberbox({
                width: 100,
                label: 'A-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:deg)',
                required: true,
                precision: 3,
                missingMessage: "不可为空",
                // min:0,
                // max:360,
            });

            $("#set2readPose").linkbutton({
                iconCls: 'icon-reload',
                onClick: function () {
                    //console.log("read2Pose");
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_SAVEPOSE);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_SAVEPOSE.start(l_msg);
                }

            });

            $("#set2readMapList").linkbutton({
                iconCls: 'icon-reload',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_MAPLIST);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_MAPLIST.start(l_msg);
                }
            });




        </script>
    </div>

    <div id="set3" class="easyui-window" title="连续定位参数"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:700px;height:295px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">

                <h3>设定位姿:&emsp;<div id="read3Pose"></div>
                </h3>
                <div id="set3poseX"></div>&emsp;&emsp;&emsp;<div id="set3poseY"></div>&emsp;&emsp;&emsp;<div
                    id="set3poseZ"></div>&emsp;&emsp;&emsp;<div id="set3poseA"></div>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>地图设置:&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&ensp;模式选择:
                </h3>
                <table width="650">
                    <tr>
                        <td width="61%">
                            <input id="map3NameList" class="easyui-combobox" name="map3NameList" label="地图/分辨率:"
                                editable="false" labelPosition="before" data-options="validType:['unnormal'],required:true,missingMessage:'不可为空',width:'240px',labelWidth:80,
                                        valueField:'id', textField:'text', panelHeight:'auto'">
                            <div id="set3readMapList">获取地图列表</div>
                        </td>
                        <td width="10%"></td>
                        <td width="29%">
                            <select id="mapModel3" class="easyui-combobox" name="mapSize" label="场景模式:"
                                labelPosition="before" editable="false" style="width:100%;">
                                <option value="0">室内场景</option>
                                <option value="1">室外场景</option>
                                <option value="2">复杂场景</option>
                            </select>
                        </td>
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />

            </div>
            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                    onclick="okSet3Value()" style="width:80px">确认</a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                    onclick="quitSet3Value()" style="width:80px">取消</a>
            </div>
        </div>
        <script>
            $("#set3poseX").numberbox({
                width: 100,
                left: '10',
                label: 'X-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#set3poseY").numberbox({
                width: 100,
                left: '10',
                label: 'Y-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#set3poseZ").numberbox({
                width: 100,
                left: '10',
                label: 'Z-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#set3poseA").numberbox({
                width: 100,
                left: '10',
                label: 'A-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:deg)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });

            $("#read3Pose").linkbutton({
                iconCls: 'icon-reload',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_SAVEPOSE);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_SAVEPOSE.start(l_msg);
                }
            });

            $("#set3readMapList").linkbutton({
                iconCls: 'icon-reload',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_MAPLIST);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_MAPLIST.start(l_msg);
                }
            });
        </script>
    </div>

    <div id="setUpdateMap" class="easyui-window" title="更新地图参数"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:700px;height:295px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">

                <h3>设定位姿:&emsp;<div id="readPoseModel4"></div>
                </h3>
                <div id="setMode4poseX"></div>&emsp;&emsp;&emsp;<div id="setMode4poseY"></div>&emsp;&emsp;&emsp;<div
                    id="setMode4poseZ"></div>&emsp;&emsp;&emsp;<div id="setMode4poseA"></div>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>地图设置:&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&ensp;模式选择:
                </h3>
                <table width="650">
                    <tr>
                        <td width="61%">
                            <input id="mapNameListModel4" class="easyui-combobox" name="mapNameListModel4"
                                label="地图/分辨率:" editable="false" labelPosition="before" data-options="validType:['unnormal'],required:true,missingMessage:'不可为空',
                                        valueField:'id', textField:'text', panelHeight:'auto'">
                            <div id="setReadMapListModel4">获取地图列表</div>
                        </td>
                        <td width="10%"></td>
                        <td width="29%">
                            <select id="mapModel4" class="easyui-combobox" name="mapSize" label="场景模式:"
                                labelPosition="before" editable="false" style="width:100%;">
                                <option value="0">室内场景</option>
                                <option value="1">室外场景</option>
                                <option value="2">复杂场景</option>
                            </select>
                        </td>
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />

            </div>
            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                    onclick="okSetModel4Value()" style="width:80px">确认</a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                    onclick="quitSetModel4Value()" style="width:80px">取消</a>
            </div>
        </div>
        <script>
            $("#setMode4poseX").numberbox({
                width: 100,
                left: '10',
                label: 'X-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#setMode4poseY").numberbox({
                width: 100,
                left: '10',
                label: 'Y-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#setMode4poseZ").numberbox({
                width: 100,
                left: '10',
                label: 'Z-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:m)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });
            $("#setMode4poseA").numberbox({
                width: 100,
                left: '10',
                label: 'A-',
                labelWidth: 20,
                labelPosition: 'before',
                prompt: '(单位:deg)',
                required: true,
                precision: 3,
                missingMessage: "不可为空"
            });

            $("#readPoseModel4").linkbutton({
                iconCls: 'icon-reload',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_SAVEPOSE);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_SAVEPOSE.start(l_msg);
                }
            });

            $("#setReadMapListModel4").linkbutton({
                iconCls: 'icon-reload',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_MAPLIST);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_MAPLIST.start(l_msg);
                }
            });
        </script>
    </div>

    <div id="set4" class="easyui-window" title="雷达参数配置"
        data-options="resizable: false, closable:true, maximizable: false, minimizable: false, collapsible:false, closed: true, modal: true, border:'thin',cls:'c2'"
        style="width:850px;height:800px;padding:5px;overflow: hidden;">

        <div id="tabs4" class="easyui-tabs" style="width:100%;height:99%;padding:1px;overflow: hidden;">
        </div>

        <script>
            $(function () {
                $('#set4').window({
                    onBeforeClose: function () {
                        $('#set4').window('close', true);
                        IsSet4 = 0;
                    }
                });
            });

            //选择tabId时调用
            $('#tabs4').tabs({
                border: false,
                onSelect: function (title) {
                    // alert(title+' is selected');
                    sel_laserId = parseInt(title[5]);
                }
            });

            //关闭tabId时调用
            $('#tabs4').tabs({
                onBeforeClose: function (title, index) {
                    let cLidarModel = $("input[name='lidarModel']:checked").val();
                    let lModelValue = $("input[name='model']:checked").val();
                    if (cLidarModel == 0 || lModelValue != 0) {
                        alert("无效操作，删除雷达请切换至雷达模式-[在线模式] 工作模式-[空闲模式] ！");
                        return false;
                    }
                    c_deleteLaserTab = this;
                    c_deleteLaserIndex = index;
                    $.messager.confirm('确认框', '确认删除该雷达配置:  ' + title + ' ?', function (r) {
                        if (r) {
                            let l_msg = PROTArray.slice();
                            setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_DELETELASER);
                            strToAscii(title, l_msg, 26);
                            addTail_(l_msg);
                            c_master.MSG_SETCMD_DELETELASER.start(l_msg);
                        }
                    });
                    return false;	// prevent from closing
                }
            });

            // 添加一个新的标签页面板（tab panel）
            $('#tabs4').tabs({
                tools: [{
                    iconCls: 'icon-add',
                    async: false,
                    handler: function () {
                        var tabcount = $('#tabs4').tabs('tabs').length;
                        if (tabcount >= 2) {
                            alert('雷达数量超限，请先删除已有雷达');
                        }
                        else {
                            AddLidar();
                        }
                    }
                }
                ]
            });
        </script>
    </div>

    <div id="AddLidarTab" class="easyui-window" title="新增雷达配置"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c2'"
        style="width:750px;height:275px;padding:5px;">
        <h3>基础信息:&emsp;<div id="readBaseParam"></div>
        </h3>
        <div id="AddLidarName"></div>&ensp;&emsp;&emsp;&emsp;
        <select id="AddlidarType" class="easyui-combobox" panelHeight="auto" ,name="dept" label="雷达型号 :"
            labelPosition="before" editable="false" style="width:220px;height:30px;">
            <option>WLR720FCW</option>
            <option>WLR720F</option>
            <option>WLR720C</option>
            <option>WLR720A</option>
        </select>

        <hr style="height:1px;border:none;border-top:1px solid #555555;" />

        <h3>网络配置:&emsp;<div id="readLaserNetParam"></div>
        </h3>
        <div id="AddLidarIP"></div>&emsp;&emsp;&emsp;
        <div id="AddLidarPort"></div>&emsp;&emsp;&emsp;
        <div id="AddPcIP"></div>&emsp;&emsp;&emsp;
        <div id="AddPcPort"></div>&emsp;&emsp;&emsp;

        <hr style="height:1px;border:none;border-top:1px solid #555555;" />

        <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
            <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                onclick="okSetAddLidarTab()" style="width:80px">确认</a>
            <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                onclick="quitAddLidarTab()" style="width:80px">取消</a>
        </div>
        <script>
            $("#AddLidarName").textbox({
                width: '160px',
                height: 30,
                label: '雷达名称 :',
                labelWidth: 60,
                labelPosition: 'before',
                prompt: '名称-不可重复',
                required: true,
                missingMessage: "不可为空 eg: left"
            });
            $("#AddLidarIP").textbox({
                width: '160px',
                height: 30,
                label: '雷达IP :',
                labelWidth: 60,
                labelPosition: 'before',
                prompt: '网络地址',
                validType: ['ip'],
                required: true,
                missingMessage: "不可为空 eg:***********"
            });
            $("#AddLidarPort").numberbox({
                width: '140px',
                height: 30,
                label: '雷达端口 :',
                labelWidth: 70,
                labelPosition: 'before',
                prompt: 'eg:8080',
                required: true,
                precision: 0,
                min: 1000,
                max: 65535,
                missingMessage: "不可为空 1000-65535"
            });
            $("#AddPcIP").textbox({
                width: '160px',
                height: 30,
                label: 'PC IP :',
                labelWidth: 60,
                labelPosition: 'before',
                prompt: '网络地址',
                validType: ['ip'],
                required: true,
                missingMessage: "不可为空 eg:***********"
            });
            $("#AddPcPort").numberbox({
                width: '140px',
                height: 30,
                label: 'PC端口 :',
                labelWidth: 70,
                labelPosition: 'before',
                prompt: 'eg:8080',
                required: true,
                precision: 0,
                min: 1000,
                max: 65535,
                missingMessage: "不可为空 1000-65535"
            });
        </script>

    </div>

    <div id="set5" class="easyui-window" title="AGV参数配置"
        data-options="closable:true, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c2'"
        style="width:700px;height:510px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <h3>网络配置:&emsp;<div id="read5AGVInfo"></div>
                </h3>

                <table width="650">
                    <tr>
                        <td>
                            <div id="agvIP"></div>&emsp;&emsp;&emsp;
                        </td>
                        <td>
                            <div id="agvPort"></div>&emsp;&emsp;&emsp;
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="AGVNetNameList" class="easyui-combobox" name="AGVNetNameList" label="AGV网卡名:"
                                labelPosition="before" editable="false"
                                data-options="required:true, validType:['unnormal'],width:220,height:'30px',labelWidth:80,valueField:'id', textField:'text', panelHeight:'auto',  missingMessage:'网卡未设置'">
                        </td>
                        <td>
                            <input id="Pcap5NameList" class="easyui-combobox" name="Pcap5NameList" label="AGV数据包:"
                                editable="false" labelPosition="before"
                                data-options="validType:['unnormal'],width:'220px',height:'30px',labelWidth:80,valueField:'id', textField:'text', panelHeight:'auto'">
                        </td>
                    </tr>
                </table>

                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3 title="主雷达在车体坐标系中的位置">车体校正:&emsp;<div id="read4CarCorrect"></div>
                </h3>
                <div></div>&emsp;&emsp;&emsp;
                <div id="MLidarX" style="clear: both;"></div>&emsp;&emsp;
                <div id="MLidarY"></div>&emsp;&emsp;&ensp;
                <div id="MLidarZ"></div>&emsp;&emsp;&ensp;
                <div id="MLidarA"></div>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>地图校正:&emsp;<div id="read4MapCorrect"></div>
                </h3>
                <h4>雷达地图:</h4>
                <div></div>&emsp;&emsp;&emsp;
                <div id="mymapX"></div>&emsp;&emsp;&emsp;&emsp;
                <div id="mymapY"></div>&emsp;&emsp;&emsp;&emsp;&ensp;
                <div id="mymapA"></div>
                <h4>目标地图:</h4>
                <div></div>&emsp;&emsp;&emsp;
                <div id="tomapX" style="clear: both;"></div>&emsp;&emsp;&emsp;&emsp;
                <div id="tomapY"></div>&emsp;&emsp;&emsp;&emsp;&ensp;
                <div id="tomapA"></div>
            </div>

            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-read'" href="javascript:void(0)"
                    onclick="readAgvParam()" style="width:100px;">读取全部</a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-download'" href="javascript:void(0)"
                    onclick="okSet5Value()" style="width:100px">配置全部</a>
            </div>
        </div>
        <script>
            $("#agvIP").textbox({
                width: '220px',
                height: 30,
                label: 'AGV IP: ',
                labelWidth: 80,
                labelPosition: 'before',
                prompt: '网络地址',
                validType: ['ip'],
                required: true,
                missingMessage: "不可为空 eg:***********"
            });
            $("#agvPort").numberbox({
                width: '220px',
                height: 30,
                label: 'AGV 端口: ',
                labelWidth: 80,
                labelPosition: 'before',
                prompt: 'eg:8080',
                required: true,
                precision: 0,
                min: 1000,
                max: 65535,
                missingMessage: "不可为空 1000-65535"
            });
            $("#read5AGVInfo").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    readAGVNetParam();

                    UpdataPcapList();

                    readAGVPcapName();

                    readNetListfun();

                    readAgvNet();
                }
            });

            //AGV校正参数
            $("#MLidarX").numberbox({
                width: '110px',
                height: 30,
                label: 'X-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'X轴 单位:m',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",

            });
            $("#MLidarY").numberbox({
                width: '120px',
                height: 30,
                label: 'Y-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'Y轴 单位:m',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",

            });
            $("#MLidarZ").numberbox({
                width: '120px',
                height: 30,
                label: 'Z-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'Y轴 单位:m',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",

            });
            $("#MLidarA").numberbox({
                width: '120px',
                height: 30,
                label: 'A-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: '角度 单位:deg',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",
                min: -360,
                max: 360,
            });
            //地图校正参数
            $("#mymapX").numberbox({
                width: '110px',
                height: 30,
                label: 'X-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'X轴 单位:m',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",

            });
            $("#mymapY").numberbox({
                width: '120px',
                height: 30,
                label: 'Y-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'Y轴 单位:m',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",

            });
            $("#mymapA").numberbox({
                width: '120px',
                height: 30,
                label: 'A-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: '角度 单位:deg',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",
                min: -360,
                max: 360,
            });
            $("#tomapX").numberbox({
                width: '110px',
                height: 30,
                label: 'X-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'X轴 单位:m',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",

            });
            $("#tomapY").numberbox({
                width: '120px',
                height: 30,
                label: 'Y-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: 'Y轴 单位:m',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",

            });
            $("#tomapA").numberbox({
                width: '120px',
                height: 30,
                label: 'A-',
                labelWidth: 30,
                labelPosition: 'before',
                prompt: '角度 单位:deg',
                value: '0',
                required: true,
                precision: 3,
                missingMessage: "不可为空",
                min: -360,
                max: 360,
            });
            $("#read4MapCorrect").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_MAPCORRECT);
                    addTail_(l_msg);
                    c_slam.MSG_QUERYCMD_MAPCORRECT.start(l_msg);
                }
            });
            $("#read4CarCorrect").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_CARCORRECT);
                    addTail_(l_msg);
                    c_slam.MSG_QUERYCMD_CARCORRECT.start(l_msg);
                }
            });
        </script>
    </div>

    <div id="set9" class="easyui-window" title="系统参数配置"
        data-options="closable:true, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c2'"
        style="width:700px;height:403px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <h3>地图总览:&emsp;<div id="readMapInfo"></div>
                    &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;模式总览:&emsp;
                    <div id="read9Model"></div>
                </h3>
                <table width="650">
                    <tr>
                        <td width="60%">
                            <div id="map9Name"></div>&ensp;&emsp;
                            <div id="map9Size"></div>&ensp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
                        </td>
                        <td width="40%">
                            <input id="map9Model" class="easyui-combobox" name="map9Model" label="场景模式:"
                                editable="false" labelPosition="before"
                                data-options="width:'220px',labelWidth:60, prompt: '仅供查看，不可更改', readonly:true,valueField:'id', textField:'text', panelHeight:'auto'">
                        </td>
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>平面运动信息:&emsp;<div id="readMovingMode"></div>
                    &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;日志存储信息:&emsp;
                    <div id="readLogPath"></div>
                </h3>

                <table width="650">
                    <tr>
                        <td width="16%">是否强制平面 ：</td>
                        <td width="7%">
                            <input type="radio" name="isPlaneMove" value="1" disabled /><span
                                style="vertical-align:middle; font-size: small">是</span>
                        </td>
                        <td width="7%">
                            <input type="radio" name="isPlaneMove" value="0" checked disabled /><span
                                style="vertical-align:middle; font-size: small">否</span>
                        </td>
                        <td width="30%"></td>
                        <td width="40%">
                            <div id="logPath"></div>&ensp;&emsp;
                        </td>
                    </tr>
                    <tr>
                        <hr style="border:1px none #000" />
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>时钟源信息:&emsp;<div id="readTimeSource"></div>
                    &ensp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;可视化信息:&emsp;
                    <div id="readViewModel"></div>
                </h3>
                <table width="650">
                    <tr>
                        <td width="60%">
                            <select id="timeSource" class="easyui-combobox" name="timeSource" label=时钟源: labelWidth=60
                                labelPosition="before" editable="false" style="width:52%"
                                data-options="panelHeight:'auto', required:true">
                                <option value="0">系统时钟</option>
                                <option value="1">雷达时钟</option>
                            </select>
                        </td>
                        <!-- <td width="18%"></td> -->
                        <td width="40%">
                            <select id="viewModel" class="easyui-combobox" name="viewModel" label=点云模式: labelWidth=60
                                labelPosition="before" editable="false" style="width:220px;"
                                data-options="panelHeight:'auto', required:true">
                                <option value="0">三维点云</option>
                                <option value="1">二维点云</option>
                            </select>
                        </td>
                    </tr>
                </table>

                <hr style="height:1px;border:none;border-top:1px solid #555555;" />

            </div>
            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-read'" href="javascript:void(0)"
                    onclick="readSysParam()" style="width:100px;">读取全部</a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-download'" href="javascript:void(0)"
                    onclick="okSet9Value()" style="width:100px">配置全部</a>
            </div>
        </div>

        <script>
            $("#readMapInfo").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_SLAMMAPINFO);
                    addTail_(l_msg);
                    c_slam.MSG_QUERYCMD_SLAMMAPINFO.start(l_msg);
                }
            });
            $("#map9Name").textbox({
                width: "200px",
                precision: 3,
                left: '10',
                label: '当前地图:',
                labelWidth: 60,
                labelPosition: 'before',
                precision: 3,
                readonly: 'true',
                prompt: '仅供查看，不可更改',
            });
            $("#logPath").textbox({
                width: "220px",
                left: '10',
                label: '日志路径:',
                labelWidth: 60,
                labelPosition: 'before',
                required: true,
                prompt: '路径目录',
                missingMessage: "不可为空"
            });
            $("#map9Size").numberbox({
                width: "140px",
                precision: 3,
                left: '10',
                label: '分辨率:',
                labelWidth: 60,
                labelPosition: 'before',
                precision: 3,
                readonly: 'true',
                prompt: '不可更改',
            });
            // $("#map9Model").numberbox({
            //     width:"140px",   
            //     precision: 3,
            //     left: '10',
            //     label: '建图模式:', 
            //     labelWidth:60,
            //     labelPosition: 'before',
            //     precision: 3,
            //     readonly: 'true',
            //     prompt: '室内模式',
            // });
            $("#read9Model").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    //console.log("send checkSlamModel");
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_MAPPINGMODE);
                    addTail_(l_msg);
                    c_slam.MSG_QUERYCMD_MAPPINGMODE.start(l_msg);
                }
            });
            $("#readMovingMode").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    getMotionMode();
                }
            });
            $("#readLogPath").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    getLogPath();
                }
            });
            $("#readTimeSource").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    getTimeSource();
                }
            });
            $("#readViewModel").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    getViewModel();
                }
            });            
        </script>
    </div>

    <div id="set6" class="easyui-window" title="数据文件清理"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c2'"
        style="width:700px;height:360px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <h3>地图信息:&emsp;<div id="read6MapList"></div>
                </h3>

                <table width="650">
                    <tr>
                        <input id="map6NameList" class="easyui-combobox" name="map6NameList" label="地图列表:"
                            editable="false" labelPosition="before"
                            data-options="validType:['unnormal'],width:'220px',labelWidth:60,valueField:'id', textField:'text', panelHeight:'auto', required:true">
                        <div id="delete6Map">删除选中</div>
                    </tr>
                </table>

                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
                <h3>数据包信息:&emsp;<div id="read6OffLineData"></div>
                </h3>
                <table width="650">
                    <tr>
                        <hr style="border:1px none #000" />
                    </tr>
                    <tr>
                        <input id="pcap6NameList" class="easyui-combobox" name="pcap6NameList" label="Pcap列表:"
                            editable="false" labelPosition="before"
                            data-options="validType:['unnormal'],width:'220px',height:'30px',labelWidth:60,valueField:'id', textField:'text', panelHeight:'auto', required:true">
                        <div id="delete6Pcap">删除选中</div>
                    </tr>
                </table>
                <hr style="height:1px;border:none;border-top:1px solid #555555;" />
            </div>
            <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
                <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                    onclick="quitSet6Value()" style="width:80px">退出</a>
            </div>
        </div>
        <script>

            $("#delete6Map").linkbutton({
                iconCls: 'icon-delete',
                onClick: function () {
                    let lModelValue = $("input[name='model']:checked").val();
                    if (lModelValue != 0) {
                        $('#getStandbyWarn').dialog('open');
                        $('#getStandbyWarn').dialog('center');
                        return;
                    }
                    deleteMapFunc();
                    clearList("#map6NameList");
                }
            });
            $("#delete6Pcap").linkbutton({
                iconCls: 'icon-delete',
                onClick: function () {
                    let lModelValue = $("input[name='model']:checked").val();
                    if (lModelValue != 0) {
                        $('#getStandbyWarn').dialog('open');
                        $('#getStandbyWarn').dialog('center');
                        return;
                    }
                    deletePcapfun();
                    clearList("#pcap6NameList");
                }
            });
            $("#bag6Name").textbox({
                width: "200px",
                precision: 3,
                left: '10',
                label: '当前bag:',
                labelWidth: 70,
                labelPosition: 'before',
                precision: 3,
                readonly: 'true'
            });

            $("#read6MapList").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    //console.log("read6Pose");
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_MAPLIST);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_MAPLIST.start(l_msg);
                }
            });
            $("#read6OffLineData").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    clearList("#pcap6NameList");

                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_PCAPLIST);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_PCAPLIST.start(l_msg);
                }

            });

        </script>
    </div>

    <div id="netParamTab" class="easyui-window" title="网络参数配置"
        data-options="closable:true, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c2'"
        style="width:600px;height:263px;padding:5px;">
        <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="padding:10px;">
                <table width="550">
                    <tr>
                        <th></th>
                        <th>网卡名称</th>
                        <th>是否DHCP</th>
                        <th>是否联网</th>
                        <th>IP地址</th>
                    </tr>
                    <tr>
                        <th>0</th>
                        <td>
                            <div id="NetCardName0"></div>
                        </td>
                        <td>
                            <div id="IsDHCP0"></div>
                        </td>
                        <td>
                            <div id="IsConnectNet0">
                                <div>
                        </td>
                        <td>
                            <div id="IpAddress0"></div>
                        </td>
                        <th>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-reload'" href="javascript:void(0)"
                                onclick="updataAllNetParamValue(0)" style="width:40px"></a>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                                onclick="okSetAllNetParamValue(0)" style="width:40px"></a>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-restart'" href="javascript:void(0)"
                                onclick="ResetAllNetParamValue(0)" style="width:40px"></a>
                        </th>
                    </tr>
                    <tr>
                        <th>1</th>
                        <td>
                            <div id="NetCardName1"></div>
                        </td>
                        <td>
                            <div id="IsDHCP1"></div>
                        </td>
                        <td>
                            <div id="IsConnectNet1"></div>
                        </td>
                        <td>
                            <div id="IpAddress1"></div>
                        </td>
                        <th>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-reload'" href="javascript:void(0)"
                                onclick="updataAllNetParamValue(1)" style="width:40px"></a>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                                onclick="okSetAllNetParamValue(1)" style="width:40px"></a>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-restart'" href="javascript:void(0)"
                                onclick="ResetAllNetParamValue(1)" style="width:40px"></a>
                        </th>
                    </tr>
                    <tr>
                        <th>2</th>
                        <td>
                            <div id="NetCardName2"></div>
                        </td>
                        <td>
                            <div id="IsDHCP2"></div>
                        </td>
                        <td>
                            <div id="IsConnectNet2"></div>
                        </td>
                        <td>
                            <div id="IpAddress2"></div>
                        </td>
                        <th>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-reload'" href="javascript:void(0)"
                                onclick="updataAllNetParamValue(2)" style="width:40px"></a>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                                onclick="okSetAllNetParamValue(2)" style="width:40px"></a>
                            <a class="easyui-linkbutton" data-options="iconCls:'icon-restart'" href="javascript:void(0)"
                                onclick="ResetAllNetParamValue(2)" style="width:40px"></a>
                        </th>
                    </tr>
                    <tr>
                        <th>3</th>
                        <td>
                            <div id="NetCardName3"></div>
                        </td>
                        <td>
                            <tdivd id="IsDHCP3">
            </div>
            </td>
            <td>
                <div id="IsConnectNet3"></div>
            </td>
            <td>
                <div id="IpAddress3"></div>
            </td>
            <th>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-reload'" href="javascript:void(0)"
                    onclick="updataAllNetParamValue(3)" style="width:40px"></a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                    onclick="okSetAllNetParamValue(3)" style="width:40px"></a>
                <a class="easyui-linkbutton" data-options="iconCls:'icon-restart'" href="javascript:void(0)"
                    onclick="ResetAllNetParamValue(3)" style="width:40px"></a>
            </th>
            </tr>
            </table>
        </div>

        <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
            <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                onclick="quitAllNetParamTab()" style="width:80px">退出</a>
        </div>
    </div>
    <script>
        $("#NetCardName0").textbox({
            width: '100px',
            readonly: true,
            height: 30,
            value: '---',
            required: true,
            missingMessage: "不可为空",
        });
        $("#IsDHCP0").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {
                    setBtnEnable("#IsConnectNet0", true);
                    setTextReadOnly("#IpAddress0",false);
                }
                else {
                    setBtnValue("#IsConnectNet0", false);
                    setBtnEnable("#IsConnectNet0", false);
                    setTextReadOnly("#IpAddress0",true);
                }
            }
        });
        $("#IsConnectNet0").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {

                }
                else {

                }
            }
        });
        $("#IpAddress0").textbox({
            width: '100px',
            height: 30,
            prompt: '网络地址',
            validType: ['ip'],
            missingMessage: "不可为空",
        });

        $("#NetCardName1").textbox({
            width: '100px',
            readonly: true,
            height: 30,
            value: '---',
            required: true,
            missingMessage: "不可为空",
        });
        $("#IsDHCP1").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {
                    setBtnEnable("#IsConnectNet1", true);
                    setTextReadOnly("#IpAddress1",false);
                }
                else {
                    setBtnValue("#IsConnectNet1", false);
                    setBtnEnable("#IsConnectNet1", false);
                    setTextReadOnly("#IpAddress1",true);
                }
            }
        });
        $("#IsConnectNet1").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {

                }
                else {

                }
            }
        });
        $("#IpAddress1").textbox({
            width: '100px',
            height: 30,
            prompt: '网络地址',
            validType: ['ip'],
            missingMessage: "不可为空",
        });

        $("#NetCardName2").textbox({
            width: '100px',
            readonly: true,
            height: 30,
            value: '---',
            required: true,
            missingMessage: "不可为空",
        });
        $("#IsDHCP2").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {
                    setBtnEnable("#IsConnectNet2", true);
                    setTextReadOnly("#IpAddress2",false);
                }
                else {
                    setBtnValue("#IsConnectNet2", false);
                    setBtnEnable("#IsConnectNet2", false);
                    setTextReadOnly("#IpAddress2",true);
                }
            }
        });
        $("#IsConnectNet2").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {

                }
                else {

                }
            }
        });
        $("#IpAddress2").textbox({
            width: '100px',
            height: 30,
            prompt: '网络地址',
            validType: ['ip'],
            missingMessage: "不可为空",
        });

        $("#NetCardName3").textbox({
            width: '100px',
            readonly: true,
            height: 30,
            value: '---',
            required: true,
            missingMessage: "不可为空",
        });
        $("#IsDHCP3").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {
                    setBtnEnable("#IsConnectNet3", true);
                    setTextReadOnly("#IpAddress3",false);
                }
                else {
                    setBtnValue("#IsConnectNet3", false);
                    setBtnEnable("#IsConnectNet3", false);
                    setTextReadOnly("#IpAddress3",true);
                }
            }
        });
        $("#IsConnectNet3").switchbutton({
            onText: "是",
            offText: "否",
            checked: false,
            onChange: function (checked) {
                if (!checked) {

                }
                else {

                }
            }
        });
        $("#IpAddress3").textbox({
            width: '100px',
            height: 30,
            prompt: '网络地址',
            validType: ['ip'],
            missingMessage: "不可为空",
        });

    </script>
    </div>

    <div id="mac" class="easyui-window" title="设备MAC解密"
        data-options="closable:true, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:400px;height:380px;padding:0px;">
        <!-- <hr style="height:1px;border:none;border-top:1px solid #555555;" /> -->
        <hr style="border:1px none #000" />
        <div id="macImg" style="width:200px; height:200px; padding:0px; margin-left:100px;"></div>
        <h3 align="center">扫描二维码发送万集工作人员，获取密钥</h3>
        <div id="macCode" style="padding:0px; margin:50px"></div>
        <h2 align="center"></h2>
        <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)" onclick="sendMacCode()"
            style="margin-left:150px;width:100px">执行解密</a>
        <script>
            $("#macCode").textbox({
                width: '350px',
                left: '20',
                label: '   ',
                labelWidth: 50,
                labelPosition: 'before',
                prompt: '                      在此输入万集密钥，并执行解密',
                validType: ['unnormal'],
                required: true,
                missingMessage: "不可为空"
            });

            function sendMacCode() {
                if ($("#macCode").textbox("isValid")) {
                    let lsecretkey = $("#macCode").textbox("getValue").toString();
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SECRETKEY);
                    strToAscii(lsecretkey, l_msg, 26)
                    addTail_(l_msg);
                    c_master.MSG_SETCMD_SECRETKEY.start(l_msg);
                }
                else {
                    alert("请检查格式，确认必选项已填");
                }
            };
        </script>
    </div>

    <div id="set7" class="easyui-window" title="数据包参数"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:700px;height:195px;padding:5px;">
        <h3>PCAP列表:&emsp;<div id="read7Pcap"></div>
        </h3>
        <table width="650">
            <tr>
                <!-- <input id="pcap7NameList" class="easyui-combobox"  name="pcap7NameList" label="播放PCAP:"    labelPosition="before" 
                data-options="validType:['unnormal'],required:true,missingMessage:'不可为空',
                valueField:'id', textField:'text', panelHeight:'auto'" > -->

                <input id="pcap7NameList" class="easyui-combobox" name="pcap7NameList" label="Pcap列表:" editable="false"
                    labelPosition="before"
                    data-options="validType:['unnormal'],width:'200px',height:'30px',labelWidth:60,valueField:'id', textField:'text', panelHeight:'auto'">
            </tr>
        </table>
        <hr style="height:1px;border:none;border-top:1px solid #555555;" />
        <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
            <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                onclick="okSet7Value()" style="width:80px">确认</a>
            <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                onclick="quitSet7Value()" style="width:80px">退出</a>
        </div>
        <script>
            $("#read7Pcap").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    // clearList("#pcap7NameList");
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_PCAPLIST);
                    addTail_(l_msg);
                    c_master.MSG_QUERYCMD_PCAPLIST.start(l_msg);
                }
            });
        </script>
    </div>

    <div id="set8" class="easyui-window" title="数据包参数"
        data-options="closable:false, maximizable: false, minimizable: false,collapsible:false,closed: true,modal: true, border:'thin',cls:'c6'"
        style="width:700px;height:195px;padding:5px;">
        <h3>BAG列表:&emsp;<div id="read8BagList"></div>
        </h3>
        <table width="650">
            <tr>
                <input id="bag8NameList" class="easyui-combobox" name="bag8NameList" label="播放BAG:" editable="false"
                    labelPosition="before" data-options="validType:['unnormal'],required:true,missingMessage:'不可为空',
                valueField:'id', textField:'text', panelHeight:'auto'">
            </tr>
        </table>
        <hr style="height:1px;border:none;border-top:1px solid #555555;" />
        <div data-options="region:'south',border:false" style="text-align:right;padding:5px 0 0;">
            <a class="easyui-linkbutton" data-options="iconCls:'icon-ok'" href="javascript:void(0)"
                onclick="okSet8Value()" style="width:80px">确认</a>
            <a class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" href="javascript:void(0)"
                onclick="quitSet8Value()" style="width:80px">退出</a>
        </div>
        <script>
            $("#read8BagList").linkbutton({
                iconCls: 'icon-reload',
                width: '5%',
                height: '70%',
                onClick: function () {
                    clearList("#bag8NameList");

                }
            });
        </script>
    </div>

    <div id="offLineLidarModelWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'确认',
					iconCls:'icon-ok',
					handler:function(){
                        $('#offLineLidarModelWarn').dialog('close');
                        $('#set7').dialog('close');
                        $('#set8').dialog('close');
					}
                },
                {
                    text:'取消',
                    iconCls:'icon-cancel',
					handler:function(){
                        $('#offLineLidarModelWarn').dialog('close');
					}
				}]
            ">
        当前模式为离线雷达模式，离线雷达数据包未选择，请确认是否关闭离线数据包窗口！
    </div>

    <div id="OffLineSuccWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
					text:'确认',
					iconCls:'icon-ok',
					handler:function(){
                        $('#OffLineSuccWarn').dialog('close');
					}
                }]
            ">
        离线模式数据包设置完成，请重启程序！
    </div>

</body>


<script>
    var c_deleteLaserTab, c_deleteLaserIndex;
    var l_SlamStateOnce = 0;
    var c_DeviceName = "index";
    var g_LaserId = 0;
    var sel_laserId = 0;
    var LaserUrl = "Laser.html?g_LaserId=" + 0;
    var tabOnce = 0;
    var IsSet4 = 0;
    var IsNorth = 0;
    var cViewLidarPose0 = false;
    var cViewLidarPose1 = false;
    var cViewLidarPose2 = false;
    var cViewLidarPose3 = false;
    var t_readLidarCalibrationResult;
    
    var l_ListNetName = [];
    var c_ListCalibLidarName = [];
    function _sPose() {
        this.PoseX = 0.0;
        this.PoseY = 0.0;
        this.PoseZ = 0.0;
        this.PoseA = 0.0;
    }
    var rvizViewer, cInterval, cTimeOut, objCenter;
    var cLidarModelChange = 0;
    var cLastModel = 0;
    var cLastLidarModel = 0;
    var cViewPoseStatus = false;
    var c_GetPose = true;
    var lastPose = new _sPose();
    var cViewMapStatus = false;
    var cViewLidarStatus = false;
    var cPclModelStatus = false;
    var cPlayPcapStatus = false;
    var cRecordLidarStatus = false;
    // var ros = new ROSLIB.Ros();
    // var ipRosServer = 'localhost';
    // //var ipRosServer = '***********';
    // var addrRosServer = 'ws://'+ipRosServer+':9090';
    // function connectRos(){
    //     ros.connect(addrRosServer);
    // };
    // function closeRos(){
    //     ros.close();
    // };

    //ROS连接状态回调函数
    ros.on('error', function (error) {
        c_slam.stopAllSend();
        c_master.stopAllSend();
        c_device.stopAllSend();
        c_param.stopAllSend();

        //console.log("ROS Connect Error");
        setTimeout(function () { setBtnValue("#openWeb", false); }, 1000);
        setBtnValue("#openWeb", false);
        alert('服务器连接失败，请确认程序启动');
    });

    ros.on('connection', function () {
        //console.log('ROS Connection !');

        //查询是否录包
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_RECORDDATA);
        addTail_(l_msg);
        c_master.MSG_QUERYCMD_RECORDDATA.start(l_msg);

        //查询Master主程序是否启动
        l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_MASTERSTATUS);
        addTail_(l_msg);
        c_master.MSG_QUERYCMD_MASTERSTATUS.start(l_msg);

        //查询Mac
        l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_SECRETKEY);
        addTail_(l_msg);
        c_master.MSG_QUERYCMD_SECRETKEY.start(l_msg);

        //启动协议订阅+临时Topic订阅
        subNetData_();


        rvizViewer.start();

    });

    ros.on('close', function () {
        c_slam.stopAllSend();
        c_master.stopAllSend();
        c_device.stopAllSend();
        c_param.stopAllSend();

        //console.log("ROS Close");
        //顶部栏位姿状态显示关闭
        // viewPoseStatus((0));
        //关闭启动web按钮
        setBtnValue("#openWeb", false);
        alert('服务器已断开');
    });

    function colorZ(z) {
        if (z < 2) {
            r = 0;
            g = (255 * (z / 2));
            b = (255 * (1 - (z / 2)));
        }
        else {
            r = (255 * (z - 2) / 2);
            g = (255 * (1 - (z - 2) / 2));
            b = 0;
        }
        return new THREE.Color(r, g, b);
    }

    //可视化初始化
    function initRviz() {
        objCenter = document.getElementById("center");
        //console.log('initRviz');
        rvizViewer = new ROS3D.Viewer({
            divID: 'rvizViewer',
            width: objCenter.clientWidth - 10,
            height: objCenter.clientHeight - 10,
            antialias: true,
            background: '#000000'
        });
        //rviz.addObject(new ROS3D.Grid());
        var tfClient = new ROSLIB.TFClient({
            ros: ros,
            angularThres: 0.01,
            transThres: 0.01,
            rate: 10.0,
            fixedFrame: 'world'
        });

        //雷达
        var rvizCloudClient = new ROS3D.PointCloud2({
            ros: ros,
            tfClient: tfClient,
            rootObject: rvizViewer.scene,
            topic: '/wanji_lidar',
            max_pts: 200000,
            pointRatio: 4,
            messageRatio: 5,
            // material: { size: 7, sizeAttenuation: false, alphaTest: 0.5, transparent: true, map: texture },
            material: { size: 3, sizeAttenuation: false },
            // colorsrc: 'i', colormap: function(i) { return new THREE.Color(3*i,0,1-3*i); }
            colorsrc: 'z', colormap: function (z) {
                rgbz = z / 2;
                if (z > 0) rgb = 0x000000 + (rgbz | 0xff) << 8 + (1 - rgbz) | 0xff;
                else rgb = (rgbz | 0xff) << 16 + ((1 - rgbz) | 0xff) << 8 + 0x000000;
                return new THREE.Color(rgb);
            }
        });

        //地图
        var cloudClient1 = new ROS3D.PointCloud2({
            ros: ros,
            tfClient: tfClient,
            rootObject: rvizViewer.scene,
            topic: '/wanji_map',
            max_pts: 5000000,
            pointRatio: 4,
            messageRatio: 5,
            material: { size: 0.2, color: 0x7CFC00 }
        });

        var pose = new ROS3D.Pose({
            ros: ros,
            tfClient: tfClient,
            rootObject: rvizViewer.scene,
            topic: '/wanji_pose',
            keep: 1,                //要保留的标记数 默认值:1
            color: 0xAE0000,         //线条颜色（默认值：0xcc00ff）
            length: 3.0,             //箭头的长度（默认值：1.0）
            headLength: 0.5,         //箭头的头部长度 (默认: 0.2)
            shaftDiameter: 0.05,     // 箭头的轴直径
            headDiameter: 0.1,       //-箭头的头部直径（默认值：0.1）
        });

        var poseAGV = new ROS3D.Pose({
            ros: ros,
            tfClient: tfClient,
            rootObject: rvizViewer.scene,
            topic: '/wanji_poseOut',
            keep: 1,                //要保留的标记数 默认值:1
            color: 0x7FFF00,         //线条颜色（默认值：0xcc00ff）
            length: 2.0,             //箭头的长度（默认值：1.0）
            headLength: 0.5,         //箭头的头部长度 (默认: 0.2)
            shaftDiameter: 0.2,     // 箭头的轴直径
            headDiameter: 0.4,       //-箭头的头部直径（默认值：0.1）
        });

        //显示来自其他处的Mark并处理
        var imClient = new ROS3D.InteractiveMarkerClient({
            ros: ros,
            tfClient: tfClient,
            topic: '/UI',
            camera: rvizViewer.camera,
            rootObject: rvizViewer.selectableObjects
        });

    };
    function subCloud() { }

    // var pubNetTopic = new ROSLIB.Topic({
    //     ros: ros,
    //     name: '/web_from',
    //     messageType: 'std_msgs/Int32MultiArray'
    // });

    // // Subscribing to a Topic
    // var subTopic = new ROSLIB.Topic({
    //     ros: ros,
    //     name: '/web_to',
    //     messageType: 'std_msgs/Int32MultiArray'
    // });


    // Subscribing to a Topic
    var subPoseTopic = new ROSLIB.Topic({
        ros: ros,
        name: '/wanji_pose',
        queue_length: 1,
        messageType: 'geometry_msgs/PoseStamped'
    });

    // Subscribing to a Topic
    var subPoseAGVTopic = new ROSLIB.Topic({
        ros: ros,
        name: '/wanji_poseOut',
        queue_length: 1,
        messageType: 'geometry_msgs/PoseStamped'
    });

    var qrcode = new QRCode(document.getElementById("macImg"), {
        text: "read mac fail",
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H
    });



</script>

</html>