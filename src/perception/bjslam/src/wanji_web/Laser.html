<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Insert title here</title>
    <link rel="stylesheet" type="text/css" href="./resources/easyui/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="./resources/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="./resources/easyui/themes/color.css">
    <script type="text/javascript" src="./resources/easyui/jquery.min.js"></script>
    <script type="text/javascript" src="./resources/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="./resources/ros/three.js"></script>
    <script type="text/javascript" src="./resources/ros/eventemitter2.min.js"></script>
    <script type="text/javascript" src="./resources/ros/roslib.js"></script>
    <script type="text/javascript" src="./resources/ros/ros3d.js"></script>
    <script type="text/javascript" src="./resources/protocol.js"></script>
    <script type="text/javascript" src="./resources/Laser.js"></script>

</head>

<body>
    <div class="easyui-layout" data-options="fit:true">
        <div data-options="region:'center'" style="padding:1px;">
            <h3>基础信息:&emsp;<div id="readLidarBaseParam"></div>
            </h3>
            <div id="lidarName"></div>&ensp;&emsp;&emsp;&emsp;
            <div id="lidarSN"></div>&ensp;&emsp;&emsp;&emsp;

            <select id="lidarType" class="easyui-combobox" panelHeight="auto" ,name="dept" label="雷达型号-"
                labelPosition="before" editable="false"
                data-options="validType:['unnormal'],width:200,height:'4%',labelWidth:80,valueField:'id', textField:'text', panelHeight:'auto'">
                <option>WLR720FCW</option>
                <option>WLR720F</option>
                <!-- <option>WLR720C</option>
                <option>WLR720A</option> -->
            </select>
            &emsp;&emsp;&emsp;
            <td width="16%">雷达使能:</td>
            <td width="7%">
                <input type="radio" name="EnableLidar" value="1" /><span
                    style="vertical-align:middle; font-size: small">是</span>
            </td>
            <td width="7%">
                <input type="radio" name="EnableLidar" value="0" checked /><span
                    style="vertical-align:middle; font-size: small">否</span>
            </td>

            <hr style="height:1px;border:none;border-top:1px solid #555555;" />

            <h3>
                网络配置:&emsp;<div id="readLidarNetParam">
            </h3>

            <table width="550">
                <tr>
                    <div id="lidarIP"></div>&emsp;&emsp;&emsp;
                    <div id="lidarPort"></div>&emsp;&emsp;&emsp;
                    <div id="PcIP"></div>&emsp;&emsp;&emsp;
                    <div id="PcPort"></div>
                </tr>
            </table>
            <h3></h3>
            <table width="550">
                <tr>
                    <input id="LidarNetNameList" class="easyui-combobox" name="LidarNetNameList" label="雷达网卡名:"
                        labelPosition="before" editable="false"
                        data-options="required:true, validType:['unnormal'],width:220,height:'4%',labelWidth:80,valueField:'id', textField:'text', panelHeight:'auto',  missingMessage:'网卡未设置'">
                </tr>
                <tr>&emsp;</tr>
                <tr>
                    <input id="LidarPcapNameList" class="easyui-combobox" name="LidarPcapNameList" label="雷达数据包:"
                        labelPosition="before" editable="false"
                        data-options="validType:['unnormal'],width:220,height:'4%',labelWidth:80,valueField:'id', textField:'text', panelHeight:'auto'">
                </tr>
                <tr>&emsp;</tr>
                <tr>
                    <div id="lidarStatus"></div>
                </tr>
            </table>

            <hr style="height:1px;border:none;border-top:1px solid #555555;" />
            <!-- <div id="content" style="background-color:#EEEEEE;height:150px;width:150px;float:right;">
                <img src="img/pic720.png" alt="Smiley face" style="right: auto;" width="150" height="150">
            </div> -->

            <h3>倾斜纠正:&emsp;<div id="readLidarDip"></div>
            </h3>
            <div id="lidarDipAngleR"></div>&ensp;&emsp;&emsp;
            <div id="lidarDipAngleP"></div>&ensp;&emsp;
            <h3></h3>
            <div id="HorizontalAlignTimes"> </div>&ensp;&emsp;&emsp;
            <td>地平校准: &ensp;</td>
            <a class="easyui-linkbutton" id="openHorizontalBtn" data-options="iconCls:'icon-start'"
                href="javascript:void(0)" onclick="openHorizontalAlign()">启动</a>&ensp;&emsp;
            <a class="easyui-linkbutton" id="closeHorizontalBtn" data-options="iconCls:'icon-stop'"
                href="javascript:void(0)" onclick="closeHorizontalAlign()">关闭</a>&ensp;&emsp;
            <a class="easyui-linkbutton" id="applyHorizontalBtn" data-options="iconCls:'icon-ok'"
                href="javascript:void(0)" onclick="applyHorizontalAlignResult()">应用</a>&ensp;&emsp;
            <h3></h3>
            <div id="horizontalState"></div>
            <hr style="height:1px;border:none;border-top:1px solid #555555;" />
            <h3>标定外参:&emsp;<div id="readLidarCalib"></div>
            </h3>

            <div id="lidarX"></div>&ensp;&emsp;&emsp;
            <div id="lidarY"></div>&ensp;&emsp;
            <div id="lidarZ"></div>
            <h3></h3>
            <div id="lidarAngleR"></div>&ensp;&emsp;&emsp;
            <div id="lidarAngleP"></div>&ensp;&emsp;
            <div id="lidarAngleY"></div>
            <h3></h3>
            <td width="16%">基准雷达:</td>
            <td width="7%">
                <input type="radio" name="isBaseLaser" value="1" /><span
                    style="vertical-align:middle; font-size: small">是</span>
            </td>
            <td width="7%">
                <input type="radio" name="isBaseLaser" value="0" checked /><span
                    style="vertical-align:middle; font-size: small">否</span>
            </td>&ensp;&emsp;&emsp;&ensp;&emsp;&ensp;
            <h3></h3>
            <hr style="height:1px;border:none;border-top:1px solid #555555;" />
            <h3>距离盲区:&emsp;<div id="readBlindDistance"></div>
            </h3>
            <div id="blindDistanceMin"></div>&ensp;&emsp;&emsp;
            <div id="blindDistanceMax"></div>&emsp;&emsp;
            <div id="lidarHeight"></div>
            <h3>角度盲区:&emsp;<div id="readBlindAngle"></div>
            </h3>
            <div id="blindAngle11"></div>&emsp;&emsp;&emsp;
            <div id="blindAngle12"></div>&emsp;&emsp;&emsp;
            <div id="blindAngle21"></div>&emsp;&emsp;&emsp;
            <div id="blindAngle22"></div>
            <hr style="border:1px none #000" />
            <div id="blindAngle31"></div>&emsp;&emsp;&emsp;
            <div id="blindAngle32"></div>&emsp;&emsp;&emsp;
            <div id="blindAngle41"></div>&emsp;&emsp;&emsp;
            <div id="blindAngle42"></div>
            <hr style="height:1px;border:none;border-top:1px solid #555555;" />

            <h3>靶标信息:&emsp;<div id="readMark"></div>
                &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;地面信息:&emsp;
                <div id="readGround"></div>
            </h3>

            <td width="100">是否使用靶标 ：</td>
            <input type="radio" name="isUseMark" value="1">是</input>
            <input type="radio" name="isUseMark" value="0" checked="true">否</input>&emsp;&emsp;&emsp;

            <div id="markSize"></div>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;

            <td width="100">是否使用地面 ：</td>
            <input type="radio" name="isUseGround" value="1">是</input>
            <input type="radio" name="isUseGround" value="0" checked="true">否</input>
        </div>
        <div data-options="region:'south',border:false" style="height:32px;text-align:right;">
            <a class="easyui-linkbutton" data-options="iconCls:'icon-read'" href="javascript:void(0)"
                onclick="ReadLaserParam()" style="width:120px;">读取全部</a>&emsp;&emsp;
            <a class="easyui-linkbutton" data-options="iconCls:'icon-download'" href="javascript:void(0)"
                onclick="SetLaserParam()" style="width:120px;">配置全部</a>
        </div>
    </div>
    <script>

        function getUrlParam(id) {
            var regExp = new RegExp('([?]|&)' + id + '=([^&]*)(&|$)');
            var result = window.location.href.match(regExp);
            if (result) {
                return decodeURIComponent(result[2]);
            } else {
                return null;
            }

        }
        //得到url参数  
        var c_DeviceName = getUrlParam('g_LaserId');
        $("#lidarAngleR").numberbox({
            width: '150px',
            height: '4%',
            label: '雷达Roll-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '角度(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: -180,
            max: 180,
        });
        $("#lidarAngleP").numberbox({
            width: '150px',
            height: '4%',
            label: '雷达Pitch-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '角度(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: -180,
            max: 180,
        });
        $("#lidarAngleY").numberbox({
            width: '150px',
            height: '4%',
            label: '雷达Yaw-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '角度(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: -180,
            max: 180,
        });
        $("#HorizontalAlignTimes").numberbox({
            width: '150px',
            height: '4%',
            label: '校平次数:',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '(单位:次数)',
            value: '20',
            required: true,
            precision: 0,
            missingMessage: "不可为空",
            min: 1,
            max: 99,
        });
        $("#lidarDipAngleR").numberbox({
            width: '150px',
            height: '4%',
            label: '补偿Roll-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '角度(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: -180,
            max: 180,
        });
        $("#lidarDipAngleP").numberbox({
            width: '150px',
            height: '4%',
            label: '补偿Pitch-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '角度(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: -180,
            max: 180,
        });
        $("#horizontalState").textbox({
            width: '330px',
            height: '4%',
            label: '校准状态:',
            labelWidth: 70,
            labelPosition: 'before',
            value: '未启动',
            required: true,
            readonly: true,
        });
        $("#calibrationState").textbox({
            width: '330px',
            height: '4%',
            label: '标定状态:',
            labelWidth: 70,
            labelPosition: 'before',
            value: '未启动',
            required: true,
            readonly: true,
        });
        $("#lidarSN").textbox({
            width: '200px',
            height: '4%',
            label: '雷达SN码 -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '-见铭牌-',
            // value: '0',
            required: true,
            missingMessage: "不可为空",
            readonly: true,
        });
        $("#lidarName").textbox({
            width: '150px',
            height: '4%',
            label: '雷达名称 -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: 'eg:left/right',
            required: true,
            missingMessage: "字符长度<=9",
            readonly: true,
        });
        $("#lidarIP").textbox({
            width: '160px',
            height: '4%',
            label: '雷达 IP -',
            labelWidth: 60,
            labelPosition: 'before',
            prompt: '网络地址',
            validType: ['ip'],
            required: true,
            missingMessage: "不可为空 eg:***********"
        });
        $("#lidarStatus").textbox({
            width: '200px',
            height: '4%',
            label: '雷达状态 -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '请刷新',
            required: false,
            readonly: true,
        });
        $("#lidarPort").numberbox({
            width: '140px',
            height: '4%',
            label: '雷达端口 -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: 'eg:8080',
            required: true,
            precision: 0,
            min: 1000,
            max: 65535,
            missingMessage: "不可为空 1000-65535"
        });
        $("#PcIP").textbox({
            width: '160px',
            height: '4%',
            label: 'PC IP -',
            labelWidth: 60,
            labelPosition: 'before',
            prompt: '网络地址',
            validType: ['ip'],
            required: true,
            missingMessage: "不可为空 eg:***********"
        });
        $("#PcPort").numberbox({
            width: '140px',
            height: '4%',
            label: 'PC 端口 -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: 'eg:8080',
            required: true,
            precision: 0,
            min: 1000,
            max: 65535,
            missingMessage: "不可为空 1000-65535"
        });
        $("#lidarX").numberbox({
            width: '150px',
            height: '4%',
            label: '雷达X -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '( 单位:m )',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
        });
        $("#lidarY").numberbox({
            width: '150px',
            height: '4%',
            label: '雷达Y -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '( 单位:m )',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
        });
        $("#lidarZ").numberbox({
            width: '150px',
            height: '4%',
            label: '雷达Z -',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '( 单位:m )',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
        });
        $("#blindDistanceMin").numberbox({
            width: '150px',
            height: '4%',
            label: ' 盲区min-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '(单位:m)',
            value: '1.5',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
        });
        $("#blindDistanceMax").numberbox({
            width: '150px',
            height: '4%',
            label: ' 盲区max-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '(单位:m)',
            value: '70.0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
        });
        $("#lidarHeight").numberbox({
            width: '150px',
            height: '4%',
            label: '雷达H-',
            labelWidth: 60,
            labelPosition: 'before',
            prompt: '( 单位:m )',
            value: '2.0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
        });
        $("#blindAngle11").numberbox({
            width: '150px',
            height: '4%',
            label: ' 盲区1起点-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#blindAngle12").numberbox({
            width: '130px',
            height: '4%',
            label: ' 终点-',
            labelWidth: 40,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#blindAngle21").numberbox({
            width: '150px',
            height: '4%',
            label: '盲区2起点-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#blindAngle22").numberbox({
            width: '130px',
            height: '4%',
            label: ' 终点-',
            labelWidth: 40,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#blindAngle31").numberbox({
            width: '150px',
            height: '4%',
            label: '盲区3起点-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#blindAngle32").numberbox({
            width: '130px',
            height: '4%',
            label: ' 终点-',
            labelWidth: 40,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#blindAngle41").numberbox({
            width: '150px',
            height: '4%',
            label: '盲区4起点-',
            labelWidth: 70,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#blindAngle42").numberbox({
            width: '130px',
            height: '4%',
            label: ' 终点-',
            labelWidth: 40,
            labelPosition: 'before',
            prompt: '(单位:deg)',
            value: '0',
            required: true,
            precision: 3,
            missingMessage: "不可为空",
            min: 0,
            max: 360,
        });
        $("#readLidarBaseParam").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                readLidarBaseParamfun();
            }
        });
        $("#readLidarNetParam").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                readLidarNetParamfun();
                readPcapListfun();
                readNetListfun();
                sleep(100);
                readLidarPcap();
                readLidarNet();
                readLidarStatus();
                readLidarIsBase();
            }
        });
        $("#readLidarCalib").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                //查询雷达安装位姿
                readLidarInstallXYZfun(2);
                readLidarInstallANGfun(2);
                // 查询基准雷达
                readLidarIsBase();
            }
        });
        $("#readLidarDip").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                //查询雷达角度
                readLidarInstallANGfun(1);
                //查询校准次数
                readLidarHorizonCalibNum();
                // 查询校准状态

            }
        });
        $("#readBlindDistance").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                readDisBlindfun();
                readLidarHightfun();
            }
        });
        $("#readBlindAngle").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                readBlindAnglefun();
            }
        });
        $("#markSize").numberbox({
            width: '150px',
            height: '4%',
            precision: 3,
            left: '10',
            label: '靶标半径:',
            labelWidth: 60,
            labelPosition: 'before',
            prompt: '半径(单位:m)',
            value: '0.00',
            precision: 3,
            // readonly: 'true'
        });
        $("#readMark").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                readMarkfun();
            }
        });
        $("#readGround").linkbutton({
            iconCls: 'icon-reload',
            width: '5%',
            height: '70%',
            onClick: function () {
                readGround();
            }
        });

        $("#lidarName").textbox("setValue", c_DeviceName);

        window.onload = function disableAllLaser() {
            connectRos();
            //启动协议订阅+临时Topic订阅
            subNetData_();
        }
    </script>
    <!---提示保存参数警告--->
    <div id="saveParamWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
            closed: true,
            iconCls: 'icon-tip',
            buttons: [
            {
                text:'是',
                iconCls:'icon-ok',
                handler:function(){
                    $('#saveParamWarn').dialog('close');
                    sendSaveParamCMD();
                }
            },
            {
                text:'否',
                iconCls:'icon-cancel',
                handler:function(){
                    $('#saveParamWarn').dialog('close');
                }
            }]
        ">
        当前操作须保存参数，是否保存参数，请确认。
    </div>
    <!---提示重启SLAM警告--->
    <div id="resetSLAMWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
                    text:'是',
                    iconCls:'icon-ok',
                    handler:function(){
                        $('#resetSLAMWarn').dialog('close');
                        sendResetSLAMCMD();
                    }
                },
                {
                    text:'否',
                    iconCls:'icon-cancel',
                    handler:function(){
                        $('#resetSLAMWarn').dialog('close');
                    }
                }]
            ">
        当前操作重启SLAM生效，是否重启SLAM，请确认。
    </div>
    <!---提示保存参数并重启SLAM警告--->
    <div id="saveParamResetSLAMWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:130px;padding:10px" data-options="
                closed: true,
                iconCls: 'icon-tip',
                buttons: [
                {
                    text:'是',
                    iconCls:'icon-ok',
                    handler:function(){
                        $('#saveParamResetSLAMWarn').dialog('close');
                        $('#waitWarnLidar').dialog('open');
                        $('#waitWarnLidar').dialog('center');
                        sendSaveParamCMD();
                        sendResetSLAMCMD();
                    }
                },
                {
                    text:'否',
                    iconCls:'icon-cancel',
                    handler:function(){
                        $('#saveParamResetSLAMWarn').dialog('close');
                        $('#waitWarnLidar').dialog('close');
                    }
                }]
            ">
        当前操作保存参数并重启SLAM后生效，是否保存并重启SLAM，请确认。
    </div>
    <!---提示切换空闲模式警告--->
    <div id="getStandbyWarn" class="easyui-dialog" title="Toolbar and Buttons"
        style="width:400px;height:100px;padding:10px" data-options="
                closed: true,
				iconCls: 'icon-tip'">
        无效操作,请切换[空闲模式]！
    </div>
    <div id="waitWarnLidar" class="easyui-dialog" title="等待窗口" style="width:400px;height:80px;padding:10px"
        data-options="
                closable: false,
                closed: true,
                iconCls: 'icon-tip',
            ">
        请耐心等待，动作执行完毕将自动关闭弹窗！
    </div>
</body>

</html>