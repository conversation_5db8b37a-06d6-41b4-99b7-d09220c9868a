/*
 * @Author: your name
 * @Date: 2021-05-20 16:02:39
 * @LastEditTime: 2022-10-14 14:20:43
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /wanji_ws/src/wanji_web/resources/wait.js
 */
//作用：弹出遮罩，提示加载中
var MaskUtil = (function () {
    var $mask, $maskMsg;

    var defMsg = '正在处理，请稍待。。。';

    function init(pHeight) {
        if (!$mask) {
            $mask = $("<div class=\"datagrid-mask mymask\"></div>").appendTo("body").css({ display: "block", width: "100%", height: $(document).height(), "z-index": "99998" });
        }

        var scrollTop = $(document.body).scrollTop();

        if (!$maskMsg) {
            $maskMsg = $("<div class=\"datagrid-mask-msg mymask\">" + defMsg + "</div>")
                .appendTo("body").css({ 'font-size': '15px', 'position': 'absolute', left: ($(document.body).outerWidth(true) - 190) / 2, top: (($(window).height() - 45) / 2) + scrollTop, 'z-index': '99998' });
        }
    }

    return {
        mask: function (msg, pHeight) {
            init(pHeight);
            $mask.show();
            $maskMsg.html(msg || defMsg).show();
        }
        , unmask: function () {
            //隐藏遮罩层之前，先判断是否存在遮罩层   
            if (undefined != $mask) {
                $mask.hide();
                $maskMsg.hide();
            }
        }
    }

}());  
