*{
	box-sizing: border-box;
}
.m-toolbar{
	position: relative;
	text-align: center;
	min-height: 34px;
}
.m-toolbar .m-title{
	line-height: 34px;
	font-size: 16px;
	font-weight: bold;
	text-align: center;
}
.m-left{
	position: absolute;
	height: 100%;
	vertical-align: middle;
	top:0;
	left:0;
	z-index: 1;
}
.m-right{
	position: absolute;
	height: 100%;
	vertical-align: middle;
	top:0;
	right:0;
	z-index: 1;
}
.m-left>.l-btn,.m-right>.l-btn,
.m-left>.switchbutton,.m-right>.switchbutton{
	position: relative;
	vertical-align: top;
	top: 50%;
	margin-top: -15px;
}
.m-back::before,.m-next::after{
	display: inline-block;
	box-sizing: border-box;
	vertical-align: top;
	border-style: solid;
	-webkit-transform:rotate(45deg);
	transform:rotate(45deg);
	width: 12px;
	height: 12px;
	content: '';
	position: absolute;
	top: 50%;
	margin-top: -6px;
}
.m-back::before{
	border-width: 0 0 1px 1px;
	left: 8px;
}
.m-next::after{
	border-width: 1px 1px 0 0;
	right: 8px;
}
.m-back .l-btn-text{
	padding-left: 12px;
}
.m-next .l-btn-text{
	padding-right: 12px;
}
.m-buttongroup{
	display: inline-block;
	margin: 0;
	padding: 0;
	overflow: hidden;
	vertical-align: middle;
}
.m-buttongroup .l-btn{
	float: left;
	margin-left: -1px;
}
.m-buttongroup .l-btn:last-child::after{
	content: '';
	clear: both;
}
.m-buttongroup .l-btn:not(:first-child):not(:last-child){
	border-radius: 0;
}
.m-buttongroup .l-btn:first-child{
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	margin-left: 0;
}
.m-buttongroup .l-btn:last-child{
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}
.m-buttongroup-justified{
	display: table;
	table-layout: fixed;
}
.m-buttongroup-justified .l-btn{
	float: none;
	display: table-cell;
}
.m-badge:not(.l-btn),
.l-btn.m-badge::after{
	display: inline-block;
	min-width: 10px;
	line-height: 1;
	font-size: 12px;
	text-align: center;
	white-space: nowrap;
	border-radius: 10px;
	padding: 2px 4px;
	border-style: solid;
	border-width: 0px;
	background-color: #d9534f;
	color: #fff;
	z-index: 99999;
}
.l-btn.m-badge::after,
.l-btn .m-badge{
	position: absolute;
	top: -10px;
	right: -10px;
}
.tabs-inner .m-badge{
	position: absolute;
	top: 1px;
	right: -10px;
}
.tabs-inner>.tabs-title>.m-badge{
	top: 0;
	right: 0;
}
.tabs-header-bottom .tabs-inner>.tabs-title>.m-badge{
	top: auto;
	bottom: 0;
	right: 0;
}
.panel-footer .l-btn .l-btn-icon-top .m-badge,
.panel-footer .l-btn .l-btn-icon-bottom .m-badge{
	top: 0;
	right: -10px;
}
.l-btn.m-badge::after{
	content: attr(data-badge);
}
.l-btn,.l-btn-left{
	overflow: visible;
	position: relative;
}

.m-in{
	-webkit-animation-timing-function: ease-out;
	-webkit-animation-duration: 250ms;
}
.m-out{
	-webkit-animation-timing-function: ease-in;
	-webkit-animation-duration: 250ms;
}
.m-slide-left.m-in{
	-webkit-animation-name: slideLeftIn;
}
.m-slide-left.m-out{
	-webkit-animation-name: slideLeftOut;
}
.m-slide-right.m-in{
	-webkit-animation-name: slideRightIn;
}
.m-slide-right.m-out{
	-webkit-animation-name: slideRightOut;
}
.m-slide-up.m-in{
	-webkit-animation-name: slideUpIn;
}
.m-slide-up.m-out{
	-webkit-animation-name: slideUpOut;
}
.m-slide-down.m-in{
	-webkit-animation-name: slideDownIn;
}
.m-slide-down.m-out{
	-webkit-animation-name: slideDownOut;
}

@-webkit-keyframes slideLeftIn{
	from {-webkit-transform: translateX(100%);}
	to {-webkit-transform: translateX(0);}
}
@-webkit-keyframes slideLeftOut{
	from {-webkit-transform: translateX(0);}
	to {-webkit-transform: translateX(-100%);}
}
@-webkit-keyframes slideRightIn{
	from {-webkit-transform: translateX(-100%);}
	to {-webkit-transform: translateX(0);}
}
@-webkit-keyframes slideRightOut{
	from {-webkit-transform: translateX(0);}
	to {-webkit-transform: translateX(100%);}
}
@-webkit-keyframes slideUpIn{
	from {-webkit-transform: translateY(100%);}
	to {-webkit-transform: translateY(0);}
}
@-webkit-keyframes slideUpOut{
	from {-webkit-transform: translateY(0);}
	to {-webkit-transform: translateY(-100%);}
}
@-webkit-keyframes slideDownIn{
	from {-webkit-transform: translateY(-100%);}
	to {-webkit-transform: translateY(0);}
}
@-webkit-keyframes slideDownOut{
	from {-webkit-transform: translateY(0);}
	to {-webkit-transform: translateY(100%);}
}

.m-fade.m-in{
	-webkit-animation-name: fadeIn;
}
.m-fade.m-out{
	-webkit-animation-name: fadeOut;
}

@-webkit-keyframes fadeIn{
	from {opacity: 0;}
	to {opacity: 1}
}
@-webkit-keyframes fadeOut{
	from {opacity: 1;}
	to {opacity: 0;}
}

.m-pop.m-in{
	-webkit-animation-name: popIn;
}
.m-pop.m-out{
	-webkit-animation-name: popOut;
}
@-webkit-keyframes popIn{
	from {
		opacity: 0;
		-webkit-transform: scale(.2);
	}
	to {
		opacity: 1;
		-webkit-transform: scale(1);
	}
}
@-webkit-keyframes popOut{
	from {
		opacity: 1;
		-webkit-transform: scale(1);
	}
	to {
		opacity: 0;
		-webkit-transform: scale(0);
	}
}

.navpanel{
	position: absolute;
}
.textbox .textbox-text{
	padding: 0 4px;
	height: 30px;
	line-height: 30px;
}
.calendar-header,.calendar-title{
	height: 30px;
}
.calendar-title span{
	height: 30px;
	line-height: 30px
}
.datebox-button{
	height: 24px;
}
.datebox-button a{
	line-height: 24px;
}
.tree-node{
	box-sizing: border-box;
	height: 32px;
	padding: 3px 0;
}

.panel-title{
	height: 26px;
	line-height: 26px;
}
.window{
	padding: 5px 0 0 0;
}
.window-shadow{
	-moz-box-shadow: 0 0 30px 0 #D3D3D3;
	-webkit-box-shadow: 0 0 30px 0 #D3D3D3;
	box-shadow: 0 0 30px 0 #D3D3D3;	
}
.window-header .panel-title{
	height: 26px;
	line-height: 26px;
	text-align: center;
}
.window-header .panel-tool{
	display: none;
}
.window .window-body{
	border: 0;
}
.dialog-button{
	border-color: transparent;
	overflow: hidden;
}
.dialog-button .l-btn{
	margin: 0;
}

.tabs-justified,
.tabs-justified .l-btn,
.tabs-justified li a.tabs-inner,
.tabs-justified li.tabs-selected a.tabs-inner,
.tabs-header-bottom .tabs-justified li.tabs-selected a.tabs-inner,
.tabs-header-bottom .tabs-justified li a.tabs-inner{
	-moz-border-radius:0;
	-webkit-border-radius:0;
	border-radius:0;
}

.datagrid-row,.datagrid-header-row{
	height: 32px;
}
.datalist .datagrid-group-title,
.m-list .m-list-group{
	padding: 0 10px;
}
.datalist .datagrid-cell,
.m-list li{
	padding: 10px;
}
.m-list li .m-right{
	right: 10px;
}
.datalist .datalist-link,
.m-list li>a{
	margin: -10px;
	padding: 10px;
	padding-right: 24px;
}
.m-list li>a .m-right{
	right: 24px;
}
.datalist .datalist-link::after,
.m-list li>a::after{
	right: 12px;
}

