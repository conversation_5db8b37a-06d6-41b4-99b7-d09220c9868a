.icon-blank {
	background: url('icons/blank.gif') no-repeat center center;
}

.icon-add {
	background: url('icons/edit_add.png') no-repeat center center;
}

.icon-edit {
	background: url('icons/pencil.png') no-repeat center center;
}

.icon-clear {
	background: url('icons/clear.png') no-repeat center center;
}

.icon-remove {
	background: url('icons/edit_remove.png') no-repeat center center;
}

.icon-save {
	background: url('icons/filesave.png') no-repeat center center;
}

.icon-cut {
	background: url('icons/cut.png') no-repeat center center;
}

.icon-ok {
	background: url('icons/ok.png') no-repeat center center;
}

.icon-no {
	background: url('icons/no.png') no-repeat center center;
}

.icon-cancel {
	background: url('icons/cancel.png') no-repeat center center;
}

.icon-reload {
	background: url('icons/reload.png') no-repeat center center;
}

.icon-search {
	background: url('icons/search.png') no-repeat center center;
}

.icon-print {
	background: url('icons/print.png') no-repeat center center;
}

.icon-help {
	background: url('icons/help.png') no-repeat center center;
}

.icon-undo {
	background: url('icons/undo.png') no-repeat center center;
}

.icon-redo {
	background: url('icons/redo.png') no-repeat center center;
}

.icon-back {
	background: url('icons/back.png') no-repeat center center;
}

.icon-sum {
	background: url('icons/sum.png') no-repeat center center;
}

.icon-tip {
	background: url('icons/tip.png') no-repeat center center;
}

.icon-filter {
	background: url('icons/filter.png') no-repeat center center;
}

.icon-man {
	background: url('icons/man.png') no-repeat center center;
}

.icon-lock {
	background: url('icons/lock.png') no-repeat center center;
}

.icon-more {
	background: url('icons/more.png') no-repeat center center;
}


.icon-mini-add {
	background: url('icons/mini_add.png') no-repeat center center;
}

.icon-mini-edit {
	background: url('icons/mini_edit.png') no-repeat center center;
}

.icon-mini-refresh {
	background: url('icons/mini_refresh.png') no-repeat center center;
}

.icon-large-picture {
	background: url('icons/large_picture.png') no-repeat center center;
}

.icon-large-clipart {
	background: url('icons/large_clipart.png') no-repeat center center;
}

.icon-large-shapes {
	background: url('icons/large_shapes.png') no-repeat center center;
}

.icon-large-smartart {
	background: url('icons/large_smartart.png') no-repeat center center;
}

.icon-large-chart {
	background: url('icons/large_chart.png') no-repeat center center;
}



.icon-initmap {
	background: url('icons/initMap.png');
	background-size: 15px 15px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-initmap2 {
	background: url('icons/initMap2.png');
	background-size: 15px 15px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-continuemap {
	background: url('icons/continueMap.png');
	background-size: 15px 15px;
	background-repeat: no-repeat;
	background-position: center;

}

.icon-lidar {
	background: url('icons/lidar.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-locate {
	background: url('icons/locate.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-net {
	background: url('icons/net.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-sys {
	background: url('icons/param2.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-read {
	background: url('icons/read.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-download {
	background: url('icons/download.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}


.icon-savemap {
	background: url('icons/savemap.png');
	background-size: 35px 35px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-view {
	background: url('icons/view.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-restart {
	background: url('icons/restart.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-param {
	background: url('icons/param.png');
	background-size: 20px 20px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-delete {
	background: url('icons/delete.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-start {
	background: url('icons/start.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-stop {
	background: url('icons/stop.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}

.icon-restart2 {
	background: url('icons/restart2.png');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	background-position: center;
}