<package>

  <name>wj_slam</name>
  <version>1.2.0</version>
  <description>
    Point cloud conversions for wanji 16 LIDARs.
  </description>
  <maintainer email="<EMAIL>">PL</maintainer>
  <author>PL</author>
  <license>GNC General Public License V3.0</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>pcl_ros</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>

  <build_depend>tf</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>interactive_markers</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>message_generation</build_depend>

  <run_depend>pcl_ros</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>nav_msgs</run_depend>

  <run_depend>tf</run_depend>
  <run_depend>interactive_markers</run_depend>
  <run_depend>visualization_msgs</run_depend>
  <run_depend>message_runtime</run_depend>
</package>
