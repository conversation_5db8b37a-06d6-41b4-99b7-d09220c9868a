/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:29:35
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-05-09 14:31:28
 */
#pragma once
#include <sys/types.h>
#include <vector>

namespace wj_slam {

/** 匹配器参数模板 */
typedef struct s_MatcherConfig
{
    u_int m_uiPlaneMaxPoints;         //设置面点k近邻搜索
    float m_fPlaneMaxRadius;          //设置面点搜索到的N点的最大距离差
    float m_fPlaneMeanDiff;           //设置面点平均误差阈值
    float m_fLine2DRadius;            //设置角点2D搜索半径
    float m_fLine2DMarkRadius;        //设置靶标匹配半径
    u_int m_uiLineMinPoints;          //设置角点2D匹配最小点数
    float m_fLineMaxZDiff;            //设置角点2D匹配有效高差范围
    float m_fMaxDist;                 //设置点到线、面距离阈值
    bool m_bSampleMatch;              //设置稀疏化
    std::vector<float> m_vfPlanePCA;  //设置面点是否平面阈值 PCA原理
} s_MatcherConfig;

}  // namespace wj_slam