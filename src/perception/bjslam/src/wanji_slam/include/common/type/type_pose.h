/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2021-12-01 15:28:36
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-06-12 18:57:56
 */
#pragma once
#include "../config/conf_timer.h"
#include "type_status.h"
#include <Eigen/Eigen>
#include <boost/shared_ptr.hpp>
#include <iostream>
#include <mutex>

namespace wj_slam {

typedef struct s_POSE6D
{
  private:
    Eigen::Vector3d m_rpy_;
    double m_pi2deg_ = 180.0 / M_PI;
    // void calcRPY_()
    // {
    //     this->m_rpy_ = this->m_quat.matrix().eulerAngles(2, 1, 0);  //绕xyz旋转
    //     // std::cout<<"rpy:"<<m_rpy_.z()<<","<<m_rpy_.y()<<","<<m_rpy_.x()<<","<<std::endl;
    //     // std::cout<<"q:"<<m_quat.x()<<","<<m_quat.y()<<","<<m_quat.z()<<","<<m_quat.w()
    //     // <<std::endl;

    //     this->m_rpy_ = this->m_rpy_ / M_PI * 180.0;
    // }
    void calcRPY_()
    {
        double sinr_cosp = +2.0 * (m_quat.w() * m_quat.x() + m_quat.y() * m_quat.z());
        double cosr_cosp = +1.0 - 2.0 * (m_quat.x() * m_quat.x() + m_quat.y() * m_quat.y());
        m_rpy_[2] = atan2(sinr_cosp, cosr_cosp) * m_pi2deg_;

        // pitch (y-axis rotation)
        double sinp = +2.0 * (m_quat.w() * m_quat.y() - m_quat.z() * m_quat.x());
        if (fabs(sinp) >= 1)
            m_rpy_[1] = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
        else
            m_rpy_[1] = asin(sinp) * m_pi2deg_;

        // yaw (z-axis rotation)
        double siny_cosp = +2.0 * (m_quat.w() * m_quat.z() + m_quat.x() * m_quat.y());
        double cosy_cosp = +1.0 - 2.0 * (m_quat.y() * m_quat.y() + m_quat.z() * m_quat.z());
        m_rpy_[0] = atan2(siny_cosp, cosy_cosp) * m_pi2deg_;
    }

  public:
    Eigen::Quaterniond m_quat;
    Eigen::Vector3d m_trans;
    PoseStatus m_bFlag;
    float m_fPercent;
    s_POSE6D()
    {
        m_quat = Eigen::Quaterniond::Identity();
        m_rpy_ = Eigen::Vector3d::Zero();
        m_trans = Eigen::Vector3d::Zero();
        m_bFlag = PoseStatus::Default;
        m_fPercent = -1;
    }
    void reset()
    {
        m_quat = Eigen::Quaterniond::Identity();
        m_rpy_ = Eigen::Vector3d::Zero();
        m_trans = Eigen::Vector3d::Zero();
        m_bFlag = PoseStatus::Default;
        m_fPercent = -1;
        return;
    }
    s_POSE6D& operator=(const s_POSE6D& p_pos)
    {
        this->m_quat = p_pos.m_quat;
        this->m_trans = p_pos.m_trans;
        this->m_bFlag = p_pos.m_bFlag;
        this->m_fPercent = p_pos.m_fPercent;
        calcRPY_();
        return *this;
    }
    s_POSE6D operator*(const s_POSE6D& p_pos)
    {
        s_POSE6D l_stPose;
        l_stPose.m_trans = this->m_quat * p_pos.m_trans + this->m_trans;
        l_stPose.m_quat = this->m_quat * p_pos.m_quat;
        l_stPose.m_quat.normalize();
        l_stPose.calcRPY_();
        return l_stPose;
    }
    s_POSE6D& operator*=(const s_POSE6D& p_pos)
    {
        this->m_trans = this->m_trans + this->m_quat * p_pos.m_trans;
        this->m_quat = this->m_quat * p_pos.m_quat;
        this->m_quat.normalize();
        calcRPY_();
        return *this;
    }
    s_POSE6D operator*(const float p_fMul)
    {
        s_POSE6D l_stPose;
        // 等于0则直接返回空
        if (std::abs(p_fMul) < 1e-4)
            return l_stPose;

        // 整数位
        int decMul = floor(abs(p_fMul));
        // 小数位
        float dotMul = abs(p_fMul) - decMul;
        // 整数位变换
        while (decMul > 0)
        {
            decMul--;
            l_stPose = l_stPose * (*this);
        }
        // 小数位变换
        s_POSE6D l_stDotPose;
        l_stDotPose.m_trans = this->m_trans * dotMul;
        l_stDotPose.m_quat = Eigen::Quaterniond().Identity().slerp(dotMul, this->m_quat);
        // // 合并整数位和小数位变换
        l_stPose = l_stPose * l_stDotPose;
        // 负数整体转换
        if (p_fMul < 0)
        {
            l_stPose = l_stPose.inverse();
        }
        l_stPose.nomalize();
        l_stPose.calcRPY_();
        return l_stPose;
    }
    s_POSE6D operator/(const float p_fMul)
    {
        // 等于乘以倒数
        if (std::abs(p_fMul) > 1e-4)
            return *this * (1.0 / p_fMul);
        else
            return *this;
    }
    s_POSE6D& operator*=(const float p_fMul)
    {
        *this = *this * p_fMul;
        return *this;
    }
    s_POSE6D& operator/=(const float p_fMul)
    {
        *this = *this / p_fMul;
        return *this;
    }
    Eigen::Vector3d operator*(const Eigen::Vector3d& p_pos)
    {
        Eigen::Vector3d l_stPose;
        l_stPose = this->m_quat * p_pos + this->m_trans;
        return l_stPose;
    }
    float* data()
    {
        float* l_pfOut = new float[7];
        l_pfOut[0] = this->m_quat.x();
        l_pfOut[1] = this->m_quat.y();
        l_pfOut[2] = this->m_quat.z();
        l_pfOut[3] = this->m_quat.w();
        l_pfOut[4] = this->m_trans.x();
        l_pfOut[5] = this->m_trans.y();
        l_pfOut[6] = this->m_trans.z();
        return l_pfOut;
    }
    std::vector<float> dataVec()
    {
        std::vector<float> l_vfData = {(float)this->x(),
                                       (float)this->y(),
                                       (float)this->z(),
                                       (float)this->roll(),
                                       (float)this->pitch(),
                                       (float)this->yaw()};
        return l_vfData;
    }
    Eigen::VectorXd coeffs_6()
    {
        Eigen::VectorXd l_coeffs = Eigen::VectorXd::Zero(6);
        l_coeffs << this->m_trans, this->roll(), this->pitch(), this->yaw();
        return l_coeffs;
    }
    Eigen::VectorXd coeffs_7()
    {
        Eigen::VectorXd l_coeffs = Eigen::VectorXd::Zero(6);
        l_coeffs << this->m_trans, this->m_quat.coeffs();
        return l_coeffs;
    }
    double normXY()
    {
        double l_dist;
        l_dist = std::sqrt(std::pow(this->m_trans.x(), 2) + std::pow(this->m_trans.y(), 2));
        return l_dist;
    }
    double norm()
    {
        return this->m_trans.norm();
    }
    double x()
    {
        return this->m_trans.x();
    }
    double y()
    {
        return this->m_trans.y();
    }
    double z()
    {
        return this->m_trans.z();
    }
    double yaw()
    {
        return this->m_rpy_.x();
    }
    double pitch()
    {
        return this->m_rpy_.y();
    }
    double roll()
    {
        return this->m_rpy_.z();
    }
    void setX(double x)
    {
        this->m_trans[0] = x;
    }
    void setY(double y)
    {
        this->m_trans[1] = y;
    }
    void setZ(double z)
    {
        this->m_trans[2] = z;
    }
    void setXYZ(double x, double y, double z)
    {
        this->m_trans << x, y, z;
    }
    void setP(float p)
    {
        this->m_fPercent = p;
    }
    void setRPY(double roll, double pitch, double yaw)
    {
        roll = std::fmod(roll, 360.0);
        pitch = std::fmod(pitch, 360.0);
        yaw = std::fmod(yaw, 360.0);

        m_rpy_ << yaw, pitch, roll;
        roll = (roll * M_PI / 180.0);
        pitch = (pitch * M_PI / 180.0);
        yaw = (yaw * M_PI / 180.0);
        Eigen::Matrix3d R;
        R = Eigen::AngleAxisd(yaw, ::Eigen::Vector3d::UnitZ())
            * Eigen::AngleAxisd(pitch, ::Eigen::Vector3d::UnitY())
            * Eigen::AngleAxisd(roll, ::Eigen::Vector3d::UnitX());
        this->m_quat = R;
        calcRPY_();
    }
    void setVector6d(const Eigen::VectorXd& p_pos)
    {
        if (6 == p_pos.size())
        {
            this->m_trans << p_pos[0], p_pos[1], p_pos[2];
            this->setRPY(p_pos[3], p_pos[4], p_pos[5]);
        }
    }
    void setQuat(const Eigen::Quaterniond& p_quat)
    {
        this->m_quat = p_quat.normalized();
        calcRPY_();
    }
    s_POSE6D inverse()
    {
        s_POSE6D l_stPose;
        l_stPose.m_quat = this->m_quat.conjugate();
        l_stPose.m_trans = -(l_stPose.m_quat * this->m_trans);
        l_stPose.m_quat.normalize();
        l_stPose.calcRPY_();
        return l_stPose;
    }
    void nomalize()
    {
        this->m_quat.normalize();
        calcRPY_();
    }
    void printf(std::string head = "*")
    {
        this->m_quat.normalize();
        calcRPY_();
        std::cout << "*********** Pose " << head << "***********" << std::endl;
        std::cout << "**  x : " << this->x() << " y : " << this->y() << " z : " << this->z()
                  << "  **" << std::endl;
        std::cout << "**  r : " << this->roll() << " p : " << this->pitch()
                  << " y : " << this->yaw() << "  **" << std::endl;
        std::cout << "pose status : " << this->m_bFlag << std::endl;
        std::cout << "**************************" << std::endl;
    }
    typedef boost::shared_ptr<s_POSE6D> Ptr;
} s_POSE6D;
typedef s_POSE6D s_TWIST;

typedef struct s_PoseWithTwist
{
    std::mutex m_lock;
    s_POSE6D m_Pose;
    s_TWIST m_Twist;
    timeMs m_tsSyncTime;
    timeMs m_tsWallTime;
    PoseStatus m_bFlag;
    std::string slamPosStaus[6] =
        {"初始定位", "初始定位", "连续定位", "虚拟定位", "无效定位", "设置定位"};
    int m_iScanId;
    s_PoseWithTwist()
    {
        this->reset();
    }
    void reset()
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        m_Pose.reset();
        m_Twist.reset();
        m_tsSyncTime = 0;
        m_tsWallTime = 0;
        m_bFlag = PoseStatus::Default;
        m_iScanId = 0;
    }
    s_PoseWithTwist(const s_PoseWithTwist& p)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        m_tsSyncTime = p.m_tsSyncTime;
        m_tsWallTime = p.m_tsWallTime;
        m_bFlag = p.m_bFlag;
        m_iScanId = p.m_iScanId;
        m_Pose = p.m_Pose;
        m_Twist = p.m_Twist;
    }
    s_PoseWithTwist& operator=(const s_PoseWithTwist& p_pos)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        this->m_Pose = p_pos.m_Pose;
        this->m_Twist = p_pos.m_Twist;
        this->m_iScanId = p_pos.m_iScanId;
        this->m_tsSyncTime = p_pos.m_tsSyncTime;
        this->m_tsWallTime = p_pos.m_tsWallTime;
        this->m_bFlag = p_pos.m_bFlag;
        return *this;
    }
    void timeAlign(timeMs p_iAlignTimeMs, float p_iLidarFrameT = 100.0)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        PoseStatus l_bFlag = this->m_Pose.m_bFlag;
        timeMs l_iTimeDiff = p_iAlignTimeMs - this->m_tsSyncTime;
        timeMs l_fTimediff = l_iTimeDiff / p_iLidarFrameT;
        timeMs l_fInt = floor(l_fTimediff);
        timeMs l_fNoInt = (l_fTimediff - l_fInt);
        this->m_Pose *= (this->m_Twist * l_fInt);
        if (std::abs(l_fNoInt) > 1e-4)
            this->m_Pose *= (this->m_Twist / (1.0 / l_fNoInt));
        this->m_Pose.m_bFlag = l_bFlag;
        this->m_tsSyncTime = p_iAlignTimeMs;
        this->m_tsWallTime += l_iTimeDiff;
    }
    void recvTimeAlign(timeMs p_iAlignTimeMs, float p_iLidarFrameT = 100.0)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        PoseStatus l_bFlag = this->m_Pose.m_bFlag;
        timeMs l_iTimeDiff = p_iAlignTimeMs - this->m_tsWallTime;
        timeMs l_fTimediff = l_iTimeDiff / p_iLidarFrameT;
        timeMs l_fInt = floor(l_fTimediff);
        timeMs l_fNoInt = (l_fTimediff - l_fInt);
        this->m_Pose *= (this->m_Twist * l_fInt);
        if (std::abs(l_fNoInt) > 1e-4)
            this->m_Pose *= (this->m_Twist / (1.0 / l_fNoInt));
        this->m_Pose.m_bFlag = l_bFlag;
        this->m_tsSyncTime += l_iTimeDiff;
        this->m_tsWallTime = p_iAlignTimeMs;
    }
    void setFlag(PoseStatus p_bFlag)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        this->m_bFlag = p_bFlag;
    }
    PoseStatus getFlag()
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        return this->m_bFlag;
    }
    s_PoseWithTwist getData()
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        s_PoseWithTwist l_pos;
        l_pos.m_Pose = this->m_Pose;
        l_pos.m_Twist = this->m_Twist;
        l_pos.m_bFlag = this->m_bFlag;
        l_pos.m_iScanId = this->m_iScanId;
        l_pos.m_tsSyncTime = this->m_tsSyncTime;
        l_pos.m_tsWallTime = this->m_tsWallTime;
        return l_pos;
    }
    std::string getPoseStatus()
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        return slamPosStaus[this->m_bFlag];
    }
} s_PoseWithTwist;
typedef struct s_fuseTwist
{
    std::mutex m_lock;
    s_TWIST m_Twist;
    timeMs m_tsSyncTime;
    timeMs m_tsWallTime;
    TwistStatus m_iStatus;
    int m_iTwistId;
    s_fuseTwist()
    {
        this->reset();
    }
    void reset()
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        m_Twist.reset();
        m_tsSyncTime = 0;
        m_tsWallTime = 0;
        m_iStatus = TwistStatus::Unknown;
        m_iTwistId = 0;
    }
    s_fuseTwist(const s_fuseTwist& p_pos)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        m_Twist = p_pos.m_Twist;
        m_tsSyncTime = p_pos.m_tsSyncTime;
        m_tsWallTime = p_pos.m_tsWallTime;
        m_iStatus = p_pos.m_iStatus;
        m_iTwistId = p_pos.m_iTwistId;
    }
    s_fuseTwist& operator=(const s_fuseTwist& p_pos)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        this->m_Twist = p_pos.m_Twist;
        this->m_tsSyncTime = p_pos.m_tsSyncTime;
        this->m_tsWallTime = p_pos.m_tsWallTime;
        this->m_iStatus = p_pos.m_iStatus;
        this->m_iTwistId = p_pos.m_iTwistId;
        return *this;
    }
    void setFlag(TwistStatus p_iStatus)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        this->m_iStatus = p_iStatus;
    }
    TwistStatus getFlag()
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        return this->m_iStatus;
    }
    s_fuseTwist getData()
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        s_fuseTwist l_twist;
        l_twist.m_Twist = this->m_Twist;
        l_twist.m_iTwistId = this->m_iTwistId;
        l_twist.m_tsSyncTime = this->m_tsSyncTime;
        l_twist.m_tsWallTime = this->m_tsWallTime;
        l_twist.m_iStatus = this->m_iStatus;
        return l_twist;
    }
    bool cutData(s_fuseTwist& p_pos)
    {
        bool l_bRes = false;
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        if (this->m_iStatus != TwistStatus::Unknown)
        {
            p_pos.m_Twist = this->m_Twist;
            p_pos.m_tsSyncTime = this->m_tsSyncTime;
            p_pos.m_tsWallTime = this->m_tsWallTime;
            p_pos.m_iStatus = this->m_iStatus;
            p_pos.m_iTwistId = this->m_iTwistId;
            this->m_iStatus = TwistStatus::Unknown;
            l_bRes = true;
            // this->printf("own");
            // p_pos.printf("out");
        }
        return l_bRes;
    }
    void printf(std::string head = "*")
    {
        std::cout << "*********** fuseTwist " << head << "***********" << std::endl;
        std::cout << "**  id       : " << this->m_iTwistId << std::endl;
        std::cout << "**  status   : " << this->m_iStatus << std::endl;
        std::cout << "**  syncTime  : " << this->m_tsSyncTime << std::endl;
        std::cout << "**  recvTime : " << this->m_tsWallTime << std::endl;
        std::cout << "**  twist    : x: " << this->m_Twist.x() << " y: " << this->m_Twist.y()
                  << " z: " << this->m_Twist.z() << " roll: " << this->m_Twist.roll()
                  << " pitch: " << this->m_Twist.pitch() << " yaw: " << this->m_Twist.yaw()
                  << std::endl;
        std::cout << "**************************" << std::endl;
    }
} s_fuseTwist;

typedef struct RobotPos
{
    std::string RobotSICKPosStaus[5] = {"初始定位", "连续定位", "虚拟定位", "停止定位", "无效定位"};
    std::string RobotWJPosStaus[4] = {"无效定位", "连续定位", "初始定位", "虚拟定位"};
    Eigen::Vector3d t;
    Eigen::Quaterniond q;
    SICKPoseStatus flagSick;  // Initial | Continue | Virtual | Stop | invalid - 0 1 2 3 4
    WJPoseStatus flagWj;      // invalid Initial | Continue | Virtual 0 1 2 3
    int mode;                 // Mark\LOAM 0 1
    timeMs wallTime;
    double meandev;
    RobotPos()
    {
        this->t = Eigen::Vector3d::Zero();
        this->q = Eigen::Quaterniond::Identity();
        this->flagSick = SICKPoseStatus::InvalidSICK;
        this->flagWj = WJPoseStatus::InvalidWJ;
        this->mode = 1;
        this->wallTime = 0;
        this->meandev = 0.0;
    }

    std::string getRobotPoseStatus(bool p_bIsSICK)
    {
        if (p_bIsSICK)
            return this->RobotSICKPosStaus[this->flagSick];
        else
            return this->RobotWJPosStaus[this->flagWj];
    }

    void printf(bool p_bIsSICK, std::string head = "")
    {
        std::cout << "*********** Pose " << head << "***********" << std::endl;
        std::cout << "**  x : " << this->t.x() << " y : " << this->t.y() << " a : " << this->t.z()
                  << "  **" << std::endl;
        std::cout << "pose status : " << this->getRobotPoseStatus(p_bIsSICK) << std::endl;
        std::cout << "time  stamp : " << this->wallTime << std::endl;
        std::cout << "**************************" << std::endl;
    }
} s_RobotPos;
}  // namespace wj_slam