/**
 * @file thread.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 模块化的线程模板
 * @version 1.0
 * @date 2022-08-22
 *
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once
#include <atomic>
#include <boost/bind.hpp>
#include <boost/function.hpp>
#include <boost/shared_ptr.hpp>
#include <condition_variable>
#include <functional>
#include <mutex>
#include <thread>
namespace wj_slam {

/**
 * @class thread Thread.h "Thread.h"
 * @brief 集成控制功能的线程模板
 *
 * @code
 * class threadTest {
 *  public:
 *  typename thread::Ptr pThread;
 *  threadTest()
 *  {
 *      pThread.reset(new thread(boost::bind(&threadTest::process, this)));
 *  }
 *   void process()
 *   {
 *      while (!pThread->check_stop())
 *      {
 *          //work
 *          std::this_thread::sleep_for(std::chrono::milliseconds(1));
 *           pThread->wait_pause();
 *      }
 *   }
 * };
 * @endcode
 *
 * @code
 * 调用:start() & stop() & pause() & resume() 控制线程
 * 注意:stop()为自阻塞,线程结束后才能退出
 * 注意:state()返回值表示运行状态,而非期望状态.
 * @endcode
 *
 *
 */
class thread {
  public:
    using Ptr = boost::shared_ptr<thread>; /**< 指针类型 */

    /** @brief 线程运行状态 */
    enum State {
        Stoped,  /**< 停止状态，包括从未启动过和启动后被停止  */
        Running, /**< 运行状态  */
        Paused   /**< 暂停状态  */
    };

    /**
     * @brief Construct a new thread object
     *
     * @param[in] func 线程执行函数
     */
    thread(boost::function<void()> func);

    /**
     * @brief Destroy the thread object
     *
     */
    ~thread();

    /**
     * @brief 返回当前运行状态
     *
     * @return [State] [当前状态]
     */
    State state() const;

    /**
     * @brief 实例化并启动线程
     *
     */
    void start();

    /**
     * @brief 结束并销毁线程
     *
     */
    void stop();

    /**
     * @brief 暂停线程
     * @attention 当当前运行轮次结束后才会触发
     * ,根据state()判断是否已经暂停
     */
    void pause();

    /**
     * @brief 恢复线程运行
     * @attention 只有在pause后使用有意义,立即进入run状态
     */
    void resume();

    /**
     * @brief 检查线程是否期望退出
     *
     * @return [true] [期望退出]
     * @return [false] [反之]
     */
    bool check_stop();

    /**
     * @brief 判断暂停.进入阻塞
     *
     */
    void wait_pause();
    /**
     * @brief 检查线程是否完成阻塞
     *
     */
    void check_pause();

  private:
    /**
     * @brief 线程函数
     * @warning 无法直接使用 boost::function,原因未知
     */
    void run();
    boost::function<void()> process; /**< 真实线程执行函数,外部定义 */

    boost::shared_ptr<std::thread> _thread; /**< 管理线程 */
    std::mutex _mutex;                      /**< 状态锁 */
    std::condition_variable _condition;     /**< 状态变量 */
    volatile std::atomic_bool _pauseFlag;   /**< 暂停标识 */
    volatile std::atomic_bool _stopFlag;    /**< 停止标识 */
    State _state;                           /**< 状态指示符号 */
};

thread::thread(boost::function<void()> func)
    : _thread(nullptr), _pauseFlag(false), _stopFlag(false), _state(Stoped)
{
    process = func;
}

thread::~thread()
{
    stop();
}

thread::State thread::state() const
{
    return _state;
}

void thread::start()
{
    if (_thread == nullptr)
    {
        _pauseFlag = false;
        _stopFlag = false;
        _thread.reset(new std::thread(&thread::run, this));
        // Run会立即触发
        _state = Running;
        //_thread->detach();
    }
}

void thread::stop()
{
    if (_thread != nullptr)
    {
        _pauseFlag = false;
        _stopFlag = true;
        // 如果正在pause中,通知退出
        _condition.notify_all();
        // 阻塞等待线程结束
        if (_thread->joinable())
            _thread->join();
        _thread = nullptr;
        // stop完成
        _state = Stoped;
    }
}

void thread::pause()
{
    if (_thread != nullptr)
        _pauseFlag = true;
}

void thread::resume()
{
    if (_thread != nullptr)
    {
        _pauseFlag = false;
        _stopFlag = false;
        _condition.notify_all();
    }
}

void thread::run()
{
    process();
    _state = Stoped;
}

bool thread::check_stop()
{
    return _stopFlag;
}

void thread::wait_pause()
{
    if (_pauseFlag)
    {
        // pause成功
        _state = Paused;
        std::unique_lock<std::mutex> locker(_mutex);
        _condition.wait(locker, [this] { return !_pauseFlag; });
        //恢复线程运行状态
        _state = Running;
    }
}

void thread::check_pause()
{
    if (_pauseFlag)
    {
        // pause成功
        while (1)
        {
            if (_state == Paused)
                break;
            else
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
}
}  // namespace wj_slam
