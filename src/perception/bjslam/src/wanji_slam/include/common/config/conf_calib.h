/**
 * @file conf_calib.h
 * <AUTHOR>
 * @brief 标定参数
 * @version 1.0
 * @date 2023-08-03
 * @copyright Copyright (c)2023 Van<PERSON>
 */
#pragma once
#include "../type/type_pose.h"
#include <mutex>
#include <string>
#include <wj_log.h>

namespace wj_slam {

typedef struct s_CalibWithDev
{
    int m_iSensorID;
    int m_iScanID;                 /**< 标定数据帧数 初始化时为1*/
    int m_iTimeStamp;              /** <标定状态*/
    Eigen::VectorXd m_stCalibData; /**< 标定参数(临时记录)*/
    Eigen::VectorXd m_stStdDev;    /**< 标定标准差(临时记录)*/
    s_CalibWithDev()
    {
        m_iSensorID = 0;
        m_iScanID = 0;
        m_iTimeStamp = 0;
        m_stCalibData = Eigen::VectorXd::Zero(6);
        m_stStdDev = Eigen::VectorXd::Zero(6);
    }
} s_CalibWithDev;

/** 雷达参数 */
typedef struct s_HorizonAlignConfig
{
    int m_iWorkLidarID;                          /**< 标定的雷达号*/
    int m_iMinTimeInit;                          /**< 初始化的最短时间限制[times]*/
    bool m_bIsStartCalib;                        /**< 是否启动校准*/
    s_CalibWithDev m_data;                       /**< 标定结果*/
    std::shared_ptr<std::mutex> m_mtRequestSend; /**< 是否需要发送:WEB要数*/
    bool m_bSaveProcess;                         /**< 是否保存过程*/
    std::string m_sProcessFile;                  /**< 保存过程文件位置*/
    s_HorizonAlignConfig()
    {
        m_iWorkLidarID = 0;
        m_iMinTimeInit = 20;
        m_bIsStartCalib = false;
        m_mtRequestSend.reset(new std::mutex());
        m_bSaveProcess = false;
        m_sProcessFile = "horizonAlign.csv";
    }
    void resetPoses()
    {
        m_mtRequestSend->lock();
        m_data = s_CalibWithDev();
        m_data.m_iSensorID = m_iWorkLidarID;
        m_mtRequestSend->unlock();
    }
    // 打印
    void log() {}

} s_HorizAlignConfig;

/** 雷达参数 */
typedef struct s_MultiLidarCalibConfig
{
    int m_iBaseLidarID; /**< 标定的基准雷达号*/
    /*静态初始化参数*/
    int m_iMinTimeInit;        /**< 初始化的最短时间限制[ms]*/
    int m_iMaxTimeReInit;      /**< 初始化的最大抖动容忍时间限制[ms]*/
    float m_fInitVeloTHR;      /**< 判为静止的最大速度[m/s]*/
    float m_fInitStdDevTHR[2]; /**<  判为静止的最大位置标准差[m,deg]*/
    /*静态标定参数*/
    float m_fMoveVeloTHR;       /**< 静态标定容忍抖动速度阈值*/
    float m_fMaxRotSpeedTHR;    /**< 静态标定容忍转速最大阈值*/
    int m_iJumpDuration;        /**< 位姿插值时间系数[ms]*/
    bool m_bIsStartCalib;       /**< 是否处于标定中*/
    float m_fMinChangeDistance; /**< 距离上次静态标定时的最小移动距离*/
    float m_fMinChangeAng;      /**< 距离上次静态标定位置的最小变化角度*/
    /*标定结果参数*/
    std::vector<s_CalibWithDev> m_data;          /**< 标定结果*/
    std::shared_ptr<std::mutex> m_mtRequestSend; /**< 是否需要发送:WEB要数*/
    bool m_bSaveProcess;                         /**< 是否保存过程*/
    std::string m_sProcessFile;                  /**< 保存过程文件位置*/

    /*弃用参数*/
    float m_fVeloDiffTHR;  // 用于匀速判定的速度差异(delta_0.1s)
    s_MultiLidarCalibConfig()
    {
        m_iBaseLidarID = 0;  // 此值不可更改
        m_iMinTimeInit = 10000;
        m_iMaxTimeReInit = 1000;
        m_fInitVeloTHR = 0.05;
        m_fInitStdDevTHR[0] = 0.05;
        m_fInitStdDevTHR[1] = 0.5;
        m_fMoveVeloTHR = 0.1;
        m_fVeloDiffTHR = 0.5;
        m_fMaxRotSpeedTHR = 5;  // 5deg/s  = 0.5deg/100ms
        m_iJumpDuration = 100;
        m_bIsStartCalib = false;
        m_fMinChangeDistance = 1;  // 1m
        m_fMinChangeAng = 30;      // 30deg
        m_mtRequestSend.reset(new std::mutex());
        m_bSaveProcess = false;
        m_sProcessFile = "calib.csv";
    }
    void resetPoses(const int p_iLidarNum)
    {
        m_mtRequestSend->lock();
        m_data.resize(p_iLidarNum);
        for (int i = 0; i < p_iLidarNum; ++i)
        {
            m_data[i] = s_CalibWithDev();
            m_data[i].m_iSensorID = i;
        }
        m_mtRequestSend->unlock();
    }
    /**
     * @brief 是否全部初始化状态
     *
     * @return [true] [初始化完成]
     * @return [false] [初始化未完成]
     */
    bool checkInitial(const int p_iLidarNum = -1)
    {
        // 检查全部初始化状态
        if (p_iLidarNum == -1)
            for (size_t i = 0; i < m_data.size(); ++i)
            {
                if (m_data[i].m_iScanID != 1)  // 1代表被设置 已初始化标志
                {
                    return false;
                }
            }
        else
        {
            if (p_iLidarNum < (int)m_data.size() && m_data[p_iLidarNum].m_iScanID != 1)
                return false;
        }
        return true;
    }
    // 打印
    void log() {}

} s_MLCalibConfig;

/* 标定集合*/
typedef struct s_CalibConfig
{
    s_CalibConfig() : m_HACalib(), m_MLCalib() {}
    s_HorizAlignConfig m_HACalib;
    s_MLCalibConfig m_MLCalib;
} s_CalibConfig;

}  // namespace wj_slam