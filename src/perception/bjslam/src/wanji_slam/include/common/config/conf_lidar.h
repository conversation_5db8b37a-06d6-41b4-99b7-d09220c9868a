/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:30:32
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-05-16 08:50:14
 */
#pragma once
#include "../config/conf_timer.h"
#include "../type/type_device.h"
#include "../type/type_pose.h"
#include <boost/array.hpp>
#include <string>
#include <wj_log.h>

// 每帧点云的时间跨度，用于计算合成帧时间跨度
#define SCAN_TIME_MS 100.0f
#define SCAN_TIME 0.1f

struct s_LIDAR_RAW_DATA
{
    typedef boost::array<uint8_t, 1472> DArray;
    wj_slam::sTimeval m_stSyncTimeval;
    wj_slam::sTimeval m_stRecvTimeval;
    DArray m_data;
    s_LIDAR_RAW_DATA()
    {
        this->reset();
    }
    s_LIDAR_RAW_DATA& operator=(s_LIDAR_RAW_DATA& p_pCfg)
    {
        this->m_stRecvTimeval = p_pCfg.m_stRecvTimeval;
        this->m_stSyncTimeval = p_pCfg.m_stSyncTimeval;
        // 此处交换内容
        this->m_data.swap(p_pCfg.m_data);
        return *this;
    }
    void reset()
    {
        DArray l_emptyArray;
        this->m_data.swap(l_emptyArray);
        this->m_stRecvTimeval.reset();
        this->m_stSyncTimeval.reset();
    }
    bool hasData()
    {
        return (this->m_stRecvTimeval.second() || this->m_stRecvTimeval.subsecond());
    }
};
struct s_LIDAR_RAW_DATAS
{
    typedef boost::shared_ptr<s_LIDAR_RAW_DATAS> Ptr;
    wj_slam::sTimeval m_recvTimeval;
    int m_iTimestamp;
    std::vector<s_LIDAR_RAW_DATA> m_vPackets;
};
namespace wj_slam {

typedef struct BlindConfig
{
    float m_fStartAng = 0;
    float m_fEndAng = 0;
} s_BlindConfig;

/** 雷达参数 */
typedef struct s_LidarConfig
{
    uint32_t id = 0;
    std::string m_sLaserName;  // 雷达命名 用于辨识
    std::string m_sLaserSN;    // 雷达SN号
    s_DevCfg m_dev;
    bool m_bGetCalibrationOnline;  // 是否在线读取表
    bool m_bIsBaseLaser;           //是否为基础雷达
    bool m_bEnable;                // 雷达使能
    std::string m_sCalibrationFile;  // 存放偏心|垂直表 文件夹名(不同类型雷达可不同)
    uint32_t m_uiRPM;                // 雷达转速
    uint32_t m_uiFramePkgNum;        // 每帧点云pcap包数量
    uint32_t m_uiMinFramePkgNum;   // 每帧点云pcap包允许解析的最小数量
    uint32_t m_uiFramePktSize;     // 每包字节数
    uint32_t m_uiFrameTime;        // 雷达频率ms
    uint32_t m_uiFrameTimeMax;     // 雷达频率ms 超时检测
    uint32_t m_uiOldFrameTimeMax;  // 雷达中断数据有效阈值
    uint16_t m_uiMinIntensity;     // 最小容忍强度
    uint16_t m_uiBlindZeroDegOffset;  // 角度盲区零度偏移量-不考虑顺逆时针-通过旋转适配0deg
    uint16_t m_uiPointMinNum;  // 最小点数 默认一半，低于此阈值 则认为包数不足
    float m_fMaxDist;          // 最大点云距离
    float m_fMinDist;          // 最小点云距离
    float m_fFeatureHeight;    // 特征提取高度 雷达高度
    float m_fDistComp;         // 距离补偿值
    bool m_bUseFloor = true;   // 是否使用z<0高度以下点云
    bool m_bIsAntiRotate;      // 是否为逆时针旋转
    std::vector<s_BlindConfig> m_vBlindSector;  //角度盲区信息
    std::vector<int> m_vCurbLine;               // curb线号信息
    s_POSE6D m_transToBase;                     // 转移车体-含地平校准&标定
    std::vector<double> m_fDipAngle;            // 地平校准结果
    std::vector<double> m_fCalibration;         // 标定结果
    bool m_bUseMulticast;                       // 雷达组播开关
    s_LidarConfig()
    {
        this->id = 0;
        this->m_sLaserName = "core";
        this->m_dev.m_sDevType = "WLR720FCW";
        this->m_sLaserSN = "Unknown";
        this->m_dev.m_sDevIP = "************";
        this->m_dev.m_uiDevPort = 3333;
        this->m_dev.m_sLocalIP = "************";
        this->m_dev.m_uiLocalPort = 3001;
        this->m_dev.m_sNetName = "NET1";  //以WLC-703为默认值
        this->m_dev.m_sPcapName = "NOSET";
        this->m_bGetCalibrationOnline = true;
        this->m_bIsBaseLaser = false;
        this->m_bEnable = true;
        this->m_sCalibrationFile = "";
        this->m_uiRPM = 600;
        this->m_uiFramePkgNum = 120;
        this->m_uiMinFramePkgNum = 24;
        this->m_uiBlindZeroDegOffset = 270;
        this->m_uiPointMinNum = 1000;
        this->m_uiFramePktSize = 1260;
        this->m_uiFrameTime = 100;
        this->m_uiFrameTimeMax = 109;
        this->m_uiOldFrameTimeMax = 300;
        this->m_dev.m_sBagName = "NOSET";
        this->m_fMaxDist = 70.0;
        this->m_fMinDist = 1.0;
        this->m_fFeatureHeight = 1.0;
        this->m_fDistComp = 0.0;
        this->m_uiMinIntensity = 0;
        this->m_bUseFloor = true;
        this->m_bIsAntiRotate = false;
        this->m_vBlindSector = std::vector<s_BlindConfig>(0);
        this->m_vCurbLine = std::vector<int>(8);
        this->m_transToBase.reset();
        this->m_fDipAngle = std::vector<double>(2, 0);
        this->m_fCalibration = std::vector<double>(6, 0);
        this->m_bUseMulticast = false;
    }
    // 打印
    void log()
    {
        LOGW(WINFO, "{} [param] Lidar_{} Name: {}", WJLog::getWholeSysTime(), id, m_sLaserName);
        LOGW(WINFO, "{} [param] Lidar_{} Type: {}", WJLog::getWholeSysTime(), id, m_dev.m_sDevType);
        LOGW(WINFO, "{} [param] Lidar_{} SN: {}", WJLog::getWholeSysTime(), id, m_sLaserSN);
        LOGW(WINFO, "{} [param] Lidar_{} IP: {}", WJLog::getWholeSysTime(), id, m_dev.m_sDevIP);
        LOGW(
            WINFO, "{} [param] Lidar_{} Port: {}", WJLog::getWholeSysTime(), id, m_dev.m_uiDevPort);
        LOGW(WINFO,
             "{} [param] Lidar_{} Taget IP: {}",
             WJLog::getWholeSysTime(),
             id,
             m_dev.m_sLocalIP);
        LOGW(WINFO,
             "{} [param] Lidar_{} Taget Port: {}",
             WJLog::getWholeSysTime(),
             id,
             m_dev.m_uiLocalPort);
        LOGW(WINFO,
             "{} [param] Lidar_{} File: {}",
             WJLog::getWholeSysTime(),
             id,
             m_sCalibrationFile);
        LOGW(WINFO, "{} [param] Lidar_{} Rpm: {}", WJLog::getWholeSysTime(), id, m_uiRPM);
        LOGW(WINFO,
             "{} [param] Lidar_{} Range: [{:.1f},{:.1f}]",
             WJLog::getWholeSysTime(),
             id,
             m_fMinDist,
             m_fMaxDist);
        LOGW(WINFO,
             "[param] Lidar_{} Height: {:.2f}, Use of floor: {}",
             id,
             m_fFeatureHeight,
             m_bUseFloor);
        LOGW(WINFO,
             "{} [param] Lidar_{} Min Intensity: {}",
             WJLog::getWholeSysTime(),
             id,
             m_uiMinIntensity);
        LOGW(WINFO,
             "{} [param] Lidar_{} Trans: [{:.3f},{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             WJLog::getWholeSysTime(),
             id,
             m_transToBase.x(),
             m_transToBase.y(),
             m_transToBase.z(),
             m_transToBase.roll(),
             m_transToBase.pitch(),
             m_transToBase.yaw());
    }

} s_LidarConfig;

}  // namespace wj_slam