/*
 * @Description: 日志相关配置
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-01 15:58:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-09 13:12:46
 */
#pragma once
#include <string>
#include <wj_log.h>

namespace wj_slam {

/*FAE日志配置信息*/
typedef struct s_FaeLogCfg
{
  private:
    std::string m_sLogLevel[6] = {"DEFAULT",
                                  "ERROR",
                                  "ERROR-WARN",
                                  "ERROR-WARN-INFO",
                                  "ERROR-WARN-INFO-DEBUG",
                                  "ERROR-WARN-INFO-DEBUG-TRACE"};

  public:
    std::string m_sErrorCode;   /*错误码*/
    std::string m_sLogPath;     /*日志路径*/
    int m_iLogLevel;            // 手动设置日志级别
    uint32_t m_uiErrorNum;      /*错误次数*/
    uint32_t m_uiLogFileSize;   /*日志文件大小，超过则新建, 默认10MB*/
    bool m_bPrintfLog;          /*是否打印详细信息 eg:Path*/
    bool m_bPrintfTimeLog;      /*是否打印时间流日志*/
    bool m_bPrintfCheckLog;     /*是否打印校验日志*/
    bool m_bSaveErrorPoseLidar; /*是否保存异常定位雷达数据*/

    s_FaeLogCfg()
        : m_sErrorCode("NOERROR"), m_sLogPath("NOSET"), m_iLogLevel(0), m_uiErrorNum(0),
          m_uiLogFileSize(10), m_bPrintfLog(false), m_bPrintfTimeLog(false),
          m_bPrintfCheckLog(false), m_bSaveErrorPoseLidar(false)
    {
    }

    s_FaeLogCfg& operator=(const s_FaeLogCfg& p_pCfg)
    {
        this->m_sErrorCode = p_pCfg.m_sErrorCode;
        return *this;
    }

    void setLogFileSize()
    {
        if (this->m_uiLogFileSize == 0)
            this->m_uiLogFileSize = 10;
    }

    uint32_t getLogFileSizeByte()
    {
        // MB转字节
        int mbToByte = 1024 * 1024;
        // 低于10MB 输出10MB
        if (this->m_uiLogFileSize == 0)
            this->m_uiLogFileSize = 10;
        return this->m_uiLogFileSize * mbToByte;
    }

    void setErrorCode(std::string p_sErrCode)
    {
        m_uiErrorNum++;
        // 已有错误 则不更新错误码
        if (this->m_sErrorCode == "NOERROR")
            this->m_sErrorCode = p_sErrCode;
    }

    void resetErrorCode()
    {
        this->m_uiErrorNum = 0;
        this->m_sErrorCode = "NOERROR";
    }

    void printf()
    {
        LOGFAE(WINFO,
               "{} 首次错误码: [{}] | 累计错误 {} 次",
               WJLog::getWholeSysTime(),
               this->m_sErrorCode,
               this->m_uiErrorNum);
        LOGFAE(WINFO, "{} 日志等级 {}", WJLog::getWholeSysTime(), this->m_iLogLevel[m_sLogLevel]);
        LOGFAE(WINFO, "{} 日志路径: [{}] ", WJLog::getWholeSysTime(), this->m_sLogPath);
        LOGFAE(WINFO, "{} 日志文件大小: {} MB ", WJLog::getWholeSysTime(), this->m_uiLogFileSize);
        LOGFAE(WINFO,
               "{} 打印Path: {} {} {}",
               WJLog::getWholeSysTime(),
               this->m_bPrintfLog,
               this->m_bPrintfTimeLog,
               this->m_bPrintfCheckLog);
    }
} s_FaeLogCfg;

}  // namespace wj_slam