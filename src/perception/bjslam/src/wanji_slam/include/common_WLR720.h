/*
 * @Author: your name
 * @Date: 2021-04-27 18:53:39
 * @LastEditTime: 2023-12-05 18:04:00
 * @LastEditors: shuangquan han
 * @Description: In User Settings Edit
 * @FilePath: /wanji_16_laser/src/wanji_slam/include/common_WLR720.h
 */
#pragma once
#include <boost/shared_ptr.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <sys/time.h>
#define WLR720_SCANS_PER_FIRING 64
#define WLR720_POINT_TDURATION 0.002924f                           // 正常两点时间差[ms]
#define WLR720_RINGPOINT_TDURATION WLR720_POINT_TDURATION * 19.f;  // 同线两点时间差[ms]
#define WLR720_POINT_ANGINCREMENT 0.2f                             // 正常两点角度差[deg]
#define HORIZON_SCAN_NUM 1800u                                     // 正常单线点数[个]
typedef struct PointAdd
{
    float intensity;
    float xydist;
    float depth;
    float ang;
    float time;
} s_PointAdd;

typedef struct AddtionMsg
{
    timeval m_gnssTime;
    uint8_t m_state[3];
    Eigen::Vector3d m_gyro;
    Eigen::Vector3d m_accel;

    AddtionMsg()
        : m_gnssTime(), m_state{0, 0, 0}, m_gyro(Eigen::Vector3d::Zero()),
          m_accel(Eigen::Vector3d::Zero())
    {
    }
    // void printf_state()
    // {
    //     if (m_state[0] == 1)
    //         printf("1_正常");
    //     else
    //         printf("0_错误");
    //     if (m_state[1] == 1)
    //         printf("1_损坏上电失败");
    //     else
    //         printf("0_正常");
    //     if (m_state[2] == 0)
    //         printf("0_断开连接");
    //     else if (m_state[2] == 1)
    //         printf("1_模块输出报文正常");
    //     else if (m_state[2] == 2)
    //         printf("2_连接卫星不稳定，无PPS信号");
    //     else if (m_state[2] == 3)
    //         printf("3_模块正常连接卫星");
    // }
} s_AddtionMsg;

// Shorthand typedefs for point cloud representations
typedef pcl::PointXYZ PRaw;
typedef s_PointAdd PAdd;
typedef pcl::PointCloud<PRaw> pcRaw;
typedef std::vector<PAdd> pcAdd;
typedef pcRaw::Ptr pcRawPtr;
typedef boost::shared_ptr<pcAdd> pcAddPtr;
typedef boost::shared_ptr<s_AddtionMsg> AddtionMsgPtr;

typedef struct PCloud
{
  public:
    PCloud(bool init = false)
    {
        if (init)
        {
            m_praw.reset(new pcRaw());
            m_padd.reset(new pcAdd());
            m_pinfo.reset(new AddtionMsg());
        }
    }
    void reset()
    {
        m_praw.reset(new pcRaw());
        m_padd.reset(new pcAdd());
        m_pinfo.reset(new AddtionMsg());
    }

    void copy(PCloud& src)
    {
        *(this->m_praw) = *(src.m_praw);
        *(this->m_padd) = *(src.m_padd);
        *(this->m_pinfo) = *(src.m_pinfo);
    }
    pcRawPtr m_praw;
    pcAddPtr m_padd;
    AddtionMsgPtr m_pinfo;
} s_PCloud;