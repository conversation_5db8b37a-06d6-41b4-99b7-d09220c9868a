/**
 * @file netApp.h
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @brief 网络通信类
 * @version 1.1
 * @date 2021-05-16 16:14:43
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once
// local
#include "common/config/conf_fae.h"
#include "protocol.h"
// sys
#include <netinet/in.h>
#include <netinet/tcp.h>
// std
#include <string.h>

class WJNetApp {
  public:
    /**
     * @brief 构造函数
     * @param setWorkModeCb 设置工作模式回调函数
     * @param p_sPrintfStr  网络设备描述符
     *
     */
    WJNetApp(boost::function<bool(int)> setWorkModeCb, std::string p_sPrintfStr);

    /**
     * @brief 析构函数
     *
     */
    ~WJNetApp();

    /**
     * @brief 开启TCP服务器
     * @param p_iPort 服务器端口号
     *
     */
    void start(int p_iPort);

    /**
     * @brief 关闭TCP服务器或客户端
     *
     */
    void shutDown();

    /**
     * @brief 向Web发送位姿
     * @param poseLidar 雷达位姿
     * @param poseAGV   AGV位姿
     *
     */
    void sendWebPose(wj_slam::s_POSE6D& poseLidar, wj_slam::s_POSE6D& poseAGV);

  private:
    /**
     * @brief TCP服务器线程
     * @param p_iPort  服务器端口号
     *
     */
    void startServer_(int p_iPort);

    /**
     * @brief 处理接收数据
     *
     */
    void procMsg_();

    /**
     * @brief 发送数据
     * @param buf 待发送数据缓存
     * @param len 待发送数据长度
     *
     */
    void sendMsg_(char* buf, int len);

    /**
     * @brief 连接TCP服务器 3s未连接则认为失败
     * @param p_iFd         网络句柄 须已建立socket
     * @param p_sMyAddr     服务器地址
     * @param p_sRemoteAddr TCP服务器连接成功
     * @code
     *
     * @endcode
     * @return [true] \n
     * [连接成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [连接失败]
     *
     */
    bool connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

    /**
     * @brief 关闭连接
     *
     */
    void shutdownConnect_();

  private:
    struct tcp_info c_stConnectInfo_;         /**< TCP连接信息 */
    struct sockaddr_in c_stServerAddr_;       /**< TCP服务器地址 */
    struct sockaddr_in c_stClientAddr_;       /**< TCP客户端地址 */
    Protocol* c_pProc_;                       /**< 协议处理对象 */
    s_NetMsg* c_pstNetMsg_;                   /**< 接收数据缓存 */
    std::string c_sPrintfStr_;                /**< 设备描述符 */
    std::shared_ptr<std::thread> c_servThr_;  /**< 服务器线程 */
    int c_iSockFd_;                           /**< TCP服务器监听socket */
    int c_iNewFd_;                            /**< TCP服务器连接socket */
    bool c_bRun_;                             /**< 线程运行标志 */
    bool c_bProcThrRun_;                      /**< 处理线程运行标志 */


};
