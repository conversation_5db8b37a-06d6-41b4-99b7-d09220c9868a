/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-24 18:26:21
 * @LastEditors: Tao<PERSON>o <PERSON>u <EMAIL>
 * @LastEditTime: 2022-12-01 15:48:42
 */
#pragma once
#include <ctime>
#include <iostream>
#include <mutex>
#include <netinet/in.h>
#include <pcap.h>
#include <poll.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <sys/time.h>
#include <thread>
#include <time.h>
#include <unistd.h>

#include "common.h"
#include "common/common_ex.h"
#include <arpa/inet.h>
#include <boost/function.hpp>
#include <boost/shared_ptr.hpp>
#include <errno.h>
#include <fcntl.h>
#include <netdb.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <semaphore.h>
#include <sys/socket.h>
#include <sys/types.h>

class AgvNetApp {
  public:
    AgvNetApp(std::string p_sPcapPath, wj_slam::s_CommunicateConfig& p_agv)
        : c_sServerIP_(p_agv.m_dev.m_sLocalIP), c_uiPort_(p_agv.m_dev.m_uiLocalPort)
    {
        if (p_agv.m_dev.m_sPcapName != "")
        {
            c_sAgvPacpPath_ = setAgvPcapFile_(p_sPcapPath, p_agv.m_dev.m_sPcapName);
            std::thread sendData(&AgvNetApp::run_, this);
            sendData.detach();
        }
    }
    ~AgvNetApp()
    {
        std::lock_guard<std::mutex> l_mutex(c_mutex_);
        if (c_pacp_)
            pcap_close(c_pacp_);
        c_pacp_ = NULL;

        if (c_sockfd_ != -1)
            (void)close(c_sockfd_);

        delete c_pstPcapMsg_;
        c_pstPcapMsg_ = NULL;
    }

    void playPause(bool isPause)
    {
        c_bIsPause_ = isPause;
        printf("agv get pause: %d\n", c_bIsPause_);
    }

    void shutDown()
    {
        // 当程序没有运行则直接返回
        if (!c_bRun_)
            return;
        c_bRun_ = false;
        c_bIsPause_ = true;
        while (1)
        {
            //  printf("agvnet shutDown\n");
            if (c_bRunOver_)
                break;
            else
                usleep(1000);
        }
    }

  private:
#pragma region 变量
    std::string c_sAgvPacpPath_ = "";
    std::string c_sServerIP_ = "";
    std::mutex c_mutex_;

    uint16_t c_uiPort_ = 2112;
    int c_sockfd_ = -1;
    int c_iBufListId_ = -1;
    int c_iSleepTime = 1000;
    int c_iLastTime_ = 0;  // 上一包AGV数据header时间

    bool c_bRun_ = false;
    bool c_bRunOver_ = false;
    bool c_bIsPause_ = false;
    pcap_t* c_pacp_ = NULL;
    bpf_program c_pcapPktFilter_;

    sockaddr_in c_clientAddr_;
    sockaddr_in c_serverAddr_;
    sTimeval c_stHeaderTimeLast_;
    s_NetMsg* c_pstPcapMsg_ = NULL;
    s_NetMsg c_stBufList_[100];
    char errbuf_[PCAP_ERRBUF_SIZE];

#pragma endregion

#pragma region 函数

    bool paramInit_()
    {
        c_sockfd_ = -1;
        c_pacp_ = NULL;
        c_bRun_ = false;
        c_pstPcapMsg_ = new s_NetMsg();
        if (!isExistFileOrFolder(c_sAgvPacpPath_))
        {
            fprintf(stderr,
                    "agv pcap file: %s is non-existent, no play agv data \n",
                    c_sAgvPacpPath_.c_str());
            return false;
        }

        fprintf(stderr, "reAgvPCAP file:%s\n", c_sAgvPacpPath_.c_str());
        if ((c_pacp_ = pcap_open_offline(c_sAgvPacpPath_.c_str(), errbuf_)) == NULL)
        {
            fprintf(stderr, "Error opening agvPcap file\n");
            return false;
        }

        // 客户端
        memset(&c_clientAddr_, 0, sizeof(c_clientAddr_));
        c_clientAddr_.sin_family = AF_INET;
        c_clientAddr_.sin_port = htons(c_uiPort_ + 1);
        c_clientAddr_.sin_addr.s_addr = INADDR_ANY;

        // 服务端
        memset(&c_clientAddr_, 0, sizeof(c_clientAddr_));
        c_serverAddr_.sin_family = AF_INET;
        c_serverAddr_.sin_port = htons(c_uiPort_);
        c_serverAddr_.sin_addr.s_addr = inet_addr(c_sServerIP_.c_str());
        printf("serverAddr: %d - %s \n", c_uiPort_, c_sServerIP_.c_str());
        return true;
    }

    void run_()
    {
        LOGW(WINFO, "{} AGVNet id {}", WJLog::getWholeSysTime(), syscall(SYS_gettid));
        // 通过agvPacp文件是否存在 进而选择是否启动内部TCP客户端
        if (paramInit_())
            c_bRun_ = agvNetInit_(c_sockfd_, c_clientAddr_, c_serverAddr_);
        if (!c_bRun_)
        {
            LOGW(WINFO, "{} AGV pcap non exist, exit thread.", WJLog::getWholeSysTime());
            return;
        }
        while (c_bRun_)
        {
            if (getPktPacp_(c_pstPcapMsg_))
            {
                sendMsg_(c_pstPcapMsg_->m_aucBuf, c_pstPcapMsg_->m_uiDataLen);
            }
            else
                break;
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        c_bRunOver_ = true;
    }

    std::string setAgvPcapFile_(std::string p_sDataFilesPath, std::string p_sPcapName)
    {
        std::string l_sPcapPath = "";
        std::string l_sDataFilesPath = p_sDataFilesPath;
        if ('/' != l_sDataFilesPath[l_sDataFilesPath.length() - 1])
            l_sDataFilesPath += "/";
        l_sPcapPath = l_sDataFilesPath + p_sPcapName + ".pcapng";
        return l_sPcapPath;
    }

    bool agvNetInit_(int& p_iFd, sockaddr_in& p_sClientAddr, sockaddr_in& p_sServerAddr)
    {
        p_iFd = -1;
        // 建立套接字
        p_iFd = socket(PF_INET, SOCK_STREAM, 0);
        if (p_iFd == -1)
        {
            perror("agvNet socket");
            return false;
        }

        // 绑定端口
        if (bind(p_iFd, (sockaddr*)&p_sClientAddr, sizeof(sockaddr)) == -1)
        {
            perror("bind");
            return false;
        }

        int flags = fcntl(p_iFd, F_GETFL, 0);
        // 设置非阻塞方式
        if (fcntl(p_iFd, F_SETFL, flags | O_NONBLOCK) < 0)
        {
            perror("non-block");
            return false;
        }

        int res = connect(p_iFd, (struct sockaddr*)&p_sServerAddr, sizeof(p_sServerAddr));

        // 连接成功(服务器和客户端在同一台机器上时就有可能发生这种情况)
        if (res == 0)
        {
            printf("agv connect succ! \n");
        }
        else if (res < 0)
        {
            if (errno == EINPROGRESS)
            {
                printf("agv connecting...\n");

                fd_set writefds;
                FD_ZERO(&writefds);
                FD_SET(p_iFd, &writefds);

                struct timeval timeout;
                timeout.tv_sec = 3;
                timeout.tv_usec = 0;

                // 调用select来等待连接建立成功完成
                res = select(p_iFd + 1, NULL, &writefds, NULL, &timeout);
                if (res < 0)
                {
                    perror("agv select");
                    close(p_iFd);
                    return false;
                }

                // 返回0,则表示建立连接超时;
                // 我们返回超时错误给用户，同时关闭连接，以防止三路握手操作继续进行
                if (res == 0)
                {
                    printf("agv connection timeout\n");
                    close(p_iFd);
                    return false;
                }
                else
                {
                    // 返回大于0的值,则需要检查套接口描述符是否可读或可写;
                    if (!FD_ISSET(p_iFd, &writefds))
                    {
                        printf("err, no events found!\n");
                        close(p_iFd);
                        return false;
                    }
                    else
                    {
                        // 套接口描述符可读或可写,通过调用getsockopt得到套接口上待处理的错误(SO_ERROR)
                        // err 0-建立成功
                        int err = 0;
                        socklen_t elen = sizeof(err);
                        res = getsockopt(p_iFd, SOL_SOCKET, SO_ERROR, (char*)&err, &elen);
                        if (res < 0)
                        {
                            perror("agv getsockopt");
                            close(p_iFd);
                            return false;
                        }
                        if (err != 0)
                        {
                            printf(
                                "agv connect failed with the error: (%d)%s\n", err, strerror(err));
                            close(p_iFd);
                            return false;
                        }
                        else
                        {
                            printf("agv connect succ!\n");
                        }
                    }
                }
            }
        }

        agvPcapStart_();
        return true;
    }

    void agvPcapStart_()
    {
        /*
         * pcap模式要求pcap文件必须存在
         * 1web切换工作模式调用 调用输入的pcap肯定存在
         * 2初始化调用前已经确认pcap文件存在
         */
        std::lock_guard<std::mutex> l_mutex(c_mutex_);
        c_bIsPause_ = true;
        if (c_pacp_)
        {
            printf("close agvPcap file\n");
            pcap_close(c_pacp_);
        }
        printf("start play agvPcap file : %s\n\r press space to play pcap\n",
               c_sAgvPacpPath_.c_str());
        c_pacp_ = pcap_open_offline(c_sAgvPacpPath_.c_str(), errbuf_);

        std::string l_filter = "dst port " + std::to_string(2112);

        pcap_compile(c_pacp_, &c_pcapPktFilter_, l_filter.c_str(), 1, 0);

        pcap_setfilter(c_pacp_, &c_pcapPktFilter_);

        pcap_compile(c_pacp_, &c_pcapPktFilter_, NULL, 1, PCAP_NETMASK_UNKNOWN);
    }

    bool getPktPacp_(s_NetMsg* p_agvdata)
    {
        static int l_iAllPktNum = 0;
        static int l_iVaildPkgNum = 0;
        struct pcap_pkthdr* l_stHeader;
        const u_char* l_ucPktData;
        bool res = false;
        int l_iJumpPkgNum = 1;
        while (true)
        {
            if (!c_pacp_)
                return false;
            // 当前帧完整提取后 空格暂停才生效
            if (c_bIsPause_)
                return false;
            {
                std::lock_guard<std::mutex> l_mutex(c_mutex_);
                while (l_iJumpPkgNum)
                {
                    if (pcap_next_ex(c_pacp_, &l_stHeader, &l_ucPktData) >= 0)
                    {
                        l_iAllPktNum++;
                        if (0 == pcap_offline_filter(&c_pcapPktFilter_, l_stHeader, l_ucPktData))
                            continue;
                        // 空包跳过
                        if (l_stHeader->len <= 66)
                            continue;
                        memcpy(&p_agvdata->m_aucBuf[0], l_ucPktData + 66, l_stHeader->len - 66);
                        p_agvdata->m_uiDataLen = l_stHeader->len - 66;
                        l_iVaildPkgNum++;
                        res = true;
                    }
                    l_iJumpPkgNum--;
                }
            }

            if (res)
            {
                if (l_iVaildPkgNum == 1)
                    c_stHeaderTimeLast_ = l_stHeader->ts;

                c_iSleepTime = wj_slam::getTimeDiffUs(l_stHeader->ts, c_stHeaderTimeLast_);
                c_stHeaderTimeLast_ = l_stHeader->ts;
            }
            else
            {
                c_iSleepTime = 0;
                printf("agv pcap file run over!!\n");
                pcap_close(c_pacp_);
                c_pacp_ = NULL;
            }

            if (c_iSleepTime)
                usleep(c_iSleepTime);
            return res;
        }
    }

    void sendMsg_(u_char* p_pcBuf, int p_iLen)
    {
        if (c_sockfd_ != -1)
        {
            int l_iId = (c_iBufListId_ + 1) % 100;
            c_stBufList_[l_iId].m_uiDataLen = p_iLen;
            memcpy(&c_stBufList_[l_iId].m_aucBuf, p_pcBuf, c_stBufList_[l_iId].m_uiDataLen);

            std::thread l_sendMsg_(send,
                                   c_sockfd_,
                                   c_stBufList_[l_iId].m_aucBuf,
                                   c_stBufList_[l_iId].m_uiDataLen,
                                   MSG_NOSIGNAL);
            l_sendMsg_.join();
        }
    }

#pragma endregion
};