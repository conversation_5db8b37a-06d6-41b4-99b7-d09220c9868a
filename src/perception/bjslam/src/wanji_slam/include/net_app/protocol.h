/**
 * @file protocol.h
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @brief 协议解析类
 * @version 1.1
 * @date 2021-06-03 10:43:56
 * @copyright Copyright (c)2023 Van<PERSON>
 */

#pragma once
// local
#include "common.h"
#include "sick_Protocol.h"
#include "wj_Protocol.h"
// std
#include <thread>
// boost
#include <boost/function.hpp>

#define WJPROTOCOL 1
#define SICKPROTOCOL 2
class Protocol {
  public:
    /**
     * @brief 构造函数
     * @param netMsg            数据缓存
     * @param sendCallBack      发送数据函数回调
     * @param setWorkModeCb     设置工作模式回调
     * @param p_sPrintfStr      设备描述符
     *
     */
    Protocol(s_NetMsg& netMsg,
             boost::function<void(char*, int)> sendCallBack,
             boost::function<bool(int)> setWorkModeCb,
             std::string p_sPrintfStr = "");

    /**
     * @brief 析构函数
     *
     */
    ~Protocol();

    /**
     * @brief 数据处理
     *
     */
    void procCmd();

    /**
     * @brief 开启数据处理线程
     *
     */
    void procCmdInThread();

    /**
     * @brief 退出数据处理线程
     *
     */
    void exitProcThread();

    /**
     * @brief 关闭所有线程
     *
     */
    void shutDown();

    /**
     * @brief 识别数据协议类型
     * @code
     *
     * @endcode
     * @return [int] \n
     * [数据协议起始地址]
     *
     */
    int recognizeProtocolType(int& p_iOffset);

  private:
    s_NetMsg& c_stNetMsg_;                           /**< 数据缓存 */
    boost::function<void(char*, int)> sendCallBack_; /**< 发送数据回调函数 */
    std::shared_ptr<std::thread> c_procThr_;         /**< 处理线程 */
    std::shared_ptr<WJProtocol> c_pWJProtocol_;      /**< WJ协议处理类 */
    std::shared_ptr<SickProtocol> c_pSickProtocol_;  /**< Sick协议处理类 */
    std::mutex c_mtxLock_;                           /**< 互斥锁 */
    std::string c_sPrintfStr_;                       /**< 设备描述符 */
    int c_iCurProcOffset_;                           /**< 当前数据处理地址偏移 */
    bool c_bProcThrRun_;                             /**< 处理线程运行标志 */
};
