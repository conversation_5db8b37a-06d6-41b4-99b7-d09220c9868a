/**
 * @file common.h
 * <AUTHOR>
 * @brief 共有协议定义头文件
 * @version 1.0
 * @date 2023-06-26
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once

#include <sys/types.h>

#define NET_LENGTH_MAX 4096
#define TOUINT16(a, b) (((a << 8) & 0xFF00 | b) & 0xFFFF)
#define TOINT32(a) ((a[0] << 24 & 0xFF000000 | a[1] << 16 & 0xff0000 | a[2] << 8 & 0xff00 | a[3]))

typedef struct NetMsg
{
    u_char m_aucBuf[NET_LENGTH_MAX];
    u_int m_uiDataLen = 0;
} s_NetMsg;

/**
 * NOMATCHPROTOCOL<默认状态/无效协议>;
 * CMD_SLAVER<SLAM实时生效协议>;
 * CMD_MASTER<Master协议>;
 * CMD_DEVICE<雷达协议>
 * CMD_PARAM<参数协议>
 * CMD_PRVT<私有协议>
 */
enum WJCMDTYPE {
    NOMATCHPROTOCOL = 0,
    CMD_SLAVER = 0x0A,  // 下位机指令,算法相关
    CMD_MASTER = 0x0B,  // 上位机指令
    CMD_DEVICE = 0x0C,  // 设备配置指令
    CMD_PARAM = 0x0D,   // 参数指令 重启后生效
    CMD_PRVT = 0x0E,    // 私有指令 slam和master通信专用
};

enum SLACMDID {               // SLAM实时生效指令ID
    SETCMD_WORKMODE = 1,      // 配置工作模式
    SETCMD_VIEWLIDAR = 3,     // 设置扫描可视化
    SETCMD_VIEWMAP = 5,       // 设置地图可视化
    SETCMD_SLAMMAPINFO = 7,   // 设置SLAM地图及分辨率
    SETCMD_SAVEMAP = 9,       // 保存地图
    SETCMD_CURRPOSE = 11,     // 设置当前位姿
    SETCMD_MAPCORRECT = 13,   // 设置地图校正
    SETCMD_CARCORRECT = 15,   // 设置车体校正
    SETCMD_VIEWMODE = 17,     // 设置点云模式 2D、3D
    SETCMD_MAPPINGMODE = 19,  // 设置建图模式
    SETCMD_MOTIONMODE = 21,   // 设置运动模式
    SETCMD_WHOLEPOSE = 23,    // 设置六自由度位姿

    QUERYCMD_WORKMODE = 2,        // 查询工作模式
    QUERYCMD_VIEWLIDAR = 4,       // 查询扫描可视化
    QUERYCMD_VIEWMAP = 6,         // 查询地图可视化
    QUERYCMD_SLAMMAPINFO = 8,     // 查询当前地图及分辨率
    QUERYCMD_CURRLASERPOSE = 10,  // 查询当前雷达位姿
    QUERYCMD_MAPCORRECT = 12,     // 查询地图校正
    QUERYCMD_CARCORRECT = 14,     // 查询车体校正
    QUERYCMD_CURRTIMESTAMP = 16,  // 查询当前时间戳
    QUERYCMD_PROCVERSION = 18,    // 查询程序版本号
    QUERYCMD_CURRAGVPOSE = 22,    // 查询当前AGV位姿
    QUERYCMD_VIEWMODE = 24,       // 查询点云模式 2D、3D
    QUERYCMD_MAPPINGMODE = 26,    // 查询建图模式
    QUERYCMD_MOTIONMODE = 28,     // 查询运动模式
};

enum WEBCMDID {                  // WEB指令ID
    SETCMD_DRIVERMODE = 1,       // 配置工作模式
    SETCMD_DELETEMAP = 3,        // 设置删除地图
    SETCMD_LASERPCAPNAME = 5,    // 设置雷达离线PCAP数据包名
    SETCMD_AGVPCAPNAME = 7,      // 设置AGV离线PCAP数据包名
    SETCMD_DRIVERCONTROL = 9,    // 设置驱动控制 0关1启
    SETCMD_DRIVERPLAYRATE = 11,  // 设置播放速率
    SETCMD_DELETEPCAP = 13,      // 设置删除PCAP数据包
    SETCMD_RECORDDATA = 15,      // 设置录制数据
    SETCMD_TIMEINTERVAL = 17,    // 设置录制间隔
    SETCMD_NETCFG = 19,          // 设置网卡配置自动保存
    SETCMD_NETRESET = 21,        // 设置还原网卡配置
    SETCMD_SLAMCONTROL = 23,     // 设置SLAM启动/关闭
    SETCMD_SECRETKEY = 25,       // 密钥(if(check) save)
    SETCMD_ADDLASER = 27,        // 增加laser
    SETCMD_DELETELASER = 29,     // 删除laser
    SETCMD_SAVEPARAM = 31,       // 保存参数
    SETCMD_LASERNET = 33,        // 设置雷达网卡
    SETCMD_AGVNET = 35,          // 设置AGV网卡
    SETCMD_LOGPATH = 37,         // 设置日志路径
    SETCMD_RESETPOSEUI = 39,     // 重置位姿箭头UI

    QUERYCMD_DRIVERMODE = 2,           // 查询工作模式
    QUERYCMD_MAPLIST = 4,              // 查询地图List
    QUERYCMD_LASERPCAPNAME = 6,        // 查询雷达离线PCAP数据包名
    QUERYCMD_AGVPCAPNAME = 8,          // 查询AGV离线PCAP数据包名
    QUERYCMD_PLAYBAGSTATUS = 10,       // 查询驱动控制状态
    QUERYCMD_DRIVERPLAYRATE = 12,      // 查询播放速率
    QUERYCMD_PCAPLIST = 14,            // 查询PCAP列表
    QUERYCMD_RECORDDATA = 16,          // 查询录制状态
    QUERYCMD_RECORDTIMEINTERVAL = 18,  // 查询录制间隔时长 -多久保存1个包
    QUERYCMD_NETLIST = 20,             // 查询网卡名List
    QUERYCMD_NETCFG = 22,              // 查询网卡配置
    QUERYCMD_SAVEPOSE = 24,            // 查询保存的位姿
    QUERYCMD_SLAMSTATE = 26,           // 查询SLAM启动状态
    QUERYCMD_SECRETKEY = 28,           // 查询密钥是否解密
    QUERYCMD_MASTERSTATUS = 30,        // 查询Master是否启动 用于启动Web控件
    QUERYCMD_LASERNET = 32,            // 查询雷达网卡
    QUERYCMD_AGVNET = 34,              // 查询AGV网卡
    QUERYCMD_LOGPATH = 36,             // 查询日志路径
    QUERYCMD_ERRORCODE = 38,           // 查询故障码
    QUERYCMD_LASERSTATE = 40,          // 查询雷达状态
};

enum DEVCMDID {
    SETCMD_LASERINSTALLXYZ = 1,          // 设置雷达安装位置
    SETCMD_LASERINSTALLANGLE = 3,        // 设置雷达安装角度
    SETCMD_LASERDISBLIND = 5,            // 设置距离盲区
    SETCMD_LASERANGBLIND = 7,            // 设置角度盲区
    SETCMD_USEFLOOR = 9,                 // 设置使用地面
    SETCMD_LASERHIGHT = 11,              // 设置雷达高度
    SETCMD_MARKMODE = 13,                // 设置靶标功能
    SETCMD_HORIZONTALCALIBRATION = 15,   // 设置水平校准
    SETCMD_MULLASERCALIBRATION = 17,     // 设置多雷达标定
    SETCMD_HORIZONTALCALIBNUM = 19,      // 设置水平校准次数
    SETCMD_MULLASERCALIBRATIONINI = 21,  // 设置多雷达标定初始化
    SETCMD_LASERENABLE = 23,             // 设置雷达使能

    QUERYCMD_LASERINSTALLXYZ = 2,         // 查询雷达安装位置
    QUERYCMD_LASERINSTALLANGLE = 4,       // 查询雷达安装角度
    QUERYCMD_LASERDISBLIND = 6,           // 查询距离盲区
    QUERYCMD_LASERANGBLIND = 8,           // 查询雷达角度盲区
    QUERYCMD_USEFLOOR = 10,               // 查询使用地面
    QUERYCMD_LASERHIGHT = 12,             // 查询雷达高度
    QUERYCMD_MARKMODE = 14,               // 查询靶标功能
    QUERYCMD_HORIZONTALCALIBRATION = 16,  // 查询水平校准结果
    QUERYCMD_MULLASERCALIBRATION = 18,    // 查询雷达标定结果
    QUERYCMD_LIDARSTATUS = 20,            // 查询雷达状态
    QUERYCMD_HORIZONTALCALIBNUM = 22,     // 查询水平校准次数
    QUERYCMD_LASERENABLE = 24,            // 查询雷达使能
};

enum PARAMCMDID {
    SETCMD_CLOCKSOURCE = 5,        // 设置时钟源
    SETCMD_LASERNETPARAM = 7,      // 设置雷达网络参数
    SETCMD_LASERBASEPARAM = 9,     // 设置雷达基础参数
    SETCMD_AGVNETPARAM = 11,       // 修改agv网络参数
    SETCMD_LOADDEFAULTPARAM = 13,  // 恢复默认参数
    SETCMD_LASERISBASE = 15,       // 设置为基准雷达

    QUERYCMD_CLOCKSOURCE = 6,      // 查询时钟源
    QUERYCMD_LASERNETPARAM = 8,    // 查询雷达网络参数
    QUERYCMD_LASERBASEPARAM = 10,  // 查询雷达基础参数
    QUERYCMD_AGVNETPARAM = 12,     // 查询agv网络参数
    QUERYCMD_LIDARNUM = 14,        // 查询雷达个数
    QUERYCMD_LASERISBASE = 16,     // 查询是否为基准雷达
};

enum PRVTCMDID {
    SETCMD_DEBUGMODE = 1,         // 设置debug模式
    SETCMD_RESETENABLELASER = 3,  // 设置雷达使能全部

    QUERYCMD_DEBUGMODE = 2,   // 查询degbu模式
    QUERYCMD_SLAMSTATUS = 4,  // 查询SLAM状态
    QUERYCMD_WEBPOSE = 6,     // 查询WebPose
};