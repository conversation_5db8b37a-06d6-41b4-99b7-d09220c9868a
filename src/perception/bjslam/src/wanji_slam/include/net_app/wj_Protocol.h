/**
 * @file wj_Protocol.h
 * <AUTHOR> x<PERSON>tian
 * @brief WJ协议处理类
 * @version 1.1
 * @date 2023-06-19
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once
// local
#include "common.h"
#include "common/common_ex.h"
#include "tool/fileTool/fileTool.h"
#include "tool/protocolTool/protocolTool.h"
// sys
#include <sys/types.h>

using namespace wj_slam;

#define WJ_HEADER_LEN 26
class WJProtocol {
  public:
    /**
     * @brief 构造函数
     * @param sendCallback    发送函数回调
     * @param netMsg          数据缓存
     * @param setWorkModeCb   设置工作模式回调
     *
     */
    WJProtocol(boost::function<void(char*, int)> sendCallback,
               s_NetMsg& netMsg,
               boost::function<bool(int)> setWorkModeCb);

    /**
     * @brief 析构函数
     *
     */
    ~WJProtocol();

    /**
     * @brief 关闭线程
     *
     */
    void shutDown();

    /**
     * @brief 获取WJ指令
     * @code
     *
     * @endcode
     * @return [u_char*] \n
     * [数据首地址]
     *
     */
    u_char* getWJCMD(int&, std::mutex&);

    /**
     * @brief 分析WJ协议
     * @param p_pcBufCMD        待分析数据
     * @param p_pcBufResponse   待回复数据
     * @code
     *
     * @endcode
     * @return [int] \n
     * [待回复数据长度]
     *
     */
    int selectWJProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse);

    /**
     * @brief 分析WJ Slaver协议
     * @param p_pcBufCMD        待分析数据
     * @param p_pcBufResponse   待回复数据
     * @code
     *
     * @endcode
     * @return [int] \n
     * [待回复数据长度]
     *
     */
    int selectSlaverProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse);

    /**
     * @brief 分析WJ Device协议
     * @param p_pcBufCMD        待分析数据
     * @param p_pcBufResponse   待回复数据
     * @code
     *
     * @endcode
     * @return [int] \n
     * [待回复数据长度]
     *
     */
    int selectDeviceProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse);

    /**
     * @brief 分析WJ Private协议
     * @param p_pcBufCMD        待分析数据
     * @param p_pcBufResponse   待回复数据
     * @code
     *
     * @endcode
     * @return [int] \n
     * [待回复数据长度]
     *
     */
    int selectPrivateProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse);

  private:
    /**
     * @brief 填充协议头
     * @param p_pcBufCMD        填充内容
     * @param p_pcBufResponse   待填充数据
     * @param p_iLen            填充后长度
     *
     */
    void calcWJProtocolHead_(u_char* p_pcBufCMD, char* p_pcBufResponse, int& p_iLen);

    /**
     * @brief 填充协议尾
     * @param p_pcBuf     待填充数据
     * @param p_iLen      填充后长度
     *
     */
    void calcWJProtocolTail_(char* p_pcBuf, int& p_iLen);

    /**
     * @brief 发送位姿信息 WJ712
     * @param p_stRobot         待发送位姿
     * @param p_ucAnswerFlag    要数模式
     * @param p_ucWorkState     工作状态
     *
     */
    void sendPoseInfoByWJ712_(s_RobotPos& p_stRobot, u_char p_ucAnswerFlag, u_char p_ucWorkState);

    /**
     * @brief 发送位姿信息
     * @param p_stRobot       待发送位姿
     * @param p_ucAnswerFlag  要数模式
     * @param p_ucWorkState   工作状态
     *
     */
    void sendPoseInfoByWJ_(s_RobotPos& p_stRobot, u_char p_ucAnswerFlag, u_char p_ucWorkState);

    /**
     * @brief 发送位姿确认指令
     * @param p_ucCmd         接收数据
     * @param p_iLen          接收数据长度
     * @param p_ucAnswerFlag  要数模式
     * @param p_iWorkState    工作状态
     *
     */
    void sendPoseACKCmdByWJ_(u_char* p_ucCmd, int p_iLen, int p_ucAnswerFlag, int p_iWorkState);

    /**
     * @brief 发送接收成功指令
     * @param p_pcBufResponse 待回复数据
     * @param p_iLen          待回复数据长度
     * @param p_iRes          结果
     *
     */
    void sendRecvSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes);

    /**
     * @brief 保存地图
     * @param p_pcBufResponse 待回复数据
     * @param p_iLen          待回复数据长度
     *
     */
    void saveMap_(char* p_pcBufResponse, int p_iLen);

    /**
     * @brief 设置工作模式
     * @param p_newMode         待设置工作模式
     * @param p_pcBufResponse   待回复数据
     * @param p_iLen            待回复数据长度
     *
     */
    void setWorkMode_(int p_newMode, char* p_pcBufResponse, int p_iLen);

    /**
     * @brief 根据四元数求yaw角
     * @param q 输入四元数
     * @code
     *
     * @endcode
     * @return [double] \n
     * [yaw角， 弧度]
     *
     */
    double toYaw_(const Eigen::Quaterniond q);

    /**
     * @brief 发送线程
     *
     */
    void sendThread_();

    /**
     * @brief 发送位姿
     * @param p_iPoseModel  位姿模式
     * @param p_iWorkState  工作模式
     * @param p_bWJ         WJ协议标志
     *
     */
    void sendPose_(int p_iPoseModel, int p_iWorkState, bool p_bWJ);

    /**
     * @brief 地图校正
     * @param p_pcBufCMD  协议数据
     * @param p_iOffset   数据便宜
     * @code
     *
     * @endcode
     * @return [true] \n
     * [成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [失败]
     *
     */
    bool setMapCorrect_(u_char*& p_pcBufCMD, int& p_iOffset);

    /**
     * @brief 获取数据文件路径
     * @param p_sPkgPath 输入路径
     * @code
     *
     * @endcode
     * @return [std::string] \n
     * [文件路径]
     *
     */
    std::string getDataPath(std::string p_sPkgPath);

    /**
     * @brief 解析雷达id
     * @param p_pcBufCMD  待解析数据
     * @param p_iOffset   数据偏移
     * @code
     *
     * @endcode
     * @return [int] \n
     * [雷达id]
     *
     */
    int getLidarId_(u_char*& p_pcBufCMD, int& p_iOffset);

    /**
     * @brief 输出位姿时间对齐
     * @param p_stOutPose   对齐后位姿
     * @param p_sLidarPose  对齐前位姿
     * @param p_iPoseModel  位姿模式
     * @code
     *
     * @endcode
     * @return [float] \n
     * [对齐前后时间差值]
     *
     */
    float
    outPoseTimeAlign(s_RobotPos& p_stOutPose, s_PoseWithTwist& p_sLidarPose, int p_iPoseModel);

  private:
    enum { MAP, PCAP, BAG };

    s_NetMsg& c_stNetMsg_;                           /**< sick协议数据缓存引用 */
    SYSPARAM* c_stSysParam_;                         /**< 系统参数指针 */
    boost::function<void(char*, int)> sendCallback_; /**< 发送函数回调 */
    boost::function<bool(int)> setWorkModeCb_;       /**< 设置工作模式回调 */
    std::shared_ptr<std::thread> c_sendThr_;         /**< 发送线程 */
    bool c_bRun_;                                    /**< 运行标志 */
    bool c_bHasRecvFlag_;                            /**< 接收标志 */
    bool c_bSaveMapFlag_;                            /**< 保存地图标志 */
    bool c_bSetWorkModeFlag_;                        /**< 设置工作模式标志 */
    bool c_bIs712_;                                  /**< 712协议标志 */
};