#include "tool/fileTool/fileTool.h"

/**
 * @brief 文件操作工具API介绍
 * 1.删除文件/文件夹：deleteFileOrDir
 * 2.备份文件/文件夹：copyFileOrFolder
 * 3.判断文件/文件夹是否存在： isExistFileOrFolder
 * 4.不存在的话 创建目录：makeDir
 */

/**
 * @description: 删除某文件 or 目录
 * @param {string} file_name 文件路径 or 目录路径
 * @return {*}
 */
bool deleteFileOrDir(std::string file_name)
{
    std::string file_path = file_name;
    struct stat st;
    //获取文件相关的信息
    if (lstat(file_path.c_str(), &st) == -1)
    {
        return false;
    }
    // S_ISREG是否是一个常规文件 S_ISDIR是否是一个目录
    if (S_ISREG(st.st_mode))
    {
        // unlink 删除文件 确认无人使用该文件才会删除
        if (unlink(file_path.c_str()) == -1)
        {
            return false;
        }
    }
    else if (S_ISDIR(st.st_mode))
    {
        if (file_name == "." || file_name == "..")
        {
            return false;
        }
        if (rm_dir_(file_path) == -1)  // delete all the files in dir.
        {
            return false;
        }
    }
    return true;
}

/**
 * @description: 递归删除目录中的所有文件
 * @param {string} dir_full_path
 * @return {*}
 */
int rm_dir_(std::string dir_full_path)
{
    DIR* dirp = opendir(dir_full_path.c_str());
    if (!dirp)
    {
        return -1;
    }
    struct dirent* dir;
    struct stat st;
    while ((dir = readdir(dirp)) != NULL)
    {
        if (strcmp(dir->d_name, ".") == 0 || strcmp(dir->d_name, "..") == 0)
        {
            continue;
        }
        std::string sub_path = dir_full_path + '/' + dir->d_name;
        if (lstat(sub_path.c_str(), &st) == -1)
        {
            // Log("rm_dir_:lstat ",sub_path," error");
            continue;
        }
        if (S_ISDIR(st.st_mode))
        {
            if (rm_dir_(sub_path) == -1)  // 如果是目录文件，递归删除
            {
                closedir(dirp);
                return -1;
            }
            rmdir(sub_path.c_str());
        }
        else if (S_ISREG(st.st_mode))
        {
            unlink(sub_path.c_str());  // 如果是普通文件，则unlink
        }
        else
        {
            // Log("rm_dir_:st_mode ",sub_path," error");
            continue;
        }
    }
    if (rmdir(dir_full_path.c_str()) == -1)  // delete dir itself.
    {
        closedir(dirp);
        return -1;
    }
    closedir(dirp);
    return 0;
}

/**
 * @description: 备份文件/文件夹
 * @param {string} p_sRawPath
 * @param {string} p_sNewPath
 * @return {*}
 */
bool copyFileOrFolder(std::string p_sRawPath, std::string p_sNewPath)
{
    std::string l_sCpCMD = "cp -r " + p_sRawPath + " " + p_sNewPath;
    int l_iRet = system(l_sCpCMD.c_str());
    if (-1 == l_iRet)
    {
        printf("[error] CMD %s fail\n", l_sCpCMD.c_str());
        return false;
    }
    return true;
}

/**
 * @description: 文件/文件夹是否存在
 * @param {string} p_sPath
 * @return {*}
 */
bool isExistFileOrFolder(std::string p_sPath)
{
    // printf("isExistFileOrFolder: %s\n", p_sPath.c_str());
    std::string l_sPath = p_sPath;
    struct stat st;
    //获取文件相关的信息,获取不到则不存在
    if (lstat(l_sPath.c_str(), &st) == -1)
        return false;

    // S_ISREG是否是一个常规文件 S_ISDIR是否是一个目录
    if (S_ISREG(st.st_mode))
    {
        if (access(l_sPath.c_str(), F_OK) == 0)
            return true;
        else
            return false;
    }
    else if (S_ISDIR(st.st_mode))
    {
        DIR* dp;
        if ((dp = opendir(l_sPath.c_str())) == NULL)
            return false;
        closedir(dp);
        return true;
    }
    return false;
}

/**
 * @description: 目录不存在则创建
 * @param {string} p_sDir
 * @return {*}
 */
bool makeDir_(std::string p_sDir)
{
    DIR* dp;
    bool l_bRes = true;
    //目录不存在
    if ((dp = opendir(p_sDir.c_str())) == NULL)
    {
        int isCreate = mkdir(p_sDir.c_str(), S_IRUSR | S_IWUSR | S_IXUSR | S_IRWXG | S_IRWXO);
        if (isCreate != 0)
        {
            l_bRes = false;
            printf("create path failed! error code : %s \n", p_sDir.c_str());
        }
    }
    closedir(dp);
    return l_bRes;
}

/**
 * @description: 目录是否存在
 * @param {string} p_sDir
 * @return {*}
 */
bool checkDir_(std::string p_sDir)
{
    DIR* dp;
    bool l_bRes = true;
    //目录不存在
    dp = opendir(p_sDir.c_str());
    if (dp == NULL)
        l_bRes = false;
    else
        closedir(dp);
    return l_bRes;
}

/**
 * @description: 目录不存在则逐级创建创建
 * @param {string} p_sDir
 * @return {*}
 */
bool makeDir(std::string p_sDir)
{
    bool l_bRes = checkDir_(p_sDir);
    if (!l_bRes)
    {
        // 路径按"/"切割填充l_vsPath
        std::string::size_type pos1, pos2;
        std::vector<std::string> l_vsPath;
        std::string l_sFilterSign = "/";
        pos2 = p_sDir.find(l_sFilterSign);
        pos1 = 0;
        while (std::string::npos != pos2)
        {
            l_vsPath.push_back(p_sDir.substr(pos1, pos2 - pos1));
            pos1 = pos2 + l_sFilterSign.size();
            pos2 = p_sDir.find(l_sFilterSign, pos1);
        }
        if (pos1 != p_sDir.length())
            l_vsPath.push_back(p_sDir.substr(pos1));

        //逐级创建
        std::string l_sSubPath = "";
        for (uint32_t i = 0; i < l_vsPath.size(); i++)
        {
            l_sSubPath = l_sSubPath + "/" + l_vsPath[i];
            if (!checkDir_(l_sSubPath))
                if (!makeDir_(l_sSubPath))
                    break;
        }
        // 最后判断是否存在
        l_bRes = checkDir_(p_sDir);
    }
    return l_bRes;
}

/**
 * @description: 只有1个分割符c, 将s 按照分割符c 切割写入v
 * @param {string}  s 字符串
 * @param {vector<string>} v 输出列表
 * @param {string}  c 分割符
 * @return {*} 注意： 此函数 如果字符串为lidar_0 分隔符为lidar_  则将输出2个字符串 空字符 和0
 */
void splitString_(const std::string& s, std::vector<std::string>& v, const std::string& c)
{
    std::string::size_type pos1, pos2;
    pos2 = s.find(c);
    pos1 = 0;
    while (std::string::npos != pos2)
    {
        v.push_back(s.substr(pos1, pos2 - pos1));

        pos1 = pos2 + c.size();
        pos2 = s.find(c, pos1);
    }
    if (pos1 != s.length())
        v.push_back(s.substr(pos1));
}
/**
 * @description: 查询某目录下 某类型后缀 写入p_vFileList 不递归 否则删除不了阿
 * @param {string} p_sFileDir 目录列表
 * @param {string} p_sFileType 后缀类型
 * @param {vector<std::string>} &p_vFileList
 * @return {*}
 */
bool findFileInFolder_(std::string p_sFileDir,
                       std::string p_sFileType,
                       std::vector<std::string>& p_vFileList)
{
    std::vector<std::string>().swap(p_vFileList);

    std::string l_sFile;
    //获取linux操作系统下文件的属性
    struct stat s;
    if (lstat(p_sFileDir.c_str(), &s) == -1)
    {
        return false;
    }

    //判断一个路径是不是目录
    if (!S_ISDIR(s.st_mode))
    {
        // std::cout << "directory is not valid !" << std::endl;
        return false;
    }

    struct dirent* filename;  // return value for readdir()
    DIR* dir;                 // return value for opendir()
    dir = opendir(p_sFileDir.c_str());
    if (NULL == dir)
    {
        // cout << "Can not open dir " << p_sFileDir << endl;
        return false;
    }

    while ((filename = readdir(dir)) != NULL)
    {
        if (strcmp(filename->d_name, ".") == 0 || strcmp(filename->d_name, "..") == 0)
            continue;

        std::string sFilename(filename->d_name);
        std::string suffixStr = sFilename.substr(sFilename.find_last_of('.') + 1);  //获取文件后缀

        if (suffixStr.compare(p_sFileType) == 0)
        {  //根据后缀筛选文件

            l_sFile = filename->d_name;
            p_vFileList.push_back(l_sFile);
        }

        // //如果目录，遍历下一级
        // if (filename->d_type & DT_DIR)
        // {
        //     std::string l_Path;
        //     struct dirent* l_filename;
        //     DIR* l_dir;

        //     if (*(p_sFileDir.end() - 1) != '/')
        //         l_Path = p_sFileDir + "/" + filename->d_name;
        //     else
        //         l_Path = p_sFileDir + filename->d_name;

        //     l_dir = opendir(l_Path.c_str());
        //     if (l_dir != NULL)
        //     {
        //         while ((l_filename = readdir(l_dir)) != NULL)
        //         {
        //             if (strcmp(l_filename->d_name, ".") == 0
        //                 || strcmp(l_filename->d_name, "..") == 0)
        //                 continue;

        //             std::string l_sFilename(l_filename->d_name);
        //             std::string l_suffixStr =
        //                 l_sFilename.substr(l_sFilename.find_last_of('.') + 1);  //获取文件后缀

        //             if (l_suffixStr.compare(p_sFileType) == 0)
        //             {  //根据后缀筛选文件

        //                 l_sFile = l_filename->d_name;
        //                 p_vFileList.push_back(l_sFile);
        //             }
        //         }
        //     }
        // }
    }
    if (!p_vFileList.empty())
        return true;
    return false;
}

/**
 * @description: 查询某目录下 文件夹 写入p_vFileList ，只找1级 不递归
 * @param {string} p_sFileDir 目录列表
 * @param {vector<std::string>} &p_vFileList
 * @return {*}
 */
bool findFolderInFolder_(std::string p_sFileDir, std::vector<std::string>& p_vFileList)
{
    std::vector<std::string>().swap(p_vFileList);

    //获取linux操作系统下文件的属性
    struct stat s;
    if (lstat(p_sFileDir.c_str(), &s) == -1)
    {
        return false;
    }

    //判断一个路径是不是目录
    if (!S_ISDIR(s.st_mode))
    {
        // std::cout << "directory is not valid !" << std::endl;
        return false;
    }

    struct dirent* filename;  // return value for readdir()
    DIR* dir;                 // return value for opendir()
    dir = opendir(p_sFileDir.c_str());
    if (NULL == dir)
    {
        // cout << "Can not open dir " << p_sFileDir << endl;
        return false;
    }

    while ((filename = readdir(dir)) != NULL)
    {
        if (strcmp(filename->d_name, ".") == 0 || strcmp(filename->d_name, "..") == 0)
            continue;

        //如果目录，遍历下一级
        if (filename->d_type & DT_DIR)
            p_vFileList.push_back(filename->d_name);
    }
    if (!p_vFileList.empty())
        return true;
    return false;
}
