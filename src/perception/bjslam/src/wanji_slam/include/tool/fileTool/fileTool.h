/*
 * @Author: wen <EMAIL>
 * @Date: 2022-05-13 15:21:02
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-14 14:02:39
 * @FilePath: /src/wanji_slam/include/tool/fileTool/fileTool.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#pragma once
#include <dirent.h>
#include <string.h>
#include <string>
#include <sys/stat.h>
#include <unistd.h>
#include <vector>

/**
 * @brief 文件操作工具API介绍
 * 1.删除文件/文件夹：deleteFileOrDir
 * 2.备份文件/文件夹：copyFileOrFolder
 * 3.判断文件/文件夹是否存在： isExistFileOrFolder
 * 4.不存在的话 创建目录：makeDir
 * 5. 获取某个目录下自定义后缀格式文件List： findFileInFolder
 * 6. 获取某个目录下文件夹List：findFolderInFolder_
 */

/**
 * @description: 递归删除目录中的所有文件
 * @param {string} dir_full_path
 * @return {*}
 */
int rm_dir_(std::string dir_full_path);

/**
 * @description: 删除某文件 or 目录
 * @param {string} file_name 文件路径 or 目录路径
 * @return {*}
 */
bool deleteFileOrDir(std::string file_name);
/**
 * @description: 备份文件/文件夹
 * @param {string} p_sRawPath
 * @param {string} p_sNewPath
 * @return {*}
 */
bool copyFileOrFolder(std::string p_sRawPath, std::string p_sNewPath);

/**
 * @description: 文件/文件夹是否存在
 * @param {string} p_sPath
 * @return {*}
 */
bool isExistFileOrFolder(std::string p_sPath);

/**
 * @description: 目录不存在则逐级创建
 * @param {string} p_sDir
 * @return {*}
 */
bool makeDir(std::string p_sDir);

/**
 * @description: 目录不存在则创建,非逐级创建
 * @param {string} p_sDir
 * @return {*}
 */
bool makeDir_(std::string p_sDir);

/**
 * @description: 目录是否存在
 * @param {string} p_sDir
 * @return {*}
 */
bool checkDir_(std::string p_sDir);

/**
 * @description: 查询某目录下 某类型后缀 写入p_vFileList 不递归 否则删除不了阿
 * @param {string} p_sFileDir 目录列表
 * @param {string} p_sFileType 后缀类型
 * @param {vector<std::string>} &p_vFileList
 * @return {*}
 */
bool findFileInFolder_(std::string p_sFileDir,
                       std::string p_sFileType,
                       std::vector<std::string>& p_vFileList);

/**
 * @description: 查询某目录下 文件夹 写入p_vFileList ，只找1级 不递归
 * @param {string} p_sFileDir 目录列表
 * @param {vector<std::string>} &p_vFileList
 * @return {*}
 */
bool findFolderInFolder_(std::string p_sFileDir, std::vector<std::string>& p_vFileList);