/*
 * @Description:
 * @Version: 1.0
 * @Autor: your name
 * @Date: 2021-12-20 16:53:49
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-04-20 10:05:21
 */

/*
////////////////////////////USE GUIDE/////////////////////////////////
1: 基类为雷达运动校验,分别包含Kdtree校验与GridMap校验
    子类（1）LikelihoodScore            Kdtree校验
    子类（2）GridMapScore               GridMap校验
2： 使用
    （1）使用基类中设置地图与当前帧点云
    （2）使用基类接受共享地图返回的指针(void *)
    （3）LikelihoodScore
        1.设置地面点Z值阈值，内部已经读到雷达安装高度，地面点判定为（雷达高度 - 设定的阈值）
          例： （1.7-0.2）
        2.设置当前位姿Pose，用于内部转换点云到全局坐标系
        3.设置Kdtree搜索半径，默认为1米
        4.调用计算函数calcuLaserVerifyScore()，计算当前帧得分
        5.返回值函数，返回当前得分值getLaserVerifyScore()
        6.返回匹配值，参与计算的点数/匹配上的点数
    （4）GridMapScore
        1.设置地面点Z值阈值，内部已经读到雷达安装高度，地面点判定为（雷达高度 - 设定的阈值）
          例： （1.7-0.2）
        2.设置当前位姿Pose，用于内部转换点云到全局坐标系
        3.调用计算函数calcuLaserVerifyScore()，计算当前帧得分
        4.返回值函数，返回当前得分值getLaserVerifyScore()
        5.返回匹配值，参与计算的点数/匹配上的点数
///////////////////////////////////////////////////////////////////////
*/

#pragma once
#ifndef LASERVERIFY_SCORE
#    define LASERVERIFY_SCORE

#    include "../../common/common_ex.h"
#    include "../include/tic_toc.h"
// #    include "algorithm/map/grid_map/impl/grid_map.hpp"
// #    include "algorithm/map/iVox_map/impl/iVox_map.hpp"
// #    include "algorithm/optimize/common.h"
#    include "algorithm/optimize/kdtree_flann2d.h"
#    include "algorithm/optimize/laserTransform.h"
#    include <pcl/io/file_io.h>
#    include <pcl/kdtree/impl/kdtree_flann.hpp>
#    include <pcl/search/impl/kdtree.hpp>
template <typename PointSource, typename PointTarget>
class LaserVerifyScore : public LaserTransform<PointSource> {
  public:
    typedef boost::shared_ptr<LaserVerifyScore<PointSource, PointTarget>> Ptr;

  private:
    typedef pcl::PointXYZI PPoint;

    typedef pcl::PointCloud<PointSource> PointCloudSrc;
    typedef typename PointCloudSrc::Ptr PointCloudSrcPtr;

    typedef pcl::PointCloud<PointTarget> PointCloudTag;
    typedef typename PointCloudTag::Ptr PointCloudTagPtr;

    typedef pcl::search::KdTree<PointTarget> KdTree;
    typedef typename pcl::search::KdTree<PointTarget>::Ptr KdTreePtr;

    typedef pcl::KdTreeFLANN<PointTarget> KdTreeFlann;
    typedef typename KdTreeFlann::Ptr KdTreeFlannPtr;

  public:
    LaserVerifyScore(wj_slam::SYSPARAM& p_stParm)
        : c_pKdtAllpc_(new KdTree), /*c_gridMap_(new grid_map::GridMap<PointTarget>()),*/
          c_stSysParm_(p_stParm)
    {
        c_qEst_ = Eigen::Quaterniond::Identity();
        c_tEst_ = Eigen::Vector3d::Zero();

        c_fPathPer_ = 0;
        c_fSrcPer_ = 0;
        c_fSrcMatNum_ = 0;

        c_fRadius_ = 1.0;
        c_fRoofHigh_ = 0;
        c_fGroundHigh_ = 0;
        c_iKNum_ = 1;

        c_fGaussSize_ = 0.2;
        c_bMatchSuccess_ = false;
        c_bCalcuMode_ = true;

        c_pGridMapAllpc_.reset(new PointCloudTag());
    }
    virtual ~LaserVerifyScore()
    {
        c_pKdtAllpc_ = nullptr;
        // c_gridMap_ = nullptr;
    }

    /**
     * @description: 设置雷达地面点高度阈值
     * @param {float} p_fHigh
     * @return {*}
     * @other:
     */
    void setLaserGroundHigh(float p_fHigh);

    /**
     * @description: 设置Kdtree当前点云搜索半径
     * @param {float} p_fRadius
     * @return {*}
     * @other:
     */
    void setSearchRadius(float p_fRadius);

    /**
     * @description: 设置Kdtree当前点云搜索点数
     * @param {int} p_iKNum
     * @return {*}
     * @other:
     */
    void setSearchKNum(int p_iKNum);
    /**
     * @description: 设置正态分布标准差，计算概率
     * @param {float} p_fGuass
     * @return {*}
     * @other:
     */
    void setGuassSize(float p_fGuass);

    /**
     * @description: 设置地图
     * @param {PointCloudTagPtr} p_pTagC
     * @return {*}
     * @other:
     */
    void setInputTargetCloud(PointCloudTagPtr& p_pTagC);

    /**
     * @brief 设置当前点云
     *
     * @param p_pSur
     * @param p_pCor
     *
     */
    void setInputSourceCloud(PointCloudSrcPtr& p_pSur, PointCloudSrcPtr& p_pCor);

    /**
     * @description: 设置当前位姿
     * @param {s_POSE6D} p_stPose
     * @return {*}
     * @other:
     */
    void setInputCurrentPose(wj_slam::s_POSE6D p_stPose);

    /**
     * @description: 设置当前路径点
     * @param {PPoint} p_pPath
     * @return {*}
     * @other:
     */
    void setInputCurrentPath(PPoint p_pPath);

    /**
     * @description: 设置共享KDtree地图
     * @param {void*} p_ptr
     * @return {*}
     * @other:
     */
    void setTargetKdtree(void* p_ptr);

    /**
     * @description: 设置共享GridMap地图
     * @param {void} *p_ptr
     * @return {*}
     * @other:
     */
    void setTargetGridMap(boost::shared_ptr<IVox<3, PointTarget>> p_ptr);
    /**
     * @description: 计算概率
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual void calcuLaserVerifyScore();

    /**
     * @description: 输出当前帧点云得分
     * @param {*}
     * @return {*}
     * @other:
     */
    float getLaserVerifyScore();

    /**
     * @description: 输出当前帧点云匹配比例
     * @param {*}
     * @return {*}
     * @other:
     */
    float getLaserVerifyMatNum();

    /**
     * @description: 输出结果
     * @param {*}
     * @return {*}
     * @other:
     */
    bool getLaserVerifyResult();
    /**
     * @brief 设置棚顶与地面高度阈值
     *
     * @param p_fGround
     * @param p_fRoof
     *
     */
    void setGroundAndRoofHigh(float p_fGround, float p_fRoof);
    /**
     * @brief 设置当地图标签为2的时候，同样按照棚顶、地面高度计算
     *
     * @param p_bCalcu
     *
     */
    void setCalcuMode(bool p_bCalcu);

  protected:
    PointCloudSrcPtr c_pSrcCor_;
    PointCloudSrcPtr c_pSrcSuf_;
    PointCloudSrcPtr c_pSrcAllpc_;
    PointCloudSrcPtr c_pmergeSrcAndCor_;

    PointCloudTagPtr c_pTagCor_;
    PointCloudTagPtr c_pTagSuf_;
    PointCloudTagPtr c_pTagAllpc_;
    PointCloudTagPtr c_pGridMapAllpc_;

    int c_iSrcPcNum_;
    int c_iTagPcNum_;

    float c_fPathPer_;
    float c_fSrcPer_;
    float c_fSrcMatNum_;

    float c_fRadius_;
    float c_fRoofHigh_;
    float c_fGroundHigh_;
    int c_iKNum_;

    float
        c_fGaussSize_;  //计算概率值的数据信任值，两种：1.建图墙厚度（0.2）；2.雷达测距一致性（0.6）

    KdTree* c_pKdtAllpc_;

    Eigen::Quaterniond c_qEst_;
    Eigen::Vector3d c_tEst_;

    bool c_bMatchSuccess_;
    bool c_bCalcuMode_;

    // grid_map::GridMap<PointTarget>* c_gridMap_ = nullptr;  //栅格地图
    boost::shared_ptr<IVox<3, PointTarget>> c_pIvox_ = nullptr;  // ivox栅格地图
    wj_slam::SYSPARAM& c_stSysParm_;
};

template <typename PointSource, typename PointTarget>
class LikelihoodScore : public LaserVerifyScore<PointSource, PointTarget> {
  public:
    typedef boost::shared_ptr<LikelihoodScore<PointSource, PointTarget>> Ptr;

  private:
    typedef pcl::PointXYZI PPoint;

    typedef pcl::PointCloud<PointSource> PointCloudSrc;
    typedef typename PointCloudSrc::Ptr PointCloudSrcPtr;

    typedef pcl::PointCloud<PointTarget> PointCloudTag;
    typedef typename PointCloudTag::Ptr PointCloudTagPtr;

    typedef pcl::KdTreeFLANN<PointTarget> KdTreeFlann;
    typedef typename KdTreeFlann::Ptr KdTreeFlannPtr;

  public:
    LikelihoodScore(wj_slam::SYSPARAM& p_stParm)
        : LaserVerifyScore<PointSource, PointTarget>(p_stParm){};
    virtual ~LikelihoodScore(){};
    /**
     * @description:
     * @param {*}
     * @return {*}
     * @other:
     */
    void calcuLaserVerifyScore();

  protected:
    using LaserVerifyScore<PointSource, PointTarget>::c_pSrcCor_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pSrcSuf_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pSrcAllpc_;

    using LaserVerifyScore<PointSource, PointTarget>::c_pTagCor_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pTagSuf_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pTagAllpc_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pGridMapAllpc_;

    using LaserVerifyScore<PointSource, PointTarget>::c_iSrcPcNum_;
    using LaserVerifyScore<PointSource, PointTarget>::c_iTagPcNum_;

    using LaserVerifyScore<PointSource, PointTarget>::c_fPathPer_;
    using LaserVerifyScore<PointSource, PointTarget>::c_fSrcPer_;
    using LaserVerifyScore<PointSource, PointTarget>::c_fSrcMatNum_;

    using LaserVerifyScore<PointSource, PointTarget>::c_fRadius_;
    using LaserVerifyScore<PointSource, PointTarget>::c_fGroundHigh_;
    using LaserVerifyScore<PointSource, PointTarget>::c_iKNum_;

    using LaserVerifyScore<PointSource, PointTarget>::c_fGaussSize_;

    using LaserVerifyScore<PointSource, PointTarget>::c_pKdtAllpc_;

    using LaserVerifyScore<PointSource, PointTarget>::c_qEst_;
    using LaserVerifyScore<PointSource, PointTarget>::c_tEst_;

    using LaserVerifyScore<PointSource, PointTarget>::c_bMatchSuccess_;

    using LaserVerifyScore<PointSource, PointTarget>::c_stSysParm_;
};

template <typename PointSource, typename PointTarget>
class GridMapScore : public LaserVerifyScore<PointSource, PointTarget> {
  public:
    typedef boost::shared_ptr<GridMapScore<PointSource, PointTarget>> Ptr;

  private:
    typedef pcl::PointXYZI PPoint;

    typedef pcl::PointCloud<PointSource> PointCloudSrc;
    typedef typename PointCloudSrc::Ptr PointCloudSrcPtr;

    typedef pcl::PointCloud<PointTarget> PointCloudTag;
    typedef typename PointCloudTag::Ptr PointCloudTagPtr;

    typedef pcl::KdTreeFLANN<PointTarget> KdTreeFlann;
    typedef typename KdTreeFlann::Ptr KdTreeFlannPtr;

  public:
    GridMapScore(wj_slam::SYSPARAM& p_stParm) : LaserVerifyScore<PointSource, PointTarget>(p_stParm)
    {
        c_pmergeSrcAndCor_.reset(new PointCloudSrc());
    };
    virtual ~GridMapScore(){};
    /**
     * @description:
     * @param {*}
     * @return {*}
     * @other:
     */
    void calcuLaserVerifyScore();
    /**
     * @brief
     *
     * @param p_pt
     * @param p_fGauss
     * @param p_iMatch
     * @param p_fPer
     *
     */
    void calcaPercent(bool p_bMode,
                      PointSource p_pt,
                      const float p_fGauss,
                      const float p_fRatio,
                      int& p_iMatch,
                      float& p_fPer);

  protected:
    using LaserVerifyScore<PointSource, PointTarget>::c_pSrcCor_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pSrcSuf_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pSrcAllpc_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pmergeSrcAndCor_;

    using LaserVerifyScore<PointSource, PointTarget>::c_pTagCor_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pTagSuf_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pTagAllpc_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pGridMapAllpc_;

    using LaserVerifyScore<PointSource, PointTarget>::c_iSrcPcNum_;
    using LaserVerifyScore<PointSource, PointTarget>::c_iTagPcNum_;

    using LaserVerifyScore<PointSource, PointTarget>::c_fPathPer_;
    using LaserVerifyScore<PointSource, PointTarget>::c_fSrcPer_;
    using LaserVerifyScore<PointSource, PointTarget>::c_fSrcMatNum_;

    using LaserVerifyScore<PointSource, PointTarget>::c_fRadius_;
    using LaserVerifyScore<PointSource, PointTarget>::c_fRoofHigh_;
    using LaserVerifyScore<PointSource, PointTarget>::c_fGroundHigh_;
    using LaserVerifyScore<PointSource, PointTarget>::c_iKNum_;

    using LaserVerifyScore<PointSource, PointTarget>::c_fGaussSize_;

    using LaserVerifyScore<PointSource, PointTarget>::c_qEst_;
    using LaserVerifyScore<PointSource, PointTarget>::c_tEst_;

    using LaserVerifyScore<PointSource, PointTarget>::c_bMatchSuccess_;
    using LaserVerifyScore<PointSource, PointTarget>::c_bCalcuMode_;

    // using LaserVerifyScore<PointSource, PointTarget>::c_gridMap_;
    using LaserVerifyScore<PointSource, PointTarget>::c_pIvox_;
};
#    include "impl/laserVerifyScore.hpp"
#endif