/*
 * @Description:
 * @Version: 1.0
 * @Autor: your name
 * @Date: 2021-12-20 16:53:56
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-04-20 10:39:46
 */
#pragma once
#include "../laserVerifyScore.h"

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setLaserGroundHigh(float p_fHigh)
{
    c_fGroundHigh_ = p_fHigh;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setSearchRadius(float p_fRadius)
{
    c_fRadius_ = p_fRadius;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setSearchKNum(int p_iKNum)
{
    c_iKNum_ = p_iKNum;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setGuassSize(float p_fGuass)
{
    c_fGaussSize_ = p_fGuass;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setInputTargetCloud(PointCloudTagPtr& p_pTagC)
{
    if (p_pTagC->points.empty())
    {
        c_iTagPcNum_ = 0;
        c_pTagAllpc_ = nullptr;
        return;
    }
    c_pTagAllpc_ = p_pTagC;
    c_iTagPcNum_ = p_pTagC->size();
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setInputSourceCloud(PointCloudSrcPtr& p_pSur,
                                                                     PointCloudSrcPtr& p_pCor)
{
    if (p_pSur->points.empty() && p_pCor->points.empty())
    {
        c_iSrcPcNum_ = 0;
        c_pmergeSrcAndCor_->clear();
        return;
    }
    *c_pmergeSrcAndCor_ = *p_pSur;
    *c_pmergeSrcAndCor_ += *p_pCor;
    c_iSrcPcNum_ = c_pmergeSrcAndCor_->size();
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setGroundAndRoofHigh(float p_fGround,
                                                                      float p_fRoof)
{
    c_fRoofHigh_ = p_fRoof;
    c_fGroundHigh_ = p_fGround;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setInputCurrentPose(wj_slam::s_POSE6D p_stPose)
{
    c_qEst_ = p_stPose.m_quat;
    c_tEst_ = p_stPose.m_trans;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setInputCurrentPath(PPoint p_pPath)
{
    c_fPathPer_ = p_pPath.intensity;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setTargetKdtree(void* p_ptr)
{
    c_pKdtAllpc_ = (KdTree*)(p_ptr);
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setTargetGridMap(
    boost::shared_ptr<IVox<3, PointTarget>> p_ptr)
{
    // c_pIvox_ = (grid_map::GridMap<PointTarget>*)(p_ptr);
    c_pIvox_ = p_ptr;
}

template <typename PointSource, typename PointTarget>
float LaserVerifyScore<PointSource, PointTarget>::getLaserVerifyScore()
{
    return c_fSrcPer_;
}

template <typename PointSource, typename PointTarget>
float LaserVerifyScore<PointSource, PointTarget>::getLaserVerifyMatNum()
{
    return c_fSrcMatNum_;
}

template <typename PointSource, typename PointTarget>
bool LaserVerifyScore<PointSource, PointTarget>::getLaserVerifyResult()
{
    if (c_fSrcMatNum_ < 0.85)
    {
        c_bMatchSuccess_ = false;
    }
    else
    {
        if (c_fSrcPer_ < c_fPathPer_)
            c_bMatchSuccess_ = false;
        else
            c_bMatchSuccess_ = true;
    }
    return c_bMatchSuccess_;
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::setCalcuMode(bool p_bCalcu)
{
    c_bCalcuMode_ = p_bCalcu;
}

template <typename PointSource, typename PointTarget>
void GridMapScore<PointSource, PointTarget>::calcaPercent(bool p_bMode,
                                                          PointSource p_pt,
                                                          const float p_fGauss,
                                                          const float p_fRatio,
                                                          int& p_iMatch,
                                                          float& p_fPer)
{
    PointCloudTagPtr l_pntnear(new PointCloudTag());
    //得到当前帧对应栅格内部的点云点
    if (c_pIvox_->getGridAllPoint(p_pt, l_pntnear, p_bMode))
    {
        p_iMatch++;
        float l_fDisXYSur = sqrt(pow((p_pt.x - l_pntnear->points[0].x), 2)
                                 + pow((p_pt.y - l_pntnear->points[0].y), 2));

        for (std::size_t j = 0; j < l_pntnear->points.size(); j++)
        {
            float l_fxy = sqrt(pow((p_pt.x - l_pntnear->points[j].x), 2)
                               + pow((p_pt.y - l_pntnear->points[j].y), 2));
            if (l_fxy < l_fDisXYSur)
                l_fDisXYSur = l_fxy;
        }
        float l_fPercent = p_fRatio * expf(-l_fDisXYSur * l_fDisXYSur * p_fGauss);
        if (l_fDisXYSur >= (3 * c_fGaussSize_))
        {
            l_fPercent = 0;
        }
        p_fPer += l_fPercent;
    }
}

template <typename PointSource, typename PointTarget>
void LaserVerifyScore<PointSource, PointTarget>::calcuLaserVerifyScore()
{
    if (!c_pSrcAllpc_ || !c_pTagAllpc_)
        return;
}

template <typename PointSource, typename PointTarget>
void LikelihoodScore<PointSource, PointTarget>::calcuLaserVerifyScore()
{
    if (!c_pSrcAllpc_ || !c_pTagAllpc_)
    {
        std::cout << "LikelihoodScore calcuLaserVerifyScore is no data" << std::endl;
        return;
    }

    PointSource l_pntO;
    PointSource l_pntWldO;

    std::vector<int> l_viPntKSrhIdx;
    std::vector<float> l_vfPntKSrhSqDis;

    int l_iAllpcCalcuNum = 0;
    int l_iAllpcMatchNum = 0;
    int l_iAllpcGroundNum = 0;

    const float l_gauss_const = static_cast<float>(1. / (2. * c_fGaussSize_ * c_fGaussSize_));
    float l_fSumPercentSur = 0;

    for (int i = 0; i < c_iSrcPcNum_; i++)
    {
        l_pntO = c_pSrcAllpc_->points[i];
        if (l_pntO.z >= -(c_stSysParm_.m_lidar.feature_height - c_fGroundHigh_))
        {
            //转雷达坐标点为预估全局坐标点
            LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);
            PointTarget l_pntSrh;
            l_pntSrh.x = l_pntWldO.x;
            l_pntSrh.y = l_pntWldO.y;
            l_pntSrh.z = l_pntWldO.z;

            if (c_pKdtAllpc_->radiusSearch(l_pntSrh, c_fRadius_, l_viPntKSrhIdx, l_vfPntKSrhSqDis)
                > 0)  //半径搜索
            // if (c_pKdtAllpc_->nearestKSearch(l_pntSrh, c_iKNum_, l_viPntKSrhIdx,
            // l_vfPntKSrhSqDis) > 0)  //点数搜索
            {
                if (!l_viPntKSrhIdx.empty())
                {
                    TicToc test2;
                    l_iAllpcMatchNum++;
                    float l_fDisXYSur =
                        sqrt(pow((l_pntSrh.x - c_pTagAllpc_->points[l_viPntKSrhIdx[0]].x), 2)
                             + pow((l_pntSrh.y - c_pTagAllpc_->points[l_viPntKSrhIdx[0]].y), 2));
                    for (int j = 0; j < l_viPntKSrhIdx.size(); j++)
                    {
                        float l_fxy = sqrt(
                            pow((l_pntSrh.x - c_pTagAllpc_->points[l_viPntKSrhIdx[j]].x), 2)
                            + pow((l_pntSrh.y - c_pTagAllpc_->points[l_viPntKSrhIdx[j]].y), 2));
                        if (l_fxy < l_fDisXYSur)
                        {
                            l_fDisXYSur = l_fxy;
                        }
                    }
                    if (l_fDisXYSur <= 0.2)
                    {
                        float l_fPercent = expf(-l_fDisXYSur * l_fDisXYSur * l_gauss_const);
                        l_iAllpcCalcuNum++;
                        l_fSumPercentSur += l_fPercent;
                    }
                }
            }
        }
        else
        {
            l_iAllpcGroundNum++;
        }
    }
    c_fSrcPer_ = l_fSumPercentSur / l_iAllpcCalcuNum;
    c_fSrcMatNum_ = ((float)l_iAllpcMatchNum) / (c_iSrcPcNum_ - l_iAllpcGroundNum);
}

template <typename PointSource, typename PointTarget>
void GridMapScore<PointSource, PointTarget>::calcuLaserVerifyScore()
{
    if (!c_pmergeSrcAndCor_ || !c_pIvox_)
    {
        std::cout << "GridMapScore calcuLaserVerifyScore is no data" << std::endl;
        return;
    }

    PointSource l_pntO;
    PointSource l_pntWldO;

    const float l_gauss_const = static_cast<float>(1. / (2. * c_fGaussSize_ * c_fGaussSize_));
    const float l_ratio_const = static_cast<float>(1. / (sqrt(2. * M_PI) * c_fGaussSize_));
    // int l_iAllpcCalcuNum = 0;
    int l_iAllpcMatchNum = 0;
    float l_fSumPercentSur = 0;
    int l_iAllpcGroundNum = 0;

    if (c_bCalcuMode_)
    {
        for (int i = 0; i < c_iSrcPcNum_; i++)
        {
            l_pntO = c_pmergeSrcAndCor_->points[i];
            if (l_pntO.v != 2)
            {
                calcaPercent(c_bCalcuMode_,
                             l_pntO,
                             l_gauss_const,
                             l_ratio_const,
                             l_iAllpcMatchNum,
                             l_fSumPercentSur);
            }
            else
            {
                l_iAllpcGroundNum++;
            }
        }
    }
    else
    {
        for (int i = 0; i < c_iSrcPcNum_; i++)
        {
            l_pntO = c_pmergeSrcAndCor_->points[i];
            if (((l_pntO.z - c_tEst_.z()) > (-c_fGroundHigh_))
                || ((l_pntO.z - c_tEst_.z()) < c_fRoofHigh_))
            {
                calcaPercent(c_bCalcuMode_,
                             l_pntO,
                             l_gauss_const,
                             l_ratio_const,
                             l_iAllpcMatchNum,
                             l_fSumPercentSur);
            }
            else
            {
                l_iAllpcGroundNum++;
            }
        }
    }
    c_fSrcPer_ = 0 != l_iAllpcMatchNum ? l_fSumPercentSur / l_iAllpcMatchNum : 0;
    c_fSrcMatNum_ = ((float)l_iAllpcMatchNum) / (c_iSrcPcNum_ - l_iAllpcGroundNum);
}
