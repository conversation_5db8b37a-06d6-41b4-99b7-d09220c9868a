/*
 * @Author: your name
 * @Date: 2021-05-08 11:02:44
 * @LastEditTime: 2022-03-29 14:36:24
 * @LastEditors: <PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_amcl3d/include/header.h
 */
#pragma once

#include "tic_toc.h"
#include <Eigen/Dense>
#include <cmath>
#include <fstream>
#include <iostream>
#include <mutex>
#include <pcl/common/transforms.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/io/pcd_io.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <random>
#include <ros/time.h>
#include <stdlib.h>
#include <string>
#include <thread>
#include <vector>

typedef struct _AMCLPOSE
{
    float x;
    float y;
    float z;
    float a;  // rad
} AMCLPOSE;

//地图模式，二维或者三维
enum MAP_DIMENSION { MAP_3D, MAP_2D };

namespace wj_slam {
typedef struct _InitPose
{
    float x;
    float y;
    float z;
    float a;

    float devXY;  //位置精度
    float devA;   //角度精度

    float score;

    _InitPose()
    {
        x = y = z = a = devA = devXY = score = 0.0f;
    }

    _InitPose(float p_fX,
              float p_fY,
              float p_fZ,
              float p_fA,
              float p_devXY,
              float p_devA,
              float p_score = 0)
    {
        x = p_fX;
        y = p_fY;
        z = p_fZ;
        a = p_fA;

        devXY = p_devXY;
        devA = p_devA;
        score = p_score;
    }
} s_InitPose;

}  // namespace wj_slam

//#define TEST 0

#ifdef TEST
extern std::ofstream g_out_file;

#endif
