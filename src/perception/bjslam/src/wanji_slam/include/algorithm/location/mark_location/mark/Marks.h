#pragma once
#ifndef __MARKS_H__
#    define __MARKS_H__

//#include "config.h"
//#include "marktype.h"
#    include "marktype.h"
//#ifdef	__MARKS_C
#    define MARKS_EXT
// #else
// #define	MARKS_EXT	extern
// #endif

#    define MARK_PULSE
//#define MARK_SEND_PULSE
//#define TEST_MARK

#    define NEW_BORAD

#    define MAX_DIST 65500

//#define USE_SOKET2

#    define FPGA_CAL_INIT 0u
#    define FPGA_CAL_POS 1u

#    define PROTOCOL_SICK 1
#    define PROTOCOL_WJ 0

#    define NET_SEND_DLY 200

//#define TEST_TIME

#    define MKWORD16(a, b) ((a << 8) | b)
#    define MKWORD32(a, b, c, d) ((a << 24) | (b << 16) | (c << 8) | d)

#    define ANG_SIZE 32
#    define ANG_ERROR 20 * 3  // 20*3

#    define ORDER_CW 0  //顺时针
#    define ORDER_CC 1  //逆时针

#    define REAL_XY
#    define UNITE 1

#    define MARK_SIZE_VAL 38

#    define FRAM_ADDR_RESET 0                         //激光器重启次数
#    define FRAM_ADDR_MARKSMATCH 1 * 1024             //激光器靶标识别参数
#    define FRAM_ADDR_PARAM_FACTORY_TEMP 2 * 1024     //激光器修正参数
#    define FRAM_ADDR_PARAM_FACTORY_DEFAULT 3 * 1024  //激光器修正参数_默认值
#    define FRAM_ADDR_ROBOT 4 * 1024                  //激光器pos
#    define FRAM_ADDR_SYSPARAM_DEFAULT 5 * 1024       //激光器系统参数_默认值
#    define FRAM_ADDR_SYSPARAM_TEMP 6 * 1024          //激光器系统参数
//#define FRAM_ADDR_MARKLAYER_INFO		7*1024		//激光器靶标分层信息
#    define FRAM_ADDR_TEST 60 * 1024

//靶标类型的宏定义
#    define MARK_TYPE_CYC 0
//#define MARK_TYPE_SQU	1
#    define MARK_TYPE_FACE 2

//上传的脉宽还是距离
#    define DATA_DIST 0u
#    define DATA_PULSE 1u
#    define DATA_PULSE01 2u

//靶标类型的定义
#    define MARK_ID_RQW 0
#    define MARK_ID_OK 1
#    define MARK_ID_NEW 2

//电机转速判断的宏定义
#    define MOTOR_SPEED_MAX 1000
#    define MOTOR_SPEED_MIN 450

#    define ACTIVE 1
#    define NOACTIVE 0
#    define VIRTUAL 2
#    define INITIAL 3

#    define PROTOCOL_OLD 0
#    define PROTOCOL_NEW 1
#    define COM_BUF_MAX 8  // 25
//先定义一个前后比较的激光器位置误差, 方位误差
#    define ERR_ROBOT_POS 1000
#    define ERR_ROBOT_ANG 4000
#    define PRIO_SELECT_DIST 15000  //靶标优先选择的距离 15米内的靶标

#    define NOCORR_SLINE_P 0.01
#    define NOCORR_SLINE_N -0.01
#    define NOCORR_SANG_P 0.1
#    define NOCORR_SANG_N -0.1
#    define MARK_COUNTIUEZERO_NUM 15

#    define Edge_Minification 4  //边长缩放

// extern __IO u16 g_u16HeartCnt ;

#    define CLR_GPIOOUT()                                                                          \
        {                                                                                          \
        }  // GPIO_ResetBits(GPIOA, GPIO_Pin_11)
#    define SET_GPIOOUT()                                                                          \
        {                                                                                          \
        }  // GPIO_SetBits(GPIOA, GPIO_Pin_11)
#    define TOG_GPIOOUT() GPIO_ToggleBits(GPIOA, GPIO_Pin_11)

#    define LandMarkSendMode 0x1A
#    define LandMark_Filter 1
#    define LandMark_NoFilter 0

// MARKS_EXT INPUTSPEED g_sInSpeedUsedCopy[12];
// MARKS_EXT STRUCT_FPGAPOS g_sFpgaPos ;
MARKS_EXT u32 SortXY_X(ROBOT_XY* probot, u16 offset, u8 size);
MARKS_EXT u32 SortXY_Y(ROBOT_XY* probot, u16 offset, u8 size);
// MARKS_EXT COMBINE g_sComb ;

// MARKS_EXT SqList g_au32TriSumEdge;//周长定义成快排的结构体

MARKS_EXT u16 Cal_MeanDev(XY2ROBOT_CNT* p_AbsCoor);
MARKS_EXT u8 Nav_Mark_Num(u8 p_MarkNum);
MARKS_EXT u32 Get_SendPos(ROBOT_XY* p_CurPos, ROBOT_XY* p_SendPos);
MARKS_EXT float Calculate_Point2Point_Dist_Float(float p_Point_1, float p_Point_2);
MARKS_EXT void Cal_MoveSpeed(INPUTSPEED* p_Speed,
                             ROBOT_XY* p_Cur_Pos,
                             ROBOT_XY* p_Old_Pos,
                             TIMESTAMP p_TS_ScanOver,
                             u32 p_TS_Diff);
MARKS_EXT int CmpRobotPos1(ROBOT_XY* old, XYANG2ROBOT* cur, u16 errdiff);
MARKS_EXT u16 CorrAng_ByLoop(u16 p_Ang, u16 p_MaxAng);
MARKS_EXT u16 Return_Max(u16 p_In1, u16 p_In2);
MARKS_EXT bool Points_Diff_Cmp(u32 p_Diff_In, u32 p_Diff_Cmp_In);
MARKS_EXT void Cal_LaserPos_WeightAver(ROBOT_XY* p_LaserPos, u32 p_WeightSum);
MARKS_EXT int Get_Laser_Pos_FromFpga(ROBOT_XY* p_LaserPos, u8 i, u8 cmd, u8 decMarkNum);
MARKS_EXT int Cal_Laser_Pose_ByFpga(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                    STRUCT_FPGAPOS* p_Fpga,
                                    u8* p_offsetbuf,
                                    u8 p_offset,
                                    ROBOT_XY* p_LaserPos,
                                    u8 p_NavMarkNum);
MARKS_EXT void Get_LaserPos_WeightAver(STRUCT_FILTER_TARGET_LITE* p_Nav_Mark,
                                       STRUCT_FPGAPOS* p_FpgaBuf,
                                       ROBOT_XY* p_LaserPos,
                                       u8 p_NavMarkNum);
MARKS_EXT void
CalEcho(u16* pResults_t, u16* pResults_d, u16 size, u8 no, u8 ang_mode, u16* pResults_echo);
MARKS_EXT int SortCombineEdge(COMBINE* comb, u32* TriEdgeofDect);
MARKS_EXT int MatchDecMark_And_RenewList(STRUCT_FILTER_TARGET_LITE* pDistNew,
                                         MappingList* pMappingList,
                                         u8 newoffset,
                                         u8* pMappingOffset);
MARKS_EXT u8 Delet_List(u8 p_offset, MappingList* p_MappingList);
MARKS_EXT u8 Filter_NotMark_CopyScan(STRUCT_FILTER_TARGET_LITE* p_Nav_MarkNew,
                                     STRUCT_FILTER_TARGET_LITE* p_Nav_MarkOld);
MARKS_EXT u8 Filter_NotMark_CopyAll(STRUCT_FILTER_TARGET_LITE* p_Nav_MarkNew,
                                    STRUCT_FILTER_TARGET_LITE* p_Nav_MarkOld);
MARKS_EXT void Find_MaxMarkRadio(MARK_SETS* p_Mark, u8* p_MaxMarkRadio);
MARKS_EXT void combine(u8 n, u8 m, u8 a[], u8 b[], const u8 M);
MARKS_EXT void combine1(u8 n, u8 m, u8 a[], u8 b[], const u8 M);
MARKS_EXT u16 Round_Ang(u16 p_u16Ang, u16 p_AngResolution);
MARKS_EXT void
Corr_Mark_MixLayout(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark, u8 p_offset, XY_TO_RelCoor* p_XYtoRel);
MARKS_EXT void Corr_Mark_MixLayout_Final(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark, u8 p_offset);
MARKS_EXT void
GetOutlineOfMarks1(u16* pResults_t, u16* pResults_d, u16 p_Offset, STRUCT_CARVE* p_CarveBuf);
// MARKS_EXT int CalSetMarkForTri(MARK_SETS *SetMark , u16 *l_u32edge) ;
// MARKS_EXT u32 CalSetMarkForTri(u8 n,u8 m,u8 a[],u8 b[],const u8 M,MARK_SETS *SetMark, u32
// *TriEdge, SqList *TriSumEdge,u8* TriNo);
MARKS_EXT int CalAbsCoordinatesofDec1(STRUCT_MARK_INFO* p_ScanMarkInfo,
                                      ROBOT_XY* robot,
                                      MARK_XY* p_MatchMark,
                                      u8 offset,
                                      XY2ROBOT_CNT* absXY);
MARKS_EXT int CalAbsCoordinatesofDec_N(STRUCT_FILTER_TARGET_LITE* pDist,
                                       ROBOT_XY* robot,
                                       u8 offset,
                                       XY2ROBOT_CNT* absXY,
                                       u8 offset_N);
MARKS_EXT int
MatchSetMark(MARK_SETS* pMark, u8 offset, XY2ROBOT_CNT* absXY, STRUCT_MarkMatch* p_IdenWindow);
MARKS_EXT void DeletCloseMark(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark);
MARKS_EXT u32 CalSetMarkForEdge(MARK_SETS* SetMark, u16* TriEdge);
MARKS_EXT u8 Sort_From_Min_TO_Max(STRUCT_FILTER_TARGET_LITE* p_Scan_Mark, u8 p_Dist_Ang_Flag);
MARKS_EXT u32 Cal_AngDiff_With_PrioAngValue(u16* buf, u8 p_AverSize);
MARKS_EXT u8 Get_Match_Least_Mark_Num(u8 p_ScanMarkNum, u8 p_NavLeastMarkNum);
MARKS_EXT s8 Find_MatchMost_Pos(STRUCT_FILTER_TARGET_LITE* p_Nav_Mark,
                                XY2ROBOT_CNT* p_MappingXY_old,
                                MARK_SETS* p_SETMARK,
                                XYANG2ROBOT* p_PosOut,
                                XYANG2ROBOT* p_PosOld,
                                STRUCT_MarkMatch* p_IdenWindow);
// MARKS_EXT u32 CofTriMatch(u32 *CofTriofDect,COMBINE *comb,SqList *CofTriofSet);
MARKS_EXT u32 TriMatch(u8* NoofDub, u32* TriEdge, u16 size, u32* TriEdgeDect, u16 i);
MARKS_EXT u32 TriMatch1(u8* NoofDub, u16* TriEdge, u32* TriEdgeDect, u16 i);
MARKS_EXT u32 Sub(u16 a, u16 n);
MARKS_EXT u32 Match_Edge_of_Detect(u16* edge,
                                   MARK_SETS* SetMark,
                                   u16* TriEdge,
                                   u16 combno,
                                   STRUCT_FILTER_TARGET_LITE* pDist);
MARKS_EXT u32 Match_LocalMapEdge_of_Detect(u16* edge,
                                           MARK_SETS* SetMark,
                                           u16* TriEdge,
                                           u16 combno,
                                           STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                           LOCAL_MAP_INFO* p_LoaclMap);
MARKS_EXT int Match_Mark_By_SetTri(STRUCT_FILTER_TARGET_LITE* pDist,
                                   u16 p_size,
                                   u16 offset,
                                   u16* p_SetMark_Edge,
                                   MARK_SETS* p_SetMark);
MARKS_EXT u16 Match_Mark_By_SetTri1(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                    u16 p_size,
                                    u16 offset,
                                    MARK_SETS* p_SetMark);
MARKS_EXT u8 CopyNavMark(STRUCT_FILTER_TARGET_LITE* p_Nav_MarkNew,
                         STRUCT_FILTER_TARGET_LITE* p_Nav_MarkOld,
                         u8 p_MarkNum,
                         u8* p_offset,
                         STRUCT_EqlTri* p_EqlTri,
                         u8 p_EqlTriOffset);
MARKS_EXT int SelMarkByDistTri(STRUCT_FILTER_TARGET_LITE* pDist,
                               STRUCT_FILTER_TARGET_LITE* pDist2,
                               u32 MinDist,
                               u8 MarkNum);
MARKS_EXT u8 Find_Closest_Pos_to_SetPos(ROBOT_XY* p_SetPos,
                                        STRUCT_EqlTri* p_EqlTri,
                                        ROBOT_XY* p_Pos_Out,
                                        XYANG2ROBOT* p_EqlTri_Pos);
MARKS_EXT int CmpRobotPos(ROBOT_XY* old, ROBOT_XY* cur, INPUTSPEED* SPEED, u32 p_ScanOverTS);
MARKS_EXT u32 StoreMarkSets(MARK_SETS* marks, u16 addr);
MARKS_EXT u32 LoadMarkSets(MARK_SETS* marks, u16 addr);
MARKS_EXT u32 StoreRobotSets(ROBOT_XY* robot, u16 addr);
MARKS_EXT u32 LoadRobotSets(ROBOT_XY* robot, u16 addr);
MARKS_EXT u32 CalSideLength(u32 a, u32 b, u16 ang);
MARKS_EXT u16 Get_Multi(u8 p_data, s8 power);
MARKS_EXT void UART_SAVEDATA(u8 p_Cnt, u8 p_DataRecv, u8 p_In);
MARKS_EXT void UART_SendDATA(u8 p_Len, u8* p_SendData);
MARKS_EXT void UART_TEST(u8 p_Data_Recv, u8* p_u8data, u16 pRecvLen);
MARKS_EXT u8 FilterMark_ByMarkMaxMinDist(STRUCT_FILTER_TARGET_LITE* p_ScanMark);
MARKS_EXT void Renew_AbsCoor_XY(STRUCT_FILTER_TARGET* p_ScanMark,
                                ROBOT_XY* robot,
                                XY2ROBOT_CNT* absXY,
                                u8 p_MarkSize);
MARKS_EXT int PollRefMark(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, MARK_SETS* g_sMarkSets);
MARKS_EXT u32 PollLength(u32 len,
                         u8 offsetj,
                         MARK_XY* ref_target,
                         u16 offset,
                         u16 left_size,
                         u8* cur_pos,
                         MARK_SETS* g_sMarkSets,
                         u8* offsetout,
                         u8 poly_size,
                         u8 side_cnt,
                         u8* last);
MARKS_EXT int AlgTargetMatch(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, MARK_SETS* marks);
MARKS_EXT int CalRobot2Xy(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, ROBOT_XY robot);
MARKS_EXT void TransMark_Polar_to_RelDecare(STRUCT_FILTER_TARGET_LITE* pDist, u8 size);
MARKS_EXT int SortMarkByAng(STRUCT_FILTER_TARGET_LITE* p_Scan_Mark);
MARKS_EXT void CalAndSort_Ang_Distribution_Value(u8 p_SeltMarkNum,
                                                 STRUCT_FILTER_TARGET_LITE* p_Nav_Mark);
MARKS_EXT void Get_Combination(u8 p_Combine_N, u8 p_Combine_X);
MARKS_EXT void Cal_Ang_Distribution_Value(COMBINE* p_Comb,
                                          u8 p_SeltMarkNum,
                                          STRUCT_FILTER_TARGET_LITE* p_Nav_Mark);
MARKS_EXT u8 Select_MarkAng_Distribution_Most_Uniform(u8 p_SeltMarkNum,
                                                      STRUCT_FILTER_TARGET_LITE* p_Nav_Mark,
                                                      STRUCT_FILTER_TARGET_LITE* p_SeltMark);
MARKS_EXT int SelectMarkForTri(STRUCT_FILTER_TARGET_LITE* pDist, u8 size);
MARKS_EXT int CalPoly(STRUCT_FILTER_TARGET_LITE* pDist, u16 offset, u8 size);
MARKS_EXT int CalRobotXy_Sets(STRUCT_FILTER_TARGET* g_sFilterDist, MARK_SETS* g_sMarkSets);
MARKS_EXT u16 Cal_Laser_AbsAng(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, ROBOT_XY* robot);
MARKS_EXT u8 Find_MaxWidthPoint1(u16* Width_Carve,
                                 STRUCT_FILTER_TARGET* p_Filter,
                                 STRUCT_CARVE* p_CraveBuf);
MARKS_EXT void Renew_AbsCoor_XY1(XY_TO_RelCoor** p_ScanMark,
                                 XYANG2ROBOT* robot,
                                 XY2ROBOT_CNT* absXY,
                                 u8 p_MarkSize);
MARKS_EXT void xy2robot_test(void);
MARKS_EXT u32 CalRobotXy(u32 dist, u16 ang, MARK_XY* xy);
MARKS_EXT u32 Get_TS_Diff(TIMESTAMP p_TS_New, TIMESTAMP p_TS_Old);
MARKS_EXT u32 Robot2Xy(XY_TO_RelCoor* rob, ROBOT_XY robot, MARK_XY* xy, MARK_XY mark0);
MARKS_EXT u32 CalSideLength(u32 a, u32 b, u16 ang);
MARKS_EXT u32 SortMarkAng(STRUCT_FILTER_TARGET_LITE* pDist, u16 offset, u8 size);
MARKS_EXT u8 SelMarkByDist(STRUCT_FILTER_TARGET_LITE* pDist, STRUCT_FILTER_TARGET_LITE* pDist2);
MARKS_EXT void Get_Ang_Distribution_Value(u8 p_SeltMarkNum, STRUCT_FILTER_TARGET_LITE* p_Nav_Mark);
MARKS_EXT u32 FilterMarkAng(STRUCT_FILTER_TARGET* pDist, u16 offset, u8 size);
MARKS_EXT u32 SortMarkDist(STRUCT_FILTER_TARGET_LITE* pDist,
                           u32 dist_min,
                           u32 dist_max,
                           u16* max_pos);
MARKS_EXT void DistinguishMark2(u16* pResults_t, u32* pResults_d, u16 size, u8 no, u8 ang_mode);
// MARKS_EXT void DistinguishMark_Center(u16*pResults_t ,u16 *pResults_d, u16 size, u8 no, u8
// ang_mode);
MARKS_EXT void
DistinguishMark_CenterNew(u16* pResults_t, u16* pResults_d, u16 size, u8 no, u8 ang_mode);
// MARKS_EXT void DistinguishMark_Pulse(u16 *pResults_t ,u16 *pResults_d, u16 size,u8 no, u8
// ang_mode) ;
MARKS_EXT void
DistinguishMark_PulseNew(u16* pResults_t, u16* pResults_d, u16 size, u8 no, u8 ang_mode);
MARKS_EXT void CodeZeroCorrect(STRUCT_FILTER_TARGET* pDist);
// MARKS_EXT void DistinguishMark_Plan(u16 *pResults_t ,u16 *pResults_d, u16 size,u8 no, u8
// ang_mode);
MARKS_EXT void GetOutlineOfMarks(u16* pResults_t,
                                 u16* pResults_d,
                                 u16 size,
                                 u8 no,
                                 u8 ang_mode,
                                 u16* p_u16pluse_cut);
MARKS_EXT u32 xy2rob_ang(MARK_XY xy1, ROBOT_XY rob, MARK_XY xy2);
MARKS_EXT u32 Xy2Robot(MARK_XY* mark, ROBOT_XY robot, MARK_XY* xy2robot);
MARKS_EXT u32 Cal_PerMark_AbsAng(MARK_XY* p_SetMark_Pos, XYANG2ROBOT* p_Laser_Pos);
MARKS_EXT int CalMarksAng2Rob(MARK_SETS* pMark, ROBOT_XY robot);
MARKS_EXT int
MatchMarksByAng(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, ROBOT_XY* robot, MARK_SETS* pMark);
MARKS_EXT int SelectMarkByAng2(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, STRUCT_FPGAPOS* pFpgaPos);
MARKS_EXT int SelectMarkByAngCX5(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, u8 p_CombNum);
MARKS_EXT void Copy_Mark_To_NavMark_Buf(u8 p_CombNum,
                                        STRUCT_FILTER_TARGET_LITE* p_Selt_Nav_Mark,
                                        STRUCT_FILTER_TARGET_LITE* p_Nav_Mark);
MARKS_EXT int
SelectMarkByAngC53(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, STRUCT_FPGAPOS* FpgaPos);
MARKS_EXT int CalFirstPos(STRUCT_FILTER_TARGET_LITE* pDist, STRUCT_FPGAPOS* robot);
MARKS_EXT int CalMark2Xy(STRUCT_FILTER_TARGET_LITE* pDist, MARK_SETS* MarkSets, ROBOT_XY robot);
MARKS_EXT int
CalAbsCoordinatesofDec(STRUCT_FILTER_TARGET_LITE* pDist, MARK_SETS* pMark, ROBOT_XY* robot);
MARKS_EXT u16 Cal_MarkDot_ByDist_Theory(u32 dist);
MARKS_EXT int Tran_To_Float(STRUCT_FPGAPOS* p_psFpgaPos, u8 num);
MARKS_EXT int SeletFirstMark(STRUCT_EqlTri* Eqltri,
                             STRUCT_FILTER_TARGET_LITE* p_Scan_Mark,
                             STRUCT_FILTER_TARGET_LITE* p_NavMark,
                             u8* offset);
MARKS_EXT int CalAbsCoordinatesofFistMark(STRUCT_FILTER_TARGET_LITE* pDist2,
                                          STRUCT_EqlTri* Eqltri,
                                          u8 i,
                                          MARK_SETS* pMark,
                                          XY2EqlTri* xy2Eqltri);
MARKS_EXT int
MatchEdge(STRUCT_EqlTri* EqlTri, XY2EqlTri* xy2Eqltri, STRUCT_FILTER_TARGET_LITE* pDist, u8 i);
MARKS_EXT void mark_test(void);
MARKS_EXT u32 Robot2Xy2(u32 dist, u16 ang, ROBOT_XY robot, MARK_XY* xy);
MARKS_EXT void NetSendDly(void);
MARKS_EXT int CalAngOffset(INPUTSPEED l_sSpeed, STRUCT_FILTER_TARGET_LITE* pDist);
MARKS_EXT u64 Calculate_Point2Point_Dist_Int(int p_Point_1, int p_Point_2);
MARKS_EXT void FixScanAng(STRUCT_FILTER_TARGET* pDist);
MARKS_EXT int Filter_Mark_By_Ref(u16* PulseWidth, u16* Dist);
// MARKS_EXT void DistinguishMark_CenterAfter(STRUCT_FILTER_TARGET *pDist, u16 *pResults_d);
MARKS_EXT void
DistinguishMark_CenterAfter(STRUCT_FILTER_TARGET* pDist, u16* pResults_t, u16* pResults_d);
MARKS_EXT u32 CalPlanMarkAngle(u32 dist, u16 len);
MARKS_EXT u32 CopyFilterMark(STRUCT_FILTER_TARGET* pDistOld, STRUCT_FILTER_TARGET* pDistNew);
MARKS_EXT void FixMarkAng_PINGXINGZHOU(STRUCT_MARK_INFO* p_MarkInfo);
MARKS_EXT void FixScanAngAfter(STRUCT_FILTER_TARGET* pDist);
MARKS_EXT int CalAngOffsetBefor(STRUCT_FILTER_TARGET* pDist);
MARKS_EXT u32 FindSpeed(u16 ang, INPUTSPEED* speed);
MARKS_EXT int CalAngOffsetBySpeed(STRUCT_FILTER_TARGET* pDist, ROBOT_XY* robot);
MARKS_EXT int CalAngOffsetBySpeed360(STRUCT_FILTER_TARGET* pDist, ROBOT_XY* robot);
MARKS_EXT int CalAngOffsetBySpeed360TOZERO(STRUCT_FILTER_TARGET* pDist, ROBOT_XY* robot);
MARKS_EXT void CalAngOffsetBySpeed360TOHalf(STRUCT_FILTER_TARGET* pDist,
                                            ROBOT_XY* robot,
                                            INPUTSPEED* p_Speed,
                                            float p_CorrTime);
MARKS_EXT u16 LaserPose_CompensationBySAng(INPUTSPEED* p_SpeedIn,
                                           XYANG2ROBOT* p_LaserPose,
                                           float p_CorrTime);
MARKS_EXT void LaserPose_CompensationBySLine(SPEED* p_SpeedIn,
                                             XYANG2ROBOT* p_LaserPose_In,
                                             XYANG2ROBOT* p_LaserPose_Out,
                                             float p_CorrTime);
MARKS_EXT void
Corr_Speed(INPUTSPEED* p_SpeedIn, float p_CorrTime, SPEED* p_SpeedOut, float p_LastMoveTime);
MARKS_EXT void Judge_VirtualPos(ROBOT_XY* p_CurPos, ROBOT_XY* p_OldPos);
MARKS_EXT u8 Match_SetMark_By_LaserPose(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                        ROBOT_XY* p_LaserPose,
                                        XY2ROBOT_CNT* p_MappingXY_old,
                                        MARK_SETS* p_SETMARK,
                                        u8 p_NavLeastMarkNum,
                                        STRUCT_MarkMatch* p_IdenWindow);
MARKS_EXT void Round_Up_To_Int(float* p_In, s32* p_Out);
MARKS_EXT void ByteToHexStr(const unsigned char* source, char* dest, int sourceLen);
MARKS_EXT u32 Trans_SendPos_To_Ts(ROBOT_XY* PosSend, ROBOT_XY* PosCalNew, INPUTSPEED* speed);
MARKS_EXT void
Find_ScanMark(u16* l_pu16pulse, u16* l_pu16pulse_aver, u16* l_pu16dist, float p_CorrTime);
MARKS_EXT void Find_MaxMin_Dist(u8 l_u8cnt,
                                u16* Dist,
                                STRUCT_FILTER_TARGET* p_Filter,
                                u16* max_dist,
                                u16* min_dist);
MARKS_EXT void Get_IdenWindow_Corr(STRUCT_MarkMatch p_IdenW_Set, STRUCT_MarkMatch* p_IdenW_Corr);
MARKS_EXT u32 U32ToHEXStr(u32 x, char* s);
MARKS_EXT u32 U16ToHEXStr(u16 x, char* s);
MARKS_EXT u32 HexStr2U32(void* Str);
MARKS_EXT int U32ToStr(u32 x, char* s, u8 len);
MARKS_EXT u32 Moving_Average(u16* p_Width_In,
                             u16* p_Width_Out,
                             u8 p_cnt,
                             u16 p_ScanDotNum);  //多点滑动平均
MARKS_EXT void Get_MaxWidth(u8 p_u8MaxMarkNum,
                            u8 p_MarkNum,
                            u16* p_u16start,
                            u16* p_u16end,
                            u16* p_Width_Carve,
                            STRUCT_FILTER_TARGET* p_Filter);
MARKS_EXT u8 Find_MaxWidthPoint(u16* Width_Carve, STRUCT_FILTER_TARGET* p_Filter);
MARKS_EXT u8
Save_MarkBreakPoint(u8 l_u8cnt, u8 p_u8MoveBit, u16 l_u16i_cyc, u16 l_u16cnt, u16* p_u16res);
MARKS_EXT u16 Find_ScanData_StaPoint(u16 p_ScanPointNum, u16* Width_Carve, u16 p_ZeroNum);
MARKS_EXT u32 Find_StaEnd_Point_Slope(u16* Width_Aver,
                                      u8 MarkNum,
                                      STRUCT_FILTER_TARGET* pDist,
                                      u16* dist,
                                      u16* Width);
MARKS_EXT u8 Load_Marks(MARKLAYER_ADDRESS* p_LayerAddr, u16 p_Layer, MARK_SETS* p_MarkSet);
MARKS_EXT void Init_List(u8 l_u8i, MappingList* p_MappingList, u8 p_Size);
MARKS_EXT bool Points_Diff_Cmp(u32 p_Diff_In, u32 p_Diff_Cmp_In);
MARKS_EXT u16 Get_IdentWindow(u16 p_MarkDist, STRUCT_MarkMatch* p_IdenWindow);
MARKS_EXT u8 Return_Match_SetMarkNum(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                     XY2ROBOT_CNT* p_MappingXY_old,
                                     STRUCT_MarkMatch* p_IdenWindow);
MARKS_EXT u8 Return_Match_KNN_Mul(MARK_XY* psMinDistPoint, u32* nMinDis);
MARKS_EXT u16 Match_LocalMapEdge_of_Detect1(u16* edge,
                                            MARK_SETS* SetMark,
                                            u16 combno,
                                            STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                            LOCAL_MAP_INFO* p_LoaclMap);
MARKS_EXT u16 Match_Edge_of_Detect1(u16* edge,
                                    MARK_SETS* p_SetMark,
                                    u16 combno,
                                    STRUCT_FILTER_TARGET_LITE* p_NAV_Mark);

MARKS_EXT s8 Match_Tri_ThirdMark_LocalMap1(MARK_XY** p_MatchMark,
                                           u16* p_DetectEdge,
                                           u16* p_EqlTriCnt,
                                           MARK_SETS* p_SetMark,
                                           u16 combno,
                                           STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                           LOCAL_MAP_INFO* p_LoaclMap);
MARKS_EXT u16 Find_80Percent_Sta_Point(u16* Width_Aver, u16 Max_Width_Point);
MARKS_EXT u16 Find_80Percent_End_Point(u16* Width_Aver, u16 Max_Width_Point);
MARKS_EXT int Filter_Mark_By_Ref1(u16* PulseWidth, u16* Dist, u16* p_RefBuf);
#endif
