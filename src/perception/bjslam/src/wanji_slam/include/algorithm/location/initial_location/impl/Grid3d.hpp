#pragma once
#include "../Grid3d.h"

namespace wanjilocal {
template <typename PointMap> void Grid3d<PointMap>::setMapType(const MAP_DIMENSION& p_mapType)
{
    c_mapType_ = p_mapType;
}

template <typename PointMap> void Grid3d<PointMap>::setResolution(float p_fResolution)
{
    c_fResolution_ = p_fResolution;
}

template <typename PointMap>
bool Grid3d<PointMap>::open(typename pcl::PointCloud<PointMap>::Ptr p_pMapMerge,
                            const double p_dSensorDev)
{
    try
    {
        init_();

        c_pcMapCloud_ = p_pMapMerge;  // que  硬拷贝？
        c_mapVoxelGrid_.setInputCloud(c_pcMapCloud_);
        c_mapVoxelGrid_.filter(*c_pcMapCloudDS_);

        computeBoxAndGrid_(p_dSensorDev);
    }
    catch (std::exception& e)
    {
        printf("[Grid3d].open: %s", e.what());
        return false;
    }

    return true;
}

template <typename PointMap> void Grid3d<PointMap>::computeBoxAndGrid_(const double p_dSensorDev)
{
    PointMap l_minPoint, l_maxPoint;
    pcl::getMinMax3D(*c_pcMapCloudDS_, l_minPoint, l_maxPoint);
    //八叉树地图，分辨率0.3m，xyz左右边界外扩5X0.3m
    c_totalMapCloudInfo_ = extendBoxInfo_(l_minPoint, l_maxPoint, c_fResolution_, 5);
    //分配3d网格,得到栅格的ijk及强度，过滤掉强度<1cm
    c_gridInfo_ = computeGrid(c_totalMapCloudInfo_, p_dSensorDev, c_pcMapCloudDS_);
}

template <typename PointMap>
PointCloudInfoS Grid3d<PointMap>::extendBoxInfo_(PointMap p_minP,
                                                 PointMap p_maxP,
                                                 float p_fResol,
                                                 float p_fResolmul)
{
    PointCloudInfoS l_pointInfo;
    float l_expSize = p_fResolmul * p_fResol;
    l_pointInfo.m_resol = p_fResol;
    l_pointInfo.m_min_x = p_minP.x - l_expSize;
    l_pointInfo.m_min_y = p_minP.y - l_expSize;
    l_pointInfo.m_min_z = p_minP.z - l_expSize;
    l_pointInfo.m_max_x = p_maxP.x + l_expSize;
    l_pointInfo.m_max_y = p_maxP.y + l_expSize;
    l_pointInfo.m_max_z = p_maxP.z + l_expSize;
    return l_pointInfo;
}

/*返回 grid_info
 *grid_info存储1个ceil(点云集指针：pcl::PointCloud<pcl::PointXYZI>::Ptr)
 *每个ceil中压入所有概率>0.01的栅格，格式也是pcl::PointXYZ，xyz表示栅格下标，i表示概率
 */
template <typename PointMap>
Grid3dInfo::Ptr Grid3d<PointMap>::computeGrid(const PointCloudInfoS& p_boxInfo,
                                              const double p_dSensorDev,
                                              typename pcl::PointCloud<PointMap>::Ptr p_pcCloudIn)
{
    Grid3dInfo::Ptr l_gridInfo(new Grid3dInfo());
    l_gridInfo->sensor_dev = p_dSensorDev;  // 3D激光传感器的偏差（米）
    l_gridInfo->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());
    /* 分配三维3D网格 */
    float l_fOctoSizeX = p_boxInfo.m_max_x - p_boxInfo.m_min_x;
    float l_fOctoSizeY = p_boxInfo.m_max_y - p_boxInfo.m_min_y;
    float octo_size_z = p_boxInfo.m_max_z - p_boxInfo.m_min_z;
    // lin_test

    uint32_t l_uiSizeX = static_cast<uint32_t>(ceil(l_fOctoSizeX / p_boxInfo.m_resol));
    uint32_t l_uiSizeY = static_cast<uint32_t>(ceil(l_fOctoSizeY / p_boxInfo.m_resol));

    uint32_t l_uiSizeZ = 1;

    if (c_mapType_ == MAP_3D)
        l_uiSizeZ = static_cast<uint32_t>(ceil(octo_size_z / p_boxInfo.m_resol));

    uint32_t l_uiStepY = l_uiSizeX;
    uint32_t l_uiStepZ = l_uiSizeX * l_uiSizeY;

    const auto l_gridSize = l_uiSizeX * l_uiSizeY * l_uiSizeZ;

    std::vector<pcl::PointXYZI> l_ceiltmp;
    // gridtmp.resize(grid_size);
    l_ceiltmp.resize(l_gridSize);
    /* Setup kdtree */
    pcl::KdTreeFLANN<PointMap> l_kdtree;
    l_kdtree.setInputCloud(p_pcCloudIn);

    /* 计算到栅格最近点的距离 */
    // 1 / (0.3 * sqrt(2PI))   = 1 / (0.3 * 2.5)
    const float l_gauss_const1 = static_cast<float>(1. / (l_gridInfo->sensor_dev * sqrt(2 * M_PI)));
    // 1 / (2*0.3*0.3)         = 1 / (0.3 * 0.6)
    const float l_gauss_const2 =
        static_cast<float>(1. / (2. * l_gridInfo->sensor_dev * l_gridInfo->sensor_dev));

    uint32_t l_uiIndex;
    float l_fDist;
    PointMap l_searchPoint;
    std::vector<int> l_pointIdxNknSearch(1);
    std::vector<float> l_pointNknSquaredDistance(1);
    // std::cout << "gridsize:z->" << size_z << std::endl;
    // std::cout << "gridsize:y->" << size_y << std::endl;
    // std::cout << "gridsize:x->" << size_x << std::endl;

    pcl::PointXYZI l_ceilValue;
    //得到一条线上每个栅格的三维栅格ijk 及
    // intensity，intensity为距离该栅格某猜测点的最近距离为基础得到的值得
    for (uint32_t iz = 0; iz < l_uiSizeZ; ++iz)
    {
        // std::cout << "iz : " << iz << std::endl;
        for (uint32_t iy = 0; iy < l_uiSizeY; ++iy)
        {
            for (uint32_t ix = 0; ix < l_uiSizeX; ++ix)
            {
                //最小点开始以分辨率为步长，作为目标点 ==遍历栅格
                //最小点，已经减了一次分辨率*5,&& 不能保证这个点正好是地图点||栅格交界
                l_searchPoint.x = p_boxInfo.m_min_x + (ix * p_boxInfo.m_resol);
                l_searchPoint.y = p_boxInfo.m_min_y + (iy * p_boxInfo.m_resol);

                if (c_mapType_ == MAP_3D)
                    l_searchPoint.z = p_boxInfo.m_min_z + (iz * p_boxInfo.m_resol);
                else
                    l_searchPoint.z = 0;

                l_uiIndex = ix + iy * l_uiStepY + iz * l_uiStepZ;

                //以距离 该栅格内某猜测点 最近的地图点距 为基础，得到栅格概率
                if (l_kdtree.nearestKSearch(
                        l_searchPoint, 1, l_pointIdxNknSearch, l_pointNknSquaredDistance)
                    > 0)
                {
                    //最近距离
                    l_fDist = l_pointNknSquaredDistance[0];
                    // gridtmp[index].dist = dist;
                    // gridtmp[index].prob = gauss_const1 * expf(-dist * dist * gauss_const2);
                    l_ceilValue.x = ix;
                    l_ceilValue.y = iy;
                    l_ceilValue.z = iz;
                    l_ceilValue.intensity =
                        l_gauss_const1 * expf(-l_fDist * l_fDist * l_gauss_const2);
                    l_ceiltmp[l_uiIndex] = l_ceilValue;
                }
                else
                {
                    // gridtmp[index].dist = -1.0;
                    // gridtmp[index].prob = 0.0;
                    l_ceilValue.x = ix;
                    l_ceilValue.y = iy;
                    l_ceilValue.z = iz;
                    l_ceilValue.intensity = 0;
                    l_ceiltmp[l_uiIndex] = l_ceilValue;
                }
            }
        }
    }
    //过滤 栅格概率<1cm的栅格
    // float l_fThres = l_gauss_const1 * expf(-sen * l_fDist * l_gauss_const2);
    for (uint32_t i = 0; i < l_gridSize; i++)
    {
        if (l_ceiltmp[i].intensity >= 0.01)  // dist约0.3m
        {
            // grid_info->grid.push_back(gridtmp[i]);
            l_gridInfo->ceil->push_back(l_ceiltmp[i]);  // lin 优化时间效率
        }
    }
    return l_gridInfo;
}

template <typename PointMap> void Grid3d<PointMap>::init_(void)
{
    c_pcMapCloud_.reset(new pcl::PointCloud<PointMap>());
    c_pcMapCloudDS_.reset(new pcl::PointCloud<PointMap>());
    c_mapVoxelGrid_.setLeafSize(c_fResolution_, c_fResolution_, c_fResolution_);
}

}  // namespace wanjilocal
