/*
 * @Author: your name
 * @Date: 2021-04-25 10:51:41
 * @LastEditTime: 2022-03-23 19:10:52
 * @LastEditors: <PERSON><PERSON>
 * @Description: amcl定位算法
 * @FilePath: /catkin_ws/src/wanji_amcl3d/include/amcl/amcl.h
 */
#ifndef AMCL_H
#define AMCL_H

#include "ParticleFilter.h"
#include "PointCloudTools.h"
#include "poseFunc.h"

namespace wanjilocal {
//定位模式，粒子滤波或者cartographer方法
enum PROCESS_MODE { PARTICLE_MODE = 0, CARTOGRAPHER_MODE };

enum GLOBAL_METHOD { GLOBAL_ALL_ONCE, GLOBAL_ONE_BY_ONE };

template <typename PointMap, typename PointScan> class Amcl2D {
  public:
    typedef typename pcl::PointCloud<PointMap>::Ptr cloudMapPtr;    //地图
    typedef typename pcl::PointCloud<PointScan>::Ptr cloudScanPtr;  //帧点云
    //    typedef typename Filter<PointT>::PointCloud PointCloud;
    //   typedef typename PointCloud::Ptr PointCloudPtr;
    //   typedef typename PointCloud::ConstPtr PointCloudConstPtr;
    //   typedef boost::shared_ptr< VoxelGrid<PointT> > Ptr;
    //   typedef boost::shared_ptr< const VoxelGrid<PointT> > ConstPtr;

  public:
    Amcl2D();
    ~Amcl2D();

    /**
     * @description: 设置模拟单线2D激光slam的点云高度范围；
     * @param {float} p_fMin 最低高度
     * @param {float} p_fMax 最大高度
     * @return {*}
     */
    void setCloudHeightRange(float p_fMin, float p_fMax);

    //设置定位方法，默认为粒子滤波
    void setProcessMode(const PROCESS_MODE& p_mode = PARTICLE_MODE);

    //设置计划采用的地图类型，2D或者3D
    void setMapType(const MAP_DIMENSION& p_mapType = MAP_2D);

    /**
     * @description: 设置定位地图，建图路径信息
     * @param {const cloudMapPtr} &p_pcMap 点云地图
     * @return {*}
     */
    bool setCloudMap(const cloudMapPtr& p_pcMap);
    // bool initData();

    /**
     * @description:  设置初始位姿
     * @param {double} p_dInitX  X坐标
     * @param {double} p_dInitY Y坐标
     * @param {double} p_dInitYarm  yarm角度
     * @param {const float} p_fXYdev 坐标方差
     * @param {const float} p_fAdev 角度方差, 默认半个PI
     * @return {*}
     */
    void setInitialPose(double p_dInitX,
                        double p_dInitY,
                        double p_dInitYarm,
                        const float p_fXYdev = 0.1,
                        const float p_fAdev = (M_PI_2));

    //设置初始位姿
    void setMultiInitPose(const std::vector<wj_slam::s_InitPose>& p_vInitPoses);

    //设置存在多个初始位置的处理策略，（1）全部一次进行处理；或者（2）分别进行处理，然后再比较
    void setGlobalType(int p_iType = GLOBAL_ALL_ONCE);

    //设置角度切分分辨率，单位：弧度
    void setYawResolution(float p_fYawReso = 0.3);

    /**
     * @description:  根据帧点云进行定位
     * @param {const cloudScanPtr} &p_pcScan 当前帧点云
     * @param {double} &p_dTx 返回值 平移量
     * @param {double} &p_dTy 返回值 平移量
     * @param {double} &p_dTheta 返回值 航向角，单位：弧度
     * @return {*}
     */
    bool amclCallBack(const cloudScanPtr& p_pcScan, double& p_dTx, double& p_dTy, double& p_dTheta);

    /**
     * @description: 假如已经有栅格地图，设置栅格地图信息
     * @param {const PointCloudInfoS} &pcInfo 栅格地图边界信息
     * @param {Ptr} p_pGrid3dInfo 栅格点云地图
     * @return {*} 检测栅格地图是否为非法指针，若非法返回false
     */
    bool setGridMapInfo(const PointCloudInfoS& pcInfo, Grid3dInfo::Ptr p_pGrid3dInfo);

    //设置评分时有效重叠阈值
    void setOverlapThresh(float p_fOverlap = 0.6);

    void setScoreThresh(double p_dScore = 1.1);

    void setGridmapResolution(double f_dGridReso = 0.3);

    std::string c_sTestOutDir;

    /***********全局amcl************/
    void sampleInitPoseFromPath(const cloudMapPtr& p_pcPath,
                                std::vector<wj_slam::s_InitPose>& p_vInitPoses,
                                float p_fSampleSize = 2.0f,
                                float p_fDevXY = 1.0f,
                                float p_fA = 0.0f,
                                float p_fDevA = M_PI);

  public:
    /*=====ROS显示的接口函数====*/

    //获取栅格地图信息，需要设置地图完成后才能获取
    void getGridMapInfo(PointCloudInfoS& pcInfo, Grid3dInfo::Ptr& p_pGrid3dInfo);

    Eigen::Quaterniond getQuaCurr()
    {
        return c_qMapCurr_;
    }

    Eigen::Vector3d getTransCurr()
    {
        return c_tMapCurr_;
    }

    cloudMapPtr getMap()
    {
        return c_pcMapMerge_;
    }

    cloudScanPtr getScanCloud()
    {
        return c_pcFeatureLast_;
    }

    double getScore()
    {
        return c_dMatchCore_;
    }

    std::vector<s_Particle> getParticles() const
    {
        return c_pfPF_.getParticles();
    }

  private:
    //初始化指针
    void initValue_();
    //高程坐标投影为同一个值
    template <typename T>
    void projectZcoor_(float p_fZ, typename pcl::PointCloud<T>::Ptr p_pcCloud);
    //过滤高度范围之外的点
    template <typename T>
    void filterByheight_(float p_fMinH, float p_fMaxH, typename pcl::PointCloud<T>::Ptr p_pcCloud);
    // amcl初始化，生成栅格概率地图
    bool amclInitFunc_();
    //定位
    void amclFunc_();
    //根据初值生成一些列方向的位姿初始值
    void fillAmclPose_(Eigen::Vector3d p_t, Eigen::Quaterniond p_q);

    void fillMultiPose_();

    //更新位姿
    void updateAmclPose_();
    //通过类似于梯度下降的方法进行定位,不适合全局定位
    void predictByCartoOptimize_();

    void outPutBadScore();  //输出评分不准确的文件

  private:
    //定位方法
    PROCESS_MODE c_processMode_;  //定位模式

    //当前帧定位
    Eigen::Quaterniond c_qMapCurr_;
    Eigen::Vector3d c_tMapCurr_;

    //上一帧定位
    Eigen::Quaterniond c_qMapLast_;
    Eigen::Vector3d c_tMapLast_;

    //扩展到全局定位
    std::vector<wj_slam::s_InitPose> c_vInitPoses_;
    std::vector<wj_slam::s_InitPose> c_vInitPosesDoing_;  //正在处理的

    //各类地图
    typename pcl::PointCloud<PointMap>::Ptr c_pcMapMerge_;    //点云特征地图
    typename pcl::PointCloud<PointMap>::Ptr c_pcMapMergeDS_;  //点云特征地图

    typename pcl::PointCloud<PointScan>::Ptr c_pcFeatureLast_;    //当前帧点云
    typename pcl::PointCloud<PointScan>::Ptr c_cpFeatureLastDS_;  //降采样当前帧点云

    Grid3dInfo::Ptr c_subGridInfo_;  //子地图生成的栅格地图//

    bool c_bLoadMap_ = false;  //地图载入标志

    double c_dTimeMsgCur_ = 0.0;  //当前帧时间戳

    Grid3d<PointMap> c_mapGrid3d_;  //生成栅格地图的实例
    std::vector<AMCLPOSE> c_vecAmclPose_;
    s_Particle c_meanP_;
    wanjilocal::ParticleFilter<PointScan> c_pfPF_;  //粒子滤波实例

    std::mutex c_subMapBuf_;  //子地图更新锁

    std::vector<int> c_vecPathPointInd_;  //定位附近范围路径点集合

    float c_fMinHeight_;   //利用高度范围进行过滤
    float c_fMaxHeight_;   //最大高度
    double c_dSensorDev_;  //传感器精度 default: 0.3

    float c_fXYDev_;    // dev of  xy
    float c_fADev_;     //航向角方差
    float c_fYawReso_;  //初始化粒子航向角切分的分辨率。0.3

    double c_dMatchCore_;      //匹配分数
    MAP_DIMENSION c_mapType_;  //地图模式，2d还是3d

    double c_dLocationThresh_;  //匹配成功评分阈值
    int c_globalType_;          //全局定位处理策略
};

template <typename PointMap, typename PointScan> class GlobalAmcl {
  public:
    typedef typename pcl::PointCloud<PointMap>::Ptr cloudMapPtr;    //地图
    typedef typename pcl::PointCloud<PointScan>::Ptr cloudScanPtr;  //帧点云

    GlobalAmcl();

    ~GlobalAmcl();

    void initPara();

    void setCloudMap(const cloudMapPtr& p_pcCorner,
                     const cloudMapPtr& p_pcSurface,
                     const cloudMapPtr& p_pPath);

    // 路径+前缀：/home/<USER>/catkin_ws/src/wanji_amcl/map/lowpark
    //自己读取文件，调试用
    bool loadMap(const std::string& p_sMapFile);

    //降采样路径点
    void downSamplePath(typename pcl::PointCloud<PointMap>::Ptr p_cloudIn,
                        typename pcl::PointCloud<PointMap>::Ptr& p_cloudOut);
    // 合并多个子栅格地图
    void spliceSubGridMap(const std::vector<Grid3dInfo::Ptr>& p_vSubGridMap,
                          const wj_slam::s_InitPose& p_pose,
                          Grid3dInfo::Ptr& p_subGrid);

    void divideGridMap(Grid3dInfo::Ptr p_gridMap,
                       const PointCloudInfoS& p_gridMapEdge,
                       std::vector<Grid3dInfo::Ptr>& p_vSubGridMap);

    bool
    beginLocation(const cloudScanPtr& p_pcScan, double& p_dTx, double& p_dTy, double& p_dTheta);

    void getSubCloud(typename pcl::PointCloud<PointMap>::Ptr f_cloudIn,
                     typename pcl::PointCloud<PointMap>::Ptr f_cloudOut,
                     float f_min,
                     float f_max);

    void getSubMap(const typename pcl::PointCloud<PointMap>::Ptr p_wholeMap,
                   const PointMap& f_pt,
                   float p_radious,
                   typename pcl::PointCloud<PointMap>::Ptr p_subMap);

    void initValue();

  private:
    boost::shared_ptr<wanjilocal::Amcl2D<PointMap, PointScan>> c_pAmcl_;
    boost::shared_ptr<wanjilocal::Amcl2D<PointMap, PointScan>> c_pAmclLocal_;

    typename pcl::PointCloud<PointMap>::Ptr c_pcMapMerge_;
    typename wanjilocal::Amcl2D<PointMap, PointScan>::cloudMapPtr c_pcPath_;
    typename pcl::PointCloud<PointMap>::Ptr c_pcPathDS_;  //路径点下采样
    typename pcl::KdTreeFLANN<PointMap>::Ptr c_kdtreePathDS;

    typename pcl::PointCloud<PointScan>::Ptr c_pcFeatureLast_;
    bool c_bLoadMap_;

    std::vector<wj_slam::s_InitPose> c_vInitPoses;  //从路径点获取的初始位置

    Grid3dInfo::Ptr c_totalGridInfo_;  //栅格地图，全图
    PointCloudInfoS c_gridMapEdge;     //栅格地图边界信息
    std::vector<Grid3dInfo::Ptr> c_vSubGridMap_;

    PointCloudInfoS c_gridMapEdge2_;               //栅格地图边界信息 第二层amcl
    std::vector<Grid3dInfo::Ptr> c_vSubGridMap2_;  //第二层amcl
};
}  // namespace wanjilocal

#ifdef WJSLAM_NO_PRECOMPILE
#    include "impl/amcl.hpp"
#    include "impl/globalAmcl.hpp"
#else
#    define WJSLAM_Amcl2D(T, P) template class wanjilocal::Amcl2D<T, P>;
#    define WJSLAM_GlobalAmcl(T, P) template class wanjilocal::GlobalAmcl<T, P>;
#endif
#endif