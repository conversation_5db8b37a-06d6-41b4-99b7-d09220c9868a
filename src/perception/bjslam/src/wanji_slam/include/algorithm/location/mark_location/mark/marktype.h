#pragma once
#ifndef __marktype_H__
#    define __marktype_H__
//#include <sys/types.h>
//#include <stm32f4xx.h>
//#include "config.h"
// #ifdef	__type_C
#    define type_EXT
// #else
// #define	type_EXT	extern
// #endif
#    include "common/common_ex.h"
typedef unsigned char u8;
typedef char s8;
typedef unsigned short u16;
typedef short s16;
typedef unsigned int u32;
typedef int s32;
typedef long u64;
typedef long unsigned int size_t;
typedef struct sFactorCorr
{
    u8 m_u16DistSub;
    u8 m_s16ArgOfCode;
} sFactorCorr;

typedef struct sSysPib
{
    u8 m_u16MarkRadio;                    // 80 靶标尺寸
    u8 m_u16MarkType;                     // 0
    u8 m_u16WorkMode_NavOrMappingOrMark;  // 0
    u16 m_u16MarkDistinguish;             // 0
    u16 m_u16MarkReflectivity;            // 0
    u16 m_u16MarkDotNumPct;               // 10 靶标点数比例
    u16 m_u16PianXinCorrEN;               // 0
    u16 m_u16WDogConfig;                  // 0
} sSysPib;

#    define __IO                     //
#    define OS_STK int               //
#    define TASK_TARGET_STACKSIZE 1  //
#    define OS_CPU_SR int            //
#    define g_u8AngMode 1
#    define DOT_NUM 600
#    define PING 1                    //
#    define PONG 1                    //
#    define ADDR_FLASH_REFLEX_DATA 1  //

//#pragma pack (4)

#    define FLASHDATAENABLE 0xaa55

#    define SYSMODE_ZEROORDISC_ZERO 0
#    define SYSMODE_ZEROORDISC_DISC 1

#    define SYSMODE_SINGLEORSCANF_SCANF 1
#    define SYSMODE_SINGLEORSCANF_SINGLE 0

#    define SYSMODE_NavOrMappingOrMark_MARK 1
#    define SYSMODE_NavOrMappingOrMark_MAPPING_P 2
#    define SYSMODE_NavOrMappingOrMark_MAPPING_N 3
#    define SYSMODE_NavOrMappingOrMark_NAV 4
#    define SYSMODE_NavOrMappingOrMark_PowerOff 5
#    define SYSMODE_NavOrMappingOrMark_SCANF 6

#    define SYSMODE_SENDDATA_REFLECTOR 0
#    define SYSMODE_SENDDATA_SCAN_REFLECTOR 1
#    define SYSMODE_SENDDATA_SCAN 2

#    define SYSMODE_SENDDATATYPE_DIST 0
#    define SYSMODE_SENDDATATYPE_ANG 1
#    define SYSMODE_SENDDATATYPE_ECHO 2
#    define SYSMODE_SENDDATATYPE_DIST_ANG 3
#    define SYSMODE_SENDDATATYPE_DIST_ECHO 4
#    define SYSMODE_SENDDATATYPE_ANG_ECHO 5
#    define SYSMODE_SENDDATATYPE_DIST_ANG_ECHO 6

#    define SYSMODE_CORRRORUNCORR_CORR 0
#    define SYSMODE_CORRRORUNCORR_UNCORR 1

#    define WHOLE_BASE_CORR 1000        //整体修正基准，为100、1000、10000
#    define WHOLE_FACTOR_CORR 1000      //整体修正系数，距离*该参数/m_u16WholeBaseCorr
#    define WHOLE_OFFSET_CORR ((s16)0)  //整体偏移修正，距离+该参数

#    define FPGA_ERROR_NORMAL 0   //正常
#    define FPGA_ERROR_NOWAVE 2   //无回波信号
#    define FPGA_ERROR_DIFFNUM 3  //上升沿、下降沿数量不一致
#    define FPGA_ERROR_BIGDISC 4  //距离大于65
#    define FPGA_ERROR_NEGDATA 5  //负数
#    define FPGA_ERROR_NOSTART 1  //上升沿、下降沿数量不一致

#    define CHANNEL_SEL_H 0
#    define CHANNEL_SEL_L 1
#    define CHANNEL_SEL_ALL 2

//#define M_PI 3.1415926
#    define Rad_Per_10mDeg M_PI / 18000.0
#    define TenmDeg_Per_Rad 18000.0 / M_PI
#    define MAX_SIZE 500

#    define DEG_PERSCAN 36000
#    define DEG_HALFSCAN 18000

#    define DOTNUM_7200 2
#    define DOTNUM_3600 1

#    define ANGMODE_005 1
#    define ANGMODE_010 2

#    define FPGAMODE_P1 1
#    define FPGAMODE_P2 2
#    define FPGAMODE_P3 3
#    define FPGAMODE_P4 4
#    define FPGAMODE_AVER 5
#    define FPGAMODE_TEST 6

#    define TARGET_MAX 32  // wen old 32
#    define MAPPING_MAX 40

#    define POS_AVER_CNT 10

#    define COMB_MAX 500
#    define NAV_MARK_SIZE 255
#    define EQLTRI_SIZE 500

#    define FIX_SIZE 50
#    define REFLEX_SIZE 128

#    define SYNCMODE_NO 0       //没有IO同步
#    define SYNCMODE_CYCLE 1    //周期触发IO
#    define SYNCMODE_COMMAND 2  //指令触发IO

#    define LOCATION_2MARKS_DIS 0  // 2个靶定位关闭
#    define LOCATION_2MARKS_EN 1   // 2个靶定位开启

#    define COORDINATE_YTPE_ABS 1        //绝对坐标系
#    define COORDINATE_YTPE_RELAVTIVE 0  //相对坐标系

#    define ISMARK 1
#    define NOTMARK 0
#    define ISMARK_TOOCLOSE 2
#    define ISMARK_COVERED 3

#    define MATCH_SUCCESS 0
#    define MATCH_FAILED 1

#    define POSSET_NOTSET 0
#    define POSSET_ROUGH 1
#    define POSSET_EXACT 2

#    define TIMESTAMP u32
#    define STRUCT_SIZE_ASCPROTOCOL sizeof(ASCPROTOCOL)

//靶标类型的宏定义
#    define MARK_TYPE_CYC 0
//#define MARK_TYPE_SQU	1
#    define MARK_TYPE_FACE 2
#    define MARK_COUNTIUEZERO_NUM 3

#    define MIDPCLEN 7200

#    define INTENSITYMAX 255
#    define INTENSITYMIN 10
#    define INTENSITYTH 220

// 全脉宽
// #    define INTENSITYMAX 9500
// #    define INTENSITYMIN 2000
// #    define INTENSITYTH 5500

/**
 * @brief 靶标中线数据结构体
 *
 */
typedef struct _NAVDATABGL
{
    u16 LDdist[MIDPCLEN];   //激光测距值
    u16 PLWidth[MIDPCLEN];  //脉冲宽度
    u16 REL[MIDPCLEN];      //反射率
} NAVDATABGL;

typedef struct _ASCPROTOCOL
{
    u8 m_u8Size;
    u8 m_u8CmdType[8];
    u8 m_u8Cmd[20];
    u8 m_u32param[5][12];
} ASCPROTOCOL;

// typedef struct List *List1;
// typedef List1 Point;
// typedef struct _List
//{
//	u8 m_u8element;
//	u8 Next;
//} List ;

typedef struct _SPEED
{
    float m_s16speedx;  //转换后的相对坐标的速度
    float m_s16speedy;  //转换后的相对坐标的速度
} SPEED;

// typedef struct _INPUTSPEED
//{
//	u16 m_u16flag;	//输入的坐标系
//	//计算的速度矢量方向(就是叉车的前进方向 - 激光器的z轴的夹角)
//	u16 l_u16ang ;
//	SPEED m_structSpeed_Absolute;	//转换后的绝对坐标的速度
//	SPEED m_structSpeed_Relative;	//转换后的相对坐标的速度
//	u16 m_u16speedsetflag;	//是否设置了速度标志
//	u16 m_u16angsetflag;	//是否设置的角速度为0;
//	u32 m_u32time ;	//输入的时间戳
//} INPUTSPEED ;
#    define STRUCT_SIZE_INPUTSPEED sizeof(INPUTSPEED)
typedef struct _INPUTSPEED
{
    //输入的时间戳
    timeMs m_tsSynctime;
    //输入的角速度信息
    float m_s32spdang;
    //输入的坐标系
    u16 m_u16Corrdinate_Type;
    //输入的速度
    SPEED m_StructSpeed_absolute;
    SPEED m_StructSpeed_relative;
    //	s16 m_s16speedx_absolute ;
    //	s16 m_s16speedy_absolute ;
    //	//计算的速度矢量方向(就是叉车的前进方向 - 激光器的z轴的夹角)
    ////	u16 l_u16ang ;
    //	//使用的相对速度
    //	s16 m_s16speedx_relative ; //转换后的相对坐标的速度
    //	s16 m_s16speedy_relative ; //转换后的相对坐标的速度
    u16 m_u16speedsetflag;  //是否设置了速度标志
    u16 m_u16angsetflag;    //是否设置的角速度为0;
    float m_f32spAngDirection;
    float m_f32spLine_Abs;
} INPUTSPEED;

typedef struct _GLOBALPARAMNAV
{
    u16 MarkMaxPointNum;       // 0.5m处1米的靶标，靶标点数上限值
    u16 ScanPointNum;          //一圈的扫描点数
    u32 MarkRadio_Coeff;       //用于理论计算靶标上有多少点的半径因数
    u16 TotolPackNum;          //一圈总包数
    u8 Ang_Resolution;         //角度分辨率 单位10mdeg
    u8 ScanFrequency;          //扫描频率
    u8 NavLeastMarkNum;        //位置识别需要的靶标数目
    u8 HasCrossFlag;           //一靶多配标志
    float Per10mDeg_Rad;       //每10mdeg对应的弧度数
    float ScanTime_Per;        //扫描一圈需要使用的时间
    float ScanTime_HalfPer;    //扫描半圈需要的时间
    float Time_PerResolution;  //扫描10mdeg所需要的时间
    u16 EdgeDiff;
    u16 FpgaZeroOffset;

    u16 CombOffset;             //初始定位排列组合的下标
    u16 Mapping_Aver_Done_Cnt;  // mapping模式平均次数统计
    u16 MappingNum;             // mapping模式平均次数
    u16 NavPoweroff;            //是否进行前后两次位置的可行性校验标志

    u32 CntOfACTIVE;       //连续定位计数
    u32 CntOfACTIVE_AVER;  //激光器位置没动计数 用于计算位置平均值

    u16 DistDiffMax;  //靶标不被过滤的最大距离差
    u8 PianXinFlag;   //偏心表是否存在
    u8 SecretFlg;

    u8 AngMode;
    u8 IntinalFlag;  //初始定位标志
    u8 LandmarkSendMode;
    u8 MaxMarkRadio;  //
} GLOBALPARAMNAV;

//基本参数设置
#    define STRUCT_SYS_PIB_LEN (sizeof(SYS_PIB_TypeDef))
typedef struct
{
    u16 FPGA_ADDR;                    // FPGA地址 未用
    u16 FPGA_DATA;                    // FPGA数据  未用
    u16 FPGA_SET;                     // FPGA设置  未用
    u16 FLASH_CHECK;                  // SRAM检测  未用
    u16 LD_MODER;                     //激光器模式程序默认为非激光器模式
    u16 NWIRE_CHECK;                  //无线通讯检测检测 未用
    char Hard_Version[20];            //硬件版本号
    u16 WDT_EN;                       //看们狗
    u16 UART_btl;                     //串口波特率
    u16 Mode_SingleOrScanf;           //单点或扫描模式设置
    u16 Resolutionset;                //分辨率
    u16 Disc_0offset;                 //码盘零刻度偏移
    u16 L_THreshold;                  //低阈值电压
    u16 L_ECOHWaveNum;                //低回波数
    u16 Bmq_offset;                   //低整体偏移 实际对应 编码器偏移m_s16BmqOffset
    u16 err_correct;                  //错误修正
    u16 CorListEN;                    //修正表使能
    u16 Heart_En;                     //心跳
    u16 Wireless_Frq;                 //无线供电平率
    u16 FPGA_Mode;                    // FPGA无线数据模式
    u16 Target_THreshold;             //靶标识别范围 m_u16MarkDistinguish
    int Dist_Offset[2];               //距离偏移
    int Eccentric_Ang;                //偏心角度
    u16 Prod_SEND_DIST_PULSE_CUT;     //生产版上位机发送类型选择
    u16 PianXinCorrEN;                //码盘偏心修正开关
    u16 MarkDotNumPct;                //靶标上扫描点数百分比
    u16 MarkReflectivity;             //靶标反射率
    u32 WorkMode_NavOrMappingOrMark;  //设备工作模式，生产识别靶标，建图等模式切换
    u16 m_u16MarkType;                //标靶类型
    u16 m_u16SendData_Mode;           //数据发送类型
    u32 m_u16MarkRadio;               //标靶半径
    u32 m_u16LocationMarkNum;         //两靶标定位开启与否
    char Sn_No[50];                   // sn号
    char BOOT_No[50];                 // boot版本号
    // APDPara_MOTOR_TypeDef  g_APDPara;
    // NetInfoTypeDef         g_NetInfo;       //网络信息
    // MOTOBGL      	g_MotoHeatparam;
    u16 Eccentric_R;       //偏心半径 未用
    int Dist_Offset2m[2];  //距离偏移 2米内
    u16 ABChannel;         // AB路发光选择
    u16 LDAChargeTime;     // A路LD高压充电时间
    u16 LDBChargeTime;     // B路LD高压充电时间
    char res[60];          //预留
    u16 MAP_EN;            //地图开启
    u16 BBCCheck;          // CRC校验，实际中目前全采用异或校验
} SYS_PIB_TypeDef;         //系统参数信息块 param info block

//排列组合的定义
#    define STRUCT_SIZE_COMBINE sizeof(COMBINE)
typedef struct _COMBINE
{
    u8 m_u8buf[260][5];
    u16 m_u16size;
    u32 m_u32val[260];
    // u8  m_u8matchFlag[260];
} COMBINE;

#    define STRUCT_SIZE_COMBINE1 sizeof(COMBINE1)
typedef struct _COMBINE1
{
    u8 m_u8buf[COMB_MAX][4];
    u16 m_u16size;
    u32 m_u32val[COMB_MAX];
    //	u8  m_u8matchFlag[COMB_MAX];
} COMBINE1;

#    define STRUCT_SIZE_CARVE sizeof(STRUCT_CARVE)

//靶标范围结构体
typedef struct _STRUCT_CARVE
{
    u32 m_u32MarkNum;      //个数
    u16 m_u16MarkSta[32];  //起点数组
    u16 m_u16MarkEnd[32];  //终点数组
} STRUCT_CARVE;

//快排
// typedef struct _SqNote{
//    u32 key;
//}SqNote;
////记录表的结构体
// typedef struct _SqList{
//    SqNote r[1];
//    u16 length;
//}SqList;

#    define STRUCT_SIZE_MARK_XY sizeof(MARK_XY)
typedef struct _MARK_XY
{
    u8 m_u8size;   //靶标尺寸
    u8 m_u8shape;  //靶标形状
    u16 m_u32no;   // ID
    int m_s32x;    // X
    int m_s32y;    // Y
} MARK_XY;

//#define STRUCT_SIZE_LOCAL_MAP_INFO sizeof(LOCAL_MAP_INFO)
// typedef  struct  _LOCAL_MAP_INFO  {

//	u8 m_uIsMark[TARGET_MAX];
//	u16 m_u16MarkOffset[TARGET_MAX];
//	u32 m_u32MarkNum;
//}LOCAL_MAP_INFO ;

#    define STRUCT_SIZE_LOCAL_MAP_INFO sizeof(LOCAL_MAP_INFO)
typedef struct _LOCAL_MAP_INFO
{
    u8 m_uIsMark[TARGET_MAX];
    MARK_XY* m_pStructSetMark[TARGET_MAX];
    u32 m_u32MarkTotalNum;
    u32 m_u32MarkMatchNum;
} LOCAL_MAP_INFO;
//路标的坐标值

#    define STRUCT_SIZE_MARK_SETS sizeof(MARK_SETS)
typedef struct _MARK_SETS
{
    u8 m_u8reserve;
    u8 m_u8flag;
    u8 m_u8hasmatchflag[MAX_SIZE];
    u16 m_u16size;  //取出的靶个数
    u16 m_u16layer;
    MARK_XY m_sMarkSets[MAX_SIZE];
} MARK_SETS;

#    define STRUCT_SIZE_MARKLAYER_ADDRESS sizeof(MARKLAYER_ADDRESS)
typedef struct _MARKLAYER_ADDRESS
{
    u16 m_u16MarkTotalNum;  //总个数
    u16 m_u16InfoPackNum;   //总包数
    u16 m_u16LayerNum;
    u16 m_u16clc;
    u16 m_u16Layer[320];
    u16 m_u16Point[320];
    // u32  m_u32Addr[320];
} MARKLAYER_ADDRESS;

// typedef struct _MARK_REMAPSETS  {
//#define MAX_REMAPPOS 64
//	u16 m_u16ang[MAX_REMAPPOS] ;
//	MARK_SETS m_sRemapMarkSets ;
//}MARK_REMAPSETS ;
#    define STRUCT_SIZE_XYANG2ROBOT sizeof(XYANG2ROBOT)
typedef struct _XYANG2ROBOT
{
    u16 m_u16ang;
    int m_s32x;
    int m_s32y;
} XYANG2ROBOT;

//将环境坐标变换到相对激光器的当前位置(x0,y0,ang0)的位置
#    define STRUCT_SIZE_XY_TO_RelCoor sizeof(XY_TO_RelCoor)
typedef struct _XY_TO_RelCoor
{
    int m_s32x;
    int m_s32y;
} XY_TO_RelCoor;

#    define STRUCT_SIZE_LASERPOS sizeof(ROBOT_XY)
typedef struct _ROBOT_XY
{
    u16 m_u16flag;  //激光器的初始位置有效标志
    XYANG2ROBOT m_Struct_LaserPos;
    int m_s32matixx[10];
    int m_s32matixy[10];
    double m_64sumx;
    double m_64sumy;
    u8 m_u8marknum;
    u8 m_u8scannum;
    u16 m_u16CurrLayer;  //
    u16 m_u16meandev;
    u32 m_u32timestamp;
    u32 m_u32MarkNum;
    XY_TO_RelCoor m_sXy2Robot[TARGET_MAX];
    u16 m_u16MatchMarkId[TARGET_MAX];
    u16 m_u16MarkAng[TARGET_MAX];
    //	u8  m_u8flagy[10];
} ROBOT_XY;

#    define STRUCT_SIZE_XY2ROBOT_CNT sizeof(XY2ROBOT_CNT)
typedef struct _XY2ROBOT_CNT
{
    u8 m_u8MarkNum;
    u16 m_u16cnt[MAPPING_MAX];
    XY_TO_RelCoor m_StructXY[MAPPING_MAX];
    int m_s32SumX[MAPPING_MAX];
    int m_s32SumY[MAPPING_MAX];
} XY2ROBOT_CNT;

#    define STRUCT_SIZE_MappingList sizeof(MappingList)
typedef struct _MappingList
{
    u8 m_u8First;
    u8 m_u8Last[MAPPING_MAX];
    u8 m_u8Next[MAPPING_MAX];
    u16 m_u16Ang[MAPPING_MAX];
    u16 m_u16Dist[MAPPING_MAX];
    XY2ROBOT_CNT m_XY2Rob;
} MappingList;

typedef struct _XY2EqlTri
{
    u16 m_u16ang;  //角度7200代表0.05°
    int m_s32x;    //定位坐标
    int m_s32y;
} XY2EqlTri;

typedef struct _STRUCT_MAPPING_INFO
{
    u16 m_u16Ang;
    u16 m_u16Dist;
    u16 m_u16Cnt;
    XY_TO_RelCoor m_Struct_MarkXY;
} STRUCT_MAPPING_INFO;

typedef STRUCT_MAPPING_INFO* MAPPING_ADDR;

typedef struct _STRUCT_LIST
{
    STRUCT_MAPPING_INFO m_MarkInfo;
    MAPPING_ADDR m_NextMark;
    MAPPING_ADDR m_LastMark;
} STRUCT_LIST;

typedef struct _STRUCT_MAPPING_LIST
{
    u16 m_u16Size;
    STRUCT_LIST m_sMapping[40];
    MAPPING_ADDR m_Start;

} STRUCT_MAPPING_LIST;

#    define STRUCT_SIZE_MARK_INFO_BASE sizeof(STRUCT_MARK_INFO_BASE)
typedef struct _STRUCT_MARK_INFO_BASE
{
    u8 m_u8res;
    u8 m_u8IsMark;
    u16 m_u16Ang;
    u16 m_u16Dist;

} STRUCT_MARK_INFO_BASE;

#    define STRUCT_SIZE_MARK_INFO sizeof(STRUCT_MARK_INFO)
typedef struct _STRUCT_MARK_INFO
{
    //	STRUCT_MARK_INFO_BASE m_Struct_MarkBase;
    u8 m_u8res;
    u8 m_u8MarkType;
    u8 m_u8MarkSize;
    u8 m_u8IsMark;
    u16 m_u16Ang;
    u16 m_u16Dist;
    u16 m_u16ScanSta;
    u16 m_u16ScanEnd;
} STRUCT_MARK_INFO;
//筛选出符合条件的基本靶标数据  //wen
#    define STRUCT_SIZE_FILTER sizeof(STRUCT_FILTER_TARGET)
typedef struct _STRUCT_FILTER_TARGET
{
    volatile u16 m_u8In;  //写指针
    STRUCT_MARK_INFO m_StructMarkInfoNoCorr[TARGET_MAX];
    STRUCT_MARK_INFO m_StructMarkInfo[TARGET_MAX];
    XY_TO_RelCoor m_sXy2Robot[TARGET_MAX];
} STRUCT_FILTER_TARGET;

typedef struct _STRUCT_FILTER_TARGET_ADDR
{
    volatile u16 m_u8In;  //写指针
    STRUCT_MARK_INFO* m_StructMarkInfo[TARGET_MAX];
    XY_TO_RelCoor* m_sXy2Robot[TARGET_MAX];
} STRUCT_FILTER_TARGET_ADDR;
//筛选出符合条件的基本靶标数据
// typedef struct _STRUCT_FILTER_TEMP
//{

//	u16 m_u16Ang ; //扫描一圈后，符合条件的点的刻度
//	u32 m_u32PulseWidth ; //脉冲宽度
//	u32 m_u32Dist ;//距离值
//	//MARK_XY m_sMark[TARGET_MAX]  ; //扫描到的这个点指向的路标
//} STRUCT_FILTER_TEMP ;

#    define STRUCT_SIZE_STRUCT_EqlTri sizeof(STRUCT_EqlTri)
typedef struct _STRUCT_EqlTri
{
    volatile u8 m_u8In;  //写指针
                         //	u16 m_u16Ang[3] ; //扫描一圈后，符合条件的点的角度
                         //	u32 m_u32Dist[3] ;//距离值
    u8 buf[3];
    MARK_XY* m_psSetMarkAddr[EQLTRI_SIZE][3];
    // u8 m_u8IsMark[EQLTRI_SIZE];

} STRUCT_EqlTri;

//筛选出符合条件的基本靶标数据
#    define STRUCT_SIZE_FILTER_TARGET_LITE sizeof(STRUCT_FILTER_TARGET_LITE)
typedef struct _STRUCT_FILTER_TARGET_LITE
{
    STRUCT_FILTER_TARGET_ADDR m_StructMarkScanInfoAddr;
    MARK_XY* m_psSetMarkAddr
        [TARGET_MAX];  //这个疑似目标指向的路标的坐标,如果不是则是扫描到的新路标的在环境坐标的位置
    // XY_TO_RelCoor m_sXy2Robot[TARGET_MAX] ; //以激光器坐标系下的靶标坐标

} STRUCT_FILTER_TARGET_LITE;

#    define STRUCT_SIZE_ICPMarkInfo sizeof(STRUCT_ICPMarkInfo)
typedef struct _STRUCT_ICPMarkInfo
{
    u32 m_MarkNum;
    XY_TO_RelCoor m_sXy2Cur[5];
    u16 m_u16MarkAngCur[5];
    u16 m_u16MarkAngOld[5];
    XY_TO_RelCoor m_sXy2Old[5];
} STRUCT_ICPMarkInfo;

// typedef struct _STRUCT_FILTER_TARGET_LITE
//{

//	volatile u16 m_u8In ; //写指针
//	u16 m_u16Ang[TARGET_LITE_MAX] ; //扫描一圈后，符合条件的点的角度
//	u32 m_u32Dist[TARGET_LITE_MAX] ;//距离值
//
//	u8 m_u8IsMark[TARGET_LITE_MAX]  ;
//	u8 m_u8IsEql[TARGET_LITE_MAX];
//	MARK_XY m_sMark[TARGET_LITE_MAX]  ;
////这个疑似目标指向的路标的坐标,如果不是则是扫描到的新路标的在环境坐标的位置 	XY_TO_RelCoor
// m_sXy2Robot[TARGET_LITE_MAX] ; //夹角和距离相对激光器的坐标 	u16 m_u16StartPoint[TARGET_LITE_MAX]
//;//靶的起始点 	u16 m_u16EndPoint[TARGET_LITE_MAX] ;//靶的结束点
//	//u32 m_u32Poly[TARGET_LITE_MAX] ; //由疑似目标构成的封闭多边形
//	//u32 m_u32Timestamp[TARGET_LITE_MAX] ;
//} STRUCT_FILTER_TARGET_LITE ;

// typedef struct _STRUCT_NAV_MAKR
//{

//	u8 m_u8size ;
//	u8 m_u8type[NAV_MARK_SIZE] ;
//	int m_s32x[NAV_MARK_SIZE] ;
//	int m_s32y[NAV_MARK_SIZE] ;
//} STRUCT_NAV_MAKR ;

// typedef struct _STRUCT_MARK
//{
//	#define MARK_SIZE 16
//	int m_s32x[MARK_SIZE] ;
//	int m_s32y[MARK_SIZE] ;
//	u16 m_u16n ; //路标的个数
//}STRUCT_MARK;
typedef struct _STRUCT_FPGA_SpeedIcp
{
    int m_s32x1;  //传递给fpga的靶标1的浮点x值
    int m_s32y1;  //传递给fpga的靶标1的浮点y值
    int m_s32x2;
    int m_s32y2;
    int m_s32x3;
    int m_s32y3;

    int m_s32Rx1;  //传递给fpga的靶标1的浮点x值,相对坐标
    int m_s32Ry1;  //传递给fpga的靶标1的浮点y值
    int m_s32Rx2;
    int m_s32Ry2;
    int m_s32Rx3;
    int m_s32Ry3;
} STRUCT_FPGA_SpeedIcp;

typedef struct _STRUCT_SpeedIcp
{
    SPEED m_sSpeed_Rel;
    SPEED m_sSpeedMatix[10];
    float m_f32SpeedAng;
    int m_s32Flag;
} STRUCT_SpeedIcp;

typedef struct _STRUCT_FPGAPOS
{
    int m_s32x0;  //传递给fpga的初始圆心方程计算的浮点x值
    int m_s32y0;  //传递给fpga的初始圆心方程计算的浮点y值
    int m_s32x1;  //传递给fpga的靶标1的浮点x值
    int m_s32y1;  //传递给fpga的靶标1的浮点y值
    int m_s32x2;
    int m_s32y2;
    int m_s32x3;
    int m_s32y3;

    int m_s32Rx1;  //传递给fpga的靶标1的浮点x值,相对坐标
    int m_s32Ry1;  //传递给fpga的靶标1的浮点y值
    int m_s32Rx2;
    int m_s32Ry2;
    int m_s32Rx3;
    int m_s32Ry3;

    u32 m_u32ang12;  // 3个靶标的夹角12
    u32 m_u32ang23;  // 3个靶标的夹角23
    u32 m_u32ang31;  // 3个靶标的夹角31
    //测试用
    int m_s32x0int;                 //圆形方程求取的x值
    int m_s32y0int;                 //圆形方程求取的y值
    u16 m_u16ang[3];                //选取的3个靶标的方位角
    u32 m_u32dist[3];               //选取的3个靶标的距离值
    MARK_XY* m_psSetMarkAddr[3];    //这2个靶标的环境坐标信息
    XY_TO_RelCoor m_psXy2Robot[3];  //这3个靶标在相对坐标的信息
    float m_f32ang[3];              //观察用的，3个靶标两两之间的夹角的弧度值
} STRUCT_FPGAPOS;

#    define STRUCT_MOTOR_PIB_LEN ((sizeof(STRUCT_MOTOR_PIB) >> 1))
typedef struct _STRUCT_MOTOR_PIB
{
    __IO u16 u16Speed_r;  //无用
    __IO u16 u16Speed_w;  //无用
    __IO u16 u16PID_P;    //用于配置生产输出数据类型
    __IO u16 u16PID_I;    //用于控制偏心修正是否开启
    __IO u16 u16PID_D;    //用于控制靶标上扫描点个数百分比
    __IO u16 u16CheckEn;  //无用
    __IO u16 u16Dir;      //无用
    __IO u16 u16Loop;     //无用
} STRUCT_MOTOR_PIB;       /*用于控制生产数据输出*/

typedef struct _STRUCT_NETPARAM
{
    u16 m_au16SerPhyAddr[6];  //物理地址
    u16 m_au16SerIpAddr[4];   // IP地址
    u16 m_au16SerNetMask[4];  //子网掩码
    u16 m_au16SerGetWay[4];   //默认网关
    u16 m_au16DecIpAddr[4];   //无用
    u16 m_au16DecNetMask[4];  //无用
    u16 m_au16DecGetWay[4];   //无用
    u16 m_u16SerPort;         // TCP端口
    u16 m_u16LocalBstPort;    // UDP端口
    u16 m_u16PCPort;          //无用
} STRUCT_NETPARAM;            /*网络参数*/

typedef struct _STRUCT_APD
{
    u16 m_u16APDVSet;         //击穿测试电压设置to fpga
    u16 m_u16APDTemperValue;  // APD温度值
    u16 m_u16APDHV_OP_Ratio;  // APD高压系数(/1000)
    u16 m_u16APDHV;           //击穿电压 ad采集
    u16 m_u16APDVTest;        //击穿测试电压 => 对应的APD电压设置
    u16 m_u16APDHvValue;      // APD高压值
} STRUCT_APD;                 /*APD参数*/

#    define STRUCT_FACTOR_CORR_LEN ((sizeof(STRUCT_FACTOR_CORR) >> 1))
typedef struct _STRUCT_FACTOR_CORR
{
    u16 m_u16FlashProgramFlag;   // Flash数据帧头 0xAA55
    u16 m_u16FlashDataLength;    //此结构体数据长度
    s16 m_s16OverallDisDif;      //保留位
    s16 m_s16BmqOffset;          //码盘零刻线偏移
    u16 m_u16TaskState;          //是否使用修正表:0使用,1不使用
    u16 m_u16ZeroDisc;           //保留
    u16 m_u16CodeZeroR;          //码盘偏心半径,暂时无用,保留
    s16 m_s16ArgOfCode;          //码盘偏心角
    u16 m_u16HLevel;             //高阈值
    u16 m_u16LLevel;             //低阈值,暂时没用,保留
    s16 m_u16DistSub;            //整体距离偏移
    s16 m_u16AngCorr;            //保留
    u16 m_u16ErrModify;          //原始数据错误修正
    u16 m_u16WirelessFreq;       //无线供电频率,发送给FPGA
    u16 m_u16DustInitValue[10];  //保留
    u16 m_u16BBCCheck;           // BBC校验

} STRUCT_FACTOR_CORR; /*修正参数*/

#    define STRUCT_RESETCOUNT_LEN ((sizeof(STRUCT_RESETCOUNT) >> 1))
typedef struct _STRUCT_RESETCOUNT
{
    u16 m_u16FlashProgramFlag;   /*Flash数据帧头 0xAA55*/
    u16 m_u16FlashDataLength;    /*此结构体数据长度*/
    u16 m_u16SOFTRSTNum;         /*软件复位次数*/
    u16 m_u16IWDGRSTNum;         /*看门狗复位次数*/
    u16 m_u16WWDGRSTNum;         /*无用*/
    u16 m_u16PORRSTNum;          //上电/掉电复位或欠压复位
    u16 m_u16NRSTNum;            /*STM32硬件复位次数*/
    u16 m_u16W5300RSTNum;        /*网络复位次数*/
    u16 m_u16W5300RstNum_Heart;  /*心跳复位次数*/
    u16 m_u16W5300RstNum_Close;  /*保留*/
    u16 m_u16W5300RstNum_SendOk; /*保留*/
    u16 m_u16NCdata1[1];         /*保留*/
    u16 m_u16BBCCheck;           /*BBC校验*/

} STRUCT_RESETCOUNT; /*复位次数*/

#    define STRUCT_MarkMatch_LEN ((sizeof(STRUCT_MarkMatch) >> 1))
typedef struct _STRUCT_MarkMatch
{
    // u16	m_u16FlashProgramFlag;		//Flash帧头标志 0xAA55
    // u16	m_u16FlashDataLength;		//该结构体的数据长度
    u16 m_u16IdentWindow_R_Min;     //靶标匹配半径RMin
    u16 m_u16IdentWindow_R_Max;     //靶标匹配半径RMax
    u32 m_u32IdentWindow_Dist_Min;  //靶标匹配半径与距离相关时Dmin
    u32 m_u32IdentWindow_Dist_Max;  //靶标匹配半径与距离相关时Dmax
    u32 m_u32MarkScan_Min;          //识别靶标半径Rmin
    u32 m_u32MarkScan_Max;          //识别靶标半径Rmax
    u16 m_u16FilterMul;             //靶标遮挡直径倍数
    u16 m_u16LocalMapMarkNum;       //小地图靶标个数
    u16 m_u16LocalMapUseNum;        //小地图靶标个数
                                    // u16 m_u16BBCCheck; 				//BBC校验

} STRUCT_MarkMatch; /*靶标扫描识别*/

#    define STRUCT_TASKLIST_LEN ((sizeof(STRUCT_TASKLIST) >> 1))
typedef struct _STRUCT_TASKLIST
{
    u16 m_u16crc;   //修正表的总校验
    u16 m_u16flag;  //烧写成功标志
    u32 m_u32Len;
    u16 m_u16PosCorrBuf[50000];  //修正表数据,0~500ns,以32ps为间隔的脉宽修正值,共15625

} STRUCT_TASKLIST; /*修正表模块*/

#    define STRUCT_REFLEXLIST_LEN ((sizeof(STRUCT_TASKLIST) >> 1))
typedef struct _STRUCT_REFLEXLIST
{
    // u16 m_u16FlashProgramFlag[REFLEX_SIZE]; //数据有效标志位
    // u16 m_u16FlashDataLen[REFLEX_SIZE];		 //FLASH中有效数据(半字)长度，包含有效标志位
    u16 m_u16crc;   //修正表的总校验
    u16 m_u16flag;  //烧写成功标志
    u32 m_u32Len;
    u16 m_u16PosCorrBuf[50000];  //定义Task数组12500  2015-11-10 使用新版放大电路更改数组容量
                                 // u16 m_u16FlashBBC[REFLEX_SIZE]; //FLASH的CRC校验
} STRUCT_REFLEXLIST; /*反射率表*/

typedef struct _STRUCT_PIANXINCORR
{
    // u16 m_u16FlashProgramFlag[REFLEX_SIZE]; //数据有效标志位
    // u16 m_u16FlashDataLen[REFLEX_SIZE];		 //FLASH中有效数据(半字)长度，包含有效标志位
    u16 m_u16crc;   //修正表的总校验
    u16 m_u16flag;  //烧写成功标志
    u32 m_u32Len;
    u16 m_u16PosCorrBuf[7200];  //[50000];		 //定义Task数组12500  2015-11-10
                                //使用新版放大电路更改数组容量
                                // u16 m_u16FlashBBC[REFLEX_SIZE]; //FLASH的CRC校验
} STRUCT_PIANXINCORR;           /*码盘偏心修正表*/

typedef struct TagFSMCWireState
{
    u16 data;    //数据错误位
    u16 addr;    //地址错误位
} FSMCWireState; /*FPGA总线检测*/

#    define STRUCT_EQUIPSTATE_LEN ((sizeof(STRUCT_EQUIPSTATE) >> 1))
typedef struct _STRUCT_EQUIPSTATE
{
    u16 m_u16WirelessVol;   //无线供电电压
    u16 m_u16MotoStartEn;   //电机启动
    u16 m_u16LaserEn;       //无用
    u16 m_u16HeartState;    //心跳状态
    u16 m_u16APD_HV;        // APD高压
    s16 m_s16APD_temper;    // APD温度
    s16 m_s16Motor_temper;  //电机温度
    u16 m_u16FlashArea;     // FLASH区域?
    u16 m_u16FlashArea1;    // FLASH区域?
    u16 m_u16heatState;     //加热状态
    u16 m_u16sourceVol;     //输入电压
    u32 m_u32BBCCheck;      // CRC校验，实际中目前全采用异或校验
} STRUCT_EQUIPSTATE;        /*设备状态*/

#    define STRUCT_DIAGNOSISPIB_LEN ((sizeof(STRUCT_EQUIPSTATE) >> 1))
typedef struct _STRUCT_DIAGNOSISPIB
{
    u16 m_u16FPGASETERR;             // FPGA相关状态
    u16 m_u16DS18B20State;           //加密状态,暂时无用
    u16 m_u16fm25cl64bState;         //铁电检测
    u16 m_u16SramState;              // SRAM检测
    u16 m_u16fsmcState;              // fpga-fsmc检测
    u16 tmp[5];                      //预留
    FSMCWireState m_sFPGAWireState;  // FPGA总线检测

} STRUCT_DIAGNOSISPIB; /*设备检测状态标志*/

#endif
