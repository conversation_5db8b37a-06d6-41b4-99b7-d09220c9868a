/*!
 * @file ParticleFilter.h
 * @copyright Copyright (c) 2019, FADA-CATEC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once
#include "Grid3d.h"
#include "header.h"

typedef struct s_PointXYZINT
{
    int m_iX;
    int m_iY;
    int m_iZ;
} s_PointXYZINT;

namespace wanjilocal {
struct s_Particle
{
    float x;  /*!< Position x */
    float y;  /*!< Position y */
    float z;  /*!< Position z */
    float a;  /*!< Yaw angle */
    float w;  /*!< 总重量 */
    float wp; /*!< 三维点云传感器的权重 */
    Grid3dInfo::Ptr gridInfoPtr;
    s_Particle() : x(0), y(0), z(0), a(0), w(0), wp(0), gridInfoPtr(NULL) {}
};

//排序函数
inline bool ComPrticleBy_wp(const s_Particle& p_p1, const s_Particle& p_p2);

template <typename PointScan> class ParticleFilter {
  public:
    explicit ParticleFilter();

    virtual ~ParticleFilter();

    //判断是否初始化
    bool isInitialized() const
    {
        return c_bInitialized_;
    }

    //重置初始化状态
    void resetEnableInit()
    {
        c_bInitialized_ = false;
    }
    //设置栅格地图边界信息
    void setPointEdgeInfo(const PointCloudInfoS& pcInfo)
    {
        c_stTotalMapCloudInfo_ = pcInfo;
    }

    void setMapType(const MAP_DIMENSION& p_mapType)
    {
        c_mapType_ = p_mapType;
    }

    //设置重叠率
    void setOverlapThresh(float p_fOverlapThresh = 0.6);

    //初始化粒子
    void initPf(const std::vector<AMCLPOSE>& p_poselist);

    //设置栅格地图
    void updateSubGridMap(Grid3dInfo::Ptr p_pGrid3dInfo);

    /**
     * @description:采用粒子滤波进行优化定位
     * @param {*}
     * @return {*}
     */
    void predict(const double p_dOdomXMod,
                 const double p_dOdomYMod,
                 const double p_dOdomZMod,
                 const double p_dOdomAMod,
                 typename pcl::PointCloud<PointScan>::ConstPtr p_pcLidarCloud);

    /**
     * @description: 计算点云的概率值
     * @param {ConstPtr} p_pcLidarCloudIn  点云
     * @param {s_Particle} &p_stPfPose 位姿
     * @param {Ptr} p_gridInfo 栅格地图
     * @return {*}
     */
    double computeWeight(typename pcl::PointCloud<PointScan>::ConstPtr p_pcLidarCloudIn,
                         wanjilocal::s_Particle& p_stPfPose,
                         Grid3dInfo::Ptr p_gridInfo);

    // amcl优化
    bool optimize(int p_iBestInd,
                  double p_dAdelta,
                  double p_dXydelta,
                  typename pcl::PointCloud<PointScan>::ConstPtr p_pcLidarCloud);

    void resetPf(float p_fBestScore, double p_dAdelta, double p_dXydelta);

    //固定阈值进行滤波
    void filterPfByThresh(float p_fScore);

    //根据概率值的大小过滤较小的一半
    void filterPfBySort();

    void resample(double p_dAdev, double p_dXydev);

    //归一化概率
    void normalization();

    // cartographer的优化方法
    double optimizeCarto(wanjilocal::s_Particle& p_poseinit,
                         Grid3dInfo::Ptr p_gridInfo,
                         typename pcl::PointCloud<PointScan>::ConstPtr p_lidar_cloud);

    //获取定位位姿
    s_Particle getMean() const
    {
        return c_stPfMean_;
    }

    std::vector<s_Particle> getParticles() const
    {
        return c_vecPtc_;
    }

    bool calcGridValue(const pcl::PointXYZI& p_pin, pcl::PointXYZI& p_pout);
    float ranGaussian_(const double p_dMean, const double p_dDigma);
    float rngUniform_(const float p_fRangeFrom, const float p_fRangeTo);

  private:
    void transformToMap_(PointScan* p_pi,
                         pcl::PointXYZI* p_pointOut,
                         Eigen::Vector3d p_pose,
                         Eigen::Quaterniond p_quat);

    std::vector<wanjilocal::s_Particle> c_vecMoveList_;

    bool c_bInitialized_;

    std::vector<s_Particle> c_vecPtc_;  //粒子
    s_Particle c_stPfMean_;             //最有位姿
    std::random_device c_rd_;
    std::mt19937 c_generator_;
    PointCloudInfoS c_stTotalMapCloudInfo_;

    Grid3dInfo::Ptr c_pPoseGridInfo_;
    MAP_DIMENSION c_mapType_;  //地图模式，2d还是3d

    float c_fValidOverlapRatio_;  //有效重叠比例阈值

    // double bestScore{0};
};

}  // namespace wanjilocal

#include "impl/ParticleFilter.hpp"