
#pragma once
#include "../ParticleFilter.h"
#include "../poseFunc.h"

/****************************/

#include <fstream>

#ifdef TEST
extern std::ofstream g_out_file;
extern std::ofstream g_out_overlap;
#endif

/**************************/

namespace wanjilocal {

inline bool ComPrticleBy_wp(const s_Particle& p_p1, const s_Particle& p_p2)
{
    return p_p1.wp < p_p2.wp;
}

template <typename PointScan>
ParticleFilter<PointScan>::ParticleFilter()
    : c_bInitialized_(false), c_generator_(c_rd_()), c_mapType_(MAP_2D), c_fValidOverlapRatio_(0.6)
{
    c_pPoseGridInfo_.reset(new wanjilocal::Grid3dInfo());
    c_pPoseGridInfo_->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());
}

template <typename PointScan> ParticleFilter<PointScan>::~ParticleFilter() {}

template <typename PointScan>
void ParticleFilter<PointScan>::setOverlapThresh(float p_fOverlapThresh)
{
    c_fValidOverlapRatio_ = p_fOverlapThresh;
}

//只有信息的传输，没有修改
template <typename PointScan>
void ParticleFilter<PointScan>::initPf(const std::vector<AMCLPOSE>& p_poselist)
{
    // fillPoseGridInfo(poselist,grid3d,kdtree);

    if (!c_vecPtc_.empty())
    {
        c_vecPtc_.clear();
    }

    int posesize = p_poselist.size();
    c_vecPtc_.resize(abs(posesize));

    for (uint32_t i = 0; i < c_vecPtc_.size(); ++i)
    {
        c_vecPtc_[i].x = p_poselist[i % posesize].x;
        c_vecPtc_[i].y = p_poselist[i % posesize].y;
        c_vecPtc_[i].z = p_poselist[i % posesize].z;
        c_vecPtc_[i].a = p_poselist[i % posesize].a;

        c_vecPtc_[i].gridInfoPtr.reset(new wanjilocal::Grid3dInfo());
        // p_[i].gridInfoPtr->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());
        c_vecPtc_[i].gridInfoPtr = c_pPoseGridInfo_;  //硬拷贝？
        c_vecPtc_[i].w = 1.0 / c_vecPtc_.size();
    }
    c_bInitialized_ = true;
}

template <typename PointScan>
void ParticleFilter<PointScan>::updateSubGridMap(Grid3dInfo::Ptr p_pGrid3dInfo)
{
    // poseGridInfo->ceil->clear();
    c_pPoseGridInfo_ = p_pGrid3dInfo;
}

// XYZ轴yaw轴滤波器预测颗粒，位姿增量，当前帧角点+面点，发布话题3个，栅格信息类
template <typename PointScan>
void ParticleFilter<PointScan>::predict(
    const double p_dOdomXMod,
    const double p_dOdomYMod,
    const double p_dOdomZMod,
    const double p_dOdomAMod,
    typename pcl::PointCloud<PointScan>::ConstPtr p_pcLidarCloud)
{
    // x轴预测增量*x轴每米预测粒子分散度
    // const double x_dev = fabs(delta_x * odom_x_mod);
    // const double y_dev = fabs(delta_y * odom_y_mod);
    // const double z_dev = fabs(delta_z * odom_z_mod);
    // const double a_dev = fabs(delta_a * odom_a_mod);

    const double l_dXdev = fabs(p_dOdomXMod);
    const double l_dYdev = fabs(p_dOdomYMod);
    const double l_dZdev = fabs(p_dOdomZMod);
    const double l_dAdev = fabs(p_dOdomAMod);

    /*  根据里程计对所有粒子进行预测 */
    float l_fSa, l_fCa, l_fRand_x, l_fRand_y;
    wanjilocal::s_Particle l_bestPose;
    double l_dBestScore = 0.0;
    //粒子数量,目前为path点数量
    int l_iBestInd = -1;

#ifdef TEST
// g_out_file<<","<<"粒子数量"<<","<<c_vecPtc_.size();
#endif

    for (uint32_t i = 0; i < c_vecPtc_.size(); ++i)
    {
        l_fSa = sin(c_vecPtc_[i].a);
        l_fCa = cos(c_vecPtc_[i].a);
        l_fRand_x = ranGaussian_(0, l_dXdev);  // que 里程计体现在哪里？
        l_fRand_y = ranGaussian_(0, l_dYdev);
        c_vecPtc_[i].x += l_fCa * l_fRand_x - l_fSa * l_fRand_y;
        c_vecPtc_[i].y += l_fSa * l_fRand_x + l_fCa * l_fRand_y;
        // p_[i].z += ranGaussian(0, z_dev);
        // p_[i].a += delta_a + ranGaussian(0, a_dev);
        c_vecPtc_[i].z += 0;  // ranGaussian(0, z_dev);
        c_vecPtc_[i].a += 0;  // ranGaussian(0, a_dev);
        // p_[i].w = 1.0 / p_.size();

        TicToc t_p;
        t_p.tic();
        c_vecPtc_[i].wp = computeWeight(p_pcLidarCloud, c_vecPtc_[i], c_vecPtc_[i].gridInfoPtr);
        if (c_vecPtc_[i].wp > l_dBestScore)
        {
            l_dBestScore = c_vecPtc_[i].wp;
            l_bestPose = c_vecPtc_[i];
            l_iBestInd = i;
        }
    }
    c_stPfMean_ = l_bestPose;
    if (l_iBestInd >= 0)
        optimize(l_iBestInd, l_dAdev, l_dXdev, p_pcLidarCloud);
    else
        printf("init pose not found for Optimization\n");
}

template <typename PointScan>
double ParticleFilter<PointScan>::computeWeight(
    typename pcl::PointCloud<PointScan>::ConstPtr p_pcLidarCloudIn,
    wanjilocal::s_Particle& p_stPfPose,
    Grid3dInfo::Ptr p_gridInfo)
{
    if (p_pcLidarCloudIn == nullptr || p_pcLidarCloudIn->empty())
    {
        p_stPfPose.wp = 0;
        return 0;
    }

    // pose(xyza)转eigen xyz+wxyz
    float l_fTx = p_stPfPose.x;
    float l_fTy = p_stPfPose.y;
    float l_fTz = p_stPfPose.z;
    Eigen::Vector3d l_pose(l_fTx, l_fTy, l_fTz);
    Eigen::Quaterniond l_quat = YAW2Quat(p_stPfPose.a);

    TicToc l_time;
    l_time.tic();

    PointScan l_pointOri;
    pcl::PointXYZI l_pointSel;
    pcl::PointXYZI* l_pPointGridMap;
    pcl::KdTreeFLANN<pcl::PointXYZI> l_gridKdtree;
    l_gridKdtree.setInputCloud(p_gridInfo->ceil);

    // printf("kdtree create, time cost: %.3f\n",l_time.toc());

    std::vector<int> l_pointSearchInd;
    std::vector<float> l_pointSearchSqDis;
    int l_iCount = 0;
    double l_dRes = 0;
    bool l_bResGridValue;

    for (uint32_t i = 0; i < p_pcLidarCloudIn->size(); i++)
    {
        l_pointOri = p_pcLidarCloudIn->points[i];
        if (l_pointOri.x == 0 && l_pointOri.y == 0 && l_pointOri.z == 0)
            continue;

        //已知pose的全局位姿，将lidar特征点转到全局坐标系
        transformToMap_(&l_pointOri, &l_pointSel, l_pose, l_quat);

        //全局坐标系下的lidar特征点变换grid的xyz
        l_bResGridValue = calcGridValue(l_pointSel, l_pointSel);

        // lin_test
        if (c_mapType_ == MAP_2D)
            l_pointSel.z = 0;

        if (l_bResGridValue)
        {
            // lidarGridInMap->push_back(pointSel);

            // lin_note
            // 栅格定位采用最近邻搜索，也可以考虑直接根据网格编号定位（需要保证网格完整，目前网格不完整）
            if (l_gridKdtree.nearestKSearch(l_pointSel, 1, l_pointSearchInd, l_pointSearchSqDis))
            {
                l_pPointGridMap = &p_gridInfo->ceil->points[l_pointSearchInd[0]];
                if ((l_pointSel.x == (l_pPointGridMap->x)) && (l_pointSel.y == (l_pPointGridMap->y))
                    && (l_pointSel.z
                        == (l_pPointGridMap->z)))  // que 为何用这个判断条件？同样的坐标本应该一样
                {
                    // lidarGridMatch->push_back(pointSel);
                    l_dRes += l_pPointGridMap->intensity;  // que 强度的含义？
                    l_iCount++;
                }
            }
        }
    }
    double l_dRatio = (l_iCount * 1.0) / (p_pcLidarCloudIn->size() * 1.0);
// std::cout<<"overlap ration: "<<l_dRatio<<endl;
#ifdef TEST
    g_out_overlap << p_stPfPose.x << "," << p_stPfPose.y << "," << p_stPfPose.a << ",," << l_dRatio
                  << "," << l_dRes << "," << l_iCount;
#endif

    double out =
        (l_iCount <= p_pcLidarCloudIn->size() * c_fValidOverlapRatio_) ? 0 : l_dRes / l_iCount;
    // double out = (l_iCount <= p_pcLidarCloudIn->size() * c_fValidOverlapRatio_) ? 0 : l_dRes /
    // p_pcLidarCloudIn->size();
    p_stPfPose.wp = out;

#ifdef TEST
    g_out_overlap << "," << out << std::endl;
#endif

    // printf("over compute\n");
    return out;
}

template <typename PointScan>
bool ParticleFilter<PointScan>::optimize(
    int p_iBestInd,
    double p_dAdelta,
    double p_dXydelta,
    typename pcl::PointCloud<PointScan>::ConstPtr p_pcLidarCloud)
{
    wanjilocal::s_Particle l_bestPose = c_vecPtc_[p_iBestInd];
    double l_dBestScore = c_vecPtc_[p_iBestInd].wp;
    double l_dCurrAdelta = p_dAdelta;  //默认0.3 ，用于判断重采样几次
    double l_dCurrXydelata = p_dXydelta;
    bool l_bUpdate;
    // int best_ind = bestInd;//不在使用，下面流程存在数组维度大小发生变化，容易产生数组越界

    // printf("first best curr score %f \n",bestScore);
    double l_dCurrBestScore = c_vecPtc_[p_iBestInd].wp;

    u_int32_t l_maxItor = 20;
    u_int32_t l_nItor = 0;  //迭代次数

    do
    {
        l_bUpdate = false;

        l_dCurrAdelta *= 0.5;
        l_dCurrXydelata *= 0.5;
        // double filterScore = bestScore * 0.5;
        if (fabs(l_dCurrBestScore) < 1e-4)
        {
            printf("定位失败，概率值为零");
            return false;
        }

        resetPf(l_dCurrBestScore, l_dCurrAdelta, l_dCurrXydelata);

        l_dCurrBestScore = 0.0;  //重置
        wanjilocal::s_Particle l_currBestPose;
        std::cout << "number of particle: " << c_vecPtc_.size() << std::endl;

#ifdef TEST
// g_out_file << "," << c_vecPtc_.size();
#endif

        for (uint32_t i = 0; i < c_vecPtc_.size(); i++)
        {
            double l_dLocalScore;
            l_dLocalScore = computeWeight(p_pcLidarCloud, c_vecPtc_[i], c_vecPtc_[i].gridInfoPtr);

            // currentScore : 最高匹配度, bestLocalPose:最高匹配pose
            if (l_dLocalScore > l_dCurrBestScore)
            {
                l_dCurrBestScore = l_dLocalScore;
                l_currBestPose = c_vecPtc_[i];
                // best_ind = i;
            }
            // printf("comput new pf:xyawwp: %f | %f | %f | %f | %f
            // \n",p_[i].x,p_[i].y,(p_[i].a/M_PI*180),p_[i].w,p_[i].wp);
        }
        printf("\n-------------------------------------------------\n");
        printf("best curr score %f | %f\n", l_dBestScore, l_dCurrBestScore);
        if (l_dCurrBestScore >= l_dBestScore)
        {
            l_dBestScore = l_dCurrBestScore;
            l_bestPose = l_currBestPose;
            l_bUpdate = true;
        }

        l_nItor++;
        //循环结束条件：或标准差低于0.2，即0.3/2/2/2/2=0.01875，四次以后，低于1度,或者Score不增
    } while ((l_dCurrAdelta > 0.04 || l_dCurrXydelata > 0.1) && l_bUpdate && l_nItor < l_maxItor);

    printf("best curr score %0.4f, itorater nums: %d\n", l_dBestScore, l_nItor);

//为了对齐
#ifdef TEST
    for (int i = l_nItor; i < 4; ++i)
    {
        // g_out_file<<",";
    }
#endif

    c_stPfMean_ = l_bestPose;
    // p_.resize(p_.size()+1);
    // p_.push_back(bestPose);

    return true;
}

template <typename PointScan>
void ParticleFilter<PointScan>::resetPf(float p_fBestScore, double p_dAdelta, double p_dXydelta)
{
    //低于score的被剔除
    // for(uint32_t i=0;i<p_.size();i++)
    // {
    //     printf("filter bef pf:xyawwp: %f  | %f  | %f  | %f  | %f
    //     \n",p_[i].x,p_[i].y,(p_[i].a/M_PI*180),p_[i].w,p_[i].wp);
    // }
    //     printf("-------------------------------------------------\n");

    // filterPf();
    filterPfBySort();
    filterPfByThresh(p_fBestScore * 0.5);

    // for(uint32_t i=0;i<p_.size();i++)
    // {
    //     printf("filter suc pf:xyawwp: %f  | %f  | %f  | %f  | %f
    //     \n",p_[i].x,p_[i].y,(p_[i].a/M_PI*180),p_[i].w,p_[i].wp);
    // }
    //     printf("-------------------------------------------------\n");

    resample(p_dAdelta, p_dXydelta);

    // for(uint32_t i=0;i<p_.size();i++)
    // {
    //     printf("sample suc pf:xyawwp: %f | %f | %f | %f | %f
    //     \n",p_[i].x,p_[i].y,(p_[i].a/M_PI*180),p_[i].w,p_[i].wp);
    // }
    //     printf("---------------------------------------------\n");
}

template <typename PointScan> void ParticleFilter<PointScan>::filterPfByThresh(float p_fScore)
{
    for (std::vector<s_Particle>::iterator it = c_vecPtc_.begin(); it != c_vecPtc_.end();)
    {
        if ((*it).wp < p_fScore)
            it = c_vecPtc_.erase(it);  //删除元素，返回值指向已删除元素的下一个位置
        else
            ++it;  //指向下一个位置
    }
}

template <typename PointScan> void ParticleFilter<PointScan>::filterPfBySort()
{
    std::vector<s_Particle> l_vecTmp = c_vecPtc_;
    std::sort(l_vecTmp.begin(), l_vecTmp.end(), ComPrticleBy_wp);

    c_vecPtc_.clear();
    for (int i = (int)l_vecTmp.size() / 2; i < (int)l_vecTmp.size(); i++)
    {
        c_vecPtc_.push_back(l_vecTmp[i]);
    }
}

template <typename PointScan>
void ParticleFilter<PointScan>::resample(double p_dAdev, double p_dXydev)
{
    normalization();
    // for(uint32_t i=0;i<p_.size();i++)
    // {
    //     printf("update suc pf:xyawwp: %f | %f | %f | %f | %f
    //     \n",p_[i].x,p_[i].y,(p_[i].a/M_PI*180),p_[i].w,p_[i].wp);
    // }
    //     printf("---------------------------------------------\n");

    int l_iNewSize = c_vecPtc_.size() * 2;
    // newSize = newSize > 5 ? newSize : newSize + 5;
    std::vector<s_Particle> l_newP(l_iNewSize);
    for (int i = 0; i < (int)c_vecPtc_.size(); i++)
    {
        l_newP[i] = c_vecPtc_[i];
        l_newP[i].a += ranGaussian_(0, p_dAdev);  // que 为何只有角度加上高斯噪声？
    }
    const float l_fFactor = 1.f / (l_iNewSize - c_vecPtc_.size());
    const float l_fR = l_fFactor * rngUniform_(0, 1);  //(0, 1/n)
    float l_fC = c_vecPtc_[0].w;
    float l_fU;

    for (int m = c_vecPtc_.size(), i = 0; m < l_iNewSize; ++m)
    {
        l_fU = l_fR + l_fFactor * (m - c_vecPtc_.size());  //等间隔进步，落在大区间的数量就大
        while (l_fU > l_fC)
        {
            if (++i >= (int)c_vecPtc_.size())
                break;
            l_fC += c_vecPtc_[i].w;
        }
        // printf("resample i: %d\n",i);
        l_newP[m].x =
            c_vecPtc_[i].x
            + ranGaussian_(0, p_dXydev);  // 加上这个值的物理意义是？高斯函数返回值的范围是多少
        l_newP[m].y = c_vecPtc_[i].y + ranGaussian_(0, p_dXydev);
        l_newP[m].z = c_vecPtc_[i].z;
        l_newP[m].a = c_vecPtc_[i].a + ranGaussian_(0, p_dAdev);  // ranGaussian(0, 0.1);
        l_newP[m].w = l_fFactor;
        // new_p[m].gridInfoPtr.reset(new Grid3dInfo());
        // new_p[m].gridInfoPtr->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());
        l_newP[m].gridInfoPtr = c_pPoseGridInfo_;
    }

    c_vecPtc_.resize(l_iNewSize);
    c_vecPtc_ = l_newP;
}

template <typename PointScan> void ParticleFilter<PointScan>::normalization()
{
    /*  合并测量值 */
    float l_wp_num = 0, l_wtr = 0;

    for (uint32_t i = 0; i < c_vecPtc_.size(); ++i)
    {
        l_wp_num += c_vecPtc_[i].wp;  //所有粒子权重和
    }

    /*  规格化所有权重 */
    float l_fWNum = 0;
    for (uint32_t i = 0; i < c_vecPtc_.size(); ++i)
    {
        if (l_wp_num > 0)
            c_vecPtc_[i].wp /= l_wp_num;  //每个粒子权重= 占总权重比值
        else
            c_vecPtc_[i].wp = 0;

        c_vecPtc_[i].w = c_vecPtc_[i].wp;
        l_fWNum += c_vecPtc_[i].w;  //每个粒子权重占比和
    }

    // Particle mean_p;
    // float max_w = 0;
    for (uint32_t i = 0; i < c_vecPtc_.size(); ++i)
    {
        if (l_fWNum > 0)
            c_vecPtc_[i].w /= l_fWNum;
        else
            c_vecPtc_[i].w = 0;

        // if (p_[i].w > max_w)
        // {
        //     max_w = p_[i].w;
        //     mean_p.x = p_[i].x;
        //     mean_p.y = p_[i].y;
        //     mean_p.z = p_[i].z;
        //     mean_p.a = p_[i].a;
        // }
    }
    // mean_ = mean_p;
}

/*------------------------------------------小功能函数模块------------------------------------------*/
template <typename PointScan>
void ParticleFilter<PointScan>::transformToMap_(PointScan* p_pi,
                                                pcl::PointXYZI* p_pointOut,
                                                Eigen::Vector3d p_pose,
                                                Eigen::Quaterniond p_quat)
{
    // printf("%f | %f | %f | %f \n",quat.x(),quat.y(),quat.z(),quat.w());
    // geometry_msgs::Quaternion base = tf::createQuaternionMsgFromYaw(1.570796327);
    // Eigen::Quaterniond base2(base.w,base.x,base.y,base.z);

    Eigen::Vector3d l_pointCurr(p_pi->x, p_pi->y, p_pi->z);
    Eigen::Vector3d l_pointW = p_quat * l_pointCurr + p_pose;
    p_pointOut->x = l_pointW.x();
    p_pointOut->y = l_pointW.y();
    p_pointOut->z = l_pointW.z();
}

template <typename PointScan>
bool ParticleFilter<PointScan>::calcGridValue(const pcl::PointXYZI& p_pin, pcl::PointXYZI& p_pout)
{
    bool l_bTmp;
    s_PointXYZINT l_stPoutInt;

    if (p_pin.x < c_stTotalMapCloudInfo_.m_min_x || p_pin.x > c_stTotalMapCloudInfo_.m_max_x
        || p_pin.y < c_stTotalMapCloudInfo_.m_min_y || p_pin.y > c_stTotalMapCloudInfo_.m_max_y
        || p_pin.z < c_stTotalMapCloudInfo_.m_min_z || p_pin.z > c_stTotalMapCloudInfo_.m_max_z)
    {
        l_bTmp = false;
    }
    else
        l_bTmp = true;

    l_stPoutInt.m_iX =
        floor((p_pin.x - c_stTotalMapCloudInfo_.m_min_x) / c_stTotalMapCloudInfo_.m_resol);
    l_stPoutInt.m_iY =
        floor((p_pin.y - c_stTotalMapCloudInfo_.m_min_y) / c_stTotalMapCloudInfo_.m_resol);
    l_stPoutInt.m_iZ =
        floor((p_pin.z - c_stTotalMapCloudInfo_.m_min_z) / c_stTotalMapCloudInfo_.m_resol);
    p_pout.x = l_stPoutInt.m_iX;
    p_pout.y = l_stPoutInt.m_iY;
    p_pout.z = l_stPoutInt.m_iZ;

    return l_bTmp;
}

template <typename PointScan>
float ParticleFilter<PointScan>::ranGaussian_(const double p_dMean, const double p_dDigma)
{
    //高斯正态分布函数  期望mean  标准差sigma
    std::normal_distribution<float> l_distribution(p_dMean, p_dDigma);
    return l_distribution(c_generator_);
}

template <typename PointScan>
float ParticleFilter<PointScan>::rngUniform_(const float p_fRangeFrom, const float p_fRangeTo)
{
    std::uniform_real_distribution<float> l_distribution(p_fRangeFrom, p_fRangeTo);
    return l_distribution(c_generator_);
}

/*------------------------------------------暂时未用------------------------------------------*/

template <typename PointScan>
double ParticleFilter<PointScan>::optimizeCarto(
    wanjilocal::s_Particle& p_poseinit,
    Grid3dInfo::Ptr p_gridInfo,
    typename pcl::PointCloud<PointScan>::ConstPtr p_lidar_cloud)
{
    // pcl::PointCloud<pcl::PointXYZI>::Ptr l_pfPC1(new pcl::PointCloud<pcl::PointXYZI>());
    // pcl::PointCloud<pcl::PointXYZI>::Ptr l_pfPC2(new pcl::PointCloud<pcl::PointXYZI>());
    // pfPC1->resize(cloud->size());

    double l_dBestScore = -1;
    wanjilocal::s_Particle l_bestPose;
    wanjilocal::s_Particle l_currentPose = p_poseinit;
    double l_dCurrentScore = l_currentPose.wp;

    double l_adelta = 0.05, l_ldelta = 0.3;
    unsigned int l_refinement = 0;
    int l_iCount = 0;

    enum Move { Front, Back, Left, Right, TurnLeft, TurnRight, Done };
    //
    do
    {
        // 5cm 17du,5次后为0.15cm，0.5du

        /*
         *currentScore增加条件：Move后匹配度增加
         *bestScore当前if不增加，之后bestScore=currentScore；
         *if满足的条件为：当前pose执行Move后匹配度不再增加时，即bestScore==currentScore，才成立，
         */
        if (l_dBestScore >= l_dCurrentScore)
        {
            l_refinement++;
            l_adelta *= .5;
            l_ldelta *= .5;
        }
        l_dBestScore = l_dCurrentScore;  // currentScore第一次为rawpose.wp，Move后为当前pose的最高wp
        l_bestPose = l_currentPose;

        wanjilocal::s_Particle l_bestLocalPose = l_currentPose;
        wanjilocal::s_Particle l_localPose = l_currentPose;

        //依次将pose向左 右 后 前 正旋 反旋
        //某个值，计算全局grid地图的匹配度wp,且将移动后的pose（pf）储存之movelist 值最初为5cm 0.3rad
        //,匹配更优，则继续缩小为0.5倍
        Move move = Front;
        do
        {
            l_localPose = l_currentPose;
            switch (move)
            {
                case Front:
                    l_localPose.x += l_ldelta;
                    move = Back;
                    break;
                case Back:
                    l_localPose.x -= l_ldelta;  //相当于回到了原位置？
                    move = Left;
                    break;
                case Left:
                    l_localPose.y -= l_ldelta;
                    move = Right;
                    break;
                case Right:
                    l_localPose.y += l_ldelta;
                    move = TurnLeft;
                    break;
                case TurnLeft:
                    l_localPose.a += l_adelta;
                    move = TurnRight;
                    break;
                case TurnRight:
                    l_localPose.a -= l_adelta;
                    move = Done;
                    break;
                default:;
            }
            l_iCount++;

            double localScore, localLikelihood;
            localScore = computeWeight(p_lidar_cloud, l_localPose, p_gridInfo);

            // currentScore : 最高匹配度, bestLocalPose:最高匹配pose
            if (localScore > l_dCurrentScore)
            {
                l_dCurrentScore = localScore;
                l_bestLocalPose = l_localPose;
            }
            c_vecMoveList_.push_back(l_localPose);

        } while (move != Done);  //不满足，推出

        // bestPose: 最高匹配pose
        l_currentPose = l_bestLocalPose;
        if (l_dCurrentScore > l_dBestScore)
            l_bestPose = l_currentPose;

    } while (l_dCurrentScore > l_dBestScore
             || l_refinement
                    < 20);  //当移动后的pose匹配度不再增加（<==）,且次数超20，则推出，最小迭代次数20

    p_poseinit = l_bestPose;
    c_stPfMean_ = l_bestPose;

    return l_dBestScore;
}
}  // namespace wanjilocal
