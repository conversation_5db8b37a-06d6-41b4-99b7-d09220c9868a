/*
 * @Author: your name
 * @Date: 2021-06-01 16:31:16
 * @LastEditTime: 2021-06-01 16:59:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /wanji_local/include/mark/kdtree.h
 */
#pragma once
#ifndef __KDTREE_H__
#    define __KDTREE_H__
#    include "marktype.h"

//#ifdef __KDTREE_C
#    define KDTREE_EXT
// #else
// #define KDTREE_EXT extern
// #endif

#    define STRUCT_SIZE_KD_NODE_MARK sizeof(KD_NODE_MARK) >> 2
// typedef struct _KD_NODE_MARK KD_NODE_MARK;
struct _KD_NODE_MARK
{
    u8 leaf; /**< 1 if node is a leaf, 0 otherwise */  //是否叶子结点的标志
    u8 ki; /**< partition key index */  //分割位置(枢轴)的维数索引(哪一维是分割位置)，取值为1-128
    u16 n; /**< number of features */  //特征点的个数
    int kv; /**< partition key value */  //枢轴的值(所有特征向量在枢轴索引维数上的分量的中值)
    int offset;
    MARK_XY* features; /**< features at this node */  //此结点对应的特征点集合(数组)
    // KD_NODE_MARK* kd_father;
    struct _KD_NODE_MARK* kd_left; /**< left child */    //左子树
    struct _KD_NODE_MARK* kd_right; /**< right child */  //右子树
};
typedef struct _KD_NODE_MARK KD_NODE_MARK;

KD_NODE_MARK* kdtree_build_Mark(MARK_XY* p_MarkSet, u16 n, MARK_XY* p_psStructMarkAddr);
static KD_NODE_MARK*
kd_node_init_Mark1(MARK_XY* features, u16 n, KD_NODE_MARK* kd_node_Addr, int offset);
KD_NODE_MARK* kd_node_init_Mark(MARK_XY* features, u16 n);
void expand_kd_node_subtree_Mark(KD_NODE_MARK* kd_node, MARK_XY* p_psStructMarkAddr[]);
void assign_part_key_Mark(KD_NODE_MARK* kd_node);
void assign_part_key_Mark1(KD_NODE_MARK* kd_node, MARK_XY** p_MarkAddr);
void partition_features_Mark(KD_NODE_MARK* kd_node);
static double median_select_Mark(float* array, int n);
static double rank_select_Mark(float* array, int n, int r);
static void insertion_sort_Mark(float* array, int n);
static int partition_array_Mark(float* array, int n, float pivot);
void innerGetClosest(KD_NODE_MARK* pNode,
                     XY_TO_RelCoor point,
                     MARK_XY** res,
                     u32* nMinDis,
                     u16 p_IdentWindow);
void KDTree_GetClosest(KD_NODE_MARK* pNode,
                       XY_TO_RelCoor point,
                       MARK_XY** res,
                       u32* nMinDis,
                       u16 p_IdentWindow);
void Free_KD_Tree(void);
KD_NODE_MARK* BuildKdTreeMapInfo(MARK_SETS* p_SetMarks);

#endif
