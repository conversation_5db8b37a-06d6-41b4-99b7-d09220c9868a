#pragma once
#ifndef __EX_SRAM_H__
#    define __EX_SRAM_H__

#    include "kdtree.h"
#    include "marktype.h"

//#ifdef	__EX_SRAM_C
#    define EX_SRAM_EXT
//#else
//#define	EX_SRAM_EXT	extern
//#endif

#    define Mark_Edge_NUM 50000

#    define CHANNELSIZE 1
// EX_SRAM_EXT u16 g_u16Llevel;
// EX_SRAM_EXT u16 g_sRamTaskList11[20000]  ;//(BANK1_SRAM1_ADDR) ;
// EX_SRAM_EXT STRUCT_TASKLIST g_sRamTaskList[1] ;//(BANK1_SRAM1_ADDR) ;	//修正表模块#define
// MOVEAVE EX_SRAM_EXT STRUCT_REFLEXLIST g_sRamReflexList  ;//(BANK1_SRAM1_ADDR) ; EX_SRAM_EXT
// STRUCT_PIANXINCORR g_s16CodeZeroCorrectBuf ;//(BANK1_SRAM1_ADDR);

// u16 g_au32TanList_Offset[6] = {2999<<3,4039<<3,4788<<3,5157<<3,5524<<3,5539<<3};

// EX_SRAM_EXT u8 g_au8PosCorrBuf[7200];
// EX_SRAM_EXT STRUCT_TASKLIST g_sRamTaskList[1] ;//(BANK1_SRAM1_ADDR) ;	//修正表模块#define
// MOVEAVE EX_SRAM_EXT STRUCT_REFLEXLIST g_sRamReflexList  ;//(BANK1_SRAM1_ADDR) ;
// //修正表模块#define MOVEAVE EX_SRAM_EXT u16 g_au16RamBootFlashBuf[1];//[256*500]
// ;//(BANK1_SRAM1_ADDR) ;

// EX_SRAM_EXT List g_sList[10] ;//(BANK1_SRAM1_ADDR);
// EX_SRAM_EXT SqList g_au32TriSumEdge ;//(BANK1_SRAM1_ADDR);

// EX_SRAM_EXT XY2EqlTri g_auXY2EqlPoll[EQLTRI_SIZE]
// ;//(BANK1_SRAM1_ADDR);//全等四边形对应的全等三角形定位出的坐标值

// EX_SRAM_EXT u16 g_us16PulseWidthBuf01Ping[7200] ;//(BANK1_SRAM1_ADDR);//;//(CCM_RAM);
// //经过阈值处理过后的 EX_SRAM_EXT u16 g_us16PulseWidthBuf01Pong[7200]
// ;//(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的

// EX_SRAM_EXT u16 g_us16DistBufCopy[7200] ;//(BANK1_SRAM1_ADDR);

// EX_SRAM_EXT	u16 g_u16SysMode_NavOrMappingOrMark;
// EX_SRAM_EXT	u16 g_u16SysMode_SendData;
// EX_SRAM_EXT   MARKLAYER_ADDRESS g_au32LayerAddr ;//(CCM_RAM);
// EX_SRAM_EXT   MARKLAYER_ADDRESS g_au32LayerAddr1;

// EX_SRAM_EXT   u32     g_au32PulseEnd[CHANNELSIZE] ;//(CCM_RAM);
// EX_SRAM_EXT   u16     g_au16PulseState[CHANNELSIZE] ;//(CCM_RAM);
// EX_SRAM_EXT   u16     g_au16PulseWidth[CHANNELSIZE] ;//(CCM_RAM);

// EX_SRAM_EXT MARK_SETS g_sNavMarkLastOK ;

// EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFpgaPosCX5 ;//(CCM_RAM);

// EX_SRAM_EXT STRUCT_FILTER_TEMP g_sFilterDistTemp ;//(CCM_RAM);

// EX_SRAM_EXT STRUCT_FILTER_TARGET g_sFilterDistPosted ;//(CCM_RAM);

// EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterMark_NoSpeedCorr ;//(CCM_RAM);

// EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterDistShortRaw ;//(CCM_RAM);

// EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterMark_SpeedCorr ;//(CCM_RAM);

// EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterDistShortNew1 ;//(CCM_RAM);
// EX_SRAM_EXT STRUCT_PIANXINCORR g_s16CodeZeroCorrectBuf ;//(BANK1_SRAM1_ADDR);
// EX_SRAM_EXT	u16	g_au16TriEdge[Mark_Edge_NUM] ;//(BANK1_SRAM1_ADDR) ;

// EX_SRAM_EXT STRUCT_MAPPING_LIST g_sMappingList ;//(CCM_RAM);

// EX_SRAM_EXT u16 g_us16EchoBufPing[7200] ;//(CCM_RAM);
// EX_SRAM_EXT u16 g_us16EchoBufPong[7200] ;//(CCM_RAM);

// EX_SRAM_EXT STRUCT_NAV_MAKR g_sNavMark ;
// EX_SRAM_EXT u8  g_u8CurrLayerSetFlag;
// EX_SRAM_EXT u8 g_au8DMA_Uart_Data[30] ;
// EX_SRAM_EXT STRUCT_QUE_NET g_sQueNetPC ;
// EX_SRAM_EXT STRUCT_QUE_NET g_sQueNetPCBst ;
EX_SRAM_EXT void* memcpy_sram16(void* dest, const void* src, size_t count);

//#ifdef MOVEAVE

//#endif
// EX_SRAM_EXT void* memcpy_sram16(void *dest,const void*src, size_t count);
#endif
