#pragma once
#ifndef __ICPSPEEDCORR_H__

#    define __ICPSPEEDCORR_H__

#    include "marktype.h"
//#ifdef	__ICPSPEEDCORR_C

#    define ICPSPEEDCORR_EXT

//#else
//#define	ICPSPEEDCORR_EXT	extern
//#endif

ICPSPEEDCORR_EXT float
Cal_IcpSpeedAng(u8 size, XYANG2ROBOT* LaserPos, STRUCT_ICPMarkInfo* p_MarkPair, u16* p_MarkAng);
ICPSPEEDCORR_EXT void IcpSpeed(u32 p_TSdiff, ROBOT_XY* p_LaserPos);
ICPSPEEDCORR_EXT void Mark_Corr_BySpeed_Icp(STRUCT_SpeedIcp* p_SpeedIcp,
                                            u8 p_MarkNum,
                                            STRUCT_FILTER_TARGET* p_Filter,
                                            float p_CorrTime,
                                            STRUCT_FILTER_TARGET_LITE* p_ScanMark);
ICPSPEEDCORR_EXT void CalAngOffsetBySpeed360TOHalf_Icp(STRUCT_FILTER_TARGET* p_Filter,
                                                       STRUCT_SpeedIcp* p_SpeedIcp,
                                                       float p_CorrTime,
                                                       STRUCT_FILTER_TARGET_LITE* p_ScanMark);
ICPSPEEDCORR_EXT int Tran_To_Float_Icp(STRUCT_FPGA_SpeedIcp* p_psFpgaIcpSpeed,
                                       u8 num,
                                       u8* p_Buf,
                                       STRUCT_ICPMarkInfo* p_MarkPair);
ICPSPEEDCORR_EXT u32 Cal_IcpSpeed_PerMarkAng(XY_TO_RelCoor* p_ScanMark, XYANG2ROBOT* p_Laser_Pos);
ICPSPEEDCORR_EXT int Get_IcpSpeed_FromFpga(STRUCT_FPGA_SpeedIcp* p_FpgaIcpSpeed,
                                           u8 decMarkNum,
                                           u8 i,
                                           STRUCT_SpeedIcp* p_IcpSpeed);
ICPSPEEDCORR_EXT void Cal_RelSpeedIcp(STRUCT_ICPMarkInfo* p_MarkPair,
                                      u32 p_TSdiff,
                                      ROBOT_XY* p_LaserPos,
                                      u8 p_MinMarkNum_CalPos);
ICPSPEEDCORR_EXT int Match_CurPos_Icp(STRUCT_MarkMatch* p_IdenWindow, u32 p_TSdiff);
ICPSPEEDCORR_EXT u8 Find_ICP_MarkPair(ROBOT_XY* p_CurPos,
                                      ROBOT_XY* p_OldPos,
                                      STRUCT_ICPMarkInfo* p_MarkPair);
ICPSPEEDCORR_EXT void SortMarkByMarkId(ROBOT_XY* p_LaserPos);
ICPSPEEDCORR_EXT void Renew_IcpMarkXY(ROBOT_XY* p_LaserPos);

#endif
