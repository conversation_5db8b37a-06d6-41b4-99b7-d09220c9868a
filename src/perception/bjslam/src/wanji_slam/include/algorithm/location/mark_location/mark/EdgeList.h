/*
 * @Author: your name
 * @Date: 2021-06-02 13:56:28
 * @LastEditTime: 2021-06-02 14:34:06
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/include/algorithm/location/mark_location/mark/EdgeList.h
 */
#pragma once
#ifndef __EDGE_LIST_H__
#    define __EDGE_LIST_H__

#    include "marktype.h"
//#include "config.h"
//#ifdef __EDGE_LIST_C
#    define EDGE_LIST_EXT
//#else
//#define EDGE_LIST_EXT extern
//#endif

u32 Build_EdgeList_DiffResolution(u16* p_pack_cnt,
                                  u16* p_cnt,
                                  u32 p_last_dist,
                                  u8 ResolutionOffset,
                                  float MaxAng,
                                  u16 p_Res);
void Build_EdgeList(void);
EDGE_LIST_EXT u32 Return_Diff_Edge(MARK_XY* p_Mark1, MARK_XY* p_Mark2, u16 edge);
EDGE_LIST_EXT u32 Cal_Edge_TwoMark(u32 p_Diff_X, u32 p_Diff_Y);
EDGE_LIST_EXT u32 Return_Diff(int in1, int in2);
#endif
