#pragma once
#ifndef __TASKTARGETAPP_H__
#    define __TASKTARGETAPP_H__
//#ifdef	__TASKTARGETAPP_C
#    define TASKTARGETAPP_EXT
// #else
// #define	TASKTARGETAPP_EXT	extern
// #endif
#    include "marktype.h"
//#define ANG_ERROR 20
#    define XY_ERROR 150
#    define ANG_DIFF 1000
TASKTARGETAPP_EXT u8 Copy_UsingData(u8 p_u8pingpong,
                                    u16** p_u16pulse_aver,
                                    u16** p_u16pulse,
                                    u16** p_u16dist);
TASKTARGETAPP_EXT void Empty_UsingBuf(void);
TASKTARGETAPP_EXT void Filter_FakeMark_ByPointNum(u8 p_MarkNum, STRUCT_FILTER_TARGET* p_Filter);
TASKTARGETAPP_EXT void Filter_Mark_ByScanRadius(STRUCT_FILTER_TARGET* p_Filter);
TASKTARGETAPP_EXT u8 WorkMode_Continue_Position(u32 p_ScanTS, ROBOT_XY* p_PosJudge);
TASKTARGETAPP_EXT u8 WorkMode_Initial_Position(STRUCT_FILTER_TARGET_LITE* p_ScanMark,
                                               TIMESTAMP p_ScanOverTS,
                                               INPUTSPEED* p_Speed);
TASKTARGETAPP_EXT u8 Judge_InitialPos_Validity(ROBOT_XY* p_NewPos,
                                               ROBOT_XY* p_OldPos,
                                               TIMESTAMP p_ScanOverTS,
                                               INPUTSPEED* p_Speed);
TASKTARGETAPP_EXT void WorkMode_Add_Mapping(void);
TASKTARGETAPP_EXT void WorkMode_Scan_Mark(STRUCT_FILTER_TARGET* p_ScanMark,
                                          STRUCT_FILTER_TARGET_LITE* p_FilterMark,
                                          u16 p_MarkSize);
TASKTARGETAPP_EXT u8 WorkMode_MappingN_First(void);
TASKTARGETAPP_EXT u8 WorkMode_MappingP_First(void);
TASKTARGETAPP_EXT void WorkMode_Mapping_End(u16 p_ScanNum, STRUCT_MarkMatch* p_IdenWindow);
TASKTARGETAPP_EXT int Match_OldSetPos(STRUCT_MarkMatch* p_IdenWindow);
TASKTARGETAPP_EXT int
Match_CurPos(STRUCT_MarkMatch* p_IdenWindow, u32 p_TSdiff, ROBOT_XY* p_LaserPos);
TASKTARGETAPP_EXT u8 Get_NavMarkNum(u8 l_u8SeltMarkNum);
TASKTARGETAPP_EXT void Copy_NewAdd_Mark(u8 p_MarkNum);
TASKTARGETAPP_EXT u8 Filter_HasSet_Or_NotEnoughHalf_Mark(u16 p_ScanNum,
                                                         STRUCT_MarkMatch* p_IdenWindow);
TASKTARGETAPP_EXT u8 Get_PosBufType_Mapping(u8* p_PosOffset, ROBOT_XY* p_Pos, u8 p_PosNum);
TASKTARGETAPP_EXT u8 Get_PosBufType_Nav(u8* p_PosOffset, ROBOT_XY* p_Pos, u8 p_PosNum);
TASKTARGETAPP_EXT void Filter_SameMark_BySpeedRad(STRUCT_FILTER_TARGET* p_Filter);
TASKTARGETAPP_EXT void Save_LaserPos_To_AverBuf(ROBOT_XY* p_Cur_LaserPos,
                                                XYANG2ROBOT* p_LaserPosBuf);
TASKTARGETAPP_EXT void
Cal_Aver_LaserPos(ROBOT_XY* p_Cur_LaserPos, XYANG2ROBOT* p_LaserPosBuf, ROBOT_XY* p_Aver_LaserPos);
TASKTARGETAPP_EXT void TaskTargetApp(void* tdata);  //启动任务
TASKTARGETAPP_EXT void Memset_IsMarkFlag(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark);
TASKTARGETAPP_EXT u16 Find_IncludedAng_Min(u16 p_Ang_In1, u16 p_Ang_In2);
TASKTARGETAPP_EXT u16 Find_IncludedAng_And_Direction(u16 p_Ang_In1, u16 p_Ang_In2, u8* p_Direction);
TASKTARGETAPP_EXT void Send_ScanData(ROBOT_XY* p_SendPos,
                                     STRUCT_FILTER_TARGET* p_Mark,
                                     STRUCT_FILTER_TARGET_LITE* p_FilterMark,
                                     u16* p_Dist,
                                     u16* p_Pluse,
                                     u16* p_FilterPluse,
                                     u8 p_ScanNum,
                                     u32 p_ScanOverTS);
TASKTARGETAPP_EXT void TransPos_To_SendOutTS(ROBOT_XY* p_PosCur,
                                             ROBOT_XY* p_PosSend,
                                             ROBOT_XY* p_PosOld,
                                             INPUTSPEED* p_SpeedIn,
                                             TIMESTAMP p_TS_ScanOver);

#endif
