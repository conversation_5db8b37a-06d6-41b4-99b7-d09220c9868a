/*
 * @Author: your name
 * @Date: 2021-05-31 10:31:32
 * @LastEditTime: 2021-06-02 14:38:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /wanji_local/src/markMatch.h
 */

#pragma once

// #include <Eigen/Core>
#include <Eigen/Dense>
// #include <Eigen/Geometry>
#include <boost/thread.hpp>
#include <boost/thread/mutex.hpp>
#include <eigen_conversions/eigen_msg.h>

#include <cmath>
#include <fstream>
#include <iostream>
#include <mutex>
#include <omp.h>
#include <queue>
#include <random>
#include <string>
#include <termios.h>
#include <thread>
#include <vector>

#include <semaphore.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

#include "mark/ExSram.h"
#include "mark/FindMarkCenter.h"
#include "mark/IcpSpeedCorr.h"
#include "mark/Marks.h"
#include "mark/TaskTargetApp.h"
#include "mark/kdtree.h"
#include "mark/marktype.h"
class MarkMatch {
  public:
    MarkMatch()
    {
        init_();
    }
    ~MarkMatch() {}

#pragma region 公有函数

    /**
     * @description: 读取靶标csv
     * @param {string} p_strMarkMapPath csv路径
     * @param {MARK_SETS*} p_sMarkSets 读取靶标填充
     * @return {Bool} true:文件成功打开且读取到mark数量>=3
     */
    bool loadMarkMap(std::string p_strMarkMapPath, MARK_SETS* p_sMarkSets);

    /**
     * @description: 设置靶标地图
     * @param {MARK_SETS*} p_sMarkMap
     * @return {*}
     */
    void updateMarkMap(MARK_SETS* p_sMarkMap);

    /**
     * @description: 外部调用接口 传入靶标当前帧 返回位置
     * @param {STRUCT_FILTER_TARGET} *p_sScanMark 当前帧
     * @param {t}  p_t_out         匹配位姿
     * @param {q}  p_q_out
     * @param {vec} p_vMatchInfo   匹配关系
     * @param {Bool} p_bisInitMark 是否为靶标初始定位模式  默认为否
     * @return {Bool}
     */
    bool getMarkPose(STRUCT_FILTER_TARGET* p_sScanMark,
                     Eigen::Vector3d& p_t_out,
                     Eigen::Quaterniond& p_q_out,
                     STRUCT_FILTER_TARGET_LITE* p_sMatchInfo,
                     bool p_bisInitMark);

    /**
     * @description: 建立kdtree
     * @param {MARK_SETS} p_SetMarks 地图靶标
     * @return {*}
     */
    KD_NODE_MARK* buildKdTreeMapInfo(MARK_SETS* p_SetMarks);

    /**
     * @description: 清空kdtree
     * @param {KD_NODE_MARK*}  p_sKd_root  kd指针
     * @return {*}
     */
    void freeKdTree(KD_NODE_MARK* p_sKd_root);

#pragma endregion

  private:
#pragma region 私有函数

    /**
     * @description: 初始化
     * @param {*}
     * @return {*}
     */
    void init_();

    /**
     * @description: 外部传入STRUCT_FILTER_TARGET 转 STRUCT_FILTER_TARGET_LITE 用于全局
     * @param {STRUCT_FILTER_TARGET} p_Filter 外部传入的当前帧靶标结构体
     * @return {*}
     */
    void targetToLite_(STRUCT_FILTER_TARGET* p_Filter);

    /**
     * @description: 设置0位姿,因传入的靶标当前帧为全局，不需要再转一道
     * @param {t} p_t 0位置
     * @param {q} p_q 0方向
     * @return {*}
     */
    void setPose_(const Eigen::Vector3d& p_t, const Eigen::Quaterniond& p_q);

    /*************************************************
    Function		:	matchedMarksToPointCloud_
    Description		:	获取match靶标数量，更新mark匹配位姿
    Input			:	p_t_out: 位置
                        p_q_out: 姿势
    Output			:	无
    Return			:	true：mark匹配成功 数量>=3
    Others			:	无
    *************************************************/
    bool matchedMarksToPointCloud_(Eigen::Vector3d& p_t_out, Eigen::Quaterniond& p_q_out);

    void
    markStructToEigen_(ROBOT_XY p_structMark, Eigen::Vector3d& t_mark, Eigen::Quaterniond& q_mark);

    /**
     * //mark全局匹配成功后check
     * 检查靶标定位是否有效，当前帧mark定位和上一帧定位计算增量，与上一帧增量对比，超过1.5倍就算无效
     */
    bool checkMarkPose(Eigen::Vector3d t_now,
                       Eigen::Quaterniond q_now,
                       Eigen::Vector3d t_last,
                       Eigen::Quaterniond q_last,
                       double l_time_now,
                       double l_time_last,
                       Eigen::Vector3d t_lastIncre,
                       Eigen::Quaterniond q_lastIncre);

    double yawIn2PI_(const Eigen::Quaterniond& q);

    double rad2deg_(double radians);

    Eigen::Quaterniond yaw2Quat_(double Yaw);

    KD_NODE_MARK* makeEmpty_(KD_NODE_MARK* tree);

#pragma endregion
};
