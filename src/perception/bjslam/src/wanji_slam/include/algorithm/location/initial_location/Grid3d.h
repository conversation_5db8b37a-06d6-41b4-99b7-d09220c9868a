/*
 * @Author: your name
 * @Date: 2021-05-08 13:41:07
 * @LastEditTime: 2021-10-25 13:31:28
 * @LastEditors: zushuang
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_amcl3d/include/amcl/Grid3d.h
 */
/*!
 * @file Grid3d.h
 * @copyright Copyright (c) 2019, FADA-CATEC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once
#include "PointCloudTools.h"
#include "header.h"

namespace wanjilocal {

template <typename PointMap> class Grid3d {
  public:
    explicit Grid3d() : c_fResolution_(0.3), c_mapType_(MAP_2D) {}

    virtual ~Grid3d() {}

    /**
     * @description: 设置地图类型，2D或者3D
     * @param {const MAP_DIMENSION} &p_mapType
     * @return {*}
     */
    void setMapType(const MAP_DIMENSION& p_mapType);

    /**
     * @description: 设置栅格大小
     * @param {float} p_fResolution
     * @return {*}
     */
    void setResolution(float p_fResolution = 0.3);

    /**
     * @description: 根据输入点云生成栅格概率地图
     * @param {Ptr} p_pMapMerge 点云
     * @param {const double} p_dSensorDev 点云精度标准差
     * @return {*} 成功则返回true，失败false
     */
    bool open(typename pcl::PointCloud<PointMap>::Ptr p_pMapMerge, const double p_dSensorDev);

    /**
     * @description: 获取降采样后的点云
     * @param {*}
     * @return {*}
     */
    typename pcl::PointCloud<PointMap>::Ptr getMapDS(void)
    {
        return c_pcMapCloudDS_;
    }

    /**
     * @description: 获取生成的栅格地图
     * @param {*}
     * @return {*}
     */
    Grid3dInfo::Ptr getGridInfo(void)
    {
        return c_gridInfo_;
    }

    /**
     * @description: 获取栅格地图边界信息
     * @param {*}
     * @return {*}
     */
    PointCloudInfoS getPointEdgeInfo(void)
    {
        return c_totalMapCloudInfo_;
    }

  private:
    void computeBoxAndGrid_(const double p_dSensorDev);

    Grid3dInfo::Ptr computeGrid(const PointCloudInfoS& p_boxInfo,
                                const double p_dSensorDev,
                                typename pcl::PointCloud<PointMap>::Ptr p_pcCloudIn);

    PointCloudInfoS
    extendBoxInfo_(PointMap p_minP, PointMap p_maxP, float p_fResol, float p_fResolmul);

    void init_(void);

  private:
    typename pcl::PointCloud<PointMap>::Ptr c_pcMapCloud_;
    typename pcl::PointCloud<PointMap>::Ptr c_pcMapCloudDS_;
    typename pcl::VoxelGrid<PointMap> c_mapVoxelGrid_;  //点云地图体素滤波

    Grid3dInfo::Ptr c_gridInfo_;  //每个轨迹点对应的栅格地图，维度与抽稀后的轨迹点数量一致。

    PointCloudInfoS c_totalMapCloudInfo_;

  private:
    float c_fResolution_;      //栅格地图分辨率 0.3
    MAP_DIMENSION c_mapType_;  //地图模式，2d还是3d
};

}  // namespace wanjilocal

#include "impl/Grid3d.hpp"
