#pragma once
#ifndef __FINDMARKCENTER_H__
#    define __FINDMARKCENTER_H__
#    include "marktype.h"
//#include "config.h"
//#include "ExSram.h"
//#include "TaskTargetApp.h"
//#include "TaskDataApp.h"
//#include "Marks.h"

//#ifdef	__FINDMARKCENTER_C
#    define FINDMARKCENTER_EXT
//#else
//#define	FINDMARKCENTER_EXT	extern
//#endif

FINDMARKCENTER_EXT void
Find_ScanMark_PluseDist(u16* p_pulse, u16* p_Ratio, u16* p_dist, float p_CorrTime, u16* p_aver);
FINDMARKCENTER_EXT u16 Find_80Percent_Sta_Point_PulseDist(u16* Width_Aver,
                                                          u16 Max_Width_Point,
                                                          u16 p_Width_80Percent);
FINDMARKCENTER_EXT u16 Find_80Percent_End_Point_PulseDist(u16* Width_Aver,
                                                          u16 Max_Width_Point,
                                                          u16 p_Width_80Percent);
FINDMARKCENTER_EXT void Renew_MarkCenter_PulseDist(u16 p_Max_Width_Point,
                                                   u16* p_PulseDistRatio,
                                                   STRUCT_MARK_INFO* p_MarkInfo,
                                                   u16* p_dist);
FINDMARKCENTER_EXT void
FindPeakData(u16* data, u16* p_dist, u16 Sta, u16 End, STRUCT_MARK_INFO* p_MarkInfo, u8* mul);
FINDMARKCENTER_EXT void Get_MaxWidth_WD(u8 p_u8MaxMarkNum,
                                        u8 p_MarkNum,
                                        u16* p_u16start,
                                        u16* p_u16end,
                                        u16* p_Width_Carve,
                                        STRUCT_FILTER_TARGET* p_Filter,
                                        u16* p_dist);
FINDMARKCENTER_EXT u8 Find_MaxWidthPoint_WD(u16* Width_Carve,
                                            STRUCT_FILTER_TARGET* p_Filter,
                                            STRUCT_CARVE* p_CraveBuf,
                                            u16* p_dist);
FINDMARKCENTER_EXT void
Find_MaxMin_Dist_WD(u16 sta, u16 end, u16* Dist, u16* max_dist, u16* min_dist, u16* Min_offset);
FINDMARKCENTER_EXT int Filter_Mark_By_Ref_WD(u16* PulseWidth, u16* Dist, u16* p_RefBuf);
FINDMARKCENTER_EXT void GetOutlineOfMarks_WD(u16* pResults_t,
                                             u16* pResults_d,
                                             u16 p_Offset,
                                             STRUCT_CARVE* p_CarveBuf,
                                             u16* p_Ref);
FINDMARKCENTER_EXT u8 Copy_UsingData_WD(u8 p_u8pingpong,
                                        u16** p_u16pulse_aver,
                                        u16** p_u16pulse,
                                        u16** p_u16dist,
                                        u16** p_u16ref);
FINDMARKCENTER_EXT u16 Find_Max_WD(u16 p_u16start, u16 p_u16end, u16* p_Data);
FINDMARKCENTER_EXT void
DeletCloseMark_WD(STRUCT_FILTER_TARGET* p_Filter, u16* p_Dist, u16* p_Pluse, u16* p_PulseDistRatio);
FINDMARKCENTER_EXT u8 FilterMark_ByMarkMaxMinDist_WD(STRUCT_FILTER_TARGET* p_Filter,
                                                     u16* p_Dist,
                                                     u16* p_Pluse,
                                                     u16* p_PulseDistRatio);
FINDMARKCENTER_EXT u16 Return_FilterMinIncludedAng_WD(STRUCT_MARK_INFO* p_Mark1,
                                                      STRUCT_MARK_INFO* p_Mark2);
FINDMARKCENTER_EXT void
Find_MaxMin_Dist_Sigle(u16* Dist, STRUCT_MARK_INFO* p_MarkInfo, u16* p_DistMax, u16* p_DistMin);
FINDMARKCENTER_EXT u8 Find_MarkCenterByPulseAndDist(u16* p_Dist,
                                                    u16* p_Pulse,
                                                    STRUCT_MARK_INFO* p_MarkInfo);
#endif
