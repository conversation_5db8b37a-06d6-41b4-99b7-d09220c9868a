/**
 * @file cloud_coloring.hpp
 * <AUTHOR> Li
 * @brief 点云上色实现
 * @version 0.1
 * @date 2023-07-12
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "../cloud_coloring.h"
#include "tic_toc.h"
#include <Eigen/Dense>
#include <pcl/search/kdtree.h>
namespace wj_cColor {

template <typename PointT> void CloudColorBase<PointT>::image(const std::string& filename) {}

template <typename PointT> void CloudColor_PCA<PointT>::color(RGBCloud& p_pcOut)
{
    TicToc tic;
    c_pRGBc_->points.resize(c_pPc_->points.size());
    pcaToRgb(c_pPc_, *c_pRGBc_, c_fGrid_);
    p_pcOut.swap(*c_pRGBc_);
    p_pcOut.height = 1;
    p_pcOut.width = p_pcOut.size();
    // std::cout << "CloudColor_PCA cost time : " << tic.toc() << std::endl;
}

template <typename PointT>
void CloudColor_PCA<PointT>::pcaToRgb(PointCloudPtr p_pPc, RGBCloud& p_pRGBc, double p_fRadius)
{
    this->setGreyRange(0.75, 1.0);
    // 创建search方法
    typename pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>);
    tree->setInputCloud(p_pPc);
    std::vector<int> indices;      // 存储查询近邻点索引
    std::vector<float> distances;  // 存储近邻点对应距离的平方
    //! 下面的处理临时修改,地面白色，其他蓝色 屏蔽部分为原来的代码
    for (size_t i = 0; i < p_pPc->points.size(); ++i)
    {
        if (tree->radiusSearch(p_pPc->points[i], p_fRadius, indices, distances) > 10)
        {  // this->GreyToRGB(calcPrincipal(*p_pPc, indices), p_pRGBc[i]);
            RGB rgb_blue;
            rgb_blue.r = 0;
            rgb_blue.g = 0;
            rgb_blue.b = 255;
            p_pRGBc[i] = rgb_blue;
        }
        else
        {
            //         RGB rgb_max;
            // this->GreyToRGB(c_greyRange_[1], rgb_max);
            // p_pRGBc[i] = rgb_max;
            RGB rgb_white;
            rgb_white.r = 255;
            rgb_white.g = 255;
            rgb_white.b = 255;
            p_pRGBc[i] = rgb_white;
        }  //白色
    }
}

template <typename PointT> void CloudColorBase<PointT>::GreyToRGB(float p_fLabel, RGB& rgb)
{
    int grayColor = (255 * ((p_fLabel - c_greyRange_[0]) / c_greySize_));
    grayColor = grayColor < 0 ? 0 : (grayColor > 255 ? 255 : grayColor);
    if (grayColor < 128)  //区间2-blue
    {                     // r=0/g=255/b->down
        rgb.r = 0;
        rgb.g = std::round(1.99 * grayColor);
        rgb.b = std::round(255 - 1.99 * grayColor);
    }
    else  //区间3-red
    {     // r->up/g=255/b=0
        rgb.r = 2 * grayColor - 255;
        rgb.g = 510 - 2 * grayColor;
        rgb.b = 0;
    }
}
template <typename PointT>
float CloudColor_PCA<PointT>::calcPrincipal(PointCloud& p_pc, std::vector<int> p_idx)
{
    // 点云分布
    int l_iPointNum = p_idx.size();
    Eigen::Matrix<double, 1, 2> l_center;
    Eigen::MatrixXd l_matA0;
    l_matA0.resize(l_iPointNum, 2);
    for (int j = 0; j < l_iPointNum; j++)
    {
        l_matA0(j, 0) = p_pc.points[p_idx[j]].x;
        l_matA0(j, 1) = p_pc.points[p_idx[j]].y;
        l_center += l_matA0.row(j);
    }
    // 计算这个5个最近邻点的中心
    l_center = l_center / float(l_iPointNum);
    // 协方差矩阵
    Eigen::Matrix2d l_covMat = Eigen::Matrix2d::Zero();
    for (int j = 0; j < l_iPointNum; j++)
    {
        Eigen::Matrix<double, 2, 1> l_tmpZeroMean;
        l_tmpZeroMean = (l_matA0.row(j) - l_center).transpose();
        l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
    }
    Eigen::SelfAdjointEigenSolver<Eigen::Matrix2d> l_saes(l_covMat);
    return (l_saes.eigenvalues().normalized())[1];
    //? 这里为什么拿大小为中间的特征值不明所以
}
}  // namespace wj_cColor
