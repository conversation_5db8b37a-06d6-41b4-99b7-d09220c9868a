/**
 * @file cloud_sample.hpp
 * <AUTHOR> Li
 * @brief 栅格降采样实现函数
 * @version 0.1
 * @date 2023-07-10
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "../cloud_sample.h"
namespace wj_sample {
#pragma region
template <typename PointT> void Sample_Octree<PointT>::filter(PointCloud& p_pcOut)
{
    copyCloud_(p_pcOut);
    Eigen::Vector4f l_min, l_max;
    if (c_bSampleFromOrigin_)
    {
        pcl::getMinMax3D<PointT>(*c_pc_, l_min, l_max);
        for (auto& p : c_pc_->points)
            p.getVector4fMap() -= l_min;
    }

    //输入原始点云
    c_pTree_->setInputCloud(c_pc_);
    //将点云中的点，添加到八叉树中进行管理
    c_pTree_->addPointsFromInputCloud();
    //获取所有占用体素的中心点坐标
    // c_pTree_->getVoxelCentroids(p_pcOut.points);

    // 构建Octree
    std::vector<int> vec_total_index;  //要保留的点

    for (auto iter = c_pTree_->leaf_begin(); iter != c_pTree_->leaf_end(); ++iter)
    {
        auto key = iter.getCurrentOctreeKey();

        auto it_key = c_pTree_->findLeaf(key.x, key.y, key.z);
        if (it_key != nullptr)
        {
            int i = iter.getLeafContainer().getPointIndex();  //获取该分支栅格的最后一个点

            vec_total_index.push_back(i);
        }
    }

    pcl::copyPointCloud(*(c_pc_), vec_total_index, p_pcOut);

    if (c_bSampleFromOrigin_)
    {
        for (auto& p : p_pcOut.points)
            p.getVector4fMap() += l_min;
    }
    p_pcOut.height = 1;
    p_pcOut.width = p_pcOut.points.size();
    c_pTree_->deleteTree();  //删除树释放内存
}
template <typename PointT> void Sample_Octree<PointT>::setLeafSize(float p_fLeafsize)
{
    // 如果栅格大小发生变化，必须重新实例化
    if (c_fLeafSize_ != p_fLeafsize && p_fLeafsize > 0)
    {
        c_fLeafSize_ = p_fLeafsize;
        c_pTree_.reset(new pcl::octree::OctreePointCloud<PointT>(c_fLeafSize_));
    }
}
template <typename PointT>
void Sample_Octree<PointT>::setLeafSize(float p_fLeafsize_x,
                                        float p_fLeafsize_y,
                                        float p_fLeafsize_z)
{
    if (p_fLeafsize_x > 0 && p_fLeafsize_y > 0 && p_fLeafsize_z > 0)
    {
        float l_fLeafsize = p_fLeafsize_x > p_fLeafsize_y
                                ? (p_fLeafsize_x > p_fLeafsize_z ? p_fLeafsize_x : p_fLeafsize_z)
                                : (p_fLeafsize_y > p_fLeafsize_z ? p_fLeafsize_y : p_fLeafsize_z);
        setLeafSize(l_fLeafsize);
    }
}
#pragma endregion

#pragma region  // Sample_Voxel
template <typename PointT> void Sample_Voxel<PointT>::filter(PointCloud& p_pcOut)
{
    copyCloud_(p_pcOut);
    Eigen::Vector4f l_min, l_max;
    if (c_bSampleFromOrigin_)
    {
        pcl::getMinMax3D<PointT>(*c_pc_, l_min, l_max);
        for (auto& p : c_pc_->points)
            p.getVector4fMap() -= l_min;  //所有点向下平移l_min
    }
    // VOXELGRID滤波器的使用
    c_pTree_->setInputCloud(c_pc_);
    c_pTree_->filter(p_pcOut);
    if (c_bSampleFromOrigin_)
    {
        for (auto& p : p_pcOut.points)
            p.getVector4fMap() += l_min;  //加回去
    }
}
template <typename PointT> void Sample_Voxel<PointT>::setLeafSize(float p_fLeafsize)
{
    if (p_fLeafsize > 0)
        c_fLeafSize_ = p_fLeafsize;
    if (c_pTree_)
        c_pTree_->setLeafSize(c_fLeafSize_, c_fLeafSize_, c_fLeafSize_);
}
template <typename PointT>
void Sample_Voxel<PointT>::setLeafSize(float p_fLeafsize_x,
                                       float p_fLeafsize_y,
                                       float p_fLeafsize_z)
{
    if (p_fLeafsize_x > 0 && p_fLeafsize_y > 0 && p_fLeafsize_z > 0)
    {
        if (c_pTree_)
            c_pTree_->setLeafSize(p_fLeafsize_x, p_fLeafsize_y, p_fLeafsize_z);
    }
}
#pragma endregion
}  // namespace wj_sample
