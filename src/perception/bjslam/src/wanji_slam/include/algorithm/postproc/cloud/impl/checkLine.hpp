/**
 * @file checkLine.hpp
 * <AUTHOR> Li
 * @brief 线性检测
 * @version 0.1
 * @date 2023-07-10
 *
 * @copyright Copyright (c) 2023
 *
 */
#include <Eigen/Dense>
#include <pcl/features/normal_3d.h>
#include <pcl/point_cloud.h>
#include <typeinfo>
template <typename PointT> class checkLine {
  public:
    enum CheckLinarityMode { LinearMode_PCA = 0, LinearMode_Noraml };

    checkLine(/* args */) {}
    virtual bool checkLinealize(boost::shared_ptr<pcl::PointCloud<PointT>>& pc) = 0;
    virtual ~checkLine() {}
};
template <typename PointT> class checkLine_PCA : public checkLine<PointT> {
  private:
    /* data */
  public:
    checkLine_PCA() {}
    /**
     * @brief 基于法向量的三维点云特征点提取
     *
     * @param pc
     * @return true
     * @return false
     */
    virtual bool checkLinealize(boost::shared_ptr<pcl::PointCloud<PointT>>& pc)
    {
        Eigen::Vector3d l_center(0, 0, 0);
        for (size_t j = 0; j < pc->size(); j++)
        {
            //累加三轴数值
            l_center = l_center + (pc->points[j].getVector3fMap().template cast<double>());
        }

        l_center = l_center / float(pc->size());  //计算平均(重心)
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (size_t j = 0; j < pc->size(); j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean =
                (pc->points[j].getVector3fMap().template cast<double>()) - l_center;
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }
        l_covMat = l_covMat / float(pc->size() - 1);  // 计算协方差矩阵
        //计算归一化协方差矩阵的特征向量和特征值
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes(l_covMat);

        //判断非直线1:主成分足够大且主成分是z轴
        //(第3个数据为最大的特征值)主成分足够大//normalized()归一化
        if ((l_saes.eigenvalues().normalized())[2] < 0.9)
            return false;
        //最大主成分对应的特征向量(第3列)
        Eigen::Vector3d unitD = l_saes.eigenvectors().col(2);  //获取第三列
        if (std::abs(unitD[2]) < 0.9)  // 第三列的Z值是否足够大,意味着是否指向Z轴方向
            return false;
        return true;
    }
    virtual ~checkLine_PCA() {}
};
template <typename PointT> class checkLine_Normal : public checkLine<PointT> {
  private:
    pcl::NormalEstimation<PointT, pcl::Normal> normal_est;
    pcl::PointCloud<pcl::Normal>::Ptr normal_rg;

  public:
    checkLine_Normal(/* args */)
    {
        normal_rg.reset(new pcl::PointCloud<pcl::Normal>());
    }
    virtual bool checkLinealize(boost::shared_ptr<pcl::PointCloud<PointT>>& pc)
    {
        int mode = 1;
        bool res = false;
        int searchsize = 0;
        std::vector<float> l_tmp;
        size_t i = 0, cnt = 0;
        std::vector<Eigen::Vector3d> l_vNearCorners;
        Eigen::Vector3d l_center(0, 0, 0);
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes;
        if (pc->size() < 5)
        {
            // std::cout << "size less" << std::endl;
            return false;
        }
        searchsize = pc->size() / 5 > 5 ? pc->size() / 5 : 5;
        normal_est.setInputCloud(pc);
        normal_est.setKSearch(searchsize);
        normal_est.compute(*normal_rg);

        switch (mode)
        {
            case 0:  //统计法线
                for (i = 0; i < pc->points.size(); i++)
                {
                    l_tmp.push_back(std::fabs(normal_rg->points[i].normal_z));
                }
                std::sort(l_tmp.begin(), l_tmp.end());
                // std::cout << "normal:  ";
                // for (const auto& c : l_tmp)
                // std::cout << c << " ";
                // std::cout << std::endl;
                for (i = 0; i < l_tmp.size(); i++)
                {
                    if (l_tmp[i] > 0.2)
                        break;
                }
                cnt = i;
                if (cnt > l_tmp.size() / 2)
                {
                    // std::cout << "save line" << cnt << "/" << l_tmp.size() << std::endl;
                    res = true;
                }
                else
                    res = false;
                break;
            case 1:  //计算法线的方差

                for (i = 0; i < pc->size(); i++)
                {
                    l_center =
                        l_center
                        + (normal_rg->points[i].getNormalVector3fMap().template cast<double>());
                }

                l_center = l_center / float(pc->size());
                // std::cout << "center :" << l_center << std::endl;
                // 协方差矩阵

                for (i = 0; i < pc->size(); i++)
                {
                    Eigen::Matrix<double, 3, 1> l_tmpZeroMean =
                        (normal_rg->points[i].getNormalVector3fMap().template cast<double>())
                        - l_center;
                    l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
                }

                // 计算协方差矩阵的特征值和特征向量，用于判断这5个点是不是呈线状分布，此为PCA的原理
                l_saes.compute(l_covMat);

                //判断非直线
                // std::cout << "l_covMat :" << l_covMat << std::endl;
                // std::cout << "eigenvaluse :" << l_saes.eigenvalues().normalized() << std::endl;
                // std::cout << "normal : " << l_saes.eigenvectors() << std::endl;
                if ((l_saes.eigenvalues().normalized())[2] < 0.995)
                {
                    return false;
                }
                // l_unitDirection = l_saes.eigenvectors().col(2);
                return true;
                break;
            default: break;
        }

        return res;
    }
    virtual ~checkLine_Normal() {}
};
