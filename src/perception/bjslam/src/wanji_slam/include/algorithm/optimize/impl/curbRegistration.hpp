/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-10-09 10:28:54
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-14 14:05:31
 */
#pragma once
#include "../curbRegistration.h"

struct LidarCurbFactor
{
    LidarCurbFactor(Eigen::Vector3d curr_point_,
                    Eigen::Vector3d last_point_a_,
                    Eigen::Vector3d last_point_b_)
        : curr_point(curr_point_), last_point_a(last_point_a_), last_point_b(last_point_b_)
    {
    }

    template <typename T> bool operator()(const T* q, const T* t, T* residual) const
    {
        Eigen::Matrix<T, 3, 1> cp{T(curr_point.x()), T(curr_point.y()), T(curr_point.z())};
        Eigen::Matrix<T, 3, 1> lpa{T(last_point_a.x()), T(last_point_a.y()), T(last_point_a.z())};
        Eigen::Matrix<T, 3, 1> lpb{T(last_point_b.x()), T(last_point_b.y()), T(last_point_b.z())};

        T quaternion[4];
        Eigen::Matrix<T, 3, 1> angleAxis{T(0.0), T(0.0), q[0]};

        // const T theta = q[0];
        ceres::AngleAxisToQuaternion(angleAxis.data(), quaternion);
        Eigen::Quaternion<T> q_last_curr{
            quaternion[0], quaternion[1], quaternion[2], quaternion[3]};
        q_last_curr.normalize();

        Eigen::Matrix<T, 3, 1> t_last_curr{t[0], t[1], t[2]};

        Eigen::Matrix<T, 3, 1> lp;

        lp = q_last_curr * cp + t_last_curr;

        Eigen::Matrix<T, 3, 1> de = lpa - lpb;

        Eigen::Matrix<T, 3, 1> nu = (lp - lpa).cross(lp - lpb);  //叉乘cross

        residual[0] = nu.norm() / de.norm();

        return true;
    }

    static ceres::CostFunction* Create(const Eigen::Vector3d curr_point_,
                                       const Eigen::Vector3d last_point_a_,
                                       const Eigen::Vector3d last_point_b_)
    {
        return (new ceres::AutoDiffCostFunction<LidarCurbFactor, 1, 1, 3>(
            //					             ^  ^  ^
            //					             |  |  |
            //			      残差的维度 ____|  |  |
            //			 优化变量q的维度 _______|  |
            //			 优化变量t的维度 __________|
            new LidarCurbFactor(curr_point_, last_point_a_, last_point_b_)));
    }

    Eigen::Vector3d curr_point, last_point_a, last_point_b;
};

template <typename PS, typename PT>
void CurbRegistration<PS, PT>::setInputSource(const PointCloudSourceConstPtr& pcSrc)
{
    c_pSrc_ = pcSrc;
}
template <typename PS, typename PT>
void CurbRegistration<PS, PT>::setInputTarget(const PointCloudTargetConstPtr& pcTar)
{
    if (c_pTar_)
        *c_pTar_ = *pcTar;
    else
    {
        c_pTar_.reset(new PointCloudTarget());
        *c_pTar_ = *pcTar;
    }
    if (!c_pTar_ || !c_pTar_->points.size())
        return;
    c_kdTree_->setInputCloud(c_pTar_);
}

template <typename PS, typename PT>
bool CurbRegistration<PS, PT>::matchCurb_(ceres::Problem& p_pbm,
                                          ceres::LossFunction* p_pLossFunc,
                                          double* p_pdParaQ,
                                          double* p_pdParaT)
{
    PS l_pointSrc;
    //方向向量
    Eigen::Vector3d l_unitDirection(0, 0, 0);

    Eigen::Vector3d l_prePntA, l_prePntB, l_currPnt;
    float l_fDisCurr2AB = 0;
    s_SmoothZDiff* l_sCloudSmoothZdiff;
    c_qEst_ = Eigen::Quaterniond::Identity();
    c_tEst_ = Eigen::Vector3d::Zero();
    int c_iCorMatchNum_ = 0;
    c_normal_.setZero();
    int c_iNum1_ = 0;
    int c_iNum2_ = 0;
    int c_iNum3_ = 0;
    int c_iNum4_ = 0;
    int c_iNum5_ = 0;
    int c_iNum6_ = 0;

    for (int i = 0; i < (int)c_pSrc_->size(); i++)
    {
        PT l_pointSearch;
        std::vector<int> l_viPntSrhIdx;
        std::vector<float> l_vfPntSrhSqDis;

        l_pointSrc = c_pSrc_->points[i];
        l_pointSearch.getVector4fMap() = l_pointSrc.getVector4fMap();

        if (c_kdTree_->radiusSearch(l_pointSearch, c_fSearchRadius_, l_viPntSrhIdx, l_vfPntSrhSqDis)
            <= 0)
        {
            continue;
        }
        c_iNum1_++;
        //进行z轴排序
        l_sCloudSmoothZdiff = new s_SmoothZDiff[l_viPntSrhIdx.size()];

        for (int j = 0; j < (int)l_viPntSrhIdx.size(); j++)
        {
            l_sCloudSmoothZdiff[j].m_ZDiff =
                std::fabs(c_pTar_->points[l_viPntSrhIdx[j]].z - l_pointSearch.z);
            l_sCloudSmoothZdiff[j].m_idx = l_viPntSrhIdx[j];
        }
        std::sort(l_sCloudSmoothZdiff, l_sCloudSmoothZdiff + l_viPntSrhIdx.size(), s_SmoothZDiff());

        //提取有效高差范围的点，计算中心点
        //匹配点容器
        std::vector<Eigen::Vector3d> l_vNearCorners;
        Eigen::Vector3d l_center(0, 0, 0);
        for (size_t j = 0; j < l_viPntSrhIdx.size(); j++)
        {
            // z轴插值过大，舍弃点
            if (l_sCloudSmoothZdiff[j].m_ZDiff > 0.1)
            {
                continue;
            }
            int l_index = l_sCloudSmoothZdiff[j].m_idx;
            Eigen::Vector3d l_tmpCor(
                c_pTar_->points[l_index].x, c_pTar_->points[l_index].y, c_pTar_->points[l_index].z);
            l_center = l_center + l_tmpCor;
            l_vNearCorners.push_back(l_tmpCor);
        }
        c_iNum2_++;
        delete[] l_sCloudSmoothZdiff;
        if (l_vNearCorners.size() < 5)
        {
            continue;
        }
        c_iNum3_++;
        // 计算最近邻点的中心
        l_center = l_center / float(l_vNearCorners.size());

        // 协方差矩阵
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (size_t j = 0; j < l_vNearCorners.size(); j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean = l_vNearCorners[j] - l_center;
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }

        // 计算协方差矩阵的特征值和特征向量，用于判断这5个点是不是呈线状分布，此为PCA的原理
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes(l_covMat);
        l_unitDirection = l_saes.eigenvectors().col(2);
        if (!(l_saes.eigenvalues()[2] > 10 * l_saes.eigenvalues()[1]))
            continue;
        c_iNum4_++;

        // std::cout<<"l_unitDirection: "<< l_unitDirection.transpose() <<std::endl;
        // std::cout<<"c_qInitialQ_: "<< getYawVector3d(c_qInitialQ_).transpose() <<std::endl;

        double l_angle = fabs(getAngleDiff(getYawVector3d(c_qInitialQ_), l_unitDirection));
        if (l_angle < 170 && l_angle > 10)
            continue;
        c_iNum5_++;

        l_center -= c_vInitialT_;
        l_prePntA = 0.1 * l_unitDirection + l_center;
        l_prePntB = -0.1 * l_unitDirection + l_center;
        l_currPnt << l_pointSearch.x, l_pointSearch.y, l_pointSearch.z;
        l_currPnt -= c_vInitialT_;
        l_fDisCurr2AB = distFromPointToLine_(l_currPnt, l_prePntA, l_prePntB);
        if (l_fDisCurr2AB > 0.2)
            continue;

        c_iNum6_++;
        ceres::CostFunction* l_pCostFunc = LidarCurbFactor::Create(l_currPnt, l_prePntA, l_prePntB);
        p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
        c_iCorMatchNum_++;
        c_normal_ += l_unitDirection;
    }
    c_normal_.normalize();
#ifdef MATCHDEBUG
    printf("curbMatch all %d | [%d - %d - %d - %d - %d- %d] | %d\n",
           (int)c_pSrc_->size(),
           c_iNum1_,
           c_iNum2_,
           c_iNum3_,
           c_iNum4_,
           c_iNum5_,
           c_iNum6_,
           c_iCorMatchNum_);
#endif
    if (c_iCorMatchNum_ <= 2)
        return false;
    return true;
}
template <typename PS, typename PT>
bool CurbRegistration<PS, PT>::align(Eigen::Quaterniond& p_qCorr,
                                     Eigen::Vector3d& p_tCorr,
                                     Eigen::Vector3d& p_tCorr1)
{
    if (!c_pTar_ || !c_pTar_->points.size())
        return false;

    // 点云特征匹配时的优化变量
    // double l_daParaQ[4] = {0, 0, 0, 1};
    // double l_daParaT[3] = {0, 0, 0};
    bool l_bMatchFlag = false;
    double q = 0;
    double t[3] = {0, 0, 0};
    p_qCorr = Eigen::Quaterniond::Identity();
    p_tCorr = Eigen::Vector3d::Zero();

    // ceres::LossFunction* l_lossFunPlane = new ceres::HuberLoss(5);
    ceres::LossFunction* l_lossFunLine = NULL;
    // ceres::LocalParameterization* l_qParam = new ceres::EigenQuaternionParameterization();
    ceres::Problem::Options l_pbmOpt;
    ceres::Problem l_pbm(l_pbmOpt);
    l_pbm.AddParameterBlock(&q, 1);
    l_pbm.AddParameterBlock(t, 3);

    // xianzhiz = true;
    l_bMatchFlag = matchCurb_(l_pbm, l_lossFunLine, &q, t);
    if (l_bMatchFlag)
    {
        ceres::Solver::Options l_opt;
        l_opt.linear_solver_type = ceres::DENSE_NORMAL_CHOLESKY;
        l_opt.num_threads = 1;
        l_opt.max_num_iterations = 30;

        l_opt.function_tolerance = 1e-8;  // 1e-6
        //信任域步长(trust region step)相对减少的最小值。
        l_opt.min_relative_decrease = 1e-6;            // 1e-3
        l_opt.min_line_search_step_contraction = 0.9;  // 1e-3
        l_opt.minimizer_progress_to_stdout = false;
        l_opt.logging_type = ceres::SILENT;

        // TicToc t_sol;
        ceres::Solver::Summary l_summary;
        //基于构建的所有残差项，求解最优的当前帧位姿与上一帧位姿的位姿增量：para_q和l_daParaT
        ceres::Solve(l_opt, &l_pbm, &l_summary);
        if (l_summary.IsSolutionUsable())
        {
            c_tEst_.x() = t[0];
            c_tEst_.y() = t[1];
            c_tEst_.z() = t[2];
            Eigen::Vector3d trans{t[0], t[1], t[2]};
            p_tCorr1 = trans;
            Eigen::Vector3d angleAxisX{double(1), double(0), double(0)};
            Eigen::Vector3d angleAxisRot;
            angleAxisRot = c_normal_.cross(angleAxisX);
            angleAxisRot.normalize();
            double rotationAngle =
                acos(c_normal_.dot(angleAxisX) / c_normal_.norm() / angleAxisX.norm());
            if (rotationAngle < double(0.0))
                rotationAngle = rotationAngle + double(M_PI);

            Eigen::Vector3d ceresAngleAxis = angleAxisRot * double(rotationAngle);
            double t_rot[3]{double(0), double(0), double(0)};

            ceres::AngleAxisRotatePoint(ceresAngleAxis.data(), trans.data(), t_rot);
            Eigen::Matrix<double, 3, 1> angleAxisRotInv = angleAxisX.cross(c_normal_);
            angleAxisRotInv.normalize();
            trans << double(0.0), t_rot[1], t_rot[2];
            Eigen::Matrix<double, 3, 1> ceresAngleAxisInv = angleAxisRotInv * double(rotationAngle);
            ceres::AngleAxisRotatePoint(ceresAngleAxisInv.data(), trans.data(), t_rot);
            trans << t_rot[0], t_rot[1], t_rot[2];
            c_qEst_.normalize();

            Eigen::Matrix3d R;
            R = Eigen::AngleAxisd(q, ::Eigen::Vector3d::UnitZ())
                * Eigen::AngleAxisd(0, ::Eigen::Vector3d::UnitY())
                * Eigen::AngleAxisd(0, ::Eigen::Vector3d::UnitX());
            p_qCorr = R;
            p_tCorr = trans;
            return true;
        }
    }
    return false;
}