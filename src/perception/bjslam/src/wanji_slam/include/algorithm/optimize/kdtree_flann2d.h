/*
 * @Author: your name
 * @Date: 2021-05-31 09:23:17
 * @LastEditTime: 2022-03-22 18:20:37
 * @LastEditors: <PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_slam/wslam/include/wslam/kdtree_flann2d.h
 */
#ifndef PCL_KDTREE_KDTREE_FLANN_2D_H_
#define PCL_KDTREE_KDTREE_FLANN_2D_H_

#include <boost/shared_array.hpp>
#include <cstdio>
#include <flann/flann.hpp>
#include <pcl/console/print.h>
#include <pcl/kdtree/kdtree.h>

// Forward declarations
namespace flann {
struct SearchParams;
template <typename T> struct L2_Simple;
template <typename T> class Index;
}  // namespace flann

namespace pcl {

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/** \brief @b PointRepresentation2D extends PointRepresentation to allow for sub-part selection on
 * the point.
 */
template <typename PointDefault>
class PointRepresentation2D : public PointRepresentation<PointDefault> {
    using PointRepresentation<PointDefault>::nr_dimensions_;
    using PointRepresentation<PointDefault>::trivial_;

  public:
    // Boost shared pointers
    typedef boost::shared_ptr<PointRepresentation2D<PointDefault>> Ptr;
    typedef boost::shared_ptr<const PointRepresentation2D<PointDefault>> ConstPtr;

    /** \brief Constructor
     * \param[in] max_dim the maximum number of dimensions to use
     * \param[in] start_dim the starting dimension
     */
    PointRepresentation2D()
    {
        nr_dimensions_ = 2;
        trivial_ = true;
    }

    inline Ptr makeShared() const
    {
        return Ptr(new PointRepresentation2D<PointDefault>(*this));
    }

    /** \brief Copy the point data into a float array
     * \param[in] p the input point
     * \param[out] out the resultant output array
     */
    virtual void copyToFloatArray(const PointDefault& p, float* out) const
    {
        out[0] = p.x;
        out[1] = p.y;
    }
};

// Forward declarations
template <typename T> class PointRepresentation2D;

/** \brief KdTreeFLANN2D is a generic type of 3D spatial locator using kD-tree structures. The class
 * is making use of the FLANN (Fast Library for Approximate Nearest Neighbor) project by Marius Muja
 * and David Lowe.
 *
 * \author Radu B. Rusu, Marius Muja
 * \ingroup kdtree
 */
template <typename PointT, typename Dist = ::flann::L2_Simple<float>>
class KdTreeFLANN2D : public pcl::KdTree<PointT> {
  public:
    using KdTree<PointT>::input_;
    using KdTree<PointT>::indices_;
    using KdTree<PointT>::epsilon_;
    using KdTree<PointT>::sorted_;
    //   using KdTree<PointT>::point_representation_;
    using KdTree<PointT>::nearestKSearch;
    using KdTree<PointT>::radiusSearch;

    typedef typename KdTree<PointT>::PointCloud PointCloud;
    typedef typename KdTree<PointT>::PointCloudConstPtr PointCloudConstPtr;

    typedef boost::shared_ptr<std::vector<int>> IndicesPtr;
    typedef boost::shared_ptr<const std::vector<int>> IndicesConstPtr;

    typedef ::flann::Index<Dist> FLANNIndex;

    // Boost shared pointers
    typedef boost::shared_ptr<KdTreeFLANN2D<PointT>> Ptr;
    typedef boost::shared_ptr<const KdTreeFLANN2D<PointT>> ConstPtr;

    /** \brief Default Constructor for KdTreeFLANN2D.
     * \param[in] sorted set to true if the application that the tree will be used for requires
     * sorted nearest neighbor indices (default). False otherwise.
     *
     * By setting sorted to false, the \ref radiusSearch operations will be faster.
     */
    KdTreeFLANN2D(bool sorted = true);

    /** \brief Copy constructor
     * \param[in] k the tree to copy into this
     */
    KdTreeFLANN2D(const KdTreeFLANN2D<PointT>& k);

    /** \brief Copy operator
     * \param[in] k the tree to copy into this
     */
    inline KdTreeFLANN2D<PointT>& operator=(const KdTreeFLANN2D<PointT>& k)
    {
        KdTree<PointT>::operator=(k);
        flann_index_ = k.flann_index_;
        cloud_ = k.cloud_;
        index_mapping_ = k.index_mapping_;
        identity_mapping_ = k.identity_mapping_;
        dim_ = k.dim_;
        total_nr_points_ = k.total_nr_points_;
        param_k_ = k.param_k_;
        param_radius_ = k.param_radius_;
        return (*this);
    }

    /** \brief Set the search epsilon precision (error bound) for nearest neighbors searches.
     * \param[in] eps precision (error bound) for nearest neighbors searches
     */
    void setEpsilon(float eps);

    void setSortedResults(bool sorted);

    inline Ptr makeShared()
    {
        return Ptr(new KdTreeFLANN2D<PointT>(*this));
    }

    /** \brief Destructor for KdTreeFLANN2D.
     * Deletes all allocated data arrays and destroys the kd-tree structures.
     */
    virtual ~KdTreeFLANN2D()
    {
        cleanup();
    }

    /** \brief Provide a pointer to the input dataset.
     * \param[in] cloud the const boost shared pointer to a PointCloud message
     * \param[in] indices the point indices subset that is to be used from \a cloud - if NULL the
     * whole cloud is used
     */
    void setInputCloud(const PointCloudConstPtr& cloud,
                       const IndicesConstPtr& indices = IndicesConstPtr());

    /** \brief Search for k-nearest neighbors for the given query point.
     *
     * \attention This method does not do any bounds checking for the input index
     * (i.e., index >= cloud.points.size () || index < 0), and assumes valid (i.e., finite) data.
     *
     * \param[in] point a given \a valid (i.e., finite) query point
     * \param[in] k the number of neighbors to search for
     * \param[out] k_indices the resultant indices of the neighboring points (must be resized to \a
     * k a priori!) \param[out] k_sqr_distances the resultant squared distances to the neighboring
     * points (must be resized to \a k a priori!) \return number of neighbors found
     *
     * \exception asserts in debug mode if the index is not between 0 and the maximum number of
     * points
     */
    int nearestKSearch(const PointT& point,
                       int k,
                       std::vector<int>& k_indices,
                       std::vector<float>& k_sqr_distances) const;

    /** \brief Search for all the nearest neighbors of the query point in a given radius.
     *
     * \attention This method does not do any bounds checking for the input index
     * (i.e., index >= cloud.points.size () || index < 0), and assumes valid (i.e., finite) data.
     *
     * \param[in] point a given \a valid (i.e., finite) query point
     * \param[in] radius the radius of the sphere bounding all of p_q's neighbors
     * \param[out] k_indices the resultant indices of the neighboring points
     * \param[out] k_sqr_distances the resultant squared distances to the neighboring points
     * \param[in] max_nn if given, bounds the maximum returned neighbors to this value. If \a max_nn
     * is set to 0 or to a number higher than the number of points in the input cloud, all neighbors
     * in \a radius will be returned. \return number of neighbors found in radius
     *
     * \exception asserts in debug mode if the index is not between 0 and the maximum number of
     * points
     */
    int radiusSearch(const PointT& point,
                     double radius,
                     std::vector<int>& k_indices,
                     std::vector<float>& k_sqr_distances,
                     unsigned int max_nn = 0) const;

  private:
    /** \brief Internal cleanup method. */
    void cleanup();

    /** \brief Converts a PointCloud to the internal FLANN point array representation. Returns the
     * number of points. \param cloud the PointCloud
     */
    void convertCloudToArray(const PointCloud& cloud);

    /** \brief Converts a PointCloud with a given set of indices to the internal FLANN point array
     * representation. Returns the number of points.
     * \param[in] cloud the PointCloud data
     * \param[in] indices the point cloud indices
     */
    void convertCloudToArray(const PointCloud& cloud, const std::vector<int>& indices);

  private:
    /** \brief Class getName method. */
    virtual std::string getName() const
    {
        return ("KdTreeFLANN2D");
    }

    /** \brief A FLANN index object. */
    boost::shared_ptr<FLANNIndex> flann_index_;

    /** \brief Internal pointer to data. */
    boost::shared_array<float> cloud_;

    /** \brief mapping between internal and external indices. */
    std::vector<int> index_mapping_;

    /** \brief whether the mapping bwwteen internal and external indices is identity */
    bool identity_mapping_;

    /** \brief Tree dimensionality (i.e. the number of dimensions per point). */
    int dim_;

    /** \brief The total size of the data (either equal to the number of points in the input cloud
     * or to the number of indices - if passed). */
    int total_nr_points_;

    /** \brief The KdTree search parameters for K-nearest neighbors. */
    ::flann::SearchParams param_k_;

    /** \brief The KdTree search parameters for radius search. */
    ::flann::SearchParams param_radius_;

    /** \brief For converting different point structures into k-dimensional vectors for
     * nearest-neighbor search. */
    boost::shared_ptr<const PointRepresentation2D<PointT>> point_representation_;
};

}  // namespace pcl
#include "impl/kdtree_flann2d.hpp"
#endif