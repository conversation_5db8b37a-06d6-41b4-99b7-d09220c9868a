/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushu<PERSON>
 * @Date: 2021-10-09 10:28:54
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-03-23 15:45:33
 */
#pragma once
#ifndef _CURB_REGISTRATION_H_
#    define _CURB_REGISTRATION_H_
#    include <Eigen/Dense>
#    include <ceres/ceres.h>
#    include <ceres/rotation.h>
#    include <pcl/point_cloud.h>
#    include <pcl/point_types.h>
#    include <pcl/search/kdtree.h>
// 匹配输出debug信息
// #define MATCHDEBUG
template <typename PS, typename PT> class CurbRegistration {
  private:
    typedef typename pcl::PointCloud<PS> PointCloudSource;
    typedef typename pcl::PointCloud<PT> PointCloudTarget;
    typedef boost::shared_ptr<PointCloudSource> PointCloudSourcePtr;
    typedef boost::shared_ptr<const PointCloudSource> PointCloudSourceConstPtr;
    typedef boost::shared_ptr<PointCloudTarget> PointCloudTargetPtr;
    typedef boost::shared_ptr<const PointCloudTarget> PointCloudTargetConstPtr;

  private:
    struct s_SmoothZDiff
    {
        float m_ZDiff;
        int m_idx;
        bool operator()(s_SmoothZDiff const& p_sLeft, s_SmoothZDiff const& p_sRight)
        {
            return p_sLeft.m_ZDiff < p_sRight.m_ZDiff;
        }
    };

  public:
    typedef boost::shared_ptr<CurbRegistration<PS, PT>> Ptr;

    CurbRegistration()
    {
        c_fSearchRadius_ = 2;
        c_kdTree_ = nullptr;
        c_pSrc_ = nullptr;
        c_pTar_ = nullptr;
        c_vInitialT_ = Eigen::Vector3d::Zero();
        c_qInitialQ_ = Eigen::Quaterniond::Identity();
        c_kdTree_.reset(new pcl::KdTreeFLANN<PT>());
    }

    ~CurbRegistration()
    {
        c_kdTree_ = nullptr;
        c_pSrc_ = nullptr;
        c_pTar_ = nullptr;
    }

    void setInputSource(const PointCloudSourceConstPtr& pcSrc);

    void setInputTarget(const PointCloudTargetConstPtr& pcTar);

    bool align(Eigen::Quaterniond& p_qCorr, Eigen::Vector3d& p_tCorr, Eigen::Vector3d& p_tCorr1);

    void setSearchRadius(float p_fSearchRadius)
    {
        c_fSearchRadius_ = p_fSearchRadius;
    }

    void setInitialPose(Eigen::Quaterniond& p_qCorr, Eigen::Vector3d& p_tCorr)
    {
        c_qInitialQ_ = p_qCorr;
        c_vInitialT_ = p_tCorr;
    }

  private:
    bool matchCurb_(ceres::Problem& p_pbm,
                    ceres::LossFunction* p_pLossFunc,
                    double* p_pdParaQ,
                    double* p_pdParaT);

    float distFromPointToLine_(Eigen::Vector3d p_pntCur,
                               Eigen::Vector3d& p_LineA,
                               Eigen::Vector3d& p_LineB)
    {
        return fabs(((p_pntCur - p_LineA).cross(p_pntCur - p_LineB)).norm()
                    / (p_LineA - p_LineB).norm());
    }

    Eigen::Vector3d getYawVector3d(Eigen::Quaterniond q)
    {
        double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
        double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
        double l_yaw = atan2(siny_cosp, cosy_cosp);
        Eigen::Vector3d eulerAngle = Eigen::Vector3d(cos(l_yaw), sin(l_yaw), 0);
        return eulerAngle;
    }

    double getAngleDiff(Eigen::Vector3d v1, Eigen::Vector3d v2)
    {
        v1[2] = 0;
        v2[2] = 0;
        return acos(v1.dot(v2) / (v1.norm() * v2.norm())) / M_PI * 180;
    }

    PointCloudSourceConstPtr c_pSrc_;
    PointCloudTargetPtr c_pTar_;
    typename pcl::KdTreeFLANN<PT>::Ptr c_kdTree_;
    Eigen::Quaterniond c_qEst_;
    Eigen::Vector3d c_tEst_;
    Eigen::Vector3d c_normal_;
    float c_fSearchRadius_;
    Eigen::Quaterniond c_qInitialQ_;
    Eigen::Vector3d c_vInitialT_;
};
#endif