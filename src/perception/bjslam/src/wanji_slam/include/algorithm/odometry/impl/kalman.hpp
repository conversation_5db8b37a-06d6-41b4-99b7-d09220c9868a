#pragma once
#include <Eigen/Dense>
#include <iostream>
#include <stdexcept>

class KalmanFilter {
  public:
    /**
     * Create a Kalman filter with the specified matrices.
     *   A - System dynamics matrix
     *   C - Output matrix
     *   Q - Process noise covariance
     *   R - Measurement noise covariance
     *   P - Estimate error covariance
     */
    KalmanFilter(const Eigen::MatrixXd& A,
                 const Eigen::MatrixXd& C,
                 const Eigen::MatrixXd& Q,
                 const Eigen::MatrixXd& R,
                 const Eigen::MatrixXd& P);

    /**
     * Create a blank estimator.
     */
    KalmanFilter();

    /**
     * Initialize the filter with initial states as zero.
     */
    virtual void init();

    /**
     * Initialize the filter with a guess for initial states.
     */
    virtual void init(const Eigen::VectorXd& x0);

    /**
     * Update the estimated state based on measured values. The
     * time step is assumed to remain constant.
     */
    virtual void update(const Eigen::VectorXd& y);

    /**
     * Update the estimated state based on measured values,
     * using the given time step and dynamics matrix.
     */
    virtual void update(const Eigen::VectorXd& y, const Eigen::MatrixXd A);

    /**
     * Return the current state and time.
     */
    Eigen::VectorXd state()
    {
        return x_hat;
    };

  protected:
    // Matrices for computation
    Eigen::MatrixXd A, C, Q, R, P, K, P0;

    // System dimensions
    int m, n;

    // Is the filter initialized?
    bool initialized;

    // n-size identity
    Eigen::MatrixXd I;

    // Estimated states
    Eigen::VectorXd x_hat, x_hat_new;
};

KalmanFilter::KalmanFilter(const Eigen::MatrixXd& A,
                           const Eigen::MatrixXd& C,
                           const Eigen::MatrixXd& Q,
                           const Eigen::MatrixXd& R,
                           const Eigen::MatrixXd& P)
    : A(A), C(C), Q(Q), R(R), P0(P), m(C.rows()), n(A.rows()), initialized(false), I(n, n),
      x_hat(n), x_hat_new(n)
{
    I.setIdentity();
}

KalmanFilter::KalmanFilter() {}

void KalmanFilter::init(const Eigen::VectorXd& x0)
{
    x_hat = x0;
    P = P0;
    initialized = true;
}

void KalmanFilter::init()
{
    x_hat.setZero();
    P = P0;
    initialized = true;
}

void KalmanFilter::update(const Eigen::VectorXd& y)
{
    if (!initialized)
        init();

    x_hat_new = A * x_hat;
    P = A * P * A.transpose() + Q;
    K = P * C.transpose() * (C * P * C.transpose() + R).inverse();
    x_hat_new += K * (y - C * x_hat_new);
    P = (I - K * C) * P;
    x_hat = x_hat_new;
}

void KalmanFilter::update(const Eigen::VectorXd& y, const Eigen::MatrixXd A)
{
    this->A = A;
    update(y);
}
