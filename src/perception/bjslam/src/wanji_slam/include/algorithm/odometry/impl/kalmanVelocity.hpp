#pragma once
#include "kalman.hpp"

/**
 * @brief 6D 恒等模型卡尔曼滤波器
 *
 */
class KalmanFilter3DV : public KalmanFilter {
  public:
    KalmanFilter3DV(double p_fProcessPN,
                    double p_fProcessRN,
                    double p_fMeasurePN,
                    double p_fMeasureRN)
        : KalmanFilter()
    {
        typedef Eigen::Matrix<double, 3, 3> Matrix3d;
        A = Matrix3d::Identity();  // System dynamics matrix
        C = Matrix3d::Identity();  // Output matrix

        Q = Matrix3d::Zero();              // Process noise covariance
        Q(0, 0) = Q(1, 1) = p_fProcessPN;  // m/100ms
        Q(2, 2) = p_fProcessRN;            // deg/100ms

        R = Matrix3d::Zero();              // Measurement noise covariance
        R(0, 0) = R(1, 1) = p_fMeasurePN;  // m/100ms
        R(2, 2) = p_fMeasureRN;            // deg/100ms

        P0 = Matrix3d::Zero();       // Estimate error covariance
        P0(0, 0) = P0(1, 1) = 0.01;  // m/100ms
        P0(2, 2) = 0.01;             // deg/100ms

        m = C.rows();
        n = A.rows();
        initialized = false;
        I = Matrix3d::Identity();
        x_hat = Eigen::VectorXd::Zero(n);
        x_hat_new = x_hat;
        init();
    }
};

/**
 * @brief 6D 恒等模型卡尔曼滤波器
 *
 */
class KalmanFilter6DV : public KalmanFilter {
  public:
    KalmanFilter6DV(double p_fProcessPN,
                    double p_fProcessRN,
                    double p_fMeasurePN,
                    double p_fMeasureRN)
        : KalmanFilter()
    {
        typedef Eigen::Matrix<double, 6, 6> Matrix6d;
        A = Matrix6d::Identity();  // System dynamics matrix
        C = Matrix6d::Identity();  // Output matrix

        Q = Matrix6d::Zero();                        // Process noise covariance
        Q(0, 0) = Q(1, 1) = Q(2, 2) = p_fProcessPN;  // m/100ms
        Q(3, 3) = Q(4, 4) = Q(5, 5) = p_fProcessRN;  // deg/100ms

        R = Matrix6d::Zero();                        // Measurement noise covariance
        R(0, 0) = R(1, 1) = R(2, 2) = p_fMeasurePN;  // m/100ms
        R(3, 3) = R(4, 4) = R(5, 5) = p_fMeasureRN;  // deg/100ms

        P0 = Matrix6d::Zero();                  // Estimate error covariance
        P0(0, 0) = P0(1, 1) = P0(2, 2) = 0.01;  // m/100ms
        P0(3, 3) = P0(4, 4) = P0(5, 5) = 0.01;  // deg/100ms

        m = C.rows();
        n = A.rows();
        initialized = false;
        I = Matrix6d::Identity();
        x_hat = Eigen::VectorXd::Zero(n);
        x_hat_new = x_hat;
        init();
    }
};