/**
 * @file ParticleFileter.hpp
 * <AUTHOR>
 * @brief 基本的粒子滤波功能
 * @version 1.0
 * @date 2023-08-30
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */
#pragma once
#include "common/common_ex.h"
#include <random>

using namespace wj_slam;
namespace amcl_loop {
struct Particle
{
    double x; /**< pose x*/
    double y; /**< pose y*/
    double z; /**< pose z*/
    double a; /**< pose yaw*/

    double w;  /**<Total weight*/
    double wp; /**< weight 1*/
    double wn; /**< weight 2*/

    Particle() : x(0), y(0), z(0), a(0), w(0), wp(0), wn(0) {}

    Particle(double p_x,
             double p_y,
             double p_z,
             double p_a,
             double p_w,
             double p_wp = 0,
             double p_wn = 0)
        : x(p_x), y(p_y), z(p_z), a(p_a), w(p_w), wp(p_wp), wn(p_wn)
    {
    }
};

inline double inPI(const double p_dYaw)
{
    //转化为-180 - 180 度
    double in2pi;
    in2pi = fmod(p_dYaw, 360.0);
    if (in2pi >= 0)
    {
        return in2pi < 180 ? in2pi : (in2pi - 360);
    }
    else
    {
        return fabs(in2pi) < 180 ? in2pi : (in2pi + 360);
    }
}

class ParticleFilter {
  public:
    ParticleFilter();
    ~ParticleFilter();
    /**
     * @brief 设置初始范围
     * @param x_min 搜索区域X方向范围左值
     * @param x_max 搜索区域X方向范围右值
     * @param y_min 搜索区域Y方向范围左值
     * @param y_max 搜索区域Y方向范围右值
     * @param z_min 搜索区域Z方向范围左值
     * @param z_max 搜索区域Z方向范围右值
     * @param a_min 搜索区域YAW角(角度)范围左值
     * @param a_max 搜索区域YAW角(角度)范围右值
     *
     */
    void setInitRegion(const float x_min,
                       const float x_max,
                       const float y_min,
                       const float y_max,
                       const float z_min,
                       const float z_max,
                       const float a_min,
                       const float a_max);
    /**
     * @brief 根据实际需求设置粒子数量
     * @param p_pNum
     *
     */
    void setParticleNum(const int p_pNum);

    /**
     * @brief 更新归一化权重,求此次滤波的平均与最佳pose
     *
     */
    void update();

    /**
     * @brief 对于上次的update计算的平均位置预估位置,重新撒粒子
     *
     */
    void renewPose(const bool p_bSampleMod = false);

    /**
     * @brief 获取位姿粒子容器
     * @code
     *
     * @endcode
     * @return [std::vector<Particle>&] \n
     * [当前位姿粒子容器]
     *
     */
    std::vector<Particle>& getParticles()
    {
        return c_vP_;
    }

    /**
     * @brief 获取粒子位姿的均值
     * @code
     *
     * @endcode
     * @return [Particle] \n
     * [details wirte here]
     *
     */
    Particle getOptimalRedult()
    {
        return c_stMeanPose_;
    }
    /**
     * @brief 获取权重最高位姿粒子
     * @code
     *
     * @endcode
     * @return [Particle] \n
     * [details wirte here]
     *
     */
    Particle getBestRedult()
    {
        return c_stBestPose_;
    }
    /**
     * @brief 基于概率重采样函数
     *
     */
    void resample();

  private:
    /**
     * @brief 高斯分布
     * @param p_dMean
     * @param p_dDigma
     * @code
     *
     * @endcode
     * @return [float] \n
     * [基于高斯分布的均值]
     *
     */
    float ranGaussian_(const double p_dMean, const double p_dDigma);
    /**
     * @brief 均匀分布
     * @param p_fRangeFrom 左边界(包含)
     * @param p_fRangeTo 有边界(不包含)
     * @code
     *
     * @endcode
     * @return [float] \n
     * [范围内随机数]
     *
     */
    float rngUniform_(const float p_fRangeFrom, const float p_fRangeTo);

    /**
     * @brief 基于位置重设范围
     *
     */
    void renewFiled_(Particle p_Pose);

  private:
    std::vector<Particle> c_vP_; /**< 粒子集合*/
    Particle c_stMeanPose_;      /**< 粒子当前预估位置*/
    Particle c_stBestPose_;      /**< 粒子权重最高位置*/
    float c_fDev_x_max_;         /**< 粒子x方向最大范围*/
    float c_fDev_x_min_;         /**< 粒子x方向最小范围*/
    float c_fDev_y_max_;         /**< 粒子y方向最大范围*/
    float c_fDev_y_min_;         /**< 粒子y方向最小范围*/
    float c_fDev_z_max_;         /**< 粒子z方向最大范围*/
    float c_fDev_z_min_;         /**< 粒子z方向最小范围*/
    float c_fDev_a_max_;         /**< 粒子角度最大范围*/
    float c_fDev_a_min_;         /**< 粒子角度最小范围*/

    int c_iParticleNum_; /**< 粒子个数*/
    bool c_bIsChanged_;  /**< 修改粒子散布范围标志*/

    /*随机数发生器*/
    std::random_device rd_;    /**< 随机数*/
    std::mt19937 c_generator_; /*!< 随机数发生器*/
};

ParticleFilter::ParticleFilter()
    : c_stMeanPose_(Particle()), c_stBestPose_(Particle()), c_fDev_x_max_(0), c_fDev_x_min_(0),
      c_fDev_y_max_(0), c_fDev_y_min_(0), c_fDev_z_max_(0), c_fDev_z_min_(0), c_fDev_a_max_(180),
      c_fDev_a_min_(-180), c_iParticleNum_(500), c_bIsChanged_(false), c_generator_(rd_())
{
}

ParticleFilter::~ParticleFilter() {}

void ParticleFilter::setParticleNum(const int p_pNum)
{
    c_iParticleNum_ = p_pNum;
}
void ParticleFilter::setInitRegion(const float x_min,
                                   const float x_max,
                                   const float y_min,
                                   const float y_max,
                                   const float z_min,
                                   const float z_max,
                                   const float a_min,
                                   const float a_max)
{
    //根据输入限定粒子范围
    c_fDev_x_max_ = x_max;
    c_fDev_x_min_ = x_min;
    c_fDev_y_max_ = y_max;
    c_fDev_y_min_ = y_min;
    c_fDev_z_max_ = z_max;
    c_fDev_z_min_ = z_min;
    //转化为-180到180度
    c_fDev_a_max_ = inPI(a_max) > inPI(a_min) ? inPI(a_max) : inPI(a_min);
    c_fDev_a_min_ = inPI(a_max) < inPI(a_min) ? inPI(a_max) : inPI(a_min);

    c_vP_.resize(c_iParticleNum_);
    for (size_t i = 0; i < c_iParticleNum_; ++i)
    {
        c_vP_[i].x = rngUniform_(c_fDev_x_min_, c_fDev_x_max_);
        c_vP_[i].y = rngUniform_(c_fDev_y_min_, c_fDev_y_max_);
        c_vP_[i].z = rngUniform_(c_fDev_z_min_, c_fDev_z_max_);
        c_vP_[i].a = rngUniform_(c_fDev_a_min_, c_fDev_a_max_);
        c_vP_[i].w = 0;
        c_vP_[i].wn = 0;
        c_vP_[i].wp = 0;
    }
    c_bIsChanged_ = false;
}

void ParticleFilter::update()
{
    float l_fWpSum = 0;
    float l_fWnSum = 0;
    float l_fWeightSum = 0;
    size_t l_Number = 0;
    for (size_t i = 0; i < c_vP_.size(); ++i)
    {
        //计算权重和,用于加权归一化
        if (c_vP_[i].w > 0)
        {
            l_fWpSum += c_vP_[i].wp;
            l_fWnSum += c_vP_[i].wn;
            l_fWeightSum += c_vP_[i].w;
            //记录不为0的权重数
            ++l_Number;
        }
    }
    Particle l_stMean_p;
    Particle l_stMax_p;

    if (l_fWeightSum > 0)
    {
        for (size_t i = 0; i < c_vP_.size(); ++i)
        {
            //求本次滤波权重最高处
            if (c_vP_[i].w > l_stMax_p.w)
                l_stMax_p = c_vP_[i];
            //只对总权重做归一化处理
            c_vP_[i].w /= l_fWeightSum;
            //根据归一化权重求平均位姿
            l_stMean_p.x += c_vP_[i].w * c_vP_[i].x;
            l_stMean_p.y += c_vP_[i].w * c_vP_[i].y;
            l_stMean_p.z += c_vP_[i].w * c_vP_[i].z;
            l_stMean_p.a += c_vP_[i].w * c_vP_[i].a;
        }
    }
    else
    {
        return;
    }
    c_stBestPose_ = l_stMax_p;
    c_stMeanPose_ = l_stMean_p;
    //平均权重
    c_stMeanPose_.wn = l_fWnSum / l_Number;
    c_stMeanPose_.wp = l_fWpSum / l_Number;
    c_stMeanPose_.w = l_fWeightSum / l_Number;
    //平均权重和最大权重已更新标志
    c_bIsChanged_ = true;
}
void ParticleFilter::renewPose(const bool p_bSampleMod)
{
    if (c_bIsChanged_)
    {
        if (p_bSampleMod)
        {
            //基于权重最高位姿处重设范围
            renewFiled_(c_stBestPose_);
            for (size_t i = 0; i < c_vP_.size(); ++i)
            {
                c_vP_[i].x = c_stBestPose_.x + ranGaussian_(0, (c_fDev_x_max_ - c_fDev_x_min_) / 6);
                c_vP_[i].y = c_stBestPose_.y + ranGaussian_(0, (c_fDev_y_max_ - c_fDev_y_min_) / 6);
                c_vP_[i].z = c_stBestPose_.z + ranGaussian_(0, (c_fDev_z_max_ - c_fDev_z_min_) / 6);
                c_vP_[i].a = c_stBestPose_.a + ranGaussian_(0, (c_fDev_a_max_ - c_fDev_a_min_) / 6);
                c_vP_[i].w = 0;
                c_vP_[i].wn = 0;
                c_vP_[i].wp = 0;
            }
        }
        else
        {
            //基于平均位姿处重设范围
            renewFiled_(c_stMeanPose_);
            for (size_t i = 0; i < c_iParticleNum_; ++i)
            {
                //重采样
                c_vP_[i].x = rngUniform_(c_fDev_x_min_, c_fDev_x_max_);
                c_vP_[i].y = rngUniform_(c_fDev_y_min_, c_fDev_y_max_);
                c_vP_[i].z = rngUniform_(c_fDev_z_min_, c_fDev_z_max_);
                c_vP_[i].a = rngUniform_(c_fDev_a_min_, c_fDev_a_max_);
                c_vP_[i].w = 0;
                c_vP_[i].wn = 0;
                c_vP_[i].wp = 0;
            }
        }
        c_bIsChanged_ = false;
    }
}

void ParticleFilter::resample()
{
    std::vector<Particle> new_p(c_vP_.size());
    const float factor = 1.f / c_vP_.size();
    const float r = factor * rngUniform_(0, 1);
    float c = c_vP_[0].w;
    float u;

    //根据归一化概率分布,尽可能保留权重较大的粒子,同时保留小概率粒子
    for (uint32_t m = 0, i = 0; m < c_vP_.size(); ++m)
    {
        u = r + factor * m;
        while (u > c)
        {
            if (++i >= c_vP_.size())
                break;
            c += c_vP_[i].w;
        }
        new_p[m] = c_vP_[i];
        // todo:重采样时,改变粒子分布
        new_p[m].w = factor;
    }

    c_vP_ = new_p;
}

float ParticleFilter::ranGaussian_(const double p_dMean, const double p_dDigma)
{
    std::normal_distribution<float> l_distribution(p_dMean, p_dDigma);
    return l_distribution(c_generator_);
}

float ParticleFilter::rngUniform_(const float p_fRangeFrom, const float p_fRangeTo)
{
    std::uniform_real_distribution<float> l_distribution(p_fRangeFrom, p_fRangeTo);
    return l_distribution(c_generator_);
}

void ParticleFilter::renewFiled_(Particle p_Pose)
{
    float l_fDelta_x = std::min(fabs(c_fDev_x_max_ - p_Pose.x), fabs(p_Pose.x - c_fDev_x_min_));
    float l_fDelta_y = std::min(fabs(c_fDev_y_max_ - p_Pose.y), fabs(p_Pose.y - c_fDev_y_min_));
    float l_fDelta_z = std::min(fabs(c_fDev_z_max_ - p_Pose.z), fabs(p_Pose.z - c_fDev_z_min_));
    float l_fDelta_a = std::min(fabs(c_fDev_a_max_ - p_Pose.a), fabs(p_Pose.a - c_fDev_a_min_));
    //重新计算粒子采样范围

    c_fDev_x_max_ = p_Pose.x + l_fDelta_x;
    c_fDev_x_min_ = p_Pose.x - l_fDelta_x;

    c_fDev_y_max_ = p_Pose.y + l_fDelta_y;
    c_fDev_y_min_ = p_Pose.y - l_fDelta_y;

    c_fDev_z_max_ = p_Pose.z + l_fDelta_z;
    c_fDev_z_min_ = p_Pose.z - l_fDelta_z;

    c_fDev_a_max_ = p_Pose.a + l_fDelta_a;
    c_fDev_a_min_ = p_Pose.a - l_fDelta_a;
}

}  // namespace amcl_loop