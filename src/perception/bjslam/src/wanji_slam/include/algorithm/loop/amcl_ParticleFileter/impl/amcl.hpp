/**
 * @file amcl.hpp
 * <AUTHOR> Li
 * @brief 局部AMCL方法,用于具有SC预估的回环检测
 * @version 1.0
 * @date 2023-09-08
 * @copyright Copyright (c)2023 Vanjee
 */
#pragma once
#include "./ParticleFileter.hpp"
#include "algorithm/map/iVox_map/iVox_map.h"
#include "algorithm/verify/laserVerifyScore.h"
#include "common/type/type_pose.h"
using namespace wj_slam;

namespace amcl_loop {

template <typename PT> class AMCL {
  private:
    using PC = pcl::PointCloud<PT>;
    using PC_PTR = typename PC::Ptr;
    using IVOX_MAP = boost::shared_ptr<IVox<3, PT>>;
    using FeaturePair = FEATURE_PAIR<PT>;
    using FeaturePairPtr = typename FeaturePair::Ptr;
    SYSPARAM* c_stSysParam_;
    PC_PTR c_pSrcSurf_; /**< 输入待匹配面点*/
    PC_PTR c_pSrcCorn_; /**< 输入待匹配角点*/

    IVOX_MAP c_pGridMap_;                                   /**< 输入栅格地图*/
    boost::shared_ptr<LaserVerifyScore<PT, PT>> c_pOccupy_; /**< 计算得分的*/
    float c_MapScore_;                                       /**< 输入进来的地图携带得分*/
    float c_MapNumber_;        /**< 输入进来的地图携带匹配比例*/
    LaserTransform<PT> c_ts_; /**< 转移点云功能*/
    boost::shared_ptr<ParticleFilter> c_PartcleFilter_; /**< 粒子滤波器*/
    Particle c_meanp_;                                 /**< 平均位置*/
    Particle c_maxp_;                                  /**< 平均位置*/
    int c_iFilteTimes_;                                 /**< 循环次数*/
    float c_fDelta_x_;                                 /**< x方向范围*/
    float c_fDelta_y_;                                 /**< y方向范围*/
    float c_fDelta_z_;                                 /**< z方向范围*/
    float c_fDelta_a_;                                 /**< 角度变化范围(度)*/

  public:
    AMCL();
    /**
     * @brief 设置粒子数目
     * @param p_iParticleNums
     *
     */
    void setParticleNum(const int p_iParticleNums)
    {
        c_PartcleFilter_->setParticleNum(p_iParticleNums);
    }
    /**
     * @brief 设置粒子分布变化范围
     * @param p_fDeltaX
     * @param p_fDeltaY
     * @param p_fDeltaZ
     * @param p_fDeltaA
     *
     */
    inline void setFilterFile(const float p_fDeltaX,
                              const float p_fDeltaY,
                              const float p_fDeltaZ,
                              float p_fDeltaA)
    {
        c_fDelta_x_ = p_fDeltaX;
        c_fDelta_y_ = p_fDeltaY;
        c_fDelta_z_ = p_fDeltaZ;
        c_fDelta_a_ = p_fDeltaA;
    }
    /**
     * @brief 设置循环滤波次数
     *
     */
    inline void setFilterTimes(const int p_iFiltTimes)
    {
        c_iFilteTimes_ = p_iFiltTimes;
    }
    /**
     * @brief 设置当前点云
     * @param p_pSrcCorn_ 角点点云
     * @param p_pSrcSurf_ 面点点云
     *
     */
    void setInputSourceCloud(const PC_PTR p_pSrcCorn_, const PC_PTR p_pSrcSurf_);
    /**
     * @brief 设置当前地图以及回环地图帧
     * @param p_Map 已构建栅格地图
     * @param p_Mapfeature 当前地图帧点云特征帧
     *
     */
    void setTargetGridMap(const IVOX_MAP p_Map, const FeaturePairPtr p_Mapfeature);

    /**
     * @brief 输入初始预估位置
     * @param p_pose
     *
     */
    void setInitialPose(s_POSE6D p_pose);

    /**
     * @brief 获取最佳结果
     * @param p_pose 待填充位姿
     *
     */
    void getOptimalPose(s_POSE6D& p_pose);

    /**
     * @brief 执行粒子滤波
     *
     */
    void filter();

  private:
    /**
     * @brief 外部更新权重函数
     * @param p
     * @todo 若有好的权重更新方式,预期结果会更加准确
     */
    void updateWeights_(std::vector<Particle>& p);
    /**
     * @brief 提前退出判断
     * @param p_pCheckPose 当前输入粒子
     * @code
     *
     * @endcode
     * @return [true] \n
     * [满足阈值,认为匹配较好]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [不满足阈值]
     *
     */
    inline bool occupyVerifyResult_(Particle p_pCheckPose)
    {
        if (p_pCheckPose.wn > 0.75 * c_MapNumber_ && p_pCheckPose.wp > 0.8 * c_MapScore_)
            return true;
        else
            return false;
    }
};

template <typename PT>
AMCL<PT>::AMCL()
    : c_stSysParam_(SYSPARAM::getIn()), c_iFilteTimes_(20), c_fDelta_x_(1), c_fDelta_y_(1),
      c_fDelta_z_(0.03), c_fDelta_a_(15)
{
    c_pOccupy_.reset(new GridMapScore<PT, PT>(*c_stSysParam_));
    c_PartcleFilter_.reset(new ParticleFilter());
    // vevify
    c_pOccupy_->setGuassSize(c_stSysParam_->m_posCheck.m_fVerifyGauss);
    c_pOccupy_->setGroundAndRoofHigh(c_stSysParam_->m_map.m_fGroundHigh,
                                     c_stSysParam_->m_map.m_fRoofHigh);
}

template <typename PT>
void AMCL<PT>::setInputSourceCloud(const PC_PTR p_pSrcCorn_, const PC_PTR p_pSrcSurf_)
{
    if (p_pSrcCorn_)
        c_pSrcCorn_ = p_pSrcCorn_;
    if (p_pSrcSurf_)
        c_pSrcSurf_ = p_pSrcSurf_;
}

template <typename PT>
void AMCL<PT>::setTargetGridMap(const IVOX_MAP p_Map, const FeaturePairPtr p_Mapfeature)
{
    //
    if (p_Map)
    {
        c_pGridMap_.reset(new IVox<3, PT>());
        c_pGridMap_ = p_Map;
        //此方法获取的得分和匹配比例数量不能直接评价好坏,与环境本身有关
        //故算地图匹配得分,作为对比基准
        c_pOccupy_->setInputSourceCloud(p_Mapfeature->second, p_Mapfeature->first);
        c_pOccupy_->setTargetGridMap(c_pGridMap_);
        c_pOccupy_->calcuLaserVerifyScore();
        c_MapScore_ = c_pOccupy_->getLaserVerifyScore();
        c_MapNumber_ = c_pOccupy_->getLaserVerifyMatNum();
        LOGM(WDEBUG, "{} [loop amcl] 校验地图得分:{},校验地图比例:{}", WJLog::getWholeSysTime(),c_MapScore_, c_MapNumber_);
    }
}
template <typename PT> void AMCL<PT>::setInitialPose(s_POSE6D p_pose)
{
    c_meanp_.x = p_pose.x();
    c_meanp_.y = p_pose.y();
    c_meanp_.z = p_pose.z();
    c_meanp_.a = p_pose.yaw();
    //初始化最优位姿
    c_maxp_ = c_meanp_;
}

template <typename PT> void AMCL<PT>::getOptimalPose(s_POSE6D& p_pose)
{
    p_pose.setXYZ(c_maxp_.x, c_maxp_.y, c_maxp_.z);
    p_pose.setRPY(0, 0, c_maxp_.a);
}

template <typename PT> void AMCL<PT>::filter()
{
    c_PartcleFilter_->setInitRegion(c_meanp_.x + c_fDelta_x_,
                                   c_meanp_.x - c_fDelta_x_,
                                   c_meanp_.y + c_fDelta_y_,
                                   c_meanp_.y - c_fDelta_y_,
                                   c_meanp_.z + c_fDelta_z_,
                                   c_meanp_.z - c_fDelta_z_,
                                   c_meanp_.a + c_fDelta_a_,
                                   c_meanp_.a - c_fDelta_a_);

    std::vector<amcl_loop::Particle>& p = c_PartcleFilter_->getParticles();

    //采用 求权重-基于均匀分布重采样 -基于平均位姿重采样 循环的方法
    //防止进入局部最优,最后重新采样一次,并重复过程
    bool l_bRenewFlag = false;
    for (size_t i = 0; i <= c_iFilteTimes_; ++i)
    {
        // 粒子权重更新
        updateWeights_(p);

        c_PartcleFilter_->update();
        //获取当前采样位姿权重最大位姿
        Particle l_bestP = c_PartcleFilter_->getBestRedult();
        if (l_bestP.w > c_maxp_.w)
        {
            c_maxp_ = l_bestP;
        }
        // 达标提前退出
        if (occupyVerifyResult_(l_bestP))
        {
            //单次采样最接近者优先
            c_maxp_ = l_bestP;
            LOGM(WDEBUG, "{} [loop amcl] 提前退出!",WJLog::getWholeSysTime());
            break;
        }

        if ((c_iFilteTimes_ - 2) == i)
        {
            LOGM(WTRACE, "{} [loop amcl] 防止局部最优重新采样",WJLog::getWholeSysTime());
            c_PartcleFilter_->setInitRegion(c_meanp_.x + c_fDelta_x_,
                                           c_meanp_.x - c_fDelta_x_,
                                           c_meanp_.y + c_fDelta_y_,
                                           c_meanp_.y - c_fDelta_y_,
                                           c_meanp_.z + c_fDelta_z_,
                                           c_meanp_.z - c_fDelta_z_,
                                           c_meanp_.a + c_fDelta_a_,
                                           c_meanp_.a - c_fDelta_a_);
            l_bRenewFlag = false;
        }
        else if (l_bRenewFlag)
        {
            LOGM(WTRACE, "{} [loop amcl] 基于平均位置采样",WJLog::getWholeSysTime());
            c_PartcleFilter_->renewPose();
            l_bRenewFlag = false;
        }
        else
        {
            // printf("从平均位置开始重新采样!\n");
            // c_PartcleFilter_->renewPose();
            LOGM(WTRACE, "{} [loop amcl] 基于概率分布重新采样",WJLog::getWholeSysTime());
            c_PartcleFilter_->resample();
            l_bRenewFlag = true;
        }
    }

    //最后再获取一次最佳得分位置
    c_PartcleFilter_->renewPose();
    updateWeights_(p);
    c_PartcleFilter_->update();
    Particle l_bestP = c_PartcleFilter_->getBestRedult();
    if (l_bestP.w > c_maxp_.w)
    {
        c_maxp_ = l_bestP;
    }
    p.clear();
}
template <typename PT> void AMCL<PT>::updateWeights_(std::vector<Particle>& p_cP)
{
    s_POSE6D l_tp;
    PC_PTR l_tsS(new PC);
    PC_PTR l_tsC(new PC);
    for (auto& pi : p_cP)
    {
        l_tp.setXYZ(pi.x, pi.y, pi.z);
        l_tp.setRPY(0, 0, inPI(pi.a));
        //根据粒子随机位置,求取得分和比例
        c_ts_.transformCloudPoints(l_tp.m_quat, l_tp.m_trans, c_pSrcCorn_, l_tsC);
        c_ts_.transformCloudPoints(l_tp.m_quat, l_tp.m_trans, c_pSrcSurf_, l_tsS);
        c_pOccupy_->setInputSourceCloud(l_tsS, l_tsC);
        c_pOccupy_->calcuLaserVerifyScore();
        pi.wp = c_pOccupy_->getLaserVerifyScore();
        pi.wn = c_pOccupy_->getLaserVerifyMatNum();
        pi.w = pi.wp / c_MapScore_ + pi.wn / c_MapNumber_;
    }
}
};  // namespace amcl_loop
