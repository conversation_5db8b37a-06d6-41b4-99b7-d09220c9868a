/*
 * @Author: your name
 * @Date: 2021-05-25 10:51:18
 * @LastEditTime: 2022-03-11 14:25:49
 * @LastEditors: <PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_slam/wslam/include/wslam/KfSave.h
 */

#ifndef KF_SAVE
#define KF_SAVE
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <vector>
#include <zlib.h>
using namespace std;
/**
 * @brief 每帧KF各类型点云数
 *
 */
struct st_KfPointNum
{
    int m_iAllNum;
    int m_iFiNum;
    int m_iSeNum;
    int m_iCurbNum;
    st_KfPointNum() : m_iAllNum(0), m_iFiNum(0), m_iSeNum(0), m_iCurbNum(0){};
    void set(int p_allNum, int p_fiNum, int p_seNum, int p_curbNum)
    {
        m_iAllNum = p_allNum;
        m_iFiNum = p_fiNum;
        m_iSeNum = p_seNum;
        m_iCurbNum = p_curbNum;
    }
};
/**
 * @brief 将关键帧KF保存成加密文件，并且解密
 * @tparam <PointT> 点云类型
 * @tparam <PointPos> 关键帧pose类型
 *
 */
template <typename PointT, typename PointPos> class KfSave {
  public:
    struct s_FeatureInfo
    {
        s_FeatureInfo()
        {
            m_iPntNum = 0;       //点数目
            m_iFieldNum = 0;     //点的字段数,次参数没有意义
            m_iByteNum = 0;      //点的字节数
            m_iFeatureType = 0;  //存储类型
        }
        int m_iPntNum;
        int m_iFieldNum;
        int m_iByteNum;
        int m_iFeatureType;
    };

    struct s_Header
    {
        int m_iTypeNum;
        std::vector<s_FeatureInfo> m_vFeatureInfo;
    };
    typedef boost::shared_ptr<KfSave<PointT, PointPos>> Ptr;
    typedef pcl::PointCloud<PointT> PointCloud;
    typedef typename PointCloud::Ptr PointCloudPtr;

    typedef pcl::PointCloud<PointPos> PointCloudPos;
    typedef typename PointCloudPos::Ptr PointCloudPosPtr;

  public:
    KfSave();
    ~KfSave();
    int c_iVersionMap = 2;
    const int c_iFieldSize_ = 1024 * 1024;
    enum FeatureType {
        Path = 0,
        Corn,
        Surf,
        Curb,
        Cloud3D = 255,
        Cloud2D = 254,
    };  // 新增类型点云须顺序添加 并保持写入读取时 压入顺序一致
    /**
     * @brief 对点云进行加密
     * @param p_strBinFile 路径
     * @param p_KFNum 每个关键帧点云对应各种类型点云数
     * @param p_pPos 关键帧pose
     * @param p_feature 关键帧特征点云等
     * @param p_pcVisible 可视化点云，allPc等
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool writeBinary(std::string p_strBinFile,
                     std::vector<st_KfPointNum> p_KFNum,
                     PointCloudPosPtr p_pPos,
                     std::vector<PointCloudPtr>& p_feature,
                     std::vector<PointCloudPtr>& p_pcVisible);  // PointCloudPtr p_pSur,
                                                                // PointCloudPtr p_pCor = NULL,
                                                                // PointCloudPosPtr p_pPos = NULL
    /**
     * @brief
     * @param p_strBinFile 路径
     * @param p_KFNum 每个关键帧点云对应各种类型点云数
     * @param p_pPos 关键帧pose
     * @param p_feature 关键帧特征点云等
     * @param p_pcVisible 可视化点云，allPc等
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool readBinary(const std::string p_strBinFile,
                    std::vector<st_KfPointNum>& p_KFNum,
                    PointCloudPosPtr p_pPos,
                    std::vector<PointCloudPtr> p_feature,
                    std::vector<PointCloudPtr> p_pcVisible);
    /**
     * @brief 设置版本
     * @param p_iVersion
     *
     */
    void setVersion(int p_iVersion);

  private:
    /**
     * @brief 处理加密文件的头
     * @param p_sHeader 头结构体信息
     * @param p_KFNum 每个关键帧点云对应各种类型点云数
     * @param p_pPos 关键帧pose
     * @param p_feature 关键帧特征点云等
     * @param p_pcVisible 可视化点云，allPc等
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool headerHandle_(s_Header& p_sHeader,
                       std::vector<st_KfPointNum> p_KFNum,
                       PointCloudPosPtr p_pPos,
                       std::vector<PointCloudPtr> p_feature,
                       std::vector<PointCloudPtr> p_pcVisible);
    /**
     * @brief 写加密文件的头
     * @param p_file 写的流
     * @param p_sHeader 加密文件头
     *
     */
    void writeHandle_(gzFile& p_file, s_Header& p_sHeader);
    /**
     * @brief 写特征点云
     * @param p_file 写的流
     * @param p_feature 关键帧特征点云等
     * @param p_sHeader 头结构体信息
     *
     */
    void
    writeFeatureBinary_(gzFile& p_file, std::vector<PointCloudPtr>& p_feature, s_Header& p_sHeader);
    /**
     * @brief 写可视化点云allpc等
     * @param p_file 写的流
     * @param p_pcVisible 可视化点云，allPc等
     * @param p_sHeader 头结构体信息
     *
     */
    void writeVisiblePcBinary_(gzFile& p_file,
                               std::vector<PointCloudPtr>& p_pcVisible,
                               s_Header& p_sHeader);
    /**
     * @brief 写path或者关键帧pose信息
     * @param p_file 写的流
     * @param p_pPos path或者关键帧pose
     * @param p_sHeader 头结构体信息
     *
     */
    void writePathBinary_(gzFile& p_file, PointCloudPosPtr& p_pPos, s_Header& p_sHeader);
    /**
     * @brief 写每个关键帧点云对应各种类型点云数
     * @param p_file 写的流
     * @param p_KFNum 每个关键帧点云对应各种类型点云数
     * @param p_sHeader 头结构体信息
     *
     */
    void writeKfNumBinary_(gzFile& p_file, std::vector<st_KfPointNum> p_KFNum, s_Header& p_sHeader);

  private:
    std::vector<int> c_vTypeList;
};

#include "KfSave.hpp"
#endif