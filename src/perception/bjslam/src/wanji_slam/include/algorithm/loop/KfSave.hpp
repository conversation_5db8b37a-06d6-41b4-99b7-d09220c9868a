/*
 * @Author: your name
 * @Date: 2021-05-25 10:51:51
 * @LastEditTime: 2023-04-24 20:16:16
 * @LastEditors: <NAME_EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_slam/wslam/src/KfSave.hpp
 */
#include "./KfSave.h"
#include <fstream>
#include <iostream>
#include <pcl/PCLPointField.h>
#include <pcl/common/io.h>
#include <pcl/io/pcd_io.h>
#include <string>
#include <vector>

template <typename PointT, typename PointPos> KfSave<PointT, PointPos>::KfSave() {}
template <typename PointT, typename PointPos> KfSave<PointT, PointPos>::~KfSave() {}

template <typename PointT, typename PointPos>
void KfSave<PointT, PointPos>::setVersion(int p_iVersion)
{
    c_iVersionMap = p_iVersion;
}

template <typename PointT, typename PointPos>
bool KfSave<PointT, PointPos>::headerHandle_(s_Header& p_sHeader,
                                             std::vector<st_KfPointNum> p_KFNum,
                                             PointCloudPosPtr p_pPos,
                                             std::vector<PointCloudPtr> p_feature,
                                             std::vector<PointCloudPtr> p_pcVisible)
{
    std::vector<pcl::PCLPointField> l_fields;
    s_FeatureInfo l_stFeatureInfo;
    if (p_pPos != NULL && !p_pPos->empty())
    {
        l_stFeatureInfo.m_iPntNum = p_pPos->size();
        pcl::getFields(*p_pPos, l_fields);
        l_stFeatureInfo.m_iFieldNum = l_fields[0].datatype;  //具体不知道什么意思，字段数
        l_stFeatureInfo.m_iByteNum = sizeof(p_pPos->at(0));
        l_stFeatureInfo.m_iFeatureType = 0;
        p_sHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
    }

    for (int i = 0; i < (int)p_feature.size(); i++)
    {
        // 指针未空 or 点数为空
        if (p_feature[i] == NULL || !p_feature[i]->size())
            continue;

        l_stFeatureInfo.m_iPntNum = p_feature[i]->size();
        pcl::getFields(*p_feature[i], l_fields);
        l_stFeatureInfo.m_iFieldNum = l_fields[0].datatype;
        l_stFeatureInfo.m_iByteNum = sizeof(p_feature[i]->at(0));
        l_stFeatureInfo.m_iFeatureType = i + 1;
        p_sHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
    }

    if (c_iVersionMap == 2)
    {
        //可视化点云
        for (int i = 0; i < (int)p_pcVisible.size(); i++)
        {
            // 指针未空 or 点数为空
            if (p_pcVisible[i] == NULL || !p_pcVisible[i]->size())
                continue;
            l_stFeatureInfo.m_iPntNum = p_pcVisible[i]->size();
            pcl::getFields(*p_pcVisible[i], l_fields);
            l_stFeatureInfo.m_iFieldNum = l_fields[0].datatype;
            l_stFeatureInfo.m_iByteNum = sizeof(p_pcVisible[i]->at(0));
            l_stFeatureInfo.m_iFeatureType = 255 - i;
            p_sHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
        }
    }
    if (!p_KFNum.empty())
    {
        l_stFeatureInfo.m_iPntNum = p_KFNum.size();
        l_stFeatureInfo.m_iByteNum = sizeof(p_KFNum.at(0));
        l_stFeatureInfo.m_iFeatureType =
            10;  //暂定此处类型为10。前面关键帧pose和关键帧各种点云类型分别为0---1234等。后面allpc类型为255开头等递减
        p_sHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
    }
    return true;
}

template <typename PointT, typename PointPos>
void KfSave<PointT, PointPos>::writeHandle_(gzFile& p_file, s_Header& p_sHeader)
{
    //写头文件
    //写类型数量
    int l_iSize = p_sHeader.m_vFeatureInfo.size();
    gzwrite(p_file, (char*)&l_iSize, sizeof(int));  //写到压缩文件中

    char l_cReserve[100];  // 100 - 8(版本占8字节)
    if (c_iVersionMap == 2)
    {
        int l_iVersion = c_iVersionMap;
        char ff[] = "ff";
        char aa[] = "aa";
        gzwrite(p_file, (char*)&ff, 2);  //写到压缩文件中
        gzwrite(p_file, (char*)&l_iVersion, 4);
        gzwrite(p_file, (char*)&aa, 2);
        gzwrite(p_file, (char*)&l_cReserve[0], 92);
    }
    else
    {
        //预留100字节
        gzwrite(p_file, (char*)&l_cReserve[0], 100);
        if (c_iVersionMap != 1)
            printf("Error set map version | %d\n", c_iVersionMap);
    }

    for (int i = 0; i < (int)p_sHeader.m_vFeatureInfo.size(); i++)
    {
        gzwrite(p_file, (char*)&(p_sHeader.m_vFeatureInfo[i].m_iFeatureType), sizeof(int));  //类型
        gzwrite(p_file, (char*)&(p_sHeader.m_vFeatureInfo[i].m_iPntNum), sizeof(int));    //点数
        gzwrite(p_file, (char*)&(p_sHeader.m_vFeatureInfo[i].m_iFieldNum), sizeof(int));  //字段数
        gzwrite(p_file, (char*)&(p_sHeader.m_vFeatureInfo[i].m_iByteNum), sizeof(int));  //字节数
        //预留100字节
        gzwrite(p_file, (char*)&(l_cReserve[0]), 100);
    }
}

template <typename PointT, typename PointPos>
void KfSave<PointT, PointPos>::writeKfNumBinary_(gzFile& p_file,
                                                 std::vector<st_KfPointNum> p_KFNum,
                                                 s_Header& p_sHeader)
{
    for (int i = 0; i < (int)p_sHeader.m_vFeatureInfo.size(); i++)
    {
        //路径点
        if (p_sHeader.m_vFeatureInfo[i].m_iFeatureType != 10)  //特殊点
            continue;

        //判断是否为空
        if (p_KFNum.empty())
            return;
        //总点数
        int l_iFldSize = c_iFieldSize_;
        char* l_cFld = new char[l_iFldSize];
        int l_iFldIdx = 0;
        for (size_t j = 0; j < p_KFNum.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_KFNum[j]),
                   p_sHeader.m_vFeatureInfo[i].m_iByteNum);
            l_iFldIdx += p_sHeader.m_vFeatureInfo[i].m_iByteNum;
            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_sHeader.m_vFeatureInfo[i].m_iByteNum) > l_iFldSize)
            {
                gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
        delete[] l_cFld;
    }
}

template <typename PointT, typename PointPos>
void KfSave<PointT, PointPos>::writePathBinary_(gzFile& p_file,
                                                PointCloudPosPtr& p_pPos,
                                                s_Header& p_sHeader)
{
    if (p_pPos->empty())
        return;
    //总点数
    int l_iFldSize = c_iFieldSize_;
    char* l_cFld = new char[l_iFldSize];
    int l_iFldIdx = 0;
    for (size_t j = 0; j < p_pPos->size(); j++)
    {
        memcpy(&l_cFld[l_iFldIdx],
               reinterpret_cast<const char*>(&p_pPos->points[j]),
               p_sHeader.m_vFeatureInfo[0].m_iByteNum
                   + (p_sHeader.m_vFeatureInfo[0].m_iFeatureType + 1));
        l_iFldIdx += (p_sHeader.m_vFeatureInfo[0].m_iByteNum
                      + (p_sHeader.m_vFeatureInfo[0].m_iFeatureType + 1));

        //该行剩余内存不够写一个点，跳入下一行
        if ((l_iFldIdx + p_sHeader.m_vFeatureInfo[0].m_iByteNum
             + (p_sHeader.m_vFeatureInfo[0].m_iFeatureType + 1))
            > l_iFldSize)
        {
            gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
            l_iFldIdx = 0;
        }
    }
    gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
    delete[] l_cFld;
}

template <typename PointT, typename PointPos>
void KfSave<PointT, PointPos>::writeFeatureBinary_(gzFile& p_file,
                                                   std::vector<PointCloudPtr>& p_feature,
                                                   s_Header& p_sHeader)
{
    //总点数
    int l_iFldSize = c_iFieldSize_;
    char* l_cFld = new char[l_iFldSize];
    int l_iFldIdx = 0;
    int l_iFeatureInd = 0;

    for (size_t i = 0; i < p_sHeader.m_vFeatureInfo.size(); i++)
    {
        //路径点
        if ((p_sHeader.m_vFeatureInfo[i].m_iFeatureType == 0)
            || (p_sHeader.m_vFeatureInfo[i].m_iFeatureType > 5))
            continue;

        l_iFldIdx = 0;
        l_iFeatureInd = p_sHeader.m_vFeatureInfo[i].m_iFeatureType - 1;

        for (size_t j = 0; j < p_feature[l_iFeatureInd]->points.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_feature[l_iFeatureInd]->points[j]),
                   p_sHeader.m_vFeatureInfo[i].m_iByteNum
                       + (p_sHeader.m_vFeatureInfo[i].m_iFeatureType + 1));

            l_iFldIdx += (p_sHeader.m_vFeatureInfo[i].m_iByteNum
                          + (p_sHeader.m_vFeatureInfo[i].m_iFeatureType + 1));

            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_sHeader.m_vFeatureInfo[i].m_iByteNum
                 + (p_sHeader.m_vFeatureInfo[i].m_iFeatureType + 1))
                > l_iFldSize)
            {
                gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
    }
    delete[] l_cFld;
}

template <typename PointT, typename PointPos>
void KfSave<PointT, PointPos>::writeVisiblePcBinary_(gzFile& p_file,
                                                     std::vector<PointCloudPtr>& p_pcVisible,
                                                     s_Header& p_sHeader)
{
    //总点数
    int l_iFldSize = c_iFieldSize_;
    char* l_cFld = new char[l_iFldSize];
    int l_iFldIdx = 0;
    int l_iFeatureInd = 0;

    for (int i = 0; i < (int)p_sHeader.m_vFeatureInfo.size(); i++)
    {
        //路径点
        if ((p_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 200)
            || (p_sHeader.m_vFeatureInfo[i].m_iFeatureType > 255))
            continue;

        l_iFldIdx = 0;
        l_iFeatureInd = 255 - p_sHeader.m_vFeatureInfo[i].m_iFeatureType;

        for (size_t j = 0; j < p_pcVisible[l_iFeatureInd]->points.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_pcVisible[l_iFeatureInd]->points[j]),
                   p_sHeader.m_vFeatureInfo[i].m_iByteNum);

            l_iFldIdx += (p_sHeader.m_vFeatureInfo[i].m_iByteNum);

            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_sHeader.m_vFeatureInfo[i].m_iByteNum) > l_iFldSize)
            {
                gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        gzwrite(p_file, (char*)&l_cFld[0], l_iFldIdx);
    }

    delete[] l_cFld;
}

template <typename PointT, typename PointPos>
bool KfSave<PointT, PointPos>::writeBinary(std::string p_strBinFile,
                                           std::vector<st_KfPointNum> p_KFNum,
                                           PointCloudPosPtr p_pPos,
                                           std::vector<PointCloudPtr>& p_feature,
                                           std::vector<PointCloudPtr>& p_pcVisible)
{
    //头文件操作
    s_Header l_sHeader;
    headerHandle_(l_sHeader, p_KFNum, p_pPos, p_feature, p_pcVisible);
    gzFile l_outFile = gzopen(p_strBinFile.c_str(), "wb");
    if (!l_outFile)
    {
        return false;
    }
    writeHandle_(l_outFile, l_sHeader);
    writePathBinary_(l_outFile, p_pPos, l_sHeader);
    writeFeatureBinary_(l_outFile, p_feature, l_sHeader);
    writeVisiblePcBinary_(l_outFile, p_pcVisible, l_sHeader);
    writeKfNumBinary_(l_outFile, p_KFNum, l_sHeader);
    gzclose(l_outFile);  //关闭压缩文件
    return true;
}

template <typename PointT, typename PointPos>
bool KfSave<PointT, PointPos>::readBinary(const std::string p_strBinFile,
                                          std::vector<st_KfPointNum>& p_KFNum,
                                          PointCloudPosPtr p_pPos,
                                          std::vector<PointCloudPtr> p_feature,
                                          std::vector<PointCloudPtr> p_pcVisible)
{
    gzFile l_inFile = gzopen(p_strBinFile.c_str(), "rb");
    if (!l_inFile)
    {
        printf("文件或者路径不对\n");
        return false;
    }

    int l_iFldSize = c_iFieldSize_;
    char* l_cFld = new char[l_iFldSize];
    s_Header l_sHeader;
    //读取头文件 获取总点数+ 类型数
    gzread(l_inFile, (char*)&l_sHeader, 4);
    //读取版本
    int l_iVersion = 1;
    char l_cVersion[8];
    gzread(l_inFile, (char*)&l_cVersion, 8);
    if (l_cVersion[0] == 'f' && l_cVersion[1] == 'f' && l_cVersion[7] == 'a'
        && l_cVersion[6] == 'a')
    {
        memcpy(&l_iVersion, &l_cVersion[2], 4);
        printf("当前读取地图版本为V%d\n", l_iVersion);
    }
    else
    {
        printf("当前读取建图数据版本为V%d\n", l_iVersion);
    }

    if (l_iVersion != c_iVersionMap)
    {
        printf("地图版本不匹配 | 设置： %d 读取：%d\n", c_iVersionMap, l_iVersion);
        gzclose(l_inFile);
        return false;
    }

    //预留100-8
    char l_cReserve[100];  //注意
    gzread(l_inFile, (char*)&l_cReserve, 92);

    s_FeatureInfo l_sFeatureInfo;
    for (int m = 0; m < l_sHeader.m_iTypeNum; m++)
    {
        int l_iIndex = 0;
        gzread(l_inFile, (char*)&l_cFld[0], (4 * 4));
        memcpy(&l_sFeatureInfo.m_iFeatureType, &l_cFld[l_iIndex], 4);
        memcpy(&l_sFeatureInfo.m_iPntNum, &l_cFld[l_iIndex + 4], 4);
        memcpy(&l_sFeatureInfo.m_iFieldNum, &l_cFld[l_iIndex + 8], 4);
        memcpy(&l_sFeatureInfo.m_iByteNum, &l_cFld[l_iIndex + 12], 4);
        l_sHeader.m_vFeatureInfo.push_back(l_sFeatureInfo);

        //预留100
        gzread(l_inFile, (char*)&l_cReserve, 100);
    }

    // 判断第1个类型是不是Path
    bool l_bHavePathMap = false;
    if (!l_sHeader.m_vFeatureInfo[0].m_iFeatureType)
        l_bHavePathMap = true;
    for (int i = 0; i < l_sHeader.m_iTypeNum; i++)
    {
        int l_iOffset = 0;
        if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 5)
        {
            l_iOffset = l_sHeader.m_vFeatureInfo[i].m_iByteNum
                        + l_sHeader.m_vFeatureInfo[i].m_iFeatureType + 1;
            if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > (int)p_feature.size())
            {
                printf("读取地图不匹配\n");
                return false;
            }
        }
        else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType == 10)
        {
            l_iOffset = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
        }
        else
        {
            l_iOffset = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
            if (255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType >= (int)p_pcVisible.size())
            {
                printf("读取地图不匹配\n");
                return false;
            }
        }

        // int l_iOffset =
        //     l_sHeader.m_vFeatureInfo[i].m_iByteNum + l_sHeader.m_vFeatureInfo[i].m_iFeatureType +
        //     1;
        int l_iPntFldSize = l_sHeader.m_vFeatureInfo[i].m_iPntNum * l_iOffset;

        // path和其他特征区分
        if (l_bHavePathMap && !i)
        {
            p_pPos->width = l_sHeader.m_vFeatureInfo[i].m_iPntNum;
            p_pPos->height = 1;
            p_pPos->points.resize(p_pPos->width);
        }
        else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > 0
                 && l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 5)  //类型改了
        {
            if ((l_sHeader.m_vFeatureInfo[i].m_iFeatureType >= (int)p_feature.size() + 1))
            {
                printf("type over size haveType[%d]: %d\n",
                       i,
                       l_sHeader.m_vFeatureInfo[i].m_iFeatureType);
                continue;
            }
            p_feature[l_sHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->width =
                l_sHeader.m_vFeatureInfo[i].m_iPntNum;
            p_feature[l_sHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->height = 1;
            p_feature[l_sHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->points.resize(
                p_feature[l_sHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->width);
        }
        else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > 200
                 && l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 255)
        {
            if (255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType >= (int)p_pcVisible.size())
            {
                printf("type over size haveType[%d]: %d\n",
                       i,
                       l_sHeader.m_vFeatureInfo[i].m_iFeatureType);
                continue;
            }
            p_pcVisible[255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType]->width =
                l_sHeader.m_vFeatureInfo[i].m_iPntNum;
            p_pcVisible[255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType]->height = 1;
            p_pcVisible[255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType]->points.resize(
                p_pcVisible[255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType]->width);
        }
        else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType == 10)
        {
            p_KFNum.resize(l_sHeader.m_vFeatureInfo[i].m_iPntNum);
        }

        if (l_iPntFldSize <= l_iFldSize)
        {
            //读数据
            gzread(l_inFile, (char*)&l_cFld[0], l_iPntFldSize);
            int l_iFldIdx = 0;
            for (int j = 0; j < l_sHeader.m_vFeatureInfo[i].m_iPntNum; j++)
            {
                if (l_bHavePathMap && !i)
                {
                    int l_iLen = sizeof(PointPos);
                    if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    PointPos a;
                    memcpy(&a, &l_cFld[l_iFldIdx], l_iLen);
                    memcpy(&p_pPos->points[j], &l_cFld[l_iFldIdx], l_iLen);
                }
                else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > 0
                         && l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 5)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(&p_feature[l_sHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->points[j],
                           &l_cFld[l_iFldIdx],
                           l_iLen);
                }
                else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > 200
                         && l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 255)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(
                        &p_pcVisible[255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType]->points[j],
                        &l_cFld[l_iFldIdx],
                        l_iLen);
                }
                else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType == 10)
                {
                    int l_iLen = sizeof(st_KfPointNum);
                    memcpy(&p_KFNum[j], &l_cFld[l_iFldIdx], l_iLen);
                }
                l_iFldIdx += l_iOffset;
            }
        }
        else
        {
            int l_iReadOffset = (l_iFldSize / l_iOffset) * l_iOffset;
            int l_iMult = l_iPntFldSize / l_iReadOffset;
            int l_iRem = l_iPntFldSize % l_iReadOffset;
            //读数据
            gzread(l_inFile, (char*)&l_cFld[0], l_iReadOffset);
            int l_iFldIdx = 0;
            int l_iN = 1;
            for (int j = 0; j < l_sHeader.m_vFeatureInfo[i].m_iPntNum; j++)
            {
                if (l_bHavePathMap && !i)
                {
                    int l_iLen = sizeof(PointPos);
                    if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(&p_pPos->points[j], &l_cFld[l_iFldIdx], l_iLen);
                }
                else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > 0
                         && l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 5)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(&p_feature[l_sHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->points[j],
                           &l_cFld[l_iFldIdx],
                           l_iLen);
                }
                else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > 200
                         && l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 255)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(
                        &p_pcVisible[255 - l_sHeader.m_vFeatureInfo[i].m_iFeatureType]->points[j],
                        &l_cFld[l_iFldIdx],
                        l_iLen);
                }
                else if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType == 10)
                {
                    int l_iLen = sizeof(st_KfPointNum);
                    memcpy(&p_KFNum[j], &l_cFld[l_iFldIdx], l_iLen);
                }

                l_iFldIdx += l_iOffset;
                if (l_iFldIdx == l_iReadOffset && l_iN != l_iMult)
                {
                    gzread(l_inFile, (char*)&l_cFld[0], l_iReadOffset);
                    l_iFldIdx = 0;
                    l_iN++;
                }  // 非第一行
                else if (l_iFldIdx == l_iReadOffset && l_iN == l_iMult)
                {
                    gzread(l_inFile, (char*)&l_cFld[0], l_iRem);
                    l_iFldIdx = 0;
                }
            }
        }
    }

    gzclose(l_inFile);

    delete[] l_cFld;
    return true;
}