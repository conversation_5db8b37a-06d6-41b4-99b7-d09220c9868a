/**
 * @file trajectoryAnalysis.h
 * <AUTHOR> Li
 * @brief 雷达标定过程静态\动态位姿处理收集处理器
 * @version 1.0
 * @date 2023-07-28
 * @copyright Copyright (c)2023 Vanjee
 */
#pragma once
#include "./impl/dynamicAverage.hpp"
#include "common/common_ex.h"
#include <map>

namespace wj_slam {
/**
 * @brief 静态初始化实现类
 *
 */
class StaticAnalysis {
  public:
    using Ptr = boost::shared_ptr<StaticAnalysis>;

  private:
    using Pose = s_POSE6D;
    using Velo = s_POSE6D;
    using PoseTwist = s_PoseWithTwist;
    using PoseTwistMean = runningStatPoseWithTwist;
    // using Time = timeMs;

    const int MINNUM_ANAL = 9; /**< 最小开始分析、发送数量*/

    int c_iUnstableTHR_;       /**< 最大容忍抖动时间*/
    double c_fVeloTHR_;        /**< 速度上限*/
    double c_fStdDevTHR_[2];   /**< 位置、角度差异上限*/
    PoseTwistMean c_MeanPose_; /**< 平均位姿*/
    timeMs c_iStaticEndTime_;    /**< 静止终止时间*/
    PoseTwist c_iNewPose_;     /**< 最新位姿*/
    PoseTwist c_iLastPose_;    /**< 上次位姿*/
    timeMs c_iUnStaticDuration_; /**< 抖动持续时间*/
    int c_iStaticTime_;        /**< 静止持续时间*/

  public:
    StaticAnalysis(int p_iUnstableTHR,
                   double p_fVeloTHR,
                   double p_fPosStdDev,
                   double p_fAnglStdDev);
    ~StaticAnalysis();

    /**
     * @brief 设置最新位姿
     * @param p_stPoseGlobal 最新位姿
     *
     */
    void renewPose(PoseTwist p_stPoseGlobal);

    /**
     * @brief 获取静止时间
     * @code
     *
     * @endcode
     * @return [int] \n
     * [返回静止时间]
     *
     */
    int hasStaticTime();

    /**
     * @brief 获取最新的静止时段平均位姿
     * @param p_stStaticMean 平均位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * [成功获取]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [获取失败,未填充]
     *
     */
    bool getLatestStaticMeanPose(PoseTwist& p_stStaticMean);

  private:
    /**
     * @brief 检测是否静止
     * @param p_newPose
     * @param p_lastPose
     * @code
     *
     * @endcode
     * @return [true] \n
     * [认为静止]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [认为不静止]
     *
     */
    bool checkStable_(PoseTwist p_newPose, PoseTwist p_lastPose);
};
/**
 * @brief 不同位置静态标定实现类
 *
 */
class TrajectoryAnalysis {
  public:
    using Ptr = boost::shared_ptr<TrajectoryAnalysis>;

  private:
    using Velo = s_POSE6D;
    using Pose = s_PoseWithTwist;
    using PoseList = std::vector<Pose>;

    const int DELET_DELAY = 410; /**< 前后帧时间差异过大阈值*/

    Pose c_LastPose_;       /**< 等待添加的路径*/
    Pose c_LastStaticPose_; /**< 记录上一次静止的位置*/
    PoseList c_vPath_;      /**< 路径*/
    int c_iCaliNum_;        /**< 记录可能用于标定计算的帧数*/
    int c_iJumpDuration_;   /**< 位姿点时间系数*/
    float c_fMoveVeloTHR_;  /**< 用于静止判定的速度最大值*/
    // float c_fVeloDiffTHR_;       /**< 用于匀速判定的速度差异(单位时间加速度)*/
    float c_fMaxRotSpeedTHR_;    /**< 用于最大旋转速度判断*/
    float c_fMinChangeDistance_; /**< 用于相比上次静态位置最小移动位置判断*/
    float c_fMinChangeAng_;      /**< 用于相比上次静态位置最小角度变化判断*/

  public:
    TrajectoryAnalysis(int p_iHarfWindowSize,
                       float p_fMoveVeloTHR,
                       float p_fMaxRotSpeedTHR,
                       float p_fMinChangeDistance,
                       float p_fMinChangeAng);

    /**
     * @brief 初始化
     *
     */
    void initParam();

    /**
     * @brief 设置最新位姿,更新队列
     * @param p_stPoseGlobal
     *
     */
    void renewPose(Pose p_stPoseGlobal);

    /**
     * @brief 拷贝位姿到外部
     * @param p_list 新增的位姿队列
     *
     */
    void getNewPoseList(PoseList& p_list);

  private:
    /**
     * @brief 计算插值时间点的位置并加入队列
     * @param p_start 起始位置速度
     * @param p_end 终止位置速度
     * @param p_path 填充p_path 队列
     *
     */
    void calcPoseAtDTime_(Pose& p_start, Pose& p_end, PoseList& p_path);

    /**
     * @brief 判断是否匀速运动
     * @param p_lastSpeed 旧速度
     * @param p_curSpeed 新速度
     * @code
     *
     * @endcode
     * @return [true] \n
     * [匀速]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [不是匀速,不参与计算]
     *
     */
    bool isUniformVelocity_(Velo& p_lastSpeed, Velo& p_curSpeed);
};

class MultiTrajectoryAnalysis {
  public:
    using Ptr = boost::shared_ptr<MultiTrajectoryAnalysis>;

  private:
    using Pose = s_POSE6D;
    using PoseID = std::pair<int, Eigen::VectorXd>;
    using PoseVec = std::pair<int, PoseID>;
    using List = std::multimap<int, PoseID>;
    using multiMapItor = List::iterator;
    using PoseMean = runningStatPose;

    const int DELET_DELAY = 3000; /**< 达到限制则删除数据*/

    List c_mPosePairList_;          /**< 时间-雷达号-位姿 组*/
    std::vector<PoseMean> c_vMean_; /**< 各雷达的平均位姿*/
    int c_iLidarNum_;               /**< 雷达数*/
    int c_iBaseLidar_;              /**< 基准雷达*/
    int c_iLastestTime_;            /**< 最新时间戳*/

  public:
    MultiTrajectoryAnalysis(int p_iLidarNum, int p_iBaseLidar);
    /**
     * @brief 更新位姿队列
     * @param p_iLidar 雷达号
     * @param p_iTimeStamp 时间戳
     * @param p_new 最新位姿
     *
     */
    void renewPose(int p_iLidar, int p_iTimeStamp, Pose p_new);

    /**
     * @brief 计算新加入的位姿
     *
     */
    void calc();

    /**
     * @brief 获取某一雷达的最新位姿转移和标准差
     * @param p_iID 雷达号
     * @param p_pose 位姿转移
     * @param p_dev 标准差
     * @code
     *
     * @endcode
     * @return [int] \n
     * [标定次数]
     *
     */
    int getNewCalibData(int p_iID, Eigen::VectorXd& p_pose, Eigen::VectorXd& p_dev);

  private:
    /**
     * @brief 扫描map中的新绑定关系进行计算
     * @param p_list
     *
     */
    void checkNewPair_(List& p_list);

    /**
     * @brief 计算新的转移关系并加入平均
     * @param p_pos 新绑定位姿对
     *
     */
    void getNewCalibData_(std::pair<multiMapItor, multiMapItor>& p_pos);
};

}  // namespace wj_slam
#include "impl/trajectoryAnalysis.hpp"