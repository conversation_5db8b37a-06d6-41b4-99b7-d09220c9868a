/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushuang
 * @Date: 2021-07-14 19:04:55
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-04-15 14:56:37
 */
#pragma once
#include "../fakeOdometry.h"
namespace wj_slam {

template <typename P>
FakeOdometry<P>::FakeOdometry(std::queue<FEATURE_PAIR_PTR>& p_feature,
                              KF_OUTPUT_CB p_keyFrameCb,
                              int timeOffset)
    : c_keyFrameCb_(p_keyFrameCb), c_featureBuf_(p_feature)
{
    c_pTranser_.reset(new LaserTransform<P>());
    c_pTranser_->setTimeOffset(timeOffset);
    paramInit_();
}
template <typename P> FakeOdometry<P>::~FakeOdometry()
{
    std::cout << "exit FakeOdometry" << std::endl;
    this->shutDown();
    c_pCurrFeature_ = nullptr;
    c_pTranser_ = nullptr;
}
template <typename P> void FakeOdometry<P>::paramReset_()
{
    std::lock_guard<std::mutex> l_mtx(c_mtxCalibLock_);
    c_stPredictPose_.m_Pose.reset();
    c_stPredictPose_.m_Twist.reset();
    c_pCurrFeature_ = nullptr;
    c_bCalibRun_ = false;
    c_bCalibRunOver_ = true;
    c_bSysHasInit = false;
    c_bShutDown_ = false;
    c_bHasLocationDone_ = true;
    c_timeCurr_ = 0;
    c_stLastPredictPose_.m_tsSyncTime = 0;
}
template <typename P> void FakeOdometry<P>::paramInit_()
{
    paramReset_();
}
template <typename P> void FakeOdometry<P>::corretPC_(FEATURE_PAIR_PTR& p_pcInput)
{
    // 点云时间段T_sp内的的增量为 V[1/100ms] * (T_sp[ms] / 100[ms])
    s_TWIST l_sPoseTimeSpan = c_stPredictPose_.m_Twist * (p_pcInput->m_dTimespan / SCAN_TIME_MS);
    c_pTranser_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->first,
                                     p_pcInput->first,
                                     p_pcInput->m_dTimespan);
    c_pTranser_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->second,
                                     p_pcInput->second,
                                     p_pcInput->m_dTimespan);
    c_pTranser_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->fourth,
                                     p_pcInput->fourth,
                                     p_pcInput->m_dTimespan);
}

template <typename P> void FakeOdometry<P>::renewOdom_()
{
    std::lock_guard<std::mutex> l_mtx(c_mtxCalibLock_);
    s_POSE6D l_ics = c_stLastPredictPose_.m_Twist * c_fJumpNum_;
    // T(w,t2) = T(w,t1) * p(t1,t2)
    c_stPredictPose_.m_Pose = c_stLastPredictPose_.m_Pose * l_ics;
}
template <typename P> void FakeOdometry<P>::addKeyFrame_()
{
    KEYFRAME_PTR l_pKeyFrame;
    l_pKeyFrame.reset(new KEYFRAME<P>());
    *(l_pKeyFrame->m_pFeature) = *c_pCurrFeature_;

    //清空位姿标志
    c_stPredictPose_.m_Pose.m_bFlag = PoseStatus::Default;
    // 将终点时间戳的点云从局部坐标系转移到全局坐标系
    l_pKeyFrame->m_Pose = c_stPredictPose_.m_Pose;
    c_pTranser_->transformCloudPoints(l_pKeyFrame->m_Pose.m_quat,
                                      l_pKeyFrame->m_Pose.m_trans,
                                      l_pKeyFrame->m_pFeature->first,
                                      l_pKeyFrame->m_pFeature->first);
    c_pTranser_->transformCloudPoints(l_pKeyFrame->m_Pose.m_quat,
                                      l_pKeyFrame->m_Pose.m_trans,
                                      l_pKeyFrame->m_pFeature->second,
                                      l_pKeyFrame->m_pFeature->second);
    c_pTranser_->transformCloudPoints(l_pKeyFrame->m_Pose.m_quat,
                                      l_pKeyFrame->m_Pose.m_trans,
                                      l_pKeyFrame->m_pFeature->fourth,
                                      l_pKeyFrame->m_pFeature->fourth);

    l_pKeyFrame->m_bIsKeyFrame = false;
    c_keyFrameCb_(l_pKeyFrame);
}

template <typename P> void FakeOdometry<P>::clearFeatureBuf2Lastest_()
{
    uint l_uiCnt = c_featureBuf_.size() - 1;
    while (l_uiCnt--)
    {
        FEATURE_PAIR_PTR l_fe = c_featureBuf_.front();
        // LOGO(WWARN,
        //      "pop [{}] at {}ms pc2lastest by loct outtime",
        //      l_fe->m_uiScanFrame,
        //      l_fe->m_dTimestamp);
        c_featureBuf_.pop();
    }
    return;
}
template <typename P> void FakeOdometry<P>::clearFeatureBuf_()
{
    while (!c_featureBuf_.empty())
    {
        FEATURE_PAIR_PTR l_fe = c_featureBuf_.front();
        // LOGO(WWARN,
        //      "pop [{}] at {}ms by shutdown or outtime",
        //      l_fe->m_uiScanFrame,
        //      l_fe->m_dTimestamp);
        c_featureBuf_.pop();
    }
}
template <typename P> bool FakeOdometry<P>::hasPointCloud_()
{
    //若特征帧队列为空,false
    if (c_featureBuf_.empty())
        return false;
    //若关闭标定模块 或 没有在工作,false
    if (c_bShutDown_ || !c_bCalibRun_)
    {
        clearFeatureBuf_();

        return false;
    }
    // c_bHasLocationDone_标志,true
    if (c_bHasLocationDone_)
    {
        // pop队列至只有最后一帧
        clearFeatureBuf2Lastest_();
        return true;
    }
    else
    {
        //否则清空队列,false
        clearFeatureBuf_();
        return false;
    }
    return !c_featureBuf_.empty();
}

template <typename P> float FakeOdometry<P>::getJumpNum_()
{
    return (c_timeCurr_ - c_stLastPredictPose_.m_tsSyncTime) / SCAN_TIME_MS;
}
template <typename P>
s_PoseWithTwist FakeOdometry<P>::renewPrecisionPose(s_PoseWithTwist& p_stPoseGlobal)
{
    // 外部计算的速度并不是单个雷达的速度,而是在雷达间切换导致的位置差
    std::lock_guard<std::mutex> l_mtx(c_mtxCalibLock_);
    c_stPredictPose_ = p_stPoseGlobal;
    //增量
    MoveSpeed l_tempSpeed = c_stLastPredictPose_.m_Pose.inverse() * c_stPredictPose_.m_Pose;
    // 增量是速度（100ms增量）的N倍
    l_tempSpeed /=
        ((c_stPredictPose_.m_tsSyncTime - c_stLastPredictPose_.m_tsSyncTime) / SCAN_TIME_MS);
    // 平均速度
    c_stPredictPose_.m_Twist = (c_stLastPredictPose_.m_Twist * l_tempSpeed) * 0.5;
    // 替换上次位置
    c_stLastPredictPose_ = c_stPredictPose_;
    c_bHasLocationDone_ = true;
    return c_stPredictPose_;
    // c_stPredictPose_.m_Pose.printf("renewPrecisionPose");
    // c_stPredictPose_.m_Twist.printf("speed");
}
template <typename P> s_PoseWithTwist FakeOdometry<P>::getPrecisionPose()
{
    std::lock_guard<std::mutex> l_mtx(c_mtxCalibLock_);
    return c_stLastPredictPose_;
}
template <typename P> void FakeOdometry<P>::setInitPose(s_PoseWithTwist& p_stPoseGlobal)
{
    std::lock_guard<std::mutex> l_mtx(c_mtxCalibLock_);
    c_stPredictPose_ = p_stPoseGlobal;        //预测当前帧位姿（即当前帧终点位姿）
    c_stPredictPose_.m_Twist.reset();         // 0.1秒标准时间的位姿变化
    c_stLastPredictPose_ = c_stPredictPose_;  //上一次全局位置速度
    c_bHasLocationDone_ = true;
}
template <typename P> void FakeOdometry<P>::setSpeed(MoveSpeed p_speed)
{
    c_stPredictPose_.m_Twist = p_speed;
}
template <typename P> void FakeOdometry<P>::waitLocatDone()
{
    c_bHasLocationDone_ = false;
}
template <typename P> void FakeOdometry<P>::shutDown()
{
    c_bCalibRun_ = false;
    c_bShutDown_ = true;
}
template <typename P> void FakeOdometry<P>::start()
{
    paramReset_();
    c_bCalibRun_ = true;
}

template <typename P> void FakeOdometry<P>::run()
{
    while (1)
    {
        if (hasPointCloud_())
        {
            c_bCalibRunOver_ = false;
            // 更新当前信息(当前帧,ID,时间戳),当前时间戳速度假设为上次速度
            c_pCurrFeature_ = getCurrFeature_();
            // LOGO(WDEBUG,
            //      "[MLC] inputFeature-{} at {}.",
            //      c_pCurrFeature_->m_uiScanFrame,
            //      c_pCurrFeature_->m_dTimestamp);
            c_timeCurr_ = c_pCurrFeature_->m_tsSyncTime;
            // 若未初始化,设置旧帧为当前帧
            if (!c_bSysHasInit)
            {
                c_bSysHasInit = true;
                c_stLastPredictPose_.m_tsSyncTime = c_timeCurr_;
            }
            //点云畸变修正
            corretPC_(c_pCurrFeature_);
            // 定位时为0.99～1.01,建图时为-0.01～0.01
            c_fJumpNum_ = getJumpNum_();
            // 更新速度&位姿
            renewOdom_();
            // 打包预估位姿和帧发布
            addKeyFrame_();
            c_bCalibRunOver_ = true;
        }
        if (c_bShutDown_)
            break;
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
    printf("exit fakeOdometry-thread.\n");
}
template <typename P> void FakeOdometry<P>::stop()
{
    c_bCalibRun_ = false;
}
template <typename P> bool FakeOdometry<P>::isStop()
{
    if (false == c_bCalibRun_ && true == c_bCalibRunOver_)
        return true;
    return false;
}

}  // namespace wj_slam
   // #define INSTANTIATE_FakeOdometry(P) template class wj_slam::FakeOdometry<P>;