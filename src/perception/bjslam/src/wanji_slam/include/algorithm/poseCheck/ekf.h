/**
 * @file ekf.h
 * <AUTHOR> (chen<PERSON><PERSON>@wanji.net.cn)
 * @brief EKF方法定义
 * @version 1.0
 * @date 2022-08-19
 * @see http://docs.ros.org/en/noetic/api/robot_localization/html/index.html
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once

#include "filter.h"

namespace filter {

//! @brief Extended Kalman filter class
//!
//! Implementation of an extended Kalman filter (EKF). This
//! class derives from FilterBase and overrides the predict()
//! and correct() methods in keeping with the discrete time
//! EKF algorithm.
//!
class Ekf : public FilterBase {
  public:
    //! @brief Constructor for the Ekf class
    Ekf();

    //! @brief Destructor for the Ekf class
    //!
    ~Ekf();

    //! @brief Carries out the correct step in the predict/update cycle.
    //!
    //! @param[in] measurement - The measurement to fuse with our estimate
    //!
    void correct(const Measurement& measurement);

    //! @brief Carries out the predict step in the predict/update cycle.
    //!
    //! Projects the state and error matrices forward using a model of
    //! the vehicle's motion.
    //!
    //! @param[in] delta - The time step over which to predict.
    //!
    void predict(const double delta);
};

}  // namespace filter