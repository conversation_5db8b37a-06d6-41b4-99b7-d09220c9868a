#pragma once
#ifndef _IMU_FILTER_H_
#    define _IMU_FILTER_H_
#    include "common/config/conf_timer.h"
#    include "common/type/type_imu.h"
#    include "common/type/type_pose.h"
#    include "complementary_filter.h"
#    include <Eigen/Dense>
#    include <iostream>
#    include <mutex>
#    include <queue>
namespace wj_slam {
//测试一次update耗时,是否需要线程操作.
//考虑双雷达问题

class imuFilter {
  private:
    const double m_Gravity_ = 9.81;
    typedef timeMs Time;
    bool hasInit_;       /**< 初始化标志,车体做完零偏计算才认为初始化完成*/
    int m_iBiasCount_;   /**< 零偏统计次数下限*/
    int m_iStaticCount_; /**< 当前已统计次数*/
    int m_iReBiasCount_; /**< 当前已重新修正次数*/
    int m_iReBiasMax_;   /**< 允许的最大修正次数*/
    IMUData m_bias;
    IMUData m_currData_;
    IMUData m_lastData_;
    IMUData m_lastGetData_;
    Eigen::Vector3d m_vAngularVelNoise_;          /**< imu角速度噪声*/
    float m_afAccCorrect[6] = {0, 0, 0, 0, 0, 0}; /**< 加速度修正量,[0-2]x->z 尺度修正,[3-5]
                                                 x->z偏移修正*/
    float m_afTempCorrect[6] = {0, 0, 0, 0, 0, 0}; /**< 温度修正函数,[0,1]:X->k,b [2,3]:Y ..类推*/
    imu_tools::ComplementaryFilter m_CFilter_;
    std::list<IMUData> m_qIMUList_;
    int m_iMoveCount_ = 0;
    int m_iListMaxLen_ = 500; /**< m_qIMUList_允许保存的最大长度*/
    mutable std::mutex m_ListMtx_;

    /**
     * @brief IMU零偏修正
     * @param datain 输入的待修正数据
     *
     */
    size_t size() const
    {
        std::lock_guard<std::mutex> loc(m_ListMtx_);
        return m_qIMUList_.size();
    }
    void IMUZeroCorr_(IMUData& datain);
    /**
     * @brief IMU温度修正
     * @param datain
     * @return IMUData
     *
     */
    void IMUTempCorr_(IMUData& datain);
    /**
     * @brief IMU加速度修正
     * @param datain
     * @return IMUData
     *
     */
    void IMUAccCorr_(IMUData& datain);

    /**
     * @brief 计算IMU角速度零偏
     *
     */
    void calculateBias_(const IMUData& datain);
    /**
     * @brief IMU数据修正补偿,包括温度修正,尺度修正,零偏修正
     *
     */
    void IMUDataCorr_(IMUData& datain);
    /**
     * @brief IMU静止判断
     *
     */
    bool IMUStaticCheck_(const IMUData& datain);
    /**
     * @brief 使用imu数据积分
     *
     */
    void updateData_();

  public:
    imuFilter(/* args */);
    ~imuFilter();
    void inputRawData(const IMUData& datain);
    /**
     * @brief Get the Angular Twist object
     * @param p_vOut 输出角速度,单位rad/s,输出 \c p_time 至 p_time-100ms 时间段内的平均速度
     * @param p_time 同步时间戳
     * @param p_iTimePrecision 时间同步精度
     * @return true 数据可用
     * @return false 数据不可用
     *
     */
    bool
    getAngularTwist(Eigen::Vector3d& p_vOut, const Time& p_time, const Time& p_iTimePrecision = 10);
    /**
     * @brief 获取imu积分信息
     * @param p_IMUOut 输出
     * @param p_time 同步时间戳
     * @param isClear 清空队列标志
     * @param p_iTimePrecision 时间戳同步精度,ms单位
     * @return true 队列中找到数据
     * @return false 队列中没找到数据
     *
     */
    bool getIMUData(IMUData& p_IMUOut,
                    const Time& p_time,
                    const Time& p_iTimePrecision = 10,
                    const bool& isClear = false);
    const IMUData bias() const;
    IMUData& bias();
    void setAngularVelNoise(const Eigen::Vector3d& noise);
    void setBias(const IMUData& data);
};

}  // namespace wj_slam
#endif