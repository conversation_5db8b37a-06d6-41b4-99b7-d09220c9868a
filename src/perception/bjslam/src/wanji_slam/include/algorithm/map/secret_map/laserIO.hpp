/**
 * @file laserIO.hpp
 * <AUTHOR> Li
 * @brief 读写地图文件功能实现文件
 * @version 0.1
 * @date 2023-06-28
 *
 * @copyright Copyright (c) 2023
 *
 */
#include "laserIO.h"
#include <fstream>
#include <iostream>
#include <pcl/PCLPointField.h>
#include <pcl/common/io.h>
#include <pcl/io/pcd_io.h>
#include <string>
#include <vector>

template <typename PointT, typename PointPos> LaserIO<PointT, PointPos>::LaserIO() {}
template <typename PointT, typename PointPos> LaserIO<PointT, PointPos>::~LaserIO() {}
//静态地图版本类外初始化
template <typename PointT, typename PointPos>
int LaserIO<PointT, PointPos>::c_iVersionMap = 2;  //静态变量初始化

template <typename PointT, typename PointPos>
void LaserIO<PointT, PointPos>::setVersion(const int p_iVersion)
{
    c_iVersionMap = p_iVersion;
}

template <typename PointT, typename PointPos> int LaserIO<PointT, PointPos>::getVersion()
{
    return c_iVersionMap;
}

template <typename PointT, typename PointPos>
bool LaserIO<PointT, PointPos>::headerHandle_(st_Header& p_stHeader,
                                              PointCloudPosPtr p_pPos,
                                              std::vector<PointCloudPtr> p_feature,
                                              std::vector<PointCloudPtr> p_pcVisible)
{
    std::vector<pcl::PCLPointField> l_fields;
    st_FeatureInfo l_stFeatureInfo;

    if (p_pPos != NULL && !p_pPos->empty())
    {
        l_stFeatureInfo.m_iPntNum = p_pPos->size();  // m_iPntNum填充路径点数量
        pcl::getFields(*p_pPos, l_fields);
        l_stFeatureInfo.m_iFieldNum = l_fields[0].datatype;  // m_iFieldNum填充的字段数
        l_stFeatureInfo.m_iByteNum = sizeof(p_pPos->at(0));  // m_iByteNum填充该点云类型占用空间
        l_stFeatureInfo.m_iFeatureType = 0;                  // pose点云m_iFeatureType填充 0
        p_stHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
    }

    for (int i = 0; i < (int)p_feature.size(); i++)
    {
        // 指针为空 or 点数为空
        if (p_feature[i] == NULL || !p_feature[i]->size())
            continue;

        l_stFeatureInfo.m_iPntNum = p_feature[i]->size();
        pcl::getFields(*p_feature[i], l_fields);
        l_stFeatureInfo.m_iFieldNum = l_fields[0].datatype;
        l_stFeatureInfo.m_iByteNum = sizeof(p_feature[i]->at(0));
        // m_iFeatureType填充 first：1-second：2-fouth：3-third：4
        l_stFeatureInfo.m_iFeatureType = i + 1;
        p_stHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
    }

    if (c_iVersionMap == 2)
    {
        //可视化点云
        for (int i = 0; i < (int)p_pcVisible.size(); i++)
        {
            // 指针为空 or 点数为空
            if (p_pcVisible[i] == NULL || !p_pcVisible[i]->size())
                continue;

            l_stFeatureInfo.m_iPntNum = p_pcVisible[i]->size();
            pcl::getFields(*p_pcVisible[i], l_fields);
            l_stFeatureInfo.m_iFieldNum = l_fields[0].datatype;
            l_stFeatureInfo.m_iByteNum = sizeof(p_pcVisible[i]->at(0));
            // m_iFeatureType填充 allpc：255-2DPC：254
            l_stFeatureInfo.m_iFeatureType = 255 - i;
            p_stHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
        }
    }

    return true;
}

template <typename PointT, typename PointPos>
void LaserIO<PointT, PointPos>::writeHandle_(std::ofstream& p_file, st_Header& p_stHeader)
{
    //写头文件
    //写类型数量
    int l_iSize = p_stHeader.m_vFeatureInfo.size();
    p_file.write((char*)&l_iSize, sizeof(int));

    char l_cReserve[100];  // 100 - 8(版本占8字节)
    if (c_iVersionMap == 2)
    {
        int l_iVersion = c_iVersionMap;
        char ff[] = "ff";
        char aa[] = "aa";
        p_file.write((char*)&ff, 2);
        p_file.write((char*)&l_iVersion, 4);
        p_file.write((char*)&aa, 2);
        p_file.write((char*)&l_cReserve[0], 92);
    }
    else
    {
        //预留100字节
        p_file.write((char*)&l_cReserve[0], 100);
        if (c_iVersionMap != 1)
            printf("Error set map version | %d\n", c_iVersionMap);
    }

    for (int i = 0; i < (int)p_stHeader.m_vFeatureInfo.size(); i++)
    {
        p_file.write((char*)&(p_stHeader.m_vFeatureInfo[i].m_iFeatureType), sizeof(int));
        p_file.write((char*)&(p_stHeader.m_vFeatureInfo[i].m_iPntNum), sizeof(int));
        p_file.write((char*)&(p_stHeader.m_vFeatureInfo[i].m_iFieldNum), sizeof(int));
        p_file.write((char*)&(p_stHeader.m_vFeatureInfo[i].m_iByteNum), sizeof(int));
        //预留100字节用于扩展头信息
        p_file.write((char*)&(l_cReserve[0]), 100);
    }
}

template <typename PointT, typename PointPos>
void LaserIO<PointT, PointPos>::writePathBinary_(std::ofstream& p_file,
                                                 PointCloudPosPtr& p_pPos,
                                                 st_Header& p_stHeader)
{
    if (p_pPos == nullptr || p_pPos->empty())
        return;
    //总点数

    char* l_cFld = new char[FIEL_SIZE_RW];  // 开一个1024*1024 char写入数组
    int l_iFldIdx = 0;
    for (size_t j = 0; j < p_pPos->size(); j++)
    {
        memcpy(&l_cFld[l_iFldIdx],
               reinterpret_cast<const char*>(&p_pPos->points[j]),  // 强制类型转换
               p_stHeader.m_vFeatureInfo[0].m_iByteNum
                   + (p_stHeader.m_vFeatureInfo[0].m_iFeatureType + 1));
        l_iFldIdx += (p_stHeader.m_vFeatureInfo[0].m_iByteNum
                      + (p_stHeader.m_vFeatureInfo[0].m_iFeatureType + 1));

        //该行剩余内存不够写一个点，跳入下一行
        if ((l_iFldIdx + p_stHeader.m_vFeatureInfo[0].m_iByteNum
             + (p_stHeader.m_vFeatureInfo[0].m_iFeatureType + 1))
            > FIEL_SIZE_RW)
        {
            p_file.write((char*)&l_cFld[0], l_iFldIdx);
            l_iFldIdx = 0;
        }
    }
    p_file.write((char*)&l_cFld[0], l_iFldIdx);
    delete[] l_cFld;
}

template <typename PointT, typename PointPos>
void LaserIO<PointT, PointPos>::writeFeatureBinary_(std::ofstream& p_file,
                                                    std::vector<PointCloudPtr>& p_feature,
                                                    st_Header& p_stHeader)
{
    //总点数

    char* l_cFld = new char[FIEL_SIZE_RW];  // 开一个1024*1024 char写入数组
    int l_iFldIdx = 0;
    int l_iFeatureInd = 0;

    for (size_t i = 0; i < p_stHeader.m_vFeatureInfo.size(); i++)
    {
        //路径点
        if ((p_stHeader.m_vFeatureInfo[i].m_iFeatureType == 0)
            || (p_stHeader.m_vFeatureInfo[i].m_iFeatureType > 200))
            continue;

        l_iFldIdx = 0;
        l_iFeatureInd = p_stHeader.m_vFeatureInfo[i].m_iFeatureType - 1;

        for (size_t j = 0; j < p_feature[l_iFeatureInd]->points.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_feature[l_iFeatureInd]->points[j]),
                   p_stHeader.m_vFeatureInfo[i].m_iByteNum
                       + (p_stHeader.m_vFeatureInfo[i].m_iFeatureType + 1));

            l_iFldIdx += (p_stHeader.m_vFeatureInfo[i].m_iByteNum
                          + (p_stHeader.m_vFeatureInfo[i].m_iFeatureType + 1));

            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_stHeader.m_vFeatureInfo[i].m_iByteNum
                 + (p_stHeader.m_vFeatureInfo[i].m_iFeatureType + 1))
                > FIEL_SIZE_RW)
            {
                p_file.write((char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        p_file.write((char*)&l_cFld[0], l_iFldIdx);
    }
    delete[] l_cFld;
}

template <typename PointT, typename PointPos>
void LaserIO<PointT, PointPos>::writeVisiblePcBinary_(std::ofstream& p_file,
                                                      std::vector<PointCloudPtr>& p_pcVisible,
                                                      st_Header& p_stHeader)
{
    //总点数

    char* l_cFld = new char[FIEL_SIZE_RW];
    int l_iFldIdx = 0;
    int l_iFeatureInd = 0;

    for (int i = 0; i < (int)p_stHeader.m_vFeatureInfo.size(); i++)
    {
        //路径点
        if ((p_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 200)
            || (p_stHeader.m_vFeatureInfo[i].m_iFeatureType > 255))
            continue;

        l_iFldIdx = 0;
        l_iFeatureInd = 255 - p_stHeader.m_vFeatureInfo[i].m_iFeatureType;

        for (size_t j = 0; j < p_pcVisible[l_iFeatureInd]->points.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_pcVisible[l_iFeatureInd]->points[j]),
                   p_stHeader.m_vFeatureInfo[i].m_iByteNum);

            l_iFldIdx += (p_stHeader.m_vFeatureInfo[i].m_iByteNum);

            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_stHeader.m_vFeatureInfo[i].m_iByteNum) > FIEL_SIZE_RW)
            {
                p_file.write((char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        p_file.write((char*)&l_cFld[0], l_iFldIdx);
    }

    delete[] l_cFld;
}

template <typename PointT, typename PointPos>
bool LaserIO<PointT, PointPos>::writeBinary(std::string p_strBinFile,
                                            PointCloudPosPtr p_pPos,
                                            std::vector<PointCloudPtr>& p_feature,
                                            std::vector<PointCloudPtr>& p_pcVisible)
{
    //头文件操作
    st_Header l_stHeader;
    headerHandle_(l_stHeader, p_pPos, p_feature, p_pcVisible);

    std::ofstream l_outFile;
    l_outFile.open(p_strBinFile, std::ios::out | std::ios::binary);
    if (!l_outFile)
        return false;

    writeHandle_(l_outFile, l_stHeader);                        //写文件头信息
    writePathBinary_(l_outFile, p_pPos, l_stHeader);            //写路径点云
    writeFeatureBinary_(l_outFile, p_feature, l_stHeader);      //写特征点云
    writeVisiblePcBinary_(l_outFile, p_pcVisible, l_stHeader);  //写可视化点云

    l_outFile.close();
    return true;
}

template <typename PointT, typename PointPos>
bool LaserIO<PointT, PointPos>::readBinary(const std::string p_strBinFile,
                                           PointCloudPosPtr p_pPos,
                                           std::vector<PointCloudPtr> p_feature,
                                           std::vector<PointCloudPtr> p_pcVisible)
{
    std::ifstream l_inFile(p_strBinFile, std::ios::in | std::ios::binary);
    if (!l_inFile)
        return false;

    // 开一个1024*1024 的char数组用于从文件读取
    char* l_cFld = new char[FIEL_SIZE_RW];
    st_Header l_stHeader;
    //读取头文件 获取总点数+ 类型数
    l_inFile.read((char*)&l_stHeader.m_iTypeNum, 4);  //读取并写入l_sHeader.m_iTypeNum

    //读取版本
    int l_iVersion = 1;
    char l_cVersion[8];
    l_inFile.read((char*)&l_cVersion, 8);  // 第5-12位依次为ff(2)-version(4)-aa(2)
    if (l_cVersion[0] == 'f' && l_cVersion[1] == 'f' && l_cVersion[7] == 'a'
        && l_cVersion[6] == 'a')
    {
        memcpy(&l_iVersion, &l_cVersion[2], 4);
        printf("当前读取地图版本为V%d\n", l_iVersion);
    }
    else
    {
        printf("当前读取地图版本为V1\n");
        c_iVersionMap = 0;  //暂时返回0
    }

    // if (l_iVersion != c_iVersionMap)
    // {
    // printf("地图版本不匹配 | 设置： %d 读取：%d\n", c_iVersionMap, l_iVersion);
    // l_inFile.close();
    // return false;
    // }
    // todo:版本为1时返回false，但实际可以按按照i版本1的读取方式读取
    // todo:按照不同版本写不同的读取函数

    //预留100-8
    char l_cReserve[100];
    l_inFile.read((char*)&l_cReserve, 92);  // 上面先读了8个字节，继续读92个，共100字节

    st_FeatureInfo l_sFeatureInfo;
    for (int m = 0; m < l_stHeader.m_iTypeNum; m++)
    {
        int l_iIndex = 0;
        l_inFile.read((char*)&l_cFld[0], (4 * 4));  // 4*int
        memcpy(&l_sFeatureInfo.m_iFeatureType, &l_cFld[l_iIndex], 4);
        memcpy(&l_sFeatureInfo.m_iPntNum, &l_cFld[l_iIndex + 4], 4);
        memcpy(&l_sFeatureInfo.m_iFieldNum, &l_cFld[l_iIndex + 8], 4);
        memcpy(&l_sFeatureInfo.m_iByteNum, &l_cFld[l_iIndex + 12], 4);
        l_stHeader.m_vFeatureInfo.push_back(l_sFeatureInfo);

        //预留100
        l_inFile.read((char*)&l_cReserve, 100);
    }

    // 判断第1个类型是不是Path
    bool l_bHavePathMap = false;
    if (!l_stHeader.m_vFeatureInfo[0].m_iFeatureType)  // path的m_iFeatureType为0
        l_bHavePathMap = true;
    // 循环结构体，根据m_iFeatureType和点数依次读取
    for (int i = 0; i < l_stHeader.m_iTypeNum; i++)
    {
        int l_iOffset = 0;
        // path 和 feature 满足判断
        if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 200)
        {
            // 写入时路径和特征点云每个点云占用m_iByteNum+m_iFeatureType + 1个字节
            l_iOffset = l_stHeader.m_vFeatureInfo[i].m_iByteNum
                        + l_stHeader.m_vFeatureInfo[i].m_iFeatureType + 1;
            if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType > (int)p_feature.size())
            {
                printf("l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 200 , 读取地图不匹配\n");
                delete[] l_cFld;  //不匹配则释放内存
                l_inFile.close();
                return false;
            }
        }
        else
        {
            // 写入时可视化点云每个点云占用m_iByteNum个字节
            l_iOffset = l_stHeader.m_vFeatureInfo[i].m_iByteNum;
            if (255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType >= (int)p_pcVisible.size())
            {
                printf("255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType >= (int)p_pcVisible.size()，读取地图不匹配\n");
                delete[] l_cFld;
                l_inFile.close();
                return false;
            }
        }

        // path和其他特征区分
        // todo:应该根据m_iFeatureType去判断，不然pos只能在第一组，虽然执行没问题，但是多个参数l_bHavePathMap

        if (l_bHavePathMap && !i)  // 有路径点且是第一组
        {
            p_pPos->width = l_stHeader.m_vFeatureInfo[i].m_iPntNum;
            p_pPos->height = 1;
            p_pPos->points.resize(p_pPos->width);
        }
        else if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType > 0
                 && l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 200)
        {
            if ((l_stHeader.m_vFeatureInfo[i].m_iFeatureType >= (int)p_feature.size() + 1))
            {
                printf("type over size haveType[%d]: %d\n",
                       i,
                       l_stHeader.m_vFeatureInfo[i].m_iFeatureType);
                continue;
            }
            p_feature[l_stHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->width =
                l_stHeader.m_vFeatureInfo[i].m_iPntNum;
            // 用（m_iFeatureType - 1）去控制给哪种特征点云赋值
            p_feature[l_stHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->height = 1;
            p_feature[l_stHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->points.resize(
                p_feature[l_stHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->width);
        }
        else if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType > 200
                 && l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 255)
        {
            if (255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType >= (int)p_pcVisible.size())
            {
                printf("type over size haveType[%d]: %d\n",
                       i,
                       l_stHeader.m_vFeatureInfo[i].m_iFeatureType);
                continue;
            }
            p_pcVisible[255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType]->width =
                l_stHeader.m_vFeatureInfo[i].m_iPntNum;
            p_pcVisible[255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType]->height = 1;
            p_pcVisible[255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType]->points.resize(
                p_pcVisible[255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType]->width);
        }

        //点数*每种点占用字节数，用于判断有多个[1024*1024]
        long l_iPntFldSize = l_stHeader.m_vFeatureInfo[i].m_iPntNum;
        l_iPntFldSize*= l_iOffset;

        if (l_iPntFldSize <= FIEL_SIZE_RW)
        {
            //读1MB内数据
            l_inFile.read((char*)&l_cFld[0], l_iPntFldSize);
            int l_iFldIdx = 0;
            for (int j = 0; j < l_stHeader.m_vFeatureInfo[i].m_iPntNum; j++)
            {
                // todo：这里疯狂判断，但此时已经知道是哪种点云，上面判断过一次
                if (l_bHavePathMap && !i)
                {
                    int l_iLen = sizeof(PointPos);
                    if (l_iLen > l_stHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_stHeader.m_vFeatureInfo[i].m_iByteNum;
                    }

                    memcpy(&p_pPos->points[j], &l_cFld[l_iFldIdx], l_iLen);
                }
                else if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType > 0
                         && l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 200)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_stHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_stHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(&p_feature[l_stHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->points[j],
                           &l_cFld[l_iFldIdx],
                           l_iLen);
                }
                else if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType > 200
                         && l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 255)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_stHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_stHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(
                        &p_pcVisible[255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType]->points[j],
                        &l_cFld[l_iFldIdx],
                        l_iLen);
                }

                l_iFldIdx += l_iOffset;
            }
        }
        else
        {
            int l_iReadOffset =
                (FIEL_SIZE_RW / l_iOffset) * l_iOffset;  // 1024*1024的内存里能放l_iReadOffset数据
            int l_iMult =
                l_iPntFldSize / l_iReadOffset;  // 所有点云分l_iMult+1次读，l_iMult MB+剩下的
            int l_iRem = l_iPntFldSize % l_iReadOffset;  // 最后一次读l_iRem个数据
            //读数据
            l_inFile.read((char*)&l_cFld[0], l_iReadOffset);
            int l_iFldIdx = 0;
            int l_iN = 1;
            for (int j = 0; j < l_stHeader.m_vFeatureInfo[i].m_iPntNum; j++)
            {
                if (l_bHavePathMap && !i)
                {
                    int l_iLen = sizeof(PointPos);
                    if (l_iLen > l_stHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_stHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(&p_pPos->points[j], &l_cFld[l_iFldIdx], l_iLen);
                }
                else if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType > 0
                         && l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 200)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_stHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_stHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(&p_feature[l_stHeader.m_vFeatureInfo[i].m_iFeatureType - 1]->points[j],
                           &l_cFld[l_iFldIdx],
                           l_iLen);
                }
                else if (l_stHeader.m_vFeatureInfo[i].m_iFeatureType > 200
                         && l_stHeader.m_vFeatureInfo[i].m_iFeatureType <= 255)
                {
                    int l_iLen = sizeof(PointT);
                    if (l_iLen > l_stHeader.m_vFeatureInfo[i].m_iByteNum)
                    {
                        l_iLen = l_stHeader.m_vFeatureInfo[i].m_iByteNum;
                    }
                    memcpy(
                        &p_pcVisible[255 - l_stHeader.m_vFeatureInfo[i].m_iFeatureType]->points[j],
                        &l_cFld[l_iFldIdx],
                        l_iLen);
                }

                l_iFldIdx += l_iOffset;  // 地址往后移动l_iOffset
                    // 读到某组（1024*1024）结尾 且 不是倒数第二组，则重新读一组数据
                if (l_iFldIdx == l_iReadOffset && l_iN != l_iMult)
                {
                    l_inFile.read((char*)&l_cFld[0], l_iReadOffset);
                    l_iFldIdx = 0;
                    l_iN++;  //当前读完了l_iN组数据
                }
                else if (l_iFldIdx == l_iReadOffset && l_iN == l_iMult)
                {
                    // 读最后一组剩余l_iRem个数据
                    l_inFile.read((char*)&l_cFld[0], l_iRem);
                    l_iFldIdx = 0;
                }
            }
        }
    }

    l_inFile.close();

    delete[] l_cFld;
    return true;
}