/**
 * @file iVox_map.hpp
 * <AUTHOR>
 * @brief
 * @version 1.0
 * @date 2022-08-26
 */

#include "../iVox_map.h"

namespace wj_slam {
template <int Dim, typename PointType> IVox<Dim, PointType>::IVox()
{
    c_vVerify_ = {KeyType(0, 0, 0),
                  KeyType(0, 0, 1),
                  KeyType(0, 0, -1),
                  KeyType(0, 1, 0),
                  KeyType(0, -1, 0),
                  KeyType(1, 0, 0),
                  KeyType(-1, 0, 0)};
}

template <int Dim, typename PointType> IVox<Dim, PointType>::~IVox()
{
    for (auto grid : c_lGridcache_)
    {
        if (grid.second)
        {
            delete grid.second;
        }
    }
}

template <int Dim, typename PointType> void IVox<Dim, PointType>::start()
{
    clearForNewGrid_();
}
template <int Dim, typename PointType> void IVox<Dim, PointType>::gridInit()
{
    c_sGridparam_.m_fInv_OuterResolu = 1.0 / c_sGridparam_.m_fOuterResolu;
    for (size_t idx = 0; idx < c_sGridparam_.m_iCapacity; idx++)
    {
        NodeType* l_node = new NodeType(c_sGridparam_.m_fOuterResolu, c_sGridparam_.m_fInnerResolu);
        c_lGridcache_.emplace_back(KeyType::Ones(), l_node);
    }
    generateNearbyGrids_();
}

template <int Dim, typename PointType>
bool IVox<Dim, PointType>::getClosestGridPoint(const PointType& pt,
                                               PointGrid& closest_pt,
                                               int max_num,
                                               int p_igetModel)
{
    // closest_pt->clear();
    closest_pt->reserve(max_num);
    int num = 0;
    auto key = Pos2Grid_(ToEigen<float, Dim>(pt));
    PointType l_pt;
    for (const KeyType& delta : c_vNearbygrid_)
    {
        auto dkey = key + delta;
        auto iter = c_mGridmap_.find(dkey);
        if (iter != c_mGridmap_.end())
        {
            l_pt = pt;
            l_pt.x += delta[0] * c_sGridparam_.m_fOuterResolu;
            l_pt.y += delta[1] * c_sGridparam_.m_fOuterResolu;
            l_pt.z += delta[2] * c_sGridparam_.m_fOuterResolu;

            int l_iIndex = -1;
            if (iter->second->second->checkGrid(l_pt, l_iIndex))
            {
                int temp = iter->second->second->getGridPoint(l_pt, closest_pt, p_igetModel);
                num += temp;
                if (num >= max_num)
                {
                    break;
                }
            }
        }
    }

    if (closest_pt->empty())
    {
        return false;
    }
    else
    {
        return true;
    }
}

template <int Dim, typename PointType>
bool IVox<Dim, PointType>::getOptimizePoint(const PointType& pt,
                                            PointGrid& closest_pt,
                                            bool p_bLabel)
{
    // closest_pt->clear();
    // closest_pt->reserve(max_num);
    auto key = Pos2Grid_(ToEigen<float, Dim>(pt));
    PointType l_pt;
    for (const KeyType& delta : c_vNearbygrid_)
    {
        auto dkey = key + delta;
        auto iter = c_mGridmap_.find(dkey);
        if (iter != c_mGridmap_.end())
        {
            l_pt = pt;
            l_pt.x += delta[0] * c_sGridparam_.m_fOuterResolu;
            l_pt.y += delta[1] * c_sGridparam_.m_fOuterResolu;
            l_pt.z += delta[2] * c_sGridparam_.m_fOuterResolu;

            int l_iIndex = -1;
            if (iter->second->second->checkGrid(l_pt, l_iIndex))
            {
                int temp = iter->second->second->getGridPoint(l_pt, closest_pt, p_bLabel);
            }
        }
    }

    if (closest_pt->empty())
    {
        return false;
    }
    else
    {
        return true;
    }
}
template <int Dim, typename PointType>
bool IVox<Dim, PointType>::getClosestGridPoint(
    const PointType& pt,
    std::list<typename std::vector<PointType>*>& p_lVPoint,
    int max_num)
{
    p_lVPoint.clear();
    int num = 0;
    auto key = Pos2Grid_(ToEigen<float, Dim>(pt));
    PointType l_pt = pt;
    for (const KeyType& delta : c_vNearbygrid_)
    {
        auto dkey = key + delta;
        auto iter = c_mGridmap_.find(dkey);
        l_pt.x += delta[0] * c_sGridparam_.m_fOuterResolu;
        l_pt.y += delta[1] * c_sGridparam_.m_fOuterResolu;
        l_pt.z += delta[2] * c_sGridparam_.m_fOuterResolu;

        if (iter != c_mGridmap_.end())
        {
            int temp = iter->second->getPointFromGrid(p_lVPoint);
            num += temp;
            if (num >= max_num)
            {
                break;
            }
        }
    }

    if (p_lVPoint.empty())
    {
        return false;
    }
    else
    {
        return true;
    }
}

template <int Dim, typename PointType>
bool IVox<Dim, PointType>::getGridAllPoint(const PointType& pt, PointGrid& closest_pt, bool p_bMode)
{
    auto key = Pos2Grid_(ToEigen<float, Dim>(pt));
    PointType l_pt;
    for (const KeyType& delta : c_vVerify_)
    {
        auto dkey = key + delta;
        auto iter = c_mGridmap_.find(dkey);
        if (iter != c_mGridmap_.end())
        {
            l_pt = pt;
            l_pt.x += delta[0] * c_sGridparam_.m_fOuterResolu;
            l_pt.y += delta[1] * c_sGridparam_.m_fOuterResolu;
            l_pt.z += delta[2] * c_sGridparam_.m_fOuterResolu;

            int l_iIndex = -1;
            if (iter->second->second->checkGrid(l_pt, l_iIndex))
            {
                iter->second->second->getGridAllPoint(pt, closest_pt, p_bMode);
            }
        }
    }
    if (closest_pt->empty())
    {
        return false;
    }
    else
    {
        return true;
    }
}

template <int Dim, typename PointType> void IVox<Dim, PointType>::generateNearbyGrids_()
{
    switch (c_sGridparam_.m_iGridNearby_Type)
    {
        case NearbyType::CENTER: c_vNearbygrid_.emplace_back(KeyType::Zero()); break;
        case NearbyType::NEARBY6:
            c_vNearbygrid_ = {KeyType(0, 0, 0),
                              KeyType(0, 0, 1),
                              KeyType(0, 0, -1),
                              KeyType(0, 1, 0),
                              KeyType(0, -1, 0),
                              KeyType(1, 0, 0),
                              KeyType(-1, 0, 0)};
            break;
        case NearbyType::NEARBY18:
            c_vNearbygrid_ = {KeyType(0, 0, 0),
                              KeyType(0, 0, 1),
                              KeyType(0, 0, -1),
                              KeyType(0, 1, 0),
                              KeyType(0, -1, 0),
                              KeyType(1, 0, 0),
                              KeyType(-1, 0, 0),
                              KeyType(1, 1, 0),
                              KeyType(-1, 1, 0),
                              KeyType(1, -1, 0),
                              KeyType(-1, -1, 0),
                              KeyType(1, 0, 1),
                              KeyType(-1, 0, 1),
                              KeyType(1, 0, -1),
                              KeyType(-1, 0, -1),
                              KeyType(0, 1, 1),
                              KeyType(0, -1, 1),
                              KeyType(0, 1, -1),
                              KeyType(0, -1, -1)};
            break;
        case NearbyType::NEARBY26:
            c_vNearbygrid_ = {
                KeyType(0, 0, 0),   KeyType(0, 0, 1),   KeyType(0, 0, -1),  KeyType(0, 1, 0),
                KeyType(0, -1, 0),  KeyType(1, 0, 0),   KeyType(-1, 0, 0),  KeyType(1, 1, 0),
                KeyType(-1, 1, 0),  KeyType(1, -1, 0),  KeyType(-1, -1, 0), KeyType(1, 0, 1),
                KeyType(-1, 0, 1),  KeyType(1, 0, -1),  KeyType(-1, 0, -1), KeyType(0, 1, 1),
                KeyType(0, -1, 1),  KeyType(0, 1, -1),  KeyType(0, -1, -1), KeyType(1, 1, 1),
                KeyType(-1, 1, 1),  KeyType(1, -1, 1),  KeyType(1, 1, -1),  KeyType(-1, -1, 1),
                KeyType(-1, 1, -1), KeyType(1, -1, -1), KeyType(-1, -1, -1)};
            break;
        default: break;
    }
}

template <int Dim, typename PointType>
void IVox<Dim, PointType>::buildGridMap(PointVector& p_surPoint,
                                        PointVector& p_corPoint,
                                        int p_iMode)
{
    clearForNewGrid_();
    if (p_iMode != MapModel::INITBUILDMAP && p_iMode != MapModel::CONTBUILDMAP
        && p_iMode != MapModel::LOCATMAP && p_iMode != MapModel::UPDATEMAP)
    {
        std::cout
            << "please input workmode = 1 or 2 or 3.(1：initbuildmap，2：contbuildmap, 3:locatmap)"
            << std::endl;
        return;
    }
    if (p_iMode == MapModel::INITBUILDMAP || p_iMode == MapModel::CONTBUILDMAP
        || p_iMode == MapModel::UPDATEMAP)
    {
        c_lNoLabel.clear();
        c_vNoSign.clear();
        c_lAddZero.clear();
        c_vaddPoint.clear();
        c_iNum = -1;
    }
    c_iMode_ = p_iMode;
    addPointToGrid_(p_surPoint, false);
    addPointToGrid_(p_corPoint, true);
}

template <int Dim, typename PointType>
void IVox<Dim, PointType>::addGridMap(PointVector& p_surPoint, PointVector& p_corPoint, int p_iMode)
{
    if (p_iMode != MapModel::INITBUILDMAP && p_iMode != MapModel::CONTBUILDMAP
        && p_iMode != MapModel::LOCATMAP && p_iMode != MapModel::UPDATEMAP)
    {
        std::cout
            << "please input workmode = 1 or 2 or 3.(1：initbuildmap，2：contbuildmap, 3:locatmap)"
            << std::endl;
        return;
    }
    // if (p_iMode == MapModel::INITBUILDMAP || p_iMode == MapModel::CONTBUILDMAP
    //     || p_iMode == MapModel::UPDATEMAP)
    // {
    //     c_lNoLabel.clear();
    //     c_vNoSign.clear();
    //     c_lAddZero.clear();
    //     c_vaddPoint.clear();
    //     c_iNum = -1;
    // }
    c_iMode_ = p_iMode;
    addPointToGrid_(p_surPoint, false);
    addPointToGrid_(p_corPoint, true);
}

template <int Dim, typename PointType>
void IVox<Dim, PointType>::addPointToGrid_(PointVector& p_pointToAdd, bool p_bCorInit)
{
    if (p_pointToAdd.empty())
    {
        std::cout << "grid add point is empty" << std::endl;
        return;
    }
    for (size_t id = 0; id < p_pointToAdd.size(); id++)
    {
        if (p_bCorInit)
            p_pointToAdd[id].v = -3;
        c_iNum++;
        KeyType key = Pos2Grid_(ToEigen<float, Dim>(p_pointToAdd[id]));
        auto iter = c_mGridmap_.find(key);
        if (iter == c_mGridmap_.end())
        {
            creatNewGrid_(key, p_pointToAdd[id]);
        }
        else
        {
            int l_iIndex = -1;
            if (iter->second->second->checkGrid(p_pointToAdd[id], l_iIndex))
            {
                c_lGridcache_.splice(c_lGridcache_.begin(), c_lGridcache_, iter->second);
                if (iter->second->second->insertOldPoint(p_pointToAdd[id], l_iIndex))
                {
                    // if (c_iMode_ == MapModel::INITBUILDMAP || c_iMode_ == MapModel::CONTBUILDMAP
                    //     || c_iMode_ == MapModel::UPDATEMAP)
                    // {
                    //     c_lAddZero.emplace_back(key);
                    //     if (p_pointToAdd[id].v == 0)
                    //     {
                    //         c_lNoLabel.emplace_back(key, p_pointToAdd[id]);
                    //         c_vNoSign.emplace_back(c_iNum, p_pointToAdd[id].v);
                    //     }
                    //     else
                    //     {
                    //         //不将拟合平面未通过的、角点添加至地图中
                    //         if (p_pointToAdd[id].v != 3 && p_pointToAdd[id].v != -3)
                    //             c_vaddPoint.emplace_back(p_pointToAdd[id]);
                    //     }
                    // }
                }
            }
            else
            {
                creatNewGrid_(key, p_pointToAdd[id]);
            }
        }
    }
}

template <int Dim, typename PointType>
void IVox<Dim, PointType>::creatNewGrid_(KeyType p_key, const PointType& p_pt)
{
    c_lGridcache_.back().first = p_key;
    c_lGridcache_.back().second->clearIVoxNode();
    c_lGridcache_.back().second->calcuGridEdge(p_pt, c_sGridparam_.m_fOuterResolu);
    if (c_lGridcache_.back().second->insertNewPoint(p_pt))
    {
        //记录每次新增栅格有哪些，用于再次将0标签尽可能赋予1或-1，
        //单帧添加之后，遍历c_lAddZero，清除0标签点(后续将未设置1或-1标签的0标签挪到-2标签中)
        // if (c_iMode_ == MapModel::INITBUILDMAP || c_iMode_ == MapModel::CONTBUILDMAP
        //     || c_iMode_ == MapModel::UPDATEMAP)
        // {
        //     c_lAddZero.emplace_back(p_key);
        //     if (p_pt.v == 0)
        //     {
        //         c_lNoLabel.emplace_back(p_key, p_pt);
        //         c_vNoSign.emplace_back(c_iNum, p_pt.v);
        //     }
        //     else
        //     {
        //         //将除了0标签外的点取出来，作为单次新增的地图点显示
        //         if (p_pt.v != 3 && p_pt.v != -3)
        //             c_vaddPoint.emplace_back(p_pt);
        //     }
        // }
    }
    c_mGridmap_.insert({p_key, std::next(c_lGridcache_.end(), -1)});
    c_lGridcache_.splice(c_lGridcache_.begin(), c_lGridcache_, std::next(c_lGridcache_.end(), -1));
}

template <int Dim, typename PointType> void IVox<Dim, PointType>::clearCache()
{
    if (c_lGridcache_.size() >= c_sGridparam_.m_iCapacity)
    {
        c_lGridcache_.back().second->second.clearIVoxNode();
        c_lGridcache_.pop_back();
    }
}
template <int Dim, typename PointType>
void IVox<Dim, PointType>::movePoint(std::pair<KeyType, PointType>& p_pMovePt)
{
    auto iter = c_mGridmap_.find(p_pMovePt.first);
    if (iter != c_mGridmap_.end())
    {
        int l_iIndex = -1;
        if (iter->second->second->checkGrid(p_pMovePt.second, l_iIndex))
            if (iter->second->second->insertOldPoint(p_pMovePt.second, l_iIndex))
                c_vaddPoint.emplace_back(p_pMovePt.second);
    }
}

template <int Dim, typename PointType> void IVox<Dim, PointType>::setInnerResolu(float p_fInnerRes)
{
    c_sGridparam_.m_fInnerResolu = p_fInnerRes;
}
template <int Dim, typename PointType> void IVox<Dim, PointType>::setOuterResolu(float p_fOuterRes)
{
    c_sGridparam_.m_fOuterResolu = p_fOuterRes;
}
template <int Dim, typename PointType>
void IVox<Dim, PointType>::setSearchPointNum(int p_iSearchNum)
{
    c_sGridparam_.m_iMatchPoints = p_iSearchNum;
}
template <int Dim, typename PointType>
void IVox<Dim, PointType>::setGridCapacity(std::size_t p_iCapacity)
{
    c_sGridparam_.m_iCapacity = p_iCapacity;
}
template <int Dim, typename PointType> void IVox<Dim, PointType>::setGridNearbyType(int p_iType)
{
    c_sGridparam_.m_iGridNearby_Type = p_iType;
}
template <int Dim, typename PointType>
Eigen::Matrix<int, Dim, 1> IVox<Dim, PointType>::Pos2Grid_(const IVox::PtType& pt) const
{
    return (pt * c_sGridparam_.m_fInv_OuterResolu).array().floor().template cast<int>();
}

template <int Dim, typename PointType> void IVox<Dim, PointType>::clearForNewGrid_()
{
    if (c_mGridmap_.empty())
        return;
    for (auto& id : c_mGridmap_)
    {
        id.second->second->clearIVoxNode();
    }
}

template <int Dim, typename PointType> void IVox<Dim, PointType>::clearGrid()
{
    if (c_lAddZero.empty())
        return;
    for (auto& id : c_lAddZero)
    {
        auto iter = c_mGridmap_.find(id);
        if (iter != c_mGridmap_.end())
        {
            iter->second->second->clearZeroVector();
        }
    }
}

template <int Dim, typename PointType> void IVox<Dim, PointType>::getiVoxMap(PointGrid& map_pt)
{
    if (c_mGridmap_.empty())
        return;
    for (const auto& id : c_mGridmap_)
    {
        id.second.second->getMapGrid(map_pt);
    }
    // if (c_vaddPoint.empty())
    //     return;

    // for (const auto& pt : c_vaddPoint)
    // {
    //     map_pt->push_back(pt);
    // }
}

template <int Dim, typename PointType>
std::vector<float> IVox<Dim, PointType>::StatGridPoints() const
{
    int num = c_lGridcache_.size(), valid_num = 0, max = 0, min = 100000000;
    int sum = 0, sum_square = 0;
    for (auto& it : c_lGridcache_)
    {
        int s = it.second.Size();
        valid_num += s > 0;
        max = s > max ? s : max;
        min = s < min ? s : min;
        sum += s;
        sum_square += s * s;
    }
    float ave = float(sum) / num;
    float stddev = num > 1 ? sqrt((float(sum_square) - num * ave * ave) / (num - 1)) : 0;
    return std::vector<float>{valid_num, ave, max, min, stddev};
}

template <int Dim, typename PointType>
void IVox<Dim, PointType>::getGridCentoidPoints(PointGrid p_pCentPt)
{
    if (c_mGridmap_.empty())
        return;
    for (auto& id : c_mGridmap_)
    {
        if (!id.second->second->c_vfrontPts_.empty())
        {
            id.second->second->getCentroidGrid(p_pCentPt);
        }
    }
}

template <int Dim, typename PointType>
void IVox<Dim, PointType>::getGridRandomPoints(PointGrid p_pRandPt)
{
    if (c_mGridmap_.empty())
        return;
    for (auto& id : c_mGridmap_)
    {
        if (!id.second->second->c_vfrontPts_.empty())
        {
            id.second->second->getRandomPoint(p_pRandPt);
        }
    }
}

template <int Dim, typename PointType>
void IVox<Dim, PointType>::getGridBaryCenterPoints(PointGrid p_pBaryPt)
{
    if (c_mGridmap_.empty())
        return;
    for (auto& id : c_mGridmap_)
    {
        if (!id.second->second->c_vfrontPts_.empty())
        {
            id.second->second->getBaryCenterGrid(p_pBaryPt);
        }
    }
}

}  // namespace wj_slam
#define WJSLAM_IVox(PointT, Dim) template class wj_slam::IVox<PointT, Dim>;