/**
 * @file Submap.h
 * <AUTHOR>
 * @brief 子图类
 * @version 1.0
 * @date 2023-08-09
 * @copyright Copyright (c)2023 Vanjee
 */

#pragma once

// local
#include "KeyFrameMap.h"
#include "SubmapBox.h"
#include "impl/SubmapUpdateCheck.hpp"

using namespace wj_slam;

/**
 *            工作模式与子图相关各参数对应表
 * ---------------------------------------------------
 * |  工作模式  | 初始建图 | 连续建图 | 连续定位 | 更新建图 |
 * ---------------------------------------------------
 * | ID模式使能 |   否    |   否    |   是    |   是    |
 * ---------------------------------------------------
 * |  运动方向  |  可设   |   可设   |   是    |   是    |
 * ---------------------------------------------------
 * |  开启回环  |  可设   |   可设   |   否    |   否    |
 * ---------------------------------------------------
 * |  读取地图  |   否    |   是    |   是    |   是    |
 * ---------------------------------------------------
 *
 * >> 子图使用步骤：
 * 1. 调用setPose函数、setScanRange函数设置当前位姿及扫描范围;
 * 2. 调用updateSubmap函数在适当时机触发子图更新;
 * 3. 调用transformPointer等函数获取对应类型子图；
 *
 * >> 子图相关说明：
 * 1. 子图主要耗时在于关键帧拷贝合并为子图阶段，该时间取决于特征点个数，
 * 经测试，通常耗时在20ms左右；定位模式下若导入之地图未进行采样，其特征
 * 点数较多，子图耗时可能在几十至几百毫秒不等，可能会影响子图实时性。
 *
 */

class Submap : public KfBase {
  public:
    /**
     * @brief 子图点云类型
     *
     */
    enum SubmapPcType {
        SUBMAP_3D = 0,           /**< 3D子图点云 */
        SUBMAP_2D = 1,           /**< 2D子图点云 */
        INCREMENT_SUBMAP_3D = 2, /**< 3D增量子图点云 */
        INCREMENT_SUBMAP_2D = 3, /**< 2D增量子图点云 */
    };

    /**
     * @brief 构造函数
     * @param p_keyFrameMap 关键帧地图对象指针
     *
     */
    Submap(boost::shared_ptr<KeyFrameMap> p_keyFrameMap);

    /**
     * @brief 析构函数
     *
     */
    ~Submap();

    /**
     * @brief 触发检验子图更新
     * @param p_bIsInit 是否初始化(首帧)
     *
     */
    void updateSubmap(bool p_bIsInit = false);

    /**
     * @brief 获取子图指针
     * @param p_pairSubMap        待获取子图
     * @param p_vKFId             回环关键帧索引
     * @param p_iNearbyOldstInd   附近框最旧索引
     * @param p_bIstrans2d        是否使用2D模式
     * @code
     *
     * @endcode
     * @return [true] \n
     * [子图已更新]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [子图未更新]
     *
     */
    bool transformPointer(type::KeyFramePtr& p_pairSubMap,
                          std::vector<int>& p_vKFId,
                          bool p_bIstrans2d = false);

    /**
     * @brief 获取2D子图指针
     * @param p_pairSubMap2d 待获取2D子图
     * @code
     *
     * @endcode
     * @return [true] \n
     * [子图已更新]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [子图未更新]
     *
     */
    bool transform2dPointer(type::KeyFramePtr& p_pairSubMap2d);

    /**
     * @brief 获取增量子图指针
     * @param p_pairWholeSubMap 完整地图
     * @param p_pairAddSubMap   待获取增量子图
     * @param p_vKFId           回环帧ID
     * @param p_iNearbyOldstInd 附近框最旧索引
     * @param p_bIstrans2d      是否使用2D模式
     * @code
     *
     * @endcode
     * @return [true] \n
     * [子图已更新]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [子图未更新]
     *
     */
    bool transformAddPointer(type::KeyFramePtr& p_pairWholeSubMap,
                             type::KeyFramePtr& p_pairAddSubMap,
                             std::vector<int>& p_vKFId,
                             bool p_bIstrans2d = false);

    /**
     * @brief 获取2D增量子图指针
     * @param l_pairSubMap2d
     * @code
     *
     * @endcode
     * @return [true] \n
     * [子图已更新]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [子图未更新]
     *
     */
    bool transformAdd2dPointer(type::KeyFramePtr& l_pairSubMap2d);

    /**
     * @brief 设置回环使能标志
     * @param p_bIsopen 使能标志
     *
     */
    void setEnableLoop(bool p_bIsopen);

    /**
     * @brief 设置扫描框范围
     * @param p_f32MinX 扫描框最小X值
     * @param p_f32MinY 扫描框最小Y值
     * @param p_f32MaxX 扫描框最大X值
     * @param p_f32MaxY 扫描框最大Y值
     *
     */
    void setScanRange(float p_f32MinX, float p_f32MinY, float p_f32MaxX, float p_f32MaxY);

    /**
     * @brief 设置当前位姿
     * @param p_tCurrPose 当前位姿平移
     * @param p_qCurrPose 当前位姿旋转
     *
     */
    void setPose(Eigen::Vector3d p_tCurrPose, Eigen::Quaterniond p_qCurrPose);

    /**
     * @brief 设置2D使能
     * @param p_bFlag 2D使能标志
     *
     */
    void setEnable2d(bool p_bFlag);

    /**
     * @brief 是否启用2D模式
     * @code
     *
     * @endcode
     * @return [true] \n
     * [启用]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未启用]
     *
     */
    bool isEnable2d();

  private:
    /**
     * @brief 子图线程函数
     *
     */
    void run_();

    /**
     * @brief 获取子图
     * @code
     *
     * @endcode
     * @return [true] \n
     * [子图已更新]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [子图未更新]
     *
     */
    bool getSubmap_();

    /**
     * @brief 判断子图关键帧索引是否改变
     * @param p_vRawInds 原关键帧索引
     * @param p_vNewInds 新关键帧索引
     * @code
     *
     * @endcode
     * @return [true] \n
     * [改变]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未改变]
     *
     */
    bool isChangeKFInd_(const std::vector<int>& p_vRawInds, const std::vector<int>& p_vNewInds);

    /**
     * @brief 位姿下采样
     * @param p_pcIn    输入点云
     * @param p_vPcInds 输出采样后点索引
     *
     */
    void downSizeFilterPose_(const type::ConstPosePcPtr p_pcIn, std::vector<int>& p_vPcInds);

    /**
     * @brief 生成2D子图
     * @param p_pairSubMap3d 输入3D子图
     *
     */
    void generateSubmap2d_(const type::ConstKeyFramePtr p_pairSubMap3d);

    /**
     * @brief 生成2D增量子图
     * @param p_pairAddSubMap3d 输入3D增量子图
     *
     */
    void generateAddSubmap2d_(const type::ConstKeyFramePtr p_pairAddSubMap3d);

    /**
     * @brief 获取子图指针
     * @param l_ptrType 待获取子图类型
     * @param p_bCopy   是否拷贝子图
     * @code
     *
     * @endcode
     * @return [KeyFramePtr] \n
     * [子图指针]
     *
     */
    type::KeyFramePtr getSubmapPtr_(SubmapPcType l_ptrType, bool p_bCopy = true);

    /**
     * @brief 设置子图指针
     * @param p_pairSubmap 输入子图
     * @param l_ptrType    待设置子图类型
     * @param p_bCopy      是否拷贝子图
     *
     */
    void setSubmapPc_(const type::KeyFramePtr p_pairSubmap,
                      SubmapPcType l_ptrType,
                      bool p_bCopy = false);

  private:
    bool c_bEnable2d_;                             /**< 2D模式使能 */
    bool c_bIsUpdateSubMap_;                       /**< 是否更新子图 */
    int c_iOptModel_;                              /**< 优化模式：KD/iVox */
    Eigen::Vector3d c_tMapCurr_;                   /**< 当前平移 */
    Eigen::Quaterniond c_qMapCurr_;                /**< 当前旋转 */
    std::vector<int> c_vSubmapKfInds_;             /**< 子图关键帧索引 */
    std::vector<int> c_vSubmapDsKfInds_;           /**< 下采样后子图关键帧索引 */
    std::mutex c_pairSubmapMutex_;                 /**< 子图锁 */
    type::KeyFramePtr c_pairSubMap_;               /**< 子图指针 */
    type::KeyFramePtr c_pairSubMap2d_;             /**< 2D子图指针 */
    type::KeyFramePtr c_pairAddSubMap_;            /**< 增量子图指针 */
    type::KeyFramePtr c_pairAddSubMap2d_;          /**< 2D增量子图指针 */
    SubmapBox c_submapBox_;                        /**< 子图框对象 */
    SubmapUpdateCheck c_submapUpdateCheck_;        /**< 子图更新检验对象 */
    boost::shared_ptr<KeyFrameMap> c_keyFrameMap_; /**< 关键帧地图对象 */

    bool c_bIsRun_;                           /**< 线程运行标志 */
    boost::shared_ptr<std::thread> c_thread_; /**< 子图线程 */
    std::mutex c_thrMutex_;                   /**< 线程锁 */
    std::condition_variable c_thrCond_;       /**< 线程状态变量 */
};

#include "impl/Submap.hpp"