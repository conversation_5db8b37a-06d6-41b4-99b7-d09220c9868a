/**
 * @file KeyFrameMap.h
 * <AUTHOR>
 * @brief 关键帧地图类，根据关键帧位姿从全局地图中生成关键帧地图, 以及从关键帧地图中获取局部地图
 * @version 1.0
 * @date 2023-07-26
 * @copyright Copyright (c)2023 Vanjee
 */

#pragma once

// local
#include "impl/FilterFactory.hpp"
#include "impl/KfMapPair.hpp"

class KeyFrameMap : public KfMapPair {
  public:
    /**
     * @brief 构造函数
     *
     */
    KeyFrameMap();

    /**
     * @brief 析构函数
     *
     */
    virtual ~KeyFrameMap();

    /**
     * @brief 根据完整地图生成关键帧地图
     * @param p_pose        关键帧位姿
     * @param p_srcMap      完整地图
     * @param p_dKfRange    关键帧地图搜索范围
     *
     */
    void
    generateKfMap(type::PosePcPtr p_pose, const type::ConstKeyFramePtr p_srcMap, double p_dKfRange);

    /**
     * @brief 根据关键帧索引从关键帧地图中获取局部地图
     * @param p_vKfInds  输入关键帧索引
     * @param p_localMap 输出局部地图
     *
     */
    void getKfMaps(const std::vector<int>& p_vKfInds, type::KeyFramePtr& p_localMap);

    /**
     * @brief 获取ID模式标志
     * @code
     *
     * @endcode
     * @return [true] \n
     * [ID模式]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [非ID模式]
     *
     */
    bool getIdModel();

    /**
     * @brief 设置ID模式使能
     * @param p_bFlag 使能标志
     *
     */
    void setIdModel(bool p_bFlag);

    /**
     * @brief 获取特征采样使能状态
     * @code
     *
     * @endcode
     * @return [uint8_t] \n
     * [特征采样使能状态]
     *
     */
    uint8_t getFeatureDsEnabled();

    /**
     * @brief 设置特征采样使能
     * @param p_bCorn 角点采样使能标志
     * @param p_bSurf 面点采样使能标志
     * @param p_bMark 靶标采样使能标志
     * @param p_bCurb Curb采样使能标志
     *
     */
    void setFeatureDsEnabled(bool p_bCorn, bool p_bSurf, bool p_bMark, bool p_bCurb);

    /**
     * @brief 过滤关键帧地图
     * @param p_pairSubMap 待过滤关键帧地图
     * @param p_pcOut      过滤后关键帧地图
     *
     */
    void filterKfMap(const type::ConstKeyFramePtr p_pairSubMap, const type::KeyFramePtr p_pcOut);

  private:
    /**
     * @brief 根据完整地图填充关键帧索引
     * @param p_pcKfPose 关键帧位姿
     * @param p_srcMap   完整地图
     * @param p_dKfRange 关键帧搜索范围
     *
     */
    void fillKfIndsFromWholeMap_(const type::ConstPosePcPtr p_pcKfPose,
                                 const type::ConstKeyFramePtr p_srcMap,
                                 double p_dKfRange);

    /**
     * @brief 根据关键帧索引填充关键帧地图
     * @param p_srcMap
     *
     */
    void fillKfIndsToKFMaps_(const type::ConstKeyFramePtr p_srcMap);

    /**
     * @brief 根据关键帧索引从关键帧地图中获取角点位姿点云
     * @param p_vKfInds 关键帧索引
     * @param p_pcCorn  输出角点位姿点云
     *
     */
    void getCornPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcCorn);

    /**
     * @brief 根据关键帧索引从关键帧地图中获取面点位姿点云
     * @param p_vKfInds 关键帧索引
     * @param p_pcSurf  输出面点位姿点云
     *
     */
    void getSurfPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcSurf);

    /**
     * @brief 根据关键帧索引从关键帧地图中获取靶标点位姿点云
     * @param p_vKfInds 关键帧索引
     * @param p_pcMark  输出靶标点位姿点云
     *
     */
    void getMarkPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcMark);

    /**
     * @brief 根据关键帧索引从关键帧地图中获取Curb点位姿点云
     * @param p_vKfInds 关键帧索引
     * @param p_pcCurb  输出Curb点位姿点云
     *
     */
    void getCurbPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcCurb);

  private:
    bool c_bEnableIdModel_;       /**< ID模式使能 */
    uint8_t c_uiEnableFeatrueDs_; /**< 特征采样使能，每一bit表示一个特征使能状态 */

    std::vector<std::vector<int>> c_vKfCornIdInds_; /**< 关键帧Corn特征ID索引集合 */
    std::vector<std::vector<int>> c_vKfSurfIdInds_; /**< 关键帧Surf特征ID索引集合 */
    std::vector<std::vector<int>> c_vKfMarkIdInds_; /**< 关键帧Mark特征ID索引集合 */
    std::vector<std::vector<int>> c_vKfCurbIdInds_; /**< 关键帧Curb特征ID索引集合 */
    type::KeyFramePtr c_srcMap_;                    /**< 原始地图 */
};

#include "impl/KeyFrameMap.hpp"