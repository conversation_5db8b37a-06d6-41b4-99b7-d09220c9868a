/**
 * @file laserIO.h
 * <AUTHOR> Li
 * @brief 地图文件读写
 * @version 0.1
 * @date 2023-06-28
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#ifndef LASER_IO
#    define LASER_IO
#    include <pcl/point_cloud.h>
#    include <pcl/point_types.h>
#    include <vector>

#    define FIEL_SIZE_RW (1024 * 1024) /**< 预设大小1024×1024字节*/

/**
 * @brief 点云类型，没用到
 *
 */
enum FeatureType {
    Path = 0,
    Corn,
    Surf,
    Curb,
    Cloud3D = 255,
    Cloud2D = 254,
};  // 新增类型点云须顺序添加 并保持写入读取时 压入顺序一致
// todo：该类型没用到
using namespace std;
/**
 * @brief 点云地图文件读写类
 *
 * @tparam PointT 特征点云类型
 * @tparam PointPos 路径点云类型
 */
template <typename PointT, typename PointPos> class LaserIO {
  public:
    using Ptr = boost::shared_ptr<LaserIO<PointT, PointPos>>;
    using PointCloud = pcl::PointCloud<PointT>;
    using PointCloudPtr = typename pcl::PointCloud<PointT>::Ptr;
    using PointCloudPos = pcl::PointCloud<PointPos>;
    using PointCloudPosPtr = typename pcl::PointCloud<PointPos>::Ptr;
    static int c_iVersionMap; /**< 地图版本号*/

    /**
     * @brief 待写入点云信息
     *
     */
    struct st_FeatureInfo
    {
        st_FeatureInfo()
        {
            m_iPntNum = 0;
            m_iFieldNum = 0;
            m_iByteNum = 0;
            m_iFeatureType = 0;
        }
        int m_iPntNum;      /**< 点云点数*/
        int m_iFieldNum;    /**< 点云字段数*/
        int m_iByteNum;     /**< 点云类型占用字节数*/
        int m_iFeatureType; /**< 存储类型*/
    };
    /**
     * @brief 点云地图文件头信息
     *
     */
    struct st_Header
    {
        int m_iTypeNum;                             /**< 存储点云类型数量*/
        std::vector<st_FeatureInfo> m_vFeatureInfo; /**< 文件头信息集合*/
    };

  public:
    LaserIO();
    ~LaserIO();
    /**
     * @brief 写加密地图接口
     *
     * @param p_strBinFile 地图文件路径及文件名
     * @param p_pPos path路径点云
     * @param p_feature 特征点云集合
     * @param p_pcVisible 可视化点云集合
     * @return true 执行完毕返回true
     * @return false 文件打开失败返回false
     */
    bool writeBinary(std::string p_strBinFile,
                     PointCloudPosPtr p_pPos,
                     std::vector<PointCloudPtr>& p_feature,
                     std::vector<PointCloudPtr>& p_pcVisible);
    /**
     * @brief 读加密地图接口
     *
     * @param p_strBinFile 地图文件夹
     * @param p_pPos 待填充path路径点云
     * @param p_feature 待填充特征点云集合
     * @param p_pcVisible 待填充可视化点云集合
     * @return true
     * @return false 文件打开失败/版本不匹配
     */
    bool readBinary(const std::string p_strBinFile,
                    PointCloudPosPtr p_pPos,
                    std::vector<PointCloudPtr> p_feature,
                    std::vector<PointCloudPtr> p_pcVisible);
    /**
     * @brief 设置系统版本号
     *
     * @param p_iVersion
     */
    void setVersion(const int p_iVersion);
    /**
     * @brief 获取版本号信息
     *
     * @return int 版本号
     */
    int getVersion();

  private:
    /**
     * @brief 生成加密地图文件头信息
     *
     * @param p_stHeader 待填充地图头信息
     * @param p_pPos path点云指针
     * @param p_feature 特征点云集合
     * @param p_pcVisible 可视化点云集合
     * @return true 只返回true
     * @return false 不返回false
     */
    bool headerHandle_(st_Header& p_stHeader,
                       PointCloudPosPtr p_pPos,
                       std::vector<PointCloudPtr> p_feature,
                       std::vector<PointCloudPtr> p_pcVisible);
    /**
     * @brief 写入文件头信息至加密地图
     *
     * @param p_file 地图文件
     * @param p_stHeader 地图头信息
     */
    void writeHandle_(std::ofstream& p_file, st_Header& p_stHeader);
    /**
     * @brief 写入特征点云至加密地图
     *
     * @param p_file 地图文件
     * @param p_feature 特征点云集合
     * @param p_stHeader 地图头信息
     */
    void writeFeatureBinary_(std::ofstream& p_file,
                             std::vector<PointCloudPtr>& p_feature,
                             st_Header& p_stHeader);
    /**
     * @brief 写入可视化点云至加密地图
     *
     * @param p_file 地图文件
     * @param p_pcVisible 可视化点云集合
     * @param p_stHeader 地图头信息
     */
    void writeVisiblePcBinary_(std::ofstream& p_file,
                               std::vector<PointCloudPtr>& p_pcVisible,
                               st_Header& p_stHeader);
    /**
     * @brief 写入路径点云至加密地图
     *
     * @param p_file 地图文件
     * @param p_pPos 路径点云指针
     * @param p_stHeader 地图头信息
     */
    void writePathBinary_(std::ofstream& p_file, PointCloudPosPtr& p_pPos, st_Header& p_stHeader);
};

#    include "laserIO.hpp"
#endif