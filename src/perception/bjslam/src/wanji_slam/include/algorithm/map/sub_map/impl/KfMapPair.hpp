/**
 * @file KfMapPair.hpp
 * <AUTHOR>
 * @brief 关键帧地图及位姿集合类
 * @version 1.0
 * @date 2023-09-14
 * @copyright Copyright (c)2023 Vanjee
 */

#pragma once

// local
#include "common/common_ex.h"
#include "common/type/type_template.h"

struct MapPosePair
{
    MapPosePair() : map(nullptr) {}
    type::KeyFramePtr map;
    type::POSE pose;
};

class KfMapPair : public KfBase {
  public:
    KfMapPair();
    virtual ~KfMapPair();

    /**
     * @brief 获取关键帧位姿点云(拷贝)
     * @code
     *
     * @endcode
     * @return [PcPosePtr] \n
     * [关键帧位姿点云]
     *
     */
    type::PosePcPtr getKfPose();

    /**
     * @brief 获取关键帧队列大小
     * @code
     *
     * @endcode
     * @return [std::size_t] \n
     * [关键帧队列大小]
     *
     */
    std::size_t getKfSize();

    /**
     * @brief
     * @param p_kfMap
     * @param p_kfPose
     *
     */
    void push(KeyFramePtr p_kfMap, type::POSE p_kfPose);

    /**
     * @brief 获取队列尾部关键帧地图位姿对
     * @code
     *
     * @endcode
     * @return [KeyFramePtr] \n
     * [关键帧地图]
     *
     */
    MapPosePair back();

    /**
     * @brief 通过关键帧索引获取对应关键帧地图位姿对(拷贝)
     * @param p_iIn
     * @code
     *
     * @endcode
     * @return [MapPosePair] \n
     * [关键帧地图位姿对]
     *
     */
    MapPosePair getKfPairByInd(int p_iIn);

    /**
     * @brief 通过关键帧索引获取对应关键帧(拷贝)
     * @param p_iInd
     * @code
     *
     * @endcode
     * @return [KeyFramePtr] \n
     * [关键帧]
     *
     */
    type::KeyFramePtr getKfMapByInd(int p_iInd);

    /**
     * @brief 通过关键帧索引集合获取对应关键帧集合(拷贝)
     * @param p_vKfInds
     * @code
     *
     * @endcode
     * @return [KeyFramePtr] \n
     * [关键帧集合]
     *
     */
    type::KeyFramePtr getKfMapByInds(const std::vector<int>& p_vKfInds);

    /**
     * @brief 通过关键帧索引获取对应关键帧位姿(拷贝)
     * @param p_vKfInds
     * @code
     *
     * @endcode
     * @return [POSE] \n
     * [关键帧位姿]
     *
     */
    type::POSE getKfPoseByInd(int p_iInd);

    /**
     * @brief 根据关键帧索引从关键帧位姿中获取对应位姿点云
     * @param p_vKfInds 输入关键帧索引
     * @code
     *
     * @endcode
     * @return [PosePcPtr] \n
     * [对应位姿点云]
     *
     */
    type::PosePcPtr getKfPoseByInds(const std::vector<int>& p_vKfInds);

    /**
     * @brief 通过关键帧索引设置对应关键帧地图
     * @param p_iInd    关键帧索引
     * @param p_kfMap   待设置关键帧地图
     * @param p_bCopy   是否拷贝  #注意：非拷贝方式下，为防止外部持有指针，会将p_kfMap置nullptr。
     *
     */
    void setKfMapByInd(int p_iInd, type::KeyFramePtr& p_kfMap, bool p_bCopy = false);

    /**
     * @brief 通过关键帧索引设置对应关键帧位姿
     * @param p_iInd    关键帧索引
     * @param p_pcPose  待设置关键帧地位姿
     *
     */
    void setKfPoseByInd(int p_iInd, const type::POSE& p_pcPose);

  protected:
    std::vector<type::KeyFramePtr> c_vpairKFMaps_; /**< 关键帧地图 */
    type::PosePcPtr c_pcKfPoses_;                  /**< 关键帧位姿点云 */
    std::mutex c_kfMutex_;                         /**< 关键帧锁 */
};

KfMapPair::KfMapPair() : c_vpairKFMaps_(), c_pcKfPoses_(boost::make_shared<type::PosePc>()) {}

KfMapPair::~KfMapPair() {}

type::PosePcPtr KfMapPair::getKfPose()
{
    type::PosePcPtr l_kfPose = boost::make_shared<type::PosePc>();
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    pcl::copyPointCloud(*c_pcKfPoses_, *l_kfPose);
    return l_kfPose;
}

std::size_t KfMapPair::getKfSize()
{
    std::lock_guard <std::mutex> lck(c_kfMutex_);
    return c_vpairKFMaps_.size();
}

void KfMapPair::push(type::KeyFramePtr p_kfMap, type::POSE p_kfPose)
{
    std::lock_guard <std::mutex> lck(c_kfMutex_);
    c_vpairKFMaps_.push_back(p_kfMap);
    c_pcKfPoses_->push_back(p_kfPose);
}

MapPosePair KfMapPair::back()
{
    MapPosePair l_pair;
    l_pair.map = boost::make_shared<type::KeyFrame>();
    std::lock_guard <std::mutex> lck(c_kfMutex_);
    *(l_pair.map) = *c_vpairKFMaps_.back();
    l_pair.pose = c_pcKfPoses_->back();
    return l_pair;
}

MapPosePair KfMapPair::getKfPairByInd(int p_iInd)
{
    MapPosePair l_pair;
    l_pair.map = boost::make_shared<type::KeyFrame>();
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    assert(c_vpairKFMaps_.size() > p_iInd && c_pcKfPoses_->size() > p_iInd);
    *(l_pair.map) = *c_vpairKFMaps_.at(p_iInd);
    l_pair.pose = c_pcKfPoses_->at(p_iInd);
    return l_pair;
}

type::KeyFramePtr KfMapPair::getKfMapByInd(int p_iInd)
{
    type::KeyFramePtr l_kfMap = boost::make_shared<type::KeyFrame>();
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    assert(c_vpairKFMaps_.size() > p_iInd);
    *l_kfMap = *c_vpairKFMaps_.at(p_iInd);
    return l_kfMap;
}

type::KeyFramePtr KfMapPair::getKfMapByInds(const std::vector<int>& p_vKfInds)
{
    type::KeyFramePtr l_kfMap = boost::make_shared<type::KeyFrame>();
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    for (uint32_t i = 0; i < p_vKfInds.size(); i++)
    {
        assert(c_vpairKFMaps_.size() > p_vKfInds[i]);
        *(l_kfMap->m_pFeature) += *(c_vpairKFMaps_.at(p_vKfInds[i])->m_pFeature);
    }
    return l_kfMap;
}

type::POSE KfMapPair::getKfPoseByInd(int p_iInd)
{
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    assert(c_pcKfPoses_->size() > p_iInd);
    return c_pcKfPoses_->at(p_iInd);
}

type::PosePcPtr KfMapPair::getKfPoseByInds(const std::vector<int>& p_vKfInds)
{
    type::PosePcPtr l_localPose = getKfPose();
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    pcl::copyPointCloud(*c_pcKfPoses_, p_vKfInds, *l_localPose);
    return l_localPose;
}

void KfMapPair::setKfMapByInd(int p_iInd, type::KeyFramePtr& p_kfMap, bool p_bCopy)
{
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    assert(c_vpairKFMaps_.size() > p_iInd);
    if (p_bCopy)
    {
        *c_vpairKFMaps_[p_iInd] = *p_kfMap;
    }
    else
    {
        c_vpairKFMaps_[p_iInd] = p_kfMap;
        p_kfMap = nullptr;
    }
}

void KfMapPair::setKfPoseByInd(int p_iInd, const type::POSE& p_pose)
{
    std::lock_guard<std::mutex> lck(c_kfMutex_);
    assert(c_pcKfPoses_->size() > p_iInd);
    c_pcKfPoses_->at(p_iInd) = p_pose;
}