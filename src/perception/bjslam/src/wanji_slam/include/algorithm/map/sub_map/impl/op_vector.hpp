/**
 * @file op_vector.hpp
 * <AUTHOR>
 * @brief vector操作函数
 * @version 1.0
 * @date 2023-07-25
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once

// std
#include <vector>
#include <algorithm>

/**
 * @brief 过滤vector中的重复数据
 * @tparam <T> 
 * @param v 待过滤vector
 * @param isSorted v是否已排序
 * @code 
 *    
 * @endcode 
 * @return [std::vector<T>] \n 
 * [details wirte here]
 * 
 */
template<class T>
std::vector<T> vectoFilterRepeat(std::vector<T> v, bool isSorted = true)
{
    std::sort(v.begin(), v.end());
    auto vector_iterator = std::unique(v.begin(), v.end());
    if (vector_iterator != v.end())
    {
        v.erase(vector_iterator, v.end());
    }
    return v;
}

/**
 * @brief 过滤v1中存在于v2中的元素
 * @tparam <T> 
 * @param v1 待操作v1
 * @param v2 待操作v2
 * @param isSorted v1, v2是否已排序
 * @code 
 *    
 * @endcode 
 * @return [std::vector<T>] \n 
 * [过滤后的vector]
 * 
 */
template<class T>
std::vector<T> vectorDifference(std::vector<T> v1, std::vector<T> v2, bool isSorted = true)
{
    std::vector<T> v;
    if (v1.empty())
        return v;
    if (v2.empty())
        return v1;

    if (!isSorted)
    {
        std::sort(v1.begin(), v1.end());
        std::sort(v2.begin(), v2.end());
    }

    std::set_difference(
        std::begin(v1), std::end(v1), std::begin(v2), std::end(v2), std::back_inserter(v));
    return v;
}

/**
 * @brief 求两vector的并集
 * @tparam <T> 
 * @param v1 待操作v1
 * @param v2 待操作v2
 * @param isSorted v1, v2是否已排序
 * @code 
 *    
 * @endcode 
 * @return [std::vector<T>] \n 
 * [求并集后的vector]
 * 
 */
template<class T>
std::vector<T> vectorUnion(std::vector<T> v1, std::vector<T> v2, bool isSorted = true)
{
    std::vector<T> v;
    if (!isSorted)
    {
        std::sort(v1.begin(), v1.end());
        std::sort(v2.begin(), v2.end());
    }

    std::set_union(
        std::begin(v1), std::end(v1), std::begin(v2), std::end(v2), std::back_inserter(v));
    v.erase(unique(v.begin(), v.end()), v.end());
    return v;
}

/**
 * @brief 求两vector的交集
 * @tparam <T> 
 * @param v1 待操作v1
 * @param v2 待操作v2
 * @param isSorted v1, v2是否已排序
 * @code 
 *    
 * @endcode 
 * @return [std::vector<T>] \n 
 * [求交集后的vector]
 * 
 */
template<class T>
std::vector<T> vectorIntersection(std::vector<T> v1, std::vector<T> v2, bool isSorted = true)
{
    std::vector<T> v;
    if (!isSorted)
    {
        std::sort(v1.begin(), v1.end());
        std::sort(v2.begin(), v2.end());
    }
    set_intersection(v1.begin(), v1.end(), v2.begin(), v2.end(),
                        back_inserter(v));  //求交集
    return v;
}