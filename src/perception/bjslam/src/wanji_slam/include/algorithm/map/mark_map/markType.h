/*
 * @Author: your name
 * @Date: 2021-05-19 18:53:48
 * @LastEditTime: 2021-05-20 14:04:19
 * @Chen <PERSON>: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /markmapping/src/markType.h
 */
#pragma once
#include <vector>
typedef unsigned char u8;
typedef unsigned short u16;
typedef struct _MARK_XY
{
    u8 m_u8size;
    u8 m_u8shape;
    u16 m_u32no;
    int m_s32x;
    int m_s32y;
} MARK_XY;
typedef std::vector<MARK_XY> MARK_LIST;