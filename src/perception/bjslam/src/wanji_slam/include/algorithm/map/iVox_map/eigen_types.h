/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-09-09 10:06:59
 * @LastEditors: Tao<PERSON><NAME_EMAIL>
 * @LastEditTime: 2022-10-17 16:33:44
 * @FilePath: /qiu_ws/src/wanji_slam/include/eigen_types.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @file eigen_types.h
 * <AUTHOR>
 * @brief
 * @version 1.0
 * @date 2022-08-26
 */

#ifndef WJ_SLAM_EIGEN_TYPES_H
#define WJ_SLAM_EIGEN_TYPES_H

#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/Geometry>

/// alias for eigen
using Vec2i = Eigen::Vector2i;
using Vec3i = Eigen::Vector3i;

using Vec2d = Eigen::Vector2d;
using Vec2f = Eigen::Vector2f;
using Vec3d = Eigen::Vector3d;
using Vec3f = Eigen::Vector3f;
using Vec5d = Eigen::Matrix<double, 5, 1>;
using Vec5f = Eigen::Matrix<float, 5, 1>;
using Vec6d = Eigen::Matrix<double, 6, 1>;
using Vec6f = Eigen::Matrix<float, 6, 1>;
using Vec15d = Eigen::Matrix<double, 15, 15>;

using Mat1d = Eigen::Matrix<double, 1, 1>;
using Mat3d = Eigen::Matrix3d;
using Mat3f = Eigen::Matrix3f;
using Mat4d = Eigen::Matrix4d;
using Mat4f = Eigen::Matrix4f;
using Mat5d = Eigen::Matrix<double, 5, 5>;
using Mat5f = Eigen::Matrix<float, 5, 5>;
using Mat6d = Eigen::Matrix<double, 6, 6>;
using Mat6f = Eigen::Matrix<float, 6, 6>;
using Mat15d = Eigen::Matrix<double, 15, 15>;

using Quatd = Eigen::Quaterniond;
using Quatf = Eigen::Quaternionf;

namespace wj_slam {

/// less of vector
template <int N> struct less_vec
{
    inline bool operator()(const Eigen::Matrix<int, N, 1>& v1,
                           const Eigen::Matrix<int, N, 1>& v2) const;
};

/// hash of vector
template <int N> struct hash_vec
{
    inline size_t operator()(const Eigen::Matrix<int, N, 1>& v) const;
};

/// implementation
template <>
inline bool less_vec<2>::operator()(const Eigen::Matrix<int, 2, 1>& v1,
                                    const Eigen::Matrix<int, 2, 1>& v2) const
{
    return v1[0] < v2[0] || (v1[0] == v2[0] && v1[1] < v2[1]);
}

template <>
inline bool less_vec<3>::operator()(const Eigen::Matrix<int, 3, 1>& v1,
                                    const Eigen::Matrix<int, 3, 1>& v2) const
{
    return v1[0] < v2[0]
           || (v1[0] == v2[0] && v1[1] < v2[1])
                  && (v1[0] == v2[0] && v1[1] == v2[1] && v1[2] < v2[2]);
}

/// vec 2 hash
/// @see Optimized Spatial Hashing for Collision Detection of Deformable Objects, Matthias Teschner
/// et. al., VMV 2003
template <> inline size_t hash_vec<2>::operator()(const Eigen::Matrix<int, 2, 1>& v) const
{
    return size_t(((v[0]) * 73856093) ^ ((v[1]) * 471943)) % 100000;
}

/// vec 3 hash
template <> inline size_t hash_vec<3>::operator()(const Eigen::Matrix<int, 3, 1>& v) const
{
    return size_t(((v[0]) * 73856093) ^ ((v[1]) * 471943) ^ ((v[2]) * 83492791)) % 1000000;
}

constexpr auto less_vec2i = [](const Vec2i& v1, const Vec2i& v2) {
    return v1[0] < v2[0] || (v1[0] == v2[0] && v1[1] < v2[1]);
};

}  // namespace wj_slam

#endif
