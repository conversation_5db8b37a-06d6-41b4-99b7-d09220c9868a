/**
 * @file FilterFactory.hpp
 * <AUTHOR>
 * @brief 子图点云过滤器工厂类
 * @version 1.0
 * @date 2023-08-10
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once

// local
#include "common/common_ex.h"
#include "common/type/type_status.h"
#include "common/type/type_template.h"
// pcl
#include <pcl/filters/random_sample.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
// std
#include <vector>
#include <mutex>
std::mutex c_lock;

class FilterFactory {
  public:
    /**
     * @brief 获取单例对象
     * @code
     *
     * @endcode
     * @return [FilterFactory*] \n
     * [单例对象]
     *
     */
    static FilterFactory* getInstance();

    /**
     * @brief 设置过滤类型
     * @param p_optModel 根据优化模式选择过滤方法
     *
     */
    void setFilterType(int p_optModel);

    /**
     * @brief 获取过滤类型
     * @code
     *
     * @endcode
     * @return [int] \n
     * [过滤类型]
     *
     */
    int getFilterType();

    /**
     * @brief 关键帧为位姿过滤
     * @tparam <T>
     * @param p_inputPc 输入位姿点云
     * @param p_outPc   过滤后点云
     *
     */
    template <typename T>
    void filterKfPose(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                      boost::shared_ptr<pcl::PointCloud<T>> p_outPc);

    /**
     * @brief 获取关键帧位姿过滤后点云索引（VoxelGrid模式过滤后邻近搜索原始点）
     * @tparam <T>
     * @param p_inputPc     输入位姿点云
     * @param p_searchPc    过滤后点云
     * @code
     *
     * @endcode
     * @return [std::vector<int>] \n
     * [过滤后点云索引]
     *
     */
    template <typename T>
    std::vector<int>
    filterKfPoseToNearestInds(const boost::shared_ptr<const pcl::PointCloud<T>> p_inputPc,
                              const boost::shared_ptr<const pcl::PointCloud<T>> p_searchPc);

    /**
     * @brief 角点过滤
     * @tparam <T>
     * @param p_inputPc 输入角点点云
     * @param p_outPc   过滤后点云
     *
     */
    template <typename T>
    void filterCorner(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                      boost::shared_ptr<pcl::PointCloud<T>> p_outPc);

    /**
     * @brief 面点过滤
     * @tparam <T>
     * @param p_inputPc 输入面点点云
     * @param p_outPc   过滤后点云
     *
     */
    template <typename T>
    void filterSurf(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                    boost::shared_ptr<pcl::PointCloud<T>> p_outPc);

    /**
     * @brief 靶标点过滤
     * @tparam <T>
     * @param p_inputPc 输入靶标点点云
     * @param p_outPc   过滤后点云
     *
     */
    template <typename T>
    void filterMark(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                    boost::shared_ptr<pcl::PointCloud<T>> p_outPc);

    /**
     * @brief Curb点过滤
     * @tparam <T>
     * @param p_inputPc 输入Curb点点云
     * @param p_outPc   过滤后点云
     *
     */
    template <typename T>
    void filterCurb(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                    boost::shared_ptr<pcl::PointCloud<T>> p_outPc);

    /**
     * @brief VoxelGrid 方式过滤点云
     * @tparam <T>
     * @param p_inputPc     输入待过滤点云
     * @param p_outPc       过滤后点云
     * @param p_fLeafsize   voxel尺寸设置
     * @param p_bSrcPoint   是否保留原始点（若保留则过滤后临近搜索对应原始点）
     *
     */
    template <typename T>
    static void voxelFilter(const boost::shared_ptr<const pcl::PointCloud<T>> p_inputPc,
                            boost::shared_ptr<pcl::PointCloud<T>> p_outPc,
                            float p_fLeafsize,
                            bool p_bSrcPoint = false);

    /**
     * @brief Random方式过滤点云
     * @tparam <T>
     * @param p_inputPc     输入待过滤点云
     * @param p_outPc       过滤后点云
     * @param p_percent     过滤百分比设置
     *
     */
    template <typename T>
    static void randomFilter(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                             boost::shared_ptr<pcl::PointCloud<T>> p_outPc,
                             float p_percent);

    /**
     * @brief 获取VoxelGrid方式过滤后点云索引（过滤后邻近搜索原始点索引）
     * @tparam <T>
     * @param p_inputPc
     * @param p_searchPc
     * @param p_leafsize
     * @code
     *
     * @endcode
     * @return [std::vector<int>] \n
     * [过滤后点云索引]
     *
     */
    template <typename T>
    static std::vector<int>
    voxelFilterToNearestInds(const boost::shared_ptr<const pcl::PointCloud<T>> p_inputPc,
                             const boost::shared_ptr<const pcl::PointCloud<T>> p_searchPc,
                             float p_leafsize);

    /**
     * @brief 设置附近路径点云
     * @param p_pcKFPoses
     * @code
     *
     * @endcode
     * @return [true] \n
     * [设置成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [设置失败]
     *
     */
    bool setNearByPath(type::PosePcPtr p_pcKFPoses);

    /**
     * @brief 获取附近路径点Z值
     * @param p_tPose 当前位置
     * @code
     *
     * @endcode
     * @return [float] \n
     * [附近路径点Z值]
     *
     */
    float getNearbyPathZ(Eigen::Vector3d p_tPose);

    /**
     * @brief 获取附近路径点I值
     * @param p_tPose       当前位置
     * @param p_fIntensity  待获取路径点I值
     * @code
     *
     * @endcode
     * @return [true] \n
     * [获取成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [获取失败]
     *
     */
    bool getNearbyPathI(Eigen::Vector3d p_tPose, float& p_fIntensity);
    static FilterFactory* c_instance;
  private:
    FilterFactory();

  private:
    float c_fKfPoseDownSize_;  // 关键帧位姿下采样尺寸
    float c_fCornDownSize_;    // 角点下采样尺寸
    float c_fSurfDownSize_;    // 面点下采样尺寸
    float c_fMarkDownSize_;    // 靶标点下采样尺寸
    float c_fCurbDownSize_;    // Curb点下采样尺寸
    int c_optModel_;

    boost::shared_ptr<pcl::KdTreeFLANN<type::POSE>> c_kdtreeNearByKFPose_;  // 邻近搜索对象
};

FilterFactory::FilterFactory()
{
    wj_slam::SYSPARAM* l_stSysParam = wj_slam::SYSPARAM::getIn();
    setFilterType(l_stSysParam->m_map.m_iOptimizeModel);
    c_kdtreeNearByKFPose_.reset(new pcl::KdTreeFLANN<type::POSE>());
}

int FilterFactory::getFilterType()
{
    return c_optModel_;
}

FilterFactory* FilterFactory::c_instance = nullptr;
FilterFactory* FilterFactory::getInstance()
{
    if (nullptr == c_instance)
    {
        c_lock.lock();
        if (nullptr == c_instance)
        {
            c_instance = new FilterFactory();
        }
        c_lock.unlock();
    }
    return c_instance;
}

void FilterFactory::setFilterType(int p_optModel)
{
    wj_slam::SYSPARAM* l_stSystem = wj_slam::SYSPARAM::getIn();
    c_optModel_ = p_optModel;
    if (c_optModel_ == wj_slam::OptimizeMapType::IVOX_TYPE)
    {
        c_fKfPoseDownSize_ = l_stSystem->m_map.m_fMapKFPoseGrid;
        c_fCornDownSize_ = l_stSystem->m_map.m_fCorSampleSize;
        c_fSurfDownSize_ = l_stSystem->m_map.m_fSurSampleSize;
        c_fMarkDownSize_ = 1.0;
        c_fCurbDownSize_ = l_stSystem->m_map.m_fCurbSampleSize;
    }
    else if (c_optModel_ == wj_slam::OptimizeMapType::KDTREE_TYPE)
    {
        c_fKfPoseDownSize_ = l_stSystem->m_map.m_fMapKFPoseGrid;
        c_fCornDownSize_ = 0.1;
        c_fSurfDownSize_ = 0.1;
        c_fMarkDownSize_ = 0.1;
        c_fCurbDownSize_ = 0.1;
    }
}

template <typename T>
void FilterFactory::filterKfPose(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                                 boost::shared_ptr<pcl::PointCloud<T>> p_outPc)
{
    voxelFilter<T>(p_inputPc, p_outPc, c_fKfPoseDownSize_, true);
}

template <typename T>
std::vector<int> FilterFactory::filterKfPoseToNearestInds(
    const boost::shared_ptr<const pcl::PointCloud<T>> p_inputPc,
    const boost::shared_ptr<const pcl::PointCloud<T>> p_searchPc)
{
    return voxelFilterToNearestInds<T>(p_inputPc, p_searchPc, c_fKfPoseDownSize_);
}

template <typename T>
void FilterFactory::filterCorner(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                                 boost::shared_ptr<pcl::PointCloud<T>> p_outPc)
{
    if (c_optModel_ == wj_slam::OptimizeMapType::IVOX_TYPE)
    {
        randomFilter<T>(p_inputPc, p_outPc, c_fCornDownSize_);
    }
    else if (c_optModel_ == wj_slam::OptimizeMapType::KDTREE_TYPE)
    {
        voxelFilter<T>(p_inputPc, p_outPc, c_fCornDownSize_);
    }
}

template <typename T>
void FilterFactory::filterSurf(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                               boost::shared_ptr<pcl::PointCloud<T>> p_outPc)
{
    if (c_optModel_ == wj_slam::OptimizeMapType::IVOX_TYPE)
    {
        randomFilter<T>(p_inputPc, p_outPc, c_fSurfDownSize_);
    }
    else if (c_optModel_ == wj_slam::OptimizeMapType::KDTREE_TYPE)
    {
        voxelFilter<T>(p_inputPc, p_outPc, c_fSurfDownSize_);
    }
}

template <typename T>
void FilterFactory::filterMark(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                               boost::shared_ptr<pcl::PointCloud<T>> p_outPc)
{
    if (c_optModel_ == wj_slam::OptimizeMapType::IVOX_TYPE)
    {
        randomFilter<T>(p_inputPc, p_outPc, c_fMarkDownSize_);
    }
    else if (c_optModel_ == wj_slam::OptimizeMapType::KDTREE_TYPE)
    {
        voxelFilter<T>(p_inputPc, p_outPc, c_fMarkDownSize_);
    }
}

template <typename T>
void FilterFactory::filterCurb(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                               boost::shared_ptr<pcl::PointCloud<T>> p_outPc)
{
    if (c_optModel_ == wj_slam::OptimizeMapType::IVOX_TYPE)
    {
        randomFilter<T>(p_inputPc, p_outPc, c_fCurbDownSize_);
    }
    else if (c_optModel_ == wj_slam::OptimizeMapType::KDTREE_TYPE)
    {
        voxelFilter<T>(p_inputPc, p_outPc, c_fCurbDownSize_);
    }
}

template <typename T>
void FilterFactory::voxelFilter(const boost::shared_ptr<const pcl::PointCloud<T>> p_inputPc,
                                boost::shared_ptr<pcl::PointCloud<T>> p_outPc,
                                float p_fLeafsize,
                                bool p_bSrcPoint)
{
    pcl::VoxelGrid<T> l_voxel;
    l_voxel.setLeafSize(p_fLeafsize, p_fLeafsize, p_fLeafsize);
    l_voxel.setInputCloud(p_inputPc);

    if (p_bSrcPoint)
    {
        pcl::KdTreeFLANN<T> l_kdTree;
        pcl::PointCloud<T> l_outPc;
        pcl::PointCloud<T> l_dsPc;
        l_voxel.filter(l_dsPc);
        l_kdTree.setInputCloud(p_inputPc);
        std::vector<float> l_fDists;
        std::vector<int> l_nInd;
        for (int i = 0; i < (int)l_dsPc.size(); i++)
        {
            l_kdTree.nearestKSearch(l_dsPc[i], 1, l_nInd, l_fDists);
            if (!l_nInd.empty())
            {
                l_outPc.push_back(p_inputPc->at(l_nInd[0]));
            }
        }
        (*p_outPc).swap(l_outPc);
    }
    else
    {
        l_voxel.filter(*p_outPc);
    }
}

template <typename T>
void FilterFactory::randomFilter(const boost::shared_ptr<pcl::PointCloud<T>> p_inputPc,
                                 boost::shared_ptr<pcl::PointCloud<T>> p_outPc,
                                 float p_percent)
{
    int l_iCloudNum = p_inputPc->size();
    pcl::RandomSample<T> l_filter;
    l_filter.setInputCloud(p_inputPc);
    if (l_iCloudNum * p_percent < 1.0)
    {
        pcl::copyPointCloud(*p_outPc, *p_inputPc);
        return;
    }

    l_filter.setSample(l_iCloudNum * p_percent);
    l_filter.filter(*p_outPc);
}

template <typename T>
std::vector<int> FilterFactory::voxelFilterToNearestInds(
    const boost::shared_ptr<const pcl::PointCloud<T>> p_inputPc,
    const boost::shared_ptr<const pcl::PointCloud<T>> p_searchPc,
    float p_leafsize)
{
    auto l_dsPc = boost::make_shared<pcl::PointCloud<T>>();
    voxelFilter<T>(p_inputPc, l_dsPc, p_leafsize);
    std::vector<int> l_vPcInds;
    pcl::KdTreeFLANN<T> l_kdTree;
    l_kdTree.setInputCloud(p_searchPc);
    std::vector<float> l_fDists;
    std::vector<int> l_nInd;
    for (int i = 0; i < (int)l_dsPc->size(); i++)
    {
        l_kdTree.nearestKSearch(l_dsPc->at(i), 1, l_nInd, l_fDists);
        if (!l_nInd.empty())
        {
            l_vPcInds.push_back(l_nInd[0]);
        }
    }
    return l_vPcInds;
}

bool FilterFactory::setNearByPath(type::PosePcPtr p_pcKFPoses)
{
    if (p_pcKFPoses && p_pcKFPoses->size())
    {
        c_kdtreeNearByKFPose_->setInputCloud(p_pcKFPoses);
        return true;
    }
    else
        c_kdtreeNearByKFPose_.reset(new pcl::KdTreeFLANN<type::POSE>());
    return false;
}

float FilterFactory::getNearbyPathZ(Eigen::Vector3d p_tPose)
{
    std::vector<float> l_fDists;
    std::vector<int> l_nInd;
    if (nullptr != c_kdtreeNearByKFPose_->getInputCloud())
    {
        type::POSE l_pPoint;
        l_pPoint.x = p_tPose[0];
        l_pPoint.y = p_tPose[1];
        l_pPoint.z = p_tPose[2];
        c_kdtreeNearByKFPose_->nearestKSearch(l_pPoint, 1, l_nInd, l_fDists);
        if (!l_nInd.empty())
        {
            return c_kdtreeNearByKFPose_->getInputCloud()->points[l_nInd[0]].z;
        }
    }
    return p_tPose[2];
}

bool FilterFactory::getNearbyPathI(Eigen::Vector3d p_tPose, float& p_fIntensity)
{
    std::vector<float> l_fDists;
    std::vector<int> l_nInd;
    if (nullptr != c_kdtreeNearByKFPose_->getInputCloud())
    {
        type::POSE l_tPose;
        l_tPose.x = p_tPose[0];
        l_tPose.y = p_tPose[1];
        l_tPose.z = p_tPose[2];
        c_kdtreeNearByKFPose_->nearestKSearch(l_tPose, 1, l_nInd, l_fDists);
        if (!l_nInd.empty())
        {
            p_fIntensity = c_kdtreeNearByKFPose_->getInputCloud()->points[l_nInd[0]].intensity;
            return true;
        }
    }
    return false;
}