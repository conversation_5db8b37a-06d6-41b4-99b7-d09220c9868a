#include "../convert_720NP.h"
namespace wanji_driver {

Convert720F::Convert720F(uint32_t p_uiLidarID, FeatureCb p_fECb) : Convert720(p_uiLidarID, p_fECb)
{
}

Convert720F::~Convert720F() {}

bool Convert720F::checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId)
{
    int l_iFirstPkg = 0;
    int l_iEndPkg = p_pScanMsg.m_data->m_vPackets.size() - 1;
    int l_iFirstAng = p_pScanMsg.m_data->m_vPackets[l_iFirstPkg].m_data[6] << 8
                      | p_pScanMsg.m_data->m_vPackets[l_iFirstPkg].m_data[5];
    int l_iEndAng = p_pScanMsg.m_data->m_vPackets[l_iEndPkg].m_data[6] << 8
                    | p_pScanMsg.m_data->m_vPackets[l_iEndPkg].m_data[5];
    //二次判断 -  首包 尾包 角度是否正确
    if (!(l_iFirstAng == 36000 && l_iEndAng == 35700))
    {
        LOGP(WERROR,
             "{} [{}]雷达数据角度异常: 帧[{}] 角度: {} -> {}\n",
             WJLog::getWholeSysTime(),
             c_stLaserCfg_.m_sLaserName,
             p_iCurId + 1,
             l_iFirstAng,
             l_iEndAng);
        return false;
    }
    return true;
}

void Convert720F::unpack_(const s_LIDAR_RAW_DATA::DArray& p_rawData,
                          double& p_f64Time,
                          s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                          s_PCloud& pmid,
                          PCOutPtr& pcOut)
{
    const s_RawPacket* raw = (const s_RawPacket*)&p_rawData[0];
    POINTTYPEOUT l_pointOut;
    PRaw pointRaw;
    PAdd pointAdd;

    float l_fDist_xyz = 0, l_fDist_xy = 0;
    Eigen::Vector3d l_Dist_xyz(0, 0, 0);
    uint32_t l_fIntensity = 0;
    float l_fAzimuthAngle = 0;

    // // 乱序包 首点更新时间
    // p_f64Time = ((raw->blocks[0].rotation % 36000) / 300) * BLOCKS_PER_PACKET * SCANS_PER_BLOCK
    //             * WLR720_POINT_TDURATION;
    // 15包
    for (int i = 0; i < BLOCKS_PER_PACKET; i++)
    {
        int scanID = 0;
        // 19块
        for (int j = 0, k = 0; j < SCANS_PER_BLOCK; j++, k += RAW_SCAN_SIZE)
        {
            // 角度 单位10mdeg
            union two_bytes rottmp;
            rottmp.bytes[0] = raw->blocks[i].rotation[0];
            rottmp.bytes[1] = raw->blocks[i].rotation[1];

            // 解析获取距离信息 单位4mm -> m
            union two_bytes tmp;
            tmp.bytes[0] = raw->blocks[i].data[k];
            tmp.bytes[1] = raw->blocks[i].data[k + 1];
            l_fDist_xyz = tmp.uint * DISTANCE_RESOLUTION;

            // 解析强度值(脉宽)
            l_fIntensity = raw->blocks[i].data[k + 2];

            // 角度 单位deg
            float first_Angazimuth = rottmp.uint / 100.0 - c_fAngleResolutionVal_;
            float second_Angazimuth = first_Angazimuth + c_fGroupAngleVal_;
            float third_Angazimuth = first_Angazimuth + 2 * c_fGroupAngleVal_;
            float fourth_Angazimuth = first_Angazimuth + 3 * c_fGroupAngleVal_;

            switch (j)
            {
                case 0: l_fAzimuthAngle = (first_Angazimuth); break;
                case 1: l_fAzimuthAngle = (first_Angazimuth + c_fLineAngleGap_); break;
                case 2: l_fAzimuthAngle = (first_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 3: l_fAzimuthAngle = (first_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 4: l_fAzimuthAngle = (first_Angazimuth + 4 * c_fLineAngleGap_); break;
                case 5: l_fAzimuthAngle = (second_Angazimuth); break;
                case 6: l_fAzimuthAngle = (second_Angazimuth + c_fLineAngleGap_); break;
                case 7: l_fAzimuthAngle = (second_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 8: l_fAzimuthAngle = (second_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 9: l_fAzimuthAngle = (third_Angazimuth); break;
                case 10: l_fAzimuthAngle = (third_Angazimuth + c_fLineAngleGap_); break;
                case 11: l_fAzimuthAngle = (third_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 12: l_fAzimuthAngle = (third_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 13: l_fAzimuthAngle = (third_Angazimuth + 4 * c_fLineAngleGap_); break;
                case 14: l_fAzimuthAngle = (fourth_Angazimuth); break;
                case 15: l_fAzimuthAngle = (fourth_Angazimuth + c_fLineAngleGap_); break;
                case 16: l_fAzimuthAngle = (fourth_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 17: l_fAzimuthAngle = (fourth_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 18: l_fAzimuthAngle = (fourth_Angazimuth + 4 * c_fLineAngleGap_); break;
                default: break;
            }

            // 真实角度计算
            l_fAzimuthAngle += 0.05 * c_aiEccentricity_[int(l_fAzimuthAngle * 20 + 7200) % 7200];
            // 角度约束0-360
            l_fAzimuthAngle = l_fAzimuthAngle < 360.0 ? l_fAzimuthAngle : l_fAzimuthAngle - 360.0;
            l_fAzimuthAngle = l_fAzimuthAngle > 0.0 ? l_fAzimuthAngle : l_fAzimuthAngle + 360.0;

            // 扫描线束计算
            if (j == 0 || j == 5 || j == 9 || j == 14)
                scanID = 8;
            if (j > 0 && j < 5)
                scanID = 16 - j;
            if (j > 5 && j < 14)
                scanID = 17 - j;
            if (j > 14 && j <= 18)
                scanID = 18 - j;

            l_fDist_xy = l_fDist_xyz * c_afVcosRotTable_[j];
            l_Dist_xyz.z() = l_fDist_xyz * c_afVsinRotTable_[j];
            l_Dist_xyz.x() = l_fDist_xy
                             * c_afSinRotTable_[int(l_fAzimuthAngle * ROTATION_RESOLUTION_INV)
                                                % ROTATION_MAX_UNITS];
            l_Dist_xyz.y() = l_fDist_xy
                             * (c_afCosRotTable_[int(l_fAzimuthAngle * ROTATION_RESOLUTION_INV)
                                                 % ROTATION_MAX_UNITS]);

            // 仿射变换矩阵
            l_Dist_xyz = c_stLaserCfg_.m_transToBase * l_Dist_xyz;

            p_f64Time += WLR720_POINT_TDURATION;

            if (!isValidData_(l_fIntensity, l_fDist_xyz, l_fAzimuthAngle))
                continue;
            if (!c_stLaserCfg_.m_bUseFloor && l_Dist_xyz.z() < -c_stLaserCfg_.m_fFeatureHeight)
                continue;

            pointRaw.x = l_Dist_xyz.x();
            pointRaw.y = l_Dist_xyz.y();
            pointRaw.z = l_Dist_xyz.z();
            pointAdd.intensity = l_fIntensity;
            pointAdd.xydist = l_fDist_xy;
            pointAdd.depth = l_fDist_xyz;
            pointAdd.ang = l_fAzimuthAngle;
            pointAdd.time = p_f64Time;

            // 中间线点云
            if (scanID == 8)
            {
                pmid.m_praw->points.push_back(pointRaw);
                pmid.m_padd->push_back(pointAdd);
            }
            if ((j == 5) || (j == 9) || (j == 14))
            {
                continue;
            }
            pc[scanID].m_praw->points.push_back(pointRaw);
            pc[scanID].m_padd->push_back(pointAdd);
        }
    }
}

void Convert720F::unpackAddMsg_(const s_LIDAR_RAW_DATA::DArray& p_rawData, AddtionMsgPtr& padd)
{
    const s_RawPacket* raw = (const s_RawPacket*)&p_rawData[0];

    // gnss
    tm l_time = {
        .tm_sec = raw->addmsg.time[0],         //秒
        .tm_min = raw->addmsg.time[1],         //分
        .tm_hour = raw->addmsg.time[2],        //时
        .tm_mday = raw->addmsg.time[3],        //日
        .tm_mon = raw->addmsg.time[4] - 1,     //月
        .tm_year = raw->addmsg.time[5] + 100,  //年
    };
    padd->m_gnssTime.tv_sec = mktime(&l_time);
    padd->m_gnssTime.tv_usec = (((raw->addmsg.nsec[3] & 0x0F) << 24) + (raw->addmsg.nsec[2] << 16)
                                + (raw->addmsg.nsec[1] << 8) + raw->addmsg.nsec[0])
                               / 100;
    if (padd->m_gnssTime.tv_usec < 0)
    {
        padd->m_gnssTime.tv_sec -= 1;
        padd->m_gnssTime.tv_usec = 1e6 - padd->m_gnssTime.tv_usec;
    }
    padd->m_state[0] = (raw->addmsg.nsec[3] & 0x80) >> 7;
    padd->m_state[1] = (raw->addmsg.nsec[3] & 0x40) >> 6;
    padd->m_state[2] = (raw->addmsg.nsec[3] & 0x30) >> 4;
    /* imu
    角速度数据[rad/s] = 原始数据 * 灵敏度 / 1000 /180 * pi
    加速度数据[m/s^2] = 原始数据 * 灵敏度 / 1000 * gravity 其中gravity = 9.80665
    */
    union two_bytes l_tmpData;
    for (int j = 0; j < 3; ++j)
    {
        l_tmpData.bytes[0] = raw->addmsg.angVel[2 * j];
        l_tmpData.bytes[1] = raw->addmsg.angVel[2 * j + 1];
        padd->m_gyro[j] = l_tmpData.sint * c_fAngScalar;
        l_tmpData.bytes[0] = raw->addmsg.accel[2 * j];
        l_tmpData.bytes[1] = raw->addmsg.accel[2 * j + 1];
        padd->m_accel[j] = l_tmpData.sint * c_fAccScalar;
    }
}

}  // namespace wanji_driver