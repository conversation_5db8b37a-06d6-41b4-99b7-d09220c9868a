/**
 * @file wanji_driver.h
 * <AUTHOR> x<PERSON>tian
 * @brief  WJ雷达驱动
 * @version 1.0
 * @date 2023-06-21
 * @copyright Copyright (c)2023 Vanjee
 */

#ifndef _LS_C16_DRIVER_H_
#define _LS_C16_DRIVER_H_

// local
#include "input.h"

namespace wanji_driver {
class wanjiDriver {
  public:
    typedef boost::shared_ptr<s_LIDAR_RAW_DATAS> RawDataPtr;
    typedef boost::function<void(uint32_t, RawDataPtr&, int, bool)> DriverCb;

    /**
     * @brief 构造函数
     *
     * @param p_iLidarID 雷达ID
     * @param p_packCB_  驱动回调
     */
    wanjiDriver(uint32_t p_iLidarID, DriverCb p_packCB_);

    /**
     * @brief 析构函数
     *
     */
    ~wanjiDriver();

    /**
     * @brief 启动驱动
     *
     */
    void start();

    /**
     * @brief 关闭驱动
     *
     */
    void shutDown();

  private:
    /**
     * @brief 扫描数据回调函数，用于处理从雷达中接收的一圈数据
     * @param scanbuffer 待处理数据
     *
     */
    void scanDataProcessCb_(st_ScanBuffer& p_scanbuffer);

  private:
    wj_slam::SYSPARAM* c_stSysParam_;   /**< 系统参数指针 */
    DriverCb c_driverCB_;               /**< 雷达数据解析回调 */
    boost::shared_ptr<Input> c_pInput_; /**< 雷达驱动对象 */
    uint32_t c_uiLidarID_;              /**< 雷达id */
    uint32_t c_uiCurID_;                /**< 当前圈号 从首圈圈号开始累积 */
    int32_t c_iLastCycleNum_;           /**< 上一次雷达圈号 */
    std::string c_sLaserType_;          /**< 雷达类型 */
};

}  // namespace wanji_driver

#endif