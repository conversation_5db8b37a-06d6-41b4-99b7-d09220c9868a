/**
 * @file convert_720NP.h
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @brief 接收 deta_X * 1600 网络原始数据 判断数据是否异常 解算点云
 * @version 1.1
 * @date 2023-06-25
 * @copyright Copyright (c)2023 Vanjee
 */

#ifndef _WANJI_CONVERT720F_H_
#define _WANJI_CONVERT720F_H_

#include "./convert.h"

namespace wanji_driver {

#pragma region 720F类
class Convert720F : public Convert720 {
  protected:
#pragma region 常量 / 结构体定义
    /***禁止使用uchar以外类型***/
    typedef struct RawHead
    {
        uint8_t header[2];  /// 0xffdd
        uint8_t scan_per_Block;
        uint8_t returnWaveNum;
        uint8_t block_per_Packet;
    } s_RawHead;

    typedef struct RawBlock
    {
        uint8_t rotation[2];
        uint8_t data[BLOCK_DATA_SIZE];
    } s_RawBlock;

    typedef struct RawAddtionMsg
    {
        uint8_t header[2];
        uint8_t rads[2];
        uint8_t time[6];
        uint8_t nsec[4];
        uint8_t angVel[6];
        uint8_t accel[6];
    } s_RawAddtionMsg;

    typedef struct RawPacket
    {
        s_RawHead head;
        s_RawBlock blocks[BLOCKS_PER_PACKET];
        s_RawAddtionMsg addmsg;
    } s_RawPacket;

#pragma endregion

  public:
    Convert720F(uint32_t p_uiLidarID, FeatureCb p_fECb);
    ~Convert720F();

  protected:
    /**
     * @brief 检查原始数据头角度范围是否异常
     * @param p_pScanMsg    原始数据
     * @param p_iCurId      帧id
     * @code
     *
     * @endcode
     * @return [true] \n
     * [正常]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [异常]
     *
     */
    virtual bool checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId);

    /**
     * @brief 逐包解算雷达数据
     * @param p_rawData     雷达原始网络数据
     * @param p_f64Time     每个点的时间
     * @param s_PCloud      原始数据
     * @param pmid          中间线点云
     * @param pcOut         16*1800点云
     *
     */
    void unpack_(const s_LIDAR_RAW_DATA::DArray& p_rawData,
                 double& p_f64Time,
                 s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                 s_PCloud& pmid,
                 PCOutPtr& pcOut);

    /**
     * @brief 逐包解算雷达数据中的附加数据
     * @param p_rawData 雷达原始网络数据
     * @param padd      附加消息 GNSS+IMU
     *
     */
    void unpackAddMsg_(const s_LIDAR_RAW_DATA::DArray& p_rawData, AddtionMsgPtr& padd);
#pragma endregion
};
#pragma endregion

}  // namespace wanji_driver
#include "impl/convert_720NP.hpp"
#endif