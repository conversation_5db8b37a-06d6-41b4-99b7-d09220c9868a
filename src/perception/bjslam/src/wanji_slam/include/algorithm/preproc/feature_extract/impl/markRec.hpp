#include "../markRec.h"
#include <fstream>
#include <sstream>
// #pragma optimize("",off)
Mark_Rec::Mark_Rec()
{
    g_sSysPib.PianXinCorrEN = 1;
    g_navparam.PianXinFlag = 1;
    g_sSysPib.Bmq_offset = 0;
    g_sSysPib.Eccentric_Ang = 0;

    g_navparam.AngMode = ANGMODE_005;
    g_navparam.PianXinFlag = 1;
    g_navparam.ScanFrequency = 10;
    g_navparam.MarkMaxPointNum = 1800;
    g_navparam.MarkRadio_Coeff = 80 * 1146;
    g_navparam.ScanPointNum = 7200 >> (g_navparam.AngMode - 1);
    g_navparam.TotolPackNum = 12 >> (g_navparam.AngMode - 1);
    g_navparam.Ang_Resolution = 5 << (g_navparam.AngMode - 1);
    g_navparam.Per10mDeg_Rad = (float)(M_PI / 18000.0);
    g_navparam.ScanTime_Per = (float)(1000.0 / g_navparam.ScanFrequency);
    g_navparam.ScanTime_HalfPer = g_navparam.ScanTime_Per / 2;
    g_navparam.Time_PerResolution = g_navparam.ScanTime_Per / DEG_PERSCAN;

    // g_navparam.NavLeastMarkNum = 3;
    // g_navparam.EdgeDiff = ((60 + (100 << 1)) >> 4) + 2;
    // g_navparam.FpgaZeroOffset = (g_navparam.ScanPointNum + g_sSysPib.Bmq_offset +
    // g_sSysPib.Eccentric_Ang) % g_navparam.ScanPointNum;
    g_navparam.DistDiffMax = 180;
    g_sMarkMatch_Set.m_u32MarkScan_Max = 65000;
    g_sMarkMatch_Set.m_u32MarkScan_Min = 200;  // 0.1m

    g_sSysPib.Target_THreshold = 180;
    g_sSysPib.MarkReflectivity = 0;
    g_sSysPib.MarkDotNumPct = 8;
    g_sSysPib.WorkMode_NavOrMappingOrMark = SYSMODE_NavOrMappingOrMark_NAV;
    g_sSysPib.m_u16MarkRadio = 100;
    g_sSysPib.m_u16MarkType = MARK_TYPE_CYC;
}

Mark_Rec::~Mark_Rec() {}

/**
 * @brief 清空需要使用的数组
 *
 */
void Mark_Rec::Empty_UsingBuf()
{
    memset((void*)&g_sFilterDist, 0, STRUCT_SIZE_FILTER);
    memset((void*)&g_sFilterMark_SpeedCorr, 0, STRUCT_SIZE_FILTER_TARGET_LITE);
    memset((void*)&g_sMappingXY_old, 0, STRUCT_SIZE_XY2ROBOT_CNT);
    memset((void*)&g_sFilterDistShortNewLast, 0, STRUCT_SIZE_FILTER_TARGET_LITE);
}

bool Mark_Rec::Points_Diff_Cmp(uint32_t p_Diff_In, uint32_t p_Diff_Cmp_In)
{
    return p_Diff_In <= p_Diff_Cmp_In;
}
uint16_t Mark_Rec::Return_Max(uint16_t p_In1, uint16_t p_In2)
{
    return (p_In1 > p_In2) ? p_In1 : p_In2;
}

void Mark_Rec::GetOutlineOfMarks_WD(uint16_t* p_plwidth,
                                    uint16_t* p_dist,
                                    STRUCT_CARVE* p_CarveBuf,
                                    uint16_t* p_Ref)
{
    uint16_t l_u16MarkEndCnt = 0;  //标靶总数记录
    uint8_t l_u8HasMark = 0;       //标靶开始点标志
    uint16_t l_u16ZeroNum =
        0;  //非标靶点数，当两个标靶之间的非标靶的点数超过MARK_COUNTIUEZERO_NUM个时认为是两个标靶
    uint16_t l_u16i;
    memset(p_CarveBuf, 0, STRUCT_SIZE_CARVE);

    for (l_u16i = 0; l_u16i < MIDPCLEN; l_u16i++)
    {
        if (l_u16MarkEndCnt < TARGET_MAX)
        {
            if ((!Filter_Mark_By_Ref_WD(p_plwidth + l_u16i, p_dist + l_u16i, &p_Ref[l_u16i], 1))
                && (p_dist[l_u16i] > 10))
            {  // && (l_plwidth_out < 255)
                if (l_u8HasMark == 0)
                {                                                        //如果是标靶起点
                    l_u8HasMark = 1;                                     //置标靶起点位
                    p_CarveBuf->m_u16MarkSta[l_u16MarkEndCnt] = l_u16i;  //记录标靶起点位
                }
                l_u16ZeroNum = 0;
            }
            else
            {
                l_u16ZeroNum++;
                if ((l_u8HasMark == 1) && (l_u16ZeroNum >= MARK_COUNTIUEZERO_NUM))
                {
                    l_u8HasMark = 0;
                    p_CarveBuf->m_u16MarkEnd[l_u16MarkEndCnt] = l_u16i - l_u16ZeroNum;
                    l_u16MarkEndCnt++;
                }
            }
        }
        else
        {
            l_u8HasMark = 0;
            break;
        }
    }
    if ((l_u16i == g_navparam.ScanPointNum) && (l_u8HasMark == 1))
    {  //此处处理的是最后一个点的情况
        p_CarveBuf->m_u16MarkEnd[l_u16MarkEndCnt] = g_navparam.ScanPointNum - l_u16ZeroNum - 1;
        l_u16MarkEndCnt++;
    }
    p_CarveBuf->m_u32MarkNum = l_u16MarkEndCnt;
}

//查找每一包数据上的靶标
int32_t Mark_Rec::Filter_Mark_By_Ref_WD(uint16_t* PulseWidth,
                                        uint16_t* Dist,
                                        uint16_t* p_RefBuf,
                                        uint8_t chn)
{
    int32_t l_s32ret = -1;
    //根据脉宽+距离来查表
    uint32_t l_u32addr;
    int16_t l_s16tmp;
    uint32_t l_u32pulse;
    uint16_t l_u16RefValue = g_sSysPib.Target_THreshold - g_sSysPib.MarkReflectivity;  //
    // *p_RefBuf = 0;
    if ((*PulseWidth > INTENSITYMAX) || (*PulseWidth < INTENSITYMIN))  //强度过大或者过小
    {
        return l_s32ret;
    }

    // 强度大于阈值说明时靶标上的点
    if (*PulseWidth > INTENSITYTH)
    {
        l_s32ret = 0;
    }

    return l_s32ret;
}

void Mark_Rec::Find_MaxMin_Dist_WD(uint16_t sta,
                                   uint16_t end,
                                   uint16_t* Dist,
                                   uint16_t* max_dist,
                                   uint16_t* min_dist,
                                   uint16_t* Min_offset)
{
    uint16_t l_u16DistOld_max = 0, l_u16DistOld_min = 0, l_u16j = 0;
    uint16_t l_u16Dist = 0;
    //	uint16_t l_u16tmp = 0;

    l_u16DistOld_max = Dist[sta];
    l_u16DistOld_min = l_u16DistOld_max;
    *Min_offset = sta;
    *max_dist = l_u16DistOld_max;
    *min_dist = l_u16DistOld_min;
    for (l_u16j = sta + 1; l_u16j <= end; l_u16j++)
    {
        l_u16Dist = Dist[l_u16j];
        if (l_u16Dist > l_u16DistOld_max)  //距离
        {
            l_u16DistOld_max = l_u16Dist;
        }
        if ((l_u16Dist < l_u16DistOld_min) && (l_u16Dist != 0))
        {
            *Min_offset = l_u16j;
            l_u16DistOld_min = l_u16Dist;
        }
    }
    *max_dist = l_u16DistOld_max;
    *min_dist = l_u16DistOld_min;
}

uint16_t Mark_Rec::Find_Max_WD(uint16_t p_u16start, uint16_t p_u16end, uint16_t* p_Data)
{
    uint16_t l_u16j = 0;
    uint16_t l_u16WidthOld = 0;
    uint16_t l_u16ret = p_u16start;

    l_u16WidthOld = p_Data[p_u16start];

    for (l_u16j = p_u16start + 1; l_u16j <= p_u16end; l_u16j++)
    {
        if (p_Data[l_u16j] > l_u16WidthOld)
        {
            l_u16ret = l_u16j;
            l_u16WidthOld = p_Data[l_u16j];
        }
    }
    return l_u16ret;
}
//最新版有改进
void Mark_Rec::FindPeakData(uint16_t* data,
                            uint16_t* p_dist,
                            uint16_t Sta,
                            uint16_t End,
                            STRUCT_MARK_INFO* p_MarkInfo,
                            uint8_t* mul)
{
    uint16_t i;
    uint8_t cnt = 0;
    uint16_t l_u16diffDist = 0;
    uint16_t OutData[100];
    uint16_t l_u16Dist[100];
    uint16_t l_u16WidthDist[100];
    uint16_t l_u16maxdist, l_u16mindist;
    uint16_t max_offset, min_offset;
    uint16_t cnt_xielv = 0;
    int16_t* pbuf = (int16_t*)malloc(1024);  // FDH2020

    OutData[0] = ((Sta + End) >> 1) % g_navparam.ScanPointNum;  //取  sta与end的中心
    l_u16Dist[0] = p_dist[OutData[0]];
    p_MarkInfo->m_u16Ang = OutData[0];
    p_MarkInfo->m_u16Dist = p_dist[OutData[0]];
    *mul = 80;
    if (End == Sta)
    {
        free(pbuf);
        return;
    }
    if ((End - Sta) > 500)
    {
        free(pbuf);
        return;
    }
    for (i = Sta; i < End; i++)
    {
        pbuf[cnt_xielv++] = data[(i + 1) % g_navparam.ScanPointNum]
                            - data[i % g_navparam.ScanPointNum];  //求反射率的斜率
    }
    for (i = 0; i < cnt_xielv; i++)
    {
        if ((pbuf[i + 1] <= 0) && (pbuf[i] > 0))
        {
            OutData[cnt] = (i + 1 + Sta) % g_navparam.ScanPointNum;  //找极大值点
            l_u16Dist[cnt] = p_dist[OutData[cnt]];
            l_u16WidthDist[cnt] = data[OutData[cnt]];
            cnt = cnt + 1;
            if (cnt >= 100)  //根据测试情况需要考虑修改为更大一点的值
                break;
        }
    }
    if (cnt > 1)
    {
        Find_MaxMin_Dist_WD(
            0,
            cnt - 1,
            l_u16Dist,
            &l_u16maxdist,
            &l_u16mindist,
            &min_offset);  //如果存在多个极大值点，则找n个极值点的最大距离和最小距离差，若差大于设定阈值，则认为存在多个靶标重叠或有遮挡，缩小百分比到90%处
        l_u16diffDist = l_u16maxdist - l_u16mindist;
        if (l_u16diffDist < g_navparam.DistDiffMax)
        {
            max_offset =
                Find_Max_WD(0, cnt - 1, l_u16WidthDist);  //不存在多个靶标，用80%去找最近的靶
            p_MarkInfo->m_u16Ang = OutData[max_offset];
            p_MarkInfo->m_u16Dist = p_dist[OutData[max_offset]];
            *mul = 70;
        }
        else
        {
            p_MarkInfo->m_u16Ang =
                OutData[min_offset];  //存在多个靶标时，选取最近的点，用90%去找最近的靶标
            p_MarkInfo->m_u16Dist = p_dist[OutData[min_offset]];
            *mul = 90;
        }
    }
    else
    {
        p_MarkInfo->m_u16Ang = OutData[0];  //仅存在一个极值点
        p_MarkInfo->m_u16Dist = p_dist[OutData[0]];
        *mul = 70;
    }
    free(pbuf);
}

void Mark_Rec::Get_MaxWidth_WD(uint8_t p_u8MaxMarkNum,
                               uint8_t p_MarkNum,
                               uint16_t* p_u16start,
                               uint16_t* p_u16end,
                               uint16_t* p_Width_Carve,
                               STRUCT_FILTER_TARGET* p_Filter,
                               uint16_t* p_dist)
{
    uint16_t l_u16i = 0;
    //	uint16_t l_u16WidthOld = 0;
    //	uint16_t l_u16offset = 0;
    //	uint16_t l_u16MarkAng = 0;
    if (p_MarkNum >= p_u8MaxMarkNum)
        p_MarkNum = p_u8MaxMarkNum;

    for (l_u16i = 0; l_u16i < p_MarkNum; l_u16i++)
    {
        FindPeakData(p_Width_Carve,
                     p_dist,
                     p_u16start[l_u16i],
                     p_u16end[l_u16i],
                     &p_Filter->m_StructMarkInfo[l_u16i],
                     &g_u8Mul[l_u16i]);
    }
}

uint8_t Mark_Rec::Find_MaxWidthPoint_WD(uint16_t* Width_Carve,
                                        STRUCT_FILTER_TARGET* p_Filter,
                                        STRUCT_CARVE* p_CraveBuf,
                                        uint16_t* p_dist)
{
    // p_CraveBuf待处理靶标
    // p_Filter存放得靶标

    uint16_t* l_u16start = NULL;  //&p_CraveBuf->m_u16MarkSta[0];
    uint16_t* l_u16end = NULL;    //&p_CraveBuf->m_u16MarkEnd[0];
    uint8_t l_u8MoveBit = 5;
    uint8_t l_u8MaxMarkNum = 1 << l_u8MoveBit;  //最多的靶标个数
    uint8_t l_u8MarkOffset = 0;
    uint32_t l_u8MarkSize = p_CraveBuf->m_u32MarkNum;

    if (l_u8MarkSize == 0)
        return 0;

    if ((p_CraveBuf->m_u16MarkSta[0] - p_CraveBuf->m_u16MarkEnd[l_u8MarkSize - 1]
         + g_navparam.ScanPointNum)
        <= MARK_COUNTIUEZERO_NUM)  //当靶标被0刻线分割
    {
        p_CraveBuf->m_u16MarkSta[0] = p_CraveBuf->m_u16MarkSta[l_u8MarkSize - 1];
        p_CraveBuf->m_u16MarkEnd[0] += g_navparam.ScanPointNum;
        l_u8MarkSize -= 1;
        l_u8MarkOffset = 0;
    }
    else if (
        l_u8MarkSize
        == TARGET_MAX)  //如果靶标个数最大，且不满足上一个if，则第一个靶标依然存在被0刻线分割但没保存切割的另一半数据，因此直接过滤
    {
        l_u8MarkOffset = 1;
        l_u8MarkSize -= 1;
    }
    l_u16start = &p_CraveBuf->m_u16MarkSta[l_u8MarkOffset];
    l_u16end = &p_CraveBuf->m_u16MarkEnd[l_u8MarkOffset];
    Get_MaxWidth_WD(
        l_u8MaxMarkNum, l_u8MarkSize, l_u16start, l_u16end, Width_Carve, p_Filter, p_dist);
    return l_u8MarkSize;
}

uint16_t Mark_Rec::Find_80Percent_Sta_Point_PulseDist(uint16_t* Width_Aver,
                                                      uint16_t Max_Width_Point,
                                                      uint16_t p_Width_80Percent)
{
    uint16_t l_u16j = Max_Width_Point;
    uint16_t l_u16Cnt = 0;
    uint16_t l_u16Width_80Percent = 0;
    uint16_t l_u16MarkMaxPointNum_Half = g_navparam.MarkMaxPointNum >> 1;

    l_u16Width_80Percent =
        p_Width_80Percent;  //(Width_Aver[Max_Width_Point] * p_mul) / 10;	//最大脉宽的80%
    while (1)
    {
        if (Width_Aver[l_u16j] < l_u16Width_80Percent)  //向前查找80%脉宽点
        {
            return (l_u16j + 1) % g_navparam.ScanPointNum;  //返回找到的起始点
        }
        else
        {
            l_u16Cnt++;
            l_u16j = (l_u16j - 1 + g_navparam.ScanPointNum) % g_navparam.ScanPointNum;
        }

        if (l_u16Cnt >= l_u16MarkMaxPointNum_Half)  //靶标太宽不要
        {
            return Max_Width_Point;
        }
    }
}

uint16_t Mark_Rec::Find_80Percent_End_Point_PulseDist(uint16_t* Width_Aver,
                                                      uint16_t Max_Width_Point,
                                                      uint16_t p_Width_80Percent)
{
    uint16_t l_u16j = Max_Width_Point;
    uint16_t l_u16Cnt = 0;
    uint16_t l_u16Width_80Percent = 0;
    uint16_t l_u16MarkMaxPointNum_Half = g_navparam.MarkMaxPointNum >> 1;

    l_u16Width_80Percent = p_Width_80Percent;  //(Width_Aver[Max_Width_Point] * p_mul) / 10;
    while (1)
    {
        if (Width_Aver[l_u16j] < l_u16Width_80Percent)  //向前查找80%脉宽点
        {
            return (l_u16j - 1 + g_navparam.ScanPointNum)
                   % g_navparam.ScanPointNum;  //返回找到的结束点
        }
        else
        {
            l_u16Cnt++;
            l_u16j = (l_u16j + 1) % g_navparam.ScanPointNum;
        }

        if (l_u16Cnt >= l_u16MarkMaxPointNum_Half)  //靶标太宽不要
        {
            return Max_Width_Point;
        }
    }
}

uint32_t Mark_Rec::Find_StaEnd_Point_RatioPulseDist(uint16_t* p_PulseDistRatio,
                                                    uint8_t p_MarkNum,
                                                    STRUCT_FILTER_TARGET* p_Filter,
                                                    uint16_t* dist,
                                                    uint8_t* mul)
{
    uint16_t l_u16i = 0;
    uint16_t l_u16Max_Width_Point = 0;
    uint16_t l_u16Start = 0, l_u16End = 0;
    //	uint16_t l_u16Ang = 0;
    uint16_t l_u16Width_80Percent;
    uint16_t l_u16MarkCenter;
    //	float l_f32StartPoint = 0,l_f32EndPoint = 0;

    for (l_u16i = 0; l_u16i < p_MarkNum; l_u16i++)
    {
        l_u16Max_Width_Point = p_Filter->m_StructMarkInfo[l_u16i]
                                   .m_u16Ang;  //*实际Ang是点序号，最大脉宽点对应的0-7199点序号
        l_u16Width_80Percent =
            p_PulseDistRatio[l_u16Max_Width_Point] * mul[l_u16i] / 100;  //*脉宽值
        l_u16Start = Find_80Percent_Sta_Point_PulseDist(
            p_PulseDistRatio, l_u16Max_Width_Point, l_u16Width_80Percent);  //寻找80%脉宽起始点
        l_u16End = Find_80Percent_End_Point_PulseDist(
            p_PulseDistRatio, l_u16Max_Width_Point, l_u16Width_80Percent);  //寻找80%脉宽结束点
        if (l_u16Start > l_u16End)
            l_u16End += g_navparam.ScanPointNum;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta = l_u16Start;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanEnd = l_u16End;
        l_u16MarkCenter = ((l_u16Start + l_u16End) >> 1) % g_navparam.ScanPointNum;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16Ang = l_u16MarkCenter;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16Dist = dist[l_u16MarkCenter];
    }
    return 0;
}

uint16_t Mark_Rec::Cal_MarkDot_ByDist_Theory(uint32_t p_dist)
{
    uint16_t l_u16MarkPointNum_Theory = 0;
    uint16_t l_u16MarkDotNumPct = g_sSysPib.MarkDotNumPct;
    uint32_t l_u16MarkDot_Coeff;

    l_u16MarkDot_Coeff = 10 << (g_navparam.AngMode - 1);

    //根据靶标距离和尺寸判断靶上有多少个点。
    if (l_u16MarkDotNumPct >= 5 && p_dist != 0)
    {
        l_u16MarkPointNum_Theory = g_navparam.MarkRadio_Coeff / p_dist;
        if ((p_dist > 20000) && (l_u16MarkPointNum_Theory < 5))
        {
            l_u16MarkPointNum_Theory = 2;
        }
        else
        {
            // l_u16MarkPointNum_Theory = l_u16MarkPointNum_Theory * l_u16MarkDotNumPct /
            // l_u16MarkDot_Coeff;
            if (p_dist < 2500)
            {
                l_u16MarkPointNum_Theory = 0.005 * p_dist + 10;
            }
        }
    }
    else  //直接粗略的切割
    {
        if (g_sSysPib.m_u16MarkRadio > 70)  // *m_u16MarkRadio  未初始化，是否需要初始化
        {
            if (p_dist > 7000)
                l_u16MarkPointNum_Theory = 7 >> (g_navparam.AngMode - 1);
            else
                l_u16MarkPointNum_Theory = 10 >> (g_navparam.AngMode - 1);
        }
        else
        {
            if (p_dist > 7000)
                l_u16MarkPointNum_Theory = 3 >> (g_navparam.AngMode - 1);
            else
                l_u16MarkPointNum_Theory = 5 >> (g_navparam.AngMode - 1);
        }
    }
    return l_u16MarkPointNum_Theory;
}

void Mark_Rec::Filter_FakeMark_ByPointNum_WD(
    uint8_t p_MarkNum,
    STRUCT_FILTER_TARGET* p_Filter)  //根据靶标上的点数滤除假靶
{
    uint8_t l_u8i;
    uint16_t l_u16MarkPointNum_Theory;
    uint16_t l_u16MarkPointNum;
    for (l_u8i = 0; l_u8i < p_MarkNum; l_u8i++)
    {
        l_u16MarkPointNum_Theory =
            Cal_MarkDot_ByDist_Theory(p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist);
        // l_u16MarkPointNum = (p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanEnd + g_u16ScanPointNum -
        // p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanSta) % g_u16ScanPointNum;
        l_u16MarkPointNum = (p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanEnd
                             - p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanSta);
        if (l_u16MarkPointNum >= 0.6 * l_u16MarkPointNum_Theory)
            p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = ISMARK;
        else
            p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = NOTMARK;
    }
    // p_Filter->m_StructMarkInfo[p_MarkNum].m_u8IsMark = NOTMARK;
}

void Mark_Rec::Filter_SameMark_BySpeedRad(STRUCT_FILTER_TARGET* p_Filter)
{
    uint8_t l_u8size = p_Filter->m_u8In, l_u8i;
    uint16_t l_u16FilterMark_Ang = (uint16_t)(
        36000
        / (g_navparam.Ang_Resolution + g_navparam.Time_PerResolution * g_sInSpeedUsed.m_s32spdang));
    //逆向角速度为0时，一圈为7200个刻度，当有角度时，激光器实际转一圈刻度小于7200，从该l_u16FilterMark_Ang刻度到-7200的刻度值就是重复扫描的；
    if (g_sInSpeedUsed.m_s32spdang
        > 0)  //只有逆时针旋转时，才可能存在一个靶标被扫描两次的可能性，根据速度计算，当存在角速度时，靶标的角度大于某一个值，则代表该靶标一定是被多次扫描的
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            if (p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang > l_u16FilterMark_Ang)
                p_Filter->m_StructMarkInfo[l_u8size - 1].m_u8IsMark =
                    NOTMARK;  //可改为l_u8i，需要调试一下
        }
    }
}

uint32_t Mark_Rec::CopyFilterMark_WD(STRUCT_FILTER_TARGET* pDistOld, STRUCT_FILTER_TARGET* pDistNew)
{
    uint8_t l_u8cnt = 0;
    uint8_t l_u8i = 0, l_u8size = 0;
    //	uint16_t l_u16fixsize = 0;
    STRUCT_MARK_INFO *l_psMarkOld = NULL, *l_psMarkNew = NULL;

    l_psMarkOld = &pDistOld->m_StructMarkInfo[0];
    l_psMarkNew = &pDistNew->m_StructMarkInfo[0];
    l_u8size = pDistOld->m_u8In;

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        if ((l_psMarkOld + l_u8i)->m_u8IsMark == ISMARK)
        {
            //(l_psMarkOld + l_u8i)->m_u16Ang *= g_u8Ang_Resolution;	//统一成7200点
            (l_psMarkOld + l_u8i)->m_u8MarkSize = g_sSysPib.m_u16MarkRadio & 0xFF;
            (l_psMarkOld + l_u8i)->m_u8MarkType = g_sSysPib.m_u16MarkType & 0xFF;
            memcpy((void*)(l_psMarkNew + l_u8cnt),
                   (void*)(l_psMarkOld + l_u8i),
                   STRUCT_SIZE_MARK_INFO);
            if (l_u8cnt < TARGET_MAX)
            {
                g_au16MarkMaxPluseDistRatio[l_u8cnt] = (l_psMarkNew + l_u8cnt)->m_u16Ang;
            }
            l_u8cnt++;
        }
    }
    pDistNew->m_u8In = l_u8cnt;
    return 0;  //  FDH2020
}

void Mark_Rec::Find_MaxMin_Dist(uint8_t l_u8cnt,
                                uint16_t* Dist,
                                STRUCT_FILTER_TARGET* p_Filter,
                                uint16_t* max_dist,
                                uint16_t* min_dist)
{
    uint16_t l_u16i = 0, l_u16DistOld_max = 0, l_u16DistOld_min = 0, l_u16j = 0;
    uint16_t l_u16Dist = 0;
    //	uint16_t l_u16tmp = 0;
    for (l_u16i = 0; l_u16i < l_u8cnt; l_u16i++)
    {
        l_u16DistOld_max = Dist[p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta];
        l_u16DistOld_min = l_u16DistOld_max;
        max_dist[l_u16i] = l_u16DistOld_max;
        min_dist[l_u16i] = l_u16DistOld_min;
        for (l_u16j = p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta + 1;
             l_u16j <= p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanEnd;
             l_u16j++)
        {
            l_u16Dist = Dist[l_u16j % g_navparam.ScanPointNum];
            if (l_u16Dist > l_u16DistOld_max)  //距离
            {
                l_u16DistOld_max = l_u16Dist;
            }
            if ((l_u16Dist < l_u16DistOld_min) && (l_u16Dist != 0))
            {
                l_u16DistOld_min = l_u16Dist;
            }
        }
        max_dist[l_u16i] = l_u16DistOld_max;
        min_dist[l_u16i] = l_u16DistOld_min;
    }
}

uint16_t Mark_Rec::Find_IncludedAng_Min(uint16_t p_Ang_In1, uint16_t p_Ang_In2)
{
    uint16_t l_u16IncludedAng = 0;

    l_u16IncludedAng = (DEG_PERSCAN + p_Ang_In1 - p_Ang_In2) % DEG_PERSCAN;

    return (l_u16IncludedAng > 18000) ? (DEG_PERSCAN - l_u16IncludedAng) : l_u16IncludedAng;
}

uint16_t Mark_Rec::Return_FilterMinIncludedAng_WD(STRUCT_MARK_INFO* p_Mark1,
                                                  STRUCT_MARK_INFO* p_Mark2)
{
    uint16_t l_u16MarkDistDiff = abs(p_Mark1->m_u16Dist - p_Mark2->m_u16Dist);
    double l_f32Mul = 0;
    //	float l_f32DistMul ;

    if (p_Mark1->m_u16Dist < p_Mark2->m_u16Dist)
    {
        if (p_Mark1->m_u16Dist > 3000)
            l_f32Mul = p_Mark1->m_u16Dist / 2000.0;
        else  // if(p_Mark1->m_u16Dist > 1060)
            l_f32Mul = p_Mark1->m_u16Dist / 4000.0 + 0.75;
        //		else if(p_Mark1->m_u16Dist < 940)
        //			l_f32Mul = 0.5;
        //		else
        //			l_f32Mul = 1;

        //		l_f32DistMul = p_Mark1->m_u16Dist/6000.0;
        //		l_f32Mul = 1.1667 - l_f32DistMul ;
    }
    else
    {
        if (p_Mark2->m_u16Dist > 3000)
            l_f32Mul = p_Mark2->m_u16Dist / 2000.0;
        else  // if(p_Mark2->m_u16Dist > 1060)
            l_f32Mul = p_Mark2->m_u16Dist / 4000.0 + 0.75;
        //		else if(p_Mark2->m_u16Dist < 940)
        //			l_f32Mul = 0.5;
        //		else
        //			l_f32Mul = 1;
        //		if(p_Mark2->m_u16Dist>1060)
        //			l_f32Mul = 1000.0 / p_Mark2->m_u16Dist;
        //		else if(p_Mark1->m_u16Dist < 940)
        //			l_f32Mul = 2;
        //		else
        //			l_f32Mul = 0.5;
        //		l_f32DistMul = p_Mark2->m_u16Dist/6000.0;
        //		l_f32Mul = 1.1667 - l_f32DistMul;
    }

    l_f32Mul = l_f32Mul * pow(2, (g_navparam.AngMode - 1));

    if (l_u16MarkDistDiff < 1300)
        return (uint16_t)(((double)(l_u16MarkDistDiff * 2 / 15) + (double)(46)) / l_f32Mul);
    else if (l_u16MarkDistDiff < 3000)
        return (uint16_t)(((double)(256) - (double)(l_u16MarkDistDiff / 30)) / l_f32Mul);
    else
        return (uint16_t)((156) / l_f32Mul);
    //	if(l_u16MarkDistDiff < 1300)
    //		return ((l_u16MarkDistDiff*4/15) + 95)*l_f32Mul;
    //	else if(l_u16MarkDistDiff < 3000)
    //		return (515-l_u16MarkDistDiff/15)*l_f32Mul;
    //	else
    //		return (315)*l_f32Mul;

    //	if(l_u16MarkDistDiff < 2500)
    //		return ((l_u16MarkDistDiff>>2) + 750)*l_f32Mul;
    //	else
    //		return (1220 - l_u16MarkDistDiff/20)*l_f32Mul;
}

void Mark_Rec::DeletCloseMark_WD(STRUCT_FILTER_TARGET* p_Filter,
                                 uint16_t* p_Dist,
                                 uint16_t* p_PulseDistRatio)
{
    uint8_t l_u8i;
    uint8_t l_u8NextMarkPoint = 0;
    uint8_t l_u8size = p_Filter->m_u8In;
    uint16_t l_u16MinAngBetweenMarks = 100;
    uint32_t l_u32diff_Ang = 0;
    g_u16MinIncludedAng = 20 >> (g_navparam.AngMode - 1);  // 1°=20*0.05或10*0.1
    if (l_u8size == 1)
        return;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u8NextMarkPoint = (l_u8i + 1) % l_u8size;

        l_u32diff_Ang =
            Find_IncludedAng_Min(p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang,
                                 p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Ang);
        l_u16MinAngBetweenMarks = Return_FilterMinIncludedAng_WD(
            &p_Filter->m_StructMarkInfo[l_u8i], &p_Filter->m_StructMarkInfo[l_u8NextMarkPoint]);
        if (l_u16MinAngBetweenMarks < g_u16MinIncludedAng)
            l_u16MinAngBetweenMarks = g_u16MinIncludedAng;  //最小的夹角为1°，20个刻度
        while (1)  //循环直至某一靶标与该靶标大于某一夹角，则后面的靶标不可能需要过滤。
        {
            if (  // Points_Diff_Cmp(l_u32diff_Dist , l_u16MinDistBetweenMarks)&&
                Points_Diff_Cmp(l_u32diff_Ang, l_u16MinAngBetweenMarks))  //两靶间最小间距
            {
                if (Points_Diff_Cmp(l_u32diff_Ang,
                                    g_u16MinIncludedAng)  //距离小，夹角也小的时候，两个靶标都过滤
                    && abs(p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist
                           - p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Dist)
                           <= g_navparam.DistDiffMax)
                {
                    p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = ISMARK_TOOCLOSE;
                    p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u8IsMark = ISMARK_TOOCLOSE;
                }
                else  //否则只过滤远处的靶标
                {
                    if (p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist
                        > p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Dist)
                    {
                        p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = ISMARK_TOOCLOSE;
                    }
                    else
                    {
                        p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u8IsMark = ISMARK_TOOCLOSE;
                    }
                }

                l_u8NextMarkPoint = (l_u8NextMarkPoint + 1) % l_u8size;
                if (l_u8NextMarkPoint == l_u8i)
                    break;
                l_u32diff_Ang =
                    Find_IncludedAng_Min(p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang,
                                         p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Ang);
                l_u16MinAngBetweenMarks =
                    Return_FilterMinIncludedAng_WD(&p_Filter->m_StructMarkInfo[l_u8i],
                                                   &p_Filter->m_StructMarkInfo[l_u8NextMarkPoint]);
                if (l_u16MinAngBetweenMarks < g_u16MinIncludedAng)
                    l_u16MinAngBetweenMarks = g_u16MinIncludedAng;
            }
            else
                break;
        }
    }
}

void Mark_Rec::Renew_MarkCenter_PulseDist(uint16_t p_Max_Width_Point,
                                          uint16_t* p_PulseDistRatio,
                                          STRUCT_MARK_INFO* p_MarkInfo,
                                          uint16_t* p_dist)
{
    //	int8_t l_s8Point_Type = 0;	//起始点 =1 ,结束点=-1;
    //	uint16_t l_u16i = 0;
    uint16_t l_u16Start = 0, l_u16End = 0;
    //	uint16_t l_u16Ang = 0;
    uint16_t l_u16Width_80Percent;
    uint16_t l_u16MarkCenter;

    l_u16Width_80Percent = p_PulseDistRatio[p_Max_Width_Point] * 9 / 10;

    l_u16Start = Find_80Percent_Sta_Point_PulseDist(
        p_PulseDistRatio, p_Max_Width_Point, l_u16Width_80Percent);  //寻找80%脉宽起始点

    l_u16End = Find_80Percent_End_Point_PulseDist(
        p_PulseDistRatio, p_Max_Width_Point, l_u16Width_80Percent);  //寻找80%脉宽结束点

    if (l_u16Start > l_u16End)
        l_u16End += g_navparam.ScanPointNum;  //求靶标中间点((sta+end)/2),加1是向上取整
    p_MarkInfo->m_u16ScanSta = l_u16Start;
    p_MarkInfo->m_u16ScanEnd = l_u16End;
    l_u16MarkCenter = ((l_u16Start + l_u16End) >> 1) % g_navparam.ScanPointNum;
    p_MarkInfo->m_u16Ang = l_u16MarkCenter;
    p_MarkInfo->m_u16Dist = p_dist[l_u16MarkCenter];
}

void Mark_Rec::Find_MaxMin_Dist_Sigle(uint16_t* Dist,
                                      STRUCT_MARK_INFO* p_MarkInfo,
                                      uint16_t* p_DistMax,
                                      uint16_t* p_DistMin)
{
    uint16_t l_u16DistOld_max = 0, l_u16DistOld_min = 0, l_u16j = 0;
    uint16_t l_u16Dist = 0;
    //	uint16_t l_u16tmp = 0;

    l_u16DistOld_max = Dist[p_MarkInfo->m_u16ScanSta];
    l_u16DistOld_min = l_u16DistOld_max;

    for (l_u16j = p_MarkInfo->m_u16ScanSta + 1; l_u16j <= p_MarkInfo->m_u16ScanEnd; l_u16j++)
    {
        l_u16Dist = Dist[l_u16j % g_navparam.ScanPointNum];
        if (l_u16Dist > l_u16DistOld_max)  //距离
        {
            l_u16DistOld_max = l_u16Dist;
        }
        if ((l_u16Dist < l_u16DistOld_min) && (l_u16Dist != 0))
        {
            l_u16DistOld_min = l_u16Dist;
        }
    }
    *p_DistMax = l_u16DistOld_max;
    *p_DistMin = l_u16DistOld_min;
}

uint8_t Mark_Rec::FilterMark_ByMarkMaxMinDist_WD(STRUCT_FILTER_TARGET* p_Filter,
                                                 uint16_t* p_Dist,
                                                 uint16_t* p_PulseDistRatio,
                                                 uint16_t* l_min_dist)
{
    uint16_t l_u16distdiff = 0;
    //	uint16_t l_u16DistDiffMax = (g_u8MaxMarkRadio>>1)* g_sMarkMatch_Set.m_u16FilterMul + 60;
    uint16_t l_u16DistMax, l_u16DistMin;
    //	if(g_sMarkMatch_Set.m_u16FilterMul == 0)
    //		return 0;
    for (uint8_t l_u8i = 0; l_u8i < p_Filter->m_u8In; l_u8i++)
    {
        l_u16distdiff = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist
                        - l_min_dist[l_u8i];  // g_au16max_dist[l_u8i] - g_au16min_dist[l_u8i] ;
        if ((p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark == ISMARK)
            && (l_u16distdiff
                > g_navparam
                      .DistDiffMax)  //靶上的最近距离与中心距离大于某一阈值，则重新对该靶进行起始点和终止点的查找，缩小ref百分比80->90
        )
        {
            //			//Find_MarkCenterByPulseAndDist(p_Dist,p_Pluse,p_ScanMark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]);
            Renew_MarkCenter_PulseDist(g_au16MarkMaxPluseDistRatio[l_u8i],
                                       p_PulseDistRatio,
                                       &p_Filter->m_StructMarkInfo[l_u8i],
                                       p_Dist);
            Find_MaxMin_Dist_Sigle(
                p_Dist, &p_Filter->m_StructMarkInfo[l_u8i], &l_u16DistMax, &l_u16DistMin);
            l_u16distdiff = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist - l_u16DistMin;
            if (l_u16distdiff > g_navparam.DistDiffMax)
                p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = NOTMARK;
        }
    }
    return 0;
}

void Mark_Rec::Filter_Mark_ByScanRadius(STRUCT_FILTER_TARGET* p_Filter)
{
    uint8_t l_u8size = p_Filter->m_u8In;
    uint8_t l_u8i;
    uint16_t l_u16Dist = 0;
    uint8_t* l_u8IsMarkFlag = NULL;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u16Dist = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist;
        l_u8IsMarkFlag = &p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark;
        if ((ISMARK == *l_u8IsMarkFlag)
            && ((l_u16Dist > g_sMarkMatch_Set.m_u32MarkScan_Max)
                || (l_u16Dist < g_sMarkMatch_Set.m_u32MarkScan_Min)))
        {
            *l_u8IsMarkFlag = NOTMARK;
        }
    }
}

void Mark_Rec::FixMarkAng_PINGXINGZHOU(STRUCT_MARK_INFO* p_MarkInfo)
{
    uint32_t l_u32dist;
    uint16_t l_u16ang;
    float l_f32tmp;
    l_u32dist = p_MarkInfo->m_u16Dist;
    if (l_u32dist < 50)
        return;
    l_f32tmp = atan2(
        13.75,
        l_u32dist);  // 13为平行轴间的距离一半，因为测距是到机器正中心，则角度也补偿至机器正中心
    if (l_f32tmp < -0.000000001)
        l_f32tmp = 0;
    l_u16ang = (uint16_t)(l_f32tmp / g_navparam.Per10mDeg_Rad);
    p_MarkInfo->m_u16Ang = (p_MarkInfo->m_u16Ang + l_u16ang) % DEG_PERSCAN;
}

void Mark_Rec::CodeZeroCorrect(STRUCT_FILTER_TARGET* pFilter)
{
    uint8_t l_u8i = 0;
    int8_t l_s8PXCorr = 0;
    uint8_t l_u8size = 0;
    //	int16_t l_s16tmp=0;
    STRUCT_MARK_INFO* l_psMark = NULL;
    uint32_t l_u32PXCorr_Offset = 0;

    l_u8size = pFilter->m_u8In;
    if ((g_sSysPib.PianXinCorrEN == 1) && (g_navparam.PianXinFlag == 1))
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            l_u32PXCorr_Offset =
                (g_navparam.ScanPointNum
                 + (pFilter->m_StructMarkInfo[l_u8i].m_u16Ang << (g_navparam.AngMode - 1))
                 + g_sSysPib.Eccentric_Ang)
                % g_navparam
                      .ScanPointNum;  //零刻线偏移8->16hz由FPGA内部除2，偏心修正刻度在16hz时要乘以2使其变成0-7200刻度。
            l_psMark = &pFilter->m_StructMarkInfo[l_u8i];
            l_psMark->m_u16Ang *= g_navparam.Ang_Resolution;
            if (l_psMark->m_u8IsMark == ISMARK)
            {
                //				Flash_Read_Byte(ADDR_FLASH_PIANXIN_DATA + l_u32PXCorr_Offset, 1 ,
                //(uint8_t
                //*)&l_s8PXCorr);
                // l_s8PXCorr = g_EccentList[l_u32PXCorr_Offset];
                l_psMark->m_u16Ang = (DEG_PERSCAN + l_psMark->m_u16Ang + l_s8PXCorr) % DEG_PERSCAN;
                FixMarkAng_PINGXINGZHOU(l_psMark);
            }
        }
    }
    else
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            l_psMark = &pFilter->m_StructMarkInfo[l_u8i];
            l_psMark->m_u16Ang *= g_navparam.Ang_Resolution;
        }
    }
}

uint32_t Mark_Rec::CopyFilterMark(STRUCT_FILTER_TARGET* pDistOld, STRUCT_FILTER_TARGET* pDistNew)
{
    uint8_t l_u8cnt = 0;
    uint8_t l_u8i = 0, l_u8size = 0;
    uint16_t l_u16fixsize = 0;
    STRUCT_MARK_INFO *l_psMarkOld = NULL, *l_psMarkNew = NULL;

    l_psMarkOld = &pDistOld->m_StructMarkInfo[0];
    l_psMarkNew = &pDistNew->m_StructMarkInfo[0];
    l_u8size = pDistOld->m_u8In;

    //
    //根据靶标的类型修正距离值 在外部修正靶标距离值
    // switch (g_sSysPib.m_u16MarkType)
    // {
    //     case MARK_TYPE_CYC: l_u16fixsize = g_sSysPib.m_u16MarkRadio >> 1; break;
    //     case MARK_TYPE_FACE: l_u16fixsize = 0; break;
    //     default: break;
    // }

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        if ((l_psMarkOld + l_u8i)->m_u8IsMark == ISMARK)
        {
            //(l_psMarkOld + l_u8i)->m_u16Ang *= g_u8Ang_Resolution;	//统一成7200点
            (l_psMarkOld + l_u8i)->m_u8MarkSize = (uint8_t)g_sSysPib.m_u16MarkRadio;
            (l_psMarkOld + l_u8i)->m_u8MarkType = (uint8_t)g_sSysPib.m_u16MarkType;
            // (l_psMarkOld + l_u8i)->m_u16Dist += l_u16fixsize; //在外部修正靶标距离值
            memcpy((void*)(l_psMarkNew + l_u8cnt),
                   (void*)(l_psMarkOld + l_u8i),
                   STRUCT_SIZE_MARK_INFO);
            l_u8cnt++;
        }
    }
    pDistNew->m_u8In = l_u8cnt;
    return 0;
}

void Mark_Rec::Corr_Speed(INPUTSPEED* p_SpeedIn,
                          float p_CorrTime,
                          SPEED* p_SpeedOut,
                          float p_LastMoveTime)
{
    float l_f32CorrAng =
        (p_LastMoveTime + p_CorrTime) / 2 * p_SpeedIn->m_s32spdang * g_navparam.Per10mDeg_Rad
        + p_SpeedIn->m_f32spAngDirection;
    float l_fCos = (float)cos(l_f32CorrAng);
    float l_fSin = (float)sin(l_f32CorrAng);

    if (p_SpeedIn->m_s32spdang != 0)
    {
        p_SpeedOut->m_s16speedx = p_SpeedIn->m_f32spLine_Abs * l_fCos;
        p_SpeedOut->m_s16speedy = p_SpeedIn->m_f32spLine_Abs * l_fSin;
    }
    else  //没角速度不修正
    {
        p_SpeedOut->m_s16speedx = p_SpeedIn->m_StructSpeed_absolute.m_s16speedx;
        p_SpeedOut->m_s16speedy = p_SpeedIn->m_StructSpeed_absolute.m_s16speedy;
    }
}

float Calculate_Point2Point_Dist_Float(float p_Point_1, float p_Point_2)
{
    float l_f64Multiply_Point_1 = 0;
    float l_f64Multiply_Point_2 = 0;

    l_f64Multiply_Point_1 = p_Point_1 * p_Point_1;
    l_f64Multiply_Point_2 = p_Point_2 * p_Point_2;

    return (float)sqrt((double)(l_f64Multiply_Point_1) + (double)(l_f64Multiply_Point_2));
}

uint64_t Mark_Rec::Calculate_Point2Point_Dist_Int(int32_t p_Point_1, int32_t p_Point_2)
{
    uint64_t l_u64Multiply_Point_1 = 0;
    uint64_t l_u64Multiply_Point_2 = 0;

    l_u64Multiply_Point_1 = (uint64_t)(p_Point_1) * (uint64_t)(p_Point_1);
    l_u64Multiply_Point_2 = (uint64_t)(p_Point_2) * (uint64_t)(p_Point_2);

    return (uint64_t)sqrt((double)(l_u64Multiply_Point_1 + l_u64Multiply_Point_2));
}

void Mark_Rec::Mark_Corr_BySpeed(INPUTSPEED* p_Speed,
                                 uint8_t p_MarkNum,
                                 STRUCT_FILTER_TARGET* p_Filter,
                                 float p_CorrTime)
{
    uint8_t l_u8i, l_u8size;
    uint16_t l_u16MarkAng;
    //	uint16_t l_u16CorrAng = 0;
    float l_f32CorrTime_ms, l_f32RadCorred_BySLine;
    float l_f32CorrTime__NoSAng_ms = 0;
    int32_t l_s32CorrGrad_BySRad;
    //	uint16_t l_u16TargetPoint  = 0;
    float l_f32CorrLine_X = 0, l_f32CorrLine_Y = 0;
    float l_f32MarkRAD = 0;  //靶标弧度值
    // int32_t l_s32CoorLidar_X = 0, l_s32CoorLidar_Y = 0;
    float l_f32CoorLidar_X = 0, l_f32CoorLidar_Y = 0;
    float l_f32_2PI = (float)(M_PI * 2);
    uint16_t l_u16GradCorred_BySLine = 0;
    //	int8_t l_s8MarkPose_Type = 0;//靶标在修至目标点方位,大于目标点=1,小于目标点 = -1;

    l_u8size = p_MarkNum;  //靶标个数
    //当目标点不为0或者g_u16ScanPointNum-1时,修正被分为两段
    //	l_u16TargetPoint = 18000 - g_u8Ang_Resolution;//(g_u16ScanPointNum >> 1) -
    // 1;//修正目标点,也代表0-目标点,目标点为[0,g_u16ScanPointNum-1]之间值 	l_u16ResiduePoint =
    // 36000
    //- l_u16TargetPoint - g_u8Ang_Resolution * 2 ;	//剩余点,扫面点数-目标点
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        if (p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark == ISMARK)
        {
            l_u16MarkAng = p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang;
            l_f32CorrTime_ms =
                (g_navparam.ScanTime_HalfPer - g_navparam.Time_PerResolution * l_u16MarkAng);
            if (p_Speed->m_s32spdang != 0)  //如果有旋转，先将刻度修至0刻线时的刻度
            {
                l_f32CorrTime__NoSAng_ms = g_navparam.Time_PerResolution * l_u16MarkAng;
                l_s32CorrGrad_BySRad = (int32_t)(l_f32CorrTime__NoSAng_ms * p_Speed->m_s32spdang);

                l_u16MarkAng = (DEG_PERSCAN + l_u16MarkAng + l_s32CorrGrad_BySRad)
                               % DEG_PERSCAN;  //更新用于计算的MarkAng,去除了角速度的影响
            }

            l_f32CorrLine_X =
                p_Speed->m_StructSpeed_relative.m_s16speedx * l_f32CorrTime_ms;  //单位 mm/s  * 毫秒
            //			l_f32CorrLine_X = l_f32CorrLine_X * l_s8MarkPose_Type; //单位mm

            l_f32CorrLine_Y =
                p_Speed->m_StructSpeed_relative.m_s16speedy * l_f32CorrTime_ms;  //单位 mm/s  * 毫秒
            //			l_f32CorrLine_Y = l_f32CorrLine_Y * l_s8MarkPose_Type; //单位mm

            l_f32MarkRAD = g_navparam.Per10mDeg_Rad * l_u16MarkAng;

            l_f32CoorLidar_X = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist * cosf(l_f32MarkRAD);
            l_f32CoorLidar_Y = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist * sinf(l_f32MarkRAD);
            //			Round_Up_To_Int(&l_f32CoorLidar_X,&l_s32CoorLidar_X);
            //			Round_Up_To_Int(&l_f32CoorLidar_Y,&l_s32CoorLidar_Y);

            l_f32RadCorred_BySLine =
                (float)(atan2((double)(l_f32CoorLidar_Y) - (double)(l_f32CorrLine_Y),
                              (double)(l_f32CoorLidar_X) - (double)(l_f32CorrLine_X)));
            l_f32RadCorred_BySLine = fmodf(l_f32RadCorred_BySLine + l_f32_2PI, l_f32_2PI);

            l_u16GradCorred_BySLine = (uint16_t)(l_f32RadCorred_BySLine / g_navparam.Per10mDeg_Rad);

            p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang =
                (DEG_PERSCAN + l_u16GradCorred_BySLine
                 - (int32_t)(g_navparam.ScanTime_HalfPer * p_Speed->m_s32spdang))
                % DEG_PERSCAN;  //再补偿

            p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist = (uint16_t)Calculate_Point2Point_Dist_Int(
                (int32_t)(l_f32CoorLidar_Y - l_f32CorrLine_Y),
                (int32_t)(l_f32CoorLidar_X - l_f32CorrLine_X));
        }
        else
            continue;
    }
}

void Mark_Rec::Find_ScanMark_PluseDist(uint16_t* p_Ratio,
                                       uint16_t* p_dist,
                                       float p_CorrTime,
                                       STRUCT_CARVE* l_PLCarve_Copy,
                                       std::vector<STRUCT_MARK_INFO>& p_vecOutXY)
{
    uint16_t l_max_dist[TARGET_MAX];  //获取靶标最大最小距离//60
    uint16_t l_min_dist[TARGET_MAX];
    // Empty_UsingBuf();//70us
    memset((void*)&g_sFilterDist, 0, STRUCT_SIZE_FILTER);
    //寻找靶标脉宽最大点
    g_sFilterDist.m_u8In =
        Find_MaxWidthPoint_WD(p_Ratio, &g_sFilterDist, l_PLCarve_Copy, p_dist);  //*找出最大脉宽点

    Find_StaEnd_Point_RatioPulseDist(
        p_Ratio,
        g_sFilterDist.m_u8In,
        &g_sFilterDist,
        p_dist,
        g_u8Mul);  //寻找靶标起始和结束点//162us *根据上一步计算出来百分比重新寻找靶标起始点和结束点
                   //对靶标的中心点距离dis和Ang序号进行了更新

    Filter_FakeMark_ByPointNum_WD(g_sFilterDist.m_u8In,
                                  &g_sFilterDist);  //筛选真假靶标(靶标上点个数)//3

    Filter_SameMark_BySpeedRad(&g_sFilterDist);  //有角速度时，过滤被扫描两次的同一靶

    CopyFilterMark_WD(&g_sFilterDist, &g_sFilterDist);  //修正圆靶距离 赋值半径

    Find_MaxMin_Dist(g_sFilterDist.m_u8In,
                     p_dist,
                     &g_sFilterDist,
                     &l_max_dist[0],
                     &l_min_dist[0]);  //获取靶标最大最小距离//60

    DeletCloseMark_WD(&g_sFilterDist, p_dist, p_Ratio);  //夹角过滤，过滤较远的靶.

    FilterMark_ByMarkMaxMinDist_WD(&g_sFilterDist, p_dist, p_Ratio, l_min_dist);  //靶上距离过滤

    Filter_Mark_ByScanRadius(&g_sFilterDist);  //根据靶标探测半径过滤靶标//1

    CodeZeroCorrect(&g_sFilterDist);  //增加半径//3

    CopyFilterMark(&g_sFilterDist, &g_sFilterDist);  //修正圆靶距离 2根据半径修正距离
    // printf("当前帧有效靶标数  %d\n", g_sFilterDist.m_u8In);

    STRUCT_MARK_INFO* l_psMarkInfoAddr = &g_sFilterDist.m_StructMarkInfo[0];

    for (int i = 0; i < g_sFilterDist.m_u8In; i++)
    {
        p_vecOutXY.push_back(l_psMarkInfoAddr[i]);
    }
}

//计算两点距离
float calDis(pcl::PointXYZHSV p_Point1, pcl::PointXYZHSV p_Point2)
{
    float x1 = p_Point1.x;
    float y1 = p_Point1.y;
    float x2 = p_Point2.x;
    float y2 = p_Point2.y;
    return sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
}

template <typename PointTypePcPtr>
bool Mark_Rec::extractMark(s_PCloud p_pmarkScan,
                           float p_lidarRotateSpeedRad,
                           PointTypePcPtr p_ptrMarkCenters,
                           std::string p_sPath,
                           float p_iMarkDisCom)
{
    if (p_pmarkScan.m_padd->size() != 7200)
    {
        return false;
    }

    NAVDATABGL g_LDdataO;
    float l_time[7200] = {0};
    uint32_t l_dis[7200] = {0};

    PAdd pointAdd0 = (*p_pmarkScan.m_padd)[0];
    float l_fAng = pointAdd0.ang;
    int l_iId0 = std::round(l_fAng * 20);  //其实index

    for (int i = 0; i < p_pmarkScan.m_padd->size(); i++)
    {
        /* code */
        PAdd pointAdd = (*p_pmarkScan.m_padd)[i];

        int l_iId = (l_iId0 + i) % 7200;
        l_iId = 9000 - l_iId;

        int l_iDist = std::round((pointAdd.xydist * 1000)) + p_iMarkDisCom;
        if (l_iDist > 65535)
        {
            l_iDist = 65535;
        }

        g_LDdataO.LDdist[l_iId % 7200] = l_iDist;

        // g_LDdataO.PLWidth[l_iId % 7200] = (int)pointAdd.intensity * 100;
        // g_LDdataO.REL[l_iId % 7200] = (int)pointAdd.intensity * 100;
        g_LDdataO.PLWidth[l_iId % 7200] = (int)pointAdd.intensity;
        g_LDdataO.REL[l_iId % 7200] = (int)pointAdd.intensity;
        l_time[l_iId % 7200] = pointAdd.time;
    }

    STRUCT_CARVE g_PLCarve_Copy;
    GetOutlineOfMarks_WD(g_LDdataO.PLWidth, g_LDdataO.LDdist, &g_PLCarve_Copy, g_LDdataO.REL);
    std::vector<STRUCT_MARK_INFO> l_vecMarkXY;
    Find_ScanMark_PluseDist(g_LDdataO.REL, g_LDdataO.LDdist, 100, &g_PLCarve_Copy, l_vecMarkXY);

    // printf("mark size-%ld \n", l_vecMarkXY.size());

    p_ptrMarkCenters->clear();
    // p_ptrMarkCenters->resize(l_vecMarkXY.size());

    static int ScanId = 0;
    static int MarkId = 1;
    std::vector<pcl::PointXYZHSV> l_trueMap;
    if (ScanId == 0)
    {
        std::fstream f;
        f.open(p_sPath + "/rtkMap.txt", std::ios::in);
        if (!f.is_open())
        {
            std::cout << "not load rkt map" << std::endl;
        }
        else
        {
            float l_data[4] = {0};
            //读取
            std::string _line;
            while (!f.eof())
            {
                getline(f, _line);
                std::stringstream l_ss(_line);  //初始化 法1
                double l_dTemp;
                int l_id = 0;
                while (l_ss >> l_dTemp)
                {  //每一行包含不同个数的数字
                    l_data[l_id] = l_dTemp;
                    l_id++;
                }
                if (l_id < 4)
                {
                    continue;
                }

                pcl::PointXYZHSV l_temp;
                l_temp.x = l_data[1];
                l_temp.y = l_data[2];
                l_temp.z = l_data[3];
                l_temp.h = MarkId;
                MarkId++;
                p_ptrMarkCenters->points.push_back(l_temp);

                l_trueMap.push_back(l_temp);
            }
            std::cout << "load rkt map file success" << std::endl;
        }
    }

    for (size_t i = 0; i < l_vecMarkXY.size(); i++)
    {
        uint16_t l_u16dist = l_vecMarkXY[i].m_u16Dist;
        uint16_t l_u16ang = l_vecMarkXY[i].m_u16Ang;  // 10mdeg为单位  + 27000 +5 -5

        // uint32_t l_u16dist2 = l_dis[l_u16ang/5];
        // if (l_u16dist2!=l_u16dist)
        // {
        //     // std::cout<<"l_u16dist:"<<l_u16dist<<" l_u16dist2:"<<l_u16dist2<<std::endl;
        //     l_u16dist=l_u16dist2;
        // }
        if (l_u16dist > 60000)
        {
            continue;
        }

        float l_f32rad = Rad_Per_10mDeg * l_u16ang;
        float m_s32x = (l_u16dist * cos(l_f32rad));
        float m_s32y = (l_u16dist * sin(l_f32rad));

        pcl::PointXYZHSV l_temp;
        l_temp.x = m_s32x / 1000.0;
        l_temp.y = m_s32y / 1000.0;
        l_temp.z = 0;
        // p_ptrMarkCenters->points[i].h = 0;
        // p_ptrMarkCenters->points[i].s = l_u16dist;
        // p_ptrMarkCenters->points[i].v = l_u16ang;

        // 第一帧不进匹配可能存在靶标
        if (ScanId == 0)
        {
            int l_iFlag = 0;
            for (int i = 0; i < (int)l_trueMap.size(); i++)
            {
                if (calDis(l_temp, l_trueMap[i]) < 1)
                {
                    l_iFlag = 1;
                }
            }
            if (l_iFlag == 1)
            {
                continue;
            }

            l_temp.h = MarkId;
            MarkId++;
        }
        else
        {
            l_temp.h = -1;
        }

        l_temp.s = l_time[int16_t(l_u16ang / 5)];
        l_temp.v = l_f32rad;

        p_ptrMarkCenters->points.push_back(l_temp);
    }
    ScanId++;
    return true;
}
// #pragma optimize("",on)