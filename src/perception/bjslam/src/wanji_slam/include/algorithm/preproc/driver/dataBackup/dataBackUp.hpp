/**
 * @file dataBackUp.hpp
 * <AUTHOR>
 * @brief 数据备份
 * @version 1.1
 * @date 2023-06-25
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once
#include "algorithm/map/secret_map/laserIO.h"
#include "common/common_ex.h"
#include "dataManage.h"

using namespace std;
namespace wj_slam {
template <typename C, typename M> class DataBackUp {
  public:
    typedef pcl::PointXYZ POSE;
    typedef pcl::PointXYZHSV PCurXYZHSV;
    typedef boost::shared_ptr<DataBackUp> Ptr;
    typedef boost::shared_ptr<KEYFRAME<C>> KEYFRAME_PTR;
    typedef boost::shared_ptr<LaserIO<C, POSE>> LaserIOPtr;
    typedef boost::shared_ptr<DataManage> DataManagePtr;

  private:
    bool c_bRun_;                  /**< 运行标志 */
    bool c_bIsExistDir_;           /**< 是否存在路径 */
    bool c_bSaveData_;             /**< 是否保存数据 */
    uint32_t c_uiBAKFrameNum_;     /**< 备份10帧特征点 */
    uint32_t c_uiSaveBAKFrameNum_; /**< 保存特征点帧数 */
    string c_sLogDataPath_;        /**< 保存的文件夹位置 */
    string c_sSaveTimeName_;       /**< 保存的文件夹 时间 名 */
    string c_sSavePath_; /**< 增加其他信息 eg:异常雷达时间戳 等之后的完整路径 */
    std::mutex m_dataLock;                  /**< 锁 */
    std::deque<KEYFRAME_PTR> c_DataBuffer_; /* 特征点Buffer */
    LaserIOPtr c_pSaveWJ_;                  /**< LaserIO指针 */
    DataManagePtr c_dataControl_;           /**< DataManager指针 */

    /**
     * @brief 判断是否保存数据 
     * @param p_bIsStartSave    是否开始保存
     * @param p_iSaveFrameNum   保存帧数量
     * 
     */
    void judgeSaveData_(bool& p_bIsStartSave, int p_iSaveFrameNum)
    {
        if (p_bIsStartSave)
        {
            if ((int)c_DataBuffer_.size() >= p_iSaveFrameNum)
            {
                saveBakDataBuffer_(p_iSaveFrameNum);
                p_bIsStartSave = false;
            }
        }
    }

    /**
     * @brief 删除备份缓存中的数据
     * @param p_iRemainNum 备份数据-保留数量
     * 
     */
    void popBakDataBuffer_(int p_iRemainNum)
    {
        while ((int)c_DataBuffer_.size() > p_iRemainNum)
            c_DataBuffer_.pop_front();
    }

    /**
     * @brief 保存备份缓存中的数据
     * @param p_iSaveNum 备份数据-保留数量
     * 
     */
    void saveBakDataBuffer_(int p_iSaveNum)
    {
        int l_iSaveNum_ = p_iSaveNum;
        int l_iInd_ = 0;
        if (!makeDir(c_sSavePath_))
        {
            printf("备份失败: [%s]\n", c_sSavePath_.c_str());
            return;
        }
        if ((int)c_DataBuffer_.size() >= l_iSaveNum_)
            while (1)
            {
                if (c_DataBuffer_.empty() || !l_iSaveNum_)
                    break;
                saveBakData(c_DataBuffer_.at(l_iInd_++), c_sSavePath_);
                l_iSaveNum_--;
            }
        c_dataControl_->deleteOldFolder(c_sSaveTimeName_);
    }

    /**
     * @brief 保存备份数据
     * @param p_stData  数据
     * @param p_sPath   路径
     * 
     */
    void saveBakData(KEYFRAME_PTR p_stData, std::string p_sPath)
    {
        std::string l_sPath = p_sPath + std::to_string(p_stData->m_pFeature->m_tsSyncTime) + ".wj";
        std::vector<typename pcl::PointCloud<C>::Ptr> l_feature;
        std::vector<typename pcl::PointCloud<C>::Ptr> l_view;
        l_feature.push_back(p_stData->m_pFeature->first);
        l_feature.push_back(p_stData->m_pFeature->second);
        l_feature.push_back(p_stData->m_pFeature->fourth);
        pcl::PointCloud<POSE>::Ptr l_savePose(new pcl::PointCloud<POSE>());
        l_savePose->push_back(
            POSE(p_stData->m_Pose.x(), p_stData->m_Pose.y(), p_stData->m_Pose.z()));
        c_pSaveWJ_->writeBinary(l_sPath, l_savePose, l_feature, l_view);
    }

    /**
     * @brief 目录不存在则逐级创建
     * @param p_sDir 路径
     * @code 
     *    
     * @endcode 
     * @return [true] \n 
     * [创建成功]
     * @code 
     *    
     * @endcode 
     * @return [false] \n 
     * [创建失败]
     * 
     */
    bool makeDir(std::string p_sDir)
    {
        boost::filesystem::path l_path = boost::filesystem::path(p_sDir);
        if (boost::filesystem::exists(l_path) || boost::filesystem::create_directories(l_path))
            return true;
        return false;
    }

    /**
     * @brief 创建文件
     * @param p_sFolderPath     文件夹路径 
     * @param p_sTimeNamePath   时间文件夹路径
     * @param p_lidarT          雷达名  
     * @code 
     *    
     * @endcode 
     * @return [std::string] \n 
     * [文件路径]
     * 
     */
    std::string makePath_(std::string p_sFolderPath, std::string& p_sTimeNamePath, int p_lidarT)
    {
        std::string l_str;
        // 基于当前系统的当前日期/时间
        time_t now = time(0);
        tm* ltm = localtime(&now);
        std::string l_myear = std::to_string(1900 + ltm->tm_year);
        std::string l_mon = std::to_string(1 + ltm->tm_mon);
        if (1 + ltm->tm_mon < 10)
            l_mon = "0" + l_mon;
        std::string l_mday = std::to_string(ltm->tm_mday);
        if (ltm->tm_mday < 10)
            l_mday = "0" + l_mday;
        std::string l_mhour = std::to_string(ltm->tm_hour);
        if (ltm->tm_hour < 10)
            l_mhour = "0" + l_mhour;
        std::string l_mMin = std::to_string(ltm->tm_min);
        if (ltm->tm_min < 10)
            l_mMin = "0" + l_mMin;
        std::string l_mSec = std::to_string(ltm->tm_sec);
        if (ltm->tm_sec < 10)
            l_mSec = "0" + l_mSec;
        p_sTimeNamePath = l_myear + l_mon + l_mday + l_mhour + l_mMin + l_mSec;
        return p_sFolderPath + l_myear + l_mon + l_mday + l_mhour + l_mMin + l_mSec + "/"
               + std::to_string(p_lidarT) + "/";
    }

  public:
    /**
     * @brief 构造函数
     * @param p_sDataPath       数据路径 
     * @param p_iMaxFilesNum    最大文件个数
     * @param p_iBAKFrameNum    保存特征点帧数
     * 
     */
    DataBackUp(std::string p_sDataPath, int p_iMaxFilesNum, int p_iBAKFrameNum = 10)
        : c_bRun_(true), c_bIsExistDir_(false), c_bSaveData_(false),
          c_uiBAKFrameNum_(p_iBAKFrameNum), c_uiSaveBAKFrameNum_(0), c_sLogDataPath_(p_sDataPath),
          c_sSaveTimeName_(""), c_sSavePath_(""), c_pSaveWJ_(nullptr), c_dataControl_(nullptr)
    {
        // 路径不存在则创建
        if (c_sLogDataPath_[c_sLogDataPath_.length() - 1] != '/')
            c_sLogDataPath_ += "/";
        // 加密地图
        c_pSaveWJ_.reset(new LaserIO<C, POSE>());
        // 开启自动删除包
        c_dataControl_.reset(new DataManage(c_sLogDataPath_, p_iMaxFilesNum));
    }

    /**
     * @brief 析构函数
     * 
     */
    virtual ~DataBackUp()
    {
        c_bRun_ = false;
        std::lock_guard<std::mutex> l_mtx(m_dataLock);
        c_bSaveData_ = false;
        c_DataBuffer_.clear();
        c_pSaveWJ_ = nullptr;
        c_dataControl_ = nullptr;
    }

    /**
     * @brief 备份数据
     * @param p_stFeaturePtr 特征
     * 
     */
    void bakData(KEYFRAME_PTR p_stFeaturePtr)
    {
        if (!c_bRun_)
            return;
        std::lock_guard<std::mutex> l_mtx(m_dataLock);
        c_DataBuffer_.push_back(p_stFeaturePtr);
        // printf("备份队列[%d] T： %d T：%d\n", c_DataBuffer_.size(),
        // c_DataBuffer_.front()->m_pFeature->m_dTimestamp,
        // c_DataBuffer_.back()->m_pFeature->m_dTimestamp);
        judgeSaveData_(c_bSaveData_, c_uiSaveBAKFrameNum_);
        // 未启动保存时维持数量，保存期间累加， 保存完毕清空之前，
        // 如此当帧帧触发时可不重复保存，只保存新的
        if (!c_bSaveData_)
            popBakDataBuffer_(c_uiBAKFrameNum_);
    }

    /**
     * @brief 触发保存备份数据，此时开始不再pop，
     * @param p_iLidarTime         雷达时间
     * @param p_iByondFrameNum     帧数量阈值
     * 
     */
    void setBakDataSign(int p_iLidarTime, int p_iByondFrameNum)
    {
        if (!c_bRun_)
            return;
        std::lock_guard<std::mutex> l_mtx(m_dataLock);

        if (c_bSaveData_)
        {
            // 已触发保存但未保存，即等待后续帧数量满足p_iByondFrameNum，则一并保存，且数量不再增，防止丢定位后疯狂累计
            // 但无法保存
            // LOGW(WWARN, "{} 重复触发异常保存机制 | T:{}", WJLog::getWholeSysTime(),
            // p_iLidarTime);
            return;
        }
        // 设定保存路径 "xxx/data/Log/LidarBak/20221031092701/p_iLidarTime/"
        c_sSavePath_ = makePath_(c_sLogDataPath_, c_sSaveTimeName_, p_iLidarTime);
        // 继续保留后续5帧
        c_uiSaveBAKFrameNum_ = c_DataBuffer_.size() + p_iByondFrameNum;
        if (c_uiSaveBAKFrameNum_ < c_uiBAKFrameNum_)
            c_uiSaveBAKFrameNum_ = c_uiBAKFrameNum_;
        LOGW(WERROR, "{} 雷达 帧[{}] 触发异常保存机制", WJLog::getWholeSysTime(), p_iLidarTime);
        // if(c_DataBuffer_.size() != 0)
        //     printf("触发异常保存机制: 当前Size: %d | 预保存Size: %d | 当前T: %d | 队列T: %d
        //     %d\n",
        //         c_DataBuffer_.size(),
        //         c_uiSaveBAKFrameNum_,
        //         p_iLidarTime,
        //         c_DataBuffer_.begin()->m_pFeature->m_dTimestamp,
        //         c_DataBuffer_.end()->m_pFeature->m_dTimestamp);
        c_bSaveData_ = true;
    }
};
}  // namespace wj_slam