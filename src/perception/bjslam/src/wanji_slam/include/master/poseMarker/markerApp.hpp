#pragma once
#include "common/type/type_pose.h"
#include "tool/protocolTool/protocolTool.h"
#include <boost/bind.hpp>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <interactive_markers/interactive_marker_server.h>
#include <interactive_markers/menu_handler.h>
#include <math.h>
#include <ros/ros.h>
#include <std_msgs/Int32MultiArray.h>
#include <tf/tf.h>
#include <tf/transform_broadcaster.h>
#include <visualization_msgs/InteractiveMarker.h>
#include <visualization_msgs/Marker.h>

class MarkerApp {
  private:
    boost::function<void(wj_slam::s_POSE6D)> c_setPoseCb_;
    boost::shared_ptr<interactive_markers::InteractiveMarkerServer> c_markerServer_;
    interactive_markers::MenuHandler c_menuHandler_;
    geometry_msgs::Pose c_PoseUI_;
    std::string c_sMarkerName = "SetPoseUI";

    void init_()
    {
        c_PoseUI_.position.x = c_PoseUI_.position.y = c_PoseUI_.position.z = 0.0;
        c_PoseUI_.orientation.x = c_PoseUI_.orientation.y = c_PoseUI_.orientation.z = 0.0;
        c_PoseUI_.orientation.w = 1.0;

        c_markerServer_.reset(new interactive_markers::InteractiveMarkerServer("UI", "", false));

        c_menuHandler_.insert("设定位姿", boost::bind(&MarkerApp::menuSetPoseCb_, this, _1));
        c_menuHandler_.insert("重置标记", boost::bind(&MarkerApp::menuResetPoseCb_, this, _1));

        tf::Vector3 position;
        position = tf::Vector3(0, 0, 0);
        make6DofMarker_(
            false, visualization_msgs::InteractiveMarkerControl::MOVE_ROTATE_3D, position, true);

        c_markerServer_->applyChanges();
    }

    void menuSetPoseCb_(const visualization_msgs::InteractiveMarkerFeedbackConstPtr& feedback)
    {
        switch (feedback->event_type)
        {
            case visualization_msgs::InteractiveMarkerFeedback::BUTTON_CLICK: break;

            case visualization_msgs::InteractiveMarkerFeedback::MENU_SELECT:
            {
                double l_f64Yaw = tf::getYaw(c_PoseUI_.orientation) * 180.0 / M_PI;
                wj_slam::s_POSE6D l_pose;
                l_pose.setX(c_PoseUI_.position.x);
                l_pose.setY(c_PoseUI_.position.y);
                l_pose.setZ(0);
                l_pose.setRPY(0, 0, l_f64Yaw);
                c_setPoseCb_(l_pose);
                break;
            }
            case visualization_msgs::InteractiveMarkerFeedback::POSE_UPDATE: break;

            case visualization_msgs::InteractiveMarkerFeedback::MOUSE_DOWN: break;

            case visualization_msgs::InteractiveMarkerFeedback::MOUSE_UP:
                //鼠标移动后触发该函数，更新Pose
                c_PoseUI_ = feedback->pose;
                c_PoseUI_.position.z = 0;
                c_markerServer_->setPose(feedback->marker_name, c_PoseUI_);
                c_markerServer_->applyChanges();
                break;
        }

        c_markerServer_->applyChanges();
    }

    void menuResetPoseCb_(const visualization_msgs::InteractiveMarkerFeedbackConstPtr& feedback)
    {
        switch (feedback->event_type)
        {
            case visualization_msgs::InteractiveMarkerFeedback::MENU_SELECT:
                geometry_msgs::Pose l_pose;
                c_markerServer_->setPose(feedback->marker_name, l_pose);
                c_markerServer_->applyChanges();
                break;
        }
        c_markerServer_->applyChanges();
    }

    visualization_msgs::Marker makeBox_(visualization_msgs::InteractiveMarker& msg)
    {
        visualization_msgs::Marker marker;

        marker.type = visualization_msgs::Marker::ARROW;
        // marker.scale.x = msg.scale * 0.9;
        // marker.scale.y = msg.scale * 0.3;
        // marker.scale.z = msg.scale * 0.3;

        marker.scale.x = msg.scale * 5;
        marker.scale.y = msg.scale * 1;
        marker.scale.z = msg.scale * 1;

        marker.color.r = 1.0;
        marker.color.g = 0.0;
        marker.color.b = 0.0;
        marker.color.a = 1.0;

        return marker;
    }

    visualization_msgs::InteractiveMarkerControl&
    makeBoxControl_(visualization_msgs::InteractiveMarker& msg)
    {
        visualization_msgs::InteractiveMarkerControl control;
        control.always_visible = true;
        control.markers.push_back(makeBox_(msg));
        msg.controls.push_back(control);

        return msg.controls.back();
    }

    void make6DofMarker_(bool fixed,
                         unsigned int interaction_mode,
                         const tf::Vector3& position,
                         bool show_6dof)
    {
        visualization_msgs::InteractiveMarker int_marker;
        int_marker.header.frame_id = "world";
        tf::pointTFToMsg(position, int_marker.pose.position);
        int_marker.scale = 1;

        int_marker.name = c_sMarkerName;
        // int_marker.description = "6-DOF Control";

        // insert a box
        makeBoxControl_(int_marker);
        int_marker.controls[0].interaction_mode = interaction_mode;

        visualization_msgs::InteractiveMarkerControl control;

        // if (fixed)
        // {
        //     //int_marker.name += "_fixed";
        //     //int_marker.description += "\n(fixed orientation)";
        //     control.orientation_mode = InteractiveMarkerControl::FIXED;
        // }

        if (interaction_mode != visualization_msgs::InteractiveMarkerControl::NONE)
        {
            // std::string mode_text;
            // if (interaction_mode == visualization_msgs::InteractiveMarkerControl::MOVE_3D)
            //     mode_text = "MOVE_3D";
            // if (interaction_mode == visualization_msgs::InteractiveMarkerControl::ROTATE_3D)
            //     mode_text = "ROTATE_3D";
            // if (interaction_mode == visualization_msgs::InteractiveMarkerControl::MOVE_ROTATE_3D)
            //     mode_text = "MOVE_ROTATE_3D";
            // int_marker.name += "_" + mode_text;
            // int_marker.description = std::string("3D Control") + (show_6dof ? " + 6-DOF controls"
            // :
            // "") + "\n" + mode_text;
            // int_marker.description = "";
        }

        if (show_6dof)
        {
            tf::Quaternion orien(1.0, 0.0, 0.0, 1.0);
            orien.normalize();
            tf::quaternionTFToMsg(orien, control.orientation);
            control.name = "move_x";
            control.always_visible = true;
            control.interaction_mode = visualization_msgs::InteractiveMarkerControl::MOVE_AXIS;
            int_marker.controls.push_back(control);

            orien = tf::Quaternion(0.0, 1.0, 0.0, 1.0);
            orien.normalize();
            tf::quaternionTFToMsg(orien, control.orientation);
            control.name = "rotate_z";
            control.always_visible = true;
            control.interaction_mode = visualization_msgs::InteractiveMarkerControl::ROTATE_AXIS;
            int_marker.controls.push_back(control);

            orien = tf::Quaternion(0.0, 0.0, 1.0, 1.0);
            orien.normalize();
            tf::quaternionTFToMsg(orien, control.orientation);
            control.name = "move_y";
            control.always_visible = true;
            control.interaction_mode = visualization_msgs::InteractiveMarkerControl::MOVE_AXIS;
            int_marker.controls.push_back(control);
        }

        control.name = "menu_setPose";
        control.interaction_mode = visualization_msgs::InteractiveMarkerControl::MENU;
        int_marker.controls.push_back(control);

        control.interaction_mode = visualization_msgs::InteractiveMarkerControl::MOVE_PLANE;
        control.always_visible = true;
        int_marker.controls.push_back(control);

        c_markerServer_->insert(int_marker);
        c_markerServer_->setCallback(int_marker.name,
                                     boost::bind(&MarkerApp::menuSetPoseCb_, this, _1));

        c_menuHandler_.apply(*c_markerServer_, int_marker.name);
    }

  public:
    MarkerApp(boost::function<void(wj_slam::s_POSE6D)> setPoseCb) : c_setPoseCb_(setPoseCb)
    {
        init_();
    }
    ~MarkerApp()
    {
        c_markerServer_.reset();
        c_markerServer_ = nullptr;
    }

    void resetPoseUI(wj_slam::s_POSE6D p_pose)
    {
        geometry_msgs::Pose l_pose;
        l_pose.position.x = p_pose.x();
        l_pose.position.y = p_pose.y();
        l_pose.position.z = p_pose.z();
        l_pose.orientation.x = l_pose.orientation.y = l_pose.orientation.z = 0.0;
        l_pose.orientation.w = 1.0;
        c_markerServer_->setPose(c_sMarkerName, l_pose);
        c_markerServer_->applyChanges();
    }
};