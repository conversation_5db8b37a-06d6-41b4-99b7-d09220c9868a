/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-18 13:35:27
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 13:25:23
 */
#ifndef _VIRTUAL_DRIVER_H_
#define _VIRTUAL_DRIVER_H_

#include "common/common_master.h"
#include "input_offline.h"
#include "tic_toc.h"
#include <boost/bind.hpp>
#include <pcl/point_types.h>
#include <string>
namespace wj_slam {
class VirtualDriver {
  private:
    typedef boost::shared_ptr<s_LIDAR_RAW_DATA_> RawDataPtr;
    typedef std::pair<uint32_t, RawDataPtr> RawDataPair;
    std::vector<boost::shared_ptr<InputOffLine>> c_vpInput_;
    std::vector<boost::shared_ptr<RateLimiterr>> c_vpToken_;
    std::vector<RawDataPair> c_vRawDataOffline_;
    std::vector<bool> c_vbDevValid_;

    std::mutex cLock;
    s_masterCfg* c_masterParamPtr = nullptr;
    bool c_bRunOlpThr = false;
    bool c_bRunOlpThrOver = false;
    bool c_bIsStart_ = false;
    bool c_bShutdown_ = false;

    /**
     * @description: 初始化驱动-支持在线切换，即重置
     * @param {*}
     * @return {*}
     * @other:
     */
    void initDriver_();

    /**
     * @description: 启动/关闭 数据对齐线程
     * @param {bool} p_bIsRestart 是否启动
     * @return {*}
     * @other:
     */
    void resetTimeAlignThread_(bool p_bIsRestart);

    /**
     * @description: 根据数据涵盖的时间戳，手动排序控制其顺序
     * @param {*}
     * @return {*}
     * @other:
     */
    void dataTimeAlign_();

    /**
     * @description: 各个设备数据量是否均满足排序需求
     * @param {*}
     * @return {bool} true: 满足排序
     * @other: 每个设备数据量应不低于2
     */
    bool isEnoughSort_();

    /**
     * @description: 是否须重新排序
     * @param {int} p_iNowSize 当前队列长度
     * @param {int&} p_iLastSize 上次队列长度
     * @return {*}
     * @other: 队列长度增加则须重新排序
     */
    bool isNeedSort_(int p_iNowSize, int& p_iLastSize);

    /**
     * @description: 回调函数-接收原始网络数据，等待压入权限，压入离线排序队列
     * @param {uint32_t} p_uiDevID 设备ID
     * @param {RawDataPtr&} p_pScanMsg 该设备的原始网络数据
     * @return {*}
     * @other:
     */
    void processScanCb(uint32_t p_uiDevID, const RawDataPtr p_pScanMsg);

  public:
    typedef boost::shared_ptr<VirtualDriver> Ptr;

    /**
     * @description: 执行雷达标定
     * @param {*}
     * @return {*}
     * @other:
     */
    void playHorizontalCalib()
    {
        //
    }

    /**
     * @description: 雷达暂停
     * @param {bool} p_bIsPause 是否暂停
     * @return {*}
     * @other:
     */
    void playPause(bool p_bIsStart)
    {
        c_masterParamPtr->m_bIsStart = p_bIsStart;
        if (p_bIsStart)
            LOGFAE(WINFO, "离线数据包：播放数据");
        else
            LOGFAE(WINFO, "离线数据包：暂停播放");
        c_bIsStart_ = p_bIsStart;
        for (uint32_t l_iDevId = 0; l_iDevId < c_masterParamPtr->m_slam->m_devList.size();
             l_iDevId++)
        {
            if (c_bIsStart_)
                c_masterParamPtr->m_slam->m_devList[l_iDevId]->setStatus(DevStatus::PCAPRUN);
            else
                c_masterParamPtr->m_slam->m_devList[l_iDevId]->setStatus(DevStatus::PCAPSTOP);
        }
    }

    /**
     * @description: 雷达启动/重启
     * @param {*}
     * @return {*}
     * @other:
     */
    void playStart()
    {
        initDriver_();
    }

    /**
     * @description: 雷达播放速度-减速
     * @param {*}
     * @return {*}
     * @other:
     */
    void playMinusSpeed()
    {
        if (c_masterParamPtr->m_uiPlayBagRate > 1)
            c_masterParamPtr->m_uiPlayBagRate--;
        printf("play speed = %.1f\n", (float)c_masterParamPtr->m_uiPlayBagRate / 10.0);
    }

    /**
     * @description: 雷达播放速度-加速
     * @param {*}
     * @return {*}
     * @other:
     */
    void playAddSpeed()
    {
        if (c_masterParamPtr->m_uiPlayBagRate <= 50)
            c_masterParamPtr->m_uiPlayBagRate++;
        printf("play speed = %.1f\n", (float)c_masterParamPtr->m_uiPlayBagRate / 10.0);
    }

    /**
     * @description: 雷达播放速度-还原默认1倍速
     * @param {*}
     * @return {*}
     * @other:
     */
    void playOriSpeed()
    {
        c_masterParamPtr->m_uiPlayBagRate = 10;
        printf("play speed = %.1f\n", (float)c_masterParamPtr->m_uiPlayBagRate / 10.0);
    }

    void shutdown()
    {
        if (c_masterParamPtr->m_bIsStart)
            playPause(false);
        c_bShutdown_ = true;
        resetTimeAlignThread_(false);
        for (uint32_t l_iDev = 0; l_iDev < c_masterParamPtr->m_slam->m_devList.size(); ++l_iDev)
        {
            if (c_vpToken_[l_iDev])
                c_vpToken_[l_iDev]->shutdown();
            if (c_vpInput_[l_iDev])
                c_vpInput_[l_iDev]->shutdown();
        }
        for (uint32_t l_iDev = 0; l_iDev < c_masterParamPtr->m_slam->m_devList.size(); ++l_iDev)
        {
            c_vpInput_[l_iDev] = nullptr;
            c_vpToken_[l_iDev] = nullptr;
        }
    }

    VirtualDriver();
    ~VirtualDriver();
};

}  // namespace wj_slam

#endif