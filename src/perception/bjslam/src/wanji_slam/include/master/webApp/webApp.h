/*
 * @Author: linyuan<PERSON><PERSON>
 * @Date: 2021-05-18 13:13:41
 * @LastEditTime: 2022-10-14 17:44:10
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_net/include/message_Proc.h
 */
#pragma once

#include "common/common_master.h"
#include "protocolProc.h"
#include <arpa/inet.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <netinet/tcp.h>
#include <ros/ros.h>
#include <std_msgs/Int32MultiArray.h>
#include <sys/socket.h>
#include <tf/tf.h>

namespace wj_slam {

class WebApp {
  public:
    WebApp(ros::NodeHandle& p_nh, boost::function<int(int, int)> setCb);
    ~WebApp();

    void stop()
    {
        c_bRun_ = false;
    }
    void shutdown()
    {
        // 断开SLAM-TCP
        quitWebServer();

        // 停止解析
        if (c_ProtocolProc_)
        {
            c_ProtocolProc_->shutdown();
            c_ProtocolProc_ = nullptr;
        }

        // 释放内存
        if (c_pstWebMsg_ != NULL)
        {
            delete c_pstWebMsg_;
            c_pstWebMsg_ = NULL;
        }

        if (c_pstSlamMsg_ != NULL)
        {
            delete c_pstSlamMsg_;
            c_pstSlamMsg_ = NULL;
        }
    }
    /**
     * @description: 将字符串(按照协议打包后)通过ROS发布出去
     * @param {char*} p_pcBuf 字符串
     * @param {int} p_iLen 长度
     * @return {*}
     * @other:
     */
    void sendRosMsg_(char* p_pcBuf, int p_iLen);

    /**
     * @description: 将字符串通过TCP发布出去
     * @param {char*} p_pcBuf 要发布的字符串
     * @param {int} p_iLen  字符串长度
     * @return {*}
     * @other:
     */
    void sendNetMsg_(char* p_pcBuf, int p_iLen);

    /**
     * @description: 断开SLAM网络连接
     * @param {}
     * @return {*}
     * @other:
     */
    void quitWebServer();
    /**
     * @description: 建立SLAM网络连接
     * @param {}
     * @return {*}
     * @other:
     */
    void connectWebServer();

    void getSlamStatus()
    {
        /**此函数为初版设计，SLAM作为类后，可省略协议发送，因为m_RunStatus参数可直接获取到
         *  通过内部while进行等待m_RunStatus为RUNNING ，防止因slam模块异常阻塞 导致误判
         *  简化为while循环等待
         */
        if (!c_bGetSend)
            return;
        c_bGetSend = false;
        int l_iSendNum = 10;
        // char l_acGetSlamStatus[34];
        // for (int i = 0; i < 34; i++)
        //     l_acGetSlamStatus[i] = static_cast<u_char>(c_aucGetSlamStatus[i]);
        while (c_masterParamPtr->m_slam->m_RunStatus != PROCSTATE::STOPERR
               && c_masterParamPtr->m_slam->m_RunStatus != PROCSTATE::RUNNING)
        {
            // sendNetMsg_(l_acGetSlamStatus, 34);
            l_iSendNum--;
            sleep(1);
            // 1min未得到 == 查询失败 算了...
            if (!l_iSendNum)
            {
                c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STARTERR;
                break;
            }
        }
        c_bGetSend = true;
    }

    void setSlamDebug(bool p_bIsDebug)
    {
        char l_acSetDebugModel[34];
        for (int i = 0; i < 34; i++)
            l_acSetDebugModel[i] = static_cast<u_char>(c_aucSetDebugModel[i]);

        if (p_bIsDebug)
        {
            l_acSetDebugModel[26] = 1;
            l_acSetDebugModel[31] = 0x1A;
        }
        sendNetMsg_(l_acSetDebugModel, 34);
    }

    void setViewMode(bool p_bIs2DView)
    {
        char l_aucSetViewMode[34];
        for (int i = 0; i < 34; i++)
            l_aucSetViewMode[i] = static_cast<u_char>(c_aucSetViewMode[i]);
        if (p_bIs2DView)
        {
            l_aucSetViewMode[26] = (int)p_bIs2DView;
            l_aucSetViewMode[31] = 0x0E;
        }
        sendNetMsg_(l_aucSetViewMode, 34);
    }

    void resetLidarEnable()
    {
        char l_acResetEnableLidar[34];
        for (int i = 0; i < 34; i++)
            l_acResetEnableLidar[i] = static_cast<u_char>(c_aucSetResetEnableLidar[i]);
        sendNetMsg_(l_acResetEnableLidar, 34);
    }

    void sendSetPose(s_POSE6D& p_setPose);

  private:
#pragma region "ROS相关"
    ros::NodeHandle& c_node_;
    ros::Subscriber c_subWeb_;       //订阅Web消息
    ros::Subscriber c_subRvizPose_;  //订阅Rviz消息
    ros::Publisher c_pubWeb_;        //发布Web消息
#pragma endregion
#pragma region "TCP相关"
    int c_sockfd_ = -1;
    sockaddr_in c_myAddr_;
    sockaddr_in c_remoteAddr_;
    struct tcp_info c_stConnectInfo_;
    int c_iConnectInfoLen_ = sizeof(struct tcp_info);
#pragma endregion
    s_masterCfg* c_masterParamPtr = nullptr;
    s_NetMsg* c_pstSlamMsg_ = nullptr;  // slam通过TCP经webApp透传的消息数组
    s_NetMsg* c_pstWebMsg_ = nullptr;   // web通过ROS经webApp发送的消息数组
    std::mutex c_mtxLockWeb_;
    std::mutex c_mtxLockSlam_;
    boost::shared_ptr<ProtocolProc> c_ProtocolProc_;
    bool c_bRun_ = true;
    bool c_bRunOver_ = false;
    bool c_bGetSend = true;

    //查询SLAM启动状态
    // u_char c_aucGetSlamStatus[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
    //                                  0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
    //                                  0x00, 0x00, 0x00, 0x00, 0x0E, 0x04, 0x00, 0x00, 0x00,
    //                                  0x00, 0x00, 0x00, 0x00, 0x1E, 0xEE, 0xEE};

    //设置debug模式
    u_char c_aucSetDebugModel[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                     0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                     0x00, 0x00, 0x00, 0x00, 0x0E, 0x01, 0x00, 0x00, 0x00,
                                     0x00, 0x00, 0x00, 0x00, 0x1B, 0xEE, 0xEE};

    //设置雷达重新使能
    u_char c_aucSetResetEnableLidar[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                           0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                           0x00, 0x00, 0x00, 0x00, 0x0E, 0x03, 0x00, 0x00, 0x00,
                                           0x00, 0x00, 0x00, 0x00, 0x19, 0xEE, 0xEE};

    //查询debug模式
    // u_char c_aucGetDebugModel[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
    //                                  0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
    //                                  0x00, 0x00, 0x00, 0x00, 0x0E, 0x02, 0x00, 0x00, 0x00,
    //                                  0x00, 0x00, 0x00, 0x00, 0x18, 0xEE, 0xEE};

    //设定雷达Pose
    u_char c_aucSetLidarPose[26] = {0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                    0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                    0x00, 0x00, 0x00, 0x00, 0x0A, 0x0B, 0x00, 0x00};

    //设定可视化模式
    u_char c_aucSetViewMode[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                   0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                   0x00, 0x00, 0x00, 0x00, 0x0A, 0x11, 0x00, 0x00, 0x00,
                                   0x00, 0x00, 0x00, 0x00, 0x0F, 0xEE, 0xEE};

    /**
     * @description: WebMsg处理
     * @param {Int32MultiArrayConstPtr&} p_msg
     * @return {*}
     * @other:
     */
    void procRecvROSMsg(const std_msgs::Int32MultiArrayConstPtr& p_msg);

    void netInit_(sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);
    /**
     * @description: 初始化 TCP客户端 连接TCP服务器 并接受数据
     * @param {}
     * @return {*}
     * @other:
     */
    void netStart_();

    /**
     * @description: 连接TCP服务器 3s未连接则认为失败
     * @param {int&} p_iFd 网络句柄 须已建立socket
     * @param {sockaddr_in&} p_sRemoteAddr 服务器地址
     * @return {bool} TCP服务器连接成功
     * @other:
     */
    bool connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

    /**
     * @description: 判断TCP是否连接成功，接受来自SLAM数据
     * @param {*}
     * @return {*}
     * @other:
     */
    void procRecvSlamMsg();

    /**
     * @description: 从数组中提取完整协议 并ROS发送
     * @param {*}
     * @return {*}
     * @other:
     */
    void getNetCmd2Send()
    {
        {
            // 每次连接后清空数据长度 因offset每次重置为0 两者因想等
            std::lock_guard<std::mutex> l_mtx(c_mtxLockSlam_);
            c_pstSlamMsg_->m_uiDataLen = 0;
        }
        if (c_ProtocolProc_)
            c_ProtocolProc_->procNetCmdInThread();
    };

    /**
     * @description: 退出线程
     * @param {*}
     * @return {*}
     * @other:
     */
    void exitGetNetCmd2Send(void)
    {
        if (c_ProtocolProc_)
            c_ProtocolProc_->exitProcNetThread();
    };

    void poseHandlerCb_(const geometry_msgs::PoseWithCovarianceStamped::ConstPtr& msg)
    {
        double l_f64Yaw = tf::getYaw(msg->pose.pose.orientation) * 180.0 / M_PI;
        wj_slam::s_POSE6D l_pose;
        l_pose.setX(msg->pose.pose.position.x);
        l_pose.setY(msg->pose.pose.position.y);
        l_pose.setZ(0);
        l_pose.setRPY(0, 0, l_f64Yaw);
        sendSetPose(l_pose);
    }
};
}  // namespace wj_slam