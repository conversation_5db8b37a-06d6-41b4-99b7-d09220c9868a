/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-04 15:49:41
 * @LastEditors: wen <EMAIL>
 * @LastEditTime: 2022-09-30 12:47:07
 */

#pragma once

#include "master/webApp/web_Protocol.h"
#include <boost/shared_ptr.hpp>
#include <ctime>
#include <netinet/tcp.h>
#include <semaphore.h>
#include <sys/time.h>
#include <thread>
#include <time.h>

class ProtocolProc {
  private:
    s_NetMsg& c_stWebMsg_;
    s_NetMsg& c_stSlamMsg_;
    boost::shared_ptr<wj_slam::WebProtocol> c_pWebProtocol_;
    boost::function<void(char*, int)> c_sendRosCb_;
    boost::function<void(char*, int)> c_sendTcpCb_;
    boost::function<int(int, int)> c_setCb_;
    uint32_t c_uiProtocolBtyeNum = 22;    // 协议标识符字节位
    uint32_t c_uiSendSlamMsgOffset_ = 0;  // 循环解析c_pstSlamMsg_数组的偏移量
    uint32_t c_uiSendWebMsgOffset_ = 0;   // 循环解析c_pstWebMsg_数组的偏移量
    bool c_bProcThrSlam_ = false;         // slamMsg循环解析并发送线程的控制信号
    bool c_bProcThrSlamOver_ = false;
    bool c_bProcThrWeb_ = false;
    bool c_bProcThrWebOver_ = false;
    std::mutex& c_mtxLockWeb_;
    std::mutex& c_mtxLockSlam_;

    /**
     * @description: 根据协议头FFEE 协议尾EE 确定协议类型 同时修改偏移量
     * @param {int&} p_iOffset 被修改的当前偏移量
     * @return {*}
     * @other:
     */
    WJCMDTYPE recognizeProtocolType_(s_NetMsg& p_msg_, uint32_t& p_uiOffset, std::mutex& p_lock);

    /**
     * @description: 从p_iOffset开始确定并复制完整WJ/WEB协议
     * @param {int&} p_iOffset 循环数组的偏移量
     * @param {u_char*} p_pcBufCMD 提取的协议（待填充）
     * @param {mutex&} p_lock
     * @return {int} 提取协议的长度
     * @other: 此函数区别与wj/sick::getCMD 提取完整协议内容涵帧头帧尾 且不进行CRC
     */
    int getAllCMD(s_NetMsg& p_sMsg_, uint32_t& p_iOffset, u_char* p_pcBufCMD, std::mutex& p_lock);

    /**
     * @description: 拆分数组 ROS发送
     * @param {*}
     * @return {*}
     * @other:
     */
    void handlerSlamMsg_();

    void handlerWebMsg_();

  public:
    /**
     * @description: 开辟线程 开始TCP协议解析
     * @param {*}
     * @return {*}
     * @other:
     */
    void procNetCmdInThread()
    {
        std::thread t(&ProtocolProc::handlerSlamMsg_, this);
        t.detach();
    }

    /**
     * @description: 推出TCP协议解析处理
     * @param {*}
     * @return {*}
     * @other:
     */
    void exitProcNetThread(void)
    {
        // 避免线程未开启 造化无法退出问题
        if (!c_bProcThrSlam_)
            c_bProcThrSlamOver_ = true;
        else
            c_bProcThrSlam_ = false;
        {
            std::lock_guard<std::mutex> l_mtx(c_mtxLockSlam_);
            c_uiSendSlamMsgOffset_ = c_stSlamMsg_.m_uiDataLen;
        }

        while (1)
        {
            if (c_bProcThrSlamOver_)
                break;
            else
                usleep(500);
        }
        c_uiSendSlamMsgOffset_ = 0;
        c_stSlamMsg_.m_uiDataLen = 0;
    }

    /**
     * @description: 推出ROS协议解析处理
     * @param {*}
     * @return {*}
     * @other:
     */
    void exitProcROSThread(void)
    {
        // 避免线程未开启 造化无法退出问题
        if (!c_bProcThrWeb_)
            c_bProcThrWebOver_ = true;
        else
            c_bProcThrWeb_ = false;
        {
            std::lock_guard<std::mutex> l_mtx(c_mtxLockWeb_);
            c_uiSendWebMsgOffset_ = c_stWebMsg_.m_uiDataLen;
        }

        while (1)
        {
            if (c_bProcThrWebOver_)
                break;
            else
                usleep(500);
        }
        c_uiSendWebMsgOffset_ = 0;
        c_stWebMsg_.m_uiDataLen = 0;
    }

    void shutdown()
    {
        // 结束TCP协议解析
        exitProcNetThread();
        // 结束ROS协议解析
        exitProcROSThread();
        // 结束web协议解析
        if (c_pWebProtocol_)
            c_pWebProtocol_ = nullptr;
    }

    ProtocolProc(s_NetMsg& p_stWebMsg,
                 std::mutex& l_mtxLockWeb_,
                 s_NetMsg& p_stSlamMsg,
                 std::mutex& l_mtxLockSlam_,
                 boost::function<void(char*, int)> sendRosCb_,
                 boost::function<void(char*, int)> sendTcpCb_,
                 boost::function<int(int, int)> setCb);
    ~ProtocolProc()
    {
        shutdown();
    };
};
