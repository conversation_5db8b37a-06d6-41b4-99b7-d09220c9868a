#ifndef PCAP_HANDLE_H_
#define PCAP_HANDLE_H_

#include <ctime>
#include <dirent.h>
#include <fstream>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <thread>

#include <pcap.h>
#include <zlib.h>

#include <iostream>
#include <vector>

#include "pcapManage.h"
#include "tool/fileTool/fileTool.h"
#include <boost/shared_ptr.hpp>
#include <signal.h>

#include <chrono>

#define PKT_HEAD_SIZE 24
#define PING 1
#define PANG 0
using namespace std::chrono;

enum MODE {
    WRITE,  //写入模式
    READ    //读取模式
};

class PcapHandle {
  public:
    typedef boost::shared_ptr<PcapHandle> Ptr;

  private:
    MODE c_mode;

    char* c_pNetCard_ = NULL;
    std::string c_sNetCard_ = "";
    std::string c_sFilterRule_ = "";
    char* c_pDataDir_ = NULL;  // 数据存储路径 用于读/写
    std::string c_sDataDir_ = "";
    boost::shared_ptr<PcapManage> c_pcapControl_;

    int c_iTimeInterval_;
    int c_iPcapTimeOut;
    int c_iResPcap_;
    int c_iPingPang_;
    int c_iMaxPcapNum_ = 30;

    bool c_bStartPcap_;
    bool c_bIsOver_;
    bool c_bIsSaveOver_;

    pcap_t* c_pDevice_ = NULL;

    std::string c_sPcapPath_;
    std::string c_sPcapPathPing_;
    std::string c_sPcapPathPang_;

    std::vector<u_char> c_vPktdataPing_;
    std::vector<u_char> c_vPktdataPang_;

    time_point<system_clock> c_CurTime_;
    time_point<system_clock> c_LastTime_;

    void captureData_();
    void saveData_(int p_iPingPang);
    void doCaptureSave_();
    void recordRun_();
    std::string makePcapPath_(std::string p_sFolderPath);
    bool createfolder_(std::string p_sFileDir);
    void init();

  public:
    PcapHandle(std::string p_sNetcardName,
               std::string p_pSavePath,
               std::string p_sFilterRule,
               int p_pTimeIntervalSec,
               int p_iMaxPcapNum,
               int p_pPcapTimeOut = 0,
               MODE p_Mode = MODE::WRITE);
    ~PcapHandle();

    void setfilter(std::string p_sFilterRule);
    bool setNetCard(char* p_pNetCard);
    void breakPcap(int signum);

    /**
     * @description: 控制播包启动/暂停
     * @param {bool} isStart 是否启动
     * @return {bool} 是否启动/关闭   返回值可用于判断是否录制
     * @other:
     */
    bool isStart(bool p_bIsStart)
    {
        bool l_bRealStart = false;
        if (p_bIsStart)
        {
            if (!c_pDevice_)
            {
                if (setNetCard(c_pNetCard_))
                {
                    setfilter(c_sFilterRule_);
                    l_bRealStart = true;
                    printf("open netcard success\n");
                }
                else
                {
                    l_bRealStart = false;
                    printf("can't open netcard\n");
                    return l_bRealStart;
                }
            }
        }
        else
        {
            l_bRealStart = false;
        }
        c_bStartPcap_ = p_bIsStart;
        return l_bRealStart;
    }

    bool c_bRuning;
};
#endif
