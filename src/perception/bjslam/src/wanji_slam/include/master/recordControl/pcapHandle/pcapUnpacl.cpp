/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-11 16:01:10
 * @LastEditors: wen <EMAIL>
 * @LastEditTime: 2022-07-13 19:08:51
 */
#include <fstream>
#include <iostream>
#include <pcap.h>
#include <string.h>
#include <string>
#include <zlib.h>

int main(int argc, char* argv[])
{
    if (argc < 3)
    {
        fprintf(stderr, "Usage: $ sudo ./Unpack “文件地址” “文件名”\n");
        exit(0);
    }
    std::string path = argv[1];
    std::string filename = argv[2];
    std::string l_sInputPath = path + "/" + filename;  //输入路径
    std::string l_sOutputPath = path + "/" + filename + "ng";

    struct pcap_pkthdr pktHeader;

    pcap_t* l_pDevice = NULL;
    char l_cErrBuf[PCAP_ERRBUF_SIZE];
    char* l_pNetCard = pcap_lookupdev(l_cErrBuf);
    if (!l_pNetCard)
    {
        printf("No Find NetWord\n");
        return -1;
    }
    l_pDevice = pcap_open_live(l_pNetCard, 65535, 1, 0, l_cErrBuf);
    if (!l_pDevice)
    {
        printf("workcard open fail\n");
        return -1;
    }
    pcap_dumper_t* l_outPcap = pcap_dump_open(l_pDevice, l_sOutputPath.c_str());

    std::ifstream inFile(l_sInputPath, std::ios::in | std::ios::binary);  //二进制读方式打开
    inFile.read((char*)&pktHeader, sizeof(pktHeader));
    char* data = new char[1600];
    while (inFile.read(data, pktHeader.caplen))
    {
        const u_char* temp = (const u_char*)data;
        pcap_dump((u_char*)l_outPcap, &pktHeader, temp);
        memset(data, 0, sizeof(data));
        inFile.read((char*)&pktHeader, sizeof(pktHeader));
    }
    pcap_dump_flush(l_outPcap);
    pcap_dump_close(l_outPcap);
    return 0;
}