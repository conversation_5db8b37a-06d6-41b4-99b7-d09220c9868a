/*
 * @Description: 网络读取/配置模块
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-02 16:08:56
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-12-01 15:54:52
 */
#include "common/config/conf_fae.h"
#include "common/config/conf_net.h"
#include "tool/fileTool/fileTool.h"
#include "wj_log.h"
#include <algorithm>
#include <arpa/inet.h>
#include <cstring>
#include <dirent.h>
#include <fstream>
#include <future>
#include <iterator>
#include <net/if.h>
#include <netdb.h>
#include <netinet/in.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <unistd.h>
#include <yaml-cpp/yaml.h>
using namespace std;

class NetCfg {
    /**
     * DISCONNECT<默认状态/未连接>;
     * NORMAL<正常>;
     * ABNORMAL<异常>;
     */
    enum NETSTATUS { DISCONNECT = 0, NORMAL, ABNORMAL };

  private:
    YAML::Node c_Config_;
    std::string c_sCfgFilePath_ = "";                 // 网络配置文件路径
    std::vector<wj_slam::s_NetCfg> c_vNetCfgList_;    // 网络配置List
    std::vector<std::string> c_vNetNameList;          // 物理网卡名List
    std::string c_sAllNetCMD = "ls /sys/class/net/";  //获取所有网卡 含回环等虚拟网卡
    std::string c_sVirtualNetCMD = "ls /sys/devices/virtual/net/";  //获取虚拟网卡
    wj_slam::s_FaeLogCfg& c_fae;

    template <typename T> T loadParam_(std::string name)
    {
        if (c_Config_["network"][name])
        {
            return c_Config_["network"][name].as<T>();
        }
        return NULL;
    }
    template <typename T> bool loadParam_(const YAML::Node& node, T& out)
    {
        if (node)
        {
            out = node.as<T>();
            return true;
        }
        return false;
    }
    template <typename T> bool loadParam_(const YAML::Node& node, std::string name, T& out)
    {
        if (node)
        {
            out = node[name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T> bool loadParam_(std::string name, T& out)
    {
        if (c_Config_["network"][name])
        {
            out = c_Config_["network"][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T> bool loadParam_(std::string dict, std::string name, T& out)
    {
        if (c_Config_["network"][dict][name])
        {
            out = c_Config_["network"][dict][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T>
    bool loadParam_(std::string dict, std::string dict2, std::string name, T& out)
    {
        if (c_Config_["network"][dict][dict2][name])
        {
            out = c_Config_["network"][dict][dict2][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T>
    bool
    loadParam_(std::string dict, std::string dict2, std::string dict3, std::string name, T& out)
    {
        if (c_Config_["network"][dict][dict2][dict3][name])
        {
            out = c_Config_["network"][dict][dict2][dict3][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T>
    bool loadParam_(std::string dict,
                    std::string dict2,
                    std::string dict3,
                    std::string dict4,
                    std::string name,
                    T& out)
    {
        if (c_Config_["network"][dict][dict2][dict3][dict4][name])
        {
            out = c_Config_["network"][dict][dict2][dict3][dict4][name].as<T>();
            return true;
        }
        return false;
    }

    /**
     * @description: 输入指令 获取网络名称 所有/虚拟网卡
     * @param {string} p_sCMD 指令
     * @param {uint32_t} p_uiStartReadLine 开始读取的行号
     * @return {std::vector<std::string>}
     * @other:
     */
    std::vector<std::string> getNetList_(std::string p_sCMD, uint32_t p_uiStartReadLine = 0)
    {
        FILE* fp = nullptr;
        std::vector<std::string> l_vsNetList;
        char l_cReadBuf[100];
        uint32_t l_iLineId = 0;
        fp = popen(p_sCMD.c_str(), "r");
        if (fp)
        {
            while (fgets(l_cReadBuf, sizeof(l_cReadBuf), fp) != nullptr)
            {
                std::string l_sName = strtok(l_cReadBuf, "\n");
                if (l_iLineId >= p_uiStartReadLine)
                    l_vsNetList.push_back(l_sName);
                l_iLineId++;
            }
        }
        else
            perror("popen");
        pclose(fp);
        return l_vsNetList;
    }

    /**
     * @description: v1  去除 v2 和 v1 重复元素
     * @param {vector<string> v1, vector<string>} v2 |  v1 雷达扫描框 更大  v2附近框
     * @return {*}
     */
    template <typename T> vector<T> vectorDifference_(vector<T> v1, vector<string> v2)
    {
        vector<T> v;
        if (v1.empty())
            return v;
        if (v2.empty())
            return v1;

        sort(v1.begin(), v1.end());
        sort(v2.begin(), v2.end());

        // v1  去除 v2 和 v1 重复元素
        std::set_difference(
            std::begin(v1), std::end(v1), std::begin(v2), std::end(v2), std::back_inserter(v));
        return v;
    }

    /**
     * @description: 读取 user / default 参数
     * @param {string} p_sPkgPath
     * @return {*}
     */
    void loadNetParam_(std::string p_sFilePath)
    {
        try
        {
            /*读取 网络配置配置文件*/
            c_Config_ = YAML::LoadFile(c_sCfgFilePath_);
        }
        catch (std::exception& ex)
        {
            LOGFAE(WERROR,
                   "网络配置文件[{}]读取失败 | 文件不存在/损坏, 请检查网络配置文件!",
                   c_sCfgFilePath_);
            // c_fae.setErrorCode("A1");
        }

        {
            std::vector<std::string> l_vsRead;
            for (uint32_t i = 0; i < c_vNetNameList.size(); i++)
            {
                wj_slam::s_NetCfg l_netCfg;
                l_netCfg.m_sNetName = c_vNetNameList[i];
                YAML::Node l_NetParam_ = c_Config_["network"]["ethernets"][c_vNetNameList[i]];

                if (l_NetParam_.IsDefined())
                {
                    l_netCfg.isSet = true;
                    loadParam_<bool>(l_NetParam_, "dhcp4", l_netCfg.isDHCP);
                    l_netCfg.setDHCP(l_netCfg.isDHCP);
                    if (!l_netCfg.isDHCP)
                    {
                        // 读取网关 未设置即不联网
                        loadParam_<std::string>(
                            "ethernets", c_vNetNameList[i], "gateway4", l_netCfg.m_sGateway);
                        if (l_netCfg.m_sGateway == "")
                            l_netCfg.isNetwork = false;
                        else
                            l_netCfg.isNetwork = true;

                        // 读取IP
                        l_vsRead.clear();
                        if (loadParam_<std::vector<std::string>>(
                                "ethernets", c_vNetNameList[i], "addresses", l_vsRead))
                        {
                            // 其实允许多个IP 但程序设定仅读取第1个
                            if (l_vsRead.size() > 1)
                                LOGW(WINFO,
                                     "{} [{}] set-ip num over 1",
                                     WJLog::getWholeSysTime(),
                                     c_vNetNameList[i]);
                            for (uint32_t i = 0; i < l_vsRead.size(); i++)
                            {
                                std::vector<std::string> l_vsTmp;
                                // eg: ************/24 分割ip和掩码
                                splitString_(l_vsRead[i], l_vsTmp, "/");
                                if (l_vsTmp.size() == 2)
                                {
                                    l_netCfg.m_sIpSet = l_vsTmp[0];
                                    l_netCfg.m_sMask = l_vsTmp[1];
                                    break;
                                }
                                else
                                    LOGW(WINFO,
                                         "{} [{}] set-ip error: ",
                                         WJLog::getWholeSysTime(),
                                         l_vsRead[i]);
                            }
                        }
                        // 读取DNS
                        l_vsRead.clear();
                        if (loadParam_("ethernets",
                                       c_vNetNameList[i],
                                       "nameservers",
                                       "addresses",
                                       l_vsRead))
                        {
                            for (uint32_t i = 0; i < l_vsRead.size(); i++)
                            {
                                // eg: ***************,******* 分割多个DNS
                                splitString_(l_vsRead[i], l_netCfg.m_vsDNS, ",");
                            }
                        }
                    }
                }
                else
                    l_netCfg.isSet = false;

                c_vNetCfgList_.push_back(l_netCfg);
            }

            for (uint32_t i = 0; i < c_vNetCfgList_.size(); i++)
            {
                c_vNetCfgList_[i].m_sIpRead = getNetRealIP(c_vNetCfgList_[i].m_sNetName);
                // c_vNetCfgList_[i].printf(std::to_string(i + 1));
            }
        }
    }

    /**
     * @description: 只有1个分割符c, 将s 按照分割符c 切割写入v
     * @param {string}  s 字符串
     * @param {vector<string>} v 输出列表
     * @param {string}  c 分割符
     * @return {*}
     */
    void splitString_(const string& s, vector<string>& v, const string& c)
    {
        string::size_type pos1, pos2;
        pos2 = s.find(c);
        pos1 = 0;
        while (string::npos != pos2)
        {
            v.push_back(s.substr(pos1, pos2 - pos1));

            pos1 = pos2 + c.size();
            pos2 = s.find(c, pos1);
        }
        if (pos1 != s.length())
            v.push_back(s.substr(pos1));
    }

    // /**
    //  * @description: 更新网络状态
    //  * @param {wj_slam::s_NetCfg&} p_stNetCfg 网卡信息结构体
    //  * @return {*}
    //  * @other:
    //  */
    // void updateNetStatus(wj_slam::s_NetCfg& p_stNetCfg)
    // {
    //     // DHCP读取到IP则认为正常,静态IP须对比IpRead和IpSet
    //     if (p_stNetCfg.isDHCP)
    //     {
    //         if(p_stNetCfg.m_sIpRead != "")
    //             p_stNetCfg.m_uiStatus = (uint32_t)NETSTATUS::NORMAL;
    //         else
    //             p_stNetCfg.m_uiStatus = (uint32_t)NETSTATUS::DISCONNECT;
    //     }
    //     else
    //     {
    //         if (p_stNetCfg.m_sIpRead != "")
    //         {
    //             if (p_stNetCfg.m_sIpRead != p_stNetCfg.m_sIpSet)
    //                 p_stNetCfg.m_uiStatus = (uint32_t)NETSTATUS::ABNORMAL;
    //             else
    //                 p_stNetCfg.m_uiStatus = (uint32_t)NETSTATUS::NORMAL;
    //         }
    //         else
    //             p_stNetCfg.m_uiStatus = (uint32_t)NETSTATUS::DISCONNECT;
    //     }
    // }

    /**
     * @description: 嗲用shell执行cmd 并获取命令输出
     * @param {const std::string&} strCmd 指令
     * @return {*}
     */
    std::string getCmdResult_(const std::string& strCmd)
    {
        char buf[10240] = {0};
        FILE* pf = NULL;
        if ((pf = popen(strCmd.c_str(), "r")) == NULL)
        {
            return "";
        }
        std::string strResult;
        while (fgets(buf, sizeof buf, pf))
        {
            strResult += buf;
        }
        pclose(pf);
        unsigned int iSize = strResult.size();
        if (iSize > 0 && strResult[iSize - 1] == '\n')  // linux
        {
            strResult = strResult.substr(0, iSize - 1);
        }
        return strResult;
    }

    std::string isPortOpen_(const std::string& p_sIp, const std::string& port)
    {
        addrinfo* result;
        addrinfo hints{};
        hints.ai_family = AF_UNSPEC;  // either IPv4 or IPv6
        hints.ai_socktype = SOCK_STREAM;
        char addressString[INET6_ADDRSTRLEN];
        const char* retval = nullptr;
        if (0 != getaddrinfo(p_sIp.c_str(), port.c_str(), &hints, &result))
        {
            return "";
        }
        for (addrinfo* addr = result; addr != nullptr; addr = addr->ai_next)
        {
            int handle = socket(addr->ai_family, addr->ai_socktype, addr->ai_protocol);
            if (handle == -1)
            {
                continue;
            }
            if (connect(handle, addr->ai_addr, addr->ai_addrlen) != -1)
            {
                switch (addr->ai_family)
                {
                    case AF_INET:
                        retval =
                            inet_ntop(addr->ai_family,
                                      &(reinterpret_cast<sockaddr_in*>(addr->ai_addr)->sin_addr),
                                      addressString,
                                      INET6_ADDRSTRLEN);
                        break;
                    case AF_INET6:
                        retval =
                            inet_ntop(addr->ai_family,
                                      &(reinterpret_cast<sockaddr_in6*>(addr->ai_addr)->sin6_addr),
                                      addressString,
                                      INET6_ADDRSTRLEN);
                        break;
                    default:
                        // unknown family
                        retval = nullptr;
                }
                close(handle);
                break;
            }
        }
        freeaddrinfo(result);
        return retval == nullptr ? "" : p_sIp + ":" + retval + "\n";
    }

    /**
     * @description: 检测ip下某端口是否可用
     * @param {*}
     * @return {bool} true，则说明，某个服务处于监听状态，同时说明这个端口号已经被这个服务所占用
     * @other:
     */
    bool isListening(const std::string& ip, const uint32_t& p_uiPort)
    {
        std::string addr = isPortOpen_(ip, std::to_string(p_uiPort));
        if (addr.empty())
            return false;
        return true;
    }

    /**
     * @description: 提取网络配置 转换至node
     * @param {wj_slam::s_NetCfg} p_sNetInfo
     * @param {YAML::Node&}p_node
     * @return {bool} 是否转换成功
     * @other:
     */
    bool netCfgToYamlNode_(wj_slam::s_NetCfg p_sNetInfo, YAML::Node& p_node)
    {
        // 是否从yaml中读取到
        if (p_sNetInfo.m_sNetName == "")
            return false;

        YAML::Node l_node;
        std::vector<std::string> l_vsTmp;
        std::string l_sTmp;
        if (p_sNetInfo.isDHCP)
        {
            l_vsTmp.clear();
            l_node["addresses"] = l_vsTmp;
            l_node["addresses"].SetStyle(YAML::EmitterStyle::Flow);
            l_node["dhcp4"] = "yes";
        }
        else
        {
            l_sTmp = p_sNetInfo.m_sIpSet + "/" + p_sNetInfo.m_sMask;
            l_vsTmp.clear();
            l_vsTmp.push_back(l_sTmp);
            l_node["addresses"] = l_vsTmp;
            l_node["addresses"].SetStyle(YAML::EmitterStyle::Flow);

            if (p_sNetInfo.m_sGateway != "")
                l_node["gateway4"] = p_sNetInfo.m_sGateway;

            if (p_sNetInfo.m_vsDNS.size())
            {
                l_vsTmp.clear();
                for (uint32_t j = 0; j < p_sNetInfo.m_vsDNS.size(); j++)
                    l_vsTmp.push_back(p_sNetInfo.m_vsDNS[j]);

                l_node["nameservers"]["addresses"] = l_vsTmp;
                l_node["nameservers"]["addresses"].SetStyle(YAML::EmitterStyle::Flow);
            }
            l_node["dhcp4"] = "no";
        }
        l_node["optional"] = "true";

        p_node = l_node;
        return true;
    }

    /**
     * @description: 从yaml读取Node  可读取某网卡 也可读取完整
     * @param {std::string} p_sYamlPath yaml路径
     * @param {YAML::Node&} p_node 待返回node
     * @param {std::string}p_sReadNetName 网卡名 缺省则代表全读取
     * @return {bool} true 存在node false 文件不存在 node不存在
     * @other: 如果返回完整node 暂时未检测node是否存在 默认 YAML::LoadFile 读取成功
     */
    bool
    readYamlToNode_(std::string p_sYamlPath, YAML::Node& p_node, std::string p_sReadNetName = "")
    {
        try
        {
            YAML::Node l_config_ = YAML::LoadFile(p_sYamlPath);
            if (p_sReadNetName != "")
            {
                if (l_config_["network"]["ethernets"][p_sReadNetName].IsDefined())
                {
                    p_node = l_config_["network"]["ethernets"][p_sReadNetName];
                    return true;
                }
                return false;
            }
            p_node = l_config_;
            return true;
        }
        catch (YAML::BadFile& e)
        {
            std::cout << "yaml2node fail |  yaml err" << std::endl;
            return false;
        }
    }

    /**
     * @description: 检查所有网卡是否连接 拥有IP
     * @param {*}
     * @return {*}
     * @other:
     */
    void checkNetConnect_()
    {
        for (uint32_t i = 0; i < c_vNetCfgList_.size(); i++)
        {
            c_vNetCfgList_[i].m_sIpRead = getNetRealIP(c_vNetCfgList_[i].m_sNetName);
            // updateNetStatus(c_vNetCfgList_[i]);
            c_vNetCfgList_[i].printf(std::to_string(i + 1));
        }
    }

    /**
     * @description: 提取IP网段 eg: ************ -> 192.168.1.
     * @param {string} p_sBasicsIPInfo 设定IP
     * @return {*}
     */
    bool getIpSegment_(std::string& p_sBasicsIPInfo, uint8_t& p_uiOffset)
    {
        std::string l_sIpSegment = "";
        std::vector<std::string> l_vsTmp;
        splitString_(p_sBasicsIPInfo, l_vsTmp, ".");
        if (l_vsTmp.size() == 4)
        {
            for (uint32_t i = 0; i < l_vsTmp.size() - 1; i++)
                l_sIpSegment = l_sIpSegment + l_vsTmp[i] + ".";
            p_sBasicsIPInfo = l_sIpSegment;
            p_uiOffset = atoi(l_vsTmp[3].c_str());
            return true;
        }
        return false;
    }

  public:
    std::vector<wj_slam::s_NetCfg>& getNetCfg()
    {
        return c_vNetCfgList_;
    }

    /**
     * @description: 保存单网卡网络参数
     * @param {*}
     * @return {bool} fase: 1.非root权限 2. 文件写入失败
     * @other: 1.源文件存在则修改对应网卡 不存在则新建 且只写当前网卡
     */
    bool saveNetCfg(wj_slam::s_NetCfg p_sNetInfo)
    {
        // 0 is root权限
        if (geteuid() != 0)
        {
            LOGFAE(WERROR, "保存网络设置失败 | 请以root权限启动此程序!");
            // c_fae.setErrorCode("A2");
            return false;
        }

        std::string l_sPathBak = c_sCfgFilePath_ + ".bak";
        if (isExistFileOrFolder(c_sCfgFilePath_))
        {
            // 源文件存在的情况下：确保有1份备份文件，不存在则拷贝
            if (!isExistFileOrFolder(l_sPathBak))
                copyFileOrFolder(c_sCfgFilePath_, l_sPathBak);

            // 存在网络文件 则只修改对应网卡
            try
            {
                YAML::Node l_config_ = YAML::LoadFile(c_sCfgFilePath_);
                YAML::Node l_ethernets_ = l_config_["network"]["ethernets"];

                // 存在则删除旧配置
                if (l_ethernets_[p_sNetInfo.m_sNetName].IsDefined())
                    l_ethernets_.remove(
                        p_sNetInfo.m_sNetName);  // 只可以使用字符串删除 且这个node为引用
                                                 // 主node相同区域同样被删除

                // 写入新配置
                YAML::Node l_netNode;
                if (netCfgToYamlNode_(p_sNetInfo, l_netNode))
                    l_ethernets_[p_sNetInfo.m_sNetName] = l_netNode;

                std::ofstream l_file(c_sCfgFilePath_);  //有则打开，没有则创建
                if (l_file.is_open())
                {
                    l_file << l_config_ << std::endl;
                    l_file.close();
                    return true;
                }
                else
                {
                    LOGW(
                        WERROR, "{} save netCfg  yaml fail | open error", WJLog::getWholeSysTime());
                    return false;
                }
            }
            catch (YAML::BadFile& e)
            {
                LOGW(WERROR,
                     "{} save netCfg  yaml fail | read fail or no exist",
                     WJLog::getWholeSysTime());
                return false;
            }
        }
        else
        {
            // 原文件都不存在的话 重新写1份 只写对应网卡
            // 须保证配置文件的路径准确 否则尽管写成功 但后续无法应用于网络配置
            std::ofstream l_file(c_sCfgFilePath_);  //有则打开，没有则创建
            if (!l_file.is_open())
                return false;
            YAML::Node l_node;
            YAML::Node l_netNode;
            if (netCfgToYamlNode_(p_sNetInfo, l_netNode))
                l_node["network"]["ethernets"][p_sNetInfo.m_sNetName] = l_netNode;
            l_node["network"]["version"] = 2;
            l_node["network"]["renderer"] = "NetworkManager";
            l_file << l_node << std::endl;
            l_file.close();
            if (!isExistFileOrFolder(l_sPathBak))
                copyFileOrFolder(c_sCfgFilePath_, l_sPathBak);
            return true;
        }
    }

    /**
     * @description: 重置单网卡网络参数
     * @param {*}
     * @return
     * {bool} 1.非root权限 2.备份网络文件不存在 3.备份文件中不存在对应网卡的备份配置 4.写入失败
     * @other: 源文件不存在 则强制使用备份文件生成源文件 所有网卡均恢复
     */
    bool resetNetCfg(wj_slam::s_NetCfg p_sNetInfo)
    {
        bool l_bSaveFile_ = false;
        if (geteuid() != 0)
        {
            LOGFAE(WERROR, "还原网络设置失败 | 请以root权限启动此程序!");
            // c_fae.setErrorCode("A3");
            return false;
        }
        std::string l_sPathBak = c_sCfgFilePath_ + ".bak";
        // 备份文件则无法还原
        if (!isExistFileOrFolder(l_sPathBak) || !isExistFileOrFolder(c_sCfgFilePath_))
        {
            if (isExistFileOrFolder(l_sPathBak))
            {
                LOGFAE(WWARN, "原始网络配置文件[{}]不存在，拷贝当前备份配置文件为原始网络配置文件");
                // c_fae.setErrorCode("A4");
                //  源文件不存在的情况下：源文件已丢失，强制使用备份文件
                copyFileOrFolder(l_sPathBak, c_sCfgFilePath_);
                return true;
            }
            else
            {
                LOGFAE(WERROR,
                       "还原网络配置失败 | 原始网络配置文件[{}] 或 备份网络配置文件 [{}] "
                       "不存在，请检查!",
                       c_sCfgFilePath_,
                       l_sPathBak);
                // c_fae.setErrorCode("A5");
                return false;
            }
        }

        YAML::Node l_netNodeBak, l_config_;
        try
        {
            readYamlToNode_(c_sCfgFilePath_, l_config_);
            // 源文件和备份文件均存在的情况下 替换对应网卡
            if (readYamlToNode_(l_sPathBak, l_netNodeBak, p_sNetInfo.m_sNetName))
            {
                l_config_["network"]["ethernets"][p_sNetInfo.m_sNetName] = l_netNodeBak;
                l_bSaveFile_ = true;
            }
            else
            {
                LOGW(WERROR,
                     "{} reset netCfg | delete cfg {}",
                     WJLog::getWholeSysTime(),
                     p_sNetInfo.m_sNetName);
                // 直接删除该网卡配置
                if (l_config_["network"]["ethernets"][p_sNetInfo.m_sNetName].IsDefined())
                {
                    if (l_config_["network"]["ethernets"].size() == 1)
                    {
                        YAML::Node l_ethernets_ = l_config_["network"];
                        l_ethernets_.remove("ethernets");
                    }
                    else
                    {
                        YAML::Node l_ethernets_ = l_config_["network"]["ethernets"];
                        l_ethernets_.remove(p_sNetInfo.m_sNetName);
                    }
                    l_bSaveFile_ = true;
                }
            }
        }
        catch (YAML::BadFile& e)
        {
            LOGW(WERROR,
                 "{} reset netCfg  yaml fail | read fail or no exist",
                 WJLog::getWholeSysTime());
        }
        if (l_bSaveFile_)
        {
            std::ofstream l_file(c_sCfgFilePath_);  //有则打开，没有则创建
            if (l_file.is_open())
            {
                l_file << l_config_ << std::endl;
                l_file.close();
                return true;
            }
            else
                LOGW(WERROR, "{} reset netCfg  save fail | open error", WJLog::getWholeSysTime());
        }
        return false;
    }

    /**
     * @description: 获取本地IP 设定IP在线的话优先选择
     * @param {string} p_sPreferIP 优先选择IP
     * @return {*}
     */
    std::string getLocalIp(std::string p_sPreferIP)
    {
        //  读取实时IP
        checkNetConnect_();
        if (p_sPreferIP != "")
        {
            //  在线IP是否包含设定IP
            for (uint32_t i = 0; i < c_vNetCfgList_.size(); i++)
            {
                // printf("readIp: %s\n", c_vNetCfgList_[i].m_sIpRead.c_str());
                if (c_vNetCfgList_[i].m_sIpRead != "" && p_sPreferIP == c_vNetCfgList_[i].m_sIpRead)
                    return p_sPreferIP;
            }
        }

        // 在线IP不包含设定IP，则选第1个网卡IP
        for (uint32_t i = 0; i < c_vNetCfgList_.size(); i++)
        {
            if (c_vNetCfgList_[i].m_sIpRead != "")
                return c_vNetCfgList_[i].m_sIpRead;
        }
        return "127.0.0.1";
    }

    /**
     * @description: 填充IP库
     * @param {string} p_sBasicsIPInfo 基础IP 找同网段ping不通的IP
     * @return {*}
     */
    void fillIpLibrary(std::string p_sBasicsIPInfo,
                       std::vector<std::string>& p_vsIPInfoLibrary,
                       uint32_t p_uiSize)
    {
        if (!p_uiSize)
            return;
        std::string l_sBasicsIPInfo = p_sBasicsIPInfo;
        uint8_t l_uiOffset, l_uiCurrId;
        uint32_t l_uiFillIpId = 0;
        if (!getIpSegment_(l_sBasicsIPInfo, l_uiCurrId))
        {
            // printf("fillIpLibrary fail\n");
            return;
        }

        l_uiOffset = (l_uiCurrId + 1) % 256;
        // 跳过0 1
        if (l_uiOffset < 2)
            l_uiOffset = 2;
        while (l_uiCurrId != l_uiOffset)
        {
            std::string l_sPingIp = l_sBasicsIPInfo + std::to_string(l_uiOffset);
            bool l_bRes = isPing(l_sPingIp);
            // printf("ping %d | %s\n", l_bRes, l_sPingIp.c_str());
            if (!l_bRes)
            {
                p_vsIPInfoLibrary.push_back(l_sPingIp);
                l_uiFillIpId++;
                if (l_uiFillIpId == p_uiSize)
                    break;
            }
            l_uiOffset = (l_uiOffset + 1) % 256;
            if (l_uiOffset < 2)
                l_uiOffset = 2;
        }

        // for (uint32_t i = 0; i < p_vsIPInfoLibrary.size(); i++)
        //     printf("fillIpLibrary[%d]:%s\n", i, p_vsIPInfoLibrary[i].c_str());
    }

    /**
     * @description: 测试IP是否可以ping通
     * @param {const std::string&} p_ip
     * @return {*}
     */
    bool isPing(const std::string& ip)
    {
        std::string strCmd = "ping " + ip + " -c 2";
        std::string strRe = getCmdResult_(strCmd);
        if (strRe.find(", 0%") != std::string::npos)
            return true;
        else
            LOGW(WWARN, "{} ping fail | {}", WJLog::getWholeSysTime(), strRe);
        return false;
    }

    /**
     * @description: 获取物理网卡的IP->以此判断网络是否连接且正常
     * @param {std::string} p_sNetDev 网卡名
     * @return {p_sNetDev} 返回读取的IP
     * @other:
     */
    std::string getNetRealIP(std::string p_sNetDev)
    {
        if (p_sNetDev == "")
            return "";
        char buf[2048];
        struct ifreq ifr;
        struct ifconf ifc;
        struct sockaddr_in* sa = NULL;

        int fd = socket(AF_INET, SOCK_STREAM, 0);
        if (fd == -1)
        {
            LOGW(WERROR, "{} get net ip  socket error", WJLog::getWholeSysTime());
            return "";
        }

        ifc.ifc_len = sizeof(buf);
        ifc.ifc_buf = buf;
        strcpy(ifr.ifr_name, p_sNetDev.c_str());
        if (ioctl(fd, SIOCGIFADDR, &ifr) < 0)
        {
            LOGW(WERROR, "{} get net ip  error | {}", WJLog::getWholeSysTime(), strerror(errno));
            return "";
        }
        sa = (struct sockaddr_in*)(&ifr.ifr_addr);
        return (char*)inet_ntop(AF_INET, &sa->sin_addr, buf, sizeof(buf));
    }

    NetCfg(std::string p_sNetCfgPath, wj_slam::s_FaeLogCfg& p_fae)
        : c_sCfgFilePath_(p_sNetCfgPath), c_fae(p_fae)
    {
        // ifconfig 获取 物理网卡名称
        c_vNetNameList =
            vectorDifference_(getNetList_(c_sAllNetCMD), getNetList_(c_sVirtualNetCMD));
        loadNetParam_(c_sCfgFilePath_.c_str());
    }
    ~NetCfg() {}
};