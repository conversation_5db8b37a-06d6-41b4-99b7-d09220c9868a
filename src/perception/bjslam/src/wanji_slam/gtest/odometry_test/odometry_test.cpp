/**
 * @file odometry_test.cc
 * <AUTHOR>
 * @brief
 * @version 1.0
 * @date 2023-07-12
 */
#include "../../include/common/common_ex.h"
#include "../../test/odomerty/odometry.h"
#include "../../test/param.hpp"
#include <gtest/gtest.h>
#include <pcl/filters/voxel_grid.h>

WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;

namespace wj_slam {
class odometry_test : public testing::Test {
  protected:
    typedef pcl::PointXYZHSV PoinT;
    typedef boost::shared_ptr<Odometry<PoinT>> Ptr;
    typedef typename KEYFRAME<PoinT>::Ptr KEYFRAME_PTR;
    typedef typename FEATURE_PAIR<PoinT>::Ptr FEATURE_PAIR_PTR;
    typedef s_POSE6D MoveSpeed;
    typedef s_POSE6D PoseDev;
    typedef s_POSE6D Pose;

    std::thread odom;
    SYSPARAM* c_stSysParam_;
    Odometry<PoinT>::Ptr c_odometry;
    pcl::PointCloud<PoinT>::Ptr c_pcMergeNewCloud_;
    generalLog* glog = new generalLog("/home/<USER>/slam_wsv2.0/src/wanji_slam/data/Log/Logfiles",
                                      "wj",
                                      FileMode::DAILY_ROTATE,
                                      LogMode::TRIAL_MODE,
                                      10);
    boost::function<void(KEYFRAME_PTR)> c_isKeyFrameCb_;
    Queue<FEATURE_PAIR_PTR> c_featureBuf;
    Queue<KEYFRAME_PTR> c_keyFramesQueue_;
    boost::shared_ptr<LaserRegistration<PoinT, PoinT>> c_pMatcher_;
    int TIME_FIELDS_OFFSET = (offsetof(PoinT, s) / 4UL);

    void locationcallback(KEYFRAME_PTR p_keyframe)
    {
        if (c_stSysParam_->m_fae.m_bPrintfTimeLog)
            LOGW(WINFO,
                 "{} 雷达 [-] 帧 {} Lo at {} {}",
                 WJLog::getWholeSysTime(),
                 p_keyframe->m_pFeature->m_uiScanFrame,
                 p_keyframe->m_pFeature->m_dTimestamp,
                 p_keyframe->m_pFeature->m_iRecvTimestamp);

        c_keyFramesQueue_.push(p_keyframe);
        if (p_keyframe->m_pFeature->m_uiScanFrame == 10)
            c_stSysParam_->m_bDebugModel = true;
    }
    bool getSendInfoState(void)
    {
        if (!c_stSysParam_->m_bDebugModel
            && (c_stSysParam_->m_iWorkMode == WorkMode::InitMapMode
                || c_stSysParam_->m_iWorkMode == WorkMode::ContMapMode))
        {
            return true;
        }
        return false;
    }
    void poseCallbackOdom(std::vector<s_POSE6D> pose)
    {
        if (getSendInfoState())
        {
            pose.front().printf("debug increaseOdom");
            pose.back().printf("debug increaseOdom");
        }
        else if (c_stSysParam_->m_bDebugModel)
        {
            pose.front().printf("dedeubg increaseOdom");
            pose.back().printf("dedebug increaseOdom");
        }
    }
    bool mergeMap(pcl::PointCloud<PoinT>::Ptr& newMap)
    {
        bool l_bMapUpdate = false;
        static long l_lLastPubTime = 0;
        static long l_lThisTime = 0;
        l_lThisTime = clock();
        if (l_lThisTime - l_lLastPubTime > 2000)
        {
            pcl::VoxelGrid<PoinT> l_downSizeFilter;
            l_downSizeFilter.setLeafSize(0.4, 0.4, 0.8);
            *c_pcMergeNewCloud_ += *newMap;
            l_downSizeFilter.setInputCloud(c_pcMergeNewCloud_);
            l_downSizeFilter.filter(*c_pcMergeNewCloud_);
            l_bMapUpdate = true;
            l_lLastPubTime = l_lThisTime;
        }
        // 用于外部非实时发送Map
        return l_bMapUpdate;
    }
    void pcCallbackOdom(std::vector<boost::shared_ptr<KEYFRAME<PoinT>>> pc)
    {
        // 如果不是建图模式,则不使用odom发送地图
        if (c_stSysParam_->m_iWorkMode != WorkMode::InitMapMode
            && c_stSysParam_->m_iWorkMode != WorkMode::ContMapMode)
        {
            return;
        }

        pcl::PointCloud<PoinT>::Ptr l_pcNewCloud(new pcl::PointCloud<PoinT>());
        pcl::copyPointCloud(*pc[0]->m_pFeature->allPC, *l_pcNewCloud);
        c_odometry->c_pMatcher_->transformCloudPoints(
            pc[0]->m_Pose.m_quat, pc[0]->m_Pose.m_trans, l_pcNewCloud, l_pcNewCloud);
        // 不论是不是debug模式,均mergemap
        bool l_bMapUpdate = mergeMap(l_pcNewCloud);
        // 建图且非debug情况下发送
        if (c_stSysParam_->m_bDebugModel)
            return;
    }
    void fillPcdNameList_(std::vector<std::string>& l_vfiles, std::string l_sPath)
    {
        boost::filesystem::path p = boost::filesystem::path(l_sPath);
        if (!boost::filesystem::exists(p))
            return;
        for (auto i = boost::filesystem::directory_iterator(p);
             i != boost::filesystem::directory_iterator();
             i++)
        {
            if (!boost::filesystem::is_directory(i->path()))
            {
                l_vfiles.push_back(i->path().filename().string());
            }
        }
        if (!l_vfiles.empty())
            sort(l_vfiles.begin(), l_vfiles.end());
    }
    void rebaseCloudTime(boost::shared_ptr<pcl::PointCloud<PoinT>> p_pPc, float p_fTimeDiff)
    {
        u_int l_iCldSize = p_pPc->size();
        for (u_int i = 0; i < l_iCldSize; ++i)
            *(ptime(p_pPc->points[i])) += p_fTimeDiff;
    }
    inline float* ptime(PoinT& p_Pnt)
    {
        return reinterpret_cast<float*>(&p_Pnt) + TIME_FIELDS_OFFSET;
    }
    void lidarsTimeReBase(std::vector<FEATURE_PAIR_PTR>& p_frames,
                          int& p_iTimeStamp,
                          int& p_iRecvTimeStamp,
                          int& p_iTimeSpan,
                          int& p_iBaseQueue)
    {
        // 初始化时间戳为最大值
        p_iTimeStamp = INT_MAX;
        int l_iTimeEndFrame = INT_MIN;
        // 计算同步帧的最旧时间
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            if (p_frames[i]->m_dTimestamp <= p_iTimeStamp)
            {
                p_iTimeStamp = p_frames[i]->m_dTimestamp;
                p_iRecvTimeStamp = p_frames[i]->m_iRecvTimestamp;
                p_iBaseQueue = i;
            }
            if (p_frames[i]->m_dTimestamp >= l_iTimeEndFrame)
            {
                l_iTimeEndFrame = p_frames[i]->m_dTimestamp;
            }
        }
        // 计算最大时间间隔(首帧起点到最后一帧终点)
        p_iTimeSpan = l_iTimeEndFrame + SCAN_TIME_MS - p_iTimeStamp;
        // LOGP(WDEBUG,"rebase to lidar-{}'s time.", p_iBaseQueue);
    }
    void pointsTimeReBase(std::vector<FEATURE_PAIR_PTR>& p_frames, int p_iTimeStamp, int p_iBaseQueue)
    {
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            // 时间基准帧不需要计算偏移
            if (i == p_iBaseQueue)
                continue;
            // 计算毫秒单位下的时间偏移量
            float l_fTimeDiff = p_frames[i]->m_dTimestamp - p_iTimeStamp;
            // 刷新时间偏移量
            rebaseCloudTime(p_frames[i]->second, l_fTimeDiff);
        }
    }
    void sample2ndArrangeMerg(std::vector<FEATURE_PAIR_PTR>& p_frames, FEATURE_PAIR_PTR& p_mergFe)
    {
        boost::shared_ptr<pcl::PointCloud<PoinT>> l_pPcS, l_pMergS;
        l_pMergS = p_mergFe->second;
        int l_iSampleSize = 0;
        int l_iMergSampleSize = 0;
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            l_pPcS = p_frames[i]->second;
            std::cout<<p_frames[i]->m_iSample2ndSize<<std::endl;
            l_iSampleSize = p_frames[i]->m_iSample2ndSize;
            // 在上个采样点序列后插入新采样点
            l_pMergS->insert(l_pMergS->begin() + l_iMergSampleSize,
                             l_pPcS->begin(),
                             l_pPcS->begin() + l_iSampleSize);
            // 更新采样点序列长度
            l_iMergSampleSize += l_iSampleSize;
            // 在尾部插入非采样点
            l_pMergS->insert(l_pMergS->end(), l_pPcS->begin() + l_iSampleSize, l_pPcS->end());
            // LOGP(WDEBUG,"merg lidar-{}'s 2ndPC to offset {}.", i, l_iMergSampleSize);
            std::cout<<l_pMergS->size()<<std::endl;
        }
        p_mergFe->m_iSample2ndSize = l_iMergSampleSize;
    }
    void mergLidars(std::vector<FEATURE_PAIR_PTR>& p_frames,
                    int& p_iTimeStamp,
                    int& p_iRecvTimeStamp,
                    int& p_iTimeSpan,
                    FEATURE_PAIR_PTR& p_mergFe)
    {
        // 基准时间戳所在雷达序号
        int l_iBaseQueue = -1;
        // 设置时间基准为序列最旧时间戳
        lidarsTimeReBase(p_frames, p_iTimeStamp, p_iRecvTimeStamp, p_iTimeSpan, l_iBaseQueue);
        std::cout<<p_iTimeStamp<<" "<<p_iTimeSpan<<std::endl;
        // 以时间基准起修改每个点的时间
        pointsTimeReBase(p_frames, p_iTimeStamp, l_iBaseQueue);
        // 合成其他点云
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            // *p_mergFe->first += *p_frames[i]->first;
            // *p_mergFe->third += *p_frames[i]->third;
            // *p_mergFe->fourth += *p_frames[i]->fourth;
            // *p_mergFe->allPC += *p_frames[i]->allPC;
            // 帧ID
            p_mergFe->m_uiScanFrame = p_frames[i]->m_uiScanFrame;
        }
        // 重新排列面点和采样offset
        sample2ndArrangeMerg(p_frames, p_mergFe);
        // for (int i = 0; i < (int)p_frames.size(); ++i)
        // {
        //     if (p_frames[i] == nullptr)
        //         continue;
        //     p_mergFe->m_bHasContext = p_frames[i]->m_bHasContext;
        //     if (p_mergFe->m_bHasContext)
        //     {
        //         *p_mergFe->sc = *p_frames[i]->sc;
        //         break;
        //     }
        // }
    }
    void SetUp() override
    {
        Param l_param(false);
        l_param.loadSysParam("/home/<USER>/slam_wsv2.0/src/wanji_slam");
        c_stSysParam_ = SYSPARAM::getIn();
        c_pcMergeNewCloud_.reset(new pcl::PointCloud<PoinT>());
        c_odometry.reset(
            new Odometry<PoinT>(c_featureBuf,
                                boost::bind(&odometry_test::locationcallback, this, _1),
                                offsetof(PoinT, s),
                                -1,
                                boost::bind(&odometry_test::poseCallbackOdom, this, _1),
                                boost::bind(&odometry_test::pcCallbackOdom, this, _1)));
    }
    void TearDown() override
    {
        // c_odometry->~Odometry();
    }
};

/*所有测试之前请将odometry.h中gtest_test.h头文件包含引用打开
  测试目的：测试Odometry构造函数，构造函数内部执行的参数类实例化、参数初始化、根据工作模式配置相应参数
  测试前置：user.yaml为初始文件*/
// TEST_F(odometry_test, Odometry)
// {
//     EXPECT_EQ(c_odometry->c_bOdomRun_, false);
//     EXPECT_EQ(c_odometry->c_stSysParam_->m_iWorkMode, 0);

//     c_stSysParam_->m_iWorkMode = 1;
//     c_odometry->setWorkMode(c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_odometry->c_bOdomRun_, true);
//     EXPECT_EQ(c_odometry->c_bOnlyLocationMode_, false);
//     EXPECT_EQ(c_odometry->c_stSysParam_->m_iWorkMode, 1);

//     c_stSysParam_->m_iWorkMode = 3;
//     c_odometry->setWorkMode(c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_odometry->c_bOdomRun_, true);
//     EXPECT_EQ(c_odometry->c_bOnlyLocationMode_, true);
//     EXPECT_EQ(c_odometry->c_stSysParam_->m_iWorkMode, 3);

//     EXPECT_EQ(c_odometry->c_pMatcher_->c_iIte_, 1);
//     EXPECT_EQ(c_odometry->c_pMatcher_->c_b2dMatch_, true);
//     EXPECT_EQ(c_odometry->c_pMatcher_->c_fZThd_, 2.0);
// }

// TEST_F(odometry_test, hasPointCloud_)
// {
//     EXPECT_EQ(c_odometry->hasPointCloud_(), false);

//     for (int i = 0; i < 10; i++)
//     {
//         FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//         l_feature->m_dTimestamp = i;
//         l_feature->m_uiScanFrame = i;
//         c_featureBuf.push(l_feature);
//     }
//     EXPECT_EQ(c_odometry->hasPointCloud_(), false);

//     for (int i = 0; i < 10; i++)
//     {
//         FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//         l_feature->m_dTimestamp = i;
//         l_feature->m_uiScanFrame = i;
//         c_featureBuf.push(l_feature);
//     }
//     c_odometry->start();
//     c_odometry->c_bShutDown_ = true;
//     EXPECT_EQ(c_odometry->hasPointCloud_(), false);

//     c_odometry->c_bShutDown_ = false;
//     for (int i = 0; i < 10; i++)
//     {
//         FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//         l_feature->m_dTimestamp = i;
//         l_feature->m_uiScanFrame = i;
//         c_featureBuf.push(l_feature);
//     }
//     c_odometry->start();
//     EXPECT_EQ(c_odometry->hasPointCloud_(), true);

//     c_stSysParam_->m_iWorkMode = 1;
//     c_odometry->setWorkMode(c_stSysParam_->m_iWorkMode);
//     c_odometry->stop();
//     EXPECT_EQ(c_odometry->hasPointCloud_(), false);

//     c_stSysParam_->m_iWorkMode = 3;
//     c_odometry->setWorkMode(c_stSysParam_->m_iWorkMode);
//     for (int i = 0; i < 10; i++)
//     {
//         FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//         l_feature->m_dTimestamp = i;
//         l_feature->m_uiScanFrame = i;
//         c_featureBuf.push(l_feature);
//     }
//     EXPECT_EQ(c_odometry->hasPointCloud_(), true);

//     c_odometry->stop();
//     EXPECT_EQ(c_odometry->hasPointCloud_(), false);
// }

// TEST_F(odometry_test, getCurrFeature_)
// {
//     FEATURE_PAIR_PTR l_fea = c_odometry->getCurrFeature_();
//     for (int i = 0; i < 10; i++)
//     {
//         FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//         l_feature->m_dTimestamp = i;
//         l_feature->m_uiScanFrame = i;
//         c_featureBuf.push(l_feature);
//         l_fea = c_odometry->getCurrFeature_();
//         EXPECT_EQ(l_fea->m_dTimestamp, i);
//         EXPECT_EQ(l_fea->m_uiScanFrame, i);
//         EXPECT_EQ(c_odometry->c_featureBuf_.size(), 0);
//     }
// }

/*测试目的：畸变修正
  测试前置：需要加载本地畸变点云数据*/
// TEST_F(odometry_test, corretPC_)
// {
//     // 单雷达畸变修正验证
//     FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//     s_POSE6D l_pose;
//     pcl::io::loadPCDFile("/home/<USER>/下载/畸变数据/data/x0y0y60/Cloud.pcd", *l_feature->first);
//     // pcl::io::loadPCDFile("/home/<USER>/下载/畸变数据/data/real/Cloud.pcd", *l_feature->first);
//     for (size_t i = 0; i < l_feature->first->size(); i++)
//     {
//         l_feature->first->points[i].s = (float)i * 125 / 7200;
//     }
//     l_feature->m_dTimespan = 125;
//     l_pose.setXYZ(0, 0, 0);
//     l_pose.setRPY(0, 0, 3.75);
//     s_POSE6D l_sPoseTimeSpan = l_pose * (l_feature->m_dTimespan / 125.0);
//     // 修正到帧头
//     c_odometry->c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
//                                                  Eigen::Vector3d::Zero(),
//                                                  l_sPoseTimeSpan.m_quat,
//                                                  l_sPoseTimeSpan.m_trans,
//                                                  l_feature->first,
//                                                  l_feature->first,
//                                                  l_feature->m_dTimespan,
//                                                  false);
//     pcl::io::savePCDFileBinary("/home/<USER>/下载/畸变数据/data/real/true.pcd", *l_feature->first);
//     // 修正到帧尾
//     // pcl::io::loadPCDFile("/home/<USER>/下载/畸变数据/data/x0y0y60/Cloud.pcd", *l_feature->first);
//     pcl::io::loadPCDFile("/home/<USER>/下载/畸变数据/data/x0y0y60/Cloud.pcd", *l_feature->first);
//     // pcl::io::loadPCDFile("/home/<USER>/下载/畸变数据/data/real/Cloud.pcd", *l_feature->first);
//     for (size_t i = 0; i < l_feature->first->size(); i++)
//     {
//         l_feature->first->points[i].s = (float)i * 125 / 7200;
//     }
//     l_feature->m_dTimespan = 125;
//     l_pose.setXYZ(0, 0, 0);
//     l_pose.setRPY(0, 0, 3.625);
//     l_sPoseTimeSpan = l_pose * (l_feature->m_dTimespan / 125.0);
//     c_odometry->c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
//                                                  Eigen::Vector3d::Zero(),
//                                                  l_sPoseTimeSpan.m_quat,
//                                                  l_sPoseTimeSpan.m_trans,
//                                                  l_feature->first,
//                                                  l_feature->first,
//                                                  l_feature->m_dTimespan,
//                                                  false);
//     pcl::io::savePCDFileBinary("/home/<USER>/下载/畸变数据/data/real/false.pcd", *l_feature->first);

//     s_POSE6D l_speed = l_pose.inverse();
//     // c_odometry->c_pMatcher_->transformCloudPoints(l_speed.m_quat, l_speed.m_trans,
//     l_feature->second, l_feature->second);
//     // pcl::io::savePCDFileBinary("/home/<USER>/下载/畸变数据/data/real/trans.pcd",
//     *l_feature->second);

//     // 双雷达畸变修正验证
//     FEATURE_PAIR_PTR l_fFront(new FEATURE_PAIR<PoinT>());
//     FEATURE_PAIR_PTR l_fback(new FEATURE_PAIR<PoinT>());
//     FEATURE_PAIR_PTR l_fout(new FEATURE_PAIR<PoinT>());

//     s_POSE6D l_pose;
//     pcl::io::loadPCDFile("/home/<USER>/文档/720SLAM开发记录/720SLAM里程计模块/双雷达畸变数据/back_/frame_28654.pcd", *l_fFront->second);
//     pcl::io::loadPCDFile("/home/<USER>/文档/720SLAM开发记录/720SLAM里程计模块/双雷达畸变数据/front_/frame_32401.pcd", *l_fback->second);
//     l_fFront->m_dTimestamp = 286;
//     l_fFront->m_iRecvTimestamp = 287;
//     l_fFront->m_dTimespan = 100;
//     l_fFront->m_iSample2ndSize = 1000;

//     l_fback->m_dTimestamp = 330;
//     l_fback->m_iRecvTimestamp = 331;
//     l_fback->m_dTimespan = 100;
//     l_fback->m_iSample2ndSize = 1000;

//     std::vector<FEATURE_PAIR_PTR> p_frames;
//     p_frames.push_back(l_fFront);
//     p_frames.push_back(l_fback);
//     // for (size_t i = 0; i < l_feature->first->size(); i++)
//     // {
//     //     l_feature->first->points[i].s = (float)i * 100 / 1800;
//     // }
//     // for (size_t i = 0; i < l_feature->second->size(); i++)
//     // {
//     //     l_feature->second->points[i].s = (float)i * 100 / 1800;
//     // }
//     int l_iTimeStamp = 0, l_iRecvTimeStamp = 0, l_iTimeSpan = 0;
//     mergLidars(p_frames, l_iTimeStamp, l_iRecvTimeStamp, l_iTimeSpan, l_fout);
//     l_fout->m_dTimestamp = l_iTimeStamp;
//     l_fout->m_iRecvTimestamp = l_iRecvTimeStamp;
//     l_fout->m_dTimespan = l_iTimeSpan;

//     l_pose.setXYZ(0, 0, 0);
//     l_pose.setRPY(0, 0, -6.65);
//     std::cout<<l_fout->m_dTimespan<<std::endl;
//     s_POSE6D l_sPoseTimeSpan = l_pose * (l_fout->m_dTimespan / 100.0);
//     // 修正到帧头
//     // c_odometry->c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
//     //                                              Eigen::Vector3d::Zero(),
//     //                                              l_sPoseTimeSpan.m_quat,
//     //                                              l_sPoseTimeSpan.m_trans,
//     //                                              l_feature->first,
//     //                                              l_feature->first,
//     //                                              l_feature->m_dTimespan,
//     //                                              false);
//     c_odometry->c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
//                                                  Eigen::Vector3d::Zero(),
//                                                  l_sPoseTimeSpan.m_quat,
//                                                  l_sPoseTimeSpan.m_trans,
//                                                  l_fout->second,
//                                                  l_fout->second,
//                                                  l_fout->m_dTimespan,
//                                                  false);
//     // pcl::io::savePCDFileBinary("/home/<USER>/slam_verify/output/back.pcd", *l_feature->first);
//     pcl::io::savePCDFileBinary("/home/<USER>/文档/720SLAM开发记录/720SLAM里程计模块/双雷达畸变数据/out.pcd", *l_fout->second);
// }

// TEST_F(odometry_test, renewMap_)
// {
//   FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//   s_POSE6D l_pose;
//   for (int i = 0; i < 10; i++)
//   {
//     PoinT l_pt;
//     l_pt.x = i;
//     l_pt.y = i;
//     l_pt.z = 0;
//     l_feature->m_dTimestamp = i;
//     l_feature->m_uiScanFrame = i;
//     l_feature->second->points.push_back(l_pt);
//   }
//   l_pose.setXYZ(0.0, 0.0, 0.0);
//   l_pose.setRPY(90.0, 0.0, 0.0);
//   (l_pose.inverse()).printf("pose");
//   c_odometry->renewMap_(l_feature, l_pose);
//   EXPECT_EQ(c_odometry->c_pFeatureMap_->second->size(), 10);
//   for (int j = 0; j < 10; j++)
//   {
//     EXPECT_FLOAT_EQ(l_feature->second->points[j].x, j);
//   }

//   c_odometry->setWorkMode(3);
//   l_feature->free();
//   for (int i = 0; i < 10; i++)
//   {
//     PoinT l_pt;
//     l_pt.x = i;
//     l_pt.y = i;
//     l_pt.z = 0;
//     l_feature->m_dTimestamp = i;
//     l_feature->m_uiScanFrame = i;
//     l_feature->second->points.push_back(l_pt);
//   }
//   l_pose.setXYZ(100.0, 0.0, 0.0);
//   l_pose.setRPY(0.0, 0.0, 0.0);
//   c_odometry->renewMap_(l_feature, l_pose);
//   EXPECT_EQ(c_odometry->c_pFeatureMap_ == nullptr, true);
//   for (int j = 0; j < 10; j++)
//   {
//     EXPECT_FLOAT_EQ(l_feature->second->points[j].x, j);
//   }
// }

// TEST_F(odometry_test, setTarget_)
// {
//   FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//   c_odometry->setTarget_(l_feature);
//   EXPECT_EQ(c_odometry->c_pMatcher_->c_pTgtCorPc_ == nullptr, true);
//   EXPECT_EQ(c_odometry->c_pMatcher_->c_pTgtSurPc_ == nullptr, true);
//   EXPECT_EQ(c_odometry->c_pMatcher_->c_pTgtMarkCorPc_ == nullptr, true);

//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_feature->first);
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_feature->second);
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corr.pcd", *l_feature->third);

//   c_odometry->setTarget_(l_feature);
//   EXPECT_EQ(c_odometry->c_pMatcher_->c_pTgtCorPc_ != nullptr, true);
//   EXPECT_EQ(c_odometry->c_pMatcher_->c_pTgtSurPc_ != nullptr, true);
//   EXPECT_EQ(c_odometry->c_pMatcher_->c_pTgtMarkCorPc_ != nullptr, true);
// }

// TEST_F(odometry_test, location_)
// {
//   FEATURE_PAIR_PTR l_fea(new FEATURE_PAIR<PoinT>());
//   FEATURE_PAIR_PTR l_map(new FEATURE_PAIR<PoinT>());
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_fea->first);
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_fea->second);
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_fea->third);
//   l_fea->m_dTimespan = 100;
//   c_odometry->c_pCurrFeature_ = l_fea;
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_map->first);
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_map->second);
//   pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_map->third);

//   for (size_t i = 0; i < l_fea->second->size(); i++)
//   {
//     l_fea->second->points[i].x += 0.1;
//   }
//   for (size_t j = 0; j < l_fea->first->size(); j++)
//   {
//     l_fea->first->points[j].x += 0.1;
//   }

//   c_odometry->c_bSysHasInit = true;
//   EXPECT_EQ(c_odometry->c_bSysHasInit, true);
//   c_odometry->setTarget_(l_map);
//   c_odometry->location_(l_fea, l_map);
//   c_odometry->c_stIncreaseOpt_.printf("optimize");
//   pcl::io::savePCDFileBinary("/home/<USER>/slam_wsv2.0/output/match.pcd",
//   *c_odometry->c_pCurrFeature_->second);
// }

// TEST_F(odometry_test, update_)
// {
//     c_odometry->setWorkMode(1);
//     EXPECT_EQ(c_odometry->c_bOnlyLocationMode_, false);
//     c_odometry->c_stIncreaseOpt_.setXYZ(1.0, 0, 0);
//     c_odometry->c_fJumpNum_ = 0;
//     c_odometry->c_pCurrFeature_.reset(new FEATURE_PAIR<PoinT>());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->first);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//                          *c_odometry->c_pCurrFeature_->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->third);

//     c_odometry->update_();
//     EXPECT_FLOAT_EQ(c_odometry->c_stCurSpeed_.x(), 1.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stCurSpeed_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stCurSpeed_.z(), 0.0);

//     EXPECT_FLOAT_EQ(c_odometry->c_stIncrease_.x(), 1.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stIncrease_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stIncrease_.z(), 0.0);

//     EXPECT_NEAR(c_odometry->c_stIncreaseOdom_.x(), 1.0, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdom_.y(), 0.0, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdom_.z(), 0.0, 0.1l);

//     EXPECT_NEAR(c_odometry->c_stIncreaseOdomLast_.x(), 1.0, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdomLast_.y(), 0.0, 0.01l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdomLast_.z(), 0.0, 0.01l);

//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomCur_.x(), 1.0, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomCur_.y(), 0.0, 0.01l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomCur_.z(), 0.0, 0.01l);

//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomPredict_.x(), 2.0, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomPredict_.y(), 0.0, 0.01l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomPredict_.z(), 0.0, 0.01l);

//     EXPECT_EQ(c_odometry->c_pFeatureMap_->first->size(),
//               c_odometry->c_pCurrFeature_->first->size());
//     EXPECT_EQ(c_odometry->c_pFeatureMap_->second->size(),
//               c_odometry->c_pCurrFeature_->second->size());
//     EXPECT_EQ(c_odometry->c_pFeatureMap_->third->size(),
//               c_odometry->c_pCurrFeature_->third->size());

//     c_odometry->c_stHPrecsPose_.setXYZ(1.0, 0, 0);
//     c_odometry->c_stOdomDev_ =
//         c_odometry->c_stHPrecsPose_ * c_odometry->c_stIncreaseOdom_.inverse();
//     c_odometry->c_stIncreaseOpt_.setXYZ(0.1, 0, 0);
//     c_odometry->update_();

//     EXPECT_FLOAT_EQ(c_odometry->c_stCurSpeed_.x(), 1.1);
//     EXPECT_FLOAT_EQ(c_odometry->c_stCurSpeed_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stCurSpeed_.z(), 0.0);

//     EXPECT_FLOAT_EQ(c_odometry->c_stIncrease_.x(), 1.1);
//     EXPECT_FLOAT_EQ(c_odometry->c_stIncrease_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stIncrease_.z(), 0.0);

//     EXPECT_NEAR(c_odometry->c_stIncreaseOdom_.x(), 2.1, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdom_.y(), 0.0, 0.01l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdom_.z(), 0.0, 0.01l);

//     EXPECT_NEAR(c_odometry->c_stIncreaseOdomLast_.x(), 2.1, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdomLast_.y(), 0.0, 0.01l);
//     EXPECT_NEAR(c_odometry->c_stIncreaseOdomLast_.z(), 0.0, 0.01l);

//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomCur_.x(), 2.1, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomCur_.y(), 0.0, 0.01l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomCur_.z(), 0.0, 0.01l);

//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomPredict_.x(), 3.2, 0.1l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomPredict_.y(), 0.0, 0.01l);
//     EXPECT_NEAR(c_odometry->c_stHPrecsOdomPredict_.z(), 0.0, 0.01l);

//     c_odometry->setWorkMode(3);
//     EXPECT_EQ(c_odometry->c_bOnlyLocationMode_, true);
//     c_odometry->c_stHPrecsPose_.setXYZ(1.0, 0, 0);
//     c_odometry->c_stOdomDev_ =
//         c_odometry->c_stHPrecsPose_ * c_odometry->c_stIncreaseOdom_.inverse();
//     c_odometry->c_stCurSpeed_.setXYZ(1.0, 0, 0);
//     c_odometry->c_fJumpNum_ = 1.0;

//     c_odometry->update_();
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomCur_.x(), 2.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomCur_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomCur_.z(), 0.0);

//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomPredict_.x(), 2.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomPredict_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomPredict_.z(), 0.0);

//     EXPECT_EQ(c_odometry->c_pFeatureMap_ == nullptr, true);

//     c_odometry->c_stHPrecsPose_.setXYZ(2.0, 0, 0);
//     c_odometry->c_stOdomDev_ =
//         c_odometry->c_stHPrecsPose_ * c_odometry->c_stIncreaseOdom_.inverse();
//     c_odometry->c_fJumpNum_ = 2.0;
//     c_odometry->update_();
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomCur_.x(), 4.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomCur_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomCur_.z(), 0.0);

//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomPredict_.x(), 4.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomPredict_.y(), 0.0);
//     EXPECT_FLOAT_EQ(c_odometry->c_stHPrecsOdomPredict_.z(), 0.0);
// }

/*此测试用例用来测试速度判定方法与边界值，后续测试中需要根据当前速度公式进行调整*/
// TEST_F(odometry_test, addKeyFrame_)
// {
//     c_odometry->setWorkMode(1);
//     c_odometry->c_pCurrFeature_.reset(new FEATURE_PAIR<PoinT>());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->first);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//                          *c_odometry->c_pCurrFeature_->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->third);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->fourth);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->allPC);
//     c_odometry->addKeyFrame_();

//     c_odometry->c_pCheckKeyFrame_->c_lastSpeed_.setXYZ(0, 0, 0);
//     c_odometry->c_pCheckKeyFrame_->c_curSpeed_.setXYZ(1, 1, 1);
//     EXPECT_EQ(c_odometry->c_pCheckKeyFrame_->isUniformVelocity(), false);
//     float x = 0.01;
//     float y = 30;
//     c_odometry->c_pCheckKeyFrame_->c_lastSpeed_.setXYZ(x, x, 0);
//     for (size_t i = 0; i < 10000; i++)
//     {
//         c_odometry->c_pCheckKeyFrame_->c_curSpeed_.setXYZ(x + 0.001 * i, x + 0.001 * i, 0);
//         c_odometry->c_pCheckKeyFrame_->c_lastSpeed_.setRPY(0, 0, y);
//         c_odometry->c_pCheckKeyFrame_->c_curSpeed_.setRPY(0, 0, y + 0.001 * i);
//         c_odometry->c_pCheckKeyFrame_->c_curSpeed_.setXYZ(x, x, 0);

//         if (c_odometry->c_pCheckKeyFrame_->isUniformVelocity())
//         {
//             c_odometry->c_pCheckKeyFrame_->c_lastSpeed_ =
//                 c_odometry->c_pCheckKeyFrame_->c_curSpeed_;
//         }
//         if (i < 200)
//             EXPECT_EQ(c_odometry->c_pCheckKeyFrame_->isUniformVelocity(), true);
//         else
//             EXPECT_EQ(c_odometry->c_pCheckKeyFrame_->isUniformVelocity(), false);
//     }
//     for (size_t j = 0; j < 10; j++)
//     {
//         c_odometry->c_pCheckKeyFrame_->c_lastSpeed_.setXYZ(1, 1, 1);
//         c_odometry->c_pCheckKeyFrame_->c_lastSpeed_.setRPY(1, 1, 1);
//         c_odometry->c_pCheckKeyFrame_->c_curSpeed_.setXYZ(1, 1, 1);
//         c_odometry->c_pCheckKeyFrame_->c_curSpeed_.setRPY(1 + 3 * j, 3 + 3 * j, 3 + 3 * j);
//         EXPECT_EQ(c_odometry->c_pCheckKeyFrame_->isUniformVelocity(), false);
//     }
// }

// TEST_F(odometry_test, settingPose)
// {
//     c_odometry->stop();
//     while (!c_odometry->isStop())
//     {
//         sleepMs(1);
//     }
//     EXPECT_EQ(c_odometry->c_bOdomRun_, false);

//     c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::Default;
//     c_stSysParam_->m_pos.m_stSetPose.setXYZ(1.0, 1.0, 1.0);
//     c_stSysParam_->m_pos.m_stSetPose.setRPY(0, 0, 90.0);
//     c_odometry->start();
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.x(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.y(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.z(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.yaw(), 0);

//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.x(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.y(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.z(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.yaw(), 0);

//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.x(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.y(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.z(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.yaw(), 0);
//     EXPECT_EQ(c_odometry->c_bOdomRun_, true);

//     c_odometry->c_pCurrFeature_.reset(new FEATURE_PAIR<PoinT>());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->first);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//                          *c_odometry->c_pCurrFeature_->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->third);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->fourth);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd",
//                          *c_odometry->c_pCurrFeature_->allPC);
//     c_odometry->addKeyFrame_();
//     EXPECT_EQ(c_odometry->c_stHPrecsOdomPredict_.m_bFlag, PoseStatus::Default);

//     c_odometry->stop();
//     while (!c_odometry->isStop())
//     {
//         sleepMs(1);
//     }
//     c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
//     c_stSysParam_->m_pos.m_stSetPose.setXYZ(1.0, 1.0, 1.0);
//     c_stSysParam_->m_pos.m_stSetPose.setRPY(0, 0, 90.0);
//     c_odometry->start();
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.x(), 1.0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.y(), 1.0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.z(), 1.0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdom_.yaw(), 90.0);

//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.x(), 1.0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.y(), 1.0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.z(), 1.0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stIncreaseOdomLast_.yaw(), 90.0);

//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.x(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.y(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.z(), 0);
//     EXPECT_DOUBLE_EQ(c_odometry->c_stOdomDev_.yaw(), 0);

//     EXPECT_EQ(c_odometry->c_bOdomRun_, true);

//     c_odometry->addKeyFrame_();
//     EXPECT_EQ(c_odometry->c_stHPrecsOdomPredict_.m_bFlag, PoseStatus::SettingPose);
// }

// TEST_F(odometry_test, run)
// {
//     std::vector<std::string> l_vcorName;
//     std::vector<std::string> l_vsurName;
//     std::string l_corfile = "/home/<USER>/slam_wsv2.0/output/corn/";
//     std::string l_surfile = "/home/<USER>/slam_wsv2.0/output/surf/";
//     fillPcdNameList_(l_vcorName, l_corfile);
//     fillPcdNameList_(l_vsurName, l_surfile);
//     EXPECT_EQ(l_vcorName.size(), l_vsurName.size());
//     for (size_t i = 0; i < l_vcorName.size(); i++)
//     {
//         FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//         std::string l_cor = "/home/<USER>/slam_wsv2.0/output/corn/" + l_vcorName[i];
//         std::string l_sur = "/home/<USER>/slam_wsv2.0/output/surf/" + l_vsurName[i];
//         pcl::io::loadPCDFile(l_cor, *l_feature->first);
//         pcl::io::loadPCDFile(l_sur, *l_feature->second);
//         pcl::io::loadPCDFile(l_sur, *l_feature->third);
//         pcl::io::loadPCDFile(l_sur, *l_feature->fourth);
//         pcl::io::loadPCDFile(l_sur, *l_feature->allPC);
//         l_feature->m_dTimespan = 100;
//         l_feature->m_dTimestamp = i * 100;
//         l_feature->m_iRecvTimestamp = i * 99;
//         l_feature->m_iSample2ndSize = 2000;
//         l_feature->m_uiScanFrame = i;
//         c_featureBuf.push(l_feature);
//     }
//     c_stSysParam_->m_fae.m_bPrintfTimeLog = true;
//     c_stSysParam_->m_iWorkMode = WorkMode::InitMapMode;
//     c_odometry->setWorkMode(c_stSysParam_->m_iWorkMode);

//     odom = std::thread(&Odometry<PoinT>::run, c_odometry);
//     odom.detach();
//     for (size_t i = 0; i < l_vcorName.size(); i++)
//     {
//         FEATURE_PAIR_PTR l_feature(new FEATURE_PAIR<PoinT>());
//         std::string l_cor = "/home/<USER>/slam_wsv2.0/output/corn/" + l_vcorName[i];
//         std::string l_sur = "/home/<USER>/slam_wsv2.0/output/surf/" + l_vsurName[i];
//         pcl::io::loadPCDFile(l_cor, *l_feature->first);
//         pcl::io::loadPCDFile(l_sur, *l_feature->second);
//         pcl::io::loadPCDFile(l_sur, *l_feature->third);
//         pcl::io::loadPCDFile(l_sur, *l_feature->fourth);
//         pcl::io::loadPCDFile(l_sur, *l_feature->allPC);
//         l_feature->m_dTimespan = 100;
//         l_feature->m_dTimestamp = (i + 10) * 100;
//         l_feature->m_iRecvTimestamp = (i + 10) * 99;
//         l_feature->m_iSample2ndSize = 2000;
//         l_feature->m_uiScanFrame = (i + 10);
//         c_featureBuf.push(l_feature);
//     }
//     c_odometry->shutDown();
// }

}  // namespace wj_slam