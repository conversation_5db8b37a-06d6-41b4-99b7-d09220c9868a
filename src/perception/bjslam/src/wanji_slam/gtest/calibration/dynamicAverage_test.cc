/*
 * @Description: 测试dynamicAverage
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-04-12 16:08:01
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-04-29 13:52:53
 */
/*
 * 测试目标1: 测试runningStat类是否正确求得平均值与标准差
 * 测试目标2: 测试runningStatAngle类是否正确求得平均值与标准差
 */
#include "algorithm/calibration/mLidarCalib/impl/dynamicAverage.hpp"
#include  <gtest/gtest.h>
#include <random>

/**
 * @description: 要使用测试fixture，请从testing:: test派生一个类
 * @note: 每个TEST_F使用同一实例的副本，任何改变都不传递到其他测试用例
 */
class dynamicAverageTest : public testing::Test {
    // 让成员受保护，因此它们可以从子类访问
  protected:
    double getNativeMean()
    {
        double l_fMean = 0;
        int l_iSize = test_random_.size();
        for (int i = 0; i < l_iSize; ++i)
            l_fMean += test_random_[i];
        return l_fMean / l_iSize;
    }
    double getNativeStdDev(const double p_fMean)
    {
        double l_fVar = 0;
        int l_iSize = test_random_.size();
        double l_fSqMean = p_fMean * p_fMean;
        for (int i = 0; i < l_iSize; ++i)
            l_fVar += test_random_[i] * test_random_[i];
        l_fVar = l_fVar / l_iSize - l_fSqMean;
        return sqrt(l_fVar);
    }
    void setNDVector(const double p_fMean, const double p_fSigma, const int p_iSize)
    {
        // 生成随机数
        std::random_device rd;
        // 用 random_device产生一个真随机数，用作“伪随机数发生器”的种子
        std::mt19937 gen(rd());
        // 一个正态“分布器”，高斯分布器是 std::normal_distribution
        std::normal_distribution<double> dis(p_fMean, p_fSigma);
        test_random_.clear();
        for (int n = 0; n < p_iSize; ++n)
            test_random_.emplace_back(dis(gen));
        test_ref_mean_ = getNativeMean();
        test_ref_stddev_ = getNativeStdDev(test_ref_mean_);
    }
    // 模拟值变化的随机数值
    std::vector<double> test_random_;
    // 参考正确值
    double test_ref_mean_, test_ref_stddev_;
};

// 测试runningStat是否成功
TEST_F(dynamicAverageTest, runningStat)
{
    // 滑动平均值
    wj_slam::runningStat test1_;
    double l_fMean, l_fStdev;
    // 测试0附近正态分布
    setNDVector(0, 0.05, 1000);
    for (int i = 0; i < 1000; ++i)
        test1_.push(test_random_[i]);
    l_fMean = test1_.mean();
    l_fStdev = test1_.stdDev();
    EXPECT_NEAR(l_fMean, test_ref_mean_, 0.001l);
    EXPECT_NEAR(l_fStdev, test_ref_stddev_, 0.001l);
    test1_.clear();
    // 测试1000附近正态分布
    setNDVector(1000, 0.1, 1000);
    for (int i = 0; i < 1000; ++i)
        test1_.push(test_random_[i]);
    l_fMean = test1_.mean();
    l_fStdev = test1_.stdDev();
    EXPECT_NEAR(l_fMean, test_ref_mean_, 0.001l);
    EXPECT_NEAR(l_fStdev, test_ref_stddev_, 0.001l);
}
// 测试runningStatAngle是否成功
TEST_F(dynamicAverageTest, runningStatAngle)
{
    // 滑动平均值
    wj_slam::runningStatAngle test1_;
    double l_fMean, l_fStdev;
    // 测试0附近正态分布
    setNDVector(0, 0.1, 1000);
    for (int i = 0; i < 1000; ++i)
        test1_.push(test_random_[i]);
    l_fMean = test1_.mean();
    l_fStdev = test1_.stdDev();
    if (test_ref_mean_ > 180.0)
        test_ref_mean_ -= 360.0;
    else if (test_ref_mean_ < -180.0)
        test_ref_mean_ += 360.0;
    EXPECT_NEAR(l_fMean, test_ref_mean_, 0.001l);
    EXPECT_NEAR(l_fStdev, test_ref_stddev_, 0.001l);
    test1_.clear();
    // 测试180附近正态分布
    setNDVector(180, 1, 1000);
    for (int i = 0; i < 1000; ++i)
        test1_.push(test_random_[i]);
    l_fMean = test1_.mean();
    if (test_ref_mean_ > 180.0)
        test_ref_mean_ -= 360.0;
    else if (test_ref_mean_ < -180.0)
        test_ref_mean_ += 360.0;
    l_fStdev = test1_.stdDev();
    EXPECT_NEAR(l_fMean, test_ref_mean_, 0.001l);
    EXPECT_NEAR(l_fStdev, test_ref_stddev_, 0.001l);
}