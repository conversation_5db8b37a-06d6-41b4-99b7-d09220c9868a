/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-01 17:18:19
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-01-06 09:38:19
 * @FilePath: /slam_wsv2.0/src/wanji_slam/gtest/verify_test/verify_test.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "../../include/algorithm/verifytest/impl/laserVerifyScore.hpp"
#include "../../include/common/common_ex.h"
#include <gtest/gtest.h>
#include <pcl/search/kdtree.h>

class verifytest : public testing::Test {
  protected:
    typedef pcl::PointXYZHSV M;
    typedef Eigen::Matrix<int, 3, 1> KeyType;
    std::vector<KeyType> Nearbygrid;
    SYSPARAM* c_stSysParam_;
    IVox<3, M>* c_piVox;
    LaserVerifyScore<M, M>* c_pVerify;

    void SetUp() override
    {
        c_stSysParam_ = SYSPARAM::getIn();

        //设置大栅格内部小栅格尺寸大小
        c_piVox->setInnerResolu(c_stSysParam_->m_map.m_fInGridResolu);
        //设置大栅格尺寸大小
        c_piVox->setOuterResolu(c_stSysParam_->m_map.m_fOutGridResolu);
        //设置搜索栅格内部点数
        c_piVox->setSearchPointNum(c_stSysParam_->m_map.m_iGridSearchNum);
        //设置iVox栅格容量大小
        c_piVox->setGridCapacity(c_stSysParam_->m_map.m_iGridSize);
        //设置栅格周围栅格环绕类型
        c_piVox->setGridNearbyType(c_stSysParam_->m_map.m_iGridMatchType);

        c_piVox = new IVox<3, M>();
        c_pVerify = new GridMapScore<M, M>(*c_stSysParam_);
        c_pVerify->setGuassSize(0.1);
        Nearbygrid = {KeyType(0, 0, 0),
                      KeyType(0, 0, 1),
                      KeyType(0, 0, -1),
                      KeyType(0, 1, 0),
                      KeyType(0, -1, 0),
                      KeyType(1, 0, 0),
                      KeyType(-1, 0, 0)};
    }

    void TearDown() override
    {
        c_piVox = nullptr;
        c_pVerify = nullptr;
    }
};

// TEST_F(verifytest, calcuLaserVerifyScore)
// {
//     M pt;
//     pt.x = 1.0;
//     pt.y = 1.0;
//     pt.z = 1.0;
//     pt.v = 1.0;
//     pcl::PointCloud<M>::Ptr l_tgt(new pcl::PointCloud<M>());
//     for (int i = 0; i < Nearbygrid.size(); i++)
//     {
//         M l_pt = pt;
//         l_pt.x += Nearbygrid[i][0] * c_stSysParam_->m_map.m_fOutGridResolu;
//         l_pt.y += Nearbygrid[i][1] * c_stSysParam_->m_map.m_fOutGridResolu;
//         l_pt.z += Nearbygrid[i][2] * c_stSysParam_->m_map.m_fOutGridResolu;
//         l_tgt->points.emplace_back(l_pt);
//     }
//     c_piVox->addGridMap(l_tgt->points, 1);
//     c_pVerify->setInputTargetCloud(l_tgt);
//     pcl::PointCloud<M>::Ptr l_src(new pcl::PointCloud<M>());

//     M pt1;
//     pt1.x = 1.0;
//     pt1.y = 1.0;
//     pt1.z = 1.0;
//     pt1.v = 1.0;
//     l_src->points.emplace_back(pt1);

//     c_pVerify->setInputSourceCloud(l_src);
//     c_pVerify->setTargetGridMap(c_piVox);
//     c_pVerify->calcuLaserVerifyScore();
//     float c_fPercent_ = c_pVerify->getLaserVerifyScore();
//     float c_fMatchNum_ = c_pVerify->getLaserVerifyMatNum();
//     EXPECT_FLOAT_EQ(c_fPercent_, 1.0);
//     EXPECT_FLOAT_EQ(c_fMatchNum_, 1.0);

//     pt1.x = 2.0;
//     pt1.y = 2.0;
//     pt1.z = 2.0;
//     pt1.v = 2.0;
//     l_src->clear();
//     l_src->points.emplace_back(pt1);
//     c_pVerify->setInputSourceCloud(l_src);
//     c_pVerify->calcuLaserVerifyScore();
//     c_fPercent_ = c_pVerify->getLaserVerifyScore();
//     c_fMatchNum_ = c_pVerify->getLaserVerifyMatNum();
// }
