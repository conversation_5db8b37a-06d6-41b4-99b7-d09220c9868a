/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-10-31 14:27:55
 * @LastEditors: <PERSON><PERSON><NAME_EMAIL>
 * @LastEditTime: 2023-01-06 09:38:01
 * @FilePath: /slam_wsv2.0/src/wanji_slam/gtest/iVox_test/iVox_test.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "../../include/algorithm/iVoxtest/impl/iVox_test_map.hpp"
// #include "../../include/algorithm/iVox_test/iVox_test_node.hpp"
#include "common/common_ex.h"
#include <gtest/gtest.h>

namespace wj_slam {
class ivoxTest : public testing::Test {
  protected:
    typedef pcl::PointXYZHSV M;
    typedef Eigen::Matrix<int, 3, 1> KeyType;

    SYSPARAM* c_stSysParam_;

    IVoxNode<M, 3>* c_pNode;
    std::vector<KeyType> Nearbygrid;
    IVox<3, M>* c_piVox;

    void SetUp() override
    {
        c_stSysParam_ = SYSPARAM::getIn();

        //设置大栅格内部小栅格尺寸大小
        c_piVox->setInnerResolu(c_stSysParam_->m_map.m_fInGridResolu);
        //设置大栅格尺寸大小
        c_piVox->setOuterResolu(c_stSysParam_->m_map.m_fOutGridResolu);
        //设置搜索栅格内部点数
        c_piVox->setSearchPointNum(c_stSysParam_->m_map.m_iGridSearchNum);
        //设置iVox栅格容量大小
        c_piVox->setGridCapacity(c_stSysParam_->m_map.m_iGridSize);
        //设置栅格周围栅格环绕类型
        c_piVox->setGridNearbyType(c_stSysParam_->m_map.m_iGridMatchType);
        c_piVox = new IVox<3, M>();
        c_pNode = new IVoxNode<M, 3>(c_stSysParam_->m_map.m_fOutGridResolu,
                                     c_stSysParam_->m_map.m_fInGridResolu);
        Nearbygrid = {KeyType(0, 0, 0),
                      KeyType(0, 0, 1),
                      KeyType(0, 0, -1),
                      KeyType(0, 1, 0),
                      KeyType(0, -1, 0),
                      KeyType(1, 0, 0),
                      KeyType(-1, 0, 0)};
    }

    void TearDown() override
    {
        c_piVox = nullptr;
        c_pNode = nullptr;
    }
};

// 测试node
// TEST_F(ivoxTest, initGridParam)
// {
//     EXPECT_FLOAT_EQ(c_pNode->c_sInnerGrid.m_fInnerResolu, 0.1);
//     EXPECT_FLOAT_EQ(c_pNode->c_sInnerGrid.m_fOuterGridRel, 0.5);
//     EXPECT_EQ(c_pNode->c_sInnerGrid.m_iInnerGridEdge, 5);
//     EXPECT_EQ(c_pNode->c_sInnerGrid.m_iGridMaxIndex, 16);
//     EXPECT_EQ(c_pNode->c_sInnerGrid.m_iGridMaxSize, 125);
//     EXPECT_EQ(c_pNode->c_sInnerGrid.m_fInv_InnerResolu, 10);
//     EXPECT_EQ(c_pNode->c_sInnerGrid.m_eDivb_mul[0], 1);
//     EXPECT_EQ(c_pNode->c_sInnerGrid.m_eDivb_mul[1], 5);
//     EXPECT_EQ(c_pNode->c_sInnerGrid.m_eDivb_mul[2], 25);
// }

// TEST_F(ivoxTest, getGridEdge)
// {
//     float p = -5.7;
//     EXPECT_FLOAT_EQ(c_pNode->getGridEdge(p, c_stSysParam_->m_map.m_fOutGridResolu), -6.0);
//     float p1 = 5.7;
//     EXPECT_FLOAT_EQ(c_pNode->getGridEdge(p1, c_stSysParam_->m_map.m_fOutGridResolu), 5.5);
// }

// TEST_F(ivoxTest, calcuGridEdge)
// {
//     M pt;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = 1;
//     c_pNode->calcuGridEdge(pt, c_stSysParam_->m_map.m_fOutGridResolu);
//     EXPECT_FLOAT_EQ(c_pNode->c_min_[0], 12.0);
//     EXPECT_FLOAT_EQ(c_pNode->c_min_[1], 5.5);
//     EXPECT_FLOAT_EQ(c_pNode->c_min_[2], -3.5);
//     EXPECT_FLOAT_EQ(c_pNode->c_min_b_[0], 120.0);
//     EXPECT_FLOAT_EQ(c_pNode->c_min_b_[1], 55.0);
//     EXPECT_FLOAT_EQ(c_pNode->c_min_b_[2], -35.0);
// }

// TEST_F(ivoxTest, insertNewPoint)
// {
//     M pt;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = 1;
//     c_pNode->calcuGridEdge(pt, c_stSysParam_->m_map.m_fOutGridResolu);
//     EXPECT_EQ(c_pNode->insertNewPoint(pt), true);
//     EXPECT_EQ(c_pNode->c_vbackPts_.size(), 1);
//     EXPECT_EQ(c_pNode->c_vfrontPts_.size(), 0);
//     EXPECT_EQ(c_pNode->c_vhighPts_.size(), 0);
//     EXPECT_EQ(c_pNode->c_vmatchPts_.size(), 0);
//     EXPECT_EQ(c_pNode->c_vzeroPts_.size(), 1);

//     M pt1;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = -1;
//     EXPECT_EQ(c_pNode->insertNewPoint(pt), true);
//     EXPECT_EQ(c_pNode->c_vbackPts_.size(), 1);
//     EXPECT_EQ(c_pNode->c_vfrontPts_.size(), 1);
//     EXPECT_EQ(c_pNode->c_vhighPts_.size(), 0);
//     EXPECT_EQ(c_pNode->c_vmatchPts_.size(), 0);
//     EXPECT_EQ(c_pNode->c_vzeroPts_.size(), 1);
// }

// TEST_F(ivoxTest, clearIVoxNode)
// {
//     M pt;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = 1;
//     c_pNode->c_vbackPts_.emplace_back(pt);
//     EXPECT_EQ(c_pNode->c_vbackPts_.size(), 1);
//     c_pNode->clearIVoxNode();
//     EXPECT_EQ(c_pNode->c_vbackPts_.size(), 0);
// }

// TEST_F(ivoxTest, checkGrid)
// {
//     M pt;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = 1;
//     c_pNode->calcuGridEdge(pt, c_stSysParam_->m_map.m_fOutGridResolu);
//     M pt_1;
//     pt_1.x = 12.3;
//     pt_1.y = 5.8;
//     pt_1.z = -3.2;
//     pt_1.v = 1;
//     int num = -1;
//     EXPECT_EQ(c_pNode->checkGrid(pt_1, num), true);
// }

// TEST_F(ivoxTest, insertOldPoint)
// {
//     M pt;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = 1;
//     c_pNode->calcuGridEdge(pt, c_stSysParam_->m_map.m_fOutGridResolu);
//     EXPECT_EQ(c_pNode->insertNewPoint(pt), true);

//     M pt_1;
//     pt_1.x = 12.3;
//     pt_1.y = 5.8;
//     pt_1.z = -3.2;
//     pt_1.v = 1;
//     int num = -1;
//     EXPECT_EQ(c_pNode->checkGrid(pt_1, num), true);
//     EXPECT_EQ(c_pNode->insertOldPoint(pt_1, num), true);
//     EXPECT_EQ(c_pNode->c_vbackPts_.size(), 2);
// }

// TEST_F(ivoxTest, getGridPoint)
// {
//     M pt;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = 1;
//     c_pNode->calcuGridEdge(pt, c_iVoxOptions.m_fOuterResolu);
//     EXPECT_EQ(c_pNode->insertNewPoint(pt), true);

//     M pt1;
//     pt1.x = 12.1;
//     pt1.y = 5.7;
//     pt1.z = -3.4;
//     pt1.v = 0;
//     pcl::PointCloud<M>::Ptr search(new pcl::PointCloud<M>());
//     EXPECT_EQ(c_pNode->getGridPoint(pt1, search, true), 1);
//     EXPECT_EQ(c_pNode->getGridPoint(pt1, search, false), 2);

//     M pt2;
//     pt2.x = 12.1;
//     pt2.y = 5.7;
//     pt2.z = -3.4;
//     pt2.v = 1;
//     pcl::PointCloud<M>::Ptr lsearch(new pcl::PointCloud<M>());
//     EXPECT_EQ(c_pNode->getGridPoint(pt2, lsearch, true), 0);
// }

// TEST_F(ivoxTest, getGridAllPoint)
// {
//     M pt;
//     pt.x = 12.1;
//     pt.y = 5.7;
//     pt.z = -3.4;
//     pt.v = 1;
//     c_pNode->calcuGridEdge(pt, c_iVoxOptions.m_fOuterResolu);
//     EXPECT_EQ(c_pNode->insertNewPoint(pt), true);

//     M pt1;
//     pt1.x = 12.1;
//     pt1.y = 5.7;
//     pt1.z = -3.4;
//     pt1.v = 0;
//     pcl::PointCloud<M>::Ptr search(new pcl::PointCloud<M>());
//     c_pNode->getGridAllPoint(pt1, search);
//     EXPECT_EQ(search->size(), 1);
// }

// 测试iVox
// TEST_F(ivoxTest, addGridMap)
// {
//     pcl::PointCloud<M> p_ptMap1;
//     p_ptMap1.resize(2);

//     p_ptMap1.points[0].x = 12.1;
//     p_ptMap1.points[0].y = 5.7;
//     p_ptMap1.points[0].z = -3.4;
//     p_ptMap1.points[0].v = 1;

//     p_ptMap1.points[1].x = 12.1;
//     p_ptMap1.points[1].y = 5.7;
//     p_ptMap1.points[1].z = -3.4;
//     p_ptMap1.points[1].v = 1;

//     c_piVox->addGridMap(p_ptMap1.points, 0);

//     c_piVox->addGridMap(p_ptMap1.points, 1);
//     EXPECT_EQ(c_piVox->c_iNum, 1);
//     KeyType key = c_piVox->Pos2Grid_(ToEigen<float, 3>(p_ptMap1.points[0]));
//     EXPECT_EQ(c_piVox->c_lGridcache_.front().first, key);
//     EXPECT_FLOAT_EQ(c_piVox->c_lGridcache_.front().second->c_min_[0], 12.0);
//     EXPECT_FLOAT_EQ(c_piVox->c_lGridcache_.front().second->c_min_[1], 5.5);
//     EXPECT_FLOAT_EQ(c_piVox->c_lGridcache_.front().second->c_min_[2], -3.5);
//     EXPECT_EQ(c_piVox->c_lGridcache_.front().second->c_vbackPts_.size(), 1);
//     EXPECT_EQ(c_piVox->c_lGridcache_.front().second->c_vzeroPts_.size(), 1);
//     EXPECT_EQ(c_piVox->c_lAddZero.size(), 1);
//     EXPECT_EQ(c_piVox->c_vaddPoint.size(), 1);
//     EXPECT_EQ(c_piVox->c_lNoLabel.size(), 0);
//     EXPECT_EQ(c_piVox->c_vNoSign.size(), 0);
// }

// TEST_F(ivoxTest, getClosestGridPoint)
// {
//     pcl::PointCloud<M> p_ptMap1;
//     p_ptMap1.resize(2);

//     p_ptMap1.points[0].x = 12.1;
//     p_ptMap1.points[0].y = 5.7;
//     p_ptMap1.points[0].z = -3.4;
//     p_ptMap1.points[0].v = 1;

//     p_ptMap1.points[1].x = 12.4;
//     p_ptMap1.points[1].y = 5.7;
//     p_ptMap1.points[1].z = -3.4;
//     p_ptMap1.points[1].v = 0;

//     c_piVox->addGridMap(p_ptMap1.points, 1);
//     M tgt;
//     tgt.x = 12.2;
//     tgt.y = 5.7;
//     tgt.z = -3.4;
//     tgt.v = 0;
//     pcl::PointCloud<M>::Ptr search(new pcl::PointCloud<M>());
//     EXPECT_EQ(c_piVox->getClosestGridPoint(tgt, search, 10, false), true);
//     EXPECT_EQ(search->size(), 2);
// }

}  // namespace wj_slam