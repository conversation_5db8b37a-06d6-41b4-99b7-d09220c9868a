// local
#include "../gtest_test.h"
#include "../../include/algorithm/map/sub_map/impl/op_vector.hpp"
#include "../../include/algorithm/map/sub_map/impl/FilterFactory.hpp"
#include "../../include/algorithm/map/sub_map/KeyFrameMap.h"
#include "../../include/algorithm/map/sub_map/SubmapBox.h"
#include "../../include/algorithm/map/sub_map/Submap.h"
#include "../../include/tic_toc.h"
#include "../../include/common/common_ex.h"
#include "../../include/common/common_master.h"
#include "../../test/param.hpp"
// gtest
#include <gtest/gtest.h>
// pcl
#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>
#include <pcl/common/common.h>

// sys
#include <random>

// ros
#include <ros/package.h>

generalLog* glog = nullptr;

// 通用日志
WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;


using namespace testing;

namespace wj_slam
{

void initLog()
{
    wj_slam::s_masterCfg* g_masterParamPtr;
    g_masterParamPtr = wj_slam::s_masterCfg::getIn();

    // 读取参数 静态指针无须传参
    wj_slam::Param l_param(false);
    std::string path = "/home/<USER>/slam_xg/src/wanji_slam";
    l_param.loadSysParam(path);
    printf("pgk Path: %s\n", g_masterParamPtr->m_slam->m_sPkgPath.c_str());

    // 挂载日志模块
    glog = new generalLog(g_masterParamPtr->m_slam->m_fae.m_sLogPath,
                            "wj",
                            FileMode::DAILY_ROTATE,
                            LogMode::TRIAL_MODE,
                            g_masterParamPtr->m_slam->m_fae.getLogFileSizeByte());
    // 重设日志等级
    glog->changeMode(g_masterParamPtr->m_slam->m_fae.m_iLogLevel);
}

void deleteLog()
{
    if (glog)
    {
        delete glog;
        glog = nullptr;
    }
}

class SubmapTest : public testing::Test
{
protected:

    void SetUp() override
    {
        initLog();
    }

    void TearDown() override
    {
        deleteLog();
    }
};

using PointT = pcl::PointXYZHSV;
using PointCloud = pcl::PointCloud<PointT>;
using PointCloudPtr = boost::shared_ptr<PointCloud>;
// 地图
using FeaturePcPtr = boost::shared_ptr<PointCloud>;
using KeyFrame = wj_slam::KEYFRAME<PointT>;
using KeyFramePtr = boost::shared_ptr<KeyFrame>;
using ConstKeyFramePtr = boost::shared_ptr<const wj_slam::KEYFRAME<PointT>>;
// 位姿
using PoseT = pcl::PointXYZI;
using PcPose = pcl::PointCloud<PoseT>;
using PcPosePtr = boost::shared_ptr<PcPose>;
using ConstPcPosePtr = boost::shared_ptr<const PcPose>;

using KeyFrameMapPtr = boost::shared_ptr<KeyFrameMap>;

void log(std::string message)
{
    printf("%s\n", message.c_str());
}

template <class T>
AssertionResult isVectorEqual(const std::vector<T>& v1, const std::vector<T>& v2)
{
    return (v1 == v2) ? AssertionSuccess() : (AssertionFailure() << "Vector No Equal");
}

// 读取PCD
template <class T>
boost::shared_ptr<pcl::PointCloud<T>> readPcd(std::string path)
{
    boost::shared_ptr<pcl::PointCloud<T>> pc = boost::make_shared<pcl::PointCloud<T>>();
    log(path.c_str());
    pcl::io::loadPCDFile<T>(path, *pc);
    log(std::string("size : ").append(std::to_string(pc->size())));
    return pc;
}

// 读取KF，模拟建图关键帧数据
void readKf(std::vector<KeyFramePtr>& p_kfMaps)
{
    static std::string l_sPathHeader = "/home/<USER>/slam_v20/gtest/map/DIKU/KF/";
    for (int i = 0; i <= 305; i++)
    {
        std::string l_sPath = l_sPathHeader + std::to_string(i);
        KeyFramePtr l_kf = boost::make_shared<KeyFrame>();
        std::string cornerName = l_sPath + "/fi.pcd";
        std::string surfName = l_sPath + "/se.pcd";

        FeaturePcPtr l_corn = readPcd<PointT>(cornerName);
        FeaturePcPtr l_surf = readPcd<PointT>(surfName);

        l_kf->m_pFeature->first = l_corn;
        l_kf->m_pFeature->second = l_surf;
        l_kf->m_pFeature->third = l_corn;
        l_kf->m_pFeature->fourth = l_corn;

        p_kfMaps.push_back(l_kf);
    }
}

// 写PCD
template <class T>
void writerPcd(boost::shared_ptr<pcl::PointCloud<T>> pc, std::string path)
{
    pcl::io::savePCDFile<T>(path, *pc);
}

// 读取地图
void readMap(KeyFramePtr& p_kfMap, PcPosePtr& l_pose, std::string path)
{
    std::string cornName = path + "fi.pcd";
    std::string surfName = path + "se.pcd";
    std::string markName = path + "mark.pcd";
    std::string curbName = path + "curb.pcd";
    std::string allName = path + "3D.pcd";
    std::string poseName = path + "p.pcd";

    PointCloudPtr cornPc = readPcd<PointT>(cornName);
    PointCloudPtr surfPc = readPcd<PointT>(surfName);
    PointCloudPtr markPc = readPcd<PointT>(markName);
    PointCloudPtr curbPc = readPcd<PointT>(curbName);
    PointCloudPtr allPc = readPcd<PointT>(allName);
    PcPosePtr posePc = readPcd<PoseT>(poseName);

    p_kfMap.reset(new KeyFrame());
    l_pose.reset(new PcPose());
    pcl::copyPointCloud(*cornPc, *(p_kfMap->m_pFeature->first));
    pcl::copyPointCloud(*surfPc, *(p_kfMap->m_pFeature->second));
    pcl::copyPointCloud(*markPc, *(p_kfMap->m_pFeature->third));
    pcl::copyPointCloud(*curbPc, *(p_kfMap->m_pFeature->fourth));
    pcl::copyPointCloud(*allPc, *(p_kfMap->m_pFeature->allPC));
    pcl::copyPointCloud(*posePc, *l_pose);

    // log(std::string("corn size: ").append(std::to_string(cornPc->size())));
    // log(std::string("surf size: ").append(std::to_string(surfPc->size())));
    // log(std::string("mark size: ").append(std::to_string(markPc->size())));
    // log(std::string("corb size: ").append(std::to_string(curbPc->size())));
    // log(std::string("allPC size: ").append(std::to_string(allPc->size())));
}

// op_vector
TEST_F(SubmapTest, vectorTest)
{
    // 测试用例1: 其中一vector为空
    std::vector<int> v11 = {};
    std::vector<int> v12 = {1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16};
    std::vector<int> v13 = {};

    // 测试用例2: vector都不为空且不相等(有序/无序)
    std::vector<int> v21 = {3,4,6,78,98,2,62};
    std::vector<int> v22 = {1,2,3,4,5,6,7,8};
    std::vector<int> v23 = {2,3,4,6};
    std::vector<int> v24 = {3,4,6,2};
    std::vector<int> v25 = {2,3,4,6,62,78,98};
    std::vector<int> v26 = {1,2,3,4,5,6,7,8,62,78,98};
    std::vector<int> v27 = {62,78,98};
    std::vector<int> v28 = {1,5,7,8};

    // 测试用例3: vector相等
    std::vector<int> v31 = {2,3,4,6,62,78,98};
    std::vector<int> v32 = {};

    // 测试用例4: vector中存在重复元素
    std::vector<int> v41 = {2,2,4,6,6,62,78,98};
    std::vector<int> v42 = {2,6,6,7,9,98};
    std::vector<int> v43 = {2,6,6,98};
    std::vector<int> v44 = {2,2,4,6,6,7,9,62,78,98};
    std::vector<int> v45 = {2,4,6,7,9,62,78,98};
    std::vector<int> v46 = {2,4,6,62,78,98};
    std::vector<int> v47 = {2,4,62,78};

    // 求交集
    EXPECT_TRUE(isVectorEqual(vectorIntersection<int>(v11, v12), v13));
    EXPECT_TRUE(isVectorEqual(vectorIntersection<int>(v12, v11), v13));
    EXPECT_TRUE(!isVectorEqual(vectorIntersection<int>(v21, v22), v23));             // false
    EXPECT_TRUE(isVectorEqual(vectorIntersection<int>(v21, v22, false), v23));       
    EXPECT_TRUE(!isVectorEqual(vectorIntersection<int>(v21, v22), v24));             // false
    EXPECT_TRUE(isVectorEqual(vectorIntersection<int>(v25, v22), v23));
    EXPECT_TRUE(isVectorEqual(vectorIntersection<int>(v31, v31), v31));
    EXPECT_TRUE(isVectorEqual(vectorIntersection<int>(v41, v42), v43));

    // 求并集
    EXPECT_TRUE(isVectorEqual(vectorUnion<int>(v11, v12), v12));
    EXPECT_TRUE(!isVectorEqual(vectorUnion<int>(v21, v22), v26));            // false
    EXPECT_TRUE(isVectorEqual(vectorUnion<int>(v21, v22, false), v26));
    EXPECT_TRUE(isVectorEqual(vectorUnion<int>(v25, v22), v26));
    EXPECT_TRUE(isVectorEqual(vectorUnion<int>(v31, v31), v31));
    EXPECT_TRUE(!isVectorEqual(vectorUnion<int>(v41, v42), v44));            // false
    EXPECT_TRUE(isVectorEqual(vectorUnion<int>(v41, v42), v45));

    // 过滤重复元素
    EXPECT_TRUE(isVectorEqual(vectoFilterRepeat<int>(v12), v12));
    EXPECT_TRUE(isVectorEqual(vectoFilterRepeat<int>(v11), v11));
    EXPECT_TRUE(!isVectorEqual(vectoFilterRepeat<int>(v21), v21));           // false
    EXPECT_TRUE(isVectorEqual(vectoFilterRepeat<int>(v41), v46));
    EXPECT_TRUE(isVectorEqual(vectoFilterRepeat<int>(v44), v45));

    // 过滤前者中存在于后者中的元素
    EXPECT_TRUE(isVectorEqual(vectorDifference<int>(v11, v12), v11));
    EXPECT_TRUE(isVectorEqual(vectorDifference<int>(v12, v11), v12));
    EXPECT_TRUE(!isVectorEqual(vectorDifference<int>(v21, v22), v27));           // false
    EXPECT_TRUE(isVectorEqual(vectorDifference<int>(v21, v22, false), v27));
    EXPECT_TRUE(isVectorEqual(vectorDifference<int>(v22, v25), v28));
    EXPECT_TRUE(isVectorEqual(vectorDifference<int>(v31, v31), v32));
    EXPECT_TRUE(isVectorEqual(vectorDifference<int>(v41, v42), v47));
}

// FilterFactory
TEST_F(SubmapTest, filterTest)
{
    FilterFactory* filter = FilterFactory::getInstance();
    TicToc tic;

    // ly_map
    std::string path = "/home/<USER>/slam_v20/gtest/map/DIKU/";
    std::string cornName = path + "fi.pcd";
    std::string surfName = path + "se.pcd";
    std::string markName = path + "mark.pcd";
    std::string curbName = path + "curb.pcd";
    std::string pathName = path + "p.pcd";
    // std::string cornName = path + "fi_seg.pcd";
    // std::string surfName = path + "se_seg.pcd";
    // std::string markName = path + "mark_seg.pcd";
    // std::string curbName = path + "curb_seg.pcd";

    PointCloudPtr cornPc = readPcd<PointT>(cornName);
    PointCloudPtr surfPc = readPcd<PointT>(surfName);
    PointCloudPtr markPc = readPcd<PointT>(markName);
    PointCloudPtr curbPc = readPcd<PointT>(curbName);
    PcPosePtr pathPc = readPcd<PoseT>(pathName);

    double time = 0;
    if (!cornPc)
    {
        log("cornPc is nullptr");
    }

    filter->setFilterType(wj_slam::DownModel::VoxelGridDown);
    tic.tic();
    filter->filterCorner(cornPc, cornPc);
    time = tic.toc();
    log(std::string("VoxelGrid filter corner time: ").append(std::to_string(time)));
    writerPcd(cornPc, path + "voxelGrid_Corn.pcd");
    filter->setFilterType(wj_slam::DownModel::RandomDown);
    tic.tic();
    filter->filterCorner(cornPc, cornPc);
    time = tic.toc();
    log(std::string("Random filter corner time: ").append(std::to_string(time)));
    writerPcd(cornPc, path + "random_Corn.pcd");

    if (!surfPc)
    {
        log("surfPc is nullptr");
    }

    filter->setFilterType(wj_slam::DownModel::VoxelGridDown);
    tic.tic();
    filter->filterSurf(surfPc, surfPc);
    time = tic.toc();
    log(std::string("VoxelGrid filter surf time: ").append(std::to_string(time)));
    writerPcd(surfPc, path + "voxelGrid_Surf.pcd");
    filter->setFilterType(wj_slam::DownModel::RandomDown);
    tic.tic();
    filter->filterSurf(surfPc, surfPc);
    time = tic.toc();
    log(std::string("Random filter surf time: ").append(std::to_string(time)));
    writerPcd(surfPc, path + "random_Surf.pcd");

    if (!markPc)
    {
        log("markPc is nullptr");
    }

    filter->setFilterType(wj_slam::DownModel::VoxelGridDown);
    tic.tic();
    filter->filterMark(markPc, markPc);
    time = tic.toc();
    log(std::string("VoxelGrid filter mark time: ").append(std::to_string(time)));
    writerPcd(markPc, path + "voxelGrid_Mark.pcd");
    filter->setFilterType(wj_slam::DownModel::RandomDown);
    tic.tic();
    filter->filterMark(markPc, markPc);
    time = tic.toc();
    log(std::string("Random filter mark time: ").append(std::to_string(time)));
    writerPcd(markPc, path + "random_Mark.pcd");

    if (!curbPc)
    {
        log("curbPc is nullptr");
    }

    filter->setFilterType(wj_slam::DownModel::VoxelGridDown);
    tic.tic();
    filter->filterCurb(curbPc, curbPc);
    time = tic.toc();
    log(std::string("VoxelGrid filter curb time: ").append(std::to_string(time)));
    writerPcd(curbPc, path + "voxelGrid_Curb.pcd");
    filter->setFilterType(wj_slam::DownModel::RandomDown);
    tic.tic();
    filter->filterCurb(curbPc, curbPc);
    time = tic.toc();
    log(std::string("Random filter curb time: ").append(std::to_string(time)));
    writerPcd(curbPc, path + "random_Curb.pcd");

    filter->setFilterType(wj_slam::DownModel::VoxelGridDown);
    PointCloudPtr nullPc = boost::make_shared<PointCloud>();
    tic.tic();
    filter->filterCurb(nullPc, nullPc);
    time = tic.toc();
    log(std::string("VoxelGrid filter nullpc time: ").append(std::to_string(time)));
    filter->setFilterType(wj_slam::DownModel::RandomDown);
    nullPc = boost::make_shared<PointCloud>();
    tic.tic();
    filter->filterCurb(nullPc, nullPc);
    time = tic.toc();
    log(std::string("Random filter nullpc time: ").append(std::to_string(time)));

    filter->setFilterType(wj_slam::DownModel::VoxelGridDown);
    tic.tic();
    PcPosePtr pose1Pc = boost::make_shared<PcPose>();
    filter->filterKfPose(pathPc, pose1Pc);
    time = tic.toc();
    log(std::string("VoxelGrid filter pose time: ").append(std::to_string(time)));
    writerPcd(pose1Pc, path + "voxelGrid_Pose1.pcd");
    filter->setFilterType(wj_slam::DownModel::RandomDown);
    tic.tic();
    pose1Pc = boost::make_shared<PcPose>();
    filter->filterKfPose(pathPc, pose1Pc);
    time = tic.toc();
    log(std::string("Random filter pose time: ").append(std::to_string(time)));
    writerPcd(pose1Pc, path + "random_Pose1.pcd");

    filter->setFilterType(wj_slam::DownModel::VoxelGridDown);
    std::vector<int> inds = filter->filterKfPoseToNearestInds<PoseT>(pathPc, pathPc);
    EXPECT_TRUE(inds.size());
    PcPosePtr pose2Pc = boost::make_shared<PcPose>();
    pcl::copyPointCloud(*cornPc, inds, *pose2Pc);
    writerPcd(pose2Pc, path + "voxelGrid_Pose2.pcd");
    filter->setFilterType(wj_slam::DownModel::RandomDown);
    inds = filter->filterKfPoseToNearestInds<PoseT>(pathPc, pathPc);
    EXPECT_TRUE(inds.size());
    pose2Pc = boost::make_shared<PcPose>();
    pcl::copyPointCloud(*cornPc, inds, *pose2Pc);
    writerPcd(pose2Pc, path + "random_Pose2.pcd");
}

// 产生随机定位位姿索引
std::vector<int> generateLocalPoseInd(PcPosePtr p_pose, int cnt)
{
    std::random_device rd;
    std::mt19937 r_eng(rd());

    int size = p_pose->size();
    int startInd = r_eng() % size;
    int endInd = r_eng() % size;

    if (startInd > endInd)
    {
        int temp = startInd;
        startInd = endInd;
        endInd = temp;
    }

    printf("local pose start ind: %d\n", startInd);
    printf("local pose end ind: %d\n", endInd);

    std::vector<int> res;
    for (int i = startInd; i <= endInd; i++)
    {
        res.push_back(i);
        p_pose->at(i).intensity = i + 1;

    }
    return res;
}

// 产生随机定位位姿索引
std::vector<int> generateLocalPoseInd2(PcPosePtr p_pose, int cnt)
{
    std::random_device rd;
    std::mt19937 r_eng(rd());

    int size = p_pose->size();
    int startInd = r_eng() % size;
    int endInd = r_eng() % size;

    if (startInd > endInd)
    {
        int temp = startInd;
        startInd = endInd;
        endInd = temp;
    }

    printf("local pose start ind: %d\n", startInd);
    printf("local pose end ind: %d\n", endInd);

    std::vector<int> res;
    for (int i = 0; i <= startInd; i++)
    {
        res.push_back(i);
        p_pose->at(i).intensity = i + 1;
    }

    for (int i = endInd; i < size; i++)
    {
        res.push_back(i);
        p_pose->at(i).intensity = i + 1;
    }
    return res;
}

// 保存局部地图
void saveLocalMap(KeyFramePtr p_localMap, PcPosePtr p_pose, std::string path, std::string name)
{
    log(std::string("corn size: ").append(std::to_string(p_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(p_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(p_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(p_localMap->m_pFeature->curbSize())));
    log(std::string("allPC size: ").append(std::to_string(p_localMap->m_pFeature->pcSize())));
    
    std::string kfName = path + "testmap/";
    std::string cornpcdName = kfName + "corn.pcd";
    std::string surfpcdName = kfName + "surf.pcd";
    std::string markpcdName = kfName + "mark.pcd";
    std::string curbpcdName = kfName + "curb.pcd";
    std::string posepcdName = kfName + "pose.pcd";

    writerPcd(p_pose, posepcdName);
    writerPcd(p_localMap->m_pFeature->first, cornpcdName);
    writerPcd(p_localMap->m_pFeature->second, surfpcdName);
    writerPcd(p_localMap->m_pFeature->third, markpcdName);
    writerPcd(p_localMap->m_pFeature->fourth, curbpcdName);
}

// KeyFrameMap类测试
TEST_F(SubmapTest, KeyFrameMapTest)
{
    KeyFramePtr l_kfmap;
    PcPosePtr l_pose;
    std::vector<int> l_localPoseInd;
    KeyFramePtr l_localMapDs;
    KeyFramePtr l_localMap;

    // ly_map
    std::string path = "/home/<USER>/slam_v20/gtest/map/DIKU/";
    readMap(l_kfmap, l_pose, path);

    FilterFactory::getInstance()->setFilterType(wj_slam::OptimizeMapType::IVOX_TYPE);
    log(std::string("FilterType:").append(std::to_string(FilterFactory::getInstance()->getFilterType())));
    KeyFrameMapPtr l_keyFrameMap = boost::make_shared<KeyFrameMap>();
    log(std::string("l_pose size : ").append(std::to_string(l_pose->size())));


    log("########################### IDMode true ###########################");
    l_keyFrameMap->setIdModel(true);
    EXPECT_TRUE(l_keyFrameMap->getIdModel());

    l_keyFrameMap->generateKfMap(l_pose, l_kfmap, 30);
    log(std::string("after l_pose size : ").append(std::to_string(l_keyFrameMap->getKfPose()->size())));
    EXPECT_TRUE(l_keyFrameMap->getKfSize() == 0);

    l_localMapDs.reset(new KeyFrame());
    l_localMap.reset(new KeyFrame());
    for (int i = 10; i < std::min<int>(l_keyFrameMap->getKfPose()->size(), 30); i++)
    {
        l_localPoseInd.push_back(i);
    }

    log("****************** FeatureDs: 0x0F ******************");
    l_keyFrameMap->setFeatureDsEnabled(true, true, true, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x0F);
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMapDs);
    log(std::string("corner size: ").append(std::to_string(l_localMapDs->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMapDs->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMapDs->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMapDs->m_pFeature->curbSize())));
    // saveLocalMap(l_localMapDs, l_pose, path, "ds");

    log("****************** FeatureDs: 0x0E ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, true, true, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x0E);
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor");
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    log("****************** FeatureDs: 0x0C ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, false, true, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x0C);
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor2");
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    log("****************** FeatureDs: 0x08 ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, false, false, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x08);
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor3");
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    log("****************** FeatureDs: 0x00 ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, false, false, false);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x00);
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor4");
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    log("###################################################################");
    log("########################### IDMode false ##########################");
    l_keyFrameMap.reset(new KeyFrameMap());
    l_keyFrameMap->setIdModel(false);
    EXPECT_TRUE(!l_keyFrameMap->getIdModel());

    // std::vector<KeyFramePtr> l_tempKfMaps;
    // readKf(l_tempKfMaps);
    // for (int i = 0; i < l_tempKfMaps.size(); i++)
    // {
    //     l_keyFrameMap->push(l_tempKfMaps[i], l_pose->at(i));
    // }
    l_keyFrameMap->generateKfMap(l_pose, l_kfmap, 30);
    log(std::string("after l_pose size : ").append(std::to_string(l_pose->size())));
    EXPECT_TRUE(l_keyFrameMap->getKfSize());

    l_localMapDs.reset(new KeyFrame());
    l_localMap.reset(new KeyFrame());
    // l_localPoseInd = generateLocalPoseInd(l_pose, 30);
    for (int i = 10; i < std::min<int>(l_keyFrameMap->getKfPose()->size(), 30); i++)
    {
        l_localPoseInd.push_back(i);
    }
    log("****************** FeatureDs: 0x0F ******************");
    l_keyFrameMap->setFeatureDsEnabled(true, true, true, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x0F);
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMapDs);
    log(std::string("corner size: ").append(std::to_string(l_localMapDs->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMapDs->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMapDs->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMapDs->m_pFeature->curbSize())));
    // saveLocalMap(l_localMapDs, l_pose, path, "ds");
    l_localMap = l_keyFrameMap->getKfMapByInds(l_localPoseInd);
    l_keyFrameMap->filterKfMap(l_localMap, l_localMap);
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() > l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() > l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() > l_localMap->m_pFeature->curbSize());
    
    log("****************** FeatureDs: 0x0E ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, true, true, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x0E);
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor");

    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    l_localMap = l_keyFrameMap->getKfMapByInds(l_localPoseInd);
    l_keyFrameMap->filterKfMap(l_localMap, l_localMap);
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() > l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() > l_localMap->m_pFeature->curbSize());

    log("****************** FeatureDs: 0x0C ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, false, true, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x0C);
    l_localMap.reset(new KeyFrame());
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor2");
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    l_localMap = l_keyFrameMap->getKfMapByInds(l_localPoseInd);
    l_keyFrameMap->filterKfMap(l_localMap, l_localMap);
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() > l_localMap->m_pFeature->curbSize());

    log("****************** FeatureDs: 0x08 ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, false, false, true);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x08);
    l_localMap.reset(new KeyFrame());
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor3");
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    l_localMap = l_keyFrameMap->getKfMapByInds(l_localPoseInd);
    l_keyFrameMap->filterKfMap(l_localMap, l_localMap);
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() > l_localMap->m_pFeature->curbSize());

    log("****************** FeatureDs: 0x00 ******************");
    l_keyFrameMap->setFeatureDsEnabled(false, false, false, false);
    EXPECT_TRUE(l_keyFrameMap->getFeatureDsEnabled() == 0x00);
    l_localMap.reset(new KeyFrame());
    l_keyFrameMap->getKfMaps(l_localPoseInd, l_localMap);
    // saveLocalMap(l_localMap, l_pose, path, "nor4");
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    l_localMap = l_keyFrameMap->getKfMapByInds(l_localPoseInd);
    l_keyFrameMap->filterKfMap(l_localMap, l_localMap);
    log(std::string("corner size: ").append(std::to_string(l_localMap->m_pFeature->cornerSize())));
    log(std::string("surf size: ").append(std::to_string(l_localMap->m_pFeature->surfSize())));
    log(std::string("mark size: ").append(std::to_string(l_localMap->m_pFeature->markSize())));
    log(std::string("corb size: ").append(std::to_string(l_localMap->m_pFeature->curbSize())));
    EXPECT_TRUE(l_localMapDs->m_pFeature->cornerSize() == l_localMap->m_pFeature->cornerSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->surfSize() == l_localMap->m_pFeature->surfSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->markSize() == l_localMap->m_pFeature->markSize());
    EXPECT_TRUE(l_localMapDs->m_pFeature->curbSize() == l_localMap->m_pFeature->curbSize());

    log("###################################################################");
}

// SubmapBox类测试
TEST_F(SubmapTest, SubmapBoxTest)
{
    KeyFramePtr l_map;
    PcPosePtr l_poseLoc = boost::make_shared<PcPose>();
    std::string path = "/home/<USER>/slam_v20/gtest/map/DIKU/";
    readMap(l_map, l_poseLoc, path);
    // pcl::copyPointCloud(*l_pose, *l_poseLoc);

    SubmapBox l_submapBox;
    KeyFrameMapPtr l_keyFrameMap = boost::make_shared<KeyFrameMap>();
    Submap l_submap();
    l_keyFrameMap->setIdModel(false);
    // l_keyFrameMap->generateKfMap(l_map, 30);
    l_keyFrameMap->setFeatureDsEnabled(false, true, true, true);

    l_submapBox.setEnableLoop(true);
    for (std::size_t i = 0; i < l_poseLoc->size(); i++)
    {
        KeyFramePtr l_kf = boost::make_shared<KeyFrame>();
        l_keyFrameMap->push(l_kf, l_poseLoc->at(i));

        PointT l_pcscanRangeMin, l_pcscanRangeMax;
        // KeyFramePtr l_kfMap = l_kfMaps[i];
        // pcl::getMinMax3D(*(l_kfMap->m_pFeature->second), l_pcscanRangeMin, l_pcscanRangeMax);
        l_pcscanRangeMin.x = l_poseLoc->at(i).x - 50;
        l_pcscanRangeMin.y = l_poseLoc->at(i).y - 50;
        l_pcscanRangeMax.x = l_poseLoc->at(i).x + 50;
        l_pcscanRangeMax.y = l_poseLoc->at(i).y + 50;       
        l_submapBox.setScanRange(l_pcscanRangeMin.x, l_pcscanRangeMin.y, l_pcscanRangeMax.x, l_pcscanRangeMax.y);
        SubmapBox::Pose l_currPose(l_poseLoc->at(i).x, l_poseLoc->at(i).y, l_poseLoc->at(i).z);
        bool res = l_submapBox.update(l_keyFrameMap->getKfPose(), l_currPose);
        std::vector<int> l_vSubmapKfInds = l_submapBox.getSubmapKfInds();
        std::vector<int> l_vMotionKfInds = l_submapBox.getLoopKfInds();
        printf("################################ Pose: %ld #####################################\n", i);
        printf("update : %s\n", res ? "true" : "false");
        printf("Pose: (%lf, %lf, %lf)\n", l_currPose.x(), l_currPose.y(), l_currPose.z());
        printf("Submap Inds: ");
        for (std::size_t i = 0; i < l_vSubmapKfInds.size(); i++)
        {
            printf("%d, " , l_vSubmapKfInds[i]);
        }
        printf("\n");
        printf("Motion Inds: ");
        for (std::size_t i = 0; i < l_vMotionKfInds.size(); i++)
        {
            printf("%d, " , l_vMotionKfInds[i]);
        }
        printf("\n");
        printf("#############################################################################\n");
    }
}

// Submap类测试 建图模式
TEST_F(SubmapTest, SubmapTest_Mapping)
{
    TicToc l_tt;
    KeyFramePtr l_map;
    PcPosePtr l_poseLoc = boost::make_shared<PcPose>();
    std::string path = "/home/<USER>/slam_v20/gtest/map/DIKU/";
    readMap(l_map, l_poseLoc, path);

    KeyFrameMapPtr c_keyFrameMap_ = boost::make_shared<KeyFrameMap>();
    Submap l_submap(c_keyFrameMap_);

    std::vector<KeyFramePtr> l_tempKfMaps;
    PcPosePtr l_tempPose = boost::make_shared<PcPose>();
    pcl::copyPointCloud(*l_poseLoc, *l_tempPose);

    readKf(l_tempKfMaps);
    l_submap.setEnableLoop(true);
    l_submap.setEnable2d(false);
    FilterFactory::getInstance()->setFilterType(wj_slam::OptimizeMapType::IVOX_TYPE);
    c_keyFrameMap_->setFeatureDsEnabled(true, true, false, false);
    c_keyFrameMap_->setIdModel(false);

    printf("init time: %lf\n", l_tt.toc());

    for (std::size_t i = 0; i < l_poseLoc->size(); i++)
    {
        KeyFramePtr l_kf = boost::make_shared<KeyFrame>();
        c_keyFrameMap_->push(l_tempKfMaps[i], l_poseLoc->at(i));

        PointT l_pcscanRangeMin, l_pcscanRangeMax;
        KeyFramePtr l_kfMap = l_tempKfMaps[i];
        pcl::getMinMax3D(*(l_kfMap->m_pFeature->second), l_pcscanRangeMin, l_pcscanRangeMax);
        
        SubmapBox::Pose l_currPose(l_poseLoc->at(i).x, l_poseLoc->at(i).y, l_poseLoc->at(i).z);
        l_submap.setScanRange(l_pcscanRangeMin.x, l_pcscanRangeMin.y, l_pcscanRangeMax.x, l_pcscanRangeMax.y);
        l_submap.setPose(l_currPose, Eigen::Quaterniond::Identity());
        
        l_tt.tic();
        bool res = l_submap.getSubmap_();
        printf("################################ Pose: %ld #####################################\n", i);
        printf("getSubmap_ time: %lf\n", l_tt.toc());
        printf("getSubmap_ return: %s\n", res ? "true" : "false");
        printf("Pose: (%lf, %lf, %lf)\n", l_currPose.x(), l_currPose.y(), l_currPose.z());
        printf("Submap Inds: ");
        for (std::size_t i = 0; i < l_submap.c_vSubmapKfInds_.size(); i++)
        {
            printf("%d, " , l_submap.c_vSubmapKfInds_[i]);
        }
        printf("\n");
        printf("Motion Inds: ");
        std::vector<int> l_motionInds = l_submap.c_submapBox_.getLoopKfInds();
        for (std::size_t i = 0; i < l_motionInds.size(); i++)
        {
            printf("%d, " , l_motionInds[i]);
        }
        printf("\n");

        if (res)
        {
            KeyFramePtr l_wholeMapPtr = boost::make_shared<KeyFrame>();
            KeyFramePtr l_addMapPtr = boost::make_shared<KeyFrame>();

            res = l_submap.transformAddPointer(l_wholeMapPtr, l_addMapPtr, l_motionInds);

            printf("WholeMap size: [%d, %d, %d, %d]\n", l_wholeMapPtr->m_pFeature->cornerSize(), l_wholeMapPtr->m_pFeature->surfSize(), 
                l_wholeMapPtr->m_pFeature->markSize(), l_wholeMapPtr->m_pFeature->curbSize());
            printf("AddMap size: [%d, %d, %d, %d]\n", l_wholeMapPtr->m_pFeature->cornerSize(), l_wholeMapPtr->m_pFeature->surfSize(), 
                l_wholeMapPtr->m_pFeature->markSize(), l_wholeMapPtr->m_pFeature->curbSize());

            printf("Motion Inds: ");
            std::vector<int> l_motionInds = l_submap.c_submapBox_.getLoopKfInds();
            for (std::size_t i = 0; i < l_motionInds.size(); i++)
            {
                printf("%d, " , l_motionInds[i]);
            }
            printf("\n");
        }
        
        printf("#############################################################################\n");
    }
}

// Submap类测试 定位模式
TEST_F(SubmapTest, SubmapTest_Loc)
{
    TicToc l_tt;
    KeyFramePtr l_map;
    PcPosePtr l_pose = boost::make_shared<PcPose>();
    std::string path = "/home/<USER>/slam_v20/gtest/map/DIKU/";
    readMap(l_map, l_pose, path);

    // 角面点采样
    FilterFactory::randomFilter<PointT>(l_map->m_pFeature->first, l_map->m_pFeature->first, 0.8);
    FilterFactory::randomFilter<PointT>(l_map->m_pFeature->second, l_map->m_pFeature->second, 0.2);

    printf("surf after ds size : %d\n", l_map->m_pFeature->surfSize());
    printf("corn after ds size : %d\n", l_map->m_pFeature->cornerSize());
    KeyFrameMapPtr c_keyFrameMap_ = boost::make_shared<KeyFrameMap>();
    Submap l_submap(c_keyFrameMap_);
    l_submap.setEnableLoop(false);
    l_submap.setEnable2d(false);
    FilterFactory::getInstance()->setFilterType(wj_slam::OptimizeMapType::IVOX_TYPE);
    c_keyFrameMap_->setFeatureDsEnabled(true, true, false, false);
    c_keyFrameMap_->setIdModel(true);
    c_keyFrameMap_->generateKfMap(l_pose, l_map, 30);

    printf("l_pose after ds size : %d\n", l_pose->size());
    writerPcd(l_pose, "/home/<USER>/slam_v20/gtest/map/pose.pcd");
    printf("init time: %lf\n", l_tt.toc());

    double l_dAvgSubmapTime = 0.0;
    double l_dMaxSubmapTime = 0.0;
    double l_dMinSubmapTime = 1000000.0;
    double l_dSubmapTime = 0;
    int l_iTimeCnt = 0;
    bool res = false;

    for (std::size_t i = 0; i < c_keyFrameMap_->getKfSize(); i++)
    {
        PointT l_pcscanRangeMin, l_pcscanRangeMax;
        KeyFramePtr l_kfMap = c_keyFrameMap_->getKfMapByInd(i);
        type::POSE l_pose = c_keyFrameMap_->getKfPoseByInd(i);
        pcl::getMinMax3D(*(l_kfMap->m_pFeature->second), l_pcscanRangeMin, l_pcscanRangeMax);
        SubmapBox::Pose l_currPose(l_pose.x, l_pose.y, l_pose.z);
        l_submap.setScanRange(l_pcscanRangeMin.x, l_pcscanRangeMin.y, l_pcscanRangeMax.x, l_pcscanRangeMax.y);
        l_submap.setPose(l_currPose, Eigen::Quaterniond::Identity());
        
        l_tt.tic();
        res = l_submap.getSubmap_();
        printf("################################ Pose: %ld #####################################\n", i);
        l_dSubmapTime = l_tt.toc();
        printf("getSubmap_ time: %lf\n", time);
        printf("getSubmap_ return: %s\n", res ? "true" : "false");
        printf("Pose: (%lf, %lf, %lf)\n", l_currPose.x(), l_currPose.y(), l_currPose.z());
        printf("Submap Inds: ");
        for (std::size_t i = 0; i < l_submap.c_vSubmapKfInds_.size(); i++)
        {
            printf("%d, " , l_submap.c_vSubmapKfInds_[i]);
        }
        printf("\n");
        printf("Motion Inds: ");
        std::vector<int> l_motionInds = l_submap.c_submapBox_.getLoopKfInds();
        for (std::size_t i = 0; i < l_motionInds.size(); i++)
        {
            printf("%d, " , l_motionInds[i]);
        }
        printf("\n");

        if (res)
        {
            l_iTimeCnt++;
            l_dAvgSubmapTime += l_dSubmapTime;
            l_dMaxSubmapTime = std::max<double>(l_dMaxSubmapTime, l_dSubmapTime);
            l_dMinSubmapTime = std::min<double>(l_dMinSubmapTime, l_dSubmapTime);
            KeyFramePtr l_wholeMapPtr = boost::make_shared<KeyFrame>();
            KeyFramePtr l_addMapPtr = boost::make_shared<KeyFrame>();

            l_tt.tic();
            res = l_submap.transformAddPointer(l_wholeMapPtr, l_addMapPtr, l_motionInds);
            printf("transformAddPointer time: %lf\n", l_tt.toc());

            printf("WholeMap size: [%d, %d, %d, %d]\n", l_wholeMapPtr->m_pFeature->cornerSize(), l_wholeMapPtr->m_pFeature->surfSize(), 
                l_wholeMapPtr->m_pFeature->markSize(), l_wholeMapPtr->m_pFeature->curbSize());
            printf("AddMap size: [%d, %d, %d, %d]\n", l_addMapPtr->m_pFeature->cornerSize(), l_addMapPtr->m_pFeature->surfSize(), 
                l_addMapPtr->m_pFeature->markSize(), l_addMapPtr->m_pFeature->curbSize());

            printf("Motion Inds: ");
            std::vector<int> l_motionInds = l_submap.c_submapBox_.getLoopKfInds();
            for (std::size_t i = 0; i < l_motionInds.size(); i++)
            {
                printf("%d, " , l_motionInds[i]);
            }
            printf("\n");
        }
        
        printf("#############################################################################\n");
    }

    l_dAvgSubmapTime /= l_iTimeCnt;
    printf("************************************ time ***************************************\n");
    printf("average time : %f\n", l_dAvgSubmapTime);
    printf("max time : %f\n", l_dMaxSubmapTime);
    printf("min time : %f\n", l_dMinSubmapTime);
    printf("************************************ time ***************************************\n");
}
} // wj_slam