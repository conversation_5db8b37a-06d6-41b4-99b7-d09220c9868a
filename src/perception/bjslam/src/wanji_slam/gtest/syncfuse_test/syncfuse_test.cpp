/**
 * @file syncfuse_test.cpp
 * <AUTHOR>
 * @brief
 * @version 1.0
 * @date 2023-08-16
 */
#include "../../include/common/common_ex.h"
#include "../../test/param.hpp"
#include "../../test/preproc/impl/timeSyncSensorFusion.hpp"
#include <gtest/gtest.h>
#include <iostream>

WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;

namespace wj_slam {
class syncfuse_test : public testing::Test {
  protected:
    typedef typename pcl::PointXYZHSV P;
    typedef typename FEATURE_PAIR<P>::Ptr FeaturePairPtr;
    typedef pcl::PointCloud<P> Feature;
    typedef boost::shared_ptr<Feature> FeaturePtr;
    typedef std::vector<FeaturePairPtr> F_QUEUE;

    SYSPARAM* c_stsysParam;
    FeaturePairPtr c_feature;
    generalLog* glog = new generalLog("/home/<USER>/slam_wsv2.0/src/wanji_slam/data/Log/Logfiles",
                                      "wj",
                                      FileMode::DAILY_ROTATE,
                                      LogMode::TRIAL_MODE,
                                      10);
    void synccallback(FeaturePairPtr& p_fEPair)
    {
        std::cout << p_fEPair->m_dTimespan << std::endl;
        std::cout << p_fEPair->m_dTimestamp << std::endl;
        std::cout << p_fEPair->m_iRecvTimestamp << std::endl;
        std::cout << p_fEPair->m_iSample2ndSize << std::endl;
        std::cout << p_fEPair->m_uiScanFrame << std::endl;
        std::cout << std::endl;
        *c_feature = *p_fEPair;
    }
    boost::shared_ptr<TimeSync> c_syncfuse;
    void SetUp() override
    {
        Param l_param(false);
        l_param.loadSysParam("/home/<USER>/slam_wsv2.0/src/wanji_slam");
        c_stsysParam = SYSPARAM::getIn();
        c_stsysParam->m_sPkgPath = "/home/<USER>/slam_wsv2.0/src/wanji_slam";
        c_feature.reset(new FEATURE_PAIR<P>());
        c_syncfuse.reset(new TimeSync(boost::bind(&syncfuse_test::synccallback, this, _1)));
    }

    void TearDown() override {}
};

// TEST_F(syncfuse_test, TimeSync)
// {
//     EXPECT_EQ(c_syncfuse->c_iMaxTimeBehind_, c_stsysParam->m_prep.m_sync.m_iMaxTimeBehind);
//     EXPECT_EQ(c_syncfuse->c_iMaxTimeSync_, c_stsysParam->m_prep.m_sync.m_iMaxTimeSync);
//     EXPECT_EQ(c_syncfuse->c_iMaxTimeDelay_, c_stsysParam->m_prep.m_sync.m_iMaxTimeDelay);
//     EXPECT_EQ(c_syncfuse->c_stQues_.m_lidars.size(), c_stsysParam->m_iLidarNum);
//     EXPECT_EQ(c_syncfuse->c_SyncSet.size(), c_stsysParam->m_iLidarNum);
//     EXPECT_EQ(c_syncfuse->TIME_FIELDS_OFFSET, 5);
//     EXPECT_EQ(c_syncfuse->ID_FIELDS_OFFSET, 6);

//     for (size_t i = 0; i < c_stsysParam->m_iLidarNum; i++)
//     {
//         EXPECT_EQ(c_syncfuse->c_SyncSet[i], TimeSync::SYNC_SET::ENABLE);
//     }

//     c_stsysParam->m_prep.m_sync.m_iMaxTimeBehind = 100;
//     c_stsysParam->m_prep.m_sync.m_iMaxTimeSync = 200;
//     c_stsysParam->m_prep.m_sync.m_iMaxTimeDelay = 300;
//     c_stsysParam->m_iLidarNum = 5;
//     c_syncfuse.reset(new TimeSync(boost::bind(&syncfuse_test::synccallback, this, _1)));

//     EXPECT_EQ(c_syncfuse->c_iMaxTimeBehind_, c_stsysParam->m_prep.m_sync.m_iMaxTimeBehind);
//     EXPECT_EQ(c_syncfuse->c_iMaxTimeSync_, c_stsysParam->m_prep.m_sync.m_iMaxTimeSync);
//     EXPECT_EQ(c_syncfuse->c_iMaxTimeDelay_, c_stsysParam->m_prep.m_sync.m_iMaxTimeDelay);
//     EXPECT_EQ(c_syncfuse->c_stQues_.m_lidars.size(), c_stsysParam->m_iLidarNum);
//     EXPECT_EQ(c_syncfuse->c_SyncSet.size(), c_stsysParam->m_iLidarNum);
//     EXPECT_EQ(c_syncfuse->TIME_FIELDS_OFFSET, 5);
//     EXPECT_EQ(c_syncfuse->ID_FIELDS_OFFSET, 6);

//     c_syncfuse->setTimeOffset(-100);
//     c_syncfuse->setIDOffset(-10);
//     EXPECT_EQ(c_syncfuse->TIME_FIELDS_OFFSET, -100);
//     EXPECT_EQ(c_syncfuse->ID_FIELDS_OFFSET, -10);
// }

// /*该测试只测试syncInputCallBack逻辑
// 由于syncInputCallBack函数内部锁的存在，会导致无法连续往雷达数据队列添加数据，此处的判断是不通过的*/
// TEST_F(syncfuse_test, syncInputCallBack)
// {
//     // 构造真实的数据，需要读取本地点云文件
//     FeaturePairPtr l_feature(new FEATURE_PAIR<P>());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_feature->first);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_feature->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_feature->third);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_feature->fourth);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd", *l_feature->allPC);

//     for (size_t i = 0; i < 10; i++)
//     {
//         l_feature->m_uiScanFrame = i;
//         l_feature->m_dTimestamp = i * 100;
//         l_feature->m_dTimespan = 100;
//         l_feature->m_iRecvTimestamp = i * 101;
//         c_syncfuse->syncInputCallBack(0, l_feature);
//         EXPECT_EQ(c_syncfuse->c_stQues_.m_lidars[0].size(), i + 1);
//     }
// }

// TEST_F(syncfuse_test, anyLidarQueueNotEmpty)
// {
//     EXPECT_EQ(c_syncfuse->anyLidarQueueNotEmpty(c_syncfuse->c_stQues_.m_lidars), false);

//     FeaturePairPtr l_feature(new FEATURE_PAIR<P>());
//     c_syncfuse->c_stQues_.m_lidars[0].push_back(l_feature);
//     EXPECT_EQ(c_syncfuse->anyLidarQueueNotEmpty(c_syncfuse->c_stQues_.m_lidars), true);
//     c_syncfuse->c_stQues_.m_lidars[0].clear();

//     for (size_t i = 0; i < c_syncfuse->c_stQues_.m_lidars.size(); i++)
//     {
//         c_syncfuse->c_stQues_.m_lidars[i].push_back(l_feature);
//     }
//     EXPECT_EQ(c_syncfuse->anyLidarQueueNotEmpty(c_syncfuse->c_stQues_.m_lidars), true);
// }

// TEST_F(syncfuse_test, getLidarFrames)
// {
//     std::vector<FeaturePairPtr> l_lidarDatas(c_syncfuse->c_stQues_.m_lidars.size(), nullptr);
//     EXPECT_EQ(c_syncfuse->getLidarFrames(l_lidarDatas), false);

//     FeaturePairPtr l_feature1(new FEATURE_PAIR<P>());
//     FeaturePairPtr l_feature2(new FEATURE_PAIR<P>());
//     l_feature1->m_dTimestamp = 500;
//     l_feature1->m_uiScanFrame = 1;
//     c_syncfuse->c_stQues_.m_lidars[0].push_back(l_feature1);
//     EXPECT_EQ(c_syncfuse->getLidarFrames(l_lidarDatas), false);

//     c_syncfuse->c_iMaxTimeDelay_ = 300;
//     c_syncfuse->c_iLastSendTime_ = 100;
//     EXPECT_EQ(c_syncfuse->getLidarFrames(l_lidarDatas), true);

//     l_lidarDatas.clear();
//     l_feature2->m_dTimestamp = 550;
//     l_feature2->m_uiScanFrame = 2;
//     c_syncfuse->c_stQues_.m_lidars[1].push_back(l_feature2);
//     EXPECT_EQ(c_syncfuse->getLidarFrames(l_lidarDatas), true);
//     EXPECT_EQ(l_lidarDatas[0]->m_uiScanFrame, 1);
//     EXPECT_EQ(l_lidarDatas[1]->m_uiScanFrame, 2);
// }

// TEST_F(syncfuse_test, mergLidars)
// {
//     // 此处需要访问点云真实数据，需要读取本地点云文件
//     FeaturePairPtr l_feature1(new FEATURE_PAIR<P>());
//     FeaturePairPtr l_feature2(new FEATURE_PAIR<P>());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn/corn_17000.pcd", *l_feature1->first);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd",
//     *l_feature1->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd", *l_feature1->third);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd",
//     *l_feature1->fourth);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd", *l_feature1->allPC);
//     l_feature1->m_dTimespan = 100;
//     l_feature1->m_dTimestamp = 500;
//     l_feature1->m_iRecvTimestamp = 501;
//     l_feature1->m_uiScanFrame = 10;
//     l_feature1->m_iSample2ndSize = 500;

//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn/corn_17010.pcd", *l_feature2->first);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17010.pcd",
//     *l_feature2->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17010.pcd", *l_feature2->third);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17010.pcd",
//     *l_feature2->fourth);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17010.pcd", *l_feature2->allPC);
//     l_feature2->m_dTimespan = 100;
//     l_feature2->m_dTimestamp = 550;
//     l_feature2->m_iRecvTimestamp = 552;
//     l_feature2->m_uiScanFrame = 15;
//     l_feature2->m_iSample2ndSize = 400;

//     FeaturePairPtr l_feaout(new FEATURE_PAIR<P>());
//     std::vector<FeaturePairPtr> l_lidarDatas(c_syncfuse->c_stQues_.m_lidars.size(), nullptr);
//     int l_iTimeStamp = 0, l_iRecvTimeStamp = 0, l_iTimeSpan = 0;
//     c_syncfuse->mergLidars(l_lidarDatas, l_iTimeStamp, l_iRecvTimeStamp, l_iTimeSpan, l_feaout);

//     l_lidarDatas.push_back(l_feature1);
//     c_syncfuse->mergLidars(l_lidarDatas, l_iTimeStamp, l_iRecvTimeStamp, l_iTimeSpan, l_feaout);
//     EXPECT_EQ(l_iTimeStamp, l_feature1->m_dTimestamp);
//     EXPECT_EQ(l_iRecvTimeStamp, l_feature1->m_iRecvTimestamp);
//     EXPECT_EQ(l_iTimeSpan, l_feature1->m_dTimespan);
//     EXPECT_EQ(l_feaout->m_uiScanFrame, l_feature1->m_uiScanFrame);
//     EXPECT_EQ(l_feaout->m_iSample2ndSize, l_feature1->m_iSample2ndSize);

//     l_lidarDatas.push_back(l_feature2);
//     c_syncfuse->mergLidars(l_lidarDatas, l_iTimeStamp, l_iRecvTimeStamp, l_iTimeSpan, l_feaout);
//     EXPECT_EQ(l_iTimeStamp, l_feature1->m_dTimestamp);
//     EXPECT_EQ(l_iRecvTimeStamp, l_feature1->m_iRecvTimestamp);
//     EXPECT_EQ(l_iTimeSpan, 150);
//     EXPECT_EQ(l_feaout->m_uiScanFrame, l_feature2->m_uiScanFrame);
//     EXPECT_EQ(l_feaout->m_iSample2ndSize, 900);
// }

TEST_F(syncfuse_test, syncInputCallBack)
{
    c_syncfuse->c_iMaxTimeDelay_ = 100;

    for (size_t i = 0; i < 200; i++)
    {
        if (i <= 99 || i >= 149)
        {
            FeaturePairPtr l_feature(new FEATURE_PAIR<P>());
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn/corn_17000.pcd", *l_feature->first);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd",
                                *l_feature->second);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd", *l_feature->third);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd",
                                *l_feature->fourth);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd", *l_feature->allPC);
            l_feature->m_dTimespan = 100;
            l_feature->m_dTimestamp = 1000 + i * 50;
            l_feature->m_iRecvTimestamp = 1000 + i * 60;
            l_feature->m_uiScanFrame = i;
            l_feature->m_iSample2ndSize = 500;

            if (i % 2 == 0)
            {
                c_syncfuse->syncInputCallBack(0, l_feature);
            }
            else
            {
                c_syncfuse->syncInputCallBack(1, l_feature);
                if (i >= 150)
                {
                    EXPECT_EQ(c_feature->m_dTimespan, 150);
                    EXPECT_EQ(c_feature->m_dTimestamp, 1000 + (i - 1) * 50);
                    EXPECT_EQ(c_feature->m_iRecvTimestamp, 1000 + (i - 1) * 60);
                    EXPECT_EQ(c_feature->m_uiScanFrame, l_feature->m_uiScanFrame);
                    EXPECT_EQ(c_feature->m_iSample2ndSize, 1000);
                }
            }
            sleepMs(40);
        }
        else
        {
            FeaturePairPtr l_feature1(new FEATURE_PAIR<P>());
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn/corn_17000.pcd", *l_feature1->first);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd",
                                *l_feature1->second);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd", *l_feature1->third);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd",
                                *l_feature1->fourth);
            pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf/surf_17000.pcd", *l_feature1->allPC);
            l_feature1->m_dTimespan = 100;
            l_feature1->m_dTimestamp = 1000 + i * 50 + 50 * (i - 100);
            l_feature1->m_iRecvTimestamp = 1000 + i * 60 + 60 * (i - 100);
            l_feature1->m_uiScanFrame = i;
            l_feature1->m_iSample2ndSize = 500;
            c_syncfuse->syncInputCallBack(1, l_feature1);
            sleepMs(100);
            if (i >= 102)
            {
                EXPECT_EQ(c_feature->m_dTimespan, l_feature1->m_dTimespan);
                EXPECT_EQ(c_feature->m_dTimestamp, l_feature1->m_dTimestamp);
                EXPECT_EQ(c_feature->m_iRecvTimestamp, l_feature1->m_iRecvTimestamp);
                EXPECT_EQ(c_feature->m_uiScanFrame, l_feature1->m_uiScanFrame);
                EXPECT_EQ(c_feature->m_iSample2ndSize, l_feature1->m_iSample2ndSize);
            }
        }
    }
}

}  // namespace wj_slam