#include <Eigen/Dense>
#include <gtest/gtest.h>
//测试调用函数
#include "../../include/algorithm/loop/amcl_ParticleFileter/impl/ParticleFileter.hpp"
class ParticleFilter : public testing::Test {
  protected:
    boost::shared_ptr<amcl_loop::ParticleFilter> particleFilter_;
    virtual void SetUp()
    {
        particleFilter_.reset(new amcl_loop::ParticleFilter());
    }
    virtual void TearDown() {}
};

/*测试inPI函数
 1.测试输入任意角度,会不会转化成-180到180度,且余弦值保持一致(大小正确)
 2.随机函数若上下界写反了,会不会出错
*/

TEST_F(ParticleFilter, inPI)
{
    for (size_t i = 0; i < 1000; ++i)
    {
        double l_fAngle = particleFilter_->rngUniform_(-10000.0, +10000.0);
        double result = amcl_loop::inPI(l_fAngle);
        //结果的绝对值小于180
        EXPECT_LT(fabs(result), 180);
        //与原值的大小一致,精确到小数点后6位(余弦一致)
        EXPECT_NEAR(cos(l_fAngle * amcl_loop::D2R), cos(result * amcl_loop::D2R), 1e-6);
    }

    for (size_t i = 0; i < 1000; ++i)
    {
        //测试上下界输入反了会不会出错,结果是不会
        double l_fAngle = particleFilter_->rngUniform_(10000.0, -10000.0);
        double result = amcl_loop::inPI(l_fAngle);
        //结果的绝对值小于180
        EXPECT_LT(fabs(result), 180);
        //与原值的大小一致,精确到小数点后6位(余弦一致)
        EXPECT_NEAR(cos(l_fAngle * amcl_loop::D2R), cos(result * amcl_loop::D2R), 1e-6);
    }
}

/*测试setInitRegion函数:
1.输入分布范围后,类内范围值被同步修改;
2.输入的角度范围缩小到正负180度范围
3.粒子分布在设定范围内
*/
TEST_F(ParticleFilter, setInitRegion)
{
    for (size_t i = 0; i < 10000; ++i)
    {
        float X_Min = particleFilter_->rngUniform_(-1000.0, 0);
        float X_Max = particleFilter_->rngUniform_(0, +1000.0);
        float Y_Min = particleFilter_->rngUniform_(-1000.0, 0);
        float Y_Max = particleFilter_->rngUniform_(0, +1000.0);
        float Z_Min = particleFilter_->rngUniform_(-1000.0, 0);
        float Z_Max = particleFilter_->rngUniform_(0, +1000.0);
        float A_Min = particleFilter_->rngUniform_(-1000.0, 0);
        float A_Max = particleFilter_->rngUniform_(0, +1000.0);
        //输入粒子范围
        particleFilter_->setInitRegion(X_Min, X_Max, Y_Min, Y_Max, Z_Min, Z_Max, A_Min, A_Max);
        //范围同步
        ASSERT_EQ(particleFilter_->c_fDev_x_max_, X_Max);
        ASSERT_EQ(particleFilter_->c_fDev_x_min_, X_Min);
        ASSERT_EQ(particleFilter_->c_fDev_y_max_, Y_Max);
        ASSERT_EQ(particleFilter_->c_fDev_y_min_, Y_Min);
        ASSERT_EQ(particleFilter_->c_fDev_z_max_, Z_Max);
        ASSERT_EQ(particleFilter_->c_fDev_z_min_, Z_Min);
        ASSERT_EQ(particleFilter_->c_fDev_a_max_,
                  amcl_loop::inPI(A_Max) > amcl_loop::inPI(A_Min) ? amcl_loop::inPI(A_Max)
                                                                  : amcl_loop::inPI(A_Min));
        ASSERT_EQ(particleFilter_->c_fDev_a_min_,
                  amcl_loop::inPI(A_Max) < amcl_loop::inPI(A_Min) ? amcl_loop::inPI(A_Max)
                                                                  : amcl_loop::inPI(A_Min));

        //输入的角度已转化为-180到180度
        // EXPECT_NEAR(cos(A_Min*
        // amcl_loop::D2R),cos(particleFilter_->c_fDev_a_min_*amcl_loop::D2R),1e-6);
        // EXPECT_NEAR(cos(A_Max*
        // amcl_loop::D2R),cos(particleFilter_->c_fDev_a_max_*amcl_loop::D2R),1e-6);

        //粒子分布
        for (auto p : particleFilter_->c_vP_)
        {
            //<=  按理说< 但是测试发现取到了右边界
            EXPECT_LE(p.x, X_Max);
            EXPECT_LE(p.y, Y_Max);
            EXPECT_LE(p.z, Z_Max);
            EXPECT_LE(p.a, particleFilter_->c_fDev_a_max_);
            //>=
            EXPECT_GE(p.x, X_Min);
            EXPECT_GE(p.y, Y_Min);
            EXPECT_GE(p.z, Z_Min);
            EXPECT_GE(p.a, particleFilter_->c_fDev_a_min_);
            //权重全为0
            ASSERT_EQ(p.w, 0);
            ASSERT_EQ(p.wn, 0);
            ASSERT_EQ(p.wp, 0);
        }
    }
}

/*权重分布函数update()测试
1.正确获取平均权重
2.每个粒子权重W归一化
3.根据归一化位姿获取正确平均位姿
*/
TEST_F(ParticleFilter, update)
{
    particleFilter_->c_vP_.resize(10000);
    for (size_t i = 0; i < particleFilter_->c_vP_.size(); ++i)
    {
        particleFilter_->c_vP_[i].x = particleFilter_->rngUniform_(-10.0, 10.0);
        particleFilter_->c_vP_[i].y = particleFilter_->rngUniform_(-10.0, 10.0);
        //越靠近0权重越大
        particleFilter_->c_vP_[i].w =
            1
            - (sqrt(pow(particleFilter_->c_vP_[i].x, 2) + pow(particleFilter_->c_vP_[i].y, 2))
               / 200);
    }
    particleFilter_->update();
    EXPECT_NEAR(particleFilter_->c_stMeanPose_.x, 0, 0.5);
    EXPECT_NEAR(particleFilter_->c_stMeanPose_.y, 0, 0.5);
    std::cout << "平均权重" << particleFilter_->c_stMeanPose_.w << std::endl;
    std::cout << "最大权重" << particleFilter_->c_stBestPose_.w << std::endl;
}

TEST_F(ParticleFilter, update2)
{
    particleFilter_->c_vP_.resize(10000);
    for (size_t i = 0; i < particleFilter_->c_vP_.size(); ++i)
    {
        particleFilter_->c_vP_[i].x = particleFilter_->rngUniform_(0, 10.0);
        particleFilter_->c_vP_[i].y = particleFilter_->rngUniform_(0, 10.0);
        //越靠近0权重越大
        particleFilter_->c_vP_[i].w = 1
                                      - (sqrt(pow(particleFilter_->c_vP_[i].x - 5, 2)
                                              + pow(particleFilter_->c_vP_[i].y - 5, 2))
                                         / 50);
    }
    particleFilter_->update();
    EXPECT_NEAR(particleFilter_->c_stMeanPose_.x, 5, 0.5);
    EXPECT_NEAR(particleFilter_->c_stMeanPose_.y, 5, 0.5);
    std::cout << "平均权重" << particleFilter_->c_stMeanPose_.w << std::endl;
    std::cout << "最大权重" << particleFilter_->c_stBestPose_.w << std::endl;
}
