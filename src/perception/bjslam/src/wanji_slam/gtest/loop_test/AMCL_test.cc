#include <Eigen/Dense>
#include <gtest/gtest.h>
#include <iostream>
#include <math.h>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgcodecs.hpp>
//测试调用函数
#include "../../include/algorithm/loop/amcl_ParticleFileter/impl/ParticleFileter.hpp"
#include "../../include/algorithm/loop/placeRecongnize/impl/placeRecongnize.hpp"
#include "../../include/algorithm/loop/placeRecongnize/placeRecongnize.h"

// pcl库
#include <pcl/common/transforms.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
// icp
#include <iostream>
#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <pcl/registration/icp.h>

// LOOP 2 MAP _ ALIGN
#include "../../include/algorithm/loop/amcl_ParticleFileter/impl/amcl.hpp"
#include "../../include/algorithm/loop/loopCheck/loop2map_align.hpp"

class amcl : public testing::Test {
  protected:
    using PointT = pcl::PointXYZHSV;
    using PointCloudT = pcl::PointCloud<PointT>;
    using FeaturePair = FEATURE_PAIR<PointT>;         /**< 输入特征组类型 */
    using FeaturePairPtr = typename FeaturePair::Ptr; /**< 输入特征组指针类型 */

    boost::shared_ptr<wj_slam::L2M_Align<PointT, PointT>> c_pL2M_;
    boost::shared_ptr<amcl_loop::AMCL<PointT>> amcl_;
    boost::shared_ptr<wj_slam::PlaceRecongnize> sc_caculer;
    SYSPARAM* c_stSysParam_;

    std::string l_sSc_MainFile = "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_rawkf/KF/";
    std::string l_sSc_saveFile = "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/";
    std::string l_sName_sc = "/sc.tif";
    std::string l_sName_pose = "/pose.csv";
    std::string l_id_1 = "59";
    std::string l_id_2 = "289";

    boost::shared_ptr<amcl_loop::ParticleFilter> particleFilter_;
    virtual void SetUp()
    {
        particleFilter_.reset(new amcl_loop::ParticleFilter());
        c_stSysParam_ = SYSPARAM::getIn();
        c_pL2M_.reset(new wj_slam::L2M_Align<PointT, PointT>());
        amcl_.reset(new amcl_loop::AMCL<PointT>());
        sc_caculer.reset(new wj_slam::PlaceRecongnize());
    }
    virtual void TearDown() {}
    void prinf(std::string type)
    {
        std::cout << type.c_str() << std::endl;
    }
    void readOneFrame(std::string p_sFilePath, FeaturePairPtr p_pPCs, s_POSE6D& p_pose)
    {
        static int i = 0;
        std::string c_sAl = "all.pcd";    /**< 全部点云文件名 */
        std::string c_sFi = "fi.pcd";     /**< 角点点云文件名 */
        std::string c_sSe = "se.pcd";     /**< 面点点云文件名 */
        std::string c_sMark = "mark.pcd"; /**< 面点点云文件名 */
        std::string c_sCu = "curb.pcd";   /**< 中线点云文件名 */
        std::string c_sPt = "p.pcd";      /**< 路径点云文件名 */
        std::string c_sPs = "pose.csv";   /**< 位姿及信息文件名 */
        std::string c_sSc = "sc.tif";     /**< 描述符图片文件名 */

        // std::cout << "read from " + p_sFilePath << std::endl;
        std::string l_sAl = p_sFilePath + "/" + c_sAl;
        std::string l_sFi = p_sFilePath + "/" + c_sFi;
        std::string l_sSe = p_sFilePath + "/" + c_sSe;
        std::string l_sCu = p_sFilePath + "/" + c_sCu;
        std::string l_sPs = p_sFilePath + "/" + c_sPs;
        std::string l_sSc = p_sFilePath + "/" + c_sSc;

        // pose.csv
        std::ifstream l_Psfile(l_sPs.c_str());
        if (!l_Psfile)
            std::cout << "Error File" << l_sPs.c_str() << std::endl;
        else
        {
            double p_dData[7];
            int p_iData[4];
            std::string str;
            // 第一行
            getline(l_Psfile, str);
            // 逗号分隔依次取出
            char* p = strtok((char*)str.data(), ",");
            //  顺序 "x,y,z,roll,pitch,yaw,m_fPercent,id,time,sampleSize,m_bHasContext"
            for (int ie = 0; ie < 7; ++ie)
            {
                p_dData[ie] = atof(p);
                // std::cout << p_dData[ie] << " ";
                p = strtok(NULL, ",");
            }
            for (int ie = 7; ie < 11; ++ie)
            {
                p_iData[ie - 7] = atoi(p);
                // std::cout << p_iData[ie - 7] << " ";
                p = strtok(NULL, ",");
            }
            l_Psfile.close();
            std::cout << std::endl;

            // 填写
            p_pose.m_trans << p_dData[0], p_dData[1], p_dData[2];

            p_pose.setRPY(p_dData[3], p_dData[4], p_dData[5]);

            p_pose.m_fPercent = p_dData[6];

            p_pPCs->m_uiScanFrame = (uint)p_iData[0];

            p_pPCs->m_dTimestamp = p_iData[1];

            p_pPCs->m_iSample2ndSize = p_iData[2];

            p_pPCs->m_bHasContext = p_iData[3];
        }
        // 4x .pcd

        pcl::io::loadPCDFile<PointT>(l_sAl, *p_pPCs->allPC);

        pcl::io::loadPCDFile<PointT>(l_sFi, *p_pPCs->first);

        pcl::io::loadPCDFile<PointT>(l_sSe, *p_pPCs->second);

        readImageToMatrix_(l_sSc, *p_pPCs->sc);
    }

    void readImageToMatrix_(std::string p_sFilePath, Eigen::MatrixXd& matrix)
    {
        cv::Mat image = cv::imread(p_sFilePath, cv::IMREAD_UNCHANGED);  //按照原格式读取

        if (!image.empty())
        {
            int rows = image.rows;
            int cols = image.cols;

            matrix.resize(rows, cols);

            for (int y = 0; y < rows; ++y)
            {
                for (int x = 0; x < cols; ++x)
                {
                    matrix(y, x) = static_cast<double>(image.at<float>(y, x));
                }
            }
        }
        else
        {
            // Handle error: Unable to read the image file
            // You might throw an exception or set an error flag here
        }
    }
};
// //功能测试:
TEST_F(amcl, function_test)
{
    /*读地图*/
    int l_iMapID = atoi(l_id_1.c_str());
    std::vector<FeaturePairPtr> l_vFearture_map(10);
    int j = 0;
    s_POSE6D l_MapPose;
    for (size_t i = l_iMapID - 5; i < l_iMapID + 5; i++)
    {
        s_POSE6D l_pose;
        std::string path = l_sSc_MainFile + std::to_string(i);
        l_vFearture_map[j].reset(new FEATURE_PAIR<PointT>());
        readOneFrame(path, l_vFearture_map[j], l_pose);
        j++;
        if (l_iMapID == i)
            l_MapPose = l_pose;
    }
    // l_MapPose.printf();
    wj_slam::KEYFRAME<PointT>::Ptr l_KFMap(new wj_slam::KEYFRAME<PointT>());
    wj_slam::KEYFRAME<PointT>::Ptr l_KFMap_one(new wj_slam::KEYFRAME<PointT>());
    s_POSE6D l_Map_one_Pose;
    readOneFrame(l_sSc_MainFile + l_id_1, l_KFMap_one->m_pFeature, l_Map_one_Pose);
    std::cout << "地图单帧面点数:" << l_KFMap_one->m_pFeature->second->size() << std::endl;
    std::cout << "地图单帧角点数:" << l_KFMap_one->m_pFeature->first->size() << std::endl;

    for (auto p : l_vFearture_map)
    {
        *(l_KFMap->m_pFeature->first) += *(p->first);
        *(l_KFMap->m_pFeature->second) += *(p->second);
    }

    s_POSE6D l_CurPose;
    wj_slam::KEYFRAME<PointT>::Ptr l_KFCur(new wj_slam::KEYFRAME<PointT>());
    readOneFrame(l_sSc_MainFile + l_id_2, l_KFCur->m_pFeature, l_CurPose);

    Eigen::MatrixXd l_matrix_1;
    Eigen::MatrixXd l_matrix_2;

    readImageToMatrix_(l_sSc_MainFile + l_id_1 + l_sName_sc, l_matrix_1);
    readImageToMatrix_(l_sSc_MainFile + l_id_2 + l_sName_sc, l_matrix_2);

    auto dist_shift = sc_caculer->distanceBtnScanContext(l_matrix_1, l_matrix_2);
    std::cout << dist_shift.first << std::endl;
    std::cout << dist_shift.second << std::endl;

    double l_dRotang = dist_shift.second / 60.0 * 360.0;
    std::cout << "角度:" << l_dRotang << std::endl;

    //转到局部坐标
    s_POSE6D l_EstTrans_wto0 = l_MapPose.inverse();

    c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
                                          l_EstTrans_wto0.m_trans,
                                          l_KFMap->m_pFeature->first,
                                          l_KFMap->m_pFeature->first);
    c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
                                          l_EstTrans_wto0.m_trans,
                                          l_KFMap->m_pFeature->second,
                                          l_KFMap->m_pFeature->second);
    c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
                                          l_EstTrans_wto0.m_trans,
                                          l_KFMap_one->m_pFeature->first,
                                          l_KFMap_one->m_pFeature->first);
    c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
                                          l_EstTrans_wto0.m_trans,
                                          l_KFMap_one->m_pFeature->second,
                                          l_KFMap_one->m_pFeature->second);

    s_POSE6D l_EstTrans_wto01 = l_CurPose.inverse();

    pcl::io::savePCDFile<pcl::PointXYZHSV>(
        "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/" + l_id_1 + "map.pcd",
        *(l_KFMap->m_pFeature->second));

    c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
                                          l_EstTrans_wto01.m_trans,
                                          l_KFCur->m_pFeature->first,
                                          l_KFCur->m_pFeature->first);
    c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
                                          l_EstTrans_wto01.m_trans,
                                          l_KFCur->m_pFeature->second,
                                          l_KFCur->m_pFeature->second);
    // pcl::io::savePCDFile<pcl::PointXYZHSV>(
    // "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/" + l_id_2
    //     + "原始p.pcd",
    // *(l_KFCur->m_pFeature->second));

    //测试 让局部地图远离,能否amcl上

    //  s_POSE6D l_EstTrans_test;
    //  l_EstTrans_test.setXYZ(5,5,0);
    //  l_EstTrans_test.setRPY(0,0,0);

    // c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_test.m_quat,
    //                                               l_EstTrans_test.m_trans,
    //                                               l_KFCur->m_pFeature->first,
    //                                               l_KFCur->m_pFeature->first);
    // c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_test.m_quat,
    //                                               l_EstTrans_test.m_trans,
    //                                               l_KFCur->m_pFeature->second,
    //                                               l_KFCur->m_pFeature->second);
    // c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_test.m_quat,
    //                                               l_EstTrans_test.m_trans,
    //                                               l_KFCur->m_pFeature->allPC,
    //                                               l_KFCur->m_pFeature->allPC);
    //     pcl::io::savePCDFile<pcl::PointXYZHSV>(
    //             "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/" + l_id_2
    //                 + "原始p2.pcd",
    //             *(l_KFCur->m_pFeature->second));

    //

    //外部参数
    c_pL2M_->c_pIvoxGrid_->setInnerResolu(c_stSysParam_->m_map.m_fInGridResolu);
    //设置大栅格尺寸大小
    c_pL2M_->c_pIvoxGrid_->setOuterResolu(c_stSysParam_->m_map.m_fOutGridResolu);
    //设置搜索栅格内部点数
    c_pL2M_->c_pIvoxGrid_->setSearchPointNum(c_stSysParam_->m_map.m_iGridSearchNum);
    //设置iVox栅格容量大小
    c_pL2M_->c_pIvoxGrid_->setGridCapacity(c_stSysParam_->m_map.m_iGridSize);
    //设置栅格周围栅格环绕类型
    c_pL2M_->c_pIvoxGrid_->setGridNearbyType(c_stSysParam_->m_map.m_iGridMatchType);
    //栅格内部初始化
    c_pL2M_->c_pIvoxGrid_->gridInit();
    c_pL2M_->c_pIvoxGrid_->buildGridMap(
        l_KFMap->m_pFeature->second->points, l_KFMap->m_pFeature->first->points, 3);

    // amcl测试

    float l_fPathNum = l_MapPose.m_fPercent - ((int)l_MapPose.m_fPercent);
    float l_fPathScore = (l_MapPose.m_fPercent / 1000);
    std::cout << "比例:" << l_fPathNum << "得分:" << l_fPathScore << std::endl;
    amcl_->setTargetGridMap(c_pL2M_->c_pIvoxGrid_, l_KFMap_one->m_pFeature);
    amcl_->setInputSourceCloud(l_KFCur->m_pFeature->first, l_KFCur->m_pFeature->second);

    s_POSE6D InitialPose;
    // InitialPose = l_EstTrans_test.inverse();
    InitialPose.setRPY(0, 0, -l_dRotang);
    amcl_->setInitialPose(InitialPose);  //输入空位置 or 预估

    amcl_->filter();

    s_POSE6D resultPose;

    amcl_->getOptimalPose(resultPose);

    c_pL2M_->c_trans.transformCloudPoints(resultPose.m_quat,
                                          resultPose.m_trans,
                                          l_KFCur->m_pFeature->allPC,
                                          l_KFCur->m_pFeature->allPC);
    pcl::io::savePCDFile<pcl::PointXYZHSV>(
        "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/" + l_id_2 + "amcl_resault.pcd",
        *(l_KFCur->m_pFeature->allPC));
}
