/**
 * @brief Runze Li
 *
 */
/*
使用说明:
1.该模块测试在user配置的参数是否正确输入到功能模块;
2.增加或删减参数后更新测试案例;
3.执行本测试案例前先检查user配置的参数能正确加载到系统参数中.
*/

// log/param
#include "../../include/common/common_ex.h"
#include "../../include/common/common_master.h"
#include "../../include/wj_log.h"
#include "../../test/param.hpp"
// pcl库
#include <pcl/common/transforms.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
// gtest
#include <gtest/gtest.h>
// opencv
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgcodecs.hpp>
//测试对象
#include "../../include/algorithm/loop/amcl_ParticleFileter/impl/amcl.hpp"
#include "../../include/algorithm/loop/loopCheck/loopCheck.hpp"
#include "../../include/algorithm/map/sub_map/KeyFrameMap.h"

generalLog* glog = nullptr;
WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;
using namespace wj_slam;
class param_test : public testing::Test {
  protected:
    using PointT = pcl::PointXYZHSV;
    using PointCloudT = pcl::PointCloud<PointT>;
    using FeaturePair = FEATURE_PAIR<PointT>;         /**< 输入特征组类型 */
    using FeaturePairPtr = typename FeaturePair::Ptr; /**< 输入特征组指针类型 */
    using Frame = KEYFRAME<PointT>;                   /**< 特征组类型*/
    using FramePtr = typename Frame::Ptr;             /**< 关键帧指针类型*/

    boost::shared_ptr<LoopCheck<PointT>> c_pLoopCheck_;
    boost::shared_ptr<KfMapPair> c_pMapPair_;

    virtual void SetUp()
    {
        // log对象
        wj_slam::s_masterCfg* g_masterParamPtr;
        wj_slam::Param l_param(false);
        g_masterParamPtr = wj_slam::s_masterCfg::getIn();
        l_param.loadSysParam("/home/<USER>/data/lrz_ws/src/wanji_slam");
        glog = new generalLog(g_masterParamPtr->m_slam->m_fae.m_sLogPath,
                              "wj",
                              FileMode::DAILY_ROTATE,
                              LogMode::TRIAL_MODE,
                              g_masterParamPtr->m_slam->m_fae.getLogFileSizeByte());

        glog->changeMode(g_masterParamPtr->m_slam->m_fae.m_iLogLevel);
        c_pMapPair_.reset(new KfMapPair());
        c_pLoopCheck_.reset(new LoopCheck<PointT>(c_pMapPair_));
    }
    virtual void TearDown() {}
};

TEST_F(param_test, DEFULTPARAM)
{
    SYSPARAM* c_stSysParam_;
    c_stSysParam_ = SYSPARAM::getIn();

    // c_pLoopCheck_加载参数
    ASSERT_EQ(c_pLoopCheck_->c_iCurrIndex_, -1);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_fLoopScroeThr_, c_stSysParam_->m_loop.m_fSCthr);
    ASSERT_EQ(c_pLoopCheck_->c_iMapWindowSize_, 10);
    ASSERT_EQ(c_pLoopCheck_->c_iLastLoopIndex_, 0);
    ASSERT_EQ(c_pLoopCheck_->c_iLoopKFIndex_, -1);
    ASSERT_FALSE(c_pLoopCheck_->c_bRun_);

    // c_pMatcher_加载参数
    ASSERT_EQ(c_pLoopCheck_->c_pMatcher_.c_fAlignScore_, 0);
    ASSERT_EQ(c_pLoopCheck_->c_pMatcher_.c_iOptTimes_, c_stSysParam_->m_loop.m_iOptTimes);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_fNumPercentMap_, 100.0);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_fOccupyScoreMap_, 100.0);
    ASSERT_FALSE(c_pLoopCheck_->c_pMatcher_.c_bAMCLModle_);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_fDistanceThr_,
                    c_stSysParam_->m_loop.m_fMatchAveDistanceThr);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_fMatchPercentThr_,
                    c_stSysParam_->m_loop.m_fMatchMinNumPercentThr);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_fVerifyPercentThr_,
                    c_stSysParam_->m_loop.m_fVerifyMatchPercentThr);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_fVerifyScoreThr_,
                    c_stSysParam_->m_loop.m_fVerifyScoreThr);
    // amcl参数
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_stAMCLPara_.m_fDeltaA,
                    c_stSysParam_->m_loop.m_fParticleFilterRangeA);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_stAMCLPara_.m_fDeltaX,
                    c_stSysParam_->m_loop.m_fParticleFilterRangeX);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_stAMCLPara_.m_fDeltaY,
                    c_stSysParam_->m_loop.m_fParticleFilterRangeY);
    ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.c_stAMCLPara_.m_fDeltaZ,
                    c_stSysParam_->m_loop.m_fParticleFilterRangeZ);

    //局部变量,额外测试结果
    // ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.amcl_->c_fDelta_x_,
    //                 c_stSysParam_->m_loop.m_fParticleFilterRangeX);
    // ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.amcl_->c_fDelta_y_,
    //                 c_stSysParam_->m_loop.m_fParticleFilterRangeY);
    // ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.amcl_->c_fDelta_z_,
    //                 c_stSysParam_->m_loop.m_fParticleFilterRangeZ);
    // ASSERT_FLOAT_EQ(c_pLoopCheck_->c_pMatcher_.amcl_->c_fDelta_a_,
    //                 c_stSysParam_->m_loop.m_fParticleFilterRangeA);
    // ASSERT_EQ(c_pLoopCheck_->c_pMatcher_.amcl_->c_iFilteTimes_,
    //           c_stSysParam_->m_loop.m_iParticleFilterTimes);
    // ASSERT_EQ(c_pLoopCheck_->c_pMatcher_.amcl_->c_PartcleFilter_->c_iParticleNum_,
    //           c_stSysParam_->m_loop.m_iParticleNums);

    // sc参数
    // debug模式自带打印,在特征提取里
}

TEST_F(param_test, SETPARAM_RECOGNIZE)
{
    //测试user是否正确设置
    std::cout << "SC粗匹配阈值:" << c_pLoopCheck_->c_fLoopScroeThr_ << std::endl;
    std::cout << "匹配次数:" << c_pLoopCheck_->c_pMatcher_.c_iOptTimes_ << std::endl;
    std::cout << "面点匹配平均距离阈值:" << c_pLoopCheck_->c_pMatcher_.c_fDistanceThr_ << std::endl;
    std::cout << "最低匹配比例阈值:" << c_pLoopCheck_->c_pMatcher_.c_fMatchPercentThr_ << std::endl;
    std::cout << "概率校验最低匹配比例阈值(与地图帧相比):"
              << c_pLoopCheck_->c_pMatcher_.c_fVerifyPercentThr_ << std::endl;
    std::cout << "概率校验最低匹配得分阈值(与地图帧相比):"
              << c_pLoopCheck_->c_pMatcher_.c_fVerifyScoreThr_ << std::endl;
}