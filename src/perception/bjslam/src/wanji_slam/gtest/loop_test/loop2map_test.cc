
#include "../../include/algorithm/loop/loopCheck/loop2map_align.hpp"
// pcl库
#include <gtest/gtest.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <random>
// log
#include "../../include/common/common_ex.h"
#include "../../include/common/common_master.h"
#include "../../include/wj_log.h"
#include "../../test/param.hpp"

using namespace wj_slam;
generalLog* glog = nullptr;
WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;
class SCAN2MAP : public testing::Test {
  protected:
    using PointT = pcl::PointXYZHSV;
    // using PointCloudT = pcl::PointCloud<PointT>;
    // using FeaturePair = FEATURE_PAIR<PointT>;         /**< 输入特征组类型 */
    // using FeaturePairPtr = typename FeaturePair::Ptr; /**< 输入特征组指针类型 */
    // using Frame = KEYFRAME<PointT>;                   /**< 输入帧-特征组指针类型*/
    // using FramePtr = typename Frame::Ptr;

    boost::shared_ptr<wj_slam::L2M_Align<PointT, PointT>> c_pL2M_;

    virtual void SetUp()
    {
        c_pL2M_.reset(new L2M_Align<PointT, PointT>);
        // log对象

        wj_slam::s_masterCfg* g_masterParamPtr;
        wj_slam::Param l_param(false);
        g_masterParamPtr = wj_slam::s_masterCfg::getIn();
        l_param.loadSysParam("/home/<USER>/data/lrz_ws/src/wanji_slam");
        glog = new generalLog(g_masterParamPtr->m_slam->m_fae.m_sLogPath,
                              "wj",
                              FileMode::DAILY_ROTATE,
                              LogMode::TRIAL_MODE,
                              g_masterParamPtr->m_slam->m_fae.getLogFileSizeByte());

        glog->changeMode(g_masterParamPtr->m_slam->m_fae.m_iLogLevel);
    }
    virtual void TearDown() {}

    float getRandomData(const float p_fMin, const float p_fMax)
    {
        std::random_device rd;
        // 用 random_device产生一个真随机数，用作“伪随机数发生器”的种子
        std::default_random_engine gen(rd());
        // 一个正态“分布器”，高斯分布器是 std::normal_distribution
        std::uniform_real_distribution<float> dis(p_fMin, p_fMax);
        return dis(gen);
    }
};

TEST_F(SCAN2MAP, paramtest)
{
    // AMCL参数
    ASSERT_NEAR(c_pL2M_->c_stAMCLPara_.m_fDeltaX, 1.5, 1e-4);
    ASSERT_NEAR(c_pL2M_->c_stAMCLPara_.m_fDeltaY, 1.5, 1e-4);
    ASSERT_NEAR(c_pL2M_->c_stAMCLPara_.m_fDeltaZ, 0.02, 1e-4);
    ASSERT_NEAR(c_pL2M_->c_stAMCLPara_.m_fDeltaA, 0.02, 10);
    ASSERT_EQ(c_pL2M_->c_stAMCLPara_.m_iFiltTimes, 4);
    ASSERT_EQ(c_pL2M_->c_stAMCLPara_.m_iParticleNums, 1000);
    ASSERT_FALSE(c_pL2M_->c_bAMCLModle_);  //默认为假
    // 2d模式
    ASSERT_FALSE(c_pL2M_->c_bXOYOpt_);  //默认为假
    ASSERT_NEAR(c_pL2M_->c_fXOYOptZ_, 0, 1e-4);
    //面点输入模式
    ASSERT_TRUE(c_pL2M_->c_bSampleSurfMatch_);
    //优化终止梯度
    ASSERT_EQ(sizeof(c_pL2M_->c_fEpsilon_) / sizeof(c_pL2M_->c_fEpsilon_[0]), 2);
    ASSERT_NEAR(c_pL2M_->c_fEpsilon_[0], 0.001, 1e-6);
    ASSERT_NEAR(c_pL2M_->c_fEpsilon_[1], 0.01, 1e-6);
    //评分
    ASSERT_NEAR(c_pL2M_->c_fAlignScore_, 0, 1e-3);
    //优化次数
    ASSERT_EQ(c_pL2M_->c_iOptTimes, 8);
    //默认sc预估位姿
    s_POSE6D l_stZero;
    for (int i = 0; i < 7; i++)
    {
        if (c_pL2M_->c_stSCPose_.data()[i] != l_stZero.data()[i])
            ASSERT_NEAR(c_pL2M_->c_stSCPose_.data()[i], l_stZero.data()[i], 1e-4);
    }
}

TEST_F(SCAN2MAP, SET_FUCTION_TEST)
{
    AMCL_param ST_TEST;

    for (size_t i = 0; i < 100; i++)
    {
        ST_TEST.m_fDeltaX = getRandomData(0, 100);
        ST_TEST.m_fDeltaY = getRandomData(0, 100);
        ST_TEST.m_fDeltaZ = getRandomData(0, 100);
        ST_TEST.m_fDeltaA = getRandomData(0, 100);
        ST_TEST.m_iFiltTimes = getRandomData(0, 1000);
        ST_TEST.m_iParticleNums = getRandomData(0, 5000);

        c_pL2M_->setAmclPara(ST_TEST);
        ASSERT_NEAR(c_pL2M_->c_stAMCLPara_.m_fDeltaX, ST_TEST.m_fDeltaX, 1e-4);
        ASSERT_NEAR(c_pL2M_->c_stAMCLPara_.m_fDeltaY, ST_TEST.m_fDeltaY, 1e-4);
        ASSERT_NEAR(c_pL2M_->c_stAMCLPara_.m_fDeltaZ, ST_TEST.m_fDeltaZ, 1e-4);
        ASSERT_EQ(c_pL2M_->c_stAMCLPara_.m_iFiltTimes, ST_TEST.m_iFiltTimes);
        ASSERT_EQ(c_pL2M_->c_stAMCLPara_.m_iParticleNums, ST_TEST.m_iParticleNums);

        // c_pL2M_->amcl_->setFilterFile(c_pL2M_->c_stAMCLPara_.m_fDeltaX,
        //                               c_pL2M_->c_stAMCLPara_.m_fDeltaY,
        //                               c_pL2M_->c_stAMCLPara_.m_fDeltaZ,
        //                               c_pL2M_->c_stAMCLPara_.m_fDeltaA);
        // c_pL2M_->amcl_->setFilterTimes(c_pL2M_->c_stAMCLPara_.m_iFiltTimes);
        // c_pL2M_->amcl_->setParticleNum(c_pL2M_->c_stAMCLPara_.m_iParticleNums);

        // ASSERT_NEAR(c_pL2M_->amcl_->c_fDelta_x_, ST_TEST.m_fDeltaX, 1e-4);
        // ASSERT_NEAR(c_pL2M_->amcl_->c_fDelta_y_, ST_TEST.m_fDeltaY, 1e-4);
        // ASSERT_NEAR(c_pL2M_->amcl_->c_fDelta_z_, ST_TEST.m_fDeltaZ, 1e-4);
        // ASSERT_EQ(c_pL2M_->amcl_->c_iFilteTimes, ST_TEST.m_iFiltTimes);
        // ASSERT_EQ(c_pL2M_->amcl_->c_PartcleFilter->c_iParticleNum_, ST_TEST.m_iParticleNums);
    }
}