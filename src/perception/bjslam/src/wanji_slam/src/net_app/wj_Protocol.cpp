// local
#include "net_app/wj_Protocol.h"
#include "tic_toc.h"
#include "common/config/conf_timer.h"

WJProtocol::WJProtocol(boost::function<void(char*, int)> sendCallback,
                       s_NetMsg& netMsg,
                       boost::function<bool(int)> setWorkModeCb)
    : c_stNetMsg_(netMsg), c_stSysParam_(SYSPARAM::getIn()), sendCallback_(sendCallback),
      setWorkModeCb_(setWorkModeCb), c_sendThr_(nullptr), c_bRun_(true), c_bHasRecvFlag_(false),
      c_bSaveMapFlag_(false), c_bSetWorkModeFlag_(false), c_bIs712_(false)
{
    c_sendThr_.reset(new std::thread(&WJProtocol::sendThread_, this));
}

WJProtocol::~WJProtocol()
{
    shutDown();
}

void WJProtocol::shutDown()
{
    c_bRun_ = false;
    if (c_sendThr_ && c_sendThr_->joinable())
    {
        c_sendThr_->join();
    }
}

void WJProtocol::sendPoseACKCmdByWJ_(u_char* p_ucCmd,
                                     int p_iLen,
                                     int p_ucAnswerFlag,
                                     int p_iWorkState)
{
    char l_acBuf[50] = {0};
    int l_iLen = 0;
    //拷贝数组0-25 l_iLen从26开始
    calcWJProtocolHead_(p_ucCmd, l_acBuf, l_iLen);
    // 发送工作状态
    // 00 : 正常收到要数定位模式下应答帧 01 非定位模式下收到 发送 04 : 要数命令已收到,等待计算完成
    if (p_iWorkState != WorkMode::LocatMode && p_iWorkState != WorkMode::UpdateMapMode)
        l_acBuf[l_iLen++] = 1;
    else if (p_ucAnswerFlag == 1)
        l_acBuf[l_iLen++] = 4;
    else
        l_acBuf[l_iLen++] = 0;
    // 补齐长度
    fillAskField(l_acBuf, l_iLen);
    calcWJProtocolTail_(l_acBuf, l_iLen);
    sendCallback_(l_acBuf, l_iLen);
}
void WJProtocol::sendPoseInfoByWJ_(s_RobotPos& p_stRobot,
                                   u_char p_ucAnswerFlag,
                                   u_char p_ucWorkState)
{
    uint32_t l_uiTime = c_stSysParam_->m_time.getTimeNowMs();
    uint8_t l_uiBuf[100] = {
        0xFF,
        0xAA,
        0x00,
        0x00,
        0x01,
        (l_uiTime >> 24) & 0xFF,
        (l_uiTime >> 16) & 0xFF,
        (l_uiTime >> 8) & 0xFF,
        l_uiTime & 0xFF,
        0x01,
        0x02,
        0x01,
        0x0B,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x0A,
        0x0A,
        0x00,
        0x00,
    };
    int l_iLen = 26;  // l_iLen从26开始
    char* l_acBuf = (char*)l_uiBuf;
    // 发送工作状态
    // 定位模式发送 01 : 工作模式不是定位模式
    if (p_ucWorkState != WorkMode::LocatMode && p_ucWorkState != WorkMode::UpdateMapMode)
        l_acBuf[l_iLen++] = 1;
    // 00 : 立即回复 和 01 : 等待计算完成回复 发送 00 : 正常工作
    else
        l_acBuf[l_iLen++] = 0;
    // 发送要数模式
    l_acBuf[l_iLen++] = p_ucAnswerFlag;
    // 发送定位数据标志
    l_acBuf[l_iLen++] = (int)p_stRobot.flagWj;
    // 填充位置
    for (int i = 0; i < 3; ++i)
        fillFloatTo32Bytes(p_stRobot.t[i], l_acBuf, l_iLen);
    fillIntTo16Bytes((int)(p_stRobot.meandev), l_acBuf, l_iLen);
    fillAskField(l_acBuf, l_iLen);
    calcWJProtocolTail_((char*)l_acBuf, l_iLen);
    sendCallback_((char*)l_acBuf, l_iLen);
}
void WJProtocol::sendPoseInfoByWJ712_(s_RobotPos& p_stRobot,
                                      u_char p_ucAnswerFlag,
                                      u_char p_ucWorkState)
{
    uint32_t l_uiTime = c_stSysParam_->m_time.getTimeNowMs();
    uint8_t l_uiBuf[100] = {
        0xFF,
        0xAA,
        0x00,
        0x00,
        0x01,
        uint8_t((l_uiTime >> 24) & 0xFF),
        uint8_t((l_uiTime >> 16) & 0xFF),
        uint8_t((l_uiTime >> 8) & 0xFF),
        uint8_t(l_uiTime & 0xFF),
        0x01,
        0x02,
        0x00,
        0x07,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x0A,
        0x0A,
        0x00,
        0x00,
    };
    int l_iLen = 26;  // l_iLen从26开始
    char* l_acBuf = (char*)l_uiBuf;
    // 发送工作状态
    // 定位模式发送 01 : 工作模式不是定位模式
    if (p_ucWorkState != WorkMode::LocatMode && p_ucWorkState != WorkMode::UpdateMapMode)
        l_acBuf[l_iLen++] = 1;
    // 00 : 立即回复 和 01 : 等待计算完成回复 发送 00 : 正常工作
    else
        l_acBuf[l_iLen++] = 0;
    // 发送要数模式
    l_acBuf[l_iLen++] = p_ucAnswerFlag;
    // 发送定位数据标志
    l_acBuf[l_iLen++] = (int)p_stRobot.flagWj;
    fillFloatTo32Bytes(p_stRobot.t[2], l_acBuf, l_iLen);
    fillFloatTo32Bytes(p_stRobot.t[0], l_acBuf, l_iLen);
    fillFloatTo32Bytes(p_stRobot.t[1], l_acBuf, l_iLen);
    // 扫描靶标数
    l_acBuf[l_iLen++] = 3;
    // 用于定位靶标数
    l_acBuf[l_iLen++] = 3;
    fillAskField(l_acBuf, l_iLen);
    calcWJProtocolTail_(l_acBuf, l_iLen);
    sendCallback_(l_acBuf, l_iLen);
}
double WJProtocol::toYaw_(const Eigen::Quaterniond q)
{
    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    return atan2(siny_cosp, cosy_cosp);
}
void WJProtocol::sendThread_()
{
    while (c_bRun_)
    {
        if (c_bHasRecvFlag_)
        {
            sendPose_(1, c_stSysParam_->m_iWorkMode, !c_bIs712_);
            c_bHasRecvFlag_ = false;
        }
        sleepMs(1);
    }
}

void WJProtocol::sendPose_(int p_iPoseModel, int p_iWorkState, bool p_bWJ = true)
{
    s_RobotPos l_stPoseSend;
    s_PoseWithTwist l_sPoseWithTwist;

    float l_tDiff = 0.0;
    // 定位模式更新l_sPoseWithTwist/l_stPoseSend 否则使用默认值
    if (c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::LocatMode
        || c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::UpdateMapMode)
    {
        // p_iPoseModel 1： 实时位姿 2：虚拟位姿
        if (p_iPoseModel)
        {
            // 每次使用前清空标志 等待新鲜的最新帧
            c_stSysParam_->m_pos.m_stCurrPoseWJ.setFlag(PoseStatus::Default);
            while (1)
            {
                //死等最新帧定位完毕
                if (c_bRun_
                    && c_stSysParam_->m_pos.m_stCurrPoseWJ.getFlag()
                           != wj_slam::PoseStatus::Default)
                {
                    //复制最新位姿
                    l_sPoseWithTwist = c_stSysParam_->m_pos.m_stCurrPoseWJ.getData();
                    break;
                }
                if (!c_bRun_)
                    return;
                sleepMs(1);
            }
        }
        else
        {
            TicToc l_TlockT;
            //复制last位姿
            l_sPoseWithTwist = c_stSysParam_->m_pos.m_stLastPoseWJ.getData();
            l_tDiff = l_TlockT.toc();
            if (l_tDiff > 5.0)
                LOGFAE(WWARN,
                       "{} 等待数据耗时 帧[{}] T:{:.3}",
                       WJLog::getWholeSysTime(),
                       l_sPoseWithTwist.m_iScanId,
                       l_tDiff);
        }
    }

    // 定位状态
    switch (l_sPoseWithTwist.m_bFlag)
    {
        case PoseStatus::InitialPose: l_stPoseSend.flagWj = WJPoseStatus::InitialWJ; break;
        case PoseStatus::ContinuePose: l_stPoseSend.flagWj = WJPoseStatus::ContinueWJ; break;
        case PoseStatus::VirtualPose: l_stPoseSend.flagWj = WJPoseStatus::VirtualWJ; break;
        case PoseStatus::CurbPose: l_stPoseSend.flagWj = WJPoseStatus::VirtualWJ; break;
        default:
            l_stPoseSend.flagWj = WJPoseStatus::InvalidWJ;
            l_sPoseWithTwist.m_Pose.m_fPercent = -1;
            break;
    }

    // l_sPoseWithTwist.m_Pose.printf("sick pose");
    // l_sPoseWithTwist.m_Twist.printf("sick vel");

    // 时间对齐
    float l_fTdiff = outPoseTimeAlign(l_stPoseSend, l_sPoseWithTwist, p_iPoseModel);

    // PoseStatus::StopPose 模式下清空位姿
    if (l_stPoseSend.flagWj == WJPoseStatus::InvalidWJ)
        l_stPoseSend.t = Eigen::Vector3d::Zero();

    if (p_bWJ)
        sendPoseInfoByWJ_(l_stPoseSend, p_iPoseModel, p_iWorkState);
    else
        sendPoseInfoByWJ712_(l_stPoseSend, p_iPoseModel, p_iWorkState);

    if (c_stSysParam_->m_fae.m_bPrintfLog)
    {
        LOGPATH(
            WINFO,
            "{} [AGV位姿] 状态: {} 时间戳: {} 雷达时间戳: {} 补偿值: {:.3} 位姿xyzrpy: {} {} {} {} "
            "{} {} "
            "速度xyzrpy: {} {} {} {} {} {} 时间差: 0 帧: [{}] 拷贝数据 {} ",
            WJLog::getWholeSysTime(),
            l_stPoseSend.getRobotPoseStatus(true),
            l_stPoseSend.wallTime,
            l_sPoseWithTwist.m_tsWallTime,
            l_fTdiff,
            (int)(l_stPoseSend.t[0] * 1000),
            (int)(l_stPoseSend.t[1] * 1000),
            0,
            0,
            0,
            (int)(l_stPoseSend.t[2] * 1000),
            (int)(l_sPoseWithTwist.m_Twist.x() * 1000),
            (int)(l_sPoseWithTwist.m_Twist.y() * 1000),
            (int)(l_sPoseWithTwist.m_Twist.z() * 1000),
            (int)(l_sPoseWithTwist.m_Twist.roll() * 1000),
            (int)(l_sPoseWithTwist.m_Twist.pitch() * 1000),
            (int)(l_sPoseWithTwist.m_Twist.yaw() * 1000),
            l_sPoseWithTwist.m_iScanId,
            (int)(l_tDiff * 1000));
    }
}

u_char* WJProtocol::getWJCMD(int& p_iOffset, std::mutex& p_lock)
{
    int l_iSta = -1, l_iEnd = -1;
    int l_iCmdLen = 0;
    l_iCmdLen = TOUINT16(c_stNetMsg_.m_aucBuf[(p_iOffset + 2) % NET_LENGTH_MAX],
                         c_stNetMsg_.m_aucBuf[(p_iOffset + 3) % NET_LENGTH_MAX]);
    if (c_stNetMsg_.m_aucBuf[p_iOffset] == 0xFF
        && c_stNetMsg_.m_aucBuf[(p_iOffset + 1) % NET_LENGTH_MAX] == 0xAA
        && c_stNetMsg_.m_aucBuf[(p_iOffset + l_iCmdLen + 2) % NET_LENGTH_MAX] == 0xEE
        && c_stNetMsg_.m_aucBuf[(p_iOffset + l_iCmdLen + 3) % NET_LENGTH_MAX] == 0xEE)
    {
        // 区别于wj/sick 这里须加入帧头帧尾
        l_iSta = p_iOffset % NET_LENGTH_MAX;
        l_iEnd = (l_iSta + l_iCmdLen + 4) % NET_LENGTH_MAX;
    }

    if (l_iSta != -1 && l_iEnd != -1)
    {
        int l_iDataProcLen = l_iCmdLen + 4;

        u_char* l_pcBufTmp = new u_char[l_iDataProcLen];
        if (l_iSta > l_iEnd)
        {
            memcpy(l_pcBufTmp, &c_stNetMsg_.m_aucBuf[l_iSta], NET_LENGTH_MAX - l_iSta);
            if (l_iEnd > 0)
                memcpy(&l_pcBufTmp[NET_LENGTH_MAX - l_iSta], c_stNetMsg_.m_aucBuf, l_iEnd);
        }
        else
            memcpy(l_pcBufTmp, &c_stNetMsg_.m_aucBuf[l_iSta], l_iDataProcLen);

        {
            std::lock_guard<std::mutex> l_mtx(p_lock);
            if (p_iOffset == (int)c_stNetMsg_.m_uiDataLen)
            {
                delete[] l_pcBufTmp;
                return NULL;
            }
            p_iOffset += l_iDataProcLen;
            p_iOffset %= NET_LENGTH_MAX;
        }

        if (l_pcBufTmp[l_iDataProcLen - 3] == checkXOR(&l_pcBufTmp[2], l_iDataProcLen - 6))  //
        {
            return l_pcBufTmp;
        }
        else
        {
            delete[] l_pcBufTmp;
            return NULL;
        }
    }

    return NULL;
}

void WJProtocol::calcWJProtocolHead_(u_char* p_pcBufCMD, char* p_pcBufResponse, int& p_iLen)
{
    p_iLen = WJ_HEADER_LEN;
    memcpy(&p_pcBufResponse[0], p_pcBufCMD, p_iLen);
    p_pcBufResponse[11] = 2;
}

void WJProtocol::calcWJProtocolTail_(char* p_pcBuf, int& p_iLen)
{
    p_pcBuf[2] = (p_iLen & 0xff00) >> 8;
    p_pcBuf[3] = p_iLen & 0xff;
    p_pcBuf[p_iLen++] = 0;
    p_pcBuf[p_iLen++] = checkXOR((u_char*)&p_pcBuf[2], p_iLen - 3);
    p_pcBuf[p_iLen++] = 0xEE;
    p_pcBuf[p_iLen++] = 0xEE;
}

int WJProtocol::selectWJProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse)
{
    int l_iSta = 11;
    int l_iSubscript = 26;
    int l_iFatherCMDID;
    int l_iSonCMDID;
    int l_iLen = 0;
    int x, y, z;
    bool res = false;
    int curStatus = -1;
    int l_iWorkMode = c_stSysParam_->m_iWorkMode;
    std::vector<std::string> l_vsMapName;

    if (p_pcBufCMD[l_iSta] == 0x01 && p_pcBufCMD[l_iSta + 1] == 0x01
        && p_pcBufCMD[l_iSta + 2] == 0x0B)
    {
        l_iFatherCMDID = p_pcBufCMD[l_iSta + 11];
        // 选择协议种类
        switch (l_iFatherCMDID)
        {
            case WJCMDTYPE::CMD_SLAVER:
                return selectSlaverProtocol(p_pcBufCMD, p_pcBufResponse);
                break;
            case WJCMDTYPE::CMD_DEVICE:
                return selectDeviceProtocol(p_pcBufCMD, p_pcBufResponse);
                break;
            case WJCMDTYPE::CMD_PRVT:
                return selectPrivateProtocol(p_pcBufCMD, p_pcBufResponse);
                break;
            default: return 0; break;
        }

        l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
        //拷贝数组0-25 l_iLen从26开始
        calcWJProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iLen);
        switch (l_iSonCMDID)
        {
            default: return 0; break;
        }
    }
    // 712协议
    if (p_pcBufCMD[l_iSta] == 0x01 && p_pcBufCMD[l_iSta + 1] == 0x00
        && p_pcBufCMD[l_iSta + 2] == 0x07 && p_pcBufCMD[l_iSta + 11] == 0x06
        && p_pcBufCMD[l_iSta + 12] == 0x08)
    {
        // 等待计算完成回复
        if (p_pcBufCMD[l_iSta + 15] == 1)
        {
            // 712应答回复帧采用和WJ协议同一个处理，仅帧头不同
            c_bIs712_ = true;
            sendPoseACKCmdByWJ_(p_pcBufCMD, WJ_HEADER_LEN, c_bHasRecvFlag_, l_iWorkMode);
            if (!c_bHasRecvFlag_)
                c_bHasRecvFlag_ = true;
        }
        // 立即回复
        else if (p_pcBufCMD[l_iSta + 15] == 0)
        {
            // 712协议Pose-0模式直接回复位姿
            sendPose_(0, l_iWorkMode, false);
        }
        return 0;
    }
    LOGFAE(WERROR,
           "无效协议 | {:#X} - {:#X},请联系万集开发人员!",
           p_pcBufCMD[l_iSta + 11],
           p_pcBufCMD[l_iSta + 12]);
    // c_stSysParam_->m_fae.setErrorCode("I1");
    return 0;
}

void WJProtocol::sendRecvSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes)
{
    // 多扩10个字节 + 27 28 29 | 校验2 帧尾2 +末尾\0
    char* l_acBuf = new char[p_iLen + 10];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);

    int l_iLen = p_iLen;
    l_acBuf[l_iLen++] = p_iRes;
    fillAskField(l_acBuf, l_iLen);
    calcWJProtocolTail_(l_acBuf, l_iLen);
    sendCallback_(l_acBuf, l_iLen);
    // std::cout << "hsq: WJProtocol::sendRecvSuccCMD_ finished" << std::endl;
    // 释放内存
    delete[] l_acBuf;
}

void WJProtocol::saveMap_(char* p_pcBufResponse, int p_iLen)
{
    char* l_acBuf = new char[p_iLen];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);
    sendRecvSuccCMD_(l_acBuf, p_iLen, setWorkModeCb_(1));
    c_bSaveMapFlag_ = false;
    // std::cout << "hsq: WJProtocol::saveMap_ finished" << std::endl;
    // 释放内存
    delete[] l_acBuf;
}

void WJProtocol::setWorkMode_(int p_newMode, char* p_pcBufResponse, int p_iLen)
{
    char* l_acBuf = new char[p_iLen];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);
    int curStatus = c_stSysParam_->m_iWorkMode;
    c_stSysParam_->m_iWorkMode = p_newMode;
    int res = setWorkModeCb_(0);
    if (!res)
    {
        c_stSysParam_->m_iWorkMode = curStatus;
        setWorkModeCb_(0);
    }
    sendRecvSuccCMD_(l_acBuf, p_iLen, res);
    c_bSetWorkModeFlag_ = false;

    // 释放内存
    delete[] l_acBuf;
}

int WJProtocol::selectSlaverProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse)
{
    int l_iSta = 11;
    int l_iSubscript = 26;
    int l_iFatherCMDID;
    int l_iSonCMDID;
    int l_iLidarId;
    int l_iLen = 0;
    int x, y, z;
    bool res = false;
    int curStatus = -1;
    l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    int l_iWorkMode = c_stSysParam_->m_iWorkMode;
    std::vector<std::string> l_vsMapName;
    //拷贝数组0-25 l_iLen从26开始
    calcWJProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iLen);
    switch (l_iSonCMDID)
    {
        case SLACMDID::SETCMD_WORKMODE:
        {
            std::cout << "SETCMD_WORKMODE" << std::endl;
            p_pcBufResponse[l_iLen++] = p_pcBufCMD[l_iSubscript];
            sendRecvSuccCMD_(p_pcBufResponse, l_iLen, 2);
            if (!c_bSetWorkModeFlag_)
            {
                c_bSetWorkModeFlag_ = true;
                std::thread l_setworkmode = std::thread(&WJProtocol::setWorkMode_,
                                                        this,
                                                        p_pcBufCMD[l_iSubscript],
                                                        p_pcBufResponse,
                                                        l_iLen);
                l_setworkmode.detach();
            }
            return 0;
        }
        case SLACMDID::SETCMD_VIEWLIDAR:
        {
            std::cout << "SETCMD_VIEWLIDAR" << std::endl;
            c_stSysParam_->m_bSendCurPC = p_pcBufCMD[l_iSubscript];
            p_pcBufResponse[l_iLen++] = p_pcBufCMD[l_iSubscript];
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case SLACMDID::SETCMD_VIEWMAP:
        {
            std::cout << "SETCMD_VIEWMAP" << std::endl;
            c_stSysParam_->m_bSendMap = p_pcBufCMD[l_iSubscript];
            // 刷新运行可视化标志
            setWorkModeCb_(2);
            p_pcBufResponse[l_iLen++] = p_pcBufCMD[l_iSubscript];
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case SLACMDID::SETCMD_SLAMMAPINFO:
        {
            std::cout << "SETCMD_SLAMMAPINFO" << std::endl;
            int l_iData = 0;
            std::string l_sStr;
            if (asciiToString_(p_pcBufCMD, l_iSubscript, l_sStr)
                && bytes16ToInt(p_pcBufCMD, l_iSubscript, l_iData))
            {
                c_stSysParam_->m_map.m_sMapName = l_sStr;
                c_stSysParam_->m_map.m_fMap_grid = l_iData / 1000.0;
                p_pcBufResponse[l_iLen++] = 1;
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case SLACMDID::SETCMD_SAVEMAP:
        {
            std::cout << "SLACMDID::SETCMD_SAVEMAP" << std::endl;
            sendRecvSuccCMD_(p_pcBufResponse, l_iLen, 2);
            if (c_stSysParam_->m_iWorkMode != WorkMode::InitMapMode
                && c_stSysParam_->m_iWorkMode != WorkMode::ContMapMode
                && c_stSysParam_->m_iWorkMode != WorkMode::UpdateMapMode)
            {
                p_pcBufResponse[l_iLen++] = 0;
                break;
            }
            if (!c_bSaveMapFlag_)
            {
                c_bSaveMapFlag_ = true;
                std::thread l_saveMap =
                    std::thread(&WJProtocol::saveMap_, this, p_pcBufResponse, l_iLen);
                l_saveMap.detach();
            }
            return 0;
        }
        case SLACMDID::SETCMD_WHOLEPOSE:
        {
            std::cout << "SETCMD_WHOLEPOSE" << std::endl;
            float l_fPoseX, l_fPoseY, l_fPoseZ, l_fPoseR, l_fPoseP, l_fPoseA;
            int l_iIsLidarPose = p_pcBufCMD[l_iSubscript++];
            if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseX)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseY)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseZ)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseR)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseP)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseA))
            {
                c_stSysParam_->m_pos.m_stSetPose.setX(l_fPoseX);
                c_stSysParam_->m_pos.m_stSetPose.setY(l_fPoseY);
                c_stSysParam_->m_pos.m_stSetPose.setZ(l_fPoseZ);
                c_stSysParam_->m_pos.m_stSetPose.setRPY(l_fPoseR, l_fPoseP, l_fPoseA);
                // c_stSysParam_->m_pos.m_stSetPose.printf("setAGVPose");
                // AGV位姿转移到雷达位姿
                if (l_iIsLidarPose & 0x01 == 1)
                    c_stSysParam_->m_pos.m_stSetPose = c_stSysParam_->m_agv.m_stTrans.inverse()
                                                       * c_stSysParam_->m_pos.m_stSetPose
                                                       * c_stSysParam_->m_agv.m_stLidarToAgv;
                c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
                c_stSysParam_->m_pos.m_bReWirteByPathZ = true;
                std::string l_sPrintStr = "setLidarPose " + std::to_string(l_iIsLidarPose);
                // c_stSysParam_->m_pos.m_stSetPose.printf(l_sPrintStr);

                // 设定位姿
                if ((l_iIsLidarPose >> 4) & 0x01 != 1)
                    res = setWorkModeCb_(10);
            }
            else
                res = 0;
            p_pcBufResponse[l_iLen++] = res;
            break;
        }
        case SLACMDID::SETCMD_CURRPOSE:
        {
            std::cout << "SETCMD_CURRPOSE" << std::endl;
            float l_fPoseX, l_fPoseY, l_fPoseZ, l_fPoseA;
            int l_iIsLidarPose = p_pcBufCMD[l_iSubscript++];
            if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseX)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseY)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseZ)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseA))
            {
                c_stSysParam_->m_pos.m_stSetPose.setX(l_fPoseX);
                c_stSysParam_->m_pos.m_stSetPose.setY(l_fPoseY);
                c_stSysParam_->m_pos.m_stSetPose.setZ(l_fPoseZ);
                c_stSysParam_->m_pos.m_stSetPose.setRPY(0, 0, l_fPoseA);
                // c_stSysParam_->m_pos.m_stSetPose.printf("setAGVPose");
                // AGV位姿转移到雷达位姿
                if (!l_iIsLidarPose)
                    c_stSysParam_->m_pos.m_stSetPose = c_stSysParam_->m_agv.m_stTrans.inverse()
                                                       * c_stSysParam_->m_pos.m_stSetPose
                                                       * c_stSysParam_->m_agv.m_stLidarToAgv;
                c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
                std::string l_sPrintStr = "setLidarPose " + std::to_string(l_iIsLidarPose);
                // c_stSysParam_->m_pos.m_stSetPose.printf(l_sPrintStr);

                // 设定位姿
                res = setWorkModeCb_(10);
            }
            else
                res = 0;
            p_pcBufResponse[l_iLen++] = res;
            break;
        }
        case SLACMDID::SETCMD_MAPCORRECT:
        {
            //地图校正
            std::cout << "SETCMD_MAPCORRECT" << std::endl;
            if (setMapCorrect_(p_pcBufCMD, l_iSubscript))
                p_pcBufResponse[l_iLen++] = 1;
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case SLACMDID::SETCMD_CARCORRECT:
        {
            //车体校正
            std::cout << "SETCMD_CARCORRECT" << std::endl;
            float l_fPoseX, l_fPoseY, l_fPoseZ, l_fPoseA;
            if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseX)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseY)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseZ)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseA))
            {
                c_stSysParam_->m_agv.m_stLidarToAgv.setRPY(0.0, 0.0, l_fPoseA);
                c_stSysParam_->m_agv.m_stLidarToAgv.setX(l_fPoseX);
                c_stSysParam_->m_agv.m_stLidarToAgv.setY(l_fPoseY);
                c_stSysParam_->m_agv.m_stLidarToAgv.setZ(l_fPoseZ);
                LOGFAE(WINFO,
                       "设置车体校正: [{:.3f},{:.3f},{:.1f}] | {:.3f}.",
                       c_stSysParam_->m_agv.m_stLidarToAgv.x(),
                       c_stSysParam_->m_agv.m_stLidarToAgv.y(),
                       c_stSysParam_->m_agv.m_stLidarToAgv.z(),
                       c_stSysParam_->m_agv.m_stLidarToAgv.yaw());
                p_pcBufResponse[l_iLen++] = 1;
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case SLACMDID::SETCMD_VIEWMODE:
        {
            //可视化点云模式
            std::cout << "SETCMD_VIEWMODE" << std::endl;
            //可视化点云模式
            c_stSysParam_->m_iViewMode = p_pcBufCMD[l_iSubscript];
            res = setWorkModeCb_(2);
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_iViewMode;
            break;
        }
        case SLACMDID::SETCMD_MAPPINGMODE:
        {
            std::cout << "SETCMD_MAPPINGMODE" << std::endl;
            c_stSysParam_->m_nParam_mode = p_pcBufCMD[l_iSubscript];
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case SLACMDID::SETCMD_MOTIONMODE:
        {
            std::cout << "SETCMD_MOTIONMODE" << std::endl;
            c_stSysParam_->m_odom.optimiz_o3D = bool(int(p_pcBufCMD[l_iSubscript]));
            c_stSysParam_->m_loct.optimiz_o3D = bool(int(p_pcBufCMD[l_iSubscript]));
            c_stSysParam_->m_slam.optimiz_o3D = bool(int(p_pcBufCMD[l_iSubscript]));
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case SLACMDID::QUERYCMD_WORKMODE:
        {
            std::cout << "QUERYCMD_WORKMODE" << std::endl;
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_iWorkMode;
            break;
        }
        case SLACMDID::QUERYCMD_VIEWLIDAR:
        {
            std::cout << "QUERYCMD_VIEWLIDAR" << std::endl;
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_bSendCurPC;
            break;
        }
        case SLACMDID::QUERYCMD_VIEWMAP:
        {
            std::cout << "QUERYCMD_VIEWMAP" << std::endl;
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_bSendMap;
            break;
        }
        case SLACMDID::QUERYCMD_SLAMMAPINFO:
        {
            std::cout << "QUERYCMD_SLAMMAPINFO" << std::endl;
            res = isExistFileOrFolder(c_stSysParam_->m_sPkgPath + "/data/Map/"
                                      + c_stSysParam_->m_map.m_sMapName);
            p_pcBufResponse[l_iLen++] = res;  //存在地图
            if (res)
            {
                stringToAscii_(c_stSysParam_->m_map.m_sMapName, p_pcBufResponse, l_iLen);
                fillIntTo16Bytes(
                    (int)(c_stSysParam_->m_map.m_fMap_grid * 1000), p_pcBufResponse, l_iLen);
            }
            break;
        }
        case SLACMDID::QUERYCMD_CURRLASERPOSE:
        {
            std::cout << "QUERYCMD_CURRLASERPOSE" << std::endl;
            // 等待计算完成回复
            if (p_pcBufCMD[l_iSubscript] == 1)
            {
                c_bIs712_ = false;
                sendPoseACKCmdByWJ_(p_pcBufCMD, WJ_HEADER_LEN, c_bHasRecvFlag_, l_iWorkMode);
                if (!c_bHasRecvFlag_)
                    c_bHasRecvFlag_ = true;
            }
            // 立即回复
            else if (p_pcBufCMD[l_iSubscript] == 0)
            {
                // WJ协议Pose-0模式直接回复位姿
                sendPose_(0, l_iWorkMode);
            }
            break;
        }
        case SLACMDID::QUERYCMD_MAPCORRECT:
        {
            std::cout << "QUERYCMD_MAPCORRECT" << std::endl;
            for (int l_iOffset = 0; l_iOffset < 3; ++l_iOffset)
            {
                fillFloatTo32Bytes(
                    c_stSysParam_->m_agv.m_fMymap[l_iOffset], p_pcBufResponse, l_iLen);
            }

            for (int l_iOffset = 0; l_iOffset < 3; ++l_iOffset)
            {
                fillFloatTo32Bytes(
                    c_stSysParam_->m_agv.m_fTomap[l_iOffset], p_pcBufResponse, l_iLen);
            }
            break;
        }
        case SLACMDID::QUERYCMD_CARCORRECT:
        {
            std::cout << "QUERYCMD_CARCORRECT" << std::endl;
            //测试，暂时未使用
            std::cout << "QUERYCMD_AGVCORRECT" << std::endl;
            for (int l_iOffset = 0; l_iOffset < 3; ++l_iOffset)
                fillFloatTo32Bytes(c_stSysParam_->m_agv.m_stLidarToAgv.m_trans[l_iOffset],
                                   p_pcBufResponse,
                                   l_iLen);
            fillFloatTo32Bytes(c_stSysParam_->m_agv.m_stLidarToAgv.yaw(), p_pcBufResponse, l_iLen);
            break;
        }
        case SLACMDID::QUERYCMD_CURRTIMESTAMP:
        {
            std::cout << "QUERYCMD_CURRTIMESTAMP" << std::endl;
            p_pcBufResponse[l_iLen++] = 0; /*code*/  //时间戳
            p_pcBufResponse[l_iLen++] = 0; /*code*/  //时间戳
            p_pcBufResponse[l_iLen++] = 0; /*code*/  //时间戳
            p_pcBufResponse[l_iLen++] = 0; /*code*/  //时间戳
            break;
        }
        case SLACMDID::QUERYCMD_PROCVERSION:
        {
            std::cout << "QUERYCMD_PROCVERSION" << std::endl;
            stringToAscii_(c_stSysParam_->m_sVersion, p_pcBufResponse, l_iLen);
            break;
        }
        case SLACMDID::QUERYCMD_CURRAGVPOSE:
        {
            std::cout << "QUERYCMD_CURRAGVPOSE" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case SLACMDID::QUERYCMD_VIEWMODE:
        {
            std::cout << "QUERYCMD_VIEWMODE" << std::endl;
            // 返回可视化点云模式 默认0 -3D
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_iViewMode;
            break;
        }
        case SLACMDID::QUERYCMD_MAPPINGMODE:
        {
            std::cout << "QUERYCMD_MAPPINGMODE: " << std::endl;
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_nParam_mode;
            break;
        }
        case SLACMDID::QUERYCMD_MOTIONMODE:
        {
            std::cout << "QUERYCMD_MOTIONMODE: " << std::endl;
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_odom.optimiz_o3D;
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_loct.optimiz_o3D;
            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_slam.optimiz_o3D;
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        default:
        {
            LOGFAE(WERROR,
                   "SLAM协议异常: 未定义 {:#X} - {:#X}, 请联系万集开发人员!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            // c_stSysParam_->m_fae.setErrorCode("I2");
            break;
        }
    }
    if (l_iLen > 26)
    {
        fillAskField(p_pcBufResponse, l_iLen);
        calcWJProtocolTail_(p_pcBufResponse, l_iLen);
        return l_iLen;
    }
    return 0;
}

int WJProtocol::selectDeviceProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse)
{
    // 注意：涉及查询协议中需要名字的 eg：查询雷达/网卡等  回复格式为xxx + 名称长度 + 名称 +
    // 是否查询成功 + xxx
    int l_iSta = 11;
    int l_iSubscript = 26;
    int l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    int l_iLen = 0;
    int x, y, z;
    bool res = false;
    int curStatus = -1;
    std::vector<std::string> l_vsMapName;
    s_CalibConfig& l_stCalib = c_stSysParam_->m_calib;
    int l_iLidarSize = c_stSysParam_->m_iLidarNum;
    int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
    calcWJProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iLen);
    if (l_iLidarId == -1)
    {
        // 雷达名解析后与配置不符，拷贝协议带的雷达名称
        memcpy(&p_pcBufResponse[l_iLen], &p_pcBufCMD[l_iSubscript], p_pcBufCMD[l_iSubscript] + 1);
        l_iLen += p_pcBufCMD[l_iSubscript] + 1;
        // 返回失败信号
        p_pcBufResponse[l_iLen++] = 0;
        calcWJProtocolTail_(p_pcBufResponse, l_iLen);
        return l_iLen;
    }
    s_LidarConfig& l_stLidar = c_stSysParam_->m_lidar[l_iLidarId];
    // 写入雷达名称Len+Str
    stringToAscii_(l_stLidar.m_sLaserName, p_pcBufResponse, l_iLen);
    switch (l_iSonCMDID)
    {
        case DEVCMDID::SETCMD_LASERINSTALLXYZ:
        {
            std::cout << "SETCMD_LASERINSTALLXYZ" << std::endl;
            // 安装信息组
            int l_iPart = p_pcBufCMD[l_iSubscript++];
            float l_fX, l_fY, l_fZ, l_fA;
            if ((l_iPart == 0 || l_iPart == 2) && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fX)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fY)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fZ))
            {
                if (l_iPart == 0)
                {
                    l_stLidar.m_transToBase.setX(l_fX);
                    l_stLidar.m_transToBase.setY(l_fY);
                    l_stLidar.m_transToBase.setZ(l_fZ);
                }
                else if (l_iPart == 2)
                {
                    l_stLidar.m_fCalibration[0] = l_fX;
                    l_stLidar.m_fCalibration[1] = l_fY;
                    l_stLidar.m_fCalibration[2] = l_fZ;
                }
                res = setWorkModeCb_(6);
                p_pcBufResponse[l_iLen++] = res;
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case DEVCMDID::SETCMD_LASERINSTALLANGLE:
        {
            std::cout << "SETCMD_LASERINSTALLANGLE" << std::endl;
            // 安装信息组
            int l_iPart = p_pcBufCMD[l_iSubscript++];
            double rpy[3] = {0, 0, 0};
            if ((l_iPart == 0 || l_iPart == 1 || l_iPart == 2)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, rpy[0])
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, rpy[1])
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, rpy[2]))
            {
                if (l_iPart == 0)
                    l_stLidar.m_transToBase.setRPY(rpy[0], rpy[1], rpy[2]);
                else if (l_iPart == 1)
                {
                    l_stLidar.m_fDipAngle[0] = rpy[0];
                    l_stLidar.m_fDipAngle[1] = rpy[1];
                }
                else if (l_iPart == 2)
                {
                    l_stLidar.m_fCalibration[3] = rpy[0];
                    l_stLidar.m_fCalibration[4] = rpy[1];
                    l_stLidar.m_fCalibration[5] = rpy[2];
                }
                res = setWorkModeCb_(6);
                p_pcBufResponse[l_iLen++] = res;
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case DEVCMDID::SETCMD_LASERDISBLIND:
        {
            std::cout << "SETCMD_LASERDISBLIND" << std::endl;
            float l_fMinDist, l_fMaxDist, l_fHeight;
            if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fMinDist)
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fMaxDist))
            {
                l_stLidar.m_fMinDist = l_fMinDist;
                l_stLidar.m_fMaxDist = l_fMaxDist;
                p_pcBufResponse[l_iLen++] = 1;
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case DEVCMDID::SETCMD_LASERANGBLIND:
        {
            std::cout << "SETCMD_LASERANGBLIND" << std::endl;
            //设置角度盲区
            float l_fStartAng, l_fEndAng;
            std::vector<wj_slam::s_BlindConfig> l_vstBlindInfo;
            wj_slam::s_BlindConfig l_vBlindAngs;
            for (int l_uiSize = 0; l_uiSize < 4; ++l_uiSize)
            {
                if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fStartAng)
                    && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fEndAng))
                {
                    l_vBlindAngs.m_fStartAng = l_fStartAng;
                    l_vBlindAngs.m_fEndAng = l_fEndAng;
                    // 起始==终止 等效与清空该盲区
                    if (l_vBlindAngs.m_fStartAng <= l_vBlindAngs.m_fEndAng)
                        l_vstBlindInfo.push_back(l_vBlindAngs);
                }
            }

            if (l_vstBlindInfo.size())
            {
                // l_vstBlindInfo 若存在起始==终止角 则删除
                // for (auto iter = l_vstBlindInfo.begin(); iter != l_vstBlindInfo.end();)
                // {
                //     if ((*iter).m_fStartAng == (*iter).m_fEndAng)
                //         iter = l_vstBlindInfo.erase(iter);
                //     else
                //         iter++;
                // }
                // for (auto iter : l_vstBlindInfo)
                //     printf("use Blind %f | %f\n", iter.m_fStartAng, iter.m_fEndAng);
                // 不论l_vstBlindInfo是否为空 都交换，用于清空该雷达盲区
                l_stLidar.m_vBlindSector.swap(l_vstBlindInfo);
                setWorkModeCb_(6);
            }
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case DEVCMDID::SETCMD_USEFLOOR:
        {
            std::cout << "SETCMD_USEFLOOR" << std::endl;
            l_stLidar.m_bUseFloor = (int)p_pcBufCMD[l_iSubscript++];
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case DEVCMDID::SETCMD_LASERHIGHT:
        {
            std::cout << "SETCMD_LASERHIGHT" << std::endl;
            float l_fHeight;
            if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fHeight))
            {
                l_stLidar.m_fFeatureHeight = l_fHeight;
                p_pcBufResponse[l_iLen++] = 1;
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case DEVCMDID::SETCMD_MARKMODE:
        {
            std::cout << "SETCMD_MARKMODE" << std::endl;
            int l_iUseMark = (int)p_pcBufCMD[l_iSubscript++];
            float l_fMarkSize;
            if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fMarkSize))
            {
                c_stSysParam_->m_bIsUseMark = l_iUseMark;
                c_stSysParam_->m_fMarkSize = l_fMarkSize;
                p_pcBufResponse[l_iLen++] = 1;
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case DEVCMDID::SETCMD_HORIZONTALCALIBRATION:
        {
            std::cout << "SETCMD_HORIZONTALCALIBRATION" << std::endl;
            l_stCalib.m_HACalib.m_iWorkLidarID = l_iLidarId;
            if (c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::StandByMode)
            {
                // 设置启动 且未启动
                if (p_pcBufCMD[l_iSubscript] && !c_stSysParam_->m_calib.m_HACalib.m_bIsStartCalib)
                    res = setWorkModeCb_(3);
                else if (p_pcBufCMD[l_iSubscript]
                         && c_stSysParam_->m_calib.m_HACalib.m_bIsStartCalib)
                    res = 1;
                // 设置关闭 且处于启动
                else if (!p_pcBufCMD[l_iSubscript]
                         && c_stSysParam_->m_calib.m_HACalib.m_bIsStartCalib)
                    res = setWorkModeCb_(8);
                else
                    res = 1;
            }
            // 传输设置动作：启动/关闭
            p_pcBufResponse[l_iLen++] = p_pcBufCMD[l_iSubscript];
            // 传输动作状态：成功/失败
            p_pcBufResponse[l_iLen++] = res;
            break;
        }
        case DEVCMDID::SETCMD_MULLASERCALIBRATION:
        {
            std::cout << "SETCMD_MULLASERCALIBRATION" << std::endl;
            // 标定前提：定位模式+首个雷达为基准雷达+当前雷达为非基准雷达+全部初始化完成
            if (c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::LocatMode
                && c_stSysParam_->m_lidar[0].m_bIsBaseLaser)
            {
                // 设置启动 且未启动
                if (p_pcBufCMD[l_iSubscript] && !c_stSysParam_->m_calib.m_MLCalib.m_bIsStartCalib)
                {
                    // 初始化
                    if ((int)l_stCalib.m_MLCalib.m_data.size() != l_iLidarSize)
                    {
                        l_stCalib.m_MLCalib.resetPoses(l_iLidarSize);
                        // debug-printf
                        std::cout << "init calib data" << std::endl;
                    }
                    res = setWorkModeCb_(4);
                }
                else if (p_pcBufCMD[l_iSubscript]
                         && c_stSysParam_->m_calib.m_MLCalib.m_bIsStartCalib)
                    res = 1;
                // 设置关闭 且处于启动
                else if (!p_pcBufCMD[l_iSubscript]
                         && c_stSysParam_->m_calib.m_MLCalib.m_bIsStartCalib)
                    res = setWorkModeCb_(7);
                else
                    res = 1;
            }
            // 传输设置动作：启动/关闭
            p_pcBufResponse[l_iLen++] = p_pcBufCMD[l_iSubscript];
            // 传输动作状态：成功/失败
            p_pcBufResponse[l_iLen++] = res;
            break;
        }
        case DEVCMDID::SETCMD_HORIZONTALCALIBNUM:
        {
            std::cout << "SETCMD_HORIZONTALCALIBNUM" << std::endl;
            l_stCalib.m_HACalib.m_iMinTimeInit = int(p_pcBufCMD[l_iSubscript++]);
            break;
        }
        case DEVCMDID::SETCMD_LASERENABLE:
        {
            std::cout << "SETCMD_LASERENABLE" << std::endl;
            l_stLidar.m_bEnable = bool(int(p_pcBufCMD[l_iSubscript++]));
            res = setWorkModeCb_(9);
            p_pcBufResponse[l_iLen++] = res;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERINSTALLXYZ:
        {
            std::cout << "QUERYCMD_LASERINSTALLXYZ" << (int)p_pcBufCMD[l_iSubscript] << std::endl;
            // 安装信息组
            int l_iPart = p_pcBufCMD[l_iSubscript++];

            //雷达安装位置XYZ
            if (l_iPart == 0)
            {
                p_pcBufResponse[l_iLen++] = 1;
                p_pcBufResponse[l_iLen++] = l_iPart;
                for (int i = 0; i < 3; ++i)
                    fillFloatTo32Bytes(l_stLidar.m_transToBase.m_trans[i], p_pcBufResponse, l_iLen);
            }
            else if (l_iPart == 2)
            {
                p_pcBufResponse[l_iLen++] = 1;
                p_pcBufResponse[l_iLen++] = l_iPart;
                for (int i = 0; i < 3; ++i)
                    fillFloatTo32Bytes(l_stLidar.m_fCalibration[i], p_pcBufResponse, l_iLen);
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case DEVCMDID::SETCMD_MULLASERCALIBRATIONINI:
        {
            std::cout << "SETCMD_MULLASERCALIBRATIONINI" << std::endl;
            // 初始化
            if ((int)l_stCalib.m_MLCalib.m_data.size() != l_iLidarSize)
            {
                l_stCalib.m_MLCalib.resetPoses(l_iLidarSize);
                // debug-printf
                std::cout << "init calib data" << std::endl;
            }
            // 此为标定前提:全部雷达已存在初始位姿
            Eigen::VectorXf l_fPose(Eigen::VectorXf::Zero(6));
            if (bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPose[0])
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPose[1])
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPose[2])
                && bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPose[5]))
            {
                l_stCalib.m_MLCalib.m_mtRequestSend->lock();
                // 如果是基准雷达
                if (l_stLidar.m_bIsBaseLaser)
                {
                    // 设定基准雷达初位置
                    l_stCalib.m_MLCalib.m_data[l_iLidarId].m_iScanID = 1;
                    l_stCalib.m_MLCalib.m_data[l_iLidarId].m_stCalibData = l_fPose.cast<double>();
                    l_stCalib.m_MLCalib.m_data[l_iLidarId].m_stStdDev = Eigen::VectorXd::Zero(6);
                }
                // 如果当前不是基准雷达且基准位置已设置
                else if (1 == l_stCalib.m_MLCalib.m_data[0].m_iScanID)
                {
                    // 计算相对位置
                    s_POSE6D l_OldQT, l_PoseBase, l_PoseThis, l_NewQt;
                    Eigen::VectorXd l_CalData = Eigen::Map<Eigen::VectorXd>(
                        c_stSysParam_->m_lidar[l_iLidarId].m_fCalibration.data(), 6);
                    l_OldQT.setVector6d(l_CalData);
                    l_PoseBase.setVector6d(l_stCalib.m_MLCalib.m_data[0].m_stCalibData);
                    Eigen::VectorXd l_fPosed = l_fPose.cast<double>();
                    l_PoseThis.setVector6d(l_fPosed);
                    l_NewQt = l_PoseBase.inverse() * l_PoseThis * l_OldQT;
                    // 新相对位置给到雷达外参储存
                    l_CalData = l_NewQt.coeffs_6();
                    c_stSysParam_->m_lidar[l_iLidarId].m_fCalibration =
                        std::vector<double>(&l_CalData[0], l_CalData.data() + 6);

                    // 位置还原到基准雷达位置
                    c_stSysParam_->m_pos.m_stSetPose = l_PoseBase;
                    c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
                    c_stSysParam_->m_pos.m_stSetPose.printf("trans to Base Pose");
                    // 下发标定参数 && 刷新定位
                    res = setWorkModeCb_(6);
                    res &= setWorkModeCb_(0);
                    if (res)
                    {
                        // 设定雷达初始标定状态为已粗标定
                        l_stCalib.m_MLCalib.m_data[l_iLidarId].m_iScanID = 1;
                        l_stCalib.m_MLCalib.m_data[l_iLidarId].m_stCalibData =
                            Eigen::VectorXd::Zero(6);
                        l_stCalib.m_MLCalib.m_data[l_iLidarId].m_stStdDev =
                            Eigen::VectorXd::Zero(6);
                    }
                    // debug-printf
                    std::cout << l_iLidarId << " : " << l_CalData << std::endl;
                }
                else
                    res = false;
                l_stCalib.m_MLCalib.m_mtRequestSend->unlock();
                res = l_stCalib.m_MLCalib.checkInitial(l_iLidarId);
            }
            else
                res = false;
            // 传输设置状态：单个成功/失败
            p_pcBufResponse[l_iLen++] = res;
            res = l_stCalib.m_MLCalib.checkInitial();
            // 传输设置状态：全部成功/失败
            p_pcBufResponse[l_iLen++] = res;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERINSTALLANGLE:
        {
            std::cout << "QUERYCMD_LASERINSTALLANGLE" << std::endl;
            // 安装信息组
            int l_iPart = p_pcBufCMD[l_iSubscript++];

            //雷达安装位置XYZ
            if (l_iPart == 0)
            {
                p_pcBufResponse[l_iLen++] = 1;
                p_pcBufResponse[l_iLen++] = l_iPart;
                //雷达安装位置Roll
                fillFloatTo32Bytes(l_stLidar.m_transToBase.roll(), p_pcBufResponse, l_iLen);
                //雷达安装位置Pitch
                fillFloatTo32Bytes(l_stLidar.m_transToBase.pitch(), p_pcBufResponse, l_iLen);
                //雷达安装位置Yaw]
                fillFloatTo32Bytes(l_stLidar.m_transToBase.yaw(), p_pcBufResponse, l_iLen);
            }
            else if (l_iPart == 1)
            {
                p_pcBufResponse[l_iLen++] = 1;
                p_pcBufResponse[l_iLen++] = l_iPart;
                fillFloatTo32Bytes(l_stLidar.m_fDipAngle[0], p_pcBufResponse, l_iLen);
                fillFloatTo32Bytes(l_stLidar.m_fDipAngle[1], p_pcBufResponse, l_iLen);
                fillFloatTo32Bytes(0, p_pcBufResponse, l_iLen);
            }
            else if (l_iPart == 2)
            {
                p_pcBufResponse[l_iLen++] = 1;
                p_pcBufResponse[l_iLen++] = l_iPart;
                for (int i = 3; i < 6; ++i)
                    fillFloatTo32Bytes(l_stLidar.m_fCalibration[i], p_pcBufResponse, l_iLen);
            }
            else
                p_pcBufResponse[l_iLen++] = 0;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERDISBLIND:
        {
            std::cout << "QUERYCMD_LASERDISBLIND" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            //最小距离盲区
            fillFloatTo32Bytes(l_stLidar.m_fMinDist, p_pcBufResponse, l_iLen);
            //最大距离盲区
            fillFloatTo32Bytes(l_stLidar.m_fMaxDist, p_pcBufResponse, l_iLen);
            break;
        }
        case DEVCMDID::QUERYCMD_LASERANGBLIND:
        {
            std::cout << "QUERYCMD_LASERANGBLIND" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            for (auto l_stSec : l_stLidar.m_vBlindSector)
            {
                fillFloatTo32Bytes(l_stSec.m_fStartAng, p_pcBufResponse, l_iLen);
                fillFloatTo32Bytes(l_stSec.m_fEndAng, p_pcBufResponse, l_iLen);
            }
            for (int i = l_stLidar.m_vBlindSector.size(); i < 4; ++i)
            {
                for (int j = 0; j < 8; ++j)
                    p_pcBufResponse[l_iLen++] = 0;
            }
            break;
        }
        case DEVCMDID::QUERYCMD_USEFLOOR:
        {
            std::cout << "QUERYCMD_USEFLOOR" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            p_pcBufResponse[l_iLen++] = l_stLidar.m_bUseFloor;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERHIGHT:
        {
            std::cout << "QUERYCMD_LASERHIGHT" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            //雷达高度
            fillFloatTo32Bytes(l_stLidar.m_fFeatureHeight, p_pcBufResponse, l_iLen);
            break;
        }
        case DEVCMDID::QUERYCMD_MARKMODE:
        {
            std::cout << "QUERYCMD_MARKMODE" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;

            p_pcBufResponse[l_iLen++] = c_stSysParam_->m_bIsUseMark;  //当前靶标功能
            p_pcBufResponse[l_iLen++] = 0;                            //靶标类型
            fillIntTo16Bytes((int)(c_stSysParam_->m_fMarkSize * 1000), p_pcBufResponse, l_iLen);
            break;
        }
        case DEVCMDID::QUERYCMD_HORIZONTALCALIBRATION:
        {
            std::cout << "QUERYCMD_HORIZONTALCALIBRATION" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            l_stCalib.m_HACalib.m_mtRequestSend->lock();
            // 校准状态
            p_pcBufResponse[l_iLen++] = l_stCalib.m_HACalib.m_data.m_iTimeStamp;
            //次数
            fillIntTo16Bytes((int)(l_stCalib.m_HACalib.m_data.m_iScanID), p_pcBufResponse, l_iLen);
            // 地面高度 & 残余角度
            for (int i = 0; i < 2; i++)
            {
                fillFloatTo32Bytes(
                    l_stCalib.m_HACalib.m_data.m_stStdDev[i], p_pcBufResponse, l_iLen);
            }
            for (int i = 3; i < 5; i++)
                fillFloatTo32Bytes(
                    l_stCalib.m_HACalib.m_data.m_stCalibData[i], p_pcBufResponse, l_iLen);
            l_stCalib.m_HACalib.m_mtRequestSend->unlock();
            break;
        }
        case DEVCMDID::QUERYCMD_MULLASERCALIBRATION:
        {
            std::cout << "QUERYCMD_MULLASERCALIBRATION" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            // 状态1:校准状态
            p_pcBufResponse[l_iLen++] = l_stCalib.m_MLCalib.m_bIsStartCalib;

            if (l_stCalib.m_MLCalib.m_bIsStartCalib)
            {
                l_stCalib.m_MLCalib.m_mtRequestSend->lock();
                // 状态2:校准次数
                fillIntTo16Bytes((int)(l_stCalib.m_MLCalib.m_data[l_iLidarId].m_iScanID),
                                 p_pcBufResponse,
                                 l_iLen);

                for (int i = 0; i < 6; i++)
                {
                    fillFloatTo32Bytes(l_stCalib.m_MLCalib.m_data[l_iLidarId].m_stCalibData[i],
                                       p_pcBufResponse,
                                       l_iLen);
                    fillFloatTo32Bytes(l_stCalib.m_MLCalib.m_data[l_iLidarId].m_stStdDev[i],
                                       p_pcBufResponse,
                                       l_iLen);
                }
                l_stCalib.m_MLCalib.m_mtRequestSend->unlock();
            }
            break;
        }
        case DEVCMDID::QUERYCMD_LIDARSTATUS:
        {
            std::cout << "QUERYCMD_LIDARSTATUS" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            p_pcBufResponse[l_iLen++] = (int)l_stLidar.m_dev.m_status;
            break;
        }
        case DEVCMDID::QUERYCMD_HORIZONTALCALIBNUM:
        {
            std::cout << "QUERYCMD_HORIZONTALCALIBNUM" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            p_pcBufResponse[l_iLen++] = (int)l_stCalib.m_HACalib.m_iMinTimeInit;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERENABLE:
        {
            std::cout << "QUERYCMD_LASERENABLE" << std::endl;
            p_pcBufResponse[l_iLen++] = 1;
            p_pcBufResponse[l_iLen++] = (int)l_stLidar.m_bEnable;
            break;
        }
        default:
        {
            LOGFAE(WERROR,
                   "DEV协议异常: 未定义 {:#X} - {:#X}, 请联系万集开发人员!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            // c_stSysParam_->m_fae.setErrorCode("I3");
            break;
        }
    }
    if (l_iLen > 26)
    {
        fillAskField(p_pcBufResponse, l_iLen);
        calcWJProtocolTail_(p_pcBufResponse, l_iLen);
        return l_iLen;
    }
    return 0;
}

int WJProtocol::selectPrivateProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse)
{
    int l_iSta = 11;
    int l_iFatherCMDID;
    int l_iSonCMDID;
    int l_iLidarId;
    int l_iLen = 0;
    int x, y, z;
    bool res = false;
    int curStatus = -1;
    int l_iSubscript = 26;
    l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    //拷贝数组0-25 l_iLen从26开始
    calcWJProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iLen);
    switch (l_iSonCMDID)
    {
        case PRVTCMDID::SETCMD_DEBUGMODE:
        {
            std::cout << "SETCMD_DEBUGMODE: " << std::endl;
            c_stSysParam_->m_bDebugModel = (int)p_pcBufCMD[l_iSubscript];
            p_pcBufResponse[l_iLen++] = 1;
            break;
        }
        case PRVTCMDID::SETCMD_RESETENABLELASER:
        {
            std::cout << "SETCMD_RESETENABLELASER: " << std::endl;
            res = setWorkModeCb_(9);
            p_pcBufResponse[l_iLen++] = res;
            break;
        }
        case PRVTCMDID::QUERYCMD_DEBUGMODE:
        {
            std::cout << "QUERYCMD_DEBUGMODE: " << std::endl;
            p_pcBufResponse[l_iLen++] = (int)(c_stSysParam_->m_bDebugModel);
            break;
        }
        case PRVTCMDID::QUERYCMD_SLAMSTATUS:
        {
            std::cout << "QUERYCMD_SLAMSTATUS: " << (int)c_stSysParam_->m_RunStatus << std::endl;
            p_pcBufResponse[l_iLen++] = (int)(c_stSysParam_->m_RunStatus);
            break;
        }
        default:
        {
            LOGFAE(WERROR,
                   "PRVT协议异常: 未定义 {:#X} - {:#X}, 请联系万集开发人员!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            // c_stSysParam_->m_fae.setErrorCode("I4");
            break;
        }
    }
    if (l_iLen > 26)
    {
        fillAskField(p_pcBufResponse, l_iLen);
        calcWJProtocolTail_(p_pcBufResponse, l_iLen);
        return l_iLen;
    }
    return 0;
}

bool WJProtocol::setMapCorrect_(u_char*& p_pcBufCMD, int& p_iOffset)
{
    float l_f64MymapX, l_f64MymapY, l_f64MymapA, l_f64TomapX, l_f64TomapY, l_f64TomapA;
    if (bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64MymapX)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64MymapY)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64MymapA)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64TomapX)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64TomapY)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64TomapA))
    {
        // 设置
        c_stSysParam_->m_agv.m_fMymap[0] = l_f64MymapX;
        c_stSysParam_->m_agv.m_fMymap[1] = l_f64MymapY;
        c_stSysParam_->m_agv.m_fMymap[2] = l_f64MymapA;
        c_stSysParam_->m_agv.m_fTomap[0] = l_f64TomapX;
        c_stSysParam_->m_agv.m_fTomap[1] = l_f64TomapY;
        c_stSysParam_->m_agv.m_fTomap[2] = l_f64TomapA;
        LOGFAE(WINFO,
               "设置地图校正: mymap {:.3f} | {:.3f} | {:.3f} |  tomap {:.3f} | {:.3f} | {:.3f}.",
               c_stSysParam_->m_agv.m_fMymap[0],
               c_stSysParam_->m_agv.m_fMymap[1],
               c_stSysParam_->m_agv.m_fMymap[2],
               c_stSysParam_->m_agv.m_fTomap[0],
               c_stSysParam_->m_agv.m_fTomap[1],
               c_stSysParam_->m_agv.m_fTomap[2]);
        double l_f64YawAngleDiff =
            (c_stSysParam_->m_agv.m_fTomap[2] - c_stSysParam_->m_agv.m_fMymap[2]) * M_PI / 180.0;
        double l_f64x = c_stSysParam_->m_agv.m_fMymap[0] * cos(l_f64YawAngleDiff)
                        - c_stSysParam_->m_agv.m_fMymap[1] * sin(l_f64YawAngleDiff);
        double l_f64y = c_stSysParam_->m_agv.m_fMymap[0] * sin(l_f64YawAngleDiff)
                        + c_stSysParam_->m_agv.m_fMymap[1] * cos(l_f64YawAngleDiff);
        s_POSE6D l_stTrans;
        l_stTrans.setRPY(
            0.0, 0.0, c_stSysParam_->m_agv.m_fTomap[2] - c_stSysParam_->m_agv.m_fMymap[2]);
        l_stTrans.setX(c_stSysParam_->m_agv.m_fTomap[0] - l_f64x);
        l_stTrans.setY(c_stSysParam_->m_agv.m_fTomap[1] - l_f64y);
        c_stSysParam_->m_agv.m_stTrans = l_stTrans;
        LOGPT(WINFO,
              "calMapCorrect {:.3f} | {:.3f} | {:.3f}.",
              c_stSysParam_->m_agv.m_stTrans.x(),
              c_stSysParam_->m_agv.m_stTrans.y(),
              c_stSysParam_->m_agv.m_stTrans.yaw());
        return true;
    }
    return false;
}

std::string WJProtocol::getDataPath(std::string p_sPkgPath)
{
    std::string l_sDataPath = "";
    l_sDataPath = p_sPkgPath.erase(p_sPkgPath.size() - 10, 10) + "wanji_data";
    return l_sDataPath;
}

int WJProtocol::getLidarId_(u_char*& p_pcBufCMD, int& p_iOffset)
{
    std::string l_lidarName = "";
    int l_iLidarId = -1;
    if (asciiToString_(p_pcBufCMD, p_iOffset, l_lidarName))
    {
        for (int i = 0; i < (int)(c_stSysParam_->m_lidar.size()); i++)
        {
            if (c_stSysParam_->m_lidar[i].m_sLaserName == l_lidarName)
            {
                l_iLidarId = i;
                break;
            }
        }
    }
    // p_iOffset += p_pcBufCMD[p_iOffset] +1;
    return l_iLidarId;
}

float WJProtocol::outPoseTimeAlign(s_RobotPos& p_stOutPose,
                                   s_PoseWithTwist& p_sLidarPose,
                                   int p_iPoseModel)
{
    // 获取发送时刻与雷达时间的差值，校正当前时刻的速度
    p_stOutPose.wallTime =c_stSysParam_->m_time.getTimeNowMs();
    float l_fTimediff = p_stOutPose.wallTime - p_sLidarPose.m_tsWallTime;
    s_PoseWithTwist l_stPose = p_sLidarPose;
    if (p_iPoseModel || c_stSysParam_->m_agv.m_bPoseAlign)
        l_stPose.recvTimeAlign(p_stOutPose.wallTime);

    // 更新输出Pose
    p_stOutPose.t = l_stPose.m_Pose.m_trans;
    p_stOutPose.t[2] = toYaw_(l_stPose.m_Pose.m_quat) * 180.0 / M_PI;
    if (p_stOutPose.t[2] < 0)
        p_stOutPose.t[2] += 360.0;
    else if (p_stOutPose.t[2] > 360.0)
        p_stOutPose.t[2] -= 360.0;
    p_stOutPose.meandev = (1 - (p_sLidarPose.m_Pose.m_fPercent / 1000 / 1.99471)) * 100;
    // saveOutPoseNoTransInFile_(l_stPose.m_Pose,"/home/<USER>/out.csv");
    return l_fTimediff;
}