#include "net_app/protocol.h"

Protocol::Protocol(s_NetMsg& p_stNetMsg,
                   boost::function<void(char*, int)> p_sendCallback,
                   boost::function<bool(int)> setWorkModeCb,
                   std::string p_sPrintfStr)
    : c_stNetMsg_(p_stNetMsg), sendCallBack_(p_sendCallback), c_procThr_(nullptr),
      c_pWJProtocol_(new WJProtocol(sendCallBack_, c_stNetMsg_, setWorkModeCb)),
      c_pSickProtocol_(new SickProtocol(sendCallBack_, c_stNetMsg_, setWorkModeCb)),
      c_sPrintfStr_(p_sPrintfStr), c_iCurProcOffset_(0), c_bProcThrRun_(false)
{
}

Protocol::~Protocol()
{
    shutDown();
}

void Protocol::procCmdInThread()
{
    // 重置速度频率监测标志
    c_pSickProtocol_->init();
    c_procThr_.reset(new std::thread(&Protocol::procCmd, this));
}

void Protocol::exitProcThread(void)
{
    // 如果解析线程未运行中不操作
    if (!c_bProcThrRun_)
        return;
    c_bProcThrRun_ = false;
    {
        std::lock_guard<std::mutex> l_mtx(c_mtxLock_);
        c_iCurProcOffset_ = c_stNetMsg_.m_uiDataLen;
    }

    if (c_procThr_->joinable())
    {
        c_procThr_->join();
    }

    c_iCurProcOffset_ = 0;
    c_stNetMsg_.m_uiDataLen = 0;
}

void Protocol::shutDown()
{
    exitProcThread();
    if (c_pSickProtocol_)
        c_pSickProtocol_->shutDown();
    if (c_pWJProtocol_)
        c_pWJProtocol_->shutDown();
}

#pragma region "判断协议类型(1.万集 2.sick)并指定到帧头位置"
int Protocol::recognizeProtocolType(int& p_iOffset)
{
    int l_iOffsetTmp = p_iOffset;
    int l_iCMDLen = 0;
    while (l_iOffsetTmp != (int)c_stNetMsg_.m_uiDataLen)
    {
        if ((u_char)(c_stNetMsg_.m_aucBuf[l_iOffsetTmp]) == 0xFF
            && (u_char)(c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 1) % NET_LENGTH_MAX]) == 0xAA)
        {
            l_iCMDLen = c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 2) % NET_LENGTH_MAX] << 8
                        | c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 3) % NET_LENGTH_MAX];
            if (c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + l_iCMDLen + 2) % NET_LENGTH_MAX] == 0xEE
                && c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + l_iCMDLen + 3) % NET_LENGTH_MAX] == 0xEE)
            {
                std::lock_guard<std::mutex> l_mtx(c_mtxLock_);
                if (p_iOffset == (int)c_stNetMsg_.m_uiDataLen)
                    return 0;
                p_iOffset = l_iOffsetTmp;
                return WJPROTOCOL;
            }
            else
            {
                // printf("length fail\n");
                l_iOffsetTmp++;
                l_iOffsetTmp %= NET_LENGTH_MAX;
            }
        }
        else if (c_stNetMsg_.m_aucBuf[l_iOffsetTmp] == 0x02
                 && c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 1) % NET_LENGTH_MAX] == 's')
        {
            std::lock_guard<std::mutex> l_mtx(c_mtxLock_);
            if (p_iOffset == (int)c_stNetMsg_.m_uiDataLen)
                return 0;
            p_iOffset = l_iOffsetTmp;
            return SICKPROTOCOL;
        }
        else
        {
            l_iOffsetTmp++;
            l_iOffsetTmp %= NET_LENGTH_MAX;
        }
    }
    return 0;
}
#pragma endregion

void Protocol::procCmd()
{
    int l_iSendNum = 0;
    int l_iSta = -1, l_iEnd = -1;
    int l_iDataProcLen = 0;
    char l_acBuf[1000] = {0};
    int l_iProctocolType = 0;
    u_char* l_pucBufTmp = NULL;
    c_bProcThrRun_ = true;
    while (c_bProcThrRun_)
    {
        while (c_iCurProcOffset_ != (int)c_stNetMsg_.m_uiDataLen)
        {
            if (!c_bProcThrRun_)
            {
                return;
            }
            l_iProctocolType = recognizeProtocolType(c_iCurProcOffset_);
            if (WJPROTOCOL == l_iProctocolType)
            {
                l_pucBufTmp = c_pWJProtocol_->getWJCMD(c_iCurProcOffset_, c_mtxLock_);
                if (l_pucBufTmp)
                {
                    l_iSendNum = c_pWJProtocol_->selectWJProtocol(l_pucBufTmp, l_acBuf);
                    sendCallBack_(l_acBuf, l_iSendNum);
                }
                else
                    LOGFAE(WERROR, "{} 服务器: 协议获取异常/校验未通过, 请检查!", c_sPrintfStr_);
            }
            else if (SICKPROTOCOL == l_iProctocolType)
            {
                l_pucBufTmp = c_pSickProtocol_->getSICKCMD(c_iCurProcOffset_, c_mtxLock_);
                if (l_pucBufTmp)
                {
                    l_iDataProcLen = strlen((char*)l_pucBufTmp);
                    c_pSickProtocol_->seprateSICKCmd(l_pucBufTmp, l_iDataProcLen);
                    l_iSendNum = c_pSickProtocol_->selectSICKProtocol(l_acBuf);
                    sendCallBack_(l_acBuf, l_iSendNum);
                }
            }

            if (l_pucBufTmp)
            {
                delete[] l_pucBufTmp;
                l_pucBufTmp = NULL;
            }

            l_iSta = -1;
            l_iEnd = -1;
        }
        usleep(1000);
    }
}
