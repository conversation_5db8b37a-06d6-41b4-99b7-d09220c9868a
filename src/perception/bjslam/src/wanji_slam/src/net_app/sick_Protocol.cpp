// local
#include "net_app/sick_Protocol.h"
// sys
#include <sys/time.h>

SickProtocol::SickProtocol(boost::function<void(char*, int)> sendCallback,
                           s_NetMsg& netMsg,
                           boost::function<bool(int)> setWorkModeCb)
    : c_stNetMsg_(netMsg), c_stSysParam_(SYSPARAM::getIn()), sendCallback_(sendCallback),
      setWorkModeCb_(setWorkModeCb), c_sendThr_(nullptr), c_cTail_(0x03), c_bRun_(true),
      c_bHasRecvFlag_(false), c_bIsPose1_(true)
{
    memset(&c_askProtocol_, 0, sizeof(c_askProtocol_));
    c_sendThr_.reset(new std::thread(&SickProtocol::sendThread_, this));
}
SickProtocol::~SickProtocol()
{
    shutDown();
}

void SickProtocol::shutDown()
{
    c_bRun_ = false;
    if (c_sendThr_ && c_sendThr_->joinable())
    {
        c_sendThr_->join();
    }
}

void SickProtocol::init()
{
    c_bRun_ = true;
    c_bIsFirst_ = true;
}

void SickProtocol::sendPoseACKCmdBySICK_(int p_iFlag)
{
    char l_acBuf[80] = {0};
    int l_iIndex = 0;
    int l_iLen = 0;
    l_acBuf[0] = 0x02;
    l_iLen += 1;
    strcpy(l_acBuf + l_iLen, "sMA mNPOSGetPose");  // 19
    l_iLen += 16;
    strcpy(l_acBuf + l_iLen, &c_cTail_);
    l_iLen += 1;
    if (p_iFlag != 0)
    {
        l_acBuf[l_iLen] = 0x02;
        l_iLen += 1;
        strcpy(l_acBuf + l_iLen, "sAN mNPOSGetPose 1 6 1 0");  // 26
        l_iLen += 24;
        strcpy(l_acBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        // // 仅定位/更新地图模式下打印
        // if (c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::LocatMode
        //     || c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::UpdateMapMode)
        // {
        //     // LOGFAE(WERROR,
        //     //        "{} 获取位姿超时 | 频率过快/定位超时/雷达中断，请检查雷达是否已断开连接!",
        //     //        WJLog::getWholeSysTime());
        //     // c_stSysParam_->m_fae.setErrorCode("F1");
        // }
    }
    sendCallback_(l_acBuf, l_iLen);
}
void SickProtocol::sendPoseInfoBySICK_(s_RobotPos& p_stRobPos, uint32_t p_uiSyncFlag)
{
    int l_iLen = 0;
    char l_acBuf[100] = {0};
    char l_acBuf2[16] = {0};
    char l_cOffset;

    l_acBuf[0] = 0x02;
    l_iLen += 1;
    // sAN mNPOSGetPose 版本 错误代码[默认为无错误]
    strcpy(l_acBuf + l_iLen, "sAN mNPOSGetPose 1 0 ");
    l_iLen += 21;
    // 同步/异步
    l_cOffset = sprintf(l_acBuf2, "%X", p_uiSyncFlag);
    strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
    l_iLen += (l_cOffset);
    // 有位姿数据
    strcpy(l_acBuf + l_iLen, " 1 ");
    l_iLen += 3;

    //转换位置信息 x - [28]
    l_cOffset = sprintf(l_acBuf2, "%X", int(p_stRobPos.t[0] * 1000));
    // strcat(l_acBuf, l_acBuf2);
    strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
    l_iLen += (l_cOffset);
    strcpy(l_acBuf + l_iLen, " ");
    l_iLen += 1;

    l_cOffset = sprintf(l_acBuf2, "%X", int(p_stRobPos.t[1] * 1000));
    strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
    l_iLen += (l_cOffset);
    strcpy(l_acBuf + l_iLen, " ");
    l_iLen += 1;

    l_cOffset = sprintf(l_acBuf2, "%X", int(p_stRobPos.t[2] * 1000));
    strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
    l_iLen += (l_cOffset);

    // 可选位姿数据 输出模式[默认为1 推断]
    strcpy(l_acBuf + l_iLen, " 1 1 ");
    l_iLen += 5;
    // 时间戳
    l_cOffset = sprintf(l_acBuf2, "%X", (int)p_stRobPos.wallTime);
    strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
    l_iLen += (l_cOffset);
    strcpy(l_acBuf + l_iLen, " ");
    l_iLen += 1;

    // 置信度
    l_cOffset = sprintf(l_acBuf2, "%X", (int)(p_stRobPos.meandev));
    strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
    l_iLen += (l_cOffset);
    strcpy(l_acBuf + l_iLen, " ");
    l_iLen += 1;

    // 导航模式
    l_cOffset = sprintf(l_acBuf2, "%X", (int)(p_stRobPos.flagSick));
    strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
    l_iLen += (l_cOffset);
    strcpy(l_acBuf + l_iLen, " ");
    l_iLen += 1;

    // 状态码 靶标数量[默认为3 否则AGV会认为无效]
    strcpy(l_acBuf + l_iLen, "4349E7 3");
    l_iLen += 8;
    strcpy(l_acBuf + l_iLen, &c_cTail_);
    l_iLen += 1;
    sendCallback_(l_acBuf, l_iLen);
}
u_char* SickProtocol::getSICKCMD(int& p_iOffset, std::mutex& p_lock)
{
    int l_iSta = -1, l_iEnd = -1;
    int l_iOffset = p_iOffset;
    while (l_iOffset != (int)c_stNetMsg_.m_uiDataLen)
    {
        if (c_stNetMsg_.m_aucBuf[l_iOffset] == 0x02)
        {
            l_iSta = l_iOffset;
            l_iOffset = (l_iOffset + 1) % NET_LENGTH_MAX;
        }
        else if (c_stNetMsg_.m_aucBuf[l_iOffset] == 0x03 && l_iSta != -1)
        {
            l_iEnd = l_iOffset;
            l_iOffset = (l_iOffset + 1) % NET_LENGTH_MAX;
        }
        else
        {
            l_iOffset = (l_iOffset + 1) % NET_LENGTH_MAX;
        }
        if (l_iSta != -1 && l_iEnd != -1)
        {
            int dataProcLen = (l_iEnd - l_iSta + 1 + NET_LENGTH_MAX) % NET_LENGTH_MAX;
            if (dataProcLen > 100)
            {
                LOGFAE(WERROR,
                       "{} SICK协议异常 | 长度错误 {}->{}",
                       WJLog::getWholeSysTime(),
                       l_iSta,
                       l_iEnd);
                // c_stSysParam_->m_fae.setErrorCode("F2");
                {
                    std::lock_guard<std::mutex> l_mtx(p_lock);
                    if (p_iOffset != (int)c_stNetMsg_.m_uiDataLen)
                        p_iOffset = l_iOffset;
                    return NULL;
                }
            }

            u_char* buftmp = new u_char[dataProcLen - 1];
            if (l_iSta > l_iEnd)
            {
                memcpy(buftmp, &c_stNetMsg_.m_aucBuf[l_iSta + 1], NET_LENGTH_MAX - l_iSta - 1);
                if (l_iEnd > 0)
                    memcpy(&buftmp[NET_LENGTH_MAX - l_iSta - 1], c_stNetMsg_.m_aucBuf, l_iEnd);
            }
            else
                memcpy(buftmp, &c_stNetMsg_.m_aucBuf[l_iSta + 1], dataProcLen - 2);
            buftmp[dataProcLen - 2] = '\0';
            {
                std::lock_guard<std::mutex> l_mtx(p_lock);
                if (p_iOffset == (int)c_stNetMsg_.m_uiDataLen)
                    return NULL;
                p_iOffset = l_iOffset;
                return buftmp;
            }
        }
    }
    {
        std::lock_guard<std::mutex> l_mtx(p_lock);
        p_iOffset = l_iOffset;
        return NULL;
    }
}
void SickProtocol::seprateSICKCmd(u_char* p_pucBuf, int p_iLen)
{
    int l_iOffset = 0;
    int l_iSta = 0, l_iEnd = 0;
    int l_iCMDCnt = 0;
    memset(&c_askProtocol_, 0, sizeof(s_AscProtocol));
    while (l_iOffset < p_iLen)
    {
        if (p_pucBuf[l_iOffset] == 0x20)
        {
            l_iEnd = l_iOffset;
            if (l_iCMDCnt == 0)
            {
                memcpy(c_askProtocol_.m_acCmdType, &p_pucBuf[l_iSta], l_iEnd - l_iSta);
                if ((l_iEnd - l_iSta > 10) || (l_iEnd - l_iSta < 0))
                {
                    LOGPT(
                        WERROR, "{} seprate fail1 | {}", WJLog::getWholeSysTime(), l_iEnd - l_iSta);
                    break;
                }
            }
            else if (l_iCMDCnt == 1)
            {
                memcpy(c_askProtocol_.m_acCmd, &p_pucBuf[l_iSta], l_iEnd - l_iSta);
                if ((l_iEnd - l_iSta > 20) || (l_iEnd - l_iSta < 0))
                {
                    LOGPT(
                        WERROR, "{} seprate fail2 | {}", WJLog::getWholeSysTime(), l_iEnd - l_iSta);
                    break;
                }
            }
            else
            {
                memcpy(&c_askProtocol_.m_acParam[l_iCMDCnt - 2][0],
                       &p_pucBuf[l_iSta],
                       l_iEnd - l_iSta);
                if ((l_iEnd - l_iSta > 20) || (l_iCMDCnt - 2 > 5))
                {
                    LOGPT(WERROR,
                          "{} seprate fail3 | [{}]: {}",
                          WJLog::getWholeSysTime(),
                          l_iCMDCnt - 2,
                          l_iEnd - l_iSta);
                    break;
                }
            }
            l_iSta = l_iOffset + 1;
            if (l_iCMDCnt > 7)
            {
                LOGPT(WERROR, "{} seprate fail4", WJLog::getWholeSysTime());
                break;
            }
            l_iCMDCnt++;
        }

        l_iOffset++;
    }
    if ((p_iLen - l_iSta > 20) || (l_iCMDCnt - 2 > 5))
    {
        LOGPT(WERROR,
              "{} seprate fail4 | [{}]: {}",
              WJLog::getWholeSysTime(),
              l_iCMDCnt - 2,
              p_iLen - l_iSta);
        return;
    }
    memcpy(&c_askProtocol_.m_acParam[l_iCMDCnt - 2][0], &p_pucBuf[l_iSta], p_iLen - l_iSta);
}
int SickProtocol::selectSICKProtocol(char* l_pcBuf)
{
    int l_iLen = 0;
    s16 l_s16speedx = 0;
    s16 l_s16speedy = 0;
    s32 l_s32spdang = 0;
    INPUTSPEED g_sInSpeed;
    if ((!strcmp("sMN", c_askProtocol_.m_acCmdType))
        && (!strcmp("mNPOSSetSpeed", c_askProtocol_.m_acCmd)))
    {
        /** @todo 先仅写在线状态*/
        sTimeval odomWallTimeval(0);
        sTimeval odomSyncTimeval;
        if (c_stSysParam_->m_bIsOnlineMode)
        {
            odomSyncTimeval = odomWallTimeval;
        }
        else
        {
            // odomSyncTimeval.set()
        }
        bool l_valid = true;
        float l_tInterval = c_bIsFirst_ ? 0 : c_tVel_.toc();
        if (l_tInterval > 100.0 && c_stSysParam_->m_bIsOnlineMode)
            LOGFAE(WWARN,
                   "{} AGV车体发送速度频率过慢 | {:.3}>100ms",
                   WJLog::getWholeSysTime(),
                   l_tInterval);
        c_tVel_.tic();

        //字符串转int
        g_sInSpeed.m_u16Corrdinate_Type = std::atoi(c_askProtocol_.m_acParam[4]);
        int l_u32ret = 0;
        //输入的时间戳
        if (!(g_sInSpeed.m_u16Corrdinate_Type == COORDINATE_YTPE_ABS
              || g_sInSpeed.m_u16Corrdinate_Type == COORDINATE_YTPE_RELAVTIVE))
        {
            LOGPT(WERROR, "{} speed type error", WJLog::getWholeSysTime());
        }
        else
        {
            l_u32ret = 1;
            uint32_t l_OwnTime = hexStr2U32_(&c_askProtocol_.m_acParam[3][0]);
            if (!c_bIsFirst_)
            {
                if (c_tVel_.toc(l_OwnTime) > 100)
                    LOGFAE(WWARN,
                           "{} AGV车体速度频率过慢 | {}>100ms",
                           WJLog::getWholeSysTime(),
                           c_tVel_.toc(l_OwnTime));
                else if (c_tVel_.toc(l_OwnTime) == 0)
                {
                    l_valid = false;
                    LOGFAE(WWARN,
                           "{} AGV车体速度时间戳未更新 | 时间戳 {} ms",
                           WJLog::getWholeSysTime(),
                           c_tVel_.toc(l_OwnTime));
                    return 0;
                }
            }

            c_tVel_.tic(l_OwnTime);
            // 授时
            if (!c_stSysParam_->m_time.isSensorTimeSet())
            {
                c_stSysParam_->m_time.setSensorBaseTime(odomSyncTimeval);
            }
            if (c_stSysParam_->m_time.isSensorTimeSet())
                g_sInSpeed.m_tsSynctime =
                    odomSyncTimeval.getDiffMs(c_stSysParam_->m_time.getSensorBaseTime());

            //请求位置数据
            l_s16speedx = (s16)hexStr2U32_(&c_askProtocol_.m_acParam[0][0]);
            l_s16speedy = (s16)hexStr2U32_(&c_askProtocol_.m_acParam[1][0]);
            //这里的角度
            l_s32spdang = (s32)hexStr2U32_(&c_askProtocol_.m_acParam[2][0]);
            //判别数据有效x
            if ((l_s16speedx < -32000) || (l_s16speedx > 32000))
            {
                LOGFAE(WWARN,
                       "{} AGV车体发送速度异常 | X轴速度 {}>3.2m/s",
                       WJLog::getWholeSysTime(),
                       l_s16speedx / 1000.0);
                l_u32ret = 0;  //数据超过设定范围
            }

            //判别数据有效y
            if ((l_s16speedy < -32000) || (l_s16speedy > 32000))
            {
                LOGFAE(WWARN,
                       "{} AGV车体发送速度异常 | Y轴速度 {}>3.2m/s",
                       WJLog::getWholeSysTime(),
                       l_s16speedy / 1000.0);
                l_u32ret = 0;  //数据超过设定范围
            }

            //角速度
            if ((l_s32spdang < -360000) || (l_s32spdang > 360000))
            {
                LOGFAE(WWARN,
                       "{} AGV车体发送速度异常 | 旋转速度 {}>360deg/s",
                       WJLog::getWholeSysTime(),
                       l_s32spdang / 1000.0);
                l_u32ret = 0;  //数据超过设定范围
            }
        }

        if (l_u32ret == 0)
        {
            LOGFAE(WERROR,
                   "{} AGV车体发送速度异常 | 速度超限/协议错误, 请检查!",
                   WJLog::getWholeSysTime());
            // c_stSysParam_->m_fae.setErrorCode("F4");
            memset(&g_sInSpeed, 0, STRUCT_SIZE_INPUTSPEED);
        }
        else
        {
            s_TWIST l_twist;
            l_twist.setXYZ(l_s16speedx / 10000.0, l_s16speedy / 10000.0, 0);  // mm/s -> m/100ms
            // 速度坐标系转移
            l_twist.m_trans = c_stSysParam_->m_vel.m_stWheelToLidar.m_quat * l_twist.m_trans;
            // 角速度  mdeg/s -> deg/100ms
            Eigen::Vector3d TWIST_AV(0, 0, l_s32spdang / 10000.0);
            // angle-velocity
            TWIST_AV = c_stSysParam_->m_vel.m_stWheelToLidar.m_quat * TWIST_AV;
            l_twist.setRPY(TWIST_AV.x(), TWIST_AV.y(), TWIST_AV.z());

            s_fuseTwist l_fuseTwist;
            l_fuseTwist.m_Twist = l_twist;
            l_fuseTwist.m_tsSyncTime = g_sInSpeed.m_tsSynctime;
            l_fuseTwist.m_tsWallTime =
                odomWallTimeval.getDiffMs(c_stSysParam_->m_time.getSystemBaseTime());

            // 最后更新速度状态（用作互斥锁）-根据状态判断是否速度完成更新
            if (g_sInSpeed.m_u16Corrdinate_Type == COORDINATE_YTPE_ABS)
                l_fuseTwist.setFlag(TwistStatus::Absolute);
            else if (g_sInSpeed.m_u16Corrdinate_Type == COORDINATE_YTPE_RELAVTIVE)
                l_fuseTwist.setFlag(TwistStatus::Relative);
            else
                l_fuseTwist.setFlag(TwistStatus::Unknown);
            if (l_valid)
                c_stSysParam_->m_vel.m_stWheelTwist = l_fuseTwist;
            if (c_stSysParam_->m_fae.m_bPrintfLog)
                LOGSPEED(WINFO,
                         "{} recvTwist: {} | {:.3} {:.3} {:.3} | {:.3} {:.3} {:.3}",
                         WJLog::getWholeSysTime(),
                         (int)l_fuseTwist.m_iStatus,
                         l_fuseTwist.m_tsSyncTime,
                         l_fuseTwist.m_tsWallTime,
                         l_tInterval,
                         l_fuseTwist.m_Twist.x(),
                         l_fuseTwist.m_Twist.y(),
                         l_fuseTwist.m_Twist.yaw());
            // /* 存速度 */
            // if(c_stSysParam_->m_bSaveLog)
            //     saveLidarVelInFile_(&l_fuseTwist, l_tInterval, c_stSysParam_->m_sPkgPath +
            //     "/data/Log/agvVel.csv");
        }

        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sAN mNPOSSetSpeed 0");
        l_iLen += 19;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;

        c_bIsFirst_ = false;
        return l_iLen;
    }
    //模拟初始化流程
    else if ((!strcmp("sMN", c_askProtocol_.m_acCmdType))
             && (!strcmp("SetAccessMode", c_askProtocol_.m_acCmd)))
    {
        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sAN SetAccessMode 1");
        l_iLen += 19;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }
    //设置模式
    else if ((!strcmp("sMN", c_askProtocol_.m_acCmdType))
             && (!strcmp("mNEVAChangeState", c_askProtocol_.m_acCmd)))
    {
        //命令应答
        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sMA mNEVAChangeState");
        l_iLen += 20;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        l_pcBuf[l_iLen] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sAN mNEVAChangeState 0 ");
        l_iLen += 23;
        strcpy(l_pcBuf + l_iLen, &c_askProtocol_.m_acParam[0][0]);
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;

        return l_iLen;
    }

    //设置层
    else if ((!strcmp("sWN", c_askProtocol_.m_acCmdType))
             && (!strcmp("NEVACurrLayer", c_askProtocol_.m_acCmd)))
    {
        //命令应答
        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sWA NEVACurrLayer");
        l_iLen += 17;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }

    // NPOSSlidingMean
    else if ((!strcmp("sWN", c_askProtocol_.m_acCmdType))
             && (!strcmp("NPOSSlidingMean", c_askProtocol_.m_acCmd)))
    {
        //命令应答
        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sWA NPOSSlidingMean");
        l_iLen += 19;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }
    else if ((!strcmp("sWN", c_askProtocol_.m_acCmdType))
             && (!strcmp("NPOSPoseDataFormat", c_askProtocol_.m_acCmd)))
    {
        //命令应答
        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sWA NPOSPoseDataFormat");
        l_iLen += 22;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }
    else if ((!strcmp("sWN", c_askProtocol_.m_acCmdType))
             && (!strcmp("NCORIdentWindow", c_askProtocol_.m_acCmd)))
    {
        //命令应答
        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sWA NCORIdentWindow");
        l_iLen += 19;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }
    else if ((!strcmp("sWN", c_askProtocol_.m_acCmdType))
             && (!strcmp("NLMDnClosest", c_askProtocol_.m_acCmd)))
    {
        //命令应答
        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sWA NLMDnClosest");
        l_iLen += 16;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }
    else if ((!strcmp("sMN", c_askProtocol_.m_acCmdType))
             && (!strcmp("mNAVGetTimestamp", c_askProtocol_.m_acCmd)))
    {
        //命令应答
        int l_iOffset;
        char l_pcBufTmp[16] = {0};
        l_pcBuf[0] = 0x02;
        l_iLen += 1;

        strcpy(l_pcBuf + l_iLen, "sAN mNAVGetTimestamp 0 ");
        l_iLen += 23;
        //添加时间戳
        int l_timeMs = c_stSysParam_->m_time.getTimeNowMs();
        LOGFAE(WINFO, "{} AGV车体授时完成:  {}", WJLog::getWholeSysTime(), l_timeMs);
        l_iOffset = sprintf(l_pcBufTmp, "%X", l_timeMs);
        strncpy(l_pcBuf + l_iLen, l_pcBufTmp, l_iOffset);
        l_iLen += (l_iOffset);
        //添加时间戳
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }
    //设置位姿数据
    else if ((!strcmp("sMN", c_askProtocol_.m_acCmdType))
             && (!strcmp("mNPOSSetPose", c_askProtocol_.m_acCmd)))
    {
        float l_fPoseX, l_fPoseY, l_fPoseA;
        l_fPoseX = ((s32)hexStr2U32_(&c_askProtocol_.m_acParam[0][0])) / 1000.0;
        l_fPoseY = ((s32)hexStr2U32_(&c_askProtocol_.m_acParam[1][0])) / 1000.0;
        l_fPoseA = ((s32)hexStr2U32_(&c_askProtocol_.m_acParam[2][0])) / 1000.0;

        c_stSysParam_->m_pos.m_stSetPose.setX(l_fPoseX);
        c_stSysParam_->m_pos.m_stSetPose.setY(l_fPoseY);
        c_stSysParam_->m_pos.m_stSetPose.setZ(0);
        c_stSysParam_->m_pos.m_stSetPose.setRPY(0, 0, l_fPoseA);
        c_stSysParam_->m_pos.m_stSetPose = c_stSysParam_->m_agv.m_stTrans.inverse()
                                            * c_stSysParam_->m_pos.m_stSetPose
                                            * c_stSysParam_->m_agv.m_stLidarToAgv;
        c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
        int res = setWorkModeCb_(10);

        l_pcBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_pcBuf + l_iLen, "sAN mNPOSSetPose 0");
        l_iLen += 18;
        strcpy(l_pcBuf + l_iLen, &c_cTail_);
        l_iLen += 1;
        return l_iLen;
    }
    //请求位置数据
    else if ((!strcmp("sMN", c_askProtocol_.m_acCmdType))
             && (!strcmp("mNPOSGetPose", c_askProtocol_.m_acCmd)))
    {
        //先看下设备是否busy
        if (c_askProtocol_.m_acParam[0][0] == 0x31)
        {
            sendPoseACKCmdBySICK_(c_bHasRecvFlag_);
            if (!c_bHasRecvFlag_)
            {
                c_bHasRecvFlag_ = true;
                c_bIsPose1_ = true;
            }
            return 0;
        }
        else if (c_askProtocol_.m_acParam[0][0] == 0x30)
        {
            sendPoseACKCmdBySICK_(0);
            if (!c_bHasRecvFlag_)
            {
                c_bHasRecvFlag_ = true;
                c_bIsPose1_ = false;
            }
            else
                LOGPT(WINFO,
                      "{} getPos 0 not handle {:.3}",
                      WJLog::getWholeSysTime(),
                      c_stSysParam_->m_time.getTimeNowMs());
            return 0;
        }
    }
    else
        return 0;
    return 0;
}
void SickProtocol::sendThread_()
{
    while (c_bRun_)
    {
        if (c_bHasRecvFlag_)
        {
            sendPose_(c_bIsPose1_);
            c_bHasRecvFlag_ = false;
        }
        sleepMs(1);
    }
}
void SickProtocol::sendPose_(int p_iPoseModel)
{
    TicToc l_tSum;
    float l_tRecord[4] = {0.0};
    float l_tlockT = 0.0;
    s_RobotPos l_stPoseSend;
    s_PoseWithTwist l_sPoseWithTwist;
    // 定位模式更新l_sPoseWithTwist/l_stPoseSend 否则使用默认值
    if (c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::LocatMode
        || c_stSysParam_->m_iWorkMode == wj_slam::WorkMode::UpdateMapMode)
    {
        // p_iPoseModel 1： 实时位姿 2：虚拟位姿
        if (p_iPoseModel)
        {
            // 每次使用前清空标志 等待新鲜的最新帧
            c_stSysParam_->m_pos.m_stCurrPose.setFlag(PoseStatus::Default);
            while (1)
            {
                //死等最新帧定位完毕
                if (c_bRun_
                    && c_stSysParam_->m_pos.m_stCurrPose.getFlag() != wj_slam::PoseStatus::Default)
                {
                    //复制最新位姿
                    TicToc l_TtempT;
                    l_sPoseWithTwist = c_stSysParam_->m_pos.m_stCurrPose.getData();
                    l_tlockT = l_TtempT.toc();
                    break;
                }
                if (!c_bRun_)
                    return;
                sleepMs(1);
            }
        }
        else
        {
            //复制last位姿
            TicToc l_TtempT;
            l_sPoseWithTwist = c_stSysParam_->m_pos.m_stLastPose.getData();
            l_tlockT = l_TtempT.toc();
        }
    }
    l_tRecord[0] = l_tSum.toc();
    // 定位状态
    switch (l_sPoseWithTwist.m_bFlag)
    {
        case PoseStatus::Default:
            l_stPoseSend.flagSick = SICKPoseStatus::StopSICK;
            l_sPoseWithTwist.m_Pose.m_fPercent = -1;
            break;
        case PoseStatus::InitialPose: l_stPoseSend.flagSick = SICKPoseStatus::InitialSICK; break;
        case PoseStatus::ContinuePose: l_stPoseSend.flagSick = SICKPoseStatus::ContinueSICK; break;
        case PoseStatus::VirtualPose: l_stPoseSend.flagSick = SICKPoseStatus::VirtualSICK; break;
        case PoseStatus::CurbPose: l_stPoseSend.flagSick = SICKPoseStatus::VirtualSICK; break;
        case PoseStatus::StopPose:
            l_stPoseSend.flagSick = SICKPoseStatus::StopSICK;
            l_sPoseWithTwist.m_Pose.m_fPercent = -1;
            break;
        default:
            l_stPoseSend.flagSick = SICKPoseStatus::StopSICK;
            l_sPoseWithTwist.m_Pose.m_fPercent = -1;
            break;
    }

    // l_sPoseWithTwist.m_Pose.printf("sick pose");
    // l_sPoseWithTwist.m_Twist.printf("sick vel");

    // 时间对齐
    float l_fTdiff = outPoseTimeAlign_(l_stPoseSend, l_sPoseWithTwist, p_iPoseModel);
    l_tRecord[1] = l_tSum.toc();

    // PoseStatus::StopPose 模式下清空位姿
    if (l_stPoseSend.flagSick == SICKPoseStatus::StopSICK)
        l_stPoseSend.t = Eigen::Vector3d::Zero();

    sendPoseInfoBySICK_(l_stPoseSend, p_iPoseModel);
    l_tRecord[2] = l_tSum.toc();

    if (c_stSysParam_->m_fae.m_bPrintfLog)
    {
        LOGPATH(WINFO,
                "{} [AGV位姿] 状态 {} 时间戳 {} 补偿值: {} 发送 {} {} {} "
                "帧 [{}] 时间戳 {} 雷达位姿 {} {} {} {} {} {}"
                "速度 {} {} {} {} {} {}",
                WJLog::getWholeSysTime(),
                l_stPoseSend.getRobotPoseStatus(true),
                l_stPoseSend.wallTime,
                l_stPoseSend.wallTime - l_sPoseWithTwist.m_tsWallTime,
                (int)(l_stPoseSend.t[0] * 1000),
                (int)(l_stPoseSend.t[1] * 1000),
                (int)(l_stPoseSend.t[2] * 1000),
                l_sPoseWithTwist.m_iScanId,
                l_sPoseWithTwist.m_tsSyncTime,
                (int)(l_sPoseWithTwist.m_Pose.x() * 1000),
                (int)(l_sPoseWithTwist.m_Pose.y() * 1000),
                (int)(l_sPoseWithTwist.m_Pose.z() * 1000),
                (int)(l_sPoseWithTwist.m_Pose.roll() * 1000),
                (int)(l_sPoseWithTwist.m_Pose.pitch() * 1000),
                (int)(l_sPoseWithTwist.m_Pose.yaw() * 1000),
                (int)(l_sPoseWithTwist.m_Twist.x() * 1000),
                (int)(l_sPoseWithTwist.m_Twist.y() * 1000),
                (int)(l_sPoseWithTwist.m_Twist.z() * 1000),
                (int)(l_sPoseWithTwist.m_Twist.roll() * 1000),
                (int)(l_sPoseWithTwist.m_Twist.pitch() * 1000),
                (int)(l_sPoseWithTwist.m_Twist.yaw() * 1000));
    }
    l_tRecord[3] = l_tSum.toc();
    if ((p_iPoseModel && l_tRecord[3] > 150) || ((!p_iPoseModel) && l_tRecord[3] > 5))
        LOGPT(WWARN,
              "{} {} Sick send timeout {:.3} | {:.3} {:.3} {:.3} {:.3}",
              WJLog::getWholeSysTime(),
              l_stPoseSend.wallTime,
              l_tlockT,
              l_tRecord[0],
              l_tRecord[1],
              l_tRecord[2],
              l_tRecord[3]);
    //保存给发送至agv的轨迹
    // saveOutPoseInFile_(l_stPoseSend, "/home/<USER>/outAGV.csv");
}

double SickProtocol::toYaw_(const Eigen::Quaterniond q, bool p_bIn2PI)
{
    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    double l_yaw = atan2(siny_cosp, cosy_cosp);

    if (p_bIn2PI)
    {
        if (l_yaw < 0.0)
            l_yaw += 2 * M_PI;
        else if (l_yaw > 2 * M_PI)
            l_yaw -= 2 * M_PI;
    }
    return l_yaw;
}

float SickProtocol::getTimeStampDiff(int p_iTimetamp)
{
    struct timeval c_CurrTime_;

    //返回s 和 us
    gettimeofday(&c_CurrTime_, NULL);  // wen

    int l_iCurrTmsec = ((c_CurrTime_.tv_sec - 1626800000) * 1000) + (c_CurrTime_.tv_usec / 1000.0);

    float l_diff = (l_iCurrTmsec - p_iTimetamp) / 100.0;

    // printf("getTimeStampDiff :%f | %d | %d \n", l_diff, l_iCurrTmsec, p_iTimetamp);
    return l_diff;
}

void SickProtocol::updateProbPose(s_PoseWithTwist& p_sPose6D)
{
    p_sPose6D.m_Pose.m_trans =
        p_sPose6D.m_Pose.m_trans + p_sPose6D.m_Pose.m_quat * p_sPose6D.m_Twist.m_trans;
    p_sPose6D.m_Pose.m_quat = p_sPose6D.m_Pose.m_quat * p_sPose6D.m_Twist.m_quat;
}

void SickProtocol::saveOutPoseInFile_(s_RobotPos& p_sCurrPose, std::string p_sFilePath)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::app);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_sCurrPose.t[0] << "," << p_sCurrPose.t[1] << "," << p_sCurrPose.t[2]
                     << std::endl;
        l_filePoseWR.close();
    }
}

void SickProtocol::saveOutPoseNoTransInFile_(s_POSE6D& p_sCurrPose, std::string p_sFilePath)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::app);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_sCurrPose.m_trans[0] << "," << p_sCurrPose.m_trans[1] << ","
                     << (toYaw_(p_sCurrPose.m_quat) / M_PI * 180) << std::endl;
        l_filePoseWR.close();
    }
}

float SickProtocol::outPoseTimeAlign_(s_RobotPos& p_stOutPose,
                                      s_PoseWithTwist& p_sLidarPose,
                                      int p_iPoseModel)
{
    // p_sLidarPose.m_Pose.printf("Send Before");
    // 获取发送时刻与雷达时间的差值，校正当前时刻的速度
    p_stOutPose.wallTime = c_stSysParam_->m_time.getTimeNowMs();
    timeMs l_fTimediff = p_stOutPose.wallTime - p_sLidarPose.m_tsWallTime;
    s_PoseWithTwist l_stPose = p_sLidarPose;
    if (p_iPoseModel || c_stSysParam_->m_agv.m_bPoseAlign)
        l_stPose.recvTimeAlign(p_stOutPose.wallTime);

    if (c_LastPose_.getFlag() == PoseStatus::Default)
        c_LastPose_ = l_stPose;
    s_POSE6D l_twist = c_LastPose_.m_Pose.inverse() * l_stPose.m_Pose;
    c_LastPose_ = l_stPose;
    if (l_twist.x() < -0.2 || l_twist.y() < -0.2)
        if (c_stSysParam_->m_fae.m_bPrintfLog)
            LOGPT(WWARN,
                  "{} sick send Pose back {:.6f} {} {:.3f} {:.3f} {:.3f}",
                  WJLog::getWholeSysTime(),
                  p_stOutPose.wallTime,
                  p_sLidarPose.m_iScanId,
                  l_twist.x(),
                  l_twist.y(),
                  l_twist.normXY());

    // 更新输出Pose
    p_stOutPose.t = l_stPose.m_Pose.m_trans;
    p_stOutPose.t[2] = toYaw_(l_stPose.m_Pose.m_quat) * 180.0 / M_PI;
    if (p_stOutPose.t[2] < 0)
        p_stOutPose.t[2] += 360.0;
    else if (p_stOutPose.t[2] > 360.0)
        p_stOutPose.t[2] -= 360.0;
    p_stOutPose.meandev = (1 - (p_sLidarPose.m_Pose.m_fPercent / 1000 / 1.99471)) * 100;
    // saveOutPoseNoTransInFile_(l_stPose.m_Pose,"/home/<USER>/out.csv");
    // p_stOutPose.printf("SICK Send");
    return l_fTimediff;
}

void SickProtocol::saveLidarVelInFile_(s_fuseTwist* p_fuseTwist,
                                       float l_tInterval,
                                       std::string p_sFilePath)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::app);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_fuseTwist->m_iStatus << "," << p_fuseTwist->m_tsWallTime << ","
                     << p_fuseTwist->m_Twist.x() << "," << p_fuseTwist->m_Twist.y() << ","
                     << p_fuseTwist->m_Twist.yaw() << "," << l_tInterval << std::endl;
        l_filePoseWR.close();
    }
}

u32 SickProtocol::hexStr2U32_(void* Str)
{
    u32 tmp = 0;
    char* p = (char*)Str;
    while (1)
    {
        if (p == nullptr)
            break;
        if (*p >= '0' && *p <= '9')
        {
            tmp *= 16;
            tmp += *p - '0';
        }
        else if (*p >= 'a' && *p <= 'f')
        {
            tmp *= 16;
            tmp += *p - 'a' + 10;
        }
        else if (*p >= 'A' && *p <= 'F')
        {
            tmp *= 16;
            tmp += *p - 'A' + 10;
        }
        else
        {
            break;
        }
        p++;
    }
    return tmp;
}