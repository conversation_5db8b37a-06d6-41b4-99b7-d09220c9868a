// local
#include "net_app/netApp.h"
// sys
#include <arpa/inet.h>

WJNetApp::WJNetApp(boost::function<bool(int)> setWorkModeCb, std::string p_sPrintfStr)
    : c_pProc_(nullptr), c_pstNetMsg_(nullptr), c_sPrintfStr_(p_sPrintfStr), c_servThr_(nullptr),
      c_iSockFd_(-1), c_iNewFd_(-1), c_bRun_(true), c_bProcThrRun_(false)
{
    c_pstNetMsg_ = new s_NetMsg;
    c_pProc_ = new Protocol(
        *c_pstNetMsg_, boost::bind(&WJNetApp::sendMsg_, this, _1, _2), setWorkModeCb, c_sPrintfStr_);
}

WJNetApp::~WJNetApp()
{
    shutDown();
    if (c_pProc_ != NULL)
        delete c_pProc_;
    if (c_pstNetMsg_ != NULL)
        delete c_pstNetMsg_;
    c_pProc_ = NULL;
    c_pstNetMsg_ = NULL;
}

void WJNetApp::sendWebPose(wj_slam::s_POSE6D& poseLidar, wj_slam::s_POSE6D& poseAGV)
{
    //反馈Web位姿
    u_char l_aucQuerycmdWebPose[26] = {0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x01, 0x02, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x00, 0x00, 0x00, 0x0E, 0x06, 0x00, 0x00};
    char l_acBuf[100] = {0};
    int l_iOffset = 26;
    memcpy(&l_acBuf[0], l_aucQuerycmdWebPose, l_iOffset);

    // Laser-xyza + AGV-xyza + 标志位
    fillFloatTo32Bytes(poseLidar.x(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(poseLidar.y(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(poseLidar.z(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(poseLidar.yaw(), l_acBuf, l_iOffset);

    fillFloatTo32Bytes(poseAGV.x(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(poseAGV.y(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(poseAGV.z(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(poseAGV.yaw(), l_acBuf, l_iOffset);
    l_acBuf[l_iOffset++] = poseAGV.m_bFlag - 1;

    // 写入帧长、校验位、帧尾
    l_acBuf[2] = (l_iOffset & 0xff00) >> 8;
    l_acBuf[3] = l_iOffset & 0xff;
    l_acBuf[l_iOffset++] = 0;
    l_acBuf[l_iOffset++] = checkXOR(&l_acBuf[2], l_iOffset - 3);
    l_acBuf[l_iOffset++] = 0xEE;
    l_acBuf[l_iOffset++] = 0xEE;

    if (c_bRun_)
        sendMsg_(l_acBuf, l_iOffset);
}

void WJNetApp::shutDown()
{
    c_bRun_ = false;
    c_bProcThrRun_ = false;
    if (c_iNewFd_ != -1)
    {
        shutdown(c_iNewFd_, SHUT_RDWR);
        close(c_iNewFd_);
    }

    if (c_iSockFd_ != -1)
    {
        shutdown(c_iSockFd_, SHUT_RDWR);
        close(c_iSockFd_);
    }

    if (c_servThr_ && c_servThr_->joinable())
    {
        c_servThr_->join();
    }
}

void WJNetApp::start(int p_iPort)
{
    shutDown();
    c_bRun_ = true;
    c_servThr_.reset(new std::thread(&WJNetApp::startServer_, this, p_iPort));
}

void WJNetApp::procMsg_()
{
    char l_acBufTmp[4096];
    int l_iConnectInfoLen_ = sizeof(struct tcp_info);
    int l_iDataNum;

    c_pProc_->procCmdInThread();
    c_bProcThrRun_ = true;
    while (c_bProcThrRun_)
    {
        int l_res = getsockopt(
            c_iNewFd_, IPPROTO_TCP, TCP_INFO, &c_stConnectInfo_, (socklen_t*)&l_iConnectInfoLen_);
        if (l_res == -1 || c_stConnectInfo_.tcpi_state != TCP_ESTABLISHED)
        {
            LOGFAE(WWARN,
                   "[{}] {}服务器: 客户端已断开 | 客户端-IP: {} 客户端-Port: {} | 网络状态: {}",
                   WJLog::getWholeSysTime(),
                   c_sPrintfStr_.c_str(),
                   inet_ntoa(c_stClientAddr_.sin_addr),
                   ntohs(c_stClientAddr_.sin_port),
                   c_stConnectInfo_.tcpi_state);
            break;
        }
        if (c_iNewFd_ != -1)
        {
            l_iDataNum = recv(c_iNewFd_, l_acBufTmp, 4096, 0);
            if (l_iDataNum <= 0)
            {
                getsockopt(c_iNewFd_,
                           IPPROTO_TCP,
                           TCP_INFO,
                           &c_stConnectInfo_,
                           (socklen_t*)&l_iConnectInfoLen_);
                LOGPT(WERROR,
                      "{} [{}] Net recvMsg fail: {} | return {} | tcp: {}",
                      WJLog::getWholeSysTime(),
                      c_sPrintfStr_.c_str(),
                      strerror(l_iDataNum),
                      c_stConnectInfo_.tcpi_state,
                      l_res);
            }
        }
        else
            l_iDataNum = 0;
        if (l_iDataNum > 0 && l_iDataNum <= NET_LENGTH_MAX)
        {
            if ((c_pstNetMsg_->m_uiDataLen + l_iDataNum) <= (NET_LENGTH_MAX))
            {
                memcpy(&c_pstNetMsg_->m_aucBuf[c_pstNetMsg_->m_uiDataLen], l_acBufTmp, l_iDataNum);
                c_pstNetMsg_->m_uiDataLen += l_iDataNum;
                c_pstNetMsg_->m_uiDataLen %= NET_LENGTH_MAX;
            }
            else
            {
                int offset = 0;
                memcpy(&c_pstNetMsg_->m_aucBuf[c_pstNetMsg_->m_uiDataLen],
                       l_acBufTmp,
                       NET_LENGTH_MAX - c_pstNetMsg_->m_uiDataLen);
                offset = NET_LENGTH_MAX - c_pstNetMsg_->m_uiDataLen;
                memcpy(&c_pstNetMsg_->m_aucBuf[0], &l_acBufTmp[offset], l_iDataNum - offset);
                c_pstNetMsg_->m_uiDataLen = l_iDataNum - offset;
            }
        }
        else if (l_iDataNum > 0)
            LOGPT(WERROR,
                  "{} [{}] Net Recv Size Too Big {}",
                  WJLog::getWholeSysTime(),
                  c_sPrintfStr_.c_str(),
                  l_iDataNum);
        usleep(1000);
    }
    shutdownConnect_();
    c_pProc_->exitProcThread();
}

void WJNetApp::sendMsg_(char* p_pcBuf, int p_iLen)
{
    if (c_iNewFd_ != -1 && p_iLen > 0)
    {
        int l_res = send(c_iNewFd_, p_pcBuf, p_iLen, MSG_NOSIGNAL);
        if (l_res != p_iLen)
        {
            LOGPT(WERROR,
                  "{} Net sendMsg fail->status: {} | value {} | buf {} | len {}",
                  WJLog::getWholeSysTime(),
                  strerror(l_res),
                  l_res,
                  p_pcBuf,
                  p_iLen);
        }
    }
}

void WJNetApp::startServer_(int p_iPort)
{
    int l_iSinSize;
    if ((c_iSockFd_ = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        LOGPT(WERROR,
              "{} {} server socket error: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return;
    }
    bzero(&c_stServerAddr_, sizeof(struct sockaddr_in));
    c_stServerAddr_.sin_family = AF_INET;
    c_stServerAddr_.sin_addr.s_addr = htonl(INADDR_ANY);
    c_stServerAddr_.sin_port = htons(p_iPort);

    int keepalive = 1;    // 开启TCP KeepAlive功能
    int keepidle = 1;     // tcp_keepalive_time 每隔1s确认1次网络连接
    int keepcnt = 3;      // tcp_keepalive_probes 重复3次，主动断开
    int keepintvl = 1;    // tcp_keepalive_intvl   重复次数之间-间隔1s
    int noDelaySend = 1;  // tcp_nodelay_flag 不延时发送，禁止Nagle算法

    if (setsockopt(c_iSockFd_, SOL_SOCKET, SO_KEEPALIVE, (void*)&keepalive, sizeof(keepalive)))
    {
        LOGPT(WERROR,
              "{} {} server socket  SO_KEEPALIVE: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return;
    }
    if (setsockopt(c_iSockFd_, SOL_TCP, TCP_KEEPIDLE, (void*)&keepidle, sizeof(keepidle)))
    {
        LOGPT(WERROR,
              "{} {} server socket  TCP_KEEPIDLE: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return;
    }
    if (setsockopt(c_iSockFd_, SOL_TCP, TCP_KEEPCNT, (void*)&keepcnt, sizeof(keepcnt)))
    {
        LOGPT(WERROR,
              "{} {} server socket  TCP_KEEPCNT: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return;
    }
    if (setsockopt(c_iSockFd_, SOL_TCP, TCP_KEEPINTVL, (void*)&keepintvl, sizeof(keepintvl)))
    {
        LOGPT(WERROR,
              "{} {} server socket  TCP_KEEPINTVL: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return;
    }
    if (setsockopt(c_iSockFd_, IPPROTO_TCP, TCP_NODELAY, &noDelaySend, sizeof(noDelaySend)))
    {
        LOGPT(WERROR,
              "{} {} server socket  TCP_NODELAY: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return;
    }

    // 服务器设置地址复用
    int optval = 1;
    setsockopt(c_iSockFd_, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));

    if (bind(c_iSockFd_, (struct sockaddr*)(&c_stServerAddr_), sizeof(struct sockaddr)) == -1)
    {
        close(c_iSockFd_);
        c_iSockFd_ = -1;
        LOGFAE(WERROR, "{} 服务器初始化失败 | 端口绑定错误: {}", c_sPrintfStr_, strerror(errno));
        return;
    }
    if (listen(c_iSockFd_, 5) == -1)
    {
        LOGPT(WERROR,
              "{} {} server socket  listen error: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return;
    }

    LOGFAE(
        WINFO, "[{}] {} 服务器: 等待客户端连接...", WJLog::getWholeSysTime(), c_sPrintfStr_.c_str());

    int l_iTempFd = 0;
    sockaddr_in l_stClinentAddr;
    std::shared_ptr<std::thread> procThr_ = nullptr; /**< 处理线程 */
    while (c_bRun_)
    {
        l_iSinSize = sizeof(struct sockaddr_in);
        if ((l_iTempFd =
                 accept(c_iSockFd_, (struct sockaddr*)(&l_stClinentAddr), (socklen_t*)&l_iSinSize))
            == -1)
        {
            LOGPT(WERROR,
                  "{} {} server socket  accept error: {}",
                  WJLog::getWholeSysTime(),
                  c_sPrintfStr_,
                  strerror(errno));
            break;
        }
        // 新的连接到来后断开旧连接，创建新的接收线程
        if (procThr_)
        {
            // 线程退出时会断开连接
            c_bProcThrRun_ = false;
            shutdownConnect_();
            if (procThr_->joinable())
                procThr_->join();
            procThr_ = nullptr;
        }

        c_iNewFd_ = l_iTempFd;
        c_stClientAddr_ = l_stClinentAddr;
        LOGFAE(WINFO,
               "[{}] {}服务器: 客户端已连接 | 客户端-IP: {} 客户端-Port: {}",
               WJLog::getWholeSysTime(),
               c_sPrintfStr_.c_str(),
               inet_ntoa(c_stClientAddr_.sin_addr),
               ntohs(c_stClientAddr_.sin_port));

        if (!c_bRun_)
            break;
        procThr_.reset(new std::thread(&WJNetApp::procMsg_, this));
        usleep(1000);
    }

    if (procThr_)
    {
        // 线程退出时会断开连接
        c_bProcThrRun_ = false;
        shutdownConnect_();
        if (procThr_->joinable())
            procThr_->join();
        procThr_ = nullptr;
    }
}

void WJNetApp::shutdownConnect_()
{
    // 关闭TCP监听套接字
    if (c_iNewFd_ != -1)
    {
        shutdown(c_iNewFd_, SHUT_RDWR);
        close(c_iNewFd_);
        c_iNewFd_ = -1;

        LOGFAE(WWARN,
               "[{}] {}服务器: 客户端已断开 | 客户端-IP: {} 客户端-Port: {} | 网络状态: {}",
               WJLog::getWholeSysTime(),
               c_sPrintfStr_.c_str(),
               inet_ntoa(c_stClientAddr_.sin_addr),
               ntohs(c_stClientAddr_.sin_port),
               c_stConnectInfo_.tcpi_state);
    }
}

bool WJNetApp::connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr)
{
    if (p_iFd != -1)
    {
        close(p_iFd);
        p_iFd = -1;
    }
    // 建立套接字
    p_iFd = socket(PF_INET, SOCK_STREAM, 0);
    if (p_iFd == -1)
    {
        LOGPT(WERROR,
              "{} {} server open socket failed: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return false;
    }
    // 端口复用
    int l_iOpt2 = 1;
    if (setsockopt(p_iFd, SOL_SOCKET, SO_REUSEPORT, &l_iOpt2, sizeof(l_iOpt2)))
    {
        LOGPT(WERROR,
              "{} {} server reuse port error: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return false;
    }

    // 绑定端口
    if (bind(p_iFd, (sockaddr*)&p_sMyAddr, sizeof(sockaddr)) == -1)
    {
        LOGPT(WERROR,
              "{} {} server bind error: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return false;
    }

    int flags = fcntl(p_iFd, F_GETFL, 0);
    // 设置非阻塞方式
    if (fcntl(p_iFd, F_SETFL, flags | O_NONBLOCK) < 0)
    {
        LOGPT(WERROR,
              "{} {} server non-block: {}",
              WJLog::getWholeSysTime(),
              c_sPrintfStr_,
              strerror(errno));
        return false;
    }

    int res = connect(p_iFd, (struct sockaddr*)&p_sRemoteAddr, sizeof(p_sRemoteAddr));

    // 连接成功(服务器和客户端在同一台机器上时就有可能发生这种情况)
    if (res == -1)
    {
        // 以非阻塞的方式来进行连接的时候，返回-1,不代表一定连接错误，如果返回EINPROGRESS，代表连接还在进行中
        // 可以通过poll或者select来判断socket是否可写，如果可以写，说明连接完成了
        if (errno == EINPROGRESS)
        {
            fd_set writefds;
            FD_ZERO(&writefds);
            FD_SET(p_iFd, &writefds);

            struct timeval timeout;
            timeout.tv_sec = 3;
            timeout.tv_usec = 0;

            // 调用select来等待连接建立成功完成
            res = select(p_iFd + 1, NULL, &writefds, NULL, &timeout);
            if (res < 0)
            {
                LOGPT(WERROR,
                      "{} {} server TCP select: {}",
                      WJLog::getWholeSysTime(),
                      c_sPrintfStr_,
                      strerror(errno));
                close(p_iFd);
                p_iFd = -1;
                return false;
            }

            // 返回0,则表示建立连接超时;
            // 我们返回超时错误给用户，同时关闭连接，以防止三路握手操作继续进行
            if (res == 0)
            {
                LOGPT(WERROR,
                      "{} {} server TCP connect timeout",
                      WJLog::getWholeSysTime(),
                      c_sPrintfStr_);
                close(p_iFd);
                p_iFd = -1;
                return false;
            }
            else
            {
                // 返回大于0的值,则需要检查套接口描述符是否可读或可写;
                if (!FD_ISSET(p_iFd, &writefds))
                {
                    LOGPT(WERROR,
                          "{} {} server no events found!",
                          WJLog::getWholeSysTime(),
                          c_sPrintfStr_);
                    close(p_iFd);
                    p_iFd = -1;
                    return false;
                }
                else
                {
                    // 套接口描述符可读或可写,通过调用getsockopt得到套接口上待处理的错误(SO_ERROR)
                    // err 0-建立成功
                    int err = 0;
                    socklen_t elen = sizeof(err);
                    res = getsockopt(p_iFd, SOL_SOCKET, SO_ERROR, (char*)&err, &elen);
                    if (res < 0)
                    {
                        LOGPT(WERROR,
                              "{} {} server TCP getsockopt: {}",
                              WJLog::getWholeSysTime(),
                              c_sPrintfStr_,
                              strerror(errno));
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    if (err != 0)
                    {
                        // printf("TCP connect failed with the error: (%d)%s\n", err,
                        // strerror(err));
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    else
                        return true;
                }
            }
        }
        else
        {
            LOGPT(WERROR,
                  "{} {} server connectTcp error: {}",
                  WJLog::getWholeSysTime(),
                  c_sPrintfStr_,
                  strerror(errno));
            return false;
        }
    }
    else
        return true;
}
