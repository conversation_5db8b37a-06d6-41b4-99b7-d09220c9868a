/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON>
 * @Date: 2021-05-17 11:01:51
 * @LastEditors: zushu<PERSON>
 * @LastEditTime: 2021-07-19 15:31:47
 */
#include "algorithm/loop/closureLoopNew.h"
#include "omp.h"
#include <gtsam/inference/Key.h>
#include <gtsam/slam/BetweenFactor.h>
#include <gtsam/slam/PriorFactor.h>

namespace wj_slam {

template <typename PT> clousureLoop<PT>::clousureLoop()
{
    c_bDebug_ = false;

    // 加载icp优化类
    opt = new icpOptimization<PT>();

    // 初始化isam
    gtsam::ISAM2Params isam_parameters;
    isam_parameters.relinearizeThreshold = 0.01;
    isam_parameters.relinearizeSkip = 3;
    c_isam_ = new gtsam::ISAM2(isam_parameters);

    // 注册点云
    c_src_.reset();
    c_trg_.reset();
}
template <typename PT> clousureLoop<PT>::~clousureLoop()
{
    delete c_isam_;
    delete opt;
}

template <typename PT>
void clousureLoop<PT>::renewPoseGraph(int p_iFrameID,
                                      Eigen::Quaterniond p_quat,
                                      Eigen::Vector3d p_trans,
                                      float p_fVariance[6])
{
    // gtSAMgraph是新加到系统中的因子
    static gtsam::NonlinearFactorGraph gtSAMgraph;
    // initialEstimate是加到系统中的新变量的初始点
    static gtsam::Values initialEstimate;

    static gtsam::noiseModel::Diagonal::shared_ptr odometryNoise;
    static gtsam::Vector Vector6(6);
    static gtsam::Pose3 poseFrom;
    static int poseFromID = 0;

    std::cout<<"find bug: closureLoopNew.cpp renewPoseGraph" << std::endl;
    // 新帧的位姿
    gtsam::Pose3 poseTo = getPose3_(p_quat, p_trans);
    // 新帧的噪声
    Vector6 << p_fVariance[0], p_fVariance[1], p_fVariance[2], p_fVariance[3], p_fVariance[4],
        p_fVariance[5];
    odometryNoise = gtsam::noiseModel::Diagonal::Variances(Vector6);
    if (p_iFrameID == 0)
    {
        // NonlinearFactorGraph增加一个PriorFactor因子
        gtSAMgraph.add(gtsam::PriorFactor<gtsam::Pose3>(p_iFrameID, poseTo, odometryNoise));
    }
    else
    {
        // add constraints
        gtSAMgraph.add(gtsam::BetweenFactor<gtsam::Pose3>(
            poseFromID, p_iFrameID, poseFrom.between(poseTo), odometryNoise));
    }
    std::cout<<"find bug: closureLoopNew.cpp renewPoseGraph finished" << std::endl;
    initialEstimate.insert(p_iFrameID, poseTo);
    c_isam_->update(gtSAMgraph, initialEstimate);
    c_isam_->update();
    gtSAMgraph.resize(0);
    initialEstimate.clear();

    // calculateEstimate(p_iFrameID, p_newFramePose);

    // 准备下一次的数据
    poseFrom = poseTo;
    poseFromID = p_iFrameID;
}

template <typename PT>
void clousureLoop<PT>::setInputSource(int p_iFrameID,
                                      Eigen::Quaterniond p_quat,
                                      Eigen::Vector3d p_trans,
                                      PCRaw_PTR p_ori1,
                                      PCRaw_PTR p_ori2)
{
    c_src_.m_iID = p_iFrameID;
    c_src_.m_stPose.m_quat = p_quat;
    c_src_.m_stPose.m_trans = p_trans;
    *c_src_.m_PCfirst = *p_ori1;
    *c_src_.m_PCsecond = *p_ori2;
}

template <typename PT>
void clousureLoop<PT>::setInputTarget(int p_iFrameID,
                                      Eigen::Quaterniond p_quat,
                                      Eigen::Vector3d p_trans,
                                      PCRaw_PTR p_ori1,
                                      PCRaw_PTR p_ori2)
{
    c_trg_.m_iID = p_iFrameID;
    c_trg_.m_stPose.m_quat = p_quat;
    c_trg_.m_stPose.m_trans = p_trans;
    *c_trg_.m_PCfirst = *p_ori1;
    *c_trg_.m_PCsecond = *p_ori2;
}

template <typename PT> bool clousureLoop<PT>::align()
{
    st_Pose l_transform, l_newPose;
    float p_fVariance[6];
    for (float& var : p_fVariance)
        var = 0.01;
    mergCloud_(c_src_.m_PCfirst, c_src_.m_PCsecond, c_src_.m_PCRaw);
    mergCloud_(c_trg_.m_PCfirst, c_trg_.m_PCsecond, c_trg_.m_PCRaw);

    if (opt->optimize(
            c_src_.m_PCRaw, c_trg_.m_PCRaw, l_transform.m_quat, l_transform.m_trans, p_fVariance))
    {
        if (c_bDebug_)
            fprintf(stderr,
                    "old=[%.3f,%.3f,%.3f]\t",
                    c_src_.m_stPose.m_trans[0],
                    c_src_.m_stPose.m_trans[1],
                    c_src_.m_stPose.m_trans[2]);
        calcCorrectFramePose_(c_src_.m_stPose, l_transform, l_newPose);
        if (c_bDebug_)
            fprintf(stderr,
                    "new=[%.3f,%.3f,%.3f]\n",
                    l_newPose.m_trans[0],
                    l_newPose.m_trans[1],
                    l_newPose.m_trans[2]);
        addConstraintToGraph_(c_src_.m_iID, c_trg_.m_iID, l_newPose, c_trg_.m_stPose, p_fVariance);
        return true;
    }
    return false;
}

template <typename PT>
void clousureLoop<PT>::setOriKeyFrame(int p_iFrameID,
                                      Eigen::Quaterniond p_oriQuat,
                                      Eigen::Vector3d p_oriTrans,
                                      PCRaw_PTR p_ori1,
                                      PCRaw_PTR p_ori2,
                                      PCRaw_PTR p_ori3)
{
    c_renewOri_.m_iID = p_iFrameID;
    c_renewOri_.m_stPose.m_quat = p_oriQuat;
    c_renewOri_.m_stPose.m_trans = p_oriTrans;
    c_renewOri_.m_PCfirst = p_ori1;
    c_renewOri_.m_PCsecond = p_ori2;
    c_renewOri_.m_PCRaw = p_ori3;
}
template <typename PT>
void clousureLoop<PT>::setOriKeyFrame(int p_iFrameID,
                                      Eigen::Quaterniond p_oriQuat,
                                      Eigen::Vector3d p_oriTrans,
                                      PCRaw_PTR p_ori1,
                                      PCRaw_PTR p_ori2)
{
    c_renewOri_.m_iID = p_iFrameID;
    c_renewOri_.m_stPose.m_quat = p_oriQuat;
    c_renewOri_.m_stPose.m_trans = p_oriTrans;
    c_renewOri_.m_PCfirst = p_ori1;
    c_renewOri_.m_PCsecond = p_ori2;
}

template <typename PT>
void clousureLoop<PT>::getRenewKeyFrame(Eigen::Quaterniond& p_newQuat,
                                        Eigen::Vector3d& p_newTrans,
                                        PCRaw_PTR& p_new1,
                                        PCRaw_PTR& p_new2,
                                        PCRaw_PTR& p_new3)
{
    PCRaw_PTR l_new1(new PointCloudRaw());
    PCRaw_PTR l_new2(new PointCloudRaw());
    p_new1 = l_new1;
    p_new2 = l_new2;

    std::cout <<"find bug: closureLoopNew.hpp getRenewKeyFrame" << std::endl;
    // 提取新位姿
    st_Pose l_newPose;
    // Eigen::Matrix4d l_newMatrix = c_isamEstimate_.at<gtsam::Pose3>(c_renewOri_.m_iID).matrix();
    // toPoseStruct_(l_newPose, l_newMatrix.cast<double>());
    toPoseStruct_(l_newPose, c_isamEstimate_.at<gtsam::Pose3>(c_renewOri_.m_iID).matrix());
    p_newQuat = l_newPose.m_quat;
    p_newTrans = l_newPose.m_trans;
    // 旧位姿到新位姿的转移
    st_Pose l_transform;
    l_transform.m_quat =
        (l_newPose.m_quat * (c_renewOri_.m_stPose.m_quat.inverse().normalized())).normalized();
    l_transform.m_trans = l_newPose.m_trans - l_transform.m_quat * c_renewOri_.m_stPose.m_trans;
    // transFormCloud
    transFormCloud_MThread(c_renewOri_.m_PCfirst, p_new1, l_transform);
    transFormCloud_MThread(c_renewOri_.m_PCsecond, p_new2, l_transform);

    if (c_renewOri_.m_PCRaw && !c_renewOri_.m_PCRaw->empty())
    {
        PCRaw_PTR l_new3(new PointCloudRaw());
        p_new3 = l_new3;
        transFormCloud_MThread(c_renewOri_.m_PCRaw, p_new3, l_transform);
    }
}
template <typename PT>
void clousureLoop<PT>::getRenewKeyFrame(Eigen::Quaterniond& p_newQuat,
                                        Eigen::Vector3d& p_newTrans,
                                        PCRaw_PTR& p_new1,
                                        PCRaw_PTR& p_new2)
{
    PCRaw_PTR l_new1(new PointCloudRaw());
    PCRaw_PTR l_new2(new PointCloudRaw());
    p_new1 = l_new1;
    p_new2 = l_new2;

    // 提取新位姿
    st_Pose l_newPose;
    std::cout <<"find bug: closureLoopNew.hpp getRenewKeyFrame2" << std::endl;
    // Eigen::Matrix4d l_newMatrix = c_isamEstimate_.at<gtsam::Pose3>(c_renewOri_.m_iID).matrix();
    // toPoseStruct_(l_newPose, l_newMatrix.cast<double>());
    toPoseStruct_(l_newPose, c_isamEstimate_.at<gtsam::Pose3>(c_renewOri_.m_iID).matrix());
    p_newQuat = l_newPose.m_quat;
    p_newTrans = l_newPose.m_trans;
    // 旧位姿到新位姿的转移
    st_Pose l_transform;
    l_transform.m_quat =
        (l_newPose.m_quat * (c_renewOri_.m_stPose.m_quat.inverse().normalized())).normalized();
    l_transform.m_trans = l_newPose.m_trans - l_transform.m_quat * c_renewOri_.m_stPose.m_trans;
    // transFormCloud
    transFormCloud_MThread(c_renewOri_.m_PCfirst, p_new1, l_transform);
    transFormCloud_MThread(c_renewOri_.m_PCsecond, p_new2, l_transform);
}

template <typename PT>
void clousureLoop<PT>::calculateEstimate_(int p_iFrameID, st_Pose& p_newFramePose)
{
    gtsam::Values currentEstimate = c_isam_->calculateEstimate();
    std::cout <<"find bug: closureLoopNew.hpp calculateEstimate_" << std::endl;
    gtsam::Pose3 latestEstimate = currentEstimate.at<gtsam::Pose3>(currentEstimate.size() - 1);
    toPoseStruct_(p_newFramePose, latestEstimate.matrix());
}

template <typename PT>
void clousureLoop<PT>::mergCloud_(PCRaw_PTR p_src1, PCRaw_PTR p_src2, PCRaw_PTR p_trg)
{
    *p_trg = *p_src1;
    *p_trg += *p_src2;
}

template <typename PT>
void clousureLoop<PT>::calcCorrectFramePose_(st_Pose& p_stOldTf,
                                             st_Pose& p_transform,
                                             st_Pose& p_stNewTf)
{
    p_stNewTf.m_quat = p_transform.m_quat * p_stOldTf.m_quat;
    p_stNewTf.m_trans = p_transform.m_quat * p_stOldTf.m_trans + p_transform.m_trans;
}

template <typename PT>
void clousureLoop<PT>::addConstraintToGraph_(int p_iFromID,
                                             int p_iToID,
                                             st_Pose& p_stFromTf,
                                             st_Pose& p_stToTf,
                                             float p_fVariance[6])
{
    // gtSAMgraph是新加到系统中的因子
    static gtsam::NonlinearFactorGraph gtSAMgraph;

    static gtsam::noiseModel::Diagonal::shared_ptr odometryNoise;
    static gtsam::Vector Vector6(6);

    gtsam::Pose3 poseFrom = getPose3_(p_stFromTf.m_quat, p_stFromTf.m_trans);
    gtsam::Pose3 poseTo = getPose3_(p_stToTf.m_quat, p_stToTf.m_trans);

    Vector6 << p_fVariance[0], p_fVariance[1], p_fVariance[2], p_fVariance[3], p_fVariance[4],
        p_fVariance[5];
    odometryNoise = gtsam::noiseModel::Diagonal::Variances(Vector6);

    // add constraints
    gtSAMgraph.add(gtsam::BetweenFactor<gtsam::Pose3>(
        p_iFromID, p_iToID, poseFrom.between(poseTo), odometryNoise));
    c_isam_->update(gtSAMgraph);
    c_isam_->update();
    gtSAMgraph.resize(0);

    c_isamEstimate_ = c_isam_->calculateEstimate();
}

template <typename PT>
void clousureLoop<PT>::transFormCloud(PCRaw_PTR p_src, PCRaw_PTR p_trg, st_Pose& p_stTransform)
{
    Eigen::Vector3d point_c;
    Eigen::Vector3d point_w;

    int cloudSize = p_src->points.size();
    p_trg->resize(cloudSize);

    for (int i = 0; i < cloudSize; ++i)
    {
        point_c.x() = p_src->points[i].x;
        point_c.y() = p_src->points[i].y;
        point_c.z() = p_src->points[i].z;
        point_w = p_stTransform.m_quat * point_c + p_stTransform.m_trans;
        p_trg->points[i].x = point_w.x();
        p_trg->points[i].y = point_w.y();
        p_trg->points[i].z = point_w.z();
    }
}

template <typename PT>
void clousureLoop<PT>::transFormCloud_MThread(PCRaw_PTR p_src,
                                              PCRaw_PTR p_trg,
                                              st_Pose& p_stTransform)
{
    Eigen::Vector3d point_c;
    Eigen::Vector3d point_w;

    int cloudSize = p_src->points.size();
    p_trg->resize(cloudSize);

#pragma omp parallel for num_threads(2) private(point_c, point_w)
    for (int i = 0; i < cloudSize; ++i)
    {
        point_c.x() = p_src->points[i].x;
        point_c.y() = p_src->points[i].y;
        point_c.z() = p_src->points[i].z;
        point_w = p_stTransform.m_quat * point_c + p_stTransform.m_trans;
        p_trg->points[i].x = point_w.x();
        p_trg->points[i].y = point_w.y();
        p_trg->points[i].z = point_w.z();
    }
}

}  // namespace wj_slam

// 显式实例化
template class wj_slam::clousureLoop<pcl::PointXYZ>;