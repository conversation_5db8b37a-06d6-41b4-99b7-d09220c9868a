#include "algorithm/imu/imuFilter.h"
#include "wj_log.h"
namespace wj_slam {
imuFilter::imuFilter(/* args */)
    : hasInit_(false), m_iBiasCount_(120), m_iStaticCount_(0), m_iMoveCount_(0), m_iReBiasCount_(0),
      m_iListMaxLen_(500) /*50帧信息*/
{
    m_afAccCorrect[0] = 1.005295;
    m_afAccCorrect[1] = 0.067525;
    m_afAccCorrect[2] = 0.998810;
    m_afAccCorrect[3] = -0.107807;
    m_afAccCorrect[4] = 1.005699;
    m_afAccCorrect[5] = 0.255329;
    m_vAngularVelNoise_ << 0.005, 0.007, 0.004;
    m_iReBiasMax_ = 10;
}

imuFilter::~imuFilter()
{
    hasInit_ = false;
    std::lock_guard<std::mutex> loc(m_ListMtx_);
    m_qIMUList_.clear();
}
void imuFilter::inputRawData(const IMUData& datain)
{
    m_currData_ = datain;

    IMUDataCorr_(m_currData_);
    if (hasInit_)
    {
        double l_detetTime = (m_currData_.imuTime() - m_lastData_.imuTime()) / 1000.0;

        m_CFilter_.update(m_currData_.ax(),
                          m_currData_.ay(),
                          m_currData_.az(),
                          m_currData_.wx(),
                          m_currData_.wy(),
                          m_currData_.wz(),
                          (float)l_detetTime,
                          m_currData_.quat().w(),
                          m_currData_.quat().x(),
                          m_currData_.quat().y(),
                          m_currData_.quat().z());
        m_CFilter_.getOrientation(m_currData_.quat().w(),
                                  m_currData_.quat().x(),
                                  m_currData_.quat().y(),
                                  m_currData_.quat().z());
        std::lock_guard<std::mutex> loc(m_ListMtx_);
        {
            if (m_qIMUList_.size() >= m_iListMaxLen_)
            {
                m_qIMUList_.pop_front();
            }
            m_qIMUList_.emplace_back(m_currData_);
        }
    }
    else
    {
        calculateBias_(m_currData_);
    }
    m_lastData_ = m_currData_;
}
void imuFilter::IMUZeroCorr_(IMUData& datain)
{
    // 静止零偏修正完事后,才能做零偏修正
    if (hasInit_)
        datain = datain - m_bias;
}
void imuFilter::IMUTempCorr_(IMUData& datain) {}
void imuFilter::IMUAccCorr_(IMUData& datain)
{
    float ax, ay, az;
    ax = datain.ax();
    ay = datain.ay();
    az = datain.az();
    ax = ax * m_afAccCorrect[0] - m_afAccCorrect[1];
    ay = ay * m_afAccCorrect[2] - m_afAccCorrect[3];
    az = az * m_afAccCorrect[4] - m_afAccCorrect[5];
    datain.linearAcceleration() << ax, ay, az;
}
void imuFilter::IMUDataCorr_(IMUData& datain)
{
    IMUTempCorr_(datain);
    IMUAccCorr_(datain);
    IMUZeroCorr_(datain);
}
bool imuFilter::getAngularTwist(Eigen::Vector3d& p_vOut,
                                const Time& p_time,
                                const Time& p_iTimePrecision)
{
    if (!hasInit_)
    {
        p_vOut = Eigen::Vector3d::Zero();
        return false;
    }
    bool res = false;
    IMUData l_findEndData, l_findStartData;
    IMUData l_lastestImuData;
    /** @todo 此处需要增加更复杂逻辑,时间可能早也可能晚,找不到数据返回false*/
    std::lock_guard<std::mutex> loc(m_ListMtx_);
    {
        if (m_qIMUList_.empty())
        {
            p_vOut = Eigen::Vector3d::Zero();
            return false;
        }
        l_lastestImuData = *m_qIMUList_.rbegin();
        for (std::list<IMUData>::reverse_iterator it = m_qIMUList_.rbegin();
             it != m_qIMUList_.rend();
             ++it)
        {
            IMUData& l_imuData = *it;
            if (std::fabs(l_imuData.syncTime() - (p_time + 90)) < p_iTimePrecision)
            {
                l_findEndData = *it;
                l_findEndData.setStatus(IMUData::IMUStatus::QuatValid);
            }
            if (std::fabs(l_imuData.syncTime() - (p_time)) < p_iTimePrecision)
            {
                l_findStartData = *it;
                l_findStartData.setStatus(IMUData::IMUStatus::QuatValid);
                break;  //反向查找,因此找到头时间后,可以退出
            }
        }
    }
    if ((l_findStartData.status() != IMUData::IMUStatus::NoValid)
        && (l_findEndData.status() != IMUData::IMUStatus::NoValid))
    {
        //使用帧间增量作为载体系的运动速度,因为IMU测量的角速度为i系下角速度
        s_TWIST twist;
        Eigen::Quaterniond quat = l_findStartData.quat().inverse() * l_findEndData.quat();
        float timediff = l_findEndData.syncTime() - l_findStartData.syncTime();
        twist.setQuat(quat);
        // @attention 100.0ms为速度单位.
        twist = twist * (100.0 / timediff);
        // 输出转为rad/s
        p_vOut[0] = twist.roll() / 18.0 * M_PI;
        p_vOut[1] = twist.pitch() / 18.0 * M_PI;
        p_vOut[2] = twist.yaw() / 18.0 * M_PI;
        res = true;
    }
    else
    {
        p_vOut = Eigen::Vector3d::Zero();
        LOGW(
            WWARN,
            "[{}]Get Speed Error. syncTime: {:.3f}, syncPrecs: {:.3f}, lastestTime: {:.3f}. {}, {}",
            WJLog::getWholeSysTime(),
            p_time,
            p_iTimePrecision,
            l_lastestImuData.syncTime(),
            l_findStartData.status(),
            l_findEndData.status());
        res = false;
    }
    return res;
}
bool imuFilter::getIMUData(IMUData& p_IMUOut,
                           const Time& p_time,
                           const Time& p_iTimePrecision,
                           const bool& isClear)
{
    if (!hasInit_)
    {
        p_IMUOut.reset();
        return false;
    }
    bool res = false;
    /** @todo 此处需要增加更复杂逻辑,时间可能早也可能晚,找不到数据返回false*/
    std::lock_guard<std::mutex> loc(m_ListMtx_);
    {
        std::list<IMUData>::iterator deleteIt;
        IMUData& l_imuData = *m_qIMUList_.begin();
        for (std::list<IMUData>::iterator it = m_qIMUList_.begin(); it != m_qIMUList_.end(); ++it)
        {
            l_imuData = *it;
            // ms 单位
            if (std::fabs(l_imuData.syncTime() - p_time) < (p_iTimePrecision * 1))
            {
                p_IMUOut = *it;
                m_lastGetData_ = p_IMUOut;
                deleteIt = it;
                p_IMUOut.setStatus(IMUData::IMUStatus::QuatValid);
                res = true;
                break;
            }
        }
        if (isClear && res)
        {
            m_qIMUList_.erase(m_qIMUList_.begin(), deleteIt);
        }
        if (!res)
        {
            LOGFAE(WERROR,
                   "[{}] Get IMU Data Error, syncTime: {:.3f}ms, ImuLatestTime: {:.3f}ms, "
                   "syncPrecs: {}ms",
                   WJLog::getWholeSysTime(),
                   p_time,
                   l_imuData.syncTime(),
                   10);
            p_IMUOut.setStatus(IMUData::IMUStatus::BiasDone);
        }
    }
    return res;
}
const IMUData imuFilter::bias() const
{
    return this->m_bias;
}
IMUData& imuFilter::bias()
{
    return this->m_bias;
}
void imuFilter::setBias(const IMUData& data)
{
    m_bias = data;
    m_bias.setStatus(IMUData::IMUStatus::BiasDone);
    hasInit_ = true;
}
void imuFilter::setAngularVelNoise(const Eigen::Vector3d& noise)
{
    this->m_vAngularVelNoise_ = noise;
}
void imuFilter::calculateBias_(const IMUData& datain)
{
    if (hasInit_)
        return;
    // 若修了10次都没修过,则停止.
    if (m_iReBiasCount_ > m_iReBiasMax_)
        return;

    bool isStatic = false;
    if (0 == m_iStaticCount_)
        isStatic = true;
    else
        isStatic = IMUStaticCheck_(datain);

    if (isStatic)
    {
        if (0 == m_iStaticCount_)
        {
            m_bias.reset();
            m_bias.angularVelocity() = datain.angularVelocity();
            LOGFAE(WINFO,
                   "[{}] IMU Bias Correct Start. AngularVelBias(rad/s): {:.3f}, {:.3f}, {:.3f}.",
                   WJLog::getWholeSysTime(),
                   m_bias.wx(),
                   m_bias.wy(),
                   m_bias.wz());
        }
        else
        {
            m_bias.angularVelocity() +=
                (datain.angularVelocity() - m_bias.angularVelocity()) / m_iStaticCount_;
        }
        m_iStaticCount_++;
        m_iMoveCount_ = 0;
        if (m_iStaticCount_ >= m_iBiasCount_)
        {
            m_bias.setStatus(IMUData::IMUStatus::BiasDone);
            LOGFAE(WINFO,
                   "[{}] IMU Bias Correct Done. AngularVelBias(rad/s): {:.3f}, {:.3f}, {:.3f}.",
                   WJLog::getWholeSysTime(),
                   m_bias.wx(),
                   m_bias.wy(),
                   m_bias.wz());
            hasInit_ = true;
        }
    }
    else
    {
        //连续统计超过一定次数就认为标定失败,暂定2帧点云数据
        m_iMoveCount_++;
        if (m_iMoveCount_ >= 20)
        {
            LOGFAE(WERROR,
                   "[{}] IMU Bias Correct Error. status: {}, ReBiasCount: {}",
                   WJLog::getWholeSysTime(),
                   m_bias.status(),
                   m_iReBiasCount_);
            if (m_iReBiasCount_ >= m_iReBiasMax_)
            {
                LOGFAE(WERROR,
                       "[{}] IMU Bias Correct Error. Please Stay And Restart SLAM.",
                       WJLog::getWholeSysTime());
            }
            m_iReBiasCount_++;
            m_iStaticCount_ = 0;
        }
    }
    return;
}
bool imuFilter::IMUStaticCheck_(const IMUData& datain)
{
    double acc_magnitude = datain.linearAcceleration().norm();

    if (fabs(acc_magnitude - m_Gravity_) > 0.2)
    {
        return false;
    }
    //按照720F内部IMU调参
    if (fabs(datain.wx() - m_bias.wx()) < m_vAngularVelNoise_.x()
        && fabs(datain.wy() - m_bias.wy()) < m_vAngularVelNoise_.y()
        && fabs(datain.wz() - m_bias.wz()) < m_vAngularVelNoise_.z())
    {
        return true;
    }
    return false;
}

}  // namespace wj_slam