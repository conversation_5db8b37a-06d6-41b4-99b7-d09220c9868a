#pragma once
#define __EDGE_LIST_C

#include "algorithm/location/mark_location/mark/EdgeList.h"
#include "algorithm/location/mark_location/mark/ExSram.h"
#include "algorithm/location/mark_location/mark/Marks.h"
#include "math.h"
extern sSysPib g_sSysPib;
extern STRUCT_MarkMatch g_sMarkMatch_Set;
extern u16 g_u16EdgeDiff;
EX_SRAM_EXT u32 g_au32TanList_Offset[6][2];
EX_SRAM_EXT u32 g_au32PulseSta[CHANNELSIZE];  //(CCM_RAM);
#define EdgeList_Mul 4096
void Build_EdgeList(void)
{
    u16 i;
    u16 cnt = 0;
    u16 pack_cnt = 0;
    u16 offset;
    u32 last_dist = EdgeList_Mul;
    //	float l_u16MaxAng[6] = {60,70,80,85,89,89.8998};
    //	u16 l_u16Res[6] = {1,4,16,64,512,4096};
    float l_u16MaxAng[3] = {60, 80, 89.3};
    u16 l_u16Res[3] = {1, 4, 64};
    u32 flag = 1;
    //__set_PRIMASK(1);
    // Flash_EraseSector_User(ADDR_FLASH_TANLIST_DATA);
    for (i = 0; i < 3; i++)
        last_dist = Build_EdgeList_DiffResolution(
            &pack_cnt, &cnt, last_dist, i, l_u16MaxAng[i], l_u16Res[i]);
    if (cnt % 300 != 0)
    {
        // Flash_Write_Word_NoErase(ADDR_FLASH_TANLIST_DATA + pack_cnt * 2400 ,
        // (cnt%300)*2,g_au32PulseSta);
    }
    // Flash_Write_Word_NoErase(ADDR_FLASH_TANLIST_DATA - 4 , 1,&flag);
    // Flash_Write_Word_NoErase(ADDR_FLASH_TANLIST_START, 12,&g_au32TanList_Offset[0][0]);
    //__set_PRIMASK(0);
}

u32 Build_EdgeList_DiffResolution(u16* p_pack_cnt,
                                  u16* p_cnt,
                                  u32 p_last_dist,
                                  u8 ResolutionOffset,
                                  float MaxAng,
                                  u16 p_Res)
{
    u16 i;
    u16 offset;
    float l_f32ang;
    for (i = 0; i < 50000; i++)
    {
        offset = ((*p_cnt) % 300) << 1;
        if (ResolutionOffset == 0)
        {
            g_au32PulseSta[offset] = p_last_dist + p_Res * i;
            g_au32PulseSta[offset + 1] =
                sqrt((u64)g_au32PulseSta[offset] * (u64)g_au32PulseSta[offset]
                     + (u64)EdgeList_Mul * (u64)EdgeList_Mul);  //;atan2(g_au32PulseSta[offset]
                                                                //,EdgeList_Mul)/M_PI*180;
        }
        else
        {
            g_au32PulseSta[offset] = p_last_dist + p_Res * (i + 1);
            g_au32PulseSta[offset + 1] =
                sqrt((u64)g_au32PulseSta[offset] * (u64)g_au32PulseSta[offset]
                     + (u64)EdgeList_Mul * (u64)EdgeList_Mul);  //;atan2(g_au32PulseSta[offset]
                                                                //,EdgeList_Mul)/M_PI*180;
        }
        l_f32ang = atan2(g_au32PulseSta[offset], EdgeList_Mul) / M_PI * 180;
        (*p_cnt)++;
        if ((*p_cnt) % 300 == 0)
        {
            // Flash_Write_Word_NoErase(ADDR_FLASH_TANLIST_DATA + (*p_pack_cnt) * 2400 ,
            // 600,g_au32PulseSta);
            (*p_pack_cnt)++;
        }
        if (l_f32ang > MaxAng)
            break;
    }
    if (g_sSysPib.m_u16WDogConfig == 1)
    {
        // IWDG_ReloadCounter();
    }
    g_au32TanList_Offset[ResolutionOffset][0] = *p_cnt - 1;
    g_au32TanList_Offset[ResolutionOffset][1] = g_au32PulseSta[offset];
    return g_au32PulseSta[offset];
}

#define Multi_List 12

// u8 Return_TanList_N(u32 data_in)
//{
//	if(data_in <= 7095)
//		return 1;
//	if(data_in <= 11255)
//		return 2;
//	if(data_in <= 23239)
//		return 3;
//	if(data_in <= 46855)
//		return 4;
//	if(data_in <= 234759)
//		return 5;
//	if(data_in <= 296199)
//		return 6;
//	return 7;
//
//}
// u32 FindList_Offset(u32 pAddr,u32 data_in)
//{
//	u32 l_u32Ret;
//	switch(Return_TanList_N(data_in))
//	{
//		case 1:
//			l_u32Ret = pAddr+((data_in - 4096)<<3);
//		break;
//		case 2:
//			l_u32Ret = pAddr+(((data_in - 7095)<<3)>>2) + g_au32TanList_Offset[0][0];
//		break;
//		case 3:
//			l_u32Ret = pAddr+(((data_in - 11255)<<3)>>4) + g_au32TanList_Offset[1][0];
//		break;
//		case 4:
//			l_u32Ret = pAddr+(((data_in - 23239)<<3)>>6) + g_au32TanList_Offset[2][0];
//		break;
//		case 5:
//			l_u32Ret = pAddr+(((data_in - 46855)<<3)>>9) + g_au32TanList_Offset[3][0];
//		break;
//		case 6:
//			l_u32Ret = pAddr+(((data_in - 234759)<<3)>>12) + g_au32TanList_Offset[4][0];
//		break;
//		default:
//			l_u32Ret = pAddr + (5539 <<3);
//		break;
//
//	}
//	return l_u32Ret;
//}

// u32 FindList_Offset1(u32 pAddr,u32 data_in)
//{
//
//	if(data_in <= g_au32TanList_Offset[0][1])
//		return pAddr+((data_in - 4096)<<3);
//
//	if(data_in <= g_au32TanList_Offset[1][1])
//		return pAddr+(((data_in - g_au32TanList_Offset[0][1] + 2 )>>2)<<3) +
// g_au32TanList_Offset[0][0];
//
//	if(data_in <= g_au32TanList_Offset[2][1])
//		return pAddr+(((data_in - g_au32TanList_Offset[1][1] + 8)>>4)<<3) +
// g_au32TanList_Offset[1][0];
//
//	if(data_in <= g_au32TanList_Offset[3][1])
//		return pAddr+(((data_in - g_au32TanList_Offset[2][1] + 32)>>6)<<3) +
// g_au32TanList_Offset[2][0];
//
//	if(data_in <= g_au32TanList_Offset[4][1])
//		return pAddr+(((data_in - g_au32TanList_Offset[3][1] + 256)>>9)<<3) +
// g_au32TanList_Offset[3][0];
//
//	if(data_in <= 296199)
//		return pAddr+(((data_in - g_au32TanList_Offset[4][1] + 2048)>>12)<<3) +
// g_au32TanList_Offset[4][0];
//
//	return pAddr + (5539 <<3);
//}

u32 FindList_Offset1(u32 pAddr, u32 data_in)
{
    if (data_in <= g_au32TanList_Offset[0][1])
        return pAddr + ((data_in - 4096) << 3);

    if (data_in <= g_au32TanList_Offset[1][1])
        return pAddr
               + ((((data_in - g_au32TanList_Offset[0][1] + 2) >> 2) + g_au32TanList_Offset[0][0])
                  << 3);

    if (data_in <= g_au32TanList_Offset[2][1])
        return pAddr
               + ((((data_in - g_au32TanList_Offset[1][1] + 32) >> 6) + g_au32TanList_Offset[1][0])
                  << 3);

    return pAddr + (11909 << 3);
}
u8 Return_Slope(u32 in1, u32 in2, u32* slope)
{
    if (in1 > in2)
    {
        *slope = (in1 << Multi_List) / in2;
        if (in2 == 0)
        {
            in2 = in2;
            return 2;
        }
        return 1;
    }
    if (in1 < in2)
    {
        *slope = (in2 << Multi_List) / in1;
        if (in1 == 0)
        {
            in1 = in1;
            return 2;
        }
        return 0;
    }
    else
        *slope = 1 << Multi_List;
    return 1;
}
u32 Return_Diff(int in1, int in2)
{
    //	if(in1 > in2)
    //		return in1 - in2;
    //	else
    //		return in2 - in1;
    return (in1 > in2) ? (in1 - in2) : (in2 - in1);
}
u32 Return_Diff_u32(u32 in1, u32 in2)
{
    //	if(in1 > in2)
    //		return in1 - in2;
    //	else
    //		return in2 - in1;
    return (in1 > in2) ? (in1 - in2) : (in2 - in1);
}
u32 Return_Slope1(u32 in1, u32 in2, u32* Edge_UsedtoCal)
{
    /*
    u32 slope;
    if(in1 > in2)
    {
        *Edge_UsedtoCal = (in1 + 8)>>Edge_Minification;
        if(in2 != 0)
        {
            slope= ((in1<<Multi_List)+ (in2>>1))/in2;
            return FindList_Offset1(ADDR_FLASH_TANLIST_DATA,slope);
        }
        else
        {
            return ADDR_FLASH_TANLIST_DATA + (11909 <<3);
        }
    }
    if(in1 < in2)
    {
        *Edge_UsedtoCal = (in2 + 8)>>Edge_Minification;
        if(in1 != 0)
        {
            slope= ((in2<<Multi_List) + (in1>>1))/in1;
            return FindList_Offset1(ADDR_FLASH_TANLIST_DATA,slope);
        }
        else
        {
            return ADDR_FLASH_TANLIST_DATA + (11909 <<3);
        }
    }
    else
    {
        *Edge_UsedtoCal = (in2 + 8)>>Edge_Minification;
        return ADDR_FLASH_TANLIST_DATA ;
    }
    */
    return 1;
}

u32 Cal_Edge_TwoMark(u32 p_Diff_X, u32 p_Diff_Y)
{
    return (u32)round(sqrt(pow(p_Diff_X, 2) + pow(p_Diff_Y, 2)));
    // u32 l_u32Cal_Edge;
    // u32 l_u32Edge_addr;
    // u32 l_u32tan[2];
    // l_u32Edge_addr = Return_Slope1(p_Diff_X,p_Diff_Y,&l_u32Cal_Edge);
    // //Flash_Read_Word(l_u32Edge_addr,2,&l_u32tan[0]);

    // return l_u32tan[1]*l_u32Cal_Edge/l_u32tan[0];
}

u32 Return_Diff_Edge(MARK_XY* p_Mark1, MARK_XY* p_Mark2, u16 edge)
{
    g_u16EdgeDiff = 120;
    u32 l_u32diff_x, l_u32diff_y;
    u32 l_u32Edge_addr;
    u32 l_u32SetEdge;
    u32 l_u32MarkScanRadio = g_sMarkMatch_Set.m_u32MarkScan_Max
                             << 1;  //+ (g_u16EdgeCalDiff<<Edge_Minification));
    u32 l_u32MarkScanRadio_Shrink =
        l_u32MarkScanRadio
        >> Edge_Minification;  //计算的边长进行了适当的缩放，缩放比例Edge_Minification
    l_u32diff_x = Return_Diff(p_Mark1->m_s32x, p_Mark2->m_s32x);

    l_u32diff_y = Return_Diff(p_Mark1->m_s32y, p_Mark2->m_s32y);

    if ((l_u32diff_x > l_u32MarkScanRadio)  //若x和y有一个大于探测直径 则距离一定大于探测直径
        || (l_u32diff_y > l_u32MarkScanRadio))
    {
        return g_u16EdgeDiff;
    }

    l_u32SetEdge = Cal_Edge_TwoMark(l_u32diff_x, l_u32diff_y);
    // l_u32SetEdge = sqrt(pow(l_u32diff_x,2) + pow(l_u32diff_y,2));
    if (l_u32SetEdge > l_u32MarkScanRadio)  //计算边长大于探测直径
    {
        return g_u16EdgeDiff;
    }

    // return Return_Diff_u32(edge,l_u32SetEdge);//abs(edge - l_u32SetEdge);
    return (edge > l_u32SetEdge) ? (edge - l_u32SetEdge) : (l_u32SetEdge - edge);
}
