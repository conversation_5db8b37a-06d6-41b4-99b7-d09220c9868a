#define __KDTREE_C
#include "algorithm/location/mark_location/mark/kdtree.h"
#include "algorithm/location/mark_location/mark/ExSram.h"
#include "algorithm/location/mark_location/mark/quicksort.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

extern STRUCT_MarkMatch g_sMarkMatch_Set;
EX_SRAM_EXT KD_NODE_MARK* g_kd_root;
;                                          //(CCM_RAM) ;
extern EX_SRAM_EXT MARK_SETS g_sMarkSets;  //(CCM_RAM);//
extern QUICKSORT_EXT MARK_XY* g_psStructMarkAddr[MAX_SIZE];
/*************************************************
*************************************************
*************************************************
*************************************************
            分割线  实际使用的函数
*************************************************
*************************************************
*************************************************
*************************************************/

KD_NODE_MARK* kdtree_build_Mark(MARK_XY* p_MarkSet, u16 p_n, MARK_XY* p_psStructMarkAddr[MAX_SIZE])
{
    KD_NODE_MARK* kd_root;

    //输入参数检查
    if (!p_MarkSet || p_n <= 0)
    {
        return NULL;
    }

    //调用函数，用给定的特征点集初始化k-d树节点，返回值作为树根
    kd_root = kd_node_init_Mark1(NULL, p_n, NULL, 0);
    //调用函数，扩展根节点kd_root及其左右孩子
    expand_kd_node_subtree_Mark(kd_root, p_psStructMarkAddr);

    return kd_root;
}

static KD_NODE_MARK*
kd_node_init_Mark1(MARK_XY* features, u16 n, KD_NODE_MARK* kd_node_Addr, int offset)
{
    KD_NODE_MARK* kd_node;
    kd_node = (KD_NODE_MARK*)malloc(sizeof(KD_NODE_MARK));  //分配内存
    memset(kd_node, 0, sizeof(KD_NODE_MARK));
    kd_node->ki = -1;              //枢轴索引
    kd_node->features = features;  //节点对应的特征点集
                                   //	kd_node->kd_father = kd_node_Addr;
    kd_node->n = n;                //特征点的个数
    kd_node->offset = offset;
    return kd_node;
}

void expand_kd_node_subtree_Mark(KD_NODE_MARK* kd_node, MARK_XY* p_psStructMarkAddr[])
{
    //基本情况：叶子节点
    /* base case: leaf node */
    if (kd_node->n == 1 || kd_node->n == 0)
    {
        kd_node->leaf = 1;  //叶节点标志位设为1
                            //		kd_node->features = g_psStructMarkAddr[kd_node->offset];
        return;
    }

    //调用函数，确定节点的枢轴索引和枢轴值
    assign_part_key_Mark1(kd_node, &p_psStructMarkAddr[kd_node->offset]);

    //继续扩展左右孩子
    if (kd_node->kd_left)
        expand_kd_node_subtree_Mark(kd_node->kd_left, p_psStructMarkAddr);
    if (kd_node->kd_right)
        expand_kd_node_subtree_Mark(kd_node->kd_right, p_psStructMarkAddr);
}
static double median_select_Mark(float* array, int n)
{
    //调用函数，找array数组中的第(n-1)/2小的数，即中值
    return rank_select_Mark(array, n, (n - 1) / 2);
}
static void insertion_sort_Mark(float* array, int n)
{
    float k;
    int i, j;

    for (i = 1; i < n; i++)
    {
        k = array[i];
        j = i - 1;
        while (j >= 0 && array[j] > k)
        {
            array[j + 1] = array[j];
            j -= 1;
        }
        array[j + 1] = k;
    }
}
static double rank_select_Mark(float* array, int n, int r)
{
    float *tmp_rank, med;
    int gr_5, gr_tot, rem_elts, i, j;

    /* base case */
    if (n == 1)
        return array[0];

    //将数组分成5个一组，共gr_tot组
    /* divide array into groups of 5 and sort them */
    gr_5 = n / 5;  //组的个数-1，n/5向下取整
    // gr_tot = n / 5.0 + 1; //组的个数，n/5向上取整
    rem_elts = n % 5;  //最后一组中的元素个数
    if (rem_elts)
        gr_tot = gr_5 + 1;
    else
        gr_tot = gr_5;
    tmp_rank = array;
    //对每组进行插入排序  ,第ki维升序排列
    for (i = 0; i < gr_5; i++)
    {
        insertion_sort_Mark(tmp_rank, 5);
        tmp_rank += 5;
    }
    //最后一组
    insertion_sort_Mark(tmp_rank, rem_elts);

    //找中值的中值
    /* recursively find the median of the medians of the groups of 5 */
    tmp_rank = (float*)calloc(gr_tot, sizeof(float));
    //将每个5元组中的中值(即下标为2,2+5,...的元素)复制到temp数组
    for (i = 0, j = 2; i < gr_5; i++, j += 5)
        tmp_rank[i] = array[j];
    //最后一组的中值
    if (rem_elts)
        tmp_rank[i++] = array[n - 1 - rem_elts / 2];
    //找temp中的中值med，即中值的中值
    med = rank_select_Mark(tmp_rank, i, (i - 1) / 2);
    free(tmp_rank);

    //利用中值的中值划分数组，看划分结果是否是第r小的数，若不是则递归调用rank_select重新选择
    /* partition around median of medians and recursively select if necessary */
    j = partition_array_Mark(array, n, med);  //划分数组，返回med在新数组中的索引
    if (r == j)                               //结果是第r小的数
        return med;
    else if (r < j)  //第r小的数在前半部分
        return rank_select_Mark(array, j, r);
    else  //第r小的数在后半部分
    {
        array += j + 1;
        return rank_select_Mark(array, (n - j - 1), (r - j - 1));
    }
}

static int partition_array_Mark(float* array, int n, float pivot)
{
    double tmp_part;
    int p, i, j;

    i = -1;
    for (j = 0; j < n; j++)
        if (array[j] <= pivot)
        {
            tmp_part = array[++i];
            array[i] = array[j];
            array[j] = tmp_part;
            if (array[i] == pivot)
                p = i;  // p保存枢轴的下标
        }
    //将枢轴和最后一个小于枢轴的数对换
    array[p] = array[i];
    array[i] = pivot;

    return i;
}

int Cal_Mean(int* p_Data, u16 n)
{
    int64_t sum = 0;
    int* l_ps32Data = p_Data;
    for (u16 i = 0; i < n; i++)
    {
        sum += *l_ps32Data;
        l_ps32Data += (STRUCT_SIZE_MARK_XY >> 2);
    }
    return sum / n;
}

int Cal_Variance(int* p_Data, int p_Mean, u16 n, int* kv)
{
    u64 sum = 0;
    u32 l_u32DiffMin = 0xffffffff;
    int* l_ps32Data = p_Data;
    u32 l_u32diff;
    for (u16 i = 0; i < n; i++)
    {
        l_u32diff = abs(*l_ps32Data - p_Mean);
        if (l_u32diff < l_u32DiffMin)
        {
            l_u32DiffMin = l_u32diff;
            *kv = *l_ps32Data;
        }
        sum += l_u32diff * l_u32diff;
        l_ps32Data += (STRUCT_SIZE_MARK_XY >> 2);
    }
    return sum / n;
}

int Cal_Mean1(MARK_XY** p_MarkAddr, u16 n, u8 axis)
{
    int64_t sum = 0;
    u16 i;

    if (axis)
    {
        for (i = 0; i < n; i++)
            sum += p_MarkAddr[i]->m_s32y;
    }
    else
    {
        for (i = 0; i < n; i++)
            sum += p_MarkAddr[i]->m_s32x;
    }

    return sum / n;
}
int Cal_Variance1(MARK_XY** p_MarkAddr, int p_Mean, u16 n, int* kv, u8 axis)
{
    u64 sum = 0;
    u32 l_u32DiffMin = 0xffffffff;
    u16 i;
    u32 l_u32diff;
    if (axis)
    {
        for (i = 0; i < n; i++)
        {
            l_u32diff = abs(p_MarkAddr[i]->m_s32y - p_Mean);
            if (l_u32diff < l_u32DiffMin)
            {
                l_u32DiffMin = l_u32diff;
                *kv = p_MarkAddr[i]->m_s32y;
            }
            sum += l_u32diff * l_u32diff;
        }
    }
    else
    {
        for (i = 0; i < n; i++)
        {
            l_u32diff = abs(p_MarkAddr[i]->m_s32x - p_Mean);
            if (l_u32diff < l_u32DiffMin)
            {
                l_u32DiffMin = l_u32diff;
                *kv = p_MarkAddr[i]->m_s32x;
            }
            sum += l_u32diff * l_u32diff;
        }
    }
    return sum / n;
}
void Find_MaxAxis(int* p_Data, u16 n, u32* p_MaxVar, u8* Max_axis, u8 axis, int* p_Kv)
{
    int l_s32Mean;
    u32 l_u32Var;
    l_s32Mean = Cal_Mean(p_Data, n);
    l_u32Var = Cal_Variance(p_Data, l_s32Mean, n, p_Kv);
    if (l_u32Var > *p_MaxVar)
    {
        *p_MaxVar = l_u32Var;
        *Max_axis = axis;
    }
}
void Find_MaxAxis1(MARK_XY** p_MarkAddr, u16 n, u32* p_MaxVar, u8* Max_axis, u8 axis, int* p_Kv)
{
    int l_s32Mean;
    u32 l_u32Var;
    l_s32Mean = Cal_Mean1(p_MarkAddr, n, axis);
    l_u32Var = Cal_Variance1(p_MarkAddr, l_s32Mean, n, p_Kv, axis);
    if (l_u32Var > *p_MaxVar)
    {
        *p_MaxVar = l_u32Var;
        *Max_axis = axis;
    }
}
// void assign_part_key_Mark(KD_NODE_MARK* kd_node)
//{
//	MARK_XY* l_psMark;
//	MARK_XY* l_psMark_Root;
//	//枢轴的值kv，均值mean，方差var，方差最大值var_max

//	int kv_x,kv_y,mean, x,var ;
//	u32 var_max = 0;
//	float* tmp_ass;
//	int d, n, i, j=0; //枢轴索引ki
//	u8 ki=0;
//	l_psMark = kd_node->features;
//	n = kd_node->n;//结点个数

//	//枢轴的索引值就是方差最大的那一维的维数,即n个128维的特征向量中，若第k维的方差最大，则k就是枢轴(分割位置)
//	/* partition key index is that along which descriptors have most variance */
//
//	Find_MaxAxis(&l_psMark->m_s32x,n,&var_max,&ki,0,&kv_x);
//	Find_MaxAxis(&l_psMark->m_s32y,n,&var_max,&ki,1,&kv_y);
//
//	//枢轴的值就是所有特征向量的ki维的中值(按ki维排序后中间的那个值)
//	/* partition key value is median of descriptor values at ki */
//	quicksort_Marks(l_psMark,0,n-1,ki);

//	kd_node->ki = ki;//枢轴的维数索引
//	if(ki)
//		kd_node->kv = kv_y;//枢轴的值
//	else
//		kd_node->kv = kv_x;//枢轴的值
//	for(i = 0; i < n ;i++)
//	{
//		if(Return_AxisData(ki,g_psStructMarkAddr[i]) > kd_node->kv)
//		{
//			j = i;
//			break;
//		}
//	}
//	if(j == (n-1))
//	{
//		kd_node->leaf =1;
//	}
//	else
//	{
//		if(j != 0)
//			l_psMark_Root = g_psStructMarkAddr[j - 1];
//		else
//			l_psMark_Root = g_psStructMarkAddr[0];
//	}

//	//初始化左孩子的根节点，左孩子共j+1个特征点
//	kd_node->kd_left = kd_node_init_Mark(l_psMark_Root, j );
//	//初始化右孩子的根节点，右孩子共n-j-1个特征点
//	kd_node->kd_right = kd_node_init_Mark(l_psMark_Root, (n - j - 1));
//}

void assign_part_key_Mark1(KD_NODE_MARK* kd_node, MARK_XY** p_MarkAddr)
{
    MARK_XY* l_psMark;
    MARK_XY* l_psMark_Root;
    //枢轴的值kv，均值mean，方差var，方差最大值var_max

    int kv_x, kv_y, mean, x, var;
    u32 var_max = 0;
    //	float* tmp_ass;
    int d, n, i, j = 0;  //枢轴索引ki
    u8 ki = 0;
    u16 num;
    l_psMark = kd_node->features;
    n = kd_node->n;  //结点个数

    //枢轴的索引值就是方差最大的那一维的维数,即n个128维的特征向量中，若第k维的方差最大，则k就是枢轴(分割位置)
    /* partition key index is that along which descriptors have most variance */

    Find_MaxAxis1(p_MarkAddr, n, &var_max, &ki, 0, &kv_x);
    Find_MaxAxis1(p_MarkAddr, n, &var_max, &ki, 1, &kv_y);

    //枢轴的值就是所有特征向量的ki维的中值(按ki维排序后中间的那个值)
    /* partition key value is median of descriptor values at ki */
    quicksort_Marks(l_psMark, 0, n - 1, ki, p_MarkAddr);

    kd_node->ki = ki;  //枢轴的维数索引
    if (ki)
        kd_node->kv = kv_y;  //枢轴的值
    else
        kd_node->kv = kv_x;  //枢轴的值
    for (i = 0; i < n; i++)
    {
        // if(Return_AxisData(ki,g_psStructMarkAddr[i]) > kd_node->kv)
        if (Return_AxisData(ki, p_MarkAddr[i]) > kd_node->kv)
        {
            j = i;
            break;
        }
    }
    if (j != 0)
    {
        l_psMark_Root = *(p_MarkAddr + j - 1);
        num = j - 1;
    }
    else
    {
        num = n - 1;
        l_psMark_Root = *(p_MarkAddr + n - 1);
    }
    kd_node->features = l_psMark_Root;
    //初始化左孩子的根节点，左孩子共j+1个特征点
    kd_node->kd_left = kd_node_init_Mark1(NULL, num, kd_node, kd_node->offset);
    if (num == 1)
        kd_node->kd_left->features = *(p_MarkAddr);
    //初始化右孩子的根节点，右孩子共n-j-1个特征点
    kd_node->kd_right = kd_node_init_Mark1(NULL, (n - num - 1), kd_node, kd_node->offset + num + 1);
    if ((n - num - 1) == 1)
        kd_node->kd_right->features = *(p_MarkAddr + num + 1);
}

void innerGetClosest(KD_NODE_MARK* pNode,
                     XY_TO_RelCoor point,
                     MARK_XY** res,
                     u32* nMinDis,
                     u16 p_IdentWindow)
{
    if (NULL == pNode || NULL == pNode->features)
        return;
    u32 l_u32DIFF_x = abs(point.m_s32x - pNode->features->m_s32x);
    u32 l_u32DIFF_y = abs(point.m_s32y - pNode->features->m_s32y);
    u8 l_u8offset;
    if ((l_u32DIFF_x <= p_IdentWindow      // Get_IdentWindow(0,g_sMarkMatch_Set)
         && l_u32DIFF_y <= p_IdentWindow)  // Get_IdentWindow(0,g_sMarkMatch_Set)
        || pNode->leaf
               == 1  //为了后面反向查找时及时return，但最终查找完毕还需判断一下是否满足小于identwindow的要求
    )
    {
        u32 nCurDis = l_u32DIFF_x * l_u32DIFF_x + l_u32DIFF_y * l_u32DIFF_y;
        l_u8offset = (nMinDis[0] >= nMinDis[1]) ? 0 : 1;
        if (nCurDis < nMinDis[l_u8offset])
        {
            if (nCurDis < nMinDis[(l_u8offset + 1) % 2])
            {
                nMinDis[l_u8offset] = nMinDis[(l_u8offset + 1) % 2];
                nMinDis[(l_u8offset + 1) % 2] = nCurDis;
                *res = pNode->features;
            }
            else
                nMinDis[l_u8offset] = nCurDis;
        }
    }

    if (pNode->leaf == 1)
        return;
    if (((!pNode->ki) && (point.m_s32x <= pNode->features->m_s32x))
        || ((pNode->ki) && (point.m_s32y <= pNode->features->m_s32y)))
    {
        if (pNode->kd_left->n != 0)
            innerGetClosest(pNode->kd_left, point, res, nMinDis, p_IdentWindow);
        else
            return;
    }
    else
    {
        if (pNode->kd_right->n != 0)
            innerGetClosest(pNode->kd_right, point, res, nMinDis, p_IdentWindow);
        else
            return;
    }

    int rang = pNode->ki ? abs(point.m_s32y - pNode->features->m_s32y)
                         : abs(point.m_s32x - pNode->features->m_s32x);

    if (rang * rang > nMinDis[0])
        return;

    KD_NODE_MARK* pGoInto = pNode->kd_left;

    if (((!pNode->ki) && (point.m_s32x <= pNode->features->m_s32x))
        || ((pNode->ki) && (point.m_s32y <= pNode->features->m_s32y)))
    {
        pGoInto = pNode->kd_right;
    }

    innerGetClosest(pGoInto, point, res, nMinDis, p_IdentWindow);
}

void KDTree_GetClosest(KD_NODE_MARK* pNode,
                       XY_TO_RelCoor point,
                       MARK_XY** res,
                       u32* nMinDis,
                       u16 p_IdentWindow)
{
    u32 l_u32DIFF_x;
    u32 l_u32DIFF_y;

    innerGetClosest(pNode, point, res, nMinDis, p_IdentWindow);
    if (*res)
    {
        l_u32DIFF_x = abs(point.m_s32x - (*res)->m_s32x);
        l_u32DIFF_y = abs(point.m_s32y - (*res)->m_s32y);

        if ((l_u32DIFF_x > p_IdentWindow  // Get_IdentWindow(0,g_sMarkMatch_Set)
             || l_u32DIFF_y > p_IdentWindow))
        {
            *res = NULL;
        }
    }
    else
    {
        *res = NULL;
    }
}

// wen
KD_NODE_MARK* BuildKdTreeMapInfo(MARK_SETS* p_SetMarks)
{
    MARK_XY* l_psStructMarkAddr[MAX_SIZE];
    memset(l_psStructMarkAddr, 0, MAX_SIZE * 4);
    for (int i = 0; i < p_SetMarks->m_u16size; i++)
    {
        l_psStructMarkAddr[i] = &p_SetMarks->m_sMarkSets[i];
    }

    return kdtree_build_Mark(&p_SetMarks->m_sMarkSets[0],
                             p_SetMarks->m_u16size,
                             l_psStructMarkAddr);  // 500个靶20870，100个3709 200个 8526
}

static KD_NODE_MARK* MakeEmpty(KD_NODE_MARK* tree)
{
    if (tree)
    {
        MakeEmpty(tree->kd_left);
        MakeEmpty(tree->kd_right);
        free(tree);
        tree = NULL;
    }
    return tree;
}
void Free_KD_Tree(void)
{
    if (g_kd_root != NULL)
        MakeEmpty(g_kd_root);

    // memset(g_psStructMarkAddr,0,MAX_SIZE * 4);

    // for(int i = 0;i<g_sMarkSets.m_u16size ;i++)
    // {
    // 	g_psStructMarkAddr[i] = &g_sMarkSets.m_sMarkSets[i];
    // }

    return;
}
