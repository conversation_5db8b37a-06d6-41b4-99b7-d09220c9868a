#define __TASKTARGETAPP_C
#include "algorithm/location/mark_location/mark/TaskTargetApp.h"
#include "algorithm/location/mark_location/mark/ExSram.h"
#include "algorithm/location/mark_location/mark/FindMarkCenter.h"
#include "algorithm/location/mark_location/mark/IcpSpeedCorr.h"
#include "algorithm/location/mark_location/mark/Marks.h"
#include "algorithm/location/mark_location/mark/marktype.h"
//#include "net.h"
#include <math.h>
// #include <ros/ros.h>  //debug infor stdout
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
//#include "timer.h"
u8 g_u8StartOver = 0;  //
u8 g_u8CalOverFlag = 0;
u8 g_u8offset = 0;
u16 g_u16CombSize = 0;
u16 g_u8TriActiveFlag = 0;

ROBOT_XY g_sFusedPosOld;  //外部里程计结果
extern float g_f32Per10mDeg_Rad;
extern ROBOT_XY g_sSavePosCur;
extern ROBOT_XY g_sSavePosSend;
extern u8 g_u8HasCrossFlag;
extern INPUTSPEED g_sInSpeedUsed;
extern float g_f32Time_PerResolution;
extern u8 g_u8Ang_Resolution;
extern XY2ROBOT_CNT g_sMappingXY_old;
extern INPUTSPEED g_sInSpeedCopy;
extern ROBOT_XY g_sSavePosOld;
extern float g_f32ScanTime_HalfPer;
extern float g_f32ScanTime_Per;
extern u8 g_u8NavLeastMarkNum;
extern ROBOT_XY g_sSavePosSet;
extern u8 g_u8LandmarkSendMode;
extern u8 g_u8IntinalFlag;
extern u16 g_u16NavPoweroff;
extern ROBOT_XY g_sSavePosAver;
extern XYANG2ROBOT g_sSavePosAverBuf[POS_AVER_CNT];
extern EX_SRAM_EXT STRUCT_CARVE g_sPulseCarveBufPing;   //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_CARVE* g_sPulseCarveBufCopy;  //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_CARVE g_sPulseCarveBufPong;   //(CCM_RAM);
extern EX_SRAM_EXT COMBINE g_sComb;                     //(BANK1_SRAM1_ADDR);
extern EX_SRAM_EXT COMBINE1 g_sCombofDecMark;           //(BANK1_SRAM1_ADDR);
extern EX_SRAM_EXT XYANG2ROBOT
    g_auXY2EqlTri[EQLTRI_SIZE];  //(BANK1_SRAM1_ADDR);//全等三角形定位出的坐标值
extern EX_SRAM_EXT STRUCT_EqlTri g_sEqlTri;  //(BANK1_SRAM1_ADDR);
extern EX_SRAM_EXT u16
    g_us16PulseWidthBuf01Copy[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
extern EX_SRAM_EXT u16
    g_us16PulseWidth_Aver_Ping[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
extern EX_SRAM_EXT u16
    g_us16PulseWidth_Aver_Pong[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
extern EX_SRAM_EXT u16
    g_us16PulseWidth_Aver[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
extern EX_SRAM_EXT MARK_SETS g_sMarkSets;                                //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_FPGAPOS g_sFpgaPos;                            //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_FILTER_TARGET g_sFilterDist;                   //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterMark_SpeedCorr;    //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterDistShortNewLast;  //(CCM_RAM);
extern EX_SRAM_EXT LOCAL_MAP_INFO g_sLocalMap_Info;                      //(CCM_RAM);
extern EX_SRAM_EXT LOCAL_MAP_INFO g_sLocalMap_Info_LastCircle;           //(CCM_RAM);
extern EX_SRAM_EXT u16 g_us16PulseWidthBufPing[7200];                    //(CCM_RAM);
extern EX_SRAM_EXT u16 g_us16PulseWidthBufPong[7200];                    //(CCM_RAM);
extern EX_SRAM_EXT u16 g_us16PulseWidthBufCopy[7200];                    //(CCM_RAM);
extern EX_SRAM_EXT u16 g_us16DistBufPing[7200];  //(BANK1_SRAM1_ADDR); //ping
extern EX_SRAM_EXT u16 g_us16DistBufPong[7200];  //(BANK1_SRAM1_ADDR); //ping
extern ICPSPEEDCORR_EXT STRUCT_SpeedIcp g_sIcpSpeed;
// extern sSysPib g_sSysPib;
// extern u16 g_u16CombOffset;
// extern u16 g_u16Mapping_Aver_Done_Cnt;
// extern u16 g_u16MappingNum;
u16 g_u16MappingNum = 100;
u16 g_u16CombOffset = 0;
u16 g_u16Mapping_Aver_Done_Cnt = 0;
sFactorCorr g_sFactorCorr = {0, 0};
sSysPib g_sSysPib = {80, 0, 0, 0, 0, 10, 0, 0};

MappingList g_auMappingList;
STRUCT_MarkMatch g_sMarkMatch_Set = {500, 500, 1500, 25000, 1500, 25000, 1, 4, 4};
STRUCT_MarkMatch g_sMarkMatch_Corr = {200, 200, 1500, 25000, 1500, 25000, 1, 4, 4};
TASKTARGETAPP_EXT s32 TaskTargetAppStk[TASK_TARGET_STACKSIZE];
TASKTARGETAPP_EXT u32 g_u32CntOfNOACTIVE;
TASKTARGETAPP_EXT u32 g_u32CntOfACTIVE_AVER;
TASKTARGETAPP_EXT u32 g_u16MaxAng_AVER;  //防止角度0点翻转导致平均角度不正确
TASKTARGETAPP_EXT u32 g_u32CntOfACTIVE;
TASKTARGETAPP_EXT u32 g_u32CntOfInitial;
TASKTARGETAPP_EXT u32 g_un32semcnt;

void Cal_SetSpeedLineANDDirection(SPEED* p_SpeedIn,
                                  INPUTSPEED* p_Speed,
                                  u8 p_coorFlag,
                                  ROBOT_XY* robot)
{
    p_Speed->m_f32spAngDirection = atan2(p_SpeedIn->m_s16speedy, p_SpeedIn->m_s16speedx);
    if (p_coorFlag == COORDINATE_YTPE_RELAVTIVE)
        p_Speed->m_f32spAngDirection =
            fmod(p_Speed->m_f32spAngDirection
                     + robot->m_Struct_LaserPos.m_u16ang * g_f32Per10mDeg_Rad + 2 * M_PI,
                 2 * M_PI);
    else
        p_Speed->m_f32spAngDirection = fmod(p_Speed->m_f32spAngDirection + 2 * M_PI, 2 * M_PI);

    p_Speed->m_f32spLine_Abs =
        Calculate_Point2Point_Dist_Float(p_SpeedIn->m_s16speedx, p_SpeedIn->m_s16speedy);
}

//判断速度是否有效
void Judge_Effectiveness_of_Speed(INPUTSPEED* p_Judge_Speed,
                                  TIMESTAMP p_Ts,
                                  INPUTSPEED* p_Use_Speed)
{
    static u32 l_u32TSdiff;
    l_u32TSdiff = Get_TS_Diff(p_Ts, p_Judge_Speed->m_u32timestamp);
    u32 l_u32CompareTs = 0;
    OS_CPU_SR cpu_sr;
    u16 l_u16sprintflen = 0;

    if (p_Judge_Speed->m_u16speedsetflag == 1)
    {
        g_sIcpSpeed.m_s32Flag = 0;
        l_u32CompareTs = 200 >> (g_u8AngMode - 1);  //绝对值取整
        if (l_u32TSdiff <= l_u32CompareTs)
        {
            memcpy(p_Use_Speed, p_Judge_Speed, STRUCT_SIZE_INPUTSPEED);
            if (p_Use_Speed->m_u16Corrdinate_Type == COORDINATE_YTPE_ABS)
                Cal_SetSpeedLineANDDirection(&p_Use_Speed->m_StructSpeed_absolute,
                                             p_Use_Speed,
                                             p_Use_Speed->m_u16Corrdinate_Type,
                                             &g_sSavePosCur);
            else
                Cal_SetSpeedLineANDDirection(&p_Use_Speed->m_StructSpeed_relative,
                                             p_Use_Speed,
                                             p_Use_Speed->m_u16Corrdinate_Type,
                                             &g_sSavePosCur);
        }
        else
        {
            l_u32TSdiff = l_u32TSdiff;
        }
    }
    //	else
    //	{
    ////		l_u16sprintflen = sprintf((char *)g_u8NetBuf_Send2,"-----SpeedTSTimeOver----");
    ////		send_qmsg(W5300_SOCKET2,g_u8NetBuf_Send2,l_u16sprintflen);
    //		//memset(p_Use_Speed,0,STRUCT_SIZE_INPUTSPEED);
    //		p_Use_Speed->m_u16speedsetflag = 0;
    //	}
}

/*************************************************
Function		: TaskTargetApp
Description		: 定位任务
Calls			: 无
Called By		: 无
Table Accessed	: 无
Table Updated	: 无
Input			: 无
Output			: 无
Return			: 无
Others			: 无
*************************************************/
void TaskTargetApp(void* tdata)  //启动任务
{
    u8 l_u8err;
    u8 l_u8i;
    u8 l_u8pingpong;
    u8 l_u8CntScanNumCopy;
    u16* l_pu16dist = NULL;
    u16* l_pu16pulse = NULL;
    u16* l_pu16pulse01 = NULL;
    u16* l_pu16pulse_aver = NULL;
    OS_CPU_SR cpu_sr;
    int l_s32ret;
    u32 l_u32ScanOverTs = 0;
    u32 l_u32TS_Diff_WithLastPos;
    float l_f32CorrTime = 0;
    u16* l_pu16ref = NULL;
    INPUTSPEED l_sInputSpeed;

    while (1)
    {
        while ((g_u8StartOver & 0x02) != 2)
        {
            // OSTimeDly(100);
        }
        // 0. 等到一圈数据搜集完成, 经过了亮面过滤处理
        // OSSemPend(g_semTargetEvent,0,&l_u8err);
        //切换pingpong缓冲区
        // OS_ENTER_CRITICAL();

        g_u8CalOverFlag = 0;
        g_u8HasCrossFlag = 0;
        l_u8CntScanNumCopy = 0;  // g_au8CntScanNum ; //读取圈号
        l_u8pingpong = 0;        // g_au32PingPong ;
        l_u32ScanOverTs = 0;     // g_u32timescanover ;

        l_u32TS_Diff_WithLastPos = Get_TS_Diff(l_u32ScanOverTs, g_sSavePosSend.m_u32timestamp);

        // Copy_UsingData(l_u8pingpong ,&l_pu16pulse_aver,&l_pu16pulse ,&l_pu16dist);//1.5ms
        // Copy_UsingData_WD(l_u8pingpong ,&l_pu16pulse_aver,&l_pu16pulse
        // ,&l_pu16dist,&l_pu16ref);//1.5ms

        //判断速度是否有效
        Judge_Effectiveness_of_Speed(&g_sInSpeedCopy, l_u32ScanOverTs, &g_sInSpeedUsed);

        l_f32CorrTime = l_u32TS_Diff_WithLastPos - g_f32ScanTime_HalfPer;

        memcpy(&l_sInputSpeed, &g_sInSpeedUsed, STRUCT_SIZE_INPUTSPEED);
        if (g_sInSpeedUsed.m_u16speedsetflag == 0)
        {
            Corr_Speed(&l_sInputSpeed,
                       g_f32ScanTime_HalfPer,
                       &l_sInputSpeed.m_StructSpeed_absolute,
                       g_f32ScanTime_Per);
            Corr_Speed(&l_sInputSpeed, 0, &l_sInputSpeed.m_StructSpeed_absolute, l_f32CorrTime);
        }

        LaserPose_CompensationBySLine(&l_sInputSpeed.m_StructSpeed_absolute,
                                      &g_sSavePosCur.m_Struct_LaserPos,
                                      &g_sSavePosCur.m_Struct_LaserPos,
                                      l_f32CorrTime);
        g_sSavePosCur.m_Struct_LaserPos.m_u16ang = LaserPose_CompensationBySAng(
            &l_sInputSpeed, &g_sSavePosCur.m_Struct_LaserPos, l_f32CorrTime);

        //扫描靶标模式
        if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark != SYSMODE_NavOrMappingOrMark_SCANF)
        {
            Find_ScanMark_PluseDist(l_pu16pulse,
                                    l_pu16ref,
                                    l_pu16dist,
                                    l_u32TS_Diff_WithLastPos,
                                    g_us16PulseWidth_Aver);

            // Find_ScanMark(l_pu16pulse,l_pu16pulse_aver,l_pu16dist,l_u32TS_Diff_WithLastPos);
            // DeletCloseMark(&g_sFilterMark_SpeedCorr);//滤掉2个十分相近的靶,200mm
            // FilterMark_ByMarkMaxMinDist(&g_sFilterMark_SpeedCorr);
            // Filter_NotMark_CopyScan(&g_sFilterMark_SpeedCorr,&g_sFilterMark_SpeedCorr);
            // //过滤掉太近的靶标
            // PrintfScanMarks(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[0],g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);//15
        }

        // g_sFilterMark_SpeedCorr 提取的靶标结构体
        //匹配模式
        if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_NAV)
        {
            if (g_sInSpeedUsed.m_u16speedsetflag == 0)
            {
                //清空靶标标志位
                Memset_IsMarkFlag(&g_sFilterMark_SpeedCorr);
                TransMark_Polar_to_RelDecare(
                    &g_sFilterMark_SpeedCorr,
                    g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
                Match_CurPos_Icp(&g_sMarkMatch_Set, l_u32TS_Diff_WithLastPos);
            }

            Memset_IsMarkFlag(&g_sFilterMark_SpeedCorr);
            TransMark_Polar_to_RelDecare(&g_sFilterMark_SpeedCorr,
                                         g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
            if (g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In >= g_u8NavLeastMarkNum)
            {
                l_s32ret =
                    Match_CurPos(&g_sMarkMatch_Set, l_u32TS_Diff_WithLastPos, &g_sSavePosCur);

                if (g_u8HasCrossFlag)
                {
                    g_sMarkMatch_Corr.m_u16IdentWindow_R_Max =
                        (XY_ERROR << 1) + (g_sSysPib.m_u16MarkRadio >> 1);
                    g_sMarkMatch_Corr.m_u16IdentWindow_R_Min =
                        g_sMarkMatch_Corr.m_u16IdentWindow_R_Max;
                    l_s32ret = Match_SetMark_By_LaserPose(&g_sFilterMark_SpeedCorr,
                                                          &g_sSavePosCur,
                                                          &g_sMappingXY_old,
                                                          &g_sMarkSets,
                                                          g_u8NavLeastMarkNum,
                                                          &g_sMarkMatch_Corr);
                }

                if (MATCH_FAILED == l_s32ret)  //用上一次定位匹配这一圈扫描失败
                {
                    l_s32ret = Match_OldSetPos(&g_sMarkMatch_Set);
                    if (l_s32ret)  //重新三角定位,//PosOld位置不对
                    {
                        if (g_sSavePosSet.m_u16flag == POSSET_EXACT)
                            g_sSavePosSet.m_u16flag = POSSET_ROUGH;

                        WorkMode_Initial_Position(
                            &g_sFilterMark_SpeedCorr, l_u32ScanOverTs, &g_sInSpeedUsed);
                    }
                    else  // old值匹配成功，直接进行相角匹配，此部分主要用于将激光器挪回原位置可以继续出数
                    {
                        if (g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In >= 3)
                            WorkMode_Continue_Position(l_u32ScanOverTs, &g_sSavePosOld);
                    }

                    if (g_sSavePosCur.m_u16flag == ACTIVE)
                    {
                        g_sSavePosCur.m_u16flag = INITIAL;
                        if (!g_u8IntinalFlag)
                        {
                            memset(&g_sLocalMap_Info_LastCircle, 0, STRUCT_SIZE_LOCAL_MAP_INFO);
                            memcpy(&g_sLocalMap_Info_LastCircle,
                                   &g_sLocalMap_Info,
                                   STRUCT_SIZE_LOCAL_MAP_INFO);
                        }
                    }
                    else
                    {
                        memset((void*)&g_sSavePosCur, 0, STRUCT_SIZE_LASERPOS);
                    }
                }
                else
                {
                    WorkMode_Continue_Position(l_u32ScanOverTs, &g_sSavePosOld);
                }
            }
            else
            {
                memset((void*)&g_sSavePosCur, 0, STRUCT_SIZE_LASERPOS);
            }

            TransPos_To_SendOutTS(
                &g_sSavePosCur, &g_sSavePosSend, &g_sSavePosOld, &g_sInSpeedUsed, l_u32ScanOverTs);
            if (g_sSavePosCur.m_u16flag != ACTIVE)
            {
                g_u32CntOfACTIVE_AVER = 0;
                g_u16MaxAng_AVER = 0;
                memset(&g_sSavePosAver, 0, STRUCT_SIZE_LASERPOS);
            }
        }
        else if ((g_sSysPib.m_u16WorkMode_NavOrMappingOrMark
                  == SYSMODE_NavOrMappingOrMark_MAPPING_P)
                 || (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark
                     == SYSMODE_NavOrMappingOrMark_MAPPING_N))
        {
            WorkMode_Add_Mapping();
        }
        else if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MARK)
        {
            WorkMode_Scan_Mark(&g_sFilterDist, &g_sFilterMark_SpeedCorr, g_sFilterDist.m_u8In);
        }
        Send_ScanData(&g_sSavePosSend,
                      &g_sFilterDist,
                      &g_sFilterMark_SpeedCorr,
                      l_pu16dist,
                      l_pu16pulse,
                      g_us16PulseWidthBuf01Copy,
                      l_u8CntScanNumCopy,
                      l_u32ScanOverTs);
        g_u8CalOverFlag = 1;  //计算完毕
    }
}

/**
 * 使用当前预估位姿进行mark匹配
 */
int Match_CurPos(STRUCT_MarkMatch* p_IdenWindow, u32 p_TSdiff, ROBOT_XY* p_LaserPos)
{
    float l_f32CorrTime = p_TSdiff - g_f32ScanTime_HalfPer;
    u8 l_u8ret = 0;
    INPUTSPEED l_sSpeedLast;
    u16 l_u16sprintflen = 0;

    if ((p_LaserPos->m_u16flag != NOACTIVE))
    {
        // 6个参数分别为：当前帧靶标，目前为当前帧预估位姿，MAPPING建图结束地图储存，//MAP-NAV地图上的靶标，NAV最少扫描靶标数3个，NAV匹配的靶标
        l_u8ret = Match_SetMark_By_LaserPose(&g_sFilterMark_SpeedCorr,
                                             p_LaserPos,
                                             &g_sMappingXY_old,
                                             &g_sMarkSets,
                                             g_u8NavLeastMarkNum,
                                             p_IdenWindow);

        if (g_sInSpeedUsed.m_u16speedsetflag != 1)
            Renew_IcpMarkXY(p_LaserPos);

        return l_u8ret;
    }
    else
    {
        return MATCH_FAILED;
    }
}

int Match_OldSetPos(STRUCT_MarkMatch* p_IdenWindow)
{
    u8 l_u8j = 0;
    u8 l_u8cnt = 0;
    u8 l_u8ret = 1;
    ROBOT_XY* p_TmpPos = NULL;
    ROBOT_XY* l_psPos[2] = {0};

    if (g_sSavePosOld.m_u16flag == ACTIVE)
        l_psPos[l_u8j++] = &g_sSavePosOld;
    if (g_sSavePosSet.m_u16flag == POSSET_EXACT)
    {
        g_u16NavPoweroff = 0;
        l_psPos[l_u8j++] = &g_sSavePosSet;
    }

    while (l_u8ret && (l_u8cnt < l_u8j))  // l_u8j:1  l_u8cnt:0
    {
        p_TmpPos = l_psPos[l_u8cnt];
        l_u8ret = Match_SetMark_By_LaserPose(
            &g_sFilterMark_SpeedCorr, p_TmpPos, &g_sMappingXY_old, &g_sMarkSets, 3, p_IdenWindow);
        l_u8cnt++;
    }
    if (!l_u8ret)
        memcpy(&g_sSavePosOld.m_Struct_LaserPos,
               &p_TmpPos->m_Struct_LaserPos,
               STRUCT_SIZE_XYANG2ROBOT);
    return l_u8ret;
}

void WorkMode_Add_Mapping(void)
{
    u8 l_u8err;
    u8 l_u8i;
    u8 l_u8cnt = 0;
    int l_s32ret = 0;

    //		FilterMark_ByMarkMaxMinDist(&g_sFilterMark_SpeedCorr);
    //		Filter_NotMark_CopyScan(&g_sFilterMark_SpeedCorr,&g_sFilterMark_SpeedCorr);
    ////过滤掉太近的靶标
    SortMarkByAng(&g_sFilterMark_SpeedCorr);
    // OSFlagPost(g_flagUser, USR_FLAG_MAPPING_CONTINUE_AVER,OS_FLAG_CLR, &l_u8err) ;

    if (g_u16Mapping_Aver_Done_Cnt == 1)  //将第一次扫描到的靶标存起来
    {
        g_u8offset = 0;
        if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark
            == SYSMODE_NavOrMappingOrMark_MAPPING_P)  //普通mapping模式
        {
            // memset((void *) &g_sSavePosSet, 0, STRUCT_SIZE_LASERPOS) ;
            WorkMode_MappingP_First();
        }
        else if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark
                 == SYSMODE_NavOrMappingOrMark_MAPPING_N)  //基于之前靶添加新靶
        {
            memcpy(&g_sSavePosSet, &g_sSavePosCur, STRUCT_SIZE_LASERPOS);
            // WorkMode_MappingN_First();
            WorkMode_MappingP_First();
        }
    }
    else if (g_u16Mapping_Aver_Done_Cnt <= g_u16MappingNum)
    {
        g_u8offset = g_auMappingList.m_u8First;
        for (l_u8i = 0; l_u8i < g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In; l_u8i++)
        {
            // ROS_ERROR("m_u16Ang==%d,
            // m_u16Dist==%d",g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Ang,
            // g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Dist);
            //	与第一次扫描到的靶进行匹配，匹配上的靶平均次数加1，新扫描到的平均次数记1
            MatchDecMark_And_RenewList(
                &g_sFilterMark_SpeedCorr, &g_auMappingList, l_u8i, &g_u8offset);
            // 反推绝对坐标 并平均
            CalAbsCoordinatesofDec_N(&g_sFilterMark_SpeedCorr,
                                     &g_sSavePosSet,
                                     l_u8i,
                                     &g_auMappingList.m_XY2Rob,
                                     g_u8offset);
            g_u8offset = g_auMappingList.m_u8Next[g_u8offset];
            // ROS_ERROR("m_s32X==%d, m_s32Y==%d",g_auMappingList.m_XY2Rob.m_StructXY[l_u8i].m_s32x,
            // g_auMappingList.m_XY2Rob.m_StructXY[l_u8i].m_s32y);
        }
    }

    if (g_u16Mapping_Aver_Done_Cnt == g_u16MappingNum)
    {
        WorkMode_Mapping_End(g_u16MappingNum >> 1, &g_sMarkMatch_Set);
    }
}

void WorkMode_Mapping_End(u16 p_ScanNum, STRUCT_MarkMatch* p_IdenWindow)
{
    /***********debug**************/
    // for(int l_u8i =0; l_u8i<g_auMappingList.m_XY2Rob.m_u8MarkNum ;l_u8i++)
    // {
    // 	ROS_ERROR("g_auMappingList::m_s32X==%d,
    // m_s32Y==%d",g_auMappingList.m_XY2Rob.m_StructXY[l_u8i].m_s32x,
    // g_auMappingList.m_XY2Rob.m_StructXY[l_u8i].m_s32y);
    // }
    /***********debug**************/
    g_sMappingXY_old.m_u8MarkNum = Filter_HasSet_Or_NotEnoughHalf_Mark(p_ScanNum, p_IdenWindow);
    //因为第一包上一次的定位值已经被匹配过了，设置值不进行匹配，直接输出，只需把已经设置的靶剔除掉即可
    Copy_NewAdd_Mark(g_sMappingXY_old.m_u8MarkNum);
    /***********debug**************/
    // for(int l_u8i =0; l_u8i<g_sMappingXY_old.m_u8MarkNum ;l_u8i++)
    // {
    // 	ROS_ERROR("g_sMappingXY_old::m_s32X==%d,
    // m_s32Y==%d",g_sMappingXY_old.m_StructXY[l_u8i].m_s32x,
    // g_sMappingXY_old.m_StructXY[l_u8i].m_s32y);
    // }
    /***********debug**************/
}

u8 Filter_HasSet_Or_NotEnoughHalf_Mark(u16 p_ScanNum, STRUCT_MarkMatch* p_IdenWindow)
{
    u8 l_u8i;
    u8 l_u8Delet_MakrCnt = 0;
    for (l_u8i = 0; l_u8i < g_auMappingList.m_XY2Rob.m_u8MarkNum; l_u8i++)  //
    {
        if (g_auMappingList.m_XY2Rob.m_u16cnt[l_u8i] >= p_ScanNum)
        {
            if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MAPPING_N)
            {
                l_u8Delet_MakrCnt +=
                    MatchSetMark(&g_sMarkSets, l_u8i, &g_auMappingList.m_XY2Rob, p_IdenWindow);
            }
        }
        else
        {
            l_u8Delet_MakrCnt += Delet_List(l_u8i, &g_auMappingList);
        }
    }
    return g_auMappingList.m_XY2Rob.m_u8MarkNum - l_u8Delet_MakrCnt;
}
void Copy_NewAdd_Mark(u8 p_MarkNum)
{
    u8 l_u8offset = 0;
    u8 l_u8cnt = 0;
    l_u8offset = g_auMappingList.m_u8First;
    while (l_u8cnt < p_MarkNum)
    {
        memcpy(&g_sMappingXY_old.m_StructXY[l_u8cnt],
               &g_auMappingList.m_XY2Rob.m_StructXY[l_u8offset],
               STRUCT_SIZE_XY_TO_RelCoor);
        l_u8cnt++;
        l_u8offset = g_auMappingList.m_u8Next[l_u8offset];
    }
}
u8 Get_PosBufType_Nav(u8* p_PosOffset, ROBOT_XY* p_Pos, u8 p_PosNum)
{
    for (u8 l_u8i = *p_PosOffset; l_u8i < p_PosNum; l_u8i++)
    {
        if (p_Pos[l_u8i].m_u16flag == ACTIVE)
        {
            *p_PosOffset = l_u8i + 1;
            return l_u8i;
        }
    }
    return 2;
}
u8 Get_PosBufType_Mapping(u8* p_PosOffset, ROBOT_XY* p_Pos, u8 p_PosNum)
{
    // ROBOT_XY *l_psPos[2] = {&g_sSavePosAver, &g_sSavePosCur};
    for (u8 l_u8i = *p_PosOffset; l_u8i < p_PosNum; l_u8i++)
    {
        if (p_Pos[l_u8i].m_u16flag == ACTIVE)
        {
            *p_PosOffset = l_u8i + 1;
            return l_u8i;
        }
    }
    return 2;
}
u8 WorkMode_MappingP_First(void)
{
    memset((void*)&g_auMappingList, 0, STRUCT_SIZE_MappingList);
    memset((void*)&g_sMarkSets.m_u8hasmatchflag, 0, MAX_SIZE);
    memcpy(&g_sSavePosCur, &g_sSavePosSet, STRUCT_SIZE_LASERPOS);
    g_sSavePosCur.m_u16flag = ACTIVE;
    memcpy(&g_sSavePosOld, &g_sSavePosCur, STRUCT_SIZE_LASERPOS);
    g_auMappingList.m_u8First = 0;
    for (u8 l_u8i = 0; l_u8i < g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In; l_u8i++)  //
    {
        Init_List(l_u8i, &g_auMappingList, g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
        CalAbsCoordinatesofDec1(
            g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i],
            &g_sSavePosSet,
            g_sFilterMark_SpeedCorr.m_psSetMarkAddr[l_u8i],
            l_u8i,
            &g_auMappingList.m_XY2Rob);
    }
    return 0;
}
u8 WorkMode_MappingN_First(void)
{
    u8 l_u8i;
    int l_s32ret = 0;
    u8 l_u8PosOffset = 0;
    // ROBOT_XY *l_psPos[3] = {&g_sSavePosAver, &g_sSavePosCur, &g_sSavePosSet};
    ROBOT_XY* p_TmpPos = &g_sSavePosSet;
    // while(Get_PosBufType_Mapping(&l_u8PosOffset, l_psPos[0],2) < 2)
    //{
    Memset_IsMarkFlag(&g_sFilterMark_SpeedCorr);
    // p_TmpPos = l_psPos[l_u8PosOffset - 1];
    memset((void*)&g_auMappingList, 0, STRUCT_SIZE_MappingList);
    memset((void*)&g_sMarkSets.m_u8hasmatchflag, 0, MAX_SIZE);
    g_auMappingList.m_u8First = 0;
    for (l_u8i = 0; l_u8i < g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In; l_u8i++)
    {
        Init_List(l_u8i, &g_auMappingList, g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
        // CalAbsCoordinatesofDec1(g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]
        // ,  &g_sSavePosSet ,g_sFilterMark_SpeedCorr.m_psSetMarkAddr[l_u8i], l_u8i ,
        // &g_auMappingList.m_XY2Rob);
    }
    l_s32ret = Match_SetMark_By_LaserPose(
        &g_sFilterMark_SpeedCorr, p_TmpPos, &g_sMappingXY_old, &g_sMarkSets, 3, &g_sMarkMatch_Set);
    if (!l_s32ret)  //说明上一次的位置有效
    {
        memcpy(
            (void*)&g_sSavePosSet, (void*)p_TmpPos, STRUCT_SIZE_LASERPOS);  //后面统一用set值去平移
                                                                            // return 0;
    }
    //}
    WorkMode_MappingP_First();
    return 0;
}

void WorkMode_Scan_Mark(STRUCT_FILTER_TARGET* p_ScanMark,
                        STRUCT_FILTER_TARGET_LITE* p_FilterMark,
                        u16 p_MarkSize)
{
    u8 l_u8i = 0;

    ROBOT_XY l_sPos = {0};
    if (g_u8LandmarkSendMode == LandMark_NoFilter)
        Renew_AbsCoor_XY(p_ScanMark, &l_sPos, &g_sMappingXY_old, p_MarkSize);
    else
    {
        TransMark_Polar_to_RelDecare(p_FilterMark, p_FilterMark->m_StructMarkScanInfoAddr.m_u8In);
        Renew_AbsCoor_XY1(&p_FilterMark->m_StructMarkScanInfoAddr.m_sXy2Robot[0],
                          &l_sPos.m_Struct_LaserPos,
                          &g_sMappingXY_old,
                          p_FilterMark->m_StructMarkScanInfoAddr.m_u8In);
    }
}
// void TransPos_To_SendOutTS(ROBOT_XY *p_PosCur, ROBOT_XY *p_PosSend , ROBOT_XY *p_PosOld,
// INPUTSPEED *p_SpeedIn, TIMESTAMP p_TS_ScanOver)
//{
//	OS_CPU_SR cpu_sr;
//	float l_f32CorrTime = 0;
//	u32 l_u32TS_Diff = Get_TS_Diff(p_TS_ScanOver , p_PosSend->m_u32timestamp);

//	XYANG2ROBOT l_pLaserPosVir;
//	SPEED l_pSpeedTmp;
//	int l_u32SumMean = 0;
//	int l_u32OldMeanDev = 0,l_u32CurrMeanDev = 0;
//	float l_f32CorrTimeVir = l_u32TS_Diff - g_f32ScanTime_HalfPer;

//	p_PosCur->m_u16CurrLayer = g_sMarkSets.m_u16layer;
//	for(u8
// l_u8i=0;l_u8i<g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In;l_u8i++)//修正混补发出坐标
//	{
//		if(g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark ==
// ISMARK) 			Corr_Mark_MixLayout_Final(&g_sFilterMark_SpeedCorr,l_u8i);
//	}
//	if((g_sSavePosCur.m_u16flag == ACTIVE)||(g_sSavePosCur.m_u16flag == INITIAL))
//	{
//		g_u32CntOfNOACTIVE = 0;
//		g_u32CntOfACTIVE ++ ;
//		/*计算新定位的meandev值*/
//		Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0] ,
//&p_PosCur->m_Struct_LaserPos ,&g_sMappingXY_old
//,g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In); 		p_PosCur->m_u16meandev =
// Cal_MeanDev(&g_sMappingXY_old); 		l_u32CurrMeanDev = p_PosCur->m_u16meandev;
// l_u32OldMeanDev = p_PosOld->m_u16meandev; 		Cal_MoveSpeed(p_SpeedIn, p_PosCur,
// p_PosOld,p_TS_ScanOver,l_u32TS_Diff);//根据上次half定位计算绝对移动速度
//		if(g_sInSpeedUsed.m_u16speedsetflag == 0)
//			Corr_Speed(p_SpeedIn,0,&l_pSpeedTmp,g_f32ScanTime_HalfPer);
////根据角速度修正计算的速度，修正时间为 半圈/2 * 角速度; 		else
//			memcpy(&l_pSpeedTmp,&p_SpeedIn->m_StructSpeed_absolute,sizeof(SPEED));
//		if(p_PosSend->m_u16flag == ACTIVE
//			|| p_PosSend->m_u16flag == INITIAL)
//		{
//			//根据上一次位置及此次速度预估当前圈half位置，修正时间为 ： 两次定位时间戳之差 -
//半圈时间 			memcpy(&l_pLaserPosVir, &p_PosSend->m_Struct_LaserPos, STRUCT_SIZE_XYANG2ROBOT);
//			LaserPose_CompensationBySLine(&l_pSpeedTmp  , &l_pLaserPosVir , &l_pLaserPosVir ,
// l_f32CorrTimeVir); 			l_pLaserPosVir.m_u16ang = LaserPose_CompensationBySAng(p_SpeedIn ,
//&l_pLaserPosVir , l_f32CorrTimeVir);
//
//			Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0] ,
//&l_pLaserPosVir ,&g_sNavXY_Vir ,g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
//			l_u32OldMeanDev = Cal_MeanDev(&g_sNavXY_Vir);//更新虚拟位置的meandev
//		}
//		l_u32SumMean = l_u32CurrMeanDev + l_u32OldMeanDev;
//		if(p_PosSend->m_u16flag == ACTIVE
//		|| p_PosSend->m_u16flag == INITIAL)
//		{
//			//加权平均求出half位置
//			if(l_u32OldMeanDev == l_u32CurrMeanDev)
//			{
//				p_PosCur->m_Struct_LaserPos.m_s32x = (p_PosCur->m_Struct_LaserPos.m_s32x +
// l_pLaserPosVir.m_s32x )/2; 				p_PosCur->m_Struct_LaserPos.m_s32y =
//(p_PosCur->m_Struct_LaserPos.m_s32y + l_pLaserPosVir.m_s32y )/2;
//				if(abs(p_PosCur->m_Struct_LaserPos.m_u16ang - l_pLaserPosVir.m_u16ang )> 18000)
//					p_PosCur->m_Struct_LaserPos.m_u16ang = ((DEG_PERSCAN +
// p_PosCur->m_Struct_LaserPos.m_u16ang + l_pLaserPosVir.m_u16ang)>>1)%DEG_PERSCAN; else
//					p_PosCur->m_Struct_LaserPos.m_u16ang =
//((u32)p_PosCur->m_Struct_LaserPos.m_u16ang
//+ l_pLaserPosVir.m_u16ang)>>1;
//			}
//			else
//			{
//				p_PosCur->m_Struct_LaserPos.m_s32x = (p_PosCur->m_Struct_LaserPos.m_s32x *
// l_u32OldMeanDev
//+ l_pLaserPosVir.m_s32x * l_u32CurrMeanDev)/l_u32SumMean;
// p_PosCur->m_Struct_LaserPos.m_s32y = (p_PosCur->m_Struct_LaserPos.m_s32y * l_u32OldMeanDev +
// l_pLaserPosVir.m_s32y * l_u32CurrMeanDev)/l_u32SumMean;
// if(abs(p_PosCur->m_Struct_LaserPos.m_u16ang - l_pLaserPosVir.m_u16ang )> 18000)
//				{
//					if(p_PosCur->m_Struct_LaserPos.m_u16ang > l_pLaserPosVir.m_u16ang)
//						p_PosCur->m_Struct_LaserPos.m_u16ang = ((DEG_PERSCAN * l_u32CurrMeanDev +
// l_u32OldMeanDev * p_PosCur->m_Struct_LaserPos.m_u16ang  + l_u32CurrMeanDev *
// l_pLaserPosVir.m_u16ang )/l_u32SumMean)%DEG_PERSCAN; 					else
// p_PosCur->m_Struct_LaserPos.m_u16ang =
//((DEG_PERSCAN * l_u32OldMeanDev + l_u32OldMeanDev * p_PosCur->m_Struct_LaserPos.m_u16ang  +
// l_u32CurrMeanDev * l_pLaserPosVir.m_u16ang )/l_u32SumMean)%DEG_PERSCAN;
//				}
//				else
//					p_PosCur->m_Struct_LaserPos.m_u16ang = (l_u32OldMeanDev *
// p_PosCur->m_Struct_LaserPos.m_u16ang + l_u32CurrMeanDev* l_pLaserPosVir.m_u16ang)/l_u32SumMean;
//			}
//		}
//		Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0] ,
//&p_PosCur->m_Struct_LaserPos ,&g_sMappingXY_old
//,g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In); 		Cal_MoveSpeed(p_SpeedIn,
// p_PosCur, p_PosOld,p_TS_ScanOver,l_u32TS_Diff);//根据拟合结果更新速度 p_PosCur->m_u8scannum =
// g_sFilterDist.m_u8In; 		p_PosCur->m_u8marknum =
// Nav_Mark_Num(g_sFilterDistShortNewLast.m_StructMarkScanInfoAddr.m_u8In);
// p_PosCur->m_u32timestamp = p_TS_ScanOver; 		memcpy(p_PosOld, p_PosCur,
// STRUCT_SIZE_LASERPOS);
//
//		p_PosOld->m_u16flag = ACTIVE;
//		StoreRobotSets(p_PosOld, FRAM_ADDR_ROBOT) ;
//
//		l_f32CorrTime = g_f32ScanTime_HalfPer;
//		if(g_sInSpeedUsed.m_u16speedsetflag == 0)
//			Corr_Speed(p_SpeedIn,l_f32CorrTime,&l_pSpeedTmp,l_u32TS_Diff);//根据角速度修正速度，修正时间
//（半圈+时间戳之差）/2 		else
//			Corr_Speed(p_SpeedIn,0,&l_pSpeedTmp,l_f32CorrTime);//根据角速度修正速度，修正时间
//（半圈+时间戳之差）/2
//
//		LaserPose_CompensationBySLine(&l_pSpeedTmp , &p_PosCur->m_Struct_LaserPos
//,&p_PosCur->m_Struct_LaserPos, l_f32CorrTime);
////g_sSavePosCur中保存的位置为180°位置,需根据速度修正至360°，修正时间 ： 半圈
//		p_PosCur->m_Struct_LaserPos.m_u16ang = LaserPose_CompensationBySAng(p_SpeedIn ,
//&p_PosCur->m_Struct_LaserPos ,l_f32CorrTime);
//
//	}
//	else
//	{
//		g_u32CntOfACTIVE = 0;
//		//g_u32CntOfNOACTIVE++;
//		memcpy(p_PosCur, p_PosSend, STRUCT_SIZE_LASERPOS);
//		p_PosCur->m_u32timestamp = p_TS_ScanOver;
//		p_PosCur->m_u16flag = VIRTUAL;
//		l_f32CorrTime = l_u32TS_Diff;
//		if(g_sInSpeedUsed.m_u16speedsetflag == 0)
//			Corr_Speed(p_SpeedIn,0,&l_pSpeedTmp,l_u32TS_Diff);
//		else
//			memcpy(&l_pSpeedTmp,&p_SpeedIn->m_StructSpeed_absolute,sizeof(SPEED));
//
//		LaserPose_CompensationBySLine(&l_pSpeedTmp  , &p_PosCur->m_Struct_LaserPos ,
//&p_PosCur->m_Struct_LaserPos , l_f32CorrTime); 		p_PosCur->m_Struct_LaserPos.m_u16ang =
// LaserPose_CompensationBySAng(p_SpeedIn , &p_PosCur->m_Struct_LaserPos , l_f32CorrTime);
//		if(p_SpeedIn->m_u16speedsetflag == 0)
//		{
//			memset(p_SpeedIn , 0 ,STRUCT_SIZE_INPUTSPEED);
//		}
//		Judge_VirtualPos(p_PosCur, p_PosOld);
//
//		Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0] ,
//&p_PosCur->m_Struct_LaserPos ,&g_sMappingXY_old
//,g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
//	}
//
//	OS_ENTER_CRITICAL();
//	memcpy(p_PosSend, p_PosCur,
// STRUCT_SIZE_LASERPOS);//send保存的都为激光器最后一点的位置,不清空,Old保存连续或初始定位数据,不清空
//	p_PosSend->m_u32timestamp = p_TS_ScanOver;
//	OS_EXIT_CRITICAL();

//}
// void TransPos_To_SendOutTS(ROBOT_XY *p_PosCur, ROBOT_XY *p_PosSend , ROBOT_XY *p_PosOld,
// INPUTSPEED *p_SpeedIn, TIMESTAMP p_TS_ScanOver)
//{
//	OS_CPU_SR cpu_sr;
//	float l_f32CorrTime = 0;
//	u32 l_u32TS_Diff = Get_TS_Diff(p_TS_ScanOver , p_PosSend->m_u32timestamp);

//	XYANG2ROBOT l_pLaserPosVir;
//	INPUTSPEED l_pSpeedTmp;
//	//int sumx=0,sumy=0,sumang=0;
//	INPUTSPEED l_pSpeedOld={NULL};
//	Corr_Speed(p_SpeedIn,g_f32ScanTime_HalfPer
//,&l_pSpeedTmp.m_StructSpeed_absolute,g_f32ScanTime_Per);
//	Corr_Speed(&l_pSpeedTmp,l_u32TS_Diff,&l_pSpeedTmp.m_StructSpeed_absolute,0);
//	if(p_PosSend->m_u16flag == ACTIVE
//		|| p_PosSend->m_u16flag == INITIAL)
//	{
//		memcpy(&l_pLaserPosVir, &p_PosSend->m_Struct_LaserPos, STRUCT_SIZE_XYANG2ROBOT);
//		LaserPose_CompensationBySLine(&l_pSpeedTmp.m_StructSpeed_absolute  , &l_pLaserPosVir ,
//&l_pLaserPosVir , l_u32TS_Diff); 		l_pLaserPosVir.m_u16ang =
// LaserPose_CompensationBySAng(p_SpeedIn , &l_pLaserPosVir , l_u32TS_Diff);
//	}
//
////	if(g_sSavePosCur.m_u16flag == ACTIVE)
////		g_u32CntOfACTIVE ++ ;
////	else
////	{
////		g_u32CntOfACTIVE =0;
//		//memset(&g_sInSpeedUsed,0,sizeof(g_sInSpeedUsed));
////	}
//	p_PosCur->m_u16CurrLayer = g_sMarkSets.m_u16layer;
//	for(u8
// l_u8i=0;l_u8i<g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In;l_u8i++)//修正混补发出坐标
//	{
//		if(g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark ==
// ISMARK) 			Corr_Mark_MixLayout_Final(&g_sFilterMark_SpeedCorr,l_u8i);
//	}
//	if((g_sSavePosCur.m_u16flag == ACTIVE)||(g_sSavePosCur.m_u16flag == INITIAL))
//	{
//		g_u32CntOfACTIVE ++ ;
//		Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0] ,
//&p_PosCur->m_Struct_LaserPos ,&g_sMappingXY_old
//,g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
////		Cal_MeanDev();
//		p_PosCur->m_u16meandev = Cal_MeanDev(&g_sMappingXY_old);
////		if(p_PosCur->m_u16meandev > 100)
////		{
////			p_PosCur->m_u16meandev =1;
////			Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0] ,
///&p_PosCur->m_Struct_LaserPos ,&g_sMappingXY_new
///,g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In); /			Cal_MeanDev(); /		}
//		g_u32CntOfNOACTIVE=0;
//		l_f32CorrTime = 62.5f/g_u8AngMode;
//		//Corr_Speed(p_SpeedIn,l_f32CorrTime,&p_SpeedIn->m_StructSpeed_absolute);
//		LaserPose_CompensationBySLine(&p_SpeedIn->m_StructSpeed_absolute ,
//&p_PosCur->m_Struct_LaserPos ,&p_PosCur->m_Struct_LaserPos, l_f32CorrTime);
////g_sSavePosCur中保存的位置为180°位置,需根据速度修正至360°
/// p_PosCur->m_Struct_LaserPos.m_u16ang
///=
// LaserPose_CompensationBySAng(p_SpeedIn , &p_PosCur->m_Struct_LaserPos ,l_f32CorrTime);
//		if(p_PosSend->m_u16flag == ACTIVE
//		|| p_PosSend->m_u16flag == INITIAL)
//		{
//			if(p_PosOld->m_u16meandev == p_PosCur->m_u16meandev)
//			{
//				p_PosCur->m_Struct_LaserPos.m_s32x = (p_PosCur->m_Struct_LaserPos.m_s32x +
// l_pLaserPosVir.m_s32x )/2; 				p_PosCur->m_Struct_LaserPos.m_s32y =
//(p_PosCur->m_Struct_LaserPos.m_s32y + l_pLaserPosVir.m_s32y )/2;
//			}
//			else
//			{
//				p_PosCur->m_Struct_LaserPos.m_s32x = (p_PosCur->m_Struct_LaserPos.m_s32x *
// p_PosOld->m_u16meandev + l_pLaserPosVir.m_s32x * p_PosCur->m_u16meandev)/(p_PosOld->m_u16meandev
// + p_PosCur->m_u16meandev ); 				p_PosCur->m_Struct_LaserPos.m_s32y =
// (p_PosCur->m_Struct_LaserPos.m_s32y
//* p_PosOld->m_u16meandev + l_pLaserPosVir.m_s32y * p_PosCur->m_u16meandev)/(p_PosOld->m_u16meandev
//+ p_PosCur->m_u16meandev );
//			}
//
////			if(abs(p_PosCur->m_Struct_LaserPos.m_u16ang - l_pLaserPosVir.m_u16ang )> 18000)
////				p_PosCur->m_Struct_LaserPos.m_u16ang = ((DEG_PERSCAN +
/// p_PosCur->m_Struct_LaserPos.m_u16ang
///+ l_pLaserPosVir.m_u16ang)/2)%DEG_PERSCAN; /			else /
/// p_PosCur->m_Struct_LaserPos.m_u16ang = ((u32)p_PosCur->m_Struct_LaserPos.m_u16ang +
/// l_pLaserPosVir.m_u16ang)/2;
//		}
//
//		p_PosCur->m_u8scannum = g_sFilterDist.m_u8In;
//		p_PosCur->m_u8marknum =
// Nav_Mark_Num(g_sFilterDistShortNewLast.m_StructMarkScanInfoAddr.m_u8In);
// p_PosCur->m_u32timestamp = p_TS_ScanOver; 		if(p_SpeedIn->m_u32timestamp != 0
//			&& p_SpeedIn->m_u16speedsetflag == 0)
//		{
//			memcpy(&l_pSpeedOld,p_SpeedIn,STRUCT_SIZE_INPUTSPEED);
////			l_pSpeedOld.m_f32spAngDirection += l_u32TS_Diff * p_SpeedIn->m_s32spdang *
/// g_f32Per10mDeg_Rad
///;
//		}
//		Cal_MoveSpeed(p_SpeedIn, p_PosCur, p_PosOld,p_TS_ScanOver,l_u32TS_Diff);
////		if(p_SpeedIn->m_u32timestamp != 0)
////			memcpy(&g_auSpeed[g_auspeedCnt%10],p_SpeedIn,STRUCT_SIZE_INPUTSPEED);
////		for(u8 i=0;i<10;i++)
////		{
////			sumx+=g_auSpeed[i].m_f32spLine_Abs;
////			//sumy+=g_auSpeed[i].m_StructSpeed_absolute.m_s16speedy;
////			sumang +=g_auSpeed[i].m_s32spdang;
////
////		}
////		p_SpeedIn->m_f32spLine_Abs = sumx/10;
////		//p_SpeedIn->m_StructSpeed_absolute.m_s16speedy = sumx/10;
////		p_SpeedIn->m_s32spdang = sumang/10;
////		if(l_pSpeedOld.m_u32timestamp != 0)
////		{
////			p_SpeedIn->m_f32spAngDirection = (l_pSpeedOld.m_f32spAngDirection +
/// p_SpeedIn->m_f32spAngDirection)/2; /		}
//		if(p_SpeedIn->m_u32timestamp != 0
//			&& l_pSpeedOld.m_u32timestamp != 0
//			&& p_SpeedIn->m_u16speedsetflag == 0)
//		{
//			if(p_PosOld->m_u16meandev == p_PosCur->m_u16meandev )
//			{
//				p_SpeedIn->m_f32spLine_Abs = (p_SpeedIn->m_f32spLine_Abs +
// l_pSpeedOld.m_f32spLine_Abs)/2; 				p_SpeedIn->m_StructSpeed_absolute.m_s16speedy =
//(p_SpeedIn->m_StructSpeed_absolute.m_s16speedy +
// l_pSpeedTmp.m_StructSpeed_absolute.m_s16speedy)/2;
// p_SpeedIn->m_StructSpeed_absolute.m_s16speedx = (p_SpeedIn->m_StructSpeed_absolute.m_s16speedx +
// l_pSpeedTmp.m_StructSpeed_absolute.m_s16speedx)/2; 				p_SpeedIn->m_s32spdang =
//(p_SpeedIn->m_s32spdang + l_pSpeedOld.m_s32spdang)/2;
//			}
//			else
//			{
//				p_SpeedIn->m_f32spLine_Abs = (p_SpeedIn->m_f32spLine_Abs * p_PosOld->m_u16meandev +
// l_pSpeedOld.m_f32spLine_Abs* p_PosCur->m_u16meandev)/(p_PosOld->m_u16meandev +
// p_PosCur->m_u16meandev ); 				p_SpeedIn->m_StructSpeed_absolute.m_s16speedy =
//(p_SpeedIn->m_StructSpeed_absolute.m_s16speedy *
// p_PosOld->m_u16meandev+l_pSpeedTmp.m_StructSpeed_absolute.m_s16speedy*
// p_PosCur->m_u16meandev)/(p_PosOld->m_u16meandev + p_PosCur->m_u16meandev );
//				p_SpeedIn->m_StructSpeed_absolute.m_s16speedx =
//(p_SpeedIn->m_StructSpeed_absolute.m_s16speedx*
// p_PosOld->m_u16meandev+l_pSpeedTmp.m_StructSpeed_absolute.m_s16speedx*
// p_PosCur->m_u16meandev)/(p_PosOld->m_u16meandev + p_PosCur->m_u16meandev );
// p_SpeedIn->m_s32spdang = (p_SpeedIn->m_s32spdang * p_PosOld->m_u16meandev+
// l_pSpeedOld.m_s32spdang* p_PosCur->m_u16meandev)/(p_PosOld->m_u16meandev + p_PosCur->m_u16meandev
// );
//			}
//		}
//
//		memcpy(p_PosOld, p_PosCur, STRUCT_SIZE_LASERPOS);
//		g_sSavePosOld.m_u16flag = ACTIVE;
//		StoreRobotSets(&g_sSavePosCur, FRAM_ADDR_ROBOT) ;
//	}
//	else
//	{
//		g_u32CntOfACTIVE = 0;
//		//g_u32CntOfNOACTIVE++;
//		memcpy(p_PosCur, p_PosSend, STRUCT_SIZE_LASERPOS);
//		p_PosCur->m_u32timestamp = p_TS_ScanOver;
//		p_PosCur->m_u16flag = VIRTUAL;
//		l_f32CorrTime = l_u32TS_Diff;
//		//Corr_Speed(p_SpeedIn,l_f32CorrTime,&p_SpeedIn->m_StructSpeed_absolute);
//		LaserPose_CompensationBySLine(&l_pSpeedTmp.m_StructSpeed_absolute  ,
//&p_PosCur->m_Struct_LaserPos , &p_PosCur->m_Struct_LaserPos , l_f32CorrTime);
//		p_PosCur->m_Struct_LaserPos.m_u16ang = LaserPose_CompensationBySAng(p_SpeedIn ,
//&p_PosCur->m_Struct_LaserPos , l_f32CorrTime); 		if(p_SpeedIn->m_u16speedsetflag == 0)
//		{
//			memset(p_SpeedIn , 0 ,STRUCT_SIZE_INPUTSPEED);
//		}
//		Judge_VirtualPos(p_PosCur, p_PosOld);
//
//		Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0] ,
//&p_PosCur->m_Struct_LaserPos ,&g_sMappingXY_old
//,g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
//	}
//
//	OS_ENTER_CRITICAL();
//	memcpy(p_PosSend, p_PosCur,
// STRUCT_SIZE_LASERPOS);//send保存的都为激光器最后一点的位置,不清空,Old保存连续或初始定位数据,不清空
//	p_PosSend->m_u32timestamp = p_TS_ScanOver;
//	OS_EXIT_CRITICAL();
////
////	g_sSavePosCur.m_u16meandev =0;
//	//Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_sXy2Robot[0] , &p_PosCur->m_Struct_LaserPos
//,&g_sMappingXY_old ,g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_u8In);
//	//Renew_AbsCoor_XY(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfo, p_PosCur,
//&g_sMappingXY_old,g_sFilterMark_SpeedCorr.m_u8In);
////	if(0)
////	{
////		Cal_MeanDev();
////	}
//}

void TransPos_To_SendOutTS(ROBOT_XY* p_PosCur,
                           ROBOT_XY* p_PosSend,
                           ROBOT_XY* p_PosOld,
                           INPUTSPEED* p_SpeedIn,
                           TIMESTAMP p_TS_ScanOver)
{
    p_PosCur->m_u16CurrLayer = g_sMarkSets.m_u16layer;

    if ((g_sSavePosCur.m_u16flag == ACTIVE) || (g_sSavePosCur.m_u16flag == INITIAL))
    {
        g_u32CntOfACTIVE++;
        Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0],
                          &p_PosCur->m_Struct_LaserPos,
                          &g_sMappingXY_old,
                          g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);

        p_PosCur->m_u16meandev = Cal_MeanDev(&g_sMappingXY_old);

        memcpy(p_PosOld, p_PosCur, STRUCT_SIZE_LASERPOS);
        g_sSavePosOld.m_u16flag = ACTIVE;
    }
    else
    {
        g_u32CntOfACTIVE = 0;
        // g_u32CntOfNOACTIVE++;
        memcpy(p_PosCur, p_PosSend, STRUCT_SIZE_LASERPOS);
        p_PosCur->m_u16flag = VIRTUAL;
        Judge_VirtualPos(p_PosCur, p_PosOld);
        Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[0],
                          &p_PosCur->m_Struct_LaserPos,
                          &g_sMappingXY_old,
                          g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In);
        p_PosCur->m_u16meandev = 0xffff;
    }
    memcpy(
        p_PosSend,
        p_PosCur,
        STRUCT_SIZE_LASERPOS);  // send保存的都为激光器最后一点的位置,不清空,Old保存连续或初始定位数据,不清空
}

void Send_ScanData(ROBOT_XY* p_SendPos,
                   STRUCT_FILTER_TARGET* p_Mark,
                   STRUCT_FILTER_TARGET_LITE* p_FilterMark,
                   u16* p_Dist,
                   u16* p_Pluse,
                   u16* p_FilterPluse,
                   u8 p_ScanNum,
                   u32 p_ScanOverTS)
{
    u8 l_u8err = 0;
    ROBOT_XY PosSend;
    u32 l_u32TimeStamp = 0;
    /*
    //if((OSFlagQuery(g_flagUser , &l_u8err) & USR_FLAG_GETPOSEDATA_SEND ) ==
    USR_FLAG_GETPOSEDATA_SEND)
    //{
        //清除
        OSFlagPost(g_flagUser, USR_FLAG_GETPOSEDATA_SEND,OS_FLAG_CLR, &l_u8err) ;
        if((g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_NAV)
        ||(g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MARK)
        )
        {
            ROBOT_XY PosSend;
            l_u32TimeStamp = Get_SendPos(&g_sSavePosSend,&PosSend);
            PackageAndSend_ScanNav_Data(&PosSend , p_Mark,p_FilterMark,l_u32TimeStamp,
    p_Dist,p_Pluse,p_ScanNum);
        }
        else
        {
            PackageAndSend_ScanData( p_Dist ,p_Pluse,p_FilterPluse , p_ScanNum ,p_ScanOverTS);
        }
    }

    if(((OSFlagQuery(g_flagUser , &l_u8err) & USR_FLAG_GETPOSE_SEND ) ==  USR_FLAG_GETPOSE_SEND) &&
        (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_NAV)
    )
    {
        OSFlagPost(g_flagUser, USR_FLAG_GETPOSE_SEND,OS_FLAG_CLR, &l_u8err) ;

        g_u8HasRecvFlag =0;

        l_u32TimeStamp = Get_SendPos(p_SendPos , &PosSend);

        if(g_u8ProtocolMode == PROTOCOL_SICK )
        {
            TCP_NavGetPose(&PosSend, 1, l_u32TimeStamp) ;
        }
        else
        {
            Doing_Ack3(&PosSend, l_u32TimeStamp);
        }
    }
    */
}

u8 Get_NavMarkNum(u8 l_u8SeltMarkNum)
{
    return (l_u8SeltMarkNum >= 3) ? 3 : 2;
}

u16 Find_IncludedAng_Min(u16 p_Ang_In1, u16 p_Ang_In2)
{
    u16 l_u16IncludedAng = 0;

    l_u16IncludedAng = (DEG_PERSCAN + p_Ang_In1 - p_Ang_In2) % DEG_PERSCAN;

    //（360+ 350-10 ）%360 = 340 ，实际上为20，340>180  return 360-340
    //（360+ 10 - 350 ）%360 = 20 ，20<=180  return 20
    return (l_u16IncludedAng > 18000) ? (DEG_PERSCAN - l_u16IncludedAng) : l_u16IncludedAng;
}

u16 Find_IncludedAng_And_Direction(u16 p_Ang_In1, u16 p_Ang_In2, u8* p_Direction)
{
    u16 l_u16IncludedAng = 0;
    int l_s32CurAng = p_Ang_In1;
    int l_s32OldAng = p_Ang_In2;
    if (l_s32CurAng >= l_s32OldAng)
    {
        if ((l_s32CurAng - l_s32OldAng) <= 18000)
        {
            *p_Direction = 1;  //逆时针
            return (l_s32CurAng - l_s32OldAng);
        }
        else
        {
            *p_Direction = 0;  //顺时针
            return (DEG_PERSCAN + l_s32OldAng - l_s32CurAng);
        }
    }
    else
    {
        if ((l_s32OldAng - l_s32CurAng) <= 18000)
        {
            *p_Direction = 0;  //顺时针
            return (l_s32OldAng - l_s32CurAng);
        }
        else
        {
            *p_Direction = 1;  //逆时针
            return (DEG_PERSCAN + l_s32CurAng - l_s32OldAng);
        }
    }
}
void Comb_For_InitialNav()
{
    u8 l_u8i;
    u8 l_u8bufa[COM_BUF_MAX] = {0};  //最多就扫描这么多靶，保证不会越界
    u8 l_u8bufb[COM_BUF_MAX] = {0};
    u8 l_u8CombMarkSize = 0;
    //	for(l_u8i = 0 ;l_u8i < g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_u8In ;l_u8i++)
    //	{
    //		l_u8bufa[l_u8i] = l_u8i ;
    //	}
    memset((void*)&g_sCombofDecMark, 0, STRUCT_SIZE_COMBINE1);
    memset((void*)&g_sComb, 0, STRUCT_SIZE_COMBINE);

    if (g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In > COM_BUF_MAX)  // COM_BUF_MAX)
        l_u8CombMarkSize = COM_BUF_MAX;
    else
        l_u8CombMarkSize = g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In;

    for (l_u8i = 0; l_u8i < l_u8CombMarkSize; l_u8i++)
    {
        l_u8bufa[l_u8i] = l_u8i;
    }
    combine1(l_u8CombMarkSize,
             3,
             l_u8bufa,
             l_u8bufb,
             3);  // combine(COM_BUF_MAX,3,l_u8bufa,l_u8bufb,3) ;
    g_u16CombSize = g_sCombofDecMark.m_u16size;
}
u8 WorkMode_Initial_Position(STRUCT_FILTER_TARGET_LITE* p_ScanMark,
                             TIMESTAMP p_ScanOverTS,
                             INPUTSPEED* p_Speed)
{
    u16 l_u16i;
    u8 l_u8MatchPosFlag = 0;
    u16 l_u16MatchTriNum = 0;
    int l_s32SelectFourth = 0;
    u8* l_u8TriBufOffset = NULL;
    u8 l_u8j = 0;
    u8 l_u8cnt = 0;
    u8 l_u8ret = 1;
    u8 l_u8FindClosestFlag = 0;
    ROBOT_XY* p_TmpPos = NULL;
    ROBOT_XY* l_psPos[2] = {0};
    // g_u16NavPoweroff = 0;

    g_sSavePosCur.m_u16flag = NOACTIVE;

    if ((g_u32CntOfNOACTIVE < g_sMarkMatch_Set.m_u16LocalMapUseNum)
        && (g_sLocalMap_Info_LastCircle.m_u32MarkMatchNum >= g_sMarkMatch_Set.m_u16LocalMapMarkNum))
    {
        g_u8IntinalFlag = 1;
    }
    else
        g_u8IntinalFlag = 0;

    g_sSavePosOld.m_Struct_LaserPos.m_s32x = g_sFusedPosOld.m_Struct_LaserPos.m_s32x;
    g_sSavePosOld.m_Struct_LaserPos.m_s32y = g_sFusedPosOld.m_Struct_LaserPos.m_s32y;
    g_sSavePosOld.m_Struct_LaserPos.m_u16ang = g_sFusedPosOld.m_Struct_LaserPos.m_u16ang;
    g_sSavePosOld.m_u16flag = g_sFusedPosOld.m_u16flag;
    g_sSavePosSet.m_Struct_LaserPos.m_s32x = g_sFusedPosOld.m_Struct_LaserPos.m_s32x;
    g_sSavePosSet.m_Struct_LaserPos.m_s32y = g_sFusedPosOld.m_Struct_LaserPos.m_s32y;
    g_sSavePosSet.m_Struct_LaserPos.m_u16ang = g_sFusedPosOld.m_Struct_LaserPos.m_u16ang;
    g_sSavePosSet.m_u16flag = g_sFusedPosOld.m_u16flag;

    // ROS_ERROR("Init_Pose:[x=%d, y=%d, rz=%d]", g_sFusedPosOld.m_Struct_LaserPos.m_s32x,
    // g_sFusedPosOld.m_Struct_LaserPos.m_s32y, g_sFusedPosOld.m_Struct_LaserPos.m_u16ang );
    if (g_sSavePosSet.m_u16flag == POSSET_ROUGH)
    {
        // g_u16NavPoweroff = 0;
        l_psPos[l_u8j++] = &g_sSavePosSet;
    }
    l_psPos[l_u8j++] = &g_sSavePosOld;

    if (p_ScanMark->m_StructMarkScanInfoAddr.m_u8In >= 3)
    {
        Comb_For_InitialNav();
        g_u16CombOffset = 0;
        do
        {
            Memset_IsMarkFlag(p_ScanMark);
            memset((void*)&g_auXY2EqlTri, 0, STRUCT_SIZE_XYANG2ROBOT * EQLTRI_SIZE);
            memset((void*)&g_sEqlTri, 0, STRUCT_SIZE_STRUCT_EqlTri);
            l_u16MatchTriNum = Match_Mark_By_SetTri1(
                p_ScanMark,
                g_u16CombSize,
                g_u16CombOffset,
                &g_sMarkSets);  //等于0说明没匹配上三角形，等于-1表示靶个数小于3个，n代表匹配到n个三角形

            printf("mark init match triangle: %d\n", l_u16MatchTriNum);
            if (l_u16MatchTriNum > 0)
            {
                l_u8TriBufOffset = &g_sEqlTri.buf[0];
                for (l_u16i = 0; l_u16i < g_sEqlTri.m_u8In;
                     l_u16i++)  // for循环完毕可以得到每个全等三角形的定位信息
                {
                    memset((void*)&g_sFilterDistShortNewLast, 0, STRUCT_SIZE_FILTER_TARGET_LITE);

                    CopyNavMark(&g_sFilterDistShortNewLast,
                                p_ScanMark,
                                3,
                                l_u8TriBufOffset,
                                &g_sEqlTri,
                                l_u16i);

                    //将数据发给fpga 计算
                    Cal_Laser_Pose_ByFpga(
                        p_ScanMark, &g_sFpgaPos, l_u8TriBufOffset, 1, &g_sSavePosCur, 3);

                    g_sSavePosCur.m_Struct_LaserPos.m_u16ang =
                        Cal_Laser_AbsAng(&g_sFilterDistShortNewLast,
                                         g_sFilterDistShortNewLast.m_StructMarkScanInfoAddr.m_u8In,
                                         &g_sSavePosCur);

                    memcpy((void*)&g_auXY2EqlTri[l_u16i],
                           (void*)&g_sSavePosCur.m_Struct_LaserPos,
                           STRUCT_SIZE_XYANG2ROBOT);
                }

                if (l_u16MatchTriNum == 1)
                {
                    //	memset((void *)&g_sMarkSets.m_u8hasmatchflag, 0 , MAX_SIZE );
                    l_u8MatchPosFlag = Match_SetMark_By_LaserPose(p_ScanMark,
                                                                  &g_sSavePosCur,
                                                                  &g_sMappingXY_old,
                                                                  &g_sMarkSets,
                                                                  3,
                                                                  &g_sMarkMatch_Set);
                    if (!l_u8MatchPosFlag)
                    {
                        l_u8ret = Judge_InitialPos_Validity(
                            &g_sSavePosCur, l_psPos[0], p_ScanOverTS, p_Speed);
                        if (!l_u8ret)
                            WorkMode_Continue_Position(p_ScanOverTS, l_psPos[0]);
                    }
                }
                else
                {
                    l_u8MatchPosFlag = 1;
                    l_u8cnt = 0;
                    while (l_u8MatchPosFlag && (l_u8cnt < l_u8j))
                    {
                        l_u8FindClosestFlag = Find_Closest_Pos_to_SetPos(
                            l_psPos[l_u8cnt],
                            &g_sEqlTri,
                            &g_sSavePosCur,
                            &g_auXY2EqlTri[0]);  //返回1说明所有的定位超过3m，不符合要求。
                        l_u8cnt++;
                        if (l_u8FindClosestFlag)
                            continue;
                        memset((void*)&g_sMarkSets.m_u8hasmatchflag, 0, MAX_SIZE);
                        l_u8MatchPosFlag = Match_SetMark_By_LaserPose(p_ScanMark,
                                                                      &g_sSavePosCur,
                                                                      &g_sMappingXY_old,
                                                                      &g_sMarkSets,
                                                                      3,
                                                                      &g_sMarkMatch_Set);
                    }
                    if (!l_u8MatchPosFlag)  //如果真，靶标的ismarkFlag就是当前位置匹配情况
                    {
                        l_u8ret = Judge_InitialPos_Validity(
                            &g_sSavePosCur, l_psPos[0], p_ScanOverTS, p_Speed);
                    }
                    else
                    {
                        l_u8ret = 1;
                    }

                    if (l_u8ret)  //如果真，靶标的ismarkflag不一定是当前位置匹配情况，需要再用找到的位置再匹配一次
                    {
                        l_u8ret = Find_MatchMost_Pos(p_ScanMark,
                                                     &g_sMappingXY_old,
                                                     &g_sMarkSets,
                                                     &g_sSavePosCur.m_Struct_LaserPos,
                                                     &l_psPos[0]->m_Struct_LaserPos,
                                                     &g_sMarkMatch_Set);

                        if (!l_u8ret)
                        {
                            l_u8ret = Judge_InitialPos_Validity(
                                &g_sSavePosCur, l_psPos[0], p_ScanOverTS, p_Speed);
                            if (!l_u8ret)
                            {
                                g_sSavePosCur.m_u16flag = NOACTIVE;
                                //如果真，靶标的ismarkflag不一定是当前位置匹配情况，需要再用找到的位置再匹配一次
                                Match_SetMark_By_LaserPose(p_ScanMark,
                                                           &g_sSavePosCur,
                                                           &g_sMappingXY_old,
                                                           &g_sMarkSets,
                                                           3,
                                                           &g_sMarkMatch_Set);
                                g_sSavePosCur.m_u16flag = ACTIVE;
                            }
                        }
                    }
                    if (!l_u8ret)
                    {
                        WorkMode_Continue_Position(p_ScanOverTS, l_psPos[0]);
                        g_sSavePosSet.m_u16flag = NOACTIVE;
                    }
                }
            }

            // g_sSavePosCur.m_u16flag = NOACTIVE;
        } while (!((g_sSavePosCur.m_u16flag == ACTIVE) || (g_u16CombOffset >= g_u16CombSize)));
        g_u16CombOffset = 0;
    }

    if (g_sSavePosCur.m_u16flag == NOACTIVE)
    {
        g_u32CntOfNOACTIVE++;
        Memset_IsMarkFlag(p_ScanMark);
    }
    return l_u8ret;
}

u8 Judge_InitialPos_Validity(ROBOT_XY* p_NewPos,
                             ROBOT_XY* p_OldPos,
                             TIMESTAMP p_ScanOverTS,
                             INPUTSPEED* p_Speed)
{
    u32 l_u32TSDiff = 100;  // Get_TS_Diff(p_ScanOverTS,p_OldPos->m_u32timestamp);
    u32 l_u32DiffX = abs(p_NewPos->m_Struct_LaserPos.m_s32x - p_OldPos->m_Struct_LaserPos.m_s32x);
    u32 l_u32DiffY = abs(p_NewPos->m_Struct_LaserPos.m_s32y - p_OldPos->m_Struct_LaserPos.m_s32y);
    u16 l_u16IncludedAng = Find_IncludedAng_Min(p_NewPos->m_Struct_LaserPos.m_u16ang,
                                                p_OldPos->m_Struct_LaserPos.m_u16ang);
    u32 l_u32DiffAng;

    l_u32DiffAng = l_u32TSDiff * (abs(p_Speed->m_s32spdang) + 10);
    if (l_u32TSDiff > 1000 && p_Speed->m_s32spdang == 0)
    {
        l_u32DiffAng = 18000;
    }
    if (l_u32DiffAng > 18000)
        l_u32DiffAng = 18000;
    // ROS_INFO("l_u32DiffAng
    // =%d,l_u16IncludedAng=%d,l_u32DiffX=%d,l_u32DiffY=%d,g_u16NavPoweroff=%d",l_u32DiffAng,l_u16IncludedAng,l_u32DiffX,l_u32DiffY,g_u16NavPoweroff);

    // wen
    int tmp = g_u16NavPoweroff;
    g_u16NavPoweroff = 0;

    if (g_u16NavPoweroff)
    {
        if ((l_u16IncludedAng <= l_u32DiffAng)  // (4500>>(g_u8AngMode -1)))
            && (l_u32DiffX
                < (1000 + abs(p_Speed->m_StructSpeed_absolute.m_s16speedx * l_u32TSDiff)))
            && (l_u32DiffY
                < (1000 + abs(p_Speed->m_StructSpeed_absolute.m_s16speedy * l_u32TSDiff))))
        {
            p_NewPos->m_u16flag = ACTIVE;
            return 0;
        }
        else
        {
            p_NewPos->m_u16flag = NOACTIVE;
            return 1;
        }
    }
    else
    {
        g_u16NavPoweroff = tmp;
        p_NewPos->m_u16flag = ACTIVE;
        return 0;
    }
}

u8 WorkMode_Continue_Position(u32 p_ScanTS, ROBOT_XY* p_PosJudge)
{
    u8 l_u8SeltMarkNum = 0;
    u16 l_u16GapAng = 0;
    u16 l_u16i = 0;
    int l_s32ret = 0;
    u8 l_u8NavMarkNum = 0;

    memset((void*)&g_sFilterDistShortNewLast, 0, STRUCT_SIZE_FILTER_TARGET_LITE);

    Filter_NotMark_CopyAll(&g_sFilterDistShortNewLast, &g_sFilterMark_SpeedCorr);

    l_u8SeltMarkNum = SelMarkByDist(
        &g_sFilterDistShortNewLast,
        &g_sFilterDistShortNewLast);  //根据距离选择的靶标存入g_sFilterDistShortNewLast
    SortMarkByAng(&g_sFilterDistShortNewLast);  //靶标按照角度排序
    Select_MarkAng_Distribution_Most_Uniform(
        l_u8SeltMarkNum,
        &g_sFilterDistShortNewLast,
        &g_sFilterDistShortNewLast);  //选择分布最均匀的l_u8SeltMarkNum个靶

    l_u8NavMarkNum = Get_NavMarkNum(l_u8SeltMarkNum);
    // ROS_INFO("l_u8NavMarkNum=%d",l_u8NavMarkNum);
    Get_Ang_Distribution_Value(l_u8NavMarkNum, &g_sFilterDistShortNewLast);

    Get_LaserPos_WeightAver(
        &g_sFilterDistShortNewLast, &g_sFpgaPos, &g_sSavePosCur, l_u8NavMarkNum);

    l_s32ret = Judge_InitialPos_Validity(&g_sSavePosCur, p_PosJudge, p_ScanTS, &g_sInSpeedUsed);
    if (!l_s32ret)
    {
        g_u16NavPoweroff = 1;
        g_u32CntOfInitial = 0;
        g_sSavePosCur.m_u16flag = ACTIVE;
        if (g_u8TriActiveFlag == 1)
        {
            g_u16CombOffset = 0;
            g_u8TriActiveFlag = 0;
        }
        //	StoreRobotSets(&g_sSavePosCur, FRAM_ADDR_ROBOT) ;
        //	mapping_N模式时求位置的10次平均

        Save_LaserPos_To_AverBuf(&g_sSavePosCur, &g_sSavePosAverBuf[0]);

        Cal_Aver_LaserPos(&g_sSavePosCur, &g_sSavePosAverBuf[0], &g_sSavePosAver);
        memset(&g_sLocalMap_Info_LastCircle, 0, STRUCT_SIZE_LOCAL_MAP_INFO);
        memcpy(&g_sLocalMap_Info_LastCircle, &g_sLocalMap_Info, STRUCT_SIZE_LOCAL_MAP_INFO);
        return 0;
    }
    else
    {
        g_sSavePosCur.m_u16flag = NOACTIVE;
        memset((void*)&g_sSavePosCur, 0, STRUCT_SIZE_LASERPOS);
        return 1;
    }
}

void Save_LaserPos_To_AverBuf(ROBOT_XY* p_Cur_LaserPos, XYANG2ROBOT* p_LaserPosBuf)
{
    u16 l_u16GapAng = 0;
    u8 l_u8tmpcnt = 0;
    u8 l_u8Diff_XY = 30;                        //定位±15mm精度
    u8 l_u8Diff_Ang = 20 << (g_u8AngMode - 1);  //角度±0.1°,±0.2°精度
    u32 l_u32Diff_X = abs(p_Cur_LaserPos->m_Struct_LaserPos.m_s32x - p_LaserPosBuf[0].m_s32x);
    u32 l_u32Diff_Y = abs(p_Cur_LaserPos->m_Struct_LaserPos.m_s32y - p_LaserPosBuf[0].m_s32y);
    l_u16GapAng =
        Find_IncludedAng_Min(p_Cur_LaserPos->m_Struct_LaserPos.m_u16ang, p_LaserPosBuf[0].m_u16ang);

    if (Points_Diff_Cmp(l_u32Diff_X, l_u8Diff_XY) && Points_Diff_Cmp(l_u32Diff_Y, l_u8Diff_XY)
        && Points_Diff_Cmp(l_u16GapAng, l_u8Diff_Ang) && (g_u32CntOfACTIVE_AVER > 0))
    {
        l_u8tmpcnt = g_u32CntOfACTIVE_AVER % (POS_AVER_CNT - 1) + 1;  //保证第0位不被更新
        if (p_Cur_LaserPos->m_Struct_LaserPos.m_u16ang > g_u16MaxAng_AVER)
        {
            g_u16MaxAng_AVER = p_Cur_LaserPos->m_Struct_LaserPos.m_u16ang;
        }
    }
    else
    {
        g_u32CntOfACTIVE_AVER = 0;
        l_u8tmpcnt = 0;
        g_u16MaxAng_AVER = 0;
        memset(p_LaserPosBuf, 0, STRUCT_SIZE_XYANG2ROBOT * POS_AVER_CNT);
    }

    memcpy(&p_LaserPosBuf[l_u8tmpcnt], &p_Cur_LaserPos->m_Struct_LaserPos, STRUCT_SIZE_XYANG2ROBOT);
    g_u32CntOfACTIVE_AVER++;
}

void Cal_Aver_LaserPos(ROBOT_XY* p_Cur_LaserPos,
                       XYANG2ROBOT* p_LaserPosBuf,
                       ROBOT_XY* p_Aver_LaserPos)
{
    u8 l_u16i;
    u16 l_u16TmpAng = 0;
    XYANG2ROBOT l_sSumPos = {0};
    u32 l_u32SumAng = 0;
    if (g_u32CntOfACTIVE_AVER >= POS_AVER_CNT)
    {
        if ((g_u32CntOfACTIVE_AVER % POS_AVER_CNT) == 0)
        {
            for (l_u16i = 0; l_u16i < POS_AVER_CNT; l_u16i++)
            {
                l_sSumPos.m_s32x += p_LaserPosBuf[l_u16i].m_s32x;
                l_sSumPos.m_s32y += p_LaserPosBuf[l_u16i].m_s32x;
                l_u32SumAng += CorrAng_ByLoop(p_LaserPosBuf[l_u16i].m_u16ang, g_u16MaxAng_AVER);
            }
            p_Aver_LaserPos->m_Struct_LaserPos.m_s32x = l_sSumPos.m_s32x / POS_AVER_CNT;
            p_Aver_LaserPos->m_Struct_LaserPos.m_s32y = l_sSumPos.m_s32y / POS_AVER_CNT;
            l_u16TmpAng = (l_u32SumAng / POS_AVER_CNT) % DEG_PERSCAN;

            // p_Aver_LaserPos->m_Struct_LaserPos.m_u16ang = Round_Ang(l_u16TmpAng,
            // g_u8Ang_Resolution);
            g_sSavePosAver.m_u16flag = ACTIVE;
        }
    }
    else
    {
        g_sSavePosAver.m_u16flag = NOACTIVE;
        memset(&g_sSavePosAver, 0, STRUCT_SIZE_LASERPOS);
    }
}

/*************************************************
Function		:	Copy_UsingData
Description		:	拷贝定位需要使用的数据
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_u8pingpong:乒乓模式,
                    u16 **p_u16pulse_aver:存储滑动平均后的脉宽数组的指针的指针
                    u16 **p_u16pulse01 :存储经反射率表绿后的脉宽数组的指针的指针
                    u16 **p_u16pulse  :存储脉宽数组的指针的指针
                    u16 **p_u16dist :存储距离数组的指针的指针
Output			:	无
Return			:	无
Others			:	无
*************************************************/
u8 Copy_UsingData(u8 p_u8pingpong, u16** p_u16pulse_aver, u16** p_u16pulse, u16** p_u16dist)
{
    if (p_u8pingpong == PING)
    {
        // PING :  写PING, 读PONG
        //		memcpy((void *)&g_us16DistBufCopy[0], (void *)&g_us16DistBufPong[0], sizeof(
        // g_us16DistBufCopy)); 		memcpy((void *)&g_us16PulseWidthBufCopy[0], (void
        //*)&g_us16PulseWidthBufPong[0], sizeof( g_us16PulseWidthBufCopy)); 		memcpy((void
        //*)&g_us16PulseWidthBuf01Copy[0], (void *)&g_us16PulseWidthBuf01Pong, sizeof(
        // g_us16PulseWidthBuf01Copy));
        *p_u16pulse_aver = &g_us16PulseWidth_Aver_Pong[0];
        memset((void*)&g_us16PulseWidth_Aver_Pong[0], 0, sizeof(g_us16PulseWidth_Aver_Pong));
        // memcpy((void *)&g_sPulseCarveBufCopy, (void *)&g_sPulseCarveBufPong, sizeof(
        // g_sPulseCarveBufCopy));
        g_sPulseCarveBufCopy = &g_sPulseCarveBufPong;
        //*p_u16pulse01 = &g_us16PulseWidthBuf01Pong[0]  ;
        *p_u16pulse = &g_us16PulseWidthBufPong[0];
        *p_u16dist = &g_us16DistBufPong[0];
    }
    else
    {
        //		memcpy((void *)&g_us16DistBufCopy[0], (void *)&g_us16DistBufPing[0], sizeof(
        // g_us16DistBufCopy)); 		memcpy((void *)&g_us16PulseWidthBufCopy[0], (void
        //*)&g_us16PulseWidthBufPing[0], sizeof( g_us16PulseWidthBufCopy)); 		memcpy((void
        //*)&g_us16PulseWidthBuf01Copy[0], (void *)&g_us16PulseWidthBuf01Ping[0], sizeof(
        // g_us16PulseWidthBuf01Copy));
        *p_u16pulse_aver = &g_us16PulseWidth_Aver_Ping[0];
        memset((void*)&g_us16PulseWidth_Aver_Ping[0], 0, sizeof(g_us16PulseWidth_Aver_Ping));
        // memcpy((void *)&g_sPulseCarveBufCopy, (void *)&g_sPulseCarveBufPing, sizeof(
        // g_sPulseCarveBufCopy));
        g_sPulseCarveBufCopy = &g_sPulseCarveBufPing;
        //*p_u16pulse01 = &g_us16PulseWidthBuf01Ping[0]  ;
        *p_u16pulse = &g_us16PulseWidthBufPing[0];
        *p_u16dist = &g_us16DistBufPing[0];
    }
    //	*p_u16pulse01 = &g_us16PulseWidthBuf01Copy[0] ;
    //	*p_u16pulse = &g_us16PulseWidthBufCopy[0]   ;
    //	*p_u16dist = &g_us16DistBufCopy[0];
    return 0;
}

/*************************************************
Function		: Empty_UsingBuf
Description		: 清空需要使用的数组
Calls			: 无
Called By		: TaskTargetApp
Table Accessed	: 无
Table Updated	: 无
Input			: 无
Output			: 无
Return			: 无
Others			: 无
*************************************************/
void Empty_UsingBuf(void)
{
    memset((void*)&g_sFilterDist, 0, STRUCT_SIZE_FILTER);
    memset((void*)&g_sFilterMark_SpeedCorr, 0, STRUCT_SIZE_FILTER_TARGET_LITE);
    memset((void*)&g_sMappingXY_old, 0, STRUCT_SIZE_XY2ROBOT_CNT);
    memset((void*)&g_sFilterDistShortNewLast, 0, STRUCT_SIZE_FILTER_TARGET_LITE);
}

/*************************************************
Function		: Filter_FakeMark_ByPointNum
Description		: 根据靶标上的点数滤除假靶
Calls			: 无
Called By		: TaskTargetApp
Table Accessed	: 无
Table Updated	: 无
Input			: p_MarkNum:疑似靶标个数 p_Filter:储存过滤假靶标结构体的首地址
Output			: 无
Return			: 无
Others			: 无
*************************************************/
void Filter_FakeMark_ByPointNum(u8 p_MarkNum,
                                STRUCT_FILTER_TARGET* p_Filter)  //根据靶标上的点数滤除假靶
{
    u8 l_u8i;
    u16 l_u16MarkPointNum_Theory;
    u16 l_u16MarkPointNum;
    for (l_u8i = 0; l_u8i < p_MarkNum; l_u8i++)
    {
        l_u16MarkPointNum_Theory =
            Cal_MarkDot_ByDist_Theory(p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist);
        // l_u16MarkPointNum = (p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanEnd + g_u16ScanPointNum -
        // p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanSta) % g_u16ScanPointNum;
        l_u16MarkPointNum = (p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanEnd
                             - p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanSta);
        if (l_u16MarkPointNum >= l_u16MarkPointNum_Theory)
            p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = ISMARK;
        else
            p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = NOTMARK;
    }
    // p_Filter->m_StructMarkInfo[p_MarkNum].m_u8IsMark = NOTMARK;
}

//有角速度时，过滤被扫描两次的同一靶
//当0刻度位置正好有靶，且agv正好顺时针转（雷达为逆时针出数），
void Filter_SameMark_BySpeedRad(STRUCT_FILTER_TARGET* p_Filter)
{
    u8 l_u8size = p_Filter->m_u8In;

    // 36000/ (  5mdeg+  （100/7200） *角速度（单位：10mg/100ms ）)

    u16 l_u16FilterMark_Ang =
        36000 / (g_u8Ang_Resolution + g_f32Time_PerResolution * g_sInSpeedUsed.m_s32spdang);

    if (g_sInSpeedUsed.m_s32spdang > 0)
    {
        for (u8 l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            if (p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang
                > l_u16FilterMark_Ang)  // m_u16Ang 储存的最高强度点的index
                p_Filter->m_StructMarkInfo[l_u8size - 1].m_u8IsMark = NOTMARK;
        }
    }
}

/**
 * 根据各个靶标强度最高点的距离，剔除距离在盲区的靶标
 */
void Filter_Mark_ByScanRadius(STRUCT_FILTER_TARGET* p_Filter)
{
    u8 l_u8size = p_Filter->m_u8In;
    u8 l_u8i;
    u16 l_u16Dist = 0;
    u8* l_u8IsMarkFlag = NULL;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u16Dist = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist;
        l_u8IsMarkFlag = &p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark;
        if ((ISMARK == *l_u8IsMarkFlag)
            && ((l_u16Dist > g_sMarkMatch_Set.m_u32MarkScan_Max)
                || (l_u16Dist < g_sMarkMatch_Set.m_u32MarkScan_Min)))
        {
            *l_u8IsMarkFlag = NOTMARK;
        }
    }
}

void Memset_IsMarkFlag(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark)
{
    u8 l_u8i = 0;
    u8 l_u8size = p_NAV_Mark->m_StructMarkScanInfoAddr.m_u8In;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)  // reset IsMark标志,但不能全清空
    {
        p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark = NOTMARK;
    }
}
