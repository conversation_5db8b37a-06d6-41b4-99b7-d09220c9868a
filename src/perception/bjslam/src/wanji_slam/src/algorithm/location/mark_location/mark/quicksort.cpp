/*
 * @Author: your name
 * @Date: 2021-06-02 13:56:10
 * @LastEditTime: 2021-06-02 14:36:51
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/src/algorithm/location/mark_location/mark/quicksort.cpp
 */
#define __QUICKSORT_C
#include "algorithm/location/mark_location/mark/quicksort.h"
#include "algorithm/location/mark_location/mark/kdtree.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
QUICKSORT_EXT MARK_XY* g_psStructMarkAddr[MAX_SIZE];
void SWAP_MARK(MARK_XY** x, MARK_XY** y)
{
    MARK_XY* t;
    t = *x;
    *x = *y;
    *y = t;
}

int partition_MARK(MARK_XY* p_MarkSet, int left, int right, char axis, MARK_XY** p_MarkAddr)
{
    int i, j, s;
    int l_s32Data;
    if (axis)
        s = p_MarkAddr[right]->m_s32y;
    else
        s = p_MarkAddr[right]->m_s32x;

    i = left - 1;

    for (j = left; j < right; j++)
    {
        if (axis)
            l_s32Data = p_MarkAddr[j]->m_s32y;
        else
            l_s32Data = p_MarkAddr[j]->m_s32x;

        if (l_s32Data <= s)
        {
            i++;
            SWAP_MARK(&p_MarkAddr[i], &p_MarkAddr[j]);
        }
    }

    SWAP_MARK(&p_MarkAddr[i + 1], &p_MarkAddr[right]);
    return i + 1;
}

void quicksort_Marks(MARK_XY* p_MarkSet, int left, int right, char axis, MARK_XY** p_MarkAddr)
{
    int q;

    if (left < right)
    {
        q = partition_MARK(p_MarkSet, left, right, axis, p_MarkAddr);
        quicksort_Marks(p_MarkSet, left, q - 1, axis, p_MarkAddr);
        quicksort_Marks(p_MarkSet, q + 1, right, axis, (p_MarkAddr));
    }
}

int Return_AxisData(u8 axis, MARK_XY* p_Mark)
{
    if (axis)
        return p_Mark->m_s32y;
    return p_Mark->m_s32x;
}
