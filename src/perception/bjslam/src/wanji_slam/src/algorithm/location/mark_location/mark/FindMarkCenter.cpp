#include "algorithm/location/mark_location/mark/FindMarkCenter.h"
#include "algorithm/location/mark_location/mark/ExSram.h"
#include "algorithm/location/mark_location/mark/Marks.h"
#include "algorithm/location/mark_location/mark/TaskTargetApp.h"
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

extern sSysPib g_sSysPib;
extern sFactorCorr g_sFactorCorr;
extern ROBOT_XY g_sSavePosCur;
extern INPUTSPEED g_sInSpeedUsed;
extern u8 g_u8Ang_Resolution;
extern ROBOT_XY g_sSavePosOld;
extern MARKS_EXT __IO u16 g_u16ScanPointNum;
extern MARKS_EXT __IO u16 g_au16max_dist[32];
extern MARKS_EXT __IO u16 g_au16min_dist[32];
extern MARKS_EXT __IO u16 g_u16MarkMaxPointNum;
EX_SRAM_EXT STRUCT_CARVE g_sPulseCarveBufPing;   //(CCM_RAM);
EX_SRAM_EXT STRUCT_CARVE* g_sPulseCarveBufCopy;  //(CCM_RAM);
EX_SRAM_EXT STRUCT_CARVE g_sPulseCarveBufPong;   //(CCM_RAM);
EX_SRAM_EXT u16
    g_us16PulseWidthBuf01Copy[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
EX_SRAM_EXT u16
    g_us16PulseWidth_Aver_Ping[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
EX_SRAM_EXT u16
    g_us16PulseWidth_Aver_Pong[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
EX_SRAM_EXT u16
    g_us16PulseWidth_Aver[7200];  //(BANK1_SRAM1_ADDR);//;//(CCM_RAM); //经过阈值处理过后的
EX_SRAM_EXT u16 g_us16REl_pong[7200];                                  //(BANK1_SRAM1_ADDR);
EX_SRAM_EXT u16 g_us16REl_ping[7200];                                  //(BANK1_SRAM1_ADDR);
extern EX_SRAM_EXT STRUCT_FILTER_TARGET g_sFilterDist;                 //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterMark_SpeedCorr;  //(CCM_RAM);
EX_SRAM_EXT u16 g_us16PulseWidthBufPing[7200];                         //(CCM_RAM);
EX_SRAM_EXT u16 g_us16PulseWidthBufPong[7200];                         //(CCM_RAM);
EX_SRAM_EXT u16 g_us16PulseWidthBufCopy[7200];                         //(CCM_RAM);
EX_SRAM_EXT u16 g_us16DistBufPing[7200];                               //(BANK1_SRAM1_ADDR); //ping
EX_SRAM_EXT u16 g_us16DistBufPong[7200];                               //(BANK1_SRAM1_ADDR); //ping
FINDMARKCENTER_EXT u16 g_au16MarkMaxPluseDistRatio[TARGET_MAX];
FINDMARKCENTER_EXT u8 g_u8Mul[TARGET_MAX];
FINDMARKCENTER_EXT u16 g_u16DistDiffMax;
FINDMARKCENTER_EXT u16 g_u16RefValue;
FINDMARKCENTER_EXT u16 g_u16MinIncludedAng;
void Cal_PulseDist_Ratio(u16* p_pulse, u16* p_Ratio, u16* p_dist)
{
    u16 l_u16i;
    for (l_u16i = 0; l_u16i < g_u16ScanPointNum; l_u16i++)
    {
        if (p_dist[l_u16i] > 400)
            p_Ratio[l_u16i] = ((u32)p_pulse[l_u16i] << 5) / p_dist[l_u16i];
        else
            p_Ratio[l_u16i] = 0;
    }
}

/*************************************************
Function		:	Find_StaEnd_Point_RatioPulseDist
Description		:	根据斜率寻找靶标的起始点和结束点
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_PulseDistRatio : 滑动平均后的脉宽值首地址
                    p_MarkNum : 疑似靶标的总个数
                    p_Filter :存放靶标数据各信息的buf首地址
                    dist :距离数据-距离首地址
                    mul : 8或者9  判断高强度点脉宽 *0.8\0.9
Output			:	p_Filter ->m_u16Ang:存放靶标中心刻度
                    p_Filter ->m_u32Dist:存放靶标中心的扫描距离
Return			:	无
Others			:	无
*************************************************/
u32 Find_StaEnd_Point_RatioPulseDist(u16* p_PulseDistRatio,
                                     u8 p_MarkNum,
                                     STRUCT_FILTER_TARGET* p_Filter,
                                     u16* dist,
                                     u8* mul)
{
    u16 l_u16i = 0;
    u16 l_u16Max_Width_Point = 0;
    u16 l_u16Start = 0, l_u16End = 0;
    u16 l_u16Ang = 0;
    u16 l_u16Width_80Percent;
    u16 l_u16MarkCenter;

    for (l_u16i = 0; l_u16i < p_MarkNum; l_u16i++)
    {
        l_u16Max_Width_Point = p_Filter->m_StructMarkInfo[l_u16i].m_u16Ang;  //高强度点的下标
        l_u16Width_80Percent =
            p_PulseDistRatio[l_u16Max_Width_Point] * mul[l_u16i] / 10;  //按照靶标段最高强度0.8或0.9
        //寻找80%脉宽起始点，脉宽太宽（前移N次没找到）则返回高强度点，==舍弃靶标段
        l_u16Start = Find_80Percent_Sta_Point_PulseDist(
            p_PulseDistRatio, l_u16Max_Width_Point, l_u16Width_80Percent);  //寻找80%脉宽起始点
        l_u16End = Find_80Percent_End_Point_PulseDist(
            p_PulseDistRatio, l_u16Max_Width_Point, l_u16Width_80Percent);  //寻找80%脉宽结束点

        if (l_u16Start > l_u16End)  ////意味着被0切割，重置后的起点为7199部分终点为1部分
            l_u16End += g_u16ScanPointNum;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta = l_u16Start;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanEnd = l_u16End;
        l_u16MarkCenter = ((l_u16Start + l_u16End) >> 1) % g_u16ScanPointNum;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16Ang = l_u16MarkCenter;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16Dist = dist[l_u16MarkCenter];
    }
    return 0;
}

void Renew_MarkCenter_PulseDist(u16 p_Max_Width_Point,
                                u16* p_PulseDistRatio,
                                STRUCT_MARK_INFO* p_MarkInfo,
                                u16* p_dist)
{
    s8 l_s8Point_Type = 0;  //起始点 =1 ,结束点=-1;
    u16 l_u16i = 0;
    u16 l_u16Start = 0, l_u16End = 0;
    u16 l_u16Ang = 0;
    u16 l_u16Width_80Percent;
    u16 l_u16MarkCenter;

    l_u16Width_80Percent = p_PulseDistRatio[p_Max_Width_Point] * 9 / 10;
    //寻找80%脉宽起始点，脉宽太宽（前移N次没找到）则返回高强度点，==舍弃靶标段
    l_u16Start = Find_80Percent_Sta_Point_PulseDist(
        p_PulseDistRatio, p_Max_Width_Point, l_u16Width_80Percent);

    l_u16End = Find_80Percent_End_Point_PulseDist(
        p_PulseDistRatio, p_Max_Width_Point, l_u16Width_80Percent);  //寻找80%脉宽结束点

    if (l_u16Start > l_u16End)  //意味着被0切割，重置后的起点为7199部分终点为1部分
        l_u16End += g_u16ScanPointNum;  //求靶标中间点((sta+end)/2),加1是向上取整

    p_MarkInfo->m_u16ScanSta = l_u16Start;
    p_MarkInfo->m_u16ScanEnd = l_u16End;
    l_u16MarkCenter = ((l_u16Start + l_u16End) >> 1) % g_u16ScanPointNum;
    p_MarkInfo->m_u16Ang = l_u16MarkCenter;
    p_MarkInfo->m_u16Dist = p_dist[l_u16MarkCenter];
}
/*************************************************
Function		:	CopyFilterMark
Description		:	剔除无效靶标
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	pDistOld	:	存放原始靶标首地址
Output			:	pDistNew	:	存放剔除后靶标首地址
Return			:	无
Others			:	无
*************************************************/
u32 CopyFilterMark_WD(STRUCT_FILTER_TARGET* pDistOld, STRUCT_FILTER_TARGET* pDistNew)
{
    u8 l_u8cnt = 0;
    u8 l_u8i = 0, l_u8size = 0;
    u16 l_u16fixsize = 0;
    STRUCT_MARK_INFO *l_psMarkOld = NULL, *l_psMarkNew = NULL;

    l_psMarkOld = &pDistOld->m_StructMarkInfo[0];
    l_psMarkNew = &pDistNew->m_StructMarkInfo[0];
    l_u8size = pDistOld->m_u8In;

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        if ((l_psMarkOld + l_u8i)->m_u8IsMark == ISMARK)
        {
            //(l_psMarkOld + l_u8i)->m_u16Ang *= g_u8Ang_Resolution;	//统一成7200点
            (l_psMarkOld + l_u8i)->m_u8MarkSize = g_sSysPib.m_u16MarkRadio;  // 80
            (l_psMarkOld + l_u8i)->m_u8MarkType = g_sSysPib.m_u16MarkType;   // 0
            memcpy((void*)(l_psMarkNew + l_u8cnt),
                   (void*)(l_psMarkOld + l_u8i),
                   STRUCT_SIZE_MARK_INFO);
            g_au16MarkMaxPluseDistRatio[l_u8cnt] =
                l_psMarkNew[l_u8cnt].m_u16Ang;  //靶标段最高强度index集合
            l_u8cnt++;
        }
    }
    pDistNew->m_u8In = l_u8cnt;
    return 0;
}
/*************************************************
Function		: Filter_FakeMark_ByPointNum
Description		: 根据靶标上的点数滤除假靶
Calls			: 无
Called By		: TaskTargetApp
Table Accessed	: 无
Table Updated	: 无
Input			: p_MarkNum:疑似靶标个数 p_Filter:储存过滤假靶标结构体的首地址
Output			: 无
Return			: 无
Others			: 无
*************************************************/
void Filter_FakeMark_ByPointNum_WD(u8 p_MarkNum,
                                   STRUCT_FILTER_TARGET* p_Filter)  //根据靶标上的点数滤除假靶
{
    u8 l_u8i;
    u16 l_u16MarkPointNum_Theory;  //靶标点理论数
    u16 l_u16MarkPointNum;
    for (l_u8i = 0; l_u8i < p_MarkNum; l_u8i++)
    {
        l_u16MarkPointNum_Theory =
            Cal_MarkDot_ByDist_Theory(p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist);
        // l_u16MarkPointNum = (p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanEnd + g_u16ScanPointNum -
        // p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanSta) % g_u16ScanPointNum;
        l_u16MarkPointNum = (p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanEnd
                             - p_Filter->m_StructMarkInfo[l_u8i].m_u16ScanSta);
        if (l_u16MarkPointNum >= l_u16MarkPointNum_Theory)
            p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = ISMARK;
        else
            p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = NOTMARK;
    }
    // p_Filter->m_StructMarkInfo[p_MarkNum].m_u8IsMark = NOTMARK;
}

/**
 * 识别靶标
 */
void Find_ScanMark_PluseDist(u16* p_pulse, u16* p_Ratio, u16* p_dist, float p_CorrTime, u16* p_aver)
{
    OS_CPU_SR cpu_sr;
    u16 l_u16i;
    u8 l_u8MarkOffsetBuf[TARGET_MAX];
    Empty_UsingBuf();  //清空数组

    //寻找靶标脉宽最大点 ，m_u8In：是否存在靶标被0刻度切，数量==32 丢0号靶？ >32则=32
    //，填充g_u8Mul，8/9
    g_sFilterDist.m_u8In = Find_MaxWidthPoint_WD(p_Ratio,
                                                 &g_sFilterDist,
                                                 g_sPulseCarveBufCopy,
                                                 p_dist);  // p_dist是距离，不仅有脉冲还有距离值

    //寻找靶标起始和结束点，（脉宽强度，靶标段个数，靶标段最高强度点的index和dist，
    // 8或9表示最高脉宽的0.8/0.9找起点终点）
    Find_StaEnd_Point_RatioPulseDist(
        p_Ratio, g_sFilterDist.m_u8In, &g_sFilterDist, p_dist, g_u8Mul);

    //根据距离得出理论点*比例，//筛选真假靶标(靶标上点个数)//3
    Filter_FakeMark_ByPointNum_WD(g_sFilterDist.m_u8In, &g_sFilterDist);

    //有角速度时，过滤被扫描两次的同一靶
    Filter_SameMark_BySpeedRad(&g_sFilterDist);

    //将非is_MARK的剔除，靶标大小设置80 type 0 ，填充 g_au16MarkMaxPluseDistRatio
    //靶标段最高强度点index数组
    CopyFilterMark_WD(&g_sFilterDist, &g_sFilterDist);

    //填充各个靶标段最大最小距离数组
    Find_MaxMin_Dist(
        g_sFilterDist.m_u8In, p_dist, &g_sFilterDist, &g_au16max_dist[0], &g_au16min_dist[0]);

    //夹角过滤，标记过滤较远的靶.
    DeletCloseMark_WD(&g_sFilterDist, p_dist, p_pulse, p_Ratio);

    //靶上距离过滤，没懂
    FilterMark_ByMarkMaxMinDist_WD(&g_sFilterDist, p_dist, p_pulse, p_Ratio);

    //根据靶标的距离，过滤盲区内靶标
    Filter_Mark_ByScanRadius(&g_sFilterDist);

    //偏心修正，ang由index转 10mdeg
    CodeZeroCorrect(&g_sFilterDist);

    //修正圆靶距离（+靶标半径），剔除无效靶
    CopyFilterMark(&g_sFilterDist, &g_sFilterDist);

    if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark
        == SYSMODE_NavOrMappingOrMark_NAV)  //速度修正 265
    {
        memcpy(&g_sFilterDist.m_StructMarkInfoNoCorr,
               &g_sFilterDist.m_StructMarkInfo,
               STRUCT_SIZE_MARK_INFO * g_sFilterDist.m_u8In);
        CalAngOffsetBySpeed360TOHalf(
            &g_sFilterDist, &g_sSavePosCur, &g_sInSpeedUsed, p_CorrTime - 62.5f / g_u8AngMode);
    }

    for (u8 i = 0; i < g_sFilterDist.m_u8In; i++)
    {
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[i] =
            &g_sFilterDist.m_StructMarkInfo[i];
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[i] =
            &g_sFilterDist.m_sXy2Robot[i];
    }

    g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In = g_sFilterDist.m_u8In;
}

/*************************************************
Function		:	DeletCloseMark_PulseDist
Description		:	剔除靠的太近的靶（3°以内）
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_NAV_Mark	:可能用于定位的靶标
Output			:	p_NAV_Mark	:将过于近的靶标的ismark标志标记为tooclose
Return			:	无
Others			:	无
*************************************************/
void DeletCloseMark_PulseDist(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark, u16* p_Dist, u16* p_Pluse)
{
    u8 l_u8i;
    u8 l_u8NextMarkPoint = 0;
    u8 l_u8size = p_NAV_Mark->m_StructMarkScanInfoAddr.m_u8In;
    u16 l_u16MinDistBetweenMarks = XY_ERROR << 1;       // 20cm
    u16 l_u16MinAngBetweenMarks = g_u16MinIncludedAng;  // 3°
    u32 l_u32diff_Dist = 0, l_u32diff_Ang = 0;

    STRUCT_MARK_INFO** l_sMarkInfo = &p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    if (l_u8size == 1)
        return;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u8NextMarkPoint = (l_u8i + 1) % l_u8size;

        // l_u32diff_Dist = abs((l_sMarkInfo + l_u8i)->m_u16Dist - (l_sMarkInfo +
        // l_u8NextMarkPoint)->m_u16Dist);
        l_u32diff_Ang = Find_IncludedAng_Min((*(l_sMarkInfo + l_u8i))->m_u16Ang,
                                             (*(l_sMarkInfo + l_u8NextMarkPoint))->m_u16Ang);
        l_u16MinAngBetweenMarks = Return_FilterMinIncludedAng_WD(
            *(l_sMarkInfo + l_u8i), *(l_sMarkInfo + l_u8NextMarkPoint));
        if (l_u16MinAngBetweenMarks < g_u16MinIncludedAng)
            l_u16MinAngBetweenMarks = g_u16MinIncludedAng;
        if (  // Points_Diff_Cmp(l_u32diff_Dist , l_u16MinDistBetweenMarks)&&
            Points_Diff_Cmp(l_u32diff_Ang, l_u16MinAngBetweenMarks))  //两靶间最小间距
        {
            // if(Points_Diff_Cmp(l_u32diff_Ang , l_u16MinAngBetweenMarks))
            {
                if (p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Dist
                    > p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8NextMarkPoint]
                          ->m_u16Dist)
                {
                    p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark =
                        ISMARK_TOOCLOSE;
                    Find_MarkCenterByPulseAndDist(
                        p_Dist,
                        p_Pluse,
                        p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8NextMarkPoint]);
                }
                else
                {
                    p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8NextMarkPoint]
                        ->m_u8IsMark = ISMARK_TOOCLOSE;
                    Find_MarkCenterByPulseAndDist(
                        p_Dist,
                        p_Pluse,
                        p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]);
                }
            }
            //			else
            //			{
            //				Find_MarkCenterByPulseAndDist(p_Dist,p_Pluse,p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]);
            //				Find_MarkCenterByPulseAndDist(p_Dist,p_Pluse,p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8NextMarkPoint]);
            //			}

            //			p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark =
            // ISMARK_TOOCLOSE;
            //			p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8NextMarkPoint]->m_u8IsMark
            //= ISMARK_TOOCLOSE;
        }
    }
}

/*************************************************
Function		:	Find_80Percent_Sta_Point
Description		:	寻找80%脉宽点(起始点)
Calls			:	无
Called By		: 	Find_StaEnd_Point_Slope
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Aver : 滑动平均后的脉宽值首地址
                    Max_Width_Point : 靶标的脉宽最大点
Output			:	无
Return			:	l_u16j :80%脉宽点(起始点)
Others			:	无
*************************************************/
u16 Find_80Percent_Sta_Point_PulseDist(u16* Width_Aver, u16 Max_Width_Point, u16 p_Width_80Percent)
{
    u16 l_u16j = Max_Width_Point;
    u16 l_u16Cnt = 0;
    u16 l_u16Width_80Percent = 0;
    u16 l_u16MarkMaxPointNum_Half =
        g_u16MarkMaxPointNum >> 1;  // g_u16MarkMaxPointNum 设为20/0.05 = 40

    l_u16Width_80Percent = p_Width_80Percent;  //最大脉宽的80%
    while (1)
    {
        if (Width_Aver[l_u16j] < l_u16Width_80Percent)  //向前查找80%脉宽点
        {
            return (l_u16j + 1) % g_u16ScanPointNum;
        }
        else
        {
            l_u16Cnt++;
            l_u16j = (l_u16j - 1 + g_u16ScanPointNum) % g_u16ScanPointNum;  //过0为7199
        }

        if (l_u16Cnt >= l_u16MarkMaxPointNum_Half)  //靶标太宽不要 ，过滤金属面
        {
            return Max_Width_Point;
        }
    }
}

/*************************************************
Function		:	Find_80Percent_End_Point
Description		:	寻找80%脉宽点(结束点)
Calls			:	无
Called By		: 	Find_StaEnd_Point_Slope
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Aver : 滑动平均后的脉宽值首地址
                    Max_Width_Point : 靶标的脉宽最大点
Return			:	l_u16j :80%脉宽点(结束点)
Others			:	无
*************************************************/
u16 Find_80Percent_End_Point_PulseDist(u16* Width_Aver, u16 Max_Width_Point, u16 p_Width_80Percent)
{
    u16 l_u16j = Max_Width_Point;
    u16 l_u16Cnt = 0;
    u16 l_u16Width_80Percent = 0;
    u16 l_u16MarkMaxPointNum_Half = g_u16MarkMaxPointNum >> 1;

    l_u16Width_80Percent = p_Width_80Percent;  //(Width_Aver[Max_Width_Point] * p_mul) / 10;
    while (1)
    {
        if (Width_Aver[l_u16j] < l_u16Width_80Percent)  //向前查找80%脉宽点
        {
            return (l_u16j - 1 + g_u16ScanPointNum) % g_u16ScanPointNum;
        }
        else
        {
            l_u16Cnt++;
            l_u16j = (l_u16j + 1) % g_u16ScanPointNum;
        }

        if (l_u16Cnt >= l_u16MarkMaxPointNum_Half)  //靶标太宽不要
        {
            return Max_Width_Point;
        }
    }
}

/*************************************************
Function		: Find_MaxWidthPoint_WD
Description		: 找靶标起点 终点
Calls			: 无
Called By		: 自身
Table Accessed	: 无
Table Updated	: 无
Input			: Width_Carve:待定 | p_Filter: 待定  | p_CraveBuf:靶标起点终点范围  | p_dist
Output			: 无
Return			: 无
Others			: 无
*************************************************/
u8 Find_MaxWidthPoint_WD(u16* Width_Carve,
                         STRUCT_FILTER_TARGET* p_Filter,
                         STRUCT_CARVE* p_CraveBuf,
                         u16* p_dist)
{
    u8 l_u8cnt = 0;
    u16* l_u16start = NULL;
    u16* l_u16end = NULL;
    u16 l_u16offset = 0;
    u8 l_u8Least_DotCnt = 4 >> (g_u8AngMode - 1);

    u8 l_u8MoveBit = 5;
    u8 l_u8MaxMarkNum = 1 << l_u8MoveBit;  // 2^5，32个

    u16 l_u16PointDiff = 0;
    u8 l_u8MarkOffset = 0;

    //	l_u16offset = Find_ScanData_StaPoint(g_u16ScanPointNum, Width_Carve,30);
    ////寻找扫描起始点,防止靶标被0点分割 	l_u8cnt = Find_StaEnd_Point_Rough(l_u16offset
    ///,Width_Carve
    //,l_u8MoveBit , &l_u16start[0], &l_u16end[0] , l_u8Least_DotCnt,20);	//粗查找靶标起始点结束点

    u32 l_u8MarkSize = p_CraveBuf->m_u32MarkNum;
    // 1.首先判断第0个靶标和最后1个靶标
    // 起点终点是否过近，防止靶标被0点分割，是，则0靶标起点更新为最后一个靶起点，终点+7200，否则直接拿32卡的话，导致0靶其实是一半
    // 2.否则 判断靶标数量是否等于
    if ((p_CraveBuf->m_u16MarkSta[0] - p_CraveBuf->m_u16MarkEnd[l_u8MarkSize - 1]
         + g_u16ScanPointNum)
        <= MARK_COUNTIUEZERO_NUM)
    {
        p_CraveBuf->m_u16MarkSta[0] = p_CraveBuf->m_u16MarkSta[l_u8MarkSize - 1];
        p_CraveBuf->m_u16MarkEnd[0] += g_u16ScanPointNum;  //+7200
        l_u8MarkSize -= 1;
        l_u8MarkOffset = 0;
    }
    else if (l_u8MarkSize == TARGET_MAX)
    {  //等于32： -1，丢0号靶
        //（程序设定数组p_CraveBuf最大32，正常实际靶标>=32，因没32后续的靶标，无法判断是否被0切割，直接丢）
        l_u8MarkSize -= 1;
        l_u8MarkOffset = 1;
    }

    // 3种情况，默认为0靶标起点终点
    //若 靶标被0切割，则l_u16start和l_u16end 为新0靶标的起点终点地址
    //否 靶标==32个，则l_u16start和l_u16end 为1靶标的起点终点地址
    l_u16start = &p_CraveBuf->m_u16MarkSta[l_u8MarkOffset];
    l_u16end = &p_CraveBuf->m_u16MarkEnd[l_u8MarkOffset];

    //允许最多靶标，当前靶标数量，起点，终点，麦宽，靶标结构体，距离
    //填充mul 8/9
    Get_MaxWidth_WD(
        l_u8MaxMarkNum, l_u8MarkSize, l_u16start, l_u16end, Width_Carve, p_Filter, p_dist);
    return l_u8MarkSize;
}

/*************************************************
Function		:	Get_MaxWidth
Description		:	在起始与结束点间找到脉宽最大点
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	l_u8MaxMarkNum : 允许存放最多的靶标个数
                    p_MarkNum : 找到的靶标个数
                    l_u16start :存放靶标起始点首地址
                    l_u16end :存放靶标结束点首地址
                    Width_Carve :切后脉宽首地址
Output			:	Max_Width_Point :存入最大脉宽对应的刻度的首地址
Return			:	无
Others			:	无
*************************************************/
void Get_MaxWidth_WD(u8 p_u8MaxMarkNum,
                     u8 p_MarkNum,
                     u16* p_u16start,
                     u16* p_u16end,
                     u16* p_Width_Carve,
                     STRUCT_FILTER_TARGET* p_Filter,
                     u16* p_dist)
{
    u16 l_u16i = 0, l_u16j = 0;
    u16 l_u16WidthOld = 0;
    u16 l_u16offset = 0;
    u16 l_u16MarkAng = 0;
    if (p_MarkNum >= p_u8MaxMarkNum)
        p_MarkNum = p_u8MaxMarkNum;

    for (l_u16i = 0; l_u16i < p_MarkNum; l_u16i++)
    {
        //更新了p_Filter->m_StructMarkInfo[l_u16i]中的最高强度的距离和index，各个靶标段的g_u8Mul =
        // 8或者9
        FindPeakData(p_Width_Carve,
                     p_dist,
                     p_u16start[l_u16i],
                     p_u16end[l_u16i],
                     &p_Filter->m_StructMarkInfo[l_u16i],
                     &g_u8Mul[l_u16i]);
    }
}

/**
 * data：脉冲强度数组
 * p_dist：脉冲距离数组
 * Sta：靶标段起点
 * End：靶标段终点
 * p_MarkInfo：
 * mul： 各个靶标段的g_u8Mul 8或者9  代表什么
 */
void FindPeakData(u16* data, u16* p_dist, u16 Sta, u16 End, STRUCT_MARK_INFO* p_MarkInfo, u8* mul)
{
    u16 i;
    s16 xielv[500];
    u8 cnt = 0;
    u16 l_u16diffDist = 0;
    u16 OutData[10];
    u16 l_u16Dist[10];
    u16 l_u16WidthDist[10];
    u16 l_u16maxdist, l_u16mindist;
    u16 max_offset, min_offset;
    u16 cnt_xielv = 0;

    OutData[0] = ((Sta + End) >> 1) % g_u16ScanPointNum;  //靶标段中心点（(Sta + End)/2）

    l_u16Dist[0] = p_dist[OutData[0]];
    p_MarkInfo->m_u16Ang = OutData[0];
    p_MarkInfo->m_u16Dist = p_dist[OutData[0]];
    *mul = 8;

    if (End == Sta)
    {
        return;
    }
    if ((End - Sta) > 500)  //靶标被0刻度切割
    {
        return;
    }

    for (i = Sta; i < End; i++)
    {
        //存储下一个点与当前点的强度差
        xielv[cnt_xielv++] = data[(i + 1) % g_u16ScanPointNum] - data[i % g_u16ScanPointNum];
    }
    for (i = 0; i < cnt_xielv; i++)
    {
        // i+1点为三点中最高强度点，前一个点小，下一个点不再增
        if (xielv[i + 1] <= 0 && xielv[i] > 0)
        {
            OutData[cnt] = (i + 1 + Sta) % g_u16ScanPointNum;  // OutData 高强度的index集合
            l_u16Dist[cnt] = p_dist[OutData[cnt]];  // l_u16Dist 对应高强度index的距离集合
            l_u16WidthDist[cnt] = data[OutData[cnt]];  // l_u16WidthDist 高强度的index的脉宽集合
            cnt = cnt + 1;
            if (cnt >= 10)  //一个靶标段最多找10个满足的最高强度点
                break;
        }
    }
    if (cnt > 1)
    {
        //得到10个高强度点的最近最远距离，及最近距离点的cnt
        Find_MaxMin_Dist_WD(0, cnt - 1, l_u16Dist, &l_u16maxdist, &l_u16mindist, &min_offset);
        l_u16diffDist = l_u16maxdist - l_u16mindist;

        if (l_u16diffDist < g_u16DistDiffMax)
        {
            //第一个比0脉宽强的点cnt
            max_offset = Find_Max_WD(0, cnt - 1, l_u16WidthDist);
            p_MarkInfo->m_u16Ang = OutData[max_offset];  // Ang此时存的下标
            p_MarkInfo->m_u16Dist = p_dist[OutData[max_offset]];
            *mul = 8;
        }
        else
        {
            p_MarkInfo->m_u16Ang = OutData[min_offset];
            p_MarkInfo->m_u16Dist = p_dist[OutData[min_offset]];
            *mul = 9;
        }
    }
    else
    {
        p_MarkInfo->m_u16Ang = OutData[0];
        p_MarkInfo->m_u16Dist = p_dist[OutData[0]];
        *mul = 8;
    }
}

/**
 * sta：0
 * end： 最高强度index的个数
 * Dist 最高强度index的距离集合
 * 被修改max_dist：最高强度index的最大距离
 * 被修改min_dist：最高强度index的最小距离
 * 被修改Min_offset：最高强度数组中的 最近距离点的下表
 */
void Find_MaxMin_Dist_WD(u16 sta, u16 end, u16* Dist, u16* max_dist, u16* min_dist, u16* Min_offset)
{
    u16 l_u16DistOld_max = 0, l_u16DistOld_min = 0, l_u16j = 0;
    u16 l_u16Dist = 0;
    u16 l_u16tmp = 0;

    l_u16DistOld_max = Dist[sta];
    l_u16DistOld_min = l_u16DistOld_max;

    *Min_offset = sta;
    *max_dist = l_u16DistOld_max;
    *min_dist = l_u16DistOld_min;

    //遍历第二个至最后一个最高强度index
    for (l_u16j = sta + 1; l_u16j <= end; l_u16j++)
    {
        l_u16Dist = Dist[l_u16j];
        if (l_u16Dist > l_u16DistOld_max)  //找距离最远的index
        {
            l_u16DistOld_max = l_u16Dist;
        }
        if ((l_u16Dist < l_u16DistOld_min) &&  //找距离最近的index，且不为0
            (l_u16Dist != 0))
        {
            *Min_offset = l_u16j;
            l_u16DistOld_min = l_u16Dist;
        }
    }
    *max_dist = l_u16DistOld_max;
    *min_dist = l_u16DistOld_min;
}

/**
 * 找到第一个比0号点脉宽高的点下标
 * p_u16start：0
 * p_u16end： p_Data的下标最大
 * p_Data：靶标段的 高强度的index的 脉宽集合
 */
u16 Find_Max_WD(u16 p_u16start, u16 p_u16end, u16* p_Data)
{
    u16 l_u16j = 0;
    u16 l_u16WidthOld = 0;
    u16 l_u16ret = p_u16start;

    l_u16WidthOld = p_Data[p_u16start];

    for (l_u16j = p_u16start + 1; l_u16j <= p_u16end; l_u16j++)
    {
        if (p_Data[l_u16j] > l_u16WidthOld)
        {
            l_u16ret = l_u16j;
        }
    }
    return l_u16ret;
}

int Filter_Mark_By_Ref_WD(u16* PulseWidth, u16* Dist, u16* p_RefBuf)
{
    int l_s32ret = -1;
    //根据脉宽+距离来查表
    u32 l_u32addr;
    u16 l_u16tmp, l_u16lvl;
    s16 l_s16tmp;
    u32 l_u32pulse;
    //	if((*Dist >( g_sMarkMatch.m_u32MarkScan_Max+(g_sSysPib.m_u16MarkRadio>>1)) )||(*Dist <
    // g_sMarkMatch.m_u32MarkScan_Min))//如果是圆靶,边缘值会测的值稍大 		return l_s32ret ;
    *p_RefBuf = 0;
    if ((*PulseWidth > (700000 >> 4)) || (*PulseWidth < (1000 >> 4)))
        return l_s32ret;

    //	l_u16lvl = g_sSysPib.m_u16MarkDistinguish - g_sSysPib.m_u16MarkReflectivity;
    l_u32pulse = ((u32)(*PulseWidth) << 4);  //还原成32位的脉宽, 因为全部改成16位的了

    l_u32addr = ((*Dist + g_sFactorCorr.m_u16DistSub) / 1000 * 700) + l_u32pulse / 1000
                - 1;  //+32330* LIGHTSPEED /
    // 2000//反射率表是0-70m，间隔1000ps脉宽的表，因此中批脉宽修正表+小批反射率表需要加个固定偏移，由于机器内部硬件延时造成的。
    // Flash_Read_HalfWord(ADDR_FLASH_REFLEX_DATA + l_u32addr*2,1,&l_u16tmp);
    l_s16tmp = (s16)l_u16tmp;
    if (l_s16tmp < 0)
        *p_RefBuf = 0;
    else
        *p_RefBuf = l_u16tmp;
    //	if(*Dist < 400)
    //		*p_RefBuf = 0;
    //	else
    //		*p_RefBuf = l_u32pulse/(*Dist);
    //
    if (l_s16tmp >= g_u16RefValue)
        l_s32ret = 0;
    return l_s32ret;
}

void GetOutlineOfMarks_WD(u16* pResults_t,
                          u16* pResults_d,
                          u16 p_Offset,
                          STRUCT_CARVE* p_CarveBuf,
                          u16* p_Ref)
{
    static u16 l_u16MarkStaCnt = 0;
    static u16 l_u16MarkEndCnt = 0;
    static u8 l_u8HasMark = 0;
    u16 l_u16i;
    u16* l_punTime;
    u16* l_punDist;
    l_punTime = pResults_t;
    l_punDist = pResults_d;
    static u16 l_u16ZeroNum;
    if (p_Offset == 0)
    {
        l_u8HasMark = 0;
        l_u16MarkStaCnt = 0;
        l_u16MarkEndCnt = 0;
        l_u16ZeroNum = 0;
        memset(p_CarveBuf, 0, STRUCT_SIZE_CARVE);
    }
    for (l_u16i = p_Offset; l_u16i < DOT_NUM + p_Offset; l_u16i++)
    {
        if (l_u16MarkEndCnt < TARGET_MAX)
        {
            if ((!Filter_Mark_By_Ref_WD(l_punTime + l_u16i, l_punDist + l_u16i, &p_Ref[l_u16i]))
                && (*(pResults_d + l_u16i) > 10))
            {
                //*(p_u16pluse_cut+l_u16i) = *(pResults_t+l_u16i);
                if (l_u8HasMark == 0)
                {
                    l_u8HasMark = 1;
                    p_CarveBuf->m_u16MarkSta[l_u16MarkEndCnt] = l_u16i;
                    l_u16MarkStaCnt++;
                }
                l_u16ZeroNum = 0;
            }
            else
            {
                //*(p_u16pluse_cut+l_u16i) = 0 ;
                l_u16ZeroNum++;
                if ((l_u8HasMark == 1) && (l_u16ZeroNum >= MARK_COUNTIUEZERO_NUM))
                {
                    l_u8HasMark = 0;
                    p_CarveBuf->m_u16MarkEnd[l_u16MarkEndCnt] = l_u16i - l_u16ZeroNum;
                    l_u16MarkEndCnt++;
                }
            }
        }
        else
        {
            l_u8HasMark = 0;
            break;
        }
    }
    if ((l_u16i == g_u16ScanPointNum) && (l_u8HasMark == 1))
    {
        p_CarveBuf->m_u16MarkEnd[l_u16MarkEndCnt] = g_u16ScanPointNum - l_u16ZeroNum - 1;
        l_u16MarkEndCnt++;
    }
    p_CarveBuf->m_u32MarkNum = l_u16MarkEndCnt;
}

u8 Copy_UsingData_WD(u8 p_u8pingpong,
                     u16** p_u16pulse_aver,
                     u16** p_u16pulse,
                     u16** p_u16dist,
                     u16** p_u16ref)
{
    if (p_u8pingpong == PING)
    {
        // PING :  写PING, 读PONG
        //		memcpy((void *)&g_us16DistBufCopy[0], (void *)&g_us16DistBufPong[0], sizeof(
        // g_us16DistBufCopy)); 		memcpy((void *)&g_us16PulseWidthBufCopy[0], (void
        //*)&g_us16PulseWidthBufPong[0], sizeof( g_us16PulseWidthBufCopy)); 		memcpy((void
        //*)&g_us16PulseWidthBuf01Copy[0], (void *)&g_us16PulseWidthBuf01Pong, sizeof(
        // g_us16PulseWidthBuf01Copy));
        *p_u16pulse_aver = &g_us16PulseWidth_Aver_Pong[0];
        memset((void*)&g_us16PulseWidth_Aver_Pong[0], 0, sizeof(g_us16PulseWidth_Aver_Pong));
        // memcpy((void *)&g_sPulseCarveBufCopy, (void *)&g_sPulseCarveBufPong, sizeof(
        // g_sPulseCarveBufCopy));
        g_sPulseCarveBufCopy = &g_sPulseCarveBufPong;
        //*p_u16pulse01 = &g_us16PulseWidthBuf01Pong[0]  ;
        *p_u16pulse = &g_us16PulseWidthBufPong[0];
        *p_u16dist = &g_us16DistBufPong[0];
        *p_u16ref = &g_us16REl_pong[0];
    }
    else
    {
        //		memcpy((void *)&g_us16DistBufCopy[0], (void *)&g_us16DistBufPing[0], sizeof(
        // g_us16DistBufCopy)); 		memcpy((void *)&g_us16PulseWidthBufCopy[0], (void
        //*)&g_us16PulseWidthBufPing[0], sizeof( g_us16PulseWidthBufCopy)); 		memcpy((void
        //*)&g_us16PulseWidthBuf01Copy[0], (void *)&g_us16PulseWidthBuf01Ping[0], sizeof(
        // g_us16PulseWidthBuf01Copy));
        *p_u16pulse_aver = &g_us16PulseWidth_Aver_Ping[0];
        memset((void*)&g_us16PulseWidth_Aver_Ping[0], 0, sizeof(g_us16PulseWidth_Aver_Ping));
        // memcpy((void *)&g_sPulseCarveBufCopy, (void *)&g_sPulseCarveBufPing, sizeof(
        // g_sPulseCarveBufCopy));
        g_sPulseCarveBufCopy = &g_sPulseCarveBufPing;
        //*p_u16pulse01 = &g_us16PulseWidthBuf01Ping[0]  ;
        *p_u16pulse = &g_us16PulseWidthBufPing[0];
        *p_u16dist = &g_us16DistBufPing[0];
        *p_u16ref = &g_us16REl_ping[0];
    }
    //	*p_u16pulse01 = &g_us16PulseWidthBuf01Copy[0] ;
    //	*p_u16pulse = &g_us16PulseWidthBufCopy[0]   ;
    //	*p_u16dist = &g_us16DistBufCopy[0];
    return 0;
}

/*************************************************
Function		:	DeletCloseMark
Description		:	剔除靠的太近的靶（3°以内）
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Filter
:可能用于定位的靶标信息，p_Dist：各个点距离首指针，p_Pluse：，p_PulseDistRatio： Output			:
p_NAV_Mark	:将过于yuan的靶标的ismark标志标记为tooclose Return			:	无 Others			:
无
*************************************************/
void DeletCloseMark_WD(STRUCT_FILTER_TARGET* p_Filter,
                       u16* p_Dist,
                       u16* p_Pluse,
                       u16* p_PulseDistRatio)
{
    u8 l_u8i;
    u8 l_u8NextMarkPoint = 0;
    u8 l_u8size = p_Filter->m_u8In;

    // g_u16DistDiffMax 多少
    //没用到
    u16 l_u16MinDistBetweenMarks = XY_ERROR << 1;  // 150mm/2

    u16 l_u16MinAngBetweenMarks = 100;  // 3°
    u32 l_u32diff_Dist = 0, l_u32diff_Ang = 0;
    g_u16MinIncludedAng = 20;  //==200mdeg

    if (l_u8size == 1)
        return;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u8NextMarkPoint = (l_u8i + 1) % l_u8size;
        //当前靶与下一靶的最高强度点的角度差值
        l_u32diff_Ang =
            Find_IncludedAng_Min(p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang,
                                 p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Ang);
        //菱形 而不是纯用角度来卡，避免角度范围内，但俩靶标相距过于远，此时是不需要过滤
        l_u16MinAngBetweenMarks = Return_FilterMinIncludedAng_WD(
            &p_Filter->m_StructMarkInfo[l_u8i], &p_Filter->m_StructMarkInfo[l_u8NextMarkPoint]);

        // l_u16MinAngBetweenMarks 最小== 200mdeg
        if (l_u16MinAngBetweenMarks < g_u16MinIncludedAng)
            l_u16MinAngBetweenMarks = g_u16MinIncludedAng;

        while (1)
        {
            if (Points_Diff_Cmp(l_u32diff_Ang, l_u16MinAngBetweenMarks))  //两靶间最小间距
            {
                // l_u32diff_Ang<200mdeg ,且俩距离距离小于g_u16DistDiffMax，舍弃俩靶
                if (Points_Diff_Cmp(l_u32diff_Ang, g_u16MinIncludedAng)
                    && abs(p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist
                           - p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Dist)
                           <= g_u16DistDiffMax)
                {
                    p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = ISMARK_TOOCLOSE;
                    p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u8IsMark = ISMARK_TOOCLOSE;
                }
                else
                {
                    //否则舍弃远处的靶
                    if (p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist
                        > p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Dist)
                    {
                        p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark =
                            ISMARK_TOOCLOSE;  //不应该是l_u8NextMarkPoint吗
                    }
                    else
                    {
                        p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u8IsMark = ISMARK_TOOCLOSE;
                    }
                }

                // 当前靶和下个靶确定过近，遍历之后，判断当前与之后的靶标是否也过近，更新l_u16MinAngBetweenMarks
                // ，直到没找到满足的靶标，推出，for到下一个靶
                l_u8NextMarkPoint = (l_u8NextMarkPoint + 1) % l_u8size;
                if (l_u8NextMarkPoint == l_u8i)
                    break;

                l_u32diff_Ang =
                    Find_IncludedAng_Min(p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang,
                                         p_Filter->m_StructMarkInfo[l_u8NextMarkPoint].m_u16Ang);
                l_u16MinAngBetweenMarks =
                    Return_FilterMinIncludedAng_WD(&p_Filter->m_StructMarkInfo[l_u8i],
                                                   &p_Filter->m_StructMarkInfo[l_u8NextMarkPoint]);
                if (l_u16MinAngBetweenMarks < g_u16MinIncludedAng)
                    l_u16MinAngBetweenMarks = g_u16MinIncludedAng;
            }
            else
                break;
        }
    }
}

/**
 *
 */
u8 FilterMark_ByMarkMaxMinDist_WD(STRUCT_FILTER_TARGET* p_Filter,
                                  u16* p_Dist,
                                  u16* p_Pluse,
                                  u16* p_PulseDistRatio)
{
    u16 l_u16distdiff = 0;
    u16 l_u16DistMax, l_u16DistMin;

    for (u8 l_u8i = 0; l_u8i < p_Filter->m_u8In; l_u8i++)
    {
        // l_u16distdiff：当前靶标段的最高强度点的距离与最小距离的差
        l_u16distdiff = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist
                        - g_au16min_dist[l_u8i];  // g_au16max_dist[l_u8i] - g_au16min_dist[l_u8i] ;

        if ((p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark == ISMARK)
            && (l_u16distdiff > g_u16DistDiffMax))
        {
            // Find_MarkCenterByPulseAndDist(p_Dist,p_Pluse,p_ScanMark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]);
            //重置该靶标段的起点终点
            //输入：该靶标段的最高强度点index，脉宽首地址，靶标段信息，距离首地址
            Renew_MarkCenter_PulseDist(g_au16MarkMaxPluseDistRatio[l_u8i],
                                       p_PulseDistRatio,
                                       &p_Filter->m_StructMarkInfo[l_u8i],
                                       p_Dist);

            //获取该靶标段的最大最小距离值，
            Find_MaxMin_Dist_Sigle(
                p_Dist, &p_Filter->m_StructMarkInfo[l_u8i], &l_u16DistMax, &l_u16DistMin);

            l_u16distdiff = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist - l_u16DistMin;
            if (l_u16distdiff > g_u16DistDiffMax)
                p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark = NOTMARK;
        }
    }
    return 0;
}

u16 Return_FilterMinIncludedAng_WD(STRUCT_MARK_INFO* p_Mark1, STRUCT_MARK_INFO* p_Mark2)
{
    u16 l_u16MarkDistDiff = abs(p_Mark1->m_u16Dist - p_Mark2->m_u16Dist);
    float l_f32Mul = 0;
    float l_f32DistMul;

    //当前靶比下一个靶 近
    if (p_Mark1->m_u16Dist < p_Mark2->m_u16Dist)
    {
        if (p_Mark1->m_u16Dist > 3000)
            l_f32Mul = p_Mark1->m_u16Dist / 2000.0;  // 1.5-～
        else                                         // if(p_Mark1->m_u16Dist > 1060)
            l_f32Mul =
                p_Mark1->m_u16Dist / 4000.0 + 0.75;  // 0.75 - 1.5
                                                     //		else if(p_Mark1->m_u16Dist < 940)
                                                     //			l_f32Mul = 0.5;
                                                     //		else
                                                     //			l_f32Mul = 1;

        //		l_f32DistMul = p_Mark1->m_u16Dist/6000.0;
        //		l_f32Mul = 1.1667 - l_f32DistMul ;
    }
    else
    {
        if (p_Mark2->m_u16Dist > 3000)
            l_f32Mul = p_Mark2->m_u16Dist / 2000.0;
        else  // if(p_Mark2->m_u16Dist > 1060)
            l_f32Mul = p_Mark2->m_u16Dist / 4000.0 + 0.75;
        //		else if(p_Mark2->m_u16Dist < 940)
        //			l_f32Mul = 0.5;
        //		else
        //			l_f32Mul = 1;
        //		if(p_Mark2->m_u16Dist>1060)
        //			l_f32Mul = 1000.0 / p_Mark2->m_u16Dist;
        //		else if(p_Mark1->m_u16Dist < 940)
        //			l_f32Mul = 2;
        //		else
        //			l_f32Mul = 0.5;
        //		l_f32DistMul = p_Mark2->m_u16Dist/6000.0;
        //		l_f32Mul = 1.1667 - l_f32DistMul;
    }

    // why

    //当前靶与下一靶的最高强度点的距离差
    if (l_u16MarkDistDiff < 1300)
        return ((l_u16MarkDistDiff * 2 / 15) + 46) / l_f32Mul;
    else if (l_u16MarkDistDiff < 3000)
        return (256 - l_u16MarkDistDiff / 30) / l_f32Mul;
    else
        return (156) / l_f32Mul;
    //	if(l_u16MarkDistDiff < 1300)
    //		return ((l_u16MarkDistDiff*4/15) + 95)*l_f32Mul;
    //	else if(l_u16MarkDistDiff < 3000)
    //		return (515-l_u16MarkDistDiff/15)*l_f32Mul;
    //	else
    //		return (315)*l_f32Mul;

    //	if(l_u16MarkDistDiff < 2500)
    //		return ((l_u16MarkDistDiff>>2) + 750)*l_f32Mul;
    //	else
    //		return (1220 - l_u16MarkDistDiff/20)*l_f32Mul;
}

void Find_MaxMin_Dist_Sigle(u16* Dist, STRUCT_MARK_INFO* p_MarkInfo, u16* p_DistMax, u16* p_DistMin)
{
    u16 l_u16DistOld_max = 0, l_u16DistOld_min = 0, l_u16j = 0;
    u16 l_u16Dist = 0;
    u16 l_u16tmp = 0;

    l_u16DistOld_max = Dist[p_MarkInfo->m_u16ScanSta];
    l_u16DistOld_min = l_u16DistOld_max;

    for (l_u16j = p_MarkInfo->m_u16ScanSta + 1; l_u16j <= p_MarkInfo->m_u16ScanEnd; l_u16j++)
    {
        l_u16Dist = Dist[l_u16j % g_u16ScanPointNum];
        if (l_u16Dist > l_u16DistOld_max)  //距离
        {
            l_u16DistOld_max = l_u16Dist;
        }
        if ((l_u16Dist < l_u16DistOld_min) && (l_u16Dist != 0))
        {
            l_u16DistOld_min = l_u16Dist;
        }
    }
    *p_DistMax = l_u16DistOld_max;
    *p_DistMin = l_u16DistOld_min;
}
u8 Find_MarkCenterByPulseAndDist(u16* p_Dist, u16* p_Pulse, STRUCT_MARK_INFO* p_MarkInfo)
{
    u16 l_u16i = 0;
    u16 l_u16cnt = 0;
    u16 l_u16MaxRatio = 0;
    u16 l_u16tmpRatio = 0;
    u16 l_u16StaTmp, l_u16EndTmp;
    u16 l_u16MarkSta = p_MarkInfo->m_u16ScanSta;
    u16 l_u16MarkEnd = p_MarkInfo->m_u16ScanEnd;
    u32 l_u32PXCorr_Offset;
    s8 l_s8PXCorr;
    if (l_u16MarkEnd < l_u16MarkSta)
        l_u16MarkEnd = l_u16MarkEnd + g_u16ScanPointNum;
    for (l_u16i = l_u16MarkSta; l_u16i < l_u16MarkEnd; l_u16i++)
    {
        l_u16tmpRatio =
            ((u32)(p_Pulse[l_u16i] + ((p_Dist[l_u16i] >> 1) + 1)) << 5) / p_Dist[l_u16i];
        if (l_u16tmpRatio > l_u16MaxRatio)
        {
            l_u16MaxRatio = l_u16tmpRatio;
            l_u16StaTmp = l_u16i;
            l_u16cnt = 1;
        }
        else if (l_u16tmpRatio == l_u16MaxRatio)
        {
            l_u16cnt++;
        }
        else
        {
            if (l_u16cnt <= 2)
                continue;
            else
            {
                l_u16EndTmp = l_u16i - 1;
                p_MarkInfo->m_u16Ang = ((l_u16EndTmp + l_u16StaTmp) >> 1) % g_u16ScanPointNum;
                p_MarkInfo->m_u16Dist = p_Dist[p_MarkInfo->m_u16Ang] + 40;
                p_MarkInfo->m_u16ScanSta = l_u16StaTmp;
                p_MarkInfo->m_u16ScanEnd = l_u16EndTmp % g_u16ScanPointNum;
                l_u32PXCorr_Offset = p_MarkInfo->m_u16Ang << 1;  //+ g_sFactorCorr.m_s16BmqOffset;

                p_MarkInfo->m_u16Ang *= g_u8Ang_Resolution;

                //			//Flash_Read_HalfWord(ADDR_FLASH_PIANXIN_DATA + l_u32PXCorr_Offset, 1 ,
                //(u16
                //*)&l_s8PXCorr);
                //
                //			p_MarkInfo->m_u16Ang = (DEG_PERSCAN + p_MarkInfo->m_u16Ang + l_s8PXCorr)
                //% DEG_PERSCAN;

                break;
            }
        }
    }
    if (l_u16cnt <= 2)
        p_MarkInfo->m_u8IsMark = NOTMARK;
    return 0;
}
