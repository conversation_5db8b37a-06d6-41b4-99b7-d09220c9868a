#include "algorithm/location/initial_location/poseFunc.h"

// long long     8个字节，64位，取值范围 -2^63 ～ 2^63-1
// unsigned long 4个字节，32位，取值范围 -2^32 ～ 2^32-1
uint32_t rosTime2HourUSec(ros::Time p_time)  // long long 8个字节，64位，取值范围 -2^63 ～ 2^63-1
{
    // int allSec  = p_time.sec + 8 * 3600;    // 总秒数 中国时区 +8小时
    // int daySec  = allSec % 86400;           //今日过去秒数    //秒 毫秒 微妙 纳秒
    // int hourSec = daySec % 3600;            //当前小时秒数
    // int uSec = p_time.nsec / 1000;          //纳秒转微妙

    int hourSec = p_time.sec % 3600;  //当前小时秒数
    // cout<<"2Sec: " <<hourSec<<endl;
    int uSec = p_time.nsec / 1000;  //纳秒转微妙
    // cout<<"2USec: " <<uSec<<endl;
    return hourSec * 1000000 + uSec;  //今时的微秒
}

uint32_t getCurrHourUSec()
{
    // 获取当前时间
    system_clock::time_point now = system_clock::now();
    // 距离1970-01-01 00:00:00的纳秒数
    chrono::nanoseconds d = now.time_since_epoch();

    int hourSec = d.count() / 1000000000 % 3600;  //当前小时秒数
    // cout<<"1Sec: " <<hourSec<<endl;
    int uSec = d.count() / 1000 % 1000000;  //微妙  纳秒转微秒 取余不足毫秒就是
    // cout<<"1USec: " <<uSec<<endl;
    return hourSec * 1000000 + uSec;  //今时的微秒
}

double secRound(double timeOffset)
{
    return round(timeOffset * 10) / 10;
}

long long gedCurrNSec()
{
    // 获取当前时间
    system_clock::time_point now = system_clock::now();
    // 距离1970-01-01 00:00:00的纳秒数
    chrono::nanoseconds d = now.time_since_epoch();
    //微秒
    // chrono::microseconds d = now.time_since_epoch();  //不对
    //毫秒
    // chrono::milliseconds d = now.time_since_epoch(); //不对

    // cout << "current nanoseconds:" << d.count() << endl;
    return d.count();
}

void getDate(long long time)
{
    int seconds = time / 1000000000 + 8 * 3600;  // time zone +8
    int days = seconds / 86400;
    int year = 1970 + (int)(days / 1461) * 4;  // recycled in 4 years = 1461 days
    int month = 0;
    int day = days % 1461;
    day = day > 730 ? day - 1 : day;
    year += (int)(day / 365);
    day = day % 365;
    int monthday[]{0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334, 365};
    for (int i = 0; i < 13; i++)
    {
        if (day < monthday[i + 1])
        {
            month = i + 1;
            day = day - monthday[i] + 1;
            break;
        }
    }
    int sec = seconds % 86400;
    int hour = (int)(sec / 3600);
    int minute = (int)(sec % 3600 / 60);
    int second = sec % 60;
    int nanoseconds = time % 1000000000;
    int millisecond = nanoseconds / 1000;

    cout << year << "/" << month << "/" << day << " " << hour << ":" << minute << ":" << second
         << "." << millisecond << endl;
}

/***********************************/
/**
 * l_localPose：输入（上一帧位姿/时间戳/增量），l_strutPoseout：输出 结构体未来预估位姿
 */
void predictPose(LOCALPOSE l_localPose,
                 uint32_t l_nowTime,
                 Eigen::Vector3d& l_prob_t,
                 Eigen::Quaterniond& l_prob_q)
{
    // std::cout<<"l_nowTime: "<<l_nowTime<<" | "<<"poseTamp: "<<l_localPose.timeTamp<<endl;

    //获取当前clock时刻us
    // double deltaT = getTimeOffset(getCurrHourUSec(),l_localPose.timeTamp) / 1000.0 /
    // 100.0;//us转ms 除雷达频率100ms 获取当前rosTimeNow时刻us
    double deltaT =
        getTimeOffset(l_nowTime, l_localPose.timeTamp) / 1000.0 / 100.0;  // us转ms 除雷达频率100ms

    // printf("predict deltaT: %f",deltaT);

    Eigen::Vector3d increCorrect_t = Eigen::Vector3d::Zero();
    Eigen::Quaterniond increCorrect_q = Eigen::Quaterniond::Identity();

    // printf("net raw incre x: %.4f   | y: %.4f   | a:
    // %.4f\n",l_localPose.incre_t[0],l_localPose.incre_t[1],toYaw(l_localPose.incre_q));
    // printf("raw local   : x: %.4f   | y: %.4f   | a:
    // %.4f\n",l_localPose.local_t[0],l_localPose.local_t[1],toYaw(l_localPose.local_q));
    correctPoseIncre(
        deltaT, l_localPose.incre_t, l_localPose.incre_q, increCorrect_t, increCorrect_q);
    // printf("net now incre x: %.4f   | y: %.4f   | a:
    // %.4f\n",increCorrect_t[0],increCorrect_t[1],toYaw(increCorrect_q));
    updateProbPose(l_localPose.local_t,
                   l_localPose.local_q,
                   increCorrect_t,
                   increCorrect_q,
                   l_prob_t,
                   l_prob_q);

    // printf("now local   : x: %.4f   | y: %.4f   | a:
    // %.4f\n",l_prob_t[0],l_prob_t[1],toYaw(l_prob_q));
}

/**
 * deltaT: 100ms的倍数
 */
void correctPoseIncre(double deltaT,
                      Eigen::Vector3d t_in,
                      Eigen::Quaterniond q_in,
                      Eigen::Vector3d& t_out,
                      Eigen::Quaterniond& q_out)
{
    if (deltaT < 0.0)
    {
        deltaT = 1.0;
        printf("time deltaT < 0.0\n");
    }
    // if(deltaT < 0.0)
    //     return;

    t_out = deltaT * t_in;

    Eigen::Vector3d l_rpy = Eigen::Vector3d::Zero();
    // s
    l_rpy[2] = toYaw(q_in) * deltaT;

    q_out = RPY2Quat(l_rpy).normalized();
}

/**
 * 更新预测位姿
 */
void updateProbPose(Eigen::Vector3d t_old,
                    Eigen::Quaterniond q_old,
                    Eigen::Vector3d t_incre,
                    Eigen::Quaterniond q_incre,
                    Eigen::Vector3d& t_new,
                    Eigen::Quaterniond& q_new)
{
    t_new = t_old + q_old * t_incre;
    q_new = q_old * q_incre;

    // printf("net raw     x: %.4f   | y: %.4f   | a: %.4f\n",t_old[0],t_old[1],toYaw(q_old));
    // printf("net incre   x: %.4f   | y: %.4f   | a: %.4f\n",t_incre[0],t_incre[1],toYaw(q_incre));
    // printf("net prob    x: %.4f   | y: %.4f   | a: %.4f\n",t_new[0],t_new[1],toYaw(q_new));
}

/**
 * 位姿 eigen 转 ROBOTPOS
 * ROBOTPOS.t.x() y()为位置，单位m，z()为方向0-360deg
 */
void transformPoseStruct(int l_mode,
                         int l_flag,
                         uint32_t l_timeStamp,
                         Eigen::Vector3d& l_t,
                         Eigen::Quaterniond& l_q,
                         ROBOTPOS& l_out)
{
    l_t.z() = rad2deg(toYawIn2PI(l_q));

    // printf("transformPoseStruct: %f\n",l_t.z());
    l_out.t = l_t;
    l_out.timeTamp = l_timeStamp;
    l_out.flag = l_flag;
    l_out.mode = l_mode;  //随意指定，无意义
    l_out.meandev = 1;

    // printf("pose: x: %.4f     | y: %.4f     |  a:%.4f \n",l_t[0],l_t[1],l_t[2]);
    // printf("pose: flag: %d    | mode: %d    |  mean:%d \n",l_flag,l_mode,l_out.meandev);
    // cout<<"tamp: "<<l_timeStamp<<endl;
}

/**
 * 针对double型time，对0.1进行四舍五入， 0.099 / 0.11 约等 0.1
 */
double secTimeRound(double timeOffset)
{
    return 0.1;
    double l_tmp = round(timeOffset * 10) / 10;

    if (l_tmp != 0.1)
        printf("secTimeRound: %.4f \n", l_tmp);

    return round(timeOffset * 10) / 10;
}

/**
 * 保证位姿增量为连续两帧之间，即100ms内
 * deltaT：100ms的倍数
 */
void correctIncLidarPose(double deltaT, Eigen::Vector3d& t_out, Eigen::Quaterniond& q_out)
{
    t_out = deltaT * t_out;

    Eigen::Vector3d tmp = Eigen::Vector3d::Zero();
    toEulerAngle(q_out, tmp[0], tmp[1], tmp[2]);
    tmp[0] *= deltaT;
    tmp[1] *= deltaT;
    tmp[2] *= deltaT;

    q_out = RPY2Quat(tmp).normalized();
}

/**
 * 两次全局位姿,求局部增量
 */
void computeIncLidarPose(Eigen::Vector3d l_now_t,
                         Eigen::Quaterniond l_now_q,
                         Eigen::Vector3d l_last_t,
                         Eigen::Quaterniond l_last_q,
                         double l_time_now,
                         double l_time_last,
                         Eigen::Vector3d& l_incre_t,
                         Eigen::Quaterniond& l_incre_q)
{
    // lidar坐标系位姿增量
    l_now_q.normalize();

    Eigen::Vector3d p2 = Eigen::Vector3d(0, 0, 0);
    l_incre_t = l_last_q.inverse().normalized() * (l_now_q * p2 + l_now_t - l_last_t);
    l_incre_q = l_last_q.inverse() * l_now_q;
    l_incre_q.normalize();

    // //考虑丢帧，计算time修到100ms增量
    double deltaTime = 1.0 / (secTimeRound(l_time_now - l_time_last) / 0.1);

    // debug
    if (deltaTime != 1.0)
        printf("computeIncLidarPose time now : %.4f | time old : %.4f\n", l_time_now, l_time_last);

    correctIncLidarPose(deltaTime, l_incre_t, l_incre_q);
}

/**
 * 限制两帧之间的位姿变化
 * */
void limitIncLidarPose(Eigen::Vector3d& l_t, Eigen::Quaterniond& l_q)
{
    // z轴5cm以内变化，认为误差
    // if (fabs(t_lidar_curr_sum.z()) < 0.05)
    l_t.z() = 0.0;

    // xy轴5mm以内变化，认为误差
    if (fabs(l_t.y()) <= 0.005)
        l_t.y() = 0.0;

    if (fabs(l_t.x()) <= 0.005)
        l_t.x() = 0.0;

    Eigen::Vector3d rpyAngle = Eigen::Vector3d::Zero();
    toEulerAngle(l_q, rpyAngle[0], rpyAngle[1], rpyAngle[2]);
    //俯仰和滚转角度+-5度内认为是匹配误差
    // if (fabs(rpyAngle[0]) < 0.09)
    rpyAngle[0] = 0.0;
    // if (fabs(rpyAngle[1]) < 0.09)
    rpyAngle[1] = 0.0;

    //航向角度，以最小0.01rad/s，100ms转动为0.057度/0.0009rad,近似0.001rad
    if (fabs(rpyAngle[2]) <= 0.01)
        rpyAngle[2] = 0.0;

    //计算修正后的结果
    l_q = RPY2Quat(rpyAngle).normalized();
}

double tPoseEigenDiff(Eigen::Vector3d p1, Eigen::Vector3d p2)
{
    return sqrt(pow(p1.x() - p2.x(), 2) + pow(p1.y() - p2.y(), 2));  //+pow(p1.z() - p2.z(), 2)
}

double tSumEigenToDist(Eigen::Vector3d p1)
{
    return sqrt(pow(p1.x(), 2) + pow(p1.y(), 2));  //+ pow(p1.z(),2)
}
