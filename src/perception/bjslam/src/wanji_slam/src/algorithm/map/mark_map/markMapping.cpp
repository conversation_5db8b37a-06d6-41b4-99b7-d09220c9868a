#include "markMapping.h"

#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/segmentation/extract_clusters.h>

namespace wj_slam {
markMapping::markMapping() : c_bDebug_(false)
{
    allocateMemery_();
    setParamDefalut_();
}
markMapping::markMapping(bool debug) : c_bDebug_(debug)
{
    allocateMemery_();
    setParamDefalut_();
}

void markMapping::allocateMemery_()
{
    try
    {
        c_inputMark_.reset(new markCloud());
        c_abnormMarkCloud_.reset(new markCloud());  // 废弃靶标点云
        c_tempMarkMap_.reset(new markCloud());      // 临时靶标地图
        c_permtMarkMap_.reset(new markCloud());     // 永久靶标地图
    }
    catch (const std::bad_alloc& e)
    {
        fprintf(stderr, "%s\n", e.what());
    }
}

void markMapping::setParamDefalut_()
{
    c_stCfg_.m_nmaxLatestSecDiff = 5;
    c_stCfg_.m_nmaxAvgSecDiff = 10;
    c_stCfg_.m_nminNumToMaybe = 5;  // m_nminNumToMaybe?>m_nminNumToMap
    c_stCfg_.m_nminNumToMap = 5;
}

void markMapping::setInputMarkList(MARK_LIST& p_newMarkList,
                                   Eigen::Quaterniond p_quat,
                                   Eigen::Vector3d p_trans,
                                   double p_fTimestamp)
{
    c_inputPose_.m_quat = p_quat;
    c_inputPose_.m_trans = p_trans;
    c_inputPose_.timeStamp = static_cast<float>(p_fTimestamp);
    c_inputMark_->clear();
    transformListToCloud_(*c_inputMark_, p_newMarkList);
    // 添加时间到v
    for (int i = 0; i < c_inputMark_->size(); i++)
    {
        c_inputMark_->points[i].v = c_inputPose_.timeStamp;
    }
}

bool markMapping::updateMarkMap()
{
    if (c_inputMark_->empty())
        return false;
    return renewMarkMap_(c_inputMark_, c_inputPose_);
}

void markMapping::getTempMarkMapList(MARK_LIST& p_list)
{
    // 临时靶标地图添加永久地图
    *c_tempMarkMap_ += *c_permtMarkMap_;
    transformCloudToList_(*c_tempMarkMap_, p_list);
}

void markMapping::getPermtMarkMapList(MARK_LIST& p_list)
{
    transformCloudToList_(*c_permtMarkMap_, p_list);
}

bool markMapping::renewMarkMap_(markCloudPtr p_inputMark, st_Pose& p_pose)
{
    // 临时靶标点云团，每次加入新靶标扫描点，而后靶标随着每次更新而逐渐转移到其他容器
    static markCloudPtr c_tempMarkCloud_(new markCloud());

    if (inputProcess_(*p_inputMark, p_pose) == true)
    {
        if (dematchMarkMap_(p_inputMark, c_permtMarkMap_))
        {
            // 更新临时点云
            *c_tempMarkCloud_ += *p_inputMark;
            // 旧临时靶标地图删除，临时靶标地图将重新绘制
            c_tempMarkMap_->clear();
            if (analysisTempMarkCloud_(c_tempMarkCloud_, p_pose.timeStamp))
            {
                if (c_bDebug_)
                    fprintf(stderr, "analysis mark done.\n");
                return true;
            }
            else
            {
                if (c_bDebug_)
                    fprintf(stderr, "analysis mark fail.\n");
            }
        }
        else
        {
            if (c_bDebug_)
                fprintf(stderr, "dematch mark fail.\n");
        }
    }
    else
    {
        if (c_bDebug_)
            fprintf(stderr, "Input mark fail.\n");
    }
    return false;
}

bool markMapping::inputProcess_(markCloud& inputMark, st_Pose& p_pose)
{
    // 首先检查时间同步，然后添加时间戳，最后转换到map坐标系
    Eigen::Vector3d l_pointc, l_pointw;
    size_t size = inputMark.points.size();
    for (size_t ind = 0; ind < size; ind++)
    {
        // 转换到map坐标系
        l_pointc.x() = inputMark.points[ind].x;
        l_pointc.y() = inputMark.points[ind].y;
        l_pointc.z() = inputMark.points[ind].z;
        l_pointw = p_pose.m_quat * l_pointc + p_pose.m_trans;
        inputMark.points[ind].x = l_pointw.x();
        inputMark.points[ind].y = l_pointw.y();
        inputMark.points[ind].z = l_pointw.z();
    }
    return true;
}

bool markMapping::dematchMarkMap_(markCloudPtr p_inputMark, markCloudPtr p_permtMarkMap)
{
    static pcl::KdTreeFLANN<mark>::Ptr l_kdSavedMap(new pcl::KdTreeFLANN<mark>());

    // 异常情况，不进行下一步
    if (p_inputMark->points.size() == 0)
    {
        fprintf(stderr, "new markcloud has none point.\n");
        return false;
    }
    // 正常情况，不需要反匹配消除
    if (p_permtMarkMap->points.size() == 0)
    {
        return true;
    }

    // 开始反匹配，设置反匹配对象
    l_kdSavedMap->setInputCloud(p_permtMarkMap);
    std::vector<int> pointIdxRadiusSearch;
    std::vector<float> pointRadiusSquaredDistance;
    for (auto l_point = p_inputMark->begin(); l_point != p_inputMark->end();)
    {
        if (l_kdSavedMap->radiusSearch(
                *l_point, 0.3, pointIdxRadiusSearch, pointRadiusSquaredDistance)
            > 0)
            l_point = p_inputMark->erase(l_point);
        else
            l_point++;
    }

    return true;
}

bool markMapping::analysisTempMarkCloud_(markCloudPtr p_tempMarkCloud, float p_fTimestamp)
{
    // 欧式聚类对象
    static pcl::search::KdTree<mark>::Ptr l_kdTempCloud(new pcl::search::KdTree<mark>());
    static pcl::EuclideanClusterExtraction<mark> l_eulerEc;
    static bool once = true;

    if (once)
    {
        l_eulerEc.setClusterTolerance(0.05);  //设置近邻搜索的搜索半径为5cm
        l_eulerEc.setMinClusterSize(1);  //设置一个聚类需要的最少点数目为MinScanTime
        l_eulerEc.setMaxClusterSize(25000);  //设置一个聚类需要的最大点数目为25000
        once = false;
    }

    // 临时分割靶标点云团
    std::vector<pcl::PointIndices> l_vAllCluster;
    // 保留序号
    std::vector<pcl::PointIndices> l_vRemainCluster;
    // 临时云团
    markCloudPtr l_OneClusterCloud(new markCloud());

    // 异常情况，不进行下一步
    if (p_tempMarkCloud->points.size() == 0)
    {
        fprintf(stderr, "temp markcloud has none point.\n");
        return false;
    }
    // 聚类并分析点集中的靶标云
    l_kdTempCloud->setInputCloud(p_tempMarkCloud);

    l_eulerEc.setSearchMethod(l_kdTempCloud);  //设置点云的搜索机制
    l_eulerEc.setInputCloud(p_tempMarkCloud);  //设置原始点云
    l_eulerEc.extract(l_vAllCluster);          //从点云中提取聚类

    //保存分割后的所有类 每一类为一个PointIndices
    for (auto getIndices : l_vAllCluster)
    {
        l_OneClusterCloud->clear();
        for (int index : getIndices.indices)
        {
            l_OneClusterCloud->push_back(p_tempMarkCloud->points[index]);
        }
        if (!shiftMarkCluster_(*l_OneClusterCloud,
                               p_fTimestamp,
                               c_abnormMarkCloud_,
                               c_tempMarkMap_,
                               c_permtMarkMap_))
        {
            // 没有转移出去的点云会保留在tempCloud
            for (int index : getIndices.indices)
            {
                l_vRemainCluster.push_back(getIndices);
            }
        }
    }

    // 更新临时点云
    pcl::copyPointCloud(*p_tempMarkCloud, l_vRemainCluster, *p_tempMarkCloud);

    return true;
}

bool markMapping::shiftMarkCluster_(markCloud& p_markCluster,
                                    float p_ftimeCurr,
                                    markCloudPtr p_abnormMark,
                                    markCloudPtr p_tempMarkMap,
                                    markCloudPtr p_permtMarkMap)
{
    //如果靶标点云团的最新时间远离当前时间
    if (p_ftimeCurr - getLatestMarkTime_(p_markCluster) > c_stCfg_.m_nmaxLatestSecDiff)
    {
        //将点云放入废弃靶标点云中
        if (c_bDebug_)
            for (mark pointAb : p_markCluster)
                p_abnormMark->points.push_back(pointAb);
        return true;
    }
    //如果靶标点云团的平均时间远离当前时间
    else if (p_ftimeCurr - getAverageMarkTime_(p_markCluster) > c_stCfg_.m_nmaxAvgSecDiff)
    {
        //将点云放入废弃靶标点云中
        if (c_bDebug_)
            for (mark pointAb : p_markCluster)
                p_abnormMark->points.push_back(pointAb);
        return true;
    }
    //如果靶标点云团点数足够,认为是靶标并加入[靶标地图]
    else if (p_markCluster.points.size() > c_stCfg_.m_nminNumToMap)
    {
        p_permtMarkMap->points.push_back(getCenteroid_(p_markCluster));
        return true;
    }
    //如果靶标点云团点数足够,认为是可能靶标并加入[临时靶标]
    else if (p_markCluster.points.size() > c_stCfg_.m_nminNumToMaybe)
    {
        p_tempMarkMap->points.push_back(getCenteroid_(p_markCluster));
        return false;
    }
    //不需要操作这个云团
    else
        return false;
}

float markMapping::getLatestMarkTime_(markCloud& p_markCloud)  //不要循环
{
    float lastestTime = 0.0;
    for (mark point : p_markCloud)
    {
        if (point.v > lastestTime)
            lastestTime = point.v;
    }
    return lastestTime;
}

float markMapping::getAverageMarkTime_(markCloud& p_markCloud)
{
    float avgTime = 0.0;
    for (mark point : p_markCloud)
    {
        avgTime += point.v;
    }
    return avgTime / float(p_markCloud.points.size());
}

markMapping::mark markMapping::getCenteroid_(markCloud& p_markCloud)
{
    mark centroid;
    size_t size = p_markCloud.size();
    for (size_t i = 0; i < size; ++i)
    {
        centroid.x += p_markCloud[i].x;
        centroid.y += p_markCloud[i].y;
        centroid.h += p_markCloud[i].h;
        centroid.s += p_markCloud[i].s;
        centroid.v += p_markCloud[i].v;
    }
    centroid.x /= float(size);
    centroid.y /= float(size);
    centroid.z = 0;
    centroid.h /= float(size);
    centroid.s /= float(size);
    centroid.v /= float(size);
    return centroid;
}

void markMapping::transformListToCloud_(markCloud& p_cloud, MARK_LIST& p_list)
{
    p_cloud.resize(p_list.size());
    for (int i = 0; i < p_cloud.size(); i++)
    {
        p_cloud.points[i].x = static_cast<float>(p_list[i].m_s32x / 1000.0f);
        p_cloud.points[i].y = static_cast<float>(p_list[i].m_s32y / 1000.0f);
        p_cloud.points[i].z = 0.0f;
        p_cloud.points[i].h = static_cast<float>(p_list[i].m_u8size);
        p_cloud.points[i].s = static_cast<float>(p_list[i].m_u8shape);
        // p_cloud.points[i].v = static_cast<float>(i);
    }
}

void markMapping::transformCloudToList_(markCloud& p_cloud, MARK_LIST& p_list)
{
    p_list.resize(p_cloud.size());
    for (int i = 0; i < p_list.size(); i++)
    {
        p_list[i].m_s32x = static_cast<int>(p_cloud.points[i].x * 1000.0f);
        p_list[i].m_s32y = static_cast<int>(p_cloud.points[i].y * 1000.0f);
        p_list[i].m_u8size = static_cast<u8>(p_cloud.points[i].h);
        p_list[i].m_u8shape = static_cast<u8>(p_cloud.points[i].s);
        p_list[i].m_u32no = i;
    }
}

}  // namespace wj_slam
