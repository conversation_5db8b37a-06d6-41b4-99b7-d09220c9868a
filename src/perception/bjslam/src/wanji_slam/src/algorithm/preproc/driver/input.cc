// local
#include "algorithm/preproc/driver/input.h"
#include "tic_toc.h"
// sys
#include <arpa/inet.h>
#include <linux/net_tstamp.h>
#include <linux/sockios.h>
#include <netinet/if_ether.h>
#include <sys/ioctl.h>

using namespace std;
namespace wanji_driver {

#pragma region 基类
Input::Input(uint32_t p_uiLidarId, ScanDataCb p_scandataCb)
    : c_stSysParam_(wj_slam::SYSPARAM::getIn()), c_stLaserCfg_(c_stSysParam_->m_lidar[p_uiLidarId]),
      c_outScanDataCb_(p_scandataCb), c_bRun_(false), c_bScanStartFlag_(false),
      c_bInitSuecss_(false), c_uiLidarId_(p_uiLidarId), c_iLevevAgPkgStatus_(0x0001),
      c_uiExpPktIdx_(0), c_iPcapHeaderLen_(0), c_iCurBufferFlag_(1), c_iFirstCycleNum_(-1), c_iFirstPktIdx_(-1),
      c_uiFirstCirclePkgNum_(0), c_recvThr_(nullptr), c_parseThr_(nullptr), c_sDevMac_("")
{
    initScanBuffer_(c_stLaserCfg_.m_uiFramePkgNum);
    if (c_stSysParam_->m_bIsOnlineMode)
        c_iPcapHeaderLen_ = 0;
    else
        c_iPcapHeaderLen_ = sizeof(offlinePcap_pkthdr);
    // 本机地址 只用了端口
    memset(&c_myAddr_, 0, sizeof(c_myAddr_));
    c_myAddr_.sin_family = AF_INET;
    c_myAddr_.sin_port = htons(c_stLaserCfg_.m_dev.m_uiLocalPort);
    if (c_stLaserCfg_.m_bUseMulticast)
    {
        c_myAddr_.sin_addr.s_addr = inet_addr(c_stLaserCfg_.m_dev.m_sMulticastIP.c_str());
        c_sockmreq_.imr_interface.s_addr = inet_addr(c_stLaserCfg_.m_dev.m_sLocalIP.c_str());
    }
    else
    {
        c_myAddr_.sin_addr.s_addr = INADDR_ANY;
        c_sockmreq_.imr_interface.s_addr = INADDR_ANY;
    }

    // 设备地址
    memset(&c_remoteAddr_, 0, sizeof(c_remoteAddr_));
    c_remoteAddr_.sin_family = AF_INET;
    c_remoteAddr_.sin_port = htons(c_stLaserCfg_.m_dev.m_uiDevPort);
    c_remoteAddr_.sin_addr.s_addr = inet_addr(c_stLaserCfg_.m_dev.m_sDevIP.c_str());

    // 套接字
    c_sockinf_.fd = -1;
    c_sockinf_.port = c_stLaserCfg_.m_dev.m_uiLocalPort;
    c_sockinf_.local = c_myAddr_;
    c_sockinf_.prev_serialnum = -1;

    // 组播参数
    c_sockmreq_.imr_multiaddr.s_addr = inet_addr(c_stLaserCfg_.m_dev.m_sMulticastIP.c_str());
    if (inputInit_())
    {
        c_bInitSuecss_ = !c_stSysParam_->m_bIsOnlineMode;
        c_bRun_ = true;
        // 创建接收线程
        c_recvThr_ = new std::thread(&Input::recvSocketData_, this);
        // 创建解析线程
        c_parseThr_ = new std::thread(&Input::parseSocketData_, this);
    }
}

Input::~Input()
{
    if (c_sockinf_.fd != -1)
    {
        shutdown(c_sockinf_.fd, SHUT_RDWR);
        close(c_sockinf_.fd);
        c_sockinf_.fd = -1;
    }

    c_bRun_ = false;
    if (c_recvThr_)
    {
        if (c_recvThr_->joinable())
            c_recvThr_->join();
        delete c_recvThr_;
    }
    if (c_parseThr_)
    {
        if (c_parseThr_->joinable())
            c_parseThr_->join();
        delete c_parseThr_;
    }
}

bool Input::inputInit_()
{
    return onlineInit_(c_sockinf_, c_myAddr_);
}

bool Input::onlineInit_(socket_info& inf, sockaddr_in& p_sMyAddr)
{
    // 建立套接字
    inf.fd = socket(PF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (inf.fd == -1)
    {
        inf.err_no = errno;
        LOGDR(WERROR,
              "{} setup_udp_server: socket failed: {}",
              WJLog::getWholeSysTime(),
              strerror(inf.err_no));
        return false;
    }

    // 是否加入组播
    if (c_stLaserCfg_.m_bUseMulticast
        && (setsockopt(inf.fd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &c_sockmreq_, sizeof(c_sockmreq_))
            < 0))
    {
        inf.err_no = errno;
        LOGDR(WERROR,
              "{} setsockopt multicast error=: {}",
              WJLog::getWholeSysTime(),
              strerror(inf.err_no));
    }

    // 设置UDP SOCKET接收缓冲区大小
    int rcvBufSize = 1024 * 1024 * 2;
    socklen_t optlen = sizeof(rcvBufSize);
    if (setsockopt(inf.fd, SOL_SOCKET, SO_RCVBUF, &rcvBufSize, optlen) < 0)
    {
        LOGDR(WERROR, "{} setsockopt error=: {}", WJLog::getWholeSysTime(), strerror(inf.err_no));
    }

    // 设置网络时间戳
    int l_iOpt = SOF_TIMESTAMPING_RX_SOFTWARE | SOF_TIMESTAMPING_SOFTWARE | 0;
    if (setsockopt(inf.fd, SOL_SOCKET, SO_TIMESTAMPING, &l_iOpt, sizeof(l_iOpt)))
    {
        inf.err_no = errno;
        LOGDR(WERROR,
              "{} setup_udp_server: setsockopt failed: {}",
              WJLog::getWholeSysTime(),
              strerror(inf.err_no));
        return false;
    }

    // 设置端口复用
    int l_iOpt2 = 1;
    if (setsockopt(inf.fd, SOL_SOCKET, SO_REUSEPORT, &l_iOpt2, sizeof(l_iOpt2)))
    {
        inf.err_no = errno;
        LOGDR(WERROR,
              "{} setup_udp_server: setsockopt2 failed: {}",
              WJLog::getWholeSysTime(),
              strerror(inf.err_no));
        return false;
    }

    // 绑定端口
    if (bind(inf.fd, (sockaddr*)&inf.local, sizeof(sockaddr)) == -1)
    {
        inf.err_no = errno;
        LOGDR(WERROR,
              "{} setup_udp_server: bind failed: {}",
              WJLog::getWholeSysTime(),
              strerror(inf.err_no));
        return false;
    }

    // 设置非阻塞方式
    if (fcntl(inf.fd, F_SETFL, O_NONBLOCK | FASYNC) < 0)
    {
        LOGDR(WERROR, "{} setup_udp_server: non-block", WJLog::getWholeSysTime());
        return false;
    }

    if (!getDevMac_(c_sockinf_.fd, c_sDevMac_))
    {
        LOGDR(WWARN, "{} get mac failed", WJLog::getWholeSysTime());
    }

    return true;
}

bool Input::isStart()
{
    return c_bInitSuecss_;
}

void Input::checkXOR_(uint8_t* p_cBuf, int p_iSize)
{
    uint8_t check = 0x00;  //用于保存异或结果
    for (int i = 2; i < p_iSize - 4; i++)
        check ^= *(p_cBuf + i);

    *(p_cBuf + p_iSize - 4) = 0x00;
    *(p_cBuf + p_iSize - 3) = check;
}

int Input::sendPacket(uint8_t* p_cBuf, const int p_iSize)
{
    checkXOR_(p_cBuf, p_iSize);
    const uint8_t* l_sendBuf = p_cBuf;

    if (sendto(
            c_sockinf_.fd, l_sendBuf, p_iSize, 0, (sockaddr*)&c_remoteAddr_, sizeof(c_remoteAddr_))
        == -1)
    {
        LOGDR(WWARN,
              "{} lidar [{}] sendto error: {}",
              WJLog::getWholeSysTime(),
              c_stLaserCfg_.m_sLaserName,
              strerror(errno));
        return -1;
    }
    return 0;
}

void Input::parseSocketData_()
{
    uint8_t* buff = new uint8_t[TEMP_BUFFER_SIZE];
    uint16_t l_uiReadBuffLen = 0, l_uiReadBuffLenRaw = 0;
    uint16_t l_uiParsedIdx = 0;
    uint16_t parsedLen = 0;
    while (c_bRun_)
    {
        l_uiParsedIdx = 0;
        l_uiReadBuffLen = c_recvBuffer_.Data(buff, TEMP_BUFFER_SIZE);
        l_uiReadBuffLenRaw = l_uiReadBuffLen;
        while (l_uiReadBuffLen > (MIN_PROTOCOL_SIZE + c_iPcapHeaderLen_))
        {
            if (!c_bRun_)
                return;

            uint16_t header = (buff[l_uiParsedIdx] << 8) | buff[l_uiParsedIdx + 1];
            if (header == 0xFFAA)
            {
                uint16_t frameLen = ((buff[l_uiParsedIdx + 2] << 8) | buff[l_uiParsedIdx + 3]) + 4
                                    + c_iPcapHeaderLen_;  // FFAA 帧长
                if (l_uiReadBuffLen < frameLen)
                    break;
                parseLidarParam_(&buff[l_uiParsedIdx], frameLen);
                l_uiParsedIdx += frameLen;
                l_uiReadBuffLen -= frameLen;
            }
            else if (header == 0xFFEE)
            {
                if (l_uiReadBuffLen < SCAN_SIZE_720_FE)  // 扫描数据帧长
                    break;
                uint16_t frameLen = SCAN_SIZE_720_FE + c_iPcapHeaderLen_;
                parseScanData_(&buff[l_uiParsedIdx], frameLen);
                l_uiParsedIdx += frameLen;
                l_uiReadBuffLen -= frameLen;
            }
            else if (header == 0xFFDD)
            {
                uint8_t echoNum = buff[l_uiParsedIdx + 3];  // 回波类型
                uint16_t frameLen =
                    (echoNum == 1) ? SCAN_SIZE_720_FD_1 : SCAN_SIZE_720_FD_2;  // FFDD 帧长
                frameLen += c_iPcapHeaderLen_;
                if (l_uiReadBuffLen < frameLen)
                    break;
                parseScanData_(&buff[l_uiParsedIdx], frameLen);
                l_uiParsedIdx += frameLen;
                l_uiReadBuffLen -= frameLen;
            }
            else
            {
                l_uiParsedIdx++;
                l_uiReadBuffLen--;
            }
        }
        parsedLen = l_uiParsedIdx;
        c_recvBuffer_.Pop(parsedLen); /** @todo pop时是否有问题. 会丢帧?*/
        usleep(1000);
    }

    delete[] buff;
}

void Input::parseLidarParam_(const uint8_t* data, uint16_t len)
{
    if (!data)
        return;

    bool gotSn = false;
    bool gotDevParam = false;
    bool gotAngle = false;

    uint16_t cmd = (data[22] << 8) | data[23];
    switch (cmd)
    {
        case GET_SN: break;
        case GET_BTN_TEMP: break;
        case GET_VERTICAL_AG: break;
        case GET_DEV_PARAM: break;
        default: break;
    }
}
wj_slam::sTimeval Input::getSyncTime_(const offlinePcap_pkthdr* pcapHeader,
                                      const wj_slam::sTimeval& timenow)
{
    /** @todo 此处可改为网卡时间,这样会更加稳定且与simu数据一致*/
    wj_slam::sTimeval time;
    if (c_stSysParam_->m_bIsOnlineMode)
        time = timenow;
    else if (pcapHeader)
        time = pcapHeader->ts;
    else
        std::cerr << "get header error\n";
    return time;
}
void Input::parseScanData_(const uint8_t* data, uint16_t len)
{
    if ((!data) || (!c_bInitSuecss_) || (len < MIN_PROTOCOL_SIZE + c_iPcapHeaderLen_))
        return;

    const uint8_t* lidarData = &data[0];
    uint16_t header = (lidarData[0] << 8) | lidarData[1];
    uint16_t frameSize = (header == 0xFFEE)
                             ? SCAN_SIZE_720_FE
                             : ((lidarData[3] == 1) ? SCAN_SIZE_720_FD_1 : SCAN_SIZE_720_FD_2);
    const offlinePcap_pkthdr* offlineHeader = (const offlinePcap_pkthdr*)(&data[frameSize]);
    //获取当前系统时间,离线使用pcap时间,在线使用系统时间
    wj_slam::sTimeval dataRecvTime(0);
    wj_slam::sTimeval dataSyncTime = getSyncTime_(offlineHeader, dataRecvTime);
    uint16_t angle = (lidarData[3] << 8) | lidarData[2];

    if (len < (frameSize + c_iPcapHeaderLen_))
    {
        return;
    }

    uint16_t l_uiCurPktIdx =
        (lidarData[frameSize - 4] << 8) | lidarData[frameSize - 3];  // 扫描包序号
    uint16_t l_uiCircleIdx =
        ((lidarData[frameSize - 58] << 8) & 0xff00) | lidarData[frameSize - 57];  // 扫描圈号
    // 由于存在包乱序，一圈的首包数据不一定按序到达，故采取以下策略：
    // 以第一个具有完整包数的圈号作为预处理圈，用以确定采集的起始圈号及包号
    // 预处理圈的下一圈号作为采集起始圈号
    if (!c_bScanStartFlag_)
    {
        // 到达采集圈号，开始正式采集
        if (c_stScanBuffer1_.m_iCircleNum == l_uiCircleIdx)
        {
            // 预处理圈不满足包数要求，重新收集
            if (c_uiFirstCirclePkgNum_ != c_stLaserCfg_.m_uiFramePkgNum)
            {
                c_iFirstCycleNum_ = l_uiCircleIdx;
                c_stScanBuffer1_.m_iCircleNum = uint16_t(l_uiCircleIdx + 1);
                c_iFirstPktIdx_ = l_uiCurPktIdx;
                c_uiFirstCirclePkgNum_ = 1;
                c_FirstCirclIdx_.clear();
                c_FirstCircleData_.clear();
                s_LIDAR_RAW_DATA l_stData;
                memcpy(l_stData.m_data.c_array(), lidarData, frameSize);  // 拷贝扫描数据
                l_stData.m_stRecvTimeval = dataRecvTime;                  // 获取当前时间戳
                l_stData.m_stSyncTimeval = dataSyncTime;                  // 获取当前时间戳
                c_FirstCircleData_.push_back(l_stData);
                c_FirstCirclIdx_.push_back(l_uiCurPktIdx);
                return;
            }

            // 根据预处理圈收集结果确定起始圈号及首包帧序号
            c_bScanStartFlag_ = true;
            int startIdx = c_iFirstPktIdx_ + c_stLaserCfg_.m_uiFramePkgNum;
            c_uiExpPktIdx_ = startIdx - 1;
            c_stScanBuffer1_.m_startIdx = startIdx;

            // 将预处理圈数据拷贝至buffer2中，同时设置收集完毕状态，等待发出
            c_iCurBufferFlag_ = 2;
            resetScanBuffer_(c_stScanBuffer2_,
                             c_iFirstCycleNum_,
                             c_iFirstPktIdx_,
                             c_stLaserCfg_.m_uiFramePkgNum);

            c_stScanBuffer2_.m_frameCnt = c_uiFirstCirclePkgNum_;
            c_stScanBuffer2_.m_bIsFull = true;
            for (uint32_t i = 0; i < c_stLaserCfg_.m_uiFramePkgNum; i++)
            {
                uint16_t idx = c_FirstCirclIdx_[i] - c_stScanBuffer2_.m_startIdx;
                if (idx > c_stLaserCfg_.m_uiFramePkgNum)
                {
                    c_stScanBuffer2_.m_bIsFull = false;
                    continue;
                }
                c_stScanBuffer2_.m_data.m_vPackets[idx] = c_FirstCircleData_[i];
            }

            c_FirstCirclIdx_.reserve(0);
            c_FirstCircleData_.reserve(0);

            LOGFAE(WINFO,
                   "[Lidar Driver] : Start Circle : {}; Start Packet : {}",
                   l_uiCircleIdx - 1,
                   c_iFirstPktIdx_);
        }
        // 预处理圈号，确定该圈起始包号
        else if (c_iFirstCycleNum_ == l_uiCircleIdx)
        {
            c_uiFirstCirclePkgNum_++;
            if (c_iFirstPktIdx_ == -1)
            {
                c_FirstCirclIdx_.reserve(c_stLaserCfg_.m_uiFramePkgNum);
                c_FirstCircleData_.reserve(c_stLaserCfg_.m_uiFramePkgNum);
                c_iFirstPktIdx_ = l_uiCurPktIdx;
            }
            else
            {
                int diff = abs(c_iFirstPktIdx_ - l_uiCurPktIdx);
                c_iFirstPktIdx_ = (diff >= (int)c_stLaserCfg_.m_uiFramePkgNum)
                                      ? max<int>(c_iFirstPktIdx_, l_uiCurPktIdx)
                                      : min<int>(c_iFirstPktIdx_, l_uiCurPktIdx);
            }

            // 保留预处理圈数据，确定首包帧序号后，排序后发出
            if (c_uiFirstCirclePkgNum_ <= c_stLaserCfg_.m_uiFramePkgNum)
            {
                s_LIDAR_RAW_DATA l_stData;
                memcpy(l_stData.m_data.c_array(), lidarData, frameSize);  // 拷贝扫描数据
                l_stData.m_stRecvTimeval = dataRecvTime;                  // 获取当前时间戳
                l_stData.m_stSyncTimeval = dataSyncTime;                  // 获取当前时间戳
                c_FirstCircleData_.push_back(l_stData);
                c_FirstCirclIdx_.push_back(l_uiCurPktIdx);
            }
            return;
        }
        // 确定预处理圈号及起始采集包号
        else
        {
            c_iFirstCycleNum_ = l_uiCircleIdx + 1;
            c_stScanBuffer1_.m_iCircleNum = uint16_t(l_uiCircleIdx + 2);
            c_iFirstPktIdx_ = -1;
            c_uiFirstCirclePkgNum_ = 0;
            c_FirstCirclIdx_.clear();
            c_FirstCircleData_.clear();
            return;
        }
    }

    c_uiExpPktIdx_++;
    // 根据当前圈号选择存储Buffer
    st_ScanBuffer* buffer = nullptr;
    if (l_uiCircleIdx == c_stScanBuffer1_.m_iCircleNum)
    {
        buffer = &c_stScanBuffer1_;
    }
    else if (l_uiCircleIdx == c_stScanBuffer2_.m_iCircleNum)
    {
        buffer = &c_stScanBuffer2_;
    }
    else
    {
        // 圈号出现异常，进入初始化阶段，重新确定起始圈号及帧序号
        LOGFAE(WINFO,
               "{} [Lidar Driver] {}: Circle number error : {} - {}",
               WJLog::getWholeSysTime(),
               c_uiLidarId_,
               l_uiCircleIdx,
               c_stScanBuffer2_.m_iCircleNum);
        c_bScanStartFlag_ = false;
        c_iFirstPktIdx_ = -1;
        c_iFirstCycleNum_ = l_uiCircleIdx + 1;
        c_uiFirstCirclePkgNum_ = 0;
        resetScanBuffer_(
            c_stScanBuffer1_, uint16_t(l_uiCircleIdx + 1), 0, c_stLaserCfg_.m_uiFramePkgNum);
        resetScanBuffer_(
            c_stScanBuffer2_, uint16_t(l_uiCircleIdx + 1), 0, c_stLaserCfg_.m_uiFramePkgNum);
        return;
    }

    // 确定当前包号在本圈中的序号，并将本包添加到对应序号的buffer中。
    uint16_t idx = l_uiCurPktIdx - buffer->m_startIdx;  // 本圈序号
    if (idx >= c_stLaserCfg_.m_uiFramePkgNum)
    {
        LOGFAE(WINFO,
               "[Lidar Driver] : Packet number error. Start:{}, Curr:{}",
               buffer->m_startIdx,
               l_uiCurPktIdx);
        return;
    }

    memcpy(buffer->m_data.m_vPackets[idx].m_data.c_array(), lidarData, frameSize);  // 拷贝扫描数据
    buffer->m_data.m_vPackets[idx].m_stRecvTimeval = dataRecvTime;  // 获取系统时间戳
    buffer->m_data.m_vPackets[idx].m_stSyncTimeval = dataSyncTime;  // 获取系统时间戳
    buffer->m_frameCnt++;
    if (buffer->m_frameCnt == c_stLaserCfg_.m_uiFramePkgNum)  // 本圈收集完毕
    {
        buffer->m_bIsFull = true;
    }

    /**
     * 解决扫描数据跨圈乱序的问题:
     * 1. 若buffer1完整，则一圈收集完毕；
     * 2. 若buffer2已经收集了12(?)包，当前使用的仍然是buffer1(buffer1未full)，
     *    则认为丢包，仍然认为buffer1收集完毕。
     * 反之亦然。
     * **/
    if (c_stScanBuffer1_.m_bIsFull || (c_iCurBufferFlag_ == 1 && c_stScanBuffer2_.m_frameCnt > 10))
    {
        c_outScanDataCb_(c_stScanBuffer1_);
        resetScanBuffer_(c_stScanBuffer1_,
                         (c_stScanBuffer2_.m_iCircleNum + 1) % 65536,
                         c_stScanBuffer2_.m_startIdx + c_stLaserCfg_.m_uiFramePkgNum,
                        c_stLaserCfg_.m_uiFramePkgNum);
        c_iCurBufferFlag_ = 2;
    }
    else if ((c_stScanBuffer2_.m_bIsFull
              || (c_iCurBufferFlag_ == 2 && c_stScanBuffer1_.m_frameCnt > 10)))
    {
        c_outScanDataCb_(c_stScanBuffer2_);
        resetScanBuffer_(c_stScanBuffer2_,
                         (c_stScanBuffer1_.m_iCircleNum + 1) % 65536,
                         c_stScanBuffer1_.m_startIdx + c_stLaserCfg_.m_uiFramePkgNum,
                        c_stLaserCfg_.m_uiFramePkgNum);
        c_iCurBufferFlag_ = 1;
    }
}

void Input::recvSocketData_()
{
    uint8_t* rbuf = new uint8_t[TEMP_BUFFER_SIZE];
    sockaddr_in sender_address;
    socklen_t sender_address_len = sizeof(sender_address);
    bool l_bTimeoutFlag = false;
    uint32_t l_uiTimeoutCnt = 0;

    TicToc l_tt;
    while (c_bRun_)
    {
        ssize_t nbytes = recvfrom(c_sockinf_.fd,
                                  rbuf,
                                  TEMP_BUFFER_SIZE,
                                  0,
                                  (sockaddr*)&sender_address,
                                  &sender_address_len);
        if (nbytes < 0)
        {
            if (errno != EWOULDBLOCK)
            {
                LOGDR(WWARN, "[input] Recvice Failed!");
                usleep(1000);
                continue;
            }
        }
        else if (nbytes > 0)
        {
            l_tt.tic();
            c_recvBuffer_.Push(rbuf, nbytes);
            if (wj_slam::DevStatus::DEVCONNECT
                != c_stSysParam_->m_lidar[c_uiLidarId_].m_dev.m_status)
                c_stSysParam_->m_lidar[c_uiLidarId_].m_dev.setStatus(
                    wj_slam::DevStatus::DEVCONNECT);
            if (l_bTimeoutFlag)
            {
                LOGFAE(WINFO,
                       "{} 雷达 [{}] 数据正常......",
                       WJLog::getWholeSysTime(),
                       c_stLaserCfg_.m_sLaserName);
                l_bTimeoutFlag = false;
                l_uiTimeoutCnt = 0;
                checkConnect_();
            }
        }

        // 超时时间内未接收到点云数据，设置设备状态 等待数据，在此处判断udp需设置为非阻塞模式
        uint32_t l_iMsDiff = l_tt.toc();
        if (l_iMsDiff > c_stLaserCfg_.m_uiFrameTimeMax)
        {
            if (l_uiTimeoutCnt % 100 == 0)
            {
                c_stSysParam_->m_lidar[c_uiLidarId_].m_dev.setStatus(wj_slam::DevStatus::DATAWAIT);
                LOGFAE(WERROR,
                       "{} 等待雷达 [{}] 数据......",
                       WJLog::getWholeSysTime(),
                       c_stLaserCfg_.m_sLaserName);
                l_bTimeoutFlag = true;
                l_uiTimeoutCnt++;
            }
        }

        usleep(100);
    }
    delete[] rbuf;
}

bool Input::checkConnect_()
{
    std::string l_sMac = "";
    if (getDevMac_(c_sockinf_.fd, l_sMac))
    {
        if (c_bInitSuecss_ && c_sDevMac_ != l_sMac)
        {
            if (c_stSysParam_->m_bIsOnlineMode)
            {
                c_bInitSuecss_ = false;
                requestBaseParam();
            }
            c_sDevMac_ = l_sMac;
        }
        return true;
    }
    return false;
}

void Input::initScanBuffer_(uint16_t npackets)
{
    // 初始化Buffer
    c_recvBuffer_.Reset(RECV_BUFFER_SIZE);
    resetScanBuffer_(c_stScanBuffer1_, -1, 0, npackets);
    resetScanBuffer_(c_stScanBuffer2_, -1, 0, npackets);
}

void Input::resetScanBuffer_(st_ScanBuffer& buffer,
                            int32_t circleNum,
                            uint16_t startIdx,
                            uint16_t npackets)
{
    buffer.m_iCircleNum = circleNum;
    buffer.m_frameCnt = 0;
    buffer.m_bIsFull = false;
    buffer.m_startIdx = startIdx;
    buffer.m_data.m_vPackets.clear();
    buffer.m_data.m_vPackets.resize(npackets);
    buffer.m_data.m_iTimestamp = 0;
}

int Input::getDevMac_(int p_iSocket, std::string& p_sMac)
{
    int ret = 0;
    char l_cMac[12];
    arpreq l_stArpreq;
    sockaddr_in l_stDstaddIn;
    socklen_t len = sizeof(sockaddr_in);
    memset(&l_stArpreq, 0, sizeof(arpreq));
    memset(&l_stDstaddIn, 0, sizeof(sockaddr_in));
    if (getpeername(p_iSocket, (sockaddr*)&l_stDstaddIn, &len) >= 0)
    {
        memcpy(&l_stArpreq.arp_pa, &l_stDstaddIn, sizeof(sockaddr_in));
        strcpy(l_stArpreq.arp_dev, c_stLaserCfg_.m_dev.m_sNetName.c_str());
        l_stArpreq.arp_pa.sa_family = AF_INET;
        l_stArpreq.arp_ha.sa_family = AF_UNSPEC;
        if (ioctl(p_iSocket, SIOCGARP, &l_stArpreq) < 0)
            printf("ioctl SIOCGARP");
        else
        {
            char* ptr = (char*)l_stArpreq.arp_ha.sa_data;
            ret = sprintf(l_cMac,
                          "%02x%02x%02x%02x%02x%02x",
                          *ptr,
                          *(ptr + 1),
                          *(ptr + 2),
                          *(ptr + 3),
                          *(ptr + 4),
                          *(ptr + 5));
            if (ret)
            {
                p_sMac = l_cMac;
                LOGFAE(WERROR, "Mac:{}", p_sMac);
            }
        }
    }
    return ret;
}

#pragma endregion

#pragma region 720初始化类

Input720::Input720(uint32_t p_uiLidarId, ScanDataCb scandataCb)
    : Input(p_uiLidarId, scandataCb), c_sVerAnglePath_(""), c_sCalibrationPath_(""),
      c_bIsGetSn_(false), c_bIsGetBaseParam_(false), c_bIsGetVerAngleData_(false),
      c_bIsGetLevelAngleData_(false)
{
    memset(&c_iLevelAngleData_, 0, sizeof(c_iLevelAngleData_));
}

Input720::~Input720(void) {}

void Input720::requestBaseParam()
{
    std::string l_sFilePath = "_" + c_stLaserCfg_.m_sLaserName;

    c_sVerAnglePath_ = c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".csv";
    c_sCalibrationPath_ = c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".txt_PX";

    // 在线查询 离线不查询
    if (c_stSysParam_->m_bIsOnlineMode && c_stLaserCfg_.m_bGetCalibrationOnline)
    {
        for (int i = 0; i < 3; i++)
        {
            if (!c_bIsGetSn_)
            {
                requestSN_();
                usleep(100 * 1000);
            }
            if (!c_bIsGetBaseParam_)
            {
                requestBaseParam_();
                usleep(100 * 1000);
            }
            if (!c_bIsGetVerAngleData_)
            {
                requestVerAngleData_();
                usleep(100 * 1000);
            }
            if (!c_bIsGetLevelAngleData_)
            {
                requestLevelAngleData_();
                usleep(1000 * 1000);
            }
        }

        if (!(c_bIsGetSn_ && c_bIsGetBaseParam_ && c_bIsGetVerAngleData_
              && c_bIsGetLevelAngleData_))
            usleep(1000 * 1000);

        // 老款雷达仅查询 不处理
        if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720F")
        {
            c_bIsGetSn_ = true;
            c_bIsGetBaseParam_ = true;
            c_bIsGetVerAngleData_ = true;
            c_bIsGetLevelAngleData_ = true;
        }

        if (!c_bIsGetSn_)
        {
            c_stSysParam_->m_fae.setErrorCode("C9");
            LOGFAE(WERROR, "雷达 [{}] 查询SN失败 | 无回复/校验异常", c_stLaserCfg_.m_sLaserName);
        }
        if (!c_bIsGetBaseParam_)
        {
            c_stSysParam_->m_fae.setErrorCode("C11");
            LOGFAE(
                WERROR, "雷达 [{}] 查询配置参数失败 | 无回复/校验异常", c_stLaserCfg_.m_sLaserName);
        }
        if (!c_bIsGetVerAngleData_)
        {
            c_stSysParam_->m_fae.setErrorCode("C12");
            LOGFAE(WERROR,
                   "雷达 [{}] 查询修正表2失败 | 无回复/校验异常，请录制雷达数据包!",
                   c_stLaserCfg_.m_sLaserName);
        }
        if (!c_bIsGetLevelAngleData_)
        {
            LOGFAE(
                WERROR, "雷达 [{}] 查询修正表1失败 | 无回复/校验异常", c_stLaserCfg_.m_sLaserName);
        }

        c_bInitSuecss_ =
            c_bIsGetSn_ && c_bIsGetBaseParam_ && c_bIsGetVerAngleData_ && c_bIsGetLevelAngleData_;
        if (!c_bInitSuecss_)
        {
            c_stSysParam_->m_lidar[c_uiLidarId_].m_dev.setStatus(wj_slam::DevStatus::GETPARAMERROR);
            LOGFAE(WERROR, " *********************************************** ");
            LOGFAE(WERROR, " * 1. 检查网线是否有松动 ");
            LOGFAE(
                WERROR,
                " * 2. 查看端口是否设置正确(默认雷达端口: 3333, PC端口: 2.88[3001]/2.87[3002]) ");
            LOGFAE(WERROR,
                   " * 3. 如果上述方法不能解决, 请启动wireshark抓包, 并联系万集开发人员。 ");
            LOGFAE(WERROR, " * **********************************************");
        }
    }
}

void Input720::parseLidarParam_(const uint8_t* data, uint16_t len)
{
    if (!data)
        return;
    int l_iLevelAgPkgIdx = 0;
    uint16_t cmd = (data[22] << 8) | data[23];
    switch (cmd)
    {
        case GET_SN:
            if (!c_bIsGetSn_)
            {
                // 老款雷达仅查询 不处理
                if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720F")
                {
                    c_bIsGetSn_ = true;
                    LOGFAE(WINFO, "雷达 [{}] 查询SN结束!", c_stLaserCfg_.m_sLaserName);
                    break;
                }
                if (analyseSN_(data, len))
                {
                    c_bIsGetSn_ = true;
                    LOGFAE(WINFO, "雷达 [{}] 查询SN成功 | 结束", c_stLaserCfg_.m_sLaserName);
                }
                else
                {
                    LOGFAE(WWARN, "雷达 [{}] 查询SN失败 | 校验异常", c_stLaserCfg_.m_sLaserName);
                }
            }
            break;
        case GET_BTN_TEMP: break;
        case GET_VERTICAL_AG:
            // 解析和储存垂直分辨率数据
            if (!c_bIsGetVerAngleData_)
            {
                // 老款雷达仅查询 不处理
                if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720F")
                {
                    c_bIsGetVerAngleData_ = true;
                    LOGFAE(WINFO, "雷达 [{}] 查询修正表2结束!", c_stLaserCfg_.m_sLaserName);
                    break;
                }
                if (analyseVerAngleData_(data, len))
                {
                    c_bIsGetVerAngleData_ = true;
                    LOGFAE(WINFO, "雷达 [{}] 查询修正表2成功 | 结束!", c_stLaserCfg_.m_sLaserName);
                }
                else
                {
                    LOGFAE(
                        WWARN, "雷达 [{}] 查询修正表2失败 | 校验异常", c_stLaserCfg_.m_sLaserName);
                }
            }
            break;
        case GET_LEVEL_AG:
            if (!c_bIsGetLevelAngleData_)
            {
                // 老款雷达仅查询 不处理
                if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720F")
                {
                    c_bIsGetLevelAngleData_ = true;
                    LOGFAE(WINFO, "雷达 [{}] 查询修正表1查询结束!", c_stLaserCfg_.m_sLaserName);
                    break;
                }
                l_iLevelAgPkgIdx = analyseLevelAngleData_(data, len);
                if (l_iLevelAgPkgIdx >= 0)
                {
                    c_iLevevAgPkgStatus_ |= (1 << l_iLevelAgPkgIdx);
                    c_bIsGetLevelAngleData_ = (c_iLevevAgPkgStatus_ == 0xFFFF);
                    if (c_bIsGetLevelAngleData_)
                    {
                        LOGFAE(WINFO,
                               "雷达 [{}] 查询修正表1第{}包 | 查询成功!",
                               c_stLaserCfg_.m_sLaserName,
                               l_iLevelAgPkgIdx);
                        LOGFAE(WINFO,
                               "雷达 [{}] 查询修正表1查询成功 | 结束!",
                               c_stLaserCfg_.m_sLaserName);
                        saveLevelAngleData_(c_iLevelAngleData_,
                                            sizeof(c_iLevelAngleData_)
                                                / sizeof(c_iLevelAngleData_[0]),
                                            c_sCalibrationPath_);
                    }
                    else
                    {
                        LOGFAE(WINFO,
                               "雷达 [{}] 查询修正表1第{}包 | 查询成功!",
                               c_stLaserCfg_.m_sLaserName,
                               l_iLevelAgPkgIdx);
                    }
                }
                else
                {
                    LOGFAE(WWARN,
                           "雷达 [{}] 查询修正表1第{}包 | 校验异常",
                           c_stLaserCfg_.m_sLaserName,
                           l_iLevelAgPkgIdx);
                }
            }
            break;
        case GET_DEV_PARAM:
            if (!c_bIsGetBaseParam_)
            {
                if (analyseBaseParam_(data, len))
                {
                    c_bIsGetBaseParam_ = true;
                    LOGFAE(WINFO, "雷达 [{}] 查询配置参数成功 | 结束!", c_stLaserCfg_.m_sLaserName);
                }
                else
                {
                    LOGFAE(
                        WWARN, "雷达 [{}] 查询配置参数失败 | 校验异常", c_stLaserCfg_.m_sLaserName);
                }
            }
            break;
        case GET_FIRE_STATUS: break;
        default: break;
    }
}

int Input720::requestSN_()
{
    // 读取SN
    uint8_t c_getSN[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01,
                           0x00, 0x0B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x10,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEE, 0xEE};

    LOGFAE(WINFO, "雷达 [{}] 查询SN...", c_stLaserCfg_.m_sLaserName);
    return sendPacket(c_getSN, sizeof(c_getSN));
}

void Input720::splitStr_(const std::string& s, std::vector<std::string>& v, const std::string& c)
{
    std::string::size_type pos1, pos2;
    pos2 = s.find(c);
    pos1 = 0;
    while (std::string::npos != pos2)
    {
        if (pos1 != pos2)
            v.push_back(s.substr(pos1, pos2 - pos1));
        v.push_back(s.substr(pos2, c.size()));

        pos1 = pos2 + c.size();
        pos2 = s.find(c, pos1);
    }
    if (pos1 != s.length())
        v.push_back(s.substr(pos1));
}

void Input720::compareSN_(std::string& p_sSetSN, std::string p_sReadSN)
{
    if (p_sReadSN != p_sSetSN)
    {
        // SN不符 则删除旧表
        deleteFileOrDir(c_sVerAnglePath_);
        deleteFileOrDir(c_sCalibrationPath_);
        LOGFAE(WWARN, "雷达SN与配置SN [{}] 不一致, 自动删除雷达修正表", c_stLaserCfg_.m_sLaserSN);
        p_sSetSN = p_sReadSN;
    }
}

bool Input720::analyseSN_(const uint8_t* p_buf, const int p_iSize)
{
    // 720SN-eg: 720A20210506x035
    char l_cSN[17] = {0};
    memcpy(l_cSN, &p_buf[26], 16);
    LOGFAE(WINFO, "雷达 [{}] 查询SN | SN码: {}", c_stLaserCfg_.m_sLaserName, std::string(l_cSN));

    compareSN_(c_stSysParam_->m_lidar[c_uiLidarId_].m_sLaserSN, std::string(l_cSN));

    // 最前面是720开头720xxxxxxxxx
    int l_iBasicType =
        (int)(l_cSN[0] - '0') * 100 + (int)(l_cSN[1] - '0') * 10 + (int)(l_cSN[2] - '0');
    char l_cUseType = l_cSN[3];
    // 生产日期
    int l_iManufactureTime = (int)(l_cSN[4] - '0') * 10 + (int)(l_cSN[5] - '0');

    // 如果不是720则异常
    if (l_iBasicType == 720)
    {
        std::string l_sReadDevType;
        // 22年启用
        if (l_iManufactureTime > 21)
        {
            if (l_cUseType == 'C')
                l_sReadDevType = "WLR720C";
            else if (l_cUseType == 'F')
                l_sReadDevType = "WLR720FCW";
            else if (l_cUseType == 'G')
                l_sReadDevType = "WLR720G";
            else
                l_sReadDevType = "WLR" + std::to_string(l_iBasicType) + l_cUseType;
        }
        // 之前的默认为720F
        else
            l_sReadDevType = "WLR720F";

        // 切割并提取主类型号  eg: WLR720F_NP和WLR720F符合
        std::vector<std::string> l_vsOut;
        splitStr_(c_stLaserCfg_.m_dev.m_sDevType, l_vsOut, "_NP");
        if (l_vsOut.size() == 1 || l_vsOut.size() == 2)
        {
            if (l_sReadDevType != l_vsOut[0])
            {
                c_stSysParam_->m_fae.setErrorCode("C10");
                // c_stSysParam_->m_fae.setErrorCode("C14");
                LOGFAE(WERROR,
                       "雷达 [{}] 类型设定错误：设定 {} 读取 {}  批次 "
                       "{},请根据雷达类型确认雷达型号配置是否正确！",
                       c_stLaserCfg_.m_sLaserName,
                       c_stLaserCfg_.m_dev.m_sDevType,
                       l_sReadDevType,
                       std::to_string(l_iManufactureTime));
                LOGFAE(WERROR, " *********************************************** ");
                LOGFAE(WERROR, " * WLR720FCW为顺时针旋转");
                LOGFAE(WERROR, " * WLR720F为逆时针旋转");
                LOGFAE(WERROR, " * **********************************************");
                LOGFAE(WWARN,
                       "雷达 [{}] 类型设定错误：根据读取本次自动修改为 {}",
                       c_stLaserCfg_.m_sLaserName,
                       l_sReadDevType);
                c_stSysParam_->m_lidar[c_uiLidarId_].m_dev.m_sDevType = l_sReadDevType;
                return false;
            }
            else
                return true;
        }
        else
        {
            // c_stSysParam_->m_fae.setErrorCode("C15");
            LOGFAE(WERROR,
                   "雷达 [{}] 类型校验异常： {}，请检查雷达类型！",
                   c_stLaserCfg_.m_sLaserName,
                   c_stLaserCfg_.m_dev.m_sDevType);
        }
    }
    else
    {
        // c_stSysParam_->m_fae.setErrorCode("C16");
        LOGFAE(WERROR,
               "雷达 [{}] 类型读取异常： {}，不支持此类型，请检查设定的雷达类型是否正确！",
               c_stLaserCfg_.m_sLaserName,
               std::to_string(l_iBasicType) + l_cUseType);
    }
    return false;
}

int Input720::requestBaseParam_()
{
    // 读取设备基本参数
    uint8_t c_getParam[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x01, 0x01, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x05, 0x01, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x11, 0xEE, 0xEE};

    LOGFAE(WINFO, "雷达 [{}] 查询配置参数...", c_stLaserCfg_.m_sLaserName);
    return sendPacket(c_getParam, sizeof(c_getParam));
}

bool Input720::analyseBaseParam_(const uint8_t* p_buf, const int p_iSize)
{
    LOGFAE(WINFO, "");
    LOGFAE(WINFO, "******  雷达 [{}] 配置参数查询结果校验 ******", c_stLaserCfg_.m_sLaserName);
    bool l_bUseFlag = true;
    char l_cParams[15] = {0};
    memcpy(l_cParams, &p_buf[26], 15);

    // 工作模式 [0]: 1:扫描模式
    int l_iWorkMode = (int)l_cParams[0];
    // 扫描模式 [1]:0~3 5~20Hz
    int l_iRotMode = (int)l_cParams[1];
    // 回波模式 [7]:0X10~0X30 第几重回波 0X40 双回波
    int l_iEchoMode = (int)l_cParams[7];
    // 协议模式 [13]: 0：720F-19线 1：720G 2：720G云台 3：720C-16线
    int l_iProcMode = (int)l_cParams[13];
    // printf("协议模式 = %X, %d\n", l_cParams[13], l_iProcMode);
    // 协议兼容使能 [14]: 0：22新协议 1: 19线单回波旧协议
    int l_iProcCompMode = (int)l_cParams[14];

    LOGFAE(WINFO,
           "雷达基础参数 工作模式: {:#X} | 扫描模式：{:#X} 回波模式：{:#X} 协议模式： {:#X} "
           "协议新旧： {:#X}",
           (int)l_cParams[0],
           (int)l_cParams[1],
           (int)l_cParams[7],
           (int)l_cParams[13],
           (int)l_cParams[14]);

    switch (l_iProcCompMode)
    {
        case 0:
        {
            l_bUseFlag = false;
            LOGFAE(WERROR, "雷达协议类型: 新协议 | 暂不支持此协议, 请修改雷达硬件配置");
            // c_stSysParam_->m_fae.setErrorCode("C30");
            break;
        }
        case 1:
        {
            if (c_stLaserCfg_.m_dev.m_sDevType != "WLR720F"
                && c_stLaserCfg_.m_dev.m_sDevType != "WLR720FCW")
            {
                // c_stSysParam_->m_fae.setErrorCode("C33");
                LOGFAE(WERROR,
                       "协议类型: 旧协议 | 对应设备类型 [WLR720F] / [WLR720FCW], "
                       "不符合设备类型[{}]，请检查设备类型与协议是否匹配!",
                       c_stLaserCfg_.m_dev.m_sDevType);
                l_bUseFlag = false;
            }
            // 19通道-单回波旧协议
            if (c_stLaserCfg_.m_uiFramePkgNum != 120 || c_stLaserCfg_.m_uiFramePktSize != 1260
                || c_stLaserCfg_.m_dev.m_uiDataMinLen != 1260 || c_stLaserCfg_.m_uiFrameTime != 100)
            {
                // c_stSysParam_->m_fae.setErrorCode("C34");
                LOGFAE(WERROR,
                       "协议类型: 旧协议 | 不符合程序参数 {} {} {} {} {} ,请联系万集开发人员!",
                       c_stLaserCfg_.m_dev.m_sDevType,
                       c_stLaserCfg_.m_uiFramePkgNum,
                       c_stLaserCfg_.m_uiFramePktSize,
                       c_stLaserCfg_.m_dev.m_uiDataMinLen,
                       c_stLaserCfg_.m_uiFrameTime);

                c_stSysParam_->m_lidar[c_uiLidarId_].m_uiFramePkgNum = 120;
                c_stSysParam_->m_lidar[c_uiLidarId_].m_uiFramePktSize = 1260;
                c_stSysParam_->m_lidar[c_uiLidarId_].m_dev.m_uiDataMinLen = 1260;
                c_stSysParam_->m_lidar[c_uiLidarId_].m_uiFrameTime = 100;
                l_bUseFlag = false;
            }
            break;
        }
        default:
            // c_stSysParam_->m_fae.setErrorCode("C35");
            LOGFAE(
                WERROR, "雷达协议类型: {:#X} | 不支持该协议,请联系万集开发人员! ", l_cParams[14]);
            l_bUseFlag = false;
            break;
    }
    LOGFAE(WINFO, "******  雷达 [{}] 配置参数结果校验完成 ******", c_stLaserCfg_.m_sLaserName);
    LOGFAE(WINFO, "");
    return l_bUseFlag;
}

int Input720::requestVerAngleData_()
{
    // 读取垂直分比例表
    uint8_t c_getVerAngleData[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                     0x00, 0x01, 0x01, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                     0x00, 0x00, 0x00, 0x00, 0x05, 0x14, 0x00, 0x00, 0x00,
                                     0x00, 0x00, 0x00, 0x00, 0x04, 0xEE, 0xEE};

    LOGFAE(WINFO, "雷达 [{}] 正在查询修正表2...", c_stLaserCfg_.m_sLaserName);
    return sendPacket(c_getVerAngleData, sizeof(c_getVerAngleData));
}

bool Input720::analyseVerAngleData_(const uint8_t* p_buf, const int p_size)
{
    LOGFAE(WINFO, "雷达 [{}] 正在校验修正表2...", c_stLaserCfg_.m_sLaserName);
    float verAngle[19] = {0};
    float recvVerAngle[16] = {0};
    for (int j = 0; j < 16; j++)
        recvVerAngle[j] = (p_buf[30 + j * 4] << 24 | p_buf[29 + j * 4] << 16
                           | p_buf[28 + j * 4] << 8 | p_buf[27 + j * 4])
                          * 1.0 / 1000.0;
    // 检查异常数据
    for (int j = 0; j < 16; j++)
    {
        if (recvVerAngle[j] < -20.0 || recvVerAngle[j] > 20.0)
        {
            LOGFAE(WERROR,
                   "雷达 [{}] 修正表2校验失败 | 线Id: {} data: {} | 数据异常，请录制雷达数据包!",
                   c_stLaserCfg_.m_sLaserName,
                   j,
                   recvVerAngle[j]);
            LOGFAE(WERROR, " *********************************************** ");
            LOGFAE(WERROR, " * 请启动wireshark录制雷达数据包，并联系万集开发人员！");
            LOGFAE(WERROR, " * **********************************************");
            c_stSysParam_->m_fae.setErrorCode("C13");
            // c_stSysParam_->m_fae.setErrorCode("C39");
            return false;
        }
    }
    // 19通道
    if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720A" || c_stLaserCfg_.m_dev.m_sDevType == "WLR720F"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720F_NP"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720FCW"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720FCW_NP"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720G")
    {
        verAngle[0] = verAngle[5] = verAngle[9] = verAngle[14] = recvVerAngle[7];
        verAngle[1] = recvVerAngle[0];
        verAngle[2] = recvVerAngle[1];
        verAngle[3] = recvVerAngle[2];
        verAngle[4] = recvVerAngle[3];

        verAngle[6] = recvVerAngle[4];
        verAngle[7] = recvVerAngle[5];
        verAngle[8] = recvVerAngle[6];

        verAngle[10] = recvVerAngle[8];
        verAngle[11] = recvVerAngle[9];
        verAngle[12] = recvVerAngle[10];
        verAngle[13] = recvVerAngle[11];

        verAngle[15] = recvVerAngle[12];
        verAngle[16] = recvVerAngle[13];
        verAngle[17] = recvVerAngle[14];
        verAngle[18] = recvVerAngle[15];
        saveVerAngleData_(verAngle, 19, c_sVerAnglePath_);
    }
    else
    {
        // 16通道
        saveVerAngleData_(recvVerAngle, 16, c_sVerAnglePath_);
    }

    return true;
}

int Input720::requestLevelAngleData_()
{
    // 读取偏心修正表
    uint8_t c_getLevelAngleData[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x01, 0x01, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x00, 0x00, 0x00, 0x05, 0x1A, 0x00, 0x00, 0x00,
                                       0x01, 0x03, 0xC0, 0x00, 0xC8, 0xEE, 0xEE};

    LOGFAE(WINFO, "雷达 [{}] 正在查询修正表1...", c_stLaserCfg_.m_sLaserName);

    bool res = true;
    for (uint16_t i = 1; i <= 15; i++)
    {
        if (c_iLevevAgPkgStatus_ & (1 << i))
        {
            continue;
        }
        *(c_getLevelAngleData + 27) = i;
        res = sendPacket(c_getLevelAngleData, sizeof(c_getLevelAngleData)) && res;
        usleep(50000);
    }
    return res;
}

int Input720::analyseLevelAngleData_(const uint8_t* p_buf, const int p_size)
{
    // 协议内包号1-15 变为数组偏移0~14
    int l_iPkgNum = (p_buf[27] | p_buf[26] << 8) - 1;
    int l_iSize = (p_buf[28] << 8) | p_buf[29];
    // 异常数据
    if (l_iPkgNum > 14 || l_iSize != 960)
    {
        c_stSysParam_->m_fae.setErrorCode("C15");
        // c_stSysParam_->m_fae.setErrorCode("C43");
        LOGFAE(WERROR,
               "雷达 [{}] 修正表1校验失败 | pkt: {} size: {},请联系万集开发人员！",
               c_stLaserCfg_.m_sLaserName,
               l_iPkgNum,
               l_iSize);
        return -1;
    }
    else
    {
        // 只使用偶数位数值
        l_iSize /= 2;
        LOGDR(WDEBUG,
              "{} Horizon angle resolution pack-{} with size {}",
              WJLog::getWholeSysTime(),
              l_iPkgNum,
              l_iSize);
    }

    // 按照分辨率0.025填写偏心修正表,但只读取0.05分辨率
    for (int j = 0; j < l_iSize; j++)
        c_iLevelAngleData_[l_iPkgNum * l_iSize + j] = static_cast<char>(p_buf[30 + 2 * j]);

    return l_iPkgNum + 1;
}

void Input720::saveVerAngleData_(float* p_VerAngleData, uint p_uiSize, std::string p_sFilePath)
{
    int l_nWriteOverSign = 1;
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
    if (l_filePoseWR.is_open())
    {
        // 保存格式:19行,两列,第二列为0
        for (uint i = 0; i < p_uiSize; i++)
            l_filePoseWR << *(p_VerAngleData + i) << ","
                         << "0" << std::endl;
        LOGFAE(
            WINFO, "雷达 [{}] 保存修正表2成功: 路径: {}", c_stLaserCfg_.m_sLaserName, p_sFilePath);
    }
    else
    {
        c_stSysParam_->m_fae.setErrorCode("C14");
        // c_stSysParam_->m_fae.setErrorCode("C40");
        LOGFAE(WERROR,
               "雷达 [{}] 保存修正表2失败: 文件打开异常 | 请联系万集开发人员!",
               c_stLaserCfg_.m_sLaserName);
    }
    l_filePoseWR.close();
}

bool Input720::saveLevelAngleData_(int* p_VerAngleData, uint p_uiSize, std::string p_sFilePath)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
    if (l_filePoseWR.is_open())
    {
        // 保存格式:1440行,5列
        for (uint i = 0; i < p_uiSize; i += 5)
            l_filePoseWR << *(p_VerAngleData + i) << "," << *(p_VerAngleData + i + 1) << ","
                         << *(p_VerAngleData + i + 2) << "," << *(p_VerAngleData + i + 3) << ","
                         << *(p_VerAngleData + i + 4) << std::endl;
        LOGFAE(
            WINFO, "雷达 [{}] 保存修正表1成功: 路径: {}", c_stLaserCfg_.m_sLaserName, p_sFilePath);
    }
    else
    {
        LOGFAE(WERROR,
               "雷达 [{}] 保存修正表1失败: 文件打开异常 | 请检查!",
               c_stLaserCfg_.m_sLaserName);
        // c_stSysParam_->m_fae.setErrorCode("C44");
        return false;
    }
    l_filePoseWR.close();
    return true;
}
#pragma endregion
}  // namespace wanji_driver
