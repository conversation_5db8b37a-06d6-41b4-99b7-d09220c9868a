/*
 * @Author: your name
 * @Date: 2021-04-21 14:37:53
 * @LastEditTime: 2023-12-07 13:45:36
 * @LastEditors: shuangquan han
 * @Description: In User Settings Edit
 * @FilePath: /test_ws/src/wanji_ros/wslam_pretreat/src/FeatureExtract.cpp
 */
#include "algorithm/preproc/feature_extract/featureExtract.h"
#include "algorithm/optimize/kdtree_flann2d.h"
#include "tic_toc.h"

namespace wj_FE {

FeatureExtract::FeatureExtract() : c_iScenarioMode_(0), c_bDebug_(false)
{
    allocateMemery_();
    setParamScenario(SCENARIO::DEFAULT);
}

FeatureExtract::FeatureExtract(bool p_bDebug) : c_iScenarioMode_(0), c_bDebug_(p_bDebug)
{
    allocateMemery_();
    setParamScenario(SCENARIO::DEFAULT);
}

int FeatureExtract::allocateMemery_()
{
    try
    {
        for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
        {
            c_pcScansCopy_[i].m_pc.reset();
            c_pcScanCorn_[i].reset(new pcFeature());
            c_pcScanSurf_[i].reset(new pcFeature());
        }
        // debug 分类点云显示
        c_pcSegment_.reset(new pcFeature());
        c_pcSegmentMiddleScan_.reset(new pcFeature());
        c_pcSegmentCurbScan_.reset(new pcFeature());
        c_pcDsCloud_.reset(new pcDs());
        c_pcAll_.reset(new pcFeature());
        // 面点采样中转
        c_pcSurfTransfer_.reset(new pcFeature());
        // 默认使用中间线作为curb通道
        c_viCurbLineId.emplace_back(8);
    }
    catch (const std::bad_alloc& e)
    {
        throw e.what();
        return state::FAIL;
    }
    return state::DONE;
}

void FeatureExtract::setParamScenario(const int p_iScenarioMode)
{
    // 如果模式未改变则不切换
    if (p_iScenarioMode == c_iScenarioMode_)
        return;

    switch (p_iScenarioMode)
    {
        case SCENARIO::INDOOR:
            c_iScenarioMode_ = SCENARIO::INDOOR;
            setParamInDoor_();
            break;
        case SCENARIO::OUTDOOR:
            c_iScenarioMode_ = SCENARIO::OUTDOOR;
            setParamOutDoor_();
            break;
        case SCENARIO::VACROUS:
            c_iScenarioMode_ = SCENARIO::VACROUS;
            setParamVacuous_();
            break;
        case SCENARIO::LABOR:
            c_iScenarioMode_ = SCENARIO::LABOR;
            setParamLabor_();
            break;
        default:
            c_iScenarioMode_ = SCENARIO::OUTDOOR;
            setParamOutDoor_();
            break;
    }
}

int FeatureExtract::setParamInDoor_()
{
    resetConfig();
    setIntensitySmoothCheck(true);
    setSmallThingScaleRange(0.03, 0.15);
    setCornCurvRange(60.0, 120.0);
    setLidarHeight(c_stCfg_.m_attach.m_fLidarHeight, 0.15);
    // 室内不进行剧烈采样
    std::vector<float> l_sampleRange = {5.0, 15.0, 20.0};
    std::vector<float> l_sampleScale = {0.04, 0.0225, 0.0144, 0.01, 0.64};
    setUniSampleSurfFilter(true, 2500, &l_sampleRange, &l_sampleScale);
    setVerticalCornFilter(true, 3, 0.3, 0.6);
    return state::DONE;
}

int FeatureExtract::setParamOutDoor_()
{
    resetConfig();
    setIntensitySmoothCheck(true);
    setSmallThingScaleRange(0.05, 0.20);
    setCornCurvRange(45.0, 135.0);
    return state::DONE;
}

int FeatureExtract::setParamVacuous_()
{
    setParamOutDoor_();
    std::vector<int> l_vUseCond = getFeatureUse();
    l_vUseCond[6] = 1;
    setFeatureUse(l_vUseCond);
    setIntensitySmoothCheck(true, 8, 0.5);
    return state::DONE;
}

int FeatureExtract::setParamLabor_()
{
    resetConfig();
    std::vector<int> l_vUseCond = getFeatureUse();
    l_vUseCond = std::vector<int>(l_vUseCond.size(), 1);
    setFeatureUse(l_vUseCond);
    return state::DONE;
}

#pragma region copy

int FeatureExtract::copyScansToThis_(s_PCloud (&p_pcGlobal)[WLR720_SCANS_PER_FIRING],
                                     st_fCloud (&p_pcLocal)[WLR720_SCANS_PER_FIRING])
{
    try
    {
        // std:: cout << "hsq copyScansToThis_: " << std::endl;
        // std::cout << p_pcGlobal[0].m_praw->header.stamp << std::endl;
        c_uiScanTimeStamp_ = p_pcGlobal[0].m_praw->header.stamp;
        for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
        {
            p_pcLocal[i].m_pc.copy(p_pcGlobal[i]);
            // 初始化标记
            memset(p_pcLocal[i].m_iCLab, 0, sizeof(p_pcLocal[i].m_iCLab));
            for (auto& lab : p_pcLocal[i].m_iSLab)
                lab = labTyp::MAY;
        }
    }
    catch (const std::exception& e)
    {
        throw e.what();
        return state::FAIL;
    }
    return state::DONE;
}

int FeatureExtract::copyFeatureToOutput_(pcFeaturePtr (&p_pcCIn)[WLR720_SCANS_PER_FIRING],
                                         pcFeaturePtr (&p_pcSIn)[WLR720_SCANS_PER_FIRING],
                                         pcFeaturePtr p_pcCorn,
                                         pcFeaturePtr p_pcSurf)
{
    // 生产方负责清空货架
    p_pcCorn->clear();
    p_pcSurf->clear();

    try
    {
        p_pcCorn->header.stamp = c_uiScanTimeStamp_;
        p_pcSurf->header.stamp = c_uiScanTimeStamp_;
        // 将内部点云转换为外部形式
        for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
        {
            p_pcCorn->insert(p_pcCorn->end(), p_pcCIn[i]->begin(), p_pcCIn[i]->end());
            // 清空已经使用过的点云
            p_pcCIn[i]->clear();
            p_pcSurf->insert(p_pcSurf->end(), p_pcSIn[i]->begin(), p_pcSIn[i]->end());
            // 清空已经使用过的点云
            p_pcSIn[i]->clear();
        }
        if (c_stCfg_.m_whlFil.m_bVerticalCornFilter)
            verticalCornerFilter_(p_pcCorn);
        if (c_stCfg_.m_whlFil.m_bUniSampleSurfFilter)
            uniformSamplingFilter_(p_pcSurf);
        else
            c_iSampleSurfSize = p_pcSurf->size();
    }
    catch (const std::exception& e)
    {
        // std::// cout << "hsq FeatureExtract::copyFeatureToOutput_:" << std::endl;
        std::cerr << e.what() << '\n';
        return state::FAIL;
    }
    return state::DONE;
}

int FeatureExtract::verticalCornerFilter_(pcFeaturePtr p_pcCorn)
{
    // 为空则直接退出
    if (p_pcCorn->empty())
    {
        return state::FAIL;
    }
    // kd树
    pcl::KdTreeFLANN2D<PFeature> c_pKdtFln2dCor_;
    // z差值
    float l_fZDiff = 0.0;
    // 搜索点
    u_int32_t l_uiLineSize = p_pcCorn->size();
    std::vector<int> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    std::vector<int> l_viSaveIndex;

    // 全部角点标记初始化
    std::vector<bool> l_viFlagIndex(l_uiLineSize, false);
    // 开始过滤角点
    c_pKdtFln2dCor_.setInputCloud(p_pcCorn);
    for (u_int32_t l_uiInd = 1; l_uiInd < l_uiLineSize; ++l_uiInd)
    {
        // 如果已经在直线上了(已经标记)。下一个
        if (l_viFlagIndex[l_uiInd] == true)
            continue;
        // 如果搜索不到临界垂向空间点。下一个
        if (c_pKdtFln2dCor_.radiusSearch(p_pcCorn->points[l_uiInd],
                                         c_stCfg_.m_whlFil.m_fCornCyldRadius,
                                         l_viPntSrhIdx,
                                         l_vfPntSrhSqDis)
            < (int)c_stCfg_.m_whlFil.m_uiCornCyldMinPoints - 1)
            continue;
        //循环检索
        for (int ind : l_viPntSrhIdx)
        {
            // 由于添加特征点是有序的，线上的首个角点来自最上或最下线号
            l_fZDiff = std::abs(p_pcCorn->points[ind].z - p_pcCorn->points[l_uiInd].z);
            if (l_fZDiff > c_stCfg_.m_whlFil.m_fCornCyldMinHeight)
                break;
        }
        // 如果通过鉴定
        if (l_fZDiff > c_stCfg_.m_whlFil.m_fCornCyldMinHeight)
        {
            for (int ind : l_viPntSrhIdx)
            {
                // 标记已经为筛选角点
                l_viFlagIndex[ind] = true;
            }
            // 添加到保存单
            l_viSaveIndex.insert(l_viSaveIndex.end(), l_viPntSrhIdx.begin(), l_viPntSrhIdx.end());
        }
    }
    // 去除重复并排序
    std::sort(l_viSaveIndex.begin(), l_viSaveIndex.end());
    l_viSaveIndex.erase(std::unique(l_viSaveIndex.begin(), l_viSaveIndex.end()),
                        l_viSaveIndex.end());
    // 拷贝
    pcl::copyPointCloud(*p_pcCorn, l_viSaveIndex, *p_pcCorn);
    return state::DONE;
}

int FeatureExtract::uniformSamplingFilter_(pcFeaturePtr p_pcSurf)
{
    // 标记距离分段
    // std::cout << "m_bUniSampleSurfFilter\n";
    u_int DistSub;
    // 点间距
    float l_fQDistance = 0.0;
    // 总点数
    u_int32_t l_uiSurfSize = p_pcSurf->size();
    // 如果点数不足，返回
    if (l_uiSurfSize < EV_NUM)
    {
        // 若不赋值则为上一帧的点数
        c_iSampleSurfSize = l_uiSurfSize;
        return state::FAIL;
    }
    // 如果需要压缩点数，使用清单
    // 保留清单
    std::vector<int> l_viSaveIndex;
    // 候选清单(根据c_stCfg_.m_whlFil.m_fSampleRange大小调整)
    std::vector<std::vector<int>> l_viUnSaveIndex;
    l_viUnSaveIndex.resize(5);
    // 候选采集顺序(根据c_stCfg_.m_whlFil.m_fSampleRange大小调整)
    std::vector<int> l_viUnSaveSupple = {2, 3, 1, 0, 4, 3, 2, 1, 4, 0};

    // 第一个采样点
    u_int32_t l_uiLastSampleInd = 0;
    // 循环标记采样点和非采样点
    for (u_int32_t l_TCnt = 0; l_TCnt < l_uiSurfSize; ++l_TCnt)
    {
        // 若为地面点则4，其余根据depth分类
        DistSub = p_pcSurf->points[l_TCnt].z < c_stCfg_.m_attach.m_fGndRange[1]
                      ? 4
                      : p_pcSurf->points[l_TCnt].v < c_stCfg_.m_whlFil.m_fSampleRange[0]
                            ? 0
                            : p_pcSurf->points[l_TCnt].v < c_stCfg_.m_whlFil.m_fSampleRange[1]
                                  ? 1
                                  : p_pcSurf->points[l_TCnt].v < c_stCfg_.m_whlFil.m_fSampleRange[2]
                                        ? 2
                                        : 3;
        // 如果实际上无法采样
        if (c_stCfg_.m_whlFil.m_fSampleScale[DistSub] < 0.009)
        {
            // 标记采样点
            l_uiLastSampleInd = l_TCnt;
        }
        else
        {
            // 如果距离上一个采样点足够远，标记一个新的采样点
            l_fQDistance =
                pow(p_pcSurf->points[l_uiLastSampleInd].x - p_pcSurf->points[l_TCnt].x, 2)
                + pow(p_pcSurf->points[l_uiLastSampleInd].y - p_pcSurf->points[l_TCnt].y, 2);
            if (l_fQDistance > c_stCfg_.m_whlFil.m_fSampleScale[DistSub])
            {
                // 标记采样点
                l_uiLastSampleInd = l_TCnt;
            }
        }
        // 储存采样点序列
        if (l_uiLastSampleInd == l_TCnt)
        {
            l_viSaveIndex.emplace_back(l_TCnt);
        }
        // 保存非采样点到各自序列中
        else
            l_viUnSaveIndex[DistSub].emplace_back(l_TCnt);
    }
    // 记录采样点数量
    c_iSampleSurfSize = l_viSaveIndex.size();
    // 对候选清单进行采样保留
    for (int i : l_viUnSaveSupple)
    {
        int l_iCntDev = 0;
        u_int32_t l_uiLastPointNum = l_viUnSaveIndex[i].size();
        // 除了采样点，还剩多少个空位
        int l_uiAllowNum = c_stCfg_.m_whlFil.m_uiMaxSurfPntNum - l_viSaveIndex.size();
        // 如果没有空位直接离开
        if (l_uiAllowNum > 0)
        {
            // 空位均匀分配给非采样点的间隔（保证>0）
            l_iCntDev = ceil(l_uiLastPointNum / static_cast<float>(l_uiAllowNum));
            for (u_int32_t l_uiCnt = 0; l_uiCnt < l_uiLastPointNum; l_uiCnt += l_iCntDev)
            {
                l_viSaveIndex.emplace_back(l_viUnSaveIndex[i][l_uiCnt]);
            }
        }
    }
    // 保存重排的点序列
    // pcl::copyCloud无法拷贝相同长度点云
    l_uiSurfSize = l_viSaveIndex.size();
    c_pcSurfTransfer_->points.resize(l_uiSurfSize);
    for (size_t i = 0; i < l_uiSurfSize; ++i)
        c_pcSurfTransfer_->points[i] = p_pcSurf->points[l_viSaveIndex[i]];
    // 返还排序后的点云
    *p_pcSurf = *c_pcSurfTransfer_;
    return state::DONE;
}

#pragma endregion

int FeatureExtract::lineProcess_(st_fCloud& p_pcL,
                                 pcFeaturePtr p_pcLC,
                                 pcFeaturePtr p_pcLS,
                                 int p_iLineId)
{
    // 创建点云分段容器
    std::vector<st_Block> l_vBlockList;

    // 分段分类点云并加入list
    // std::// cout << "hsq lineProcess_: segmenteCloud_ "  << std::endl;
    if (segmenteCloud_(p_pcL, l_vBlockList) == state::FAIL)
    {
        // std::cout << "hsq: return " << std::endl;
        return state::FAIL;
    }

    // std::cout << "hsq: classificateCloud_ " << std::endl;
    classificateCloud_(p_pcL, l_vBlockList, p_iLineId);
    // std::cout << "hsq: segmentCloudFilter_ " << std::endl;
    segmentCloudFilter_(p_pcL, l_vBlockList);
    // std::cout << "hsq: extractFeature_ " << std::endl;
    extractFeature_(p_pcL, l_vBlockList, p_pcLC, p_pcLS);

    // 记录分段所有点并储存
    // std::cout << "hsq: debugSegmentCopy_ " << std::endl;
    for (auto& l_stBlk : l_vBlockList)
    {
        debugSegmentCopy_(p_pcL, p_iLineId, l_stBlk);
    }
    // std::cout << "hsq: finished " << std::endl;
    return state::DONE;
}

#pragma region segment and classificate

int FeatureExtract::segmenteCloud_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList)
{
    u_int32_t l_uiLineSize = p_pcL.m_pc.m_praw->points.size();
    // 如果是空线则直接退出
    if (l_uiLineSize < EV_NUM)
        return state::FAIL;

    // 初始化一个起止点为1，长为1的分段
    st_Block l_stTmpBlk(1);
    l_stTmpBlk.m_iStaType = shltTye::OTHER;
    shltTye l_iShelter = shltTye::NONE;

    for (u_int32_t l_uiInd = 1; l_uiInd < l_uiLineSize; ++l_uiInd)
    {
        // 最后一个点必须分段
        l_iShelter =
            (l_uiInd == l_uiLineSize - 1) ? shltTye::OTHER : isBoundaryPoint_(p_pcL, l_uiInd);
        // 如果当前点是分段点
        if (l_iShelter != shltTye::NONE)
        {
            l_stTmpBlk.m_uiEnd = l_uiInd - 1;
            l_stTmpBlk.m_uiLen = l_stTmpBlk.m_uiEnd - l_stTmpBlk.m_uiSta + 1;

            // 至少要有BLK_MINNUM个点
            if (l_stTmpBlk.m_uiLen >= c_stCfg_.m_class.m_uiMinBlkPoints)
            {
                // 如果当前ind为拖尾前景点，证明上个点为拖尾背景点
                if (l_iShelter == shltTye::BACK)
                    // 判断这个分段点作为前景点的有效性，如果大入射角则不是
                    l_stTmpBlk.m_iEndType = FRNT;
                else if (l_iShelter == shltTye::FRNT)
                    l_stTmpBlk.m_iEndType = shltTye::BACK;
                else
                    l_stTmpBlk.m_iEndType = l_iShelter;

                p_vBlockList.emplace_back(l_stTmpBlk);
            }

            // 对下一个分段来说，起点是已知的，类型也是已知的
            l_stTmpBlk.m_uiSta = l_uiInd;
            l_stTmpBlk.m_iStaType = l_iShelter;
        }
    }

    return state::DONE;
}

FeatureExtract::shltTye FeatureExtract::isBoundaryPoint_(st_fCloud& p_pcL, u_int32_t& p_uiInd)
{
    // 中间差一个点允许继续
    // TODO:偶尔有两落差极大的分段间出现强度极低点，跳过这个点有利于找到遮挡前沿
    constexpr float l_fMaxTimeDiff = 1.5 * WLR720_RINGPOINT_TDURATION;
    // 比较两个相邻点的时间差【us】，如果时间差大于1.5倍意味着缺失点
    if (p_pcL.m_pc.m_padd->at(p_uiInd).time - p_pcL.m_pc.m_padd->at(p_uiInd - 1).time
        > l_fMaxTimeDiff)
        return shltTye::EMPTY;

    // 如果两个点之间的距离差很小，则可能是测距误差而不是分界点
    constexpr float l_fMinDistFrntBack = 0.1;
    // TODO:如果两点间距离非常近，不论点深度，那么是测量误差而不是分界点
    // 由于近距离测量误差过大，直线可能会被分为多个段落
    // 如果真的是两个物体间夹缝？所以不能直接判为连续
    float l_fPosDiff = calcDistP2P_point(p_pcL.m_pc.m_praw->points[p_uiInd],
                                         p_pcL.m_pc.m_praw->points[p_uiInd - 1]);
    // 当前点深度-上一个点的深度
    float l_fDiff =
        p_pcL.m_pc.m_padd->at(p_uiInd).xydist - p_pcL.m_pc.m_padd->at(p_uiInd - 1).xydist;
    // 若当前点更远，且距离差超过阈值，当前点是拖尾背景点
    if (l_fDiff > 0
        && l_fPosDiff
               > c_stCfg_.m_class.m_fBondaryDiffMulti * p_pcL.m_pc.m_padd->at(p_uiInd - 1).xydist)
    {
        // 如果差异很小
        if (l_fPosDiff < l_fMinDistFrntBack)
            return shltTye::OTHER;
        return shltTye::BACK;
    }
    // 若当前点更近，且距离差超过阈值，当前点是拖尾前景点
    if (l_fDiff < 0
        && l_fPosDiff
               > c_stCfg_.m_class.m_fBondaryDiffMulti * p_pcL.m_pc.m_padd->at(p_uiInd).xydist)
    {
        if (l_fPosDiff < l_fMinDistFrntBack)
            return shltTye::OTHER;
        return shltTye::FRNT;
    }
    // 其他情况，不是拖尾点
    return shltTye::NONE;
}

int FeatureExtract::classificateCloud_(st_fCloud& p_pcL,
                                       std::vector<st_Block>& p_vBlockList,
                                       int p_iLineId)
{
    // 简单地面检测，如果设为0则不检测地面
    constexpr int l_iMidLineId = 7;

    // 向下扫描线分析地面点段
    if (c_stCfg_.m_use.m_bUseGND && p_iLineId < l_iMidLineId)
    {
        std::vector<st_Block> l_vTmpGND_BLKlkList;
        // 分析点段
        for (auto l_stTmpBlk = p_vBlockList.begin(); l_stTmpBlk != p_vBlockList.end(); ++l_stTmpBlk)
        {
            // 如果分段包含地面，则拆散分段
            if (isBlockGnd_(p_pcL, *l_stTmpBlk, l_vTmpGND_BLKlkList) == state::DONE)
            {
                // 移除当前块,指向下一个块
                l_stTmpBlk = p_vBlockList.erase(l_stTmpBlk);
                // 在下一个块之前插入新的分块，返回指向新添加元素的迭代器
                l_stTmpBlk = p_vBlockList.insert(
                    l_stTmpBlk, l_vTmpGND_BLKlkList.begin(), l_vTmpGND_BLKlkList.end());
                l_vTmpGND_BLKlkList.clear();
            }
        }
    }
    // 分析点段
    for (auto l_stTmpBlk = p_vBlockList.begin(); l_stTmpBlk != p_vBlockList.end();)
    {
        if (classificateBlock_(p_pcL, *l_stTmpBlk) == state::DONE)
        {
            l_stTmpBlk++;
        }
        else
        {
            if (c_stCfg_.m_use.m_bUseUNK)
            {
                l_stTmpBlk->m_iType = blkTyp::UNK_BLK;
                l_stTmpBlk++;
            }
            else
            {
                l_stTmpBlk = p_vBlockList.erase(l_stTmpBlk);
            }
        }
    }
    return state::DONE;
}

bool FeatureExtract::isBlockGnd_(st_fCloud& p_pcL,
                                 st_Block& p_stBlk,
                                 std::vector<st_Block>& p_vNewBlkList)
{
    // 地面空间分段点
    constexpr float disT = 10.0f;
    constexpr float disMax = 40.0f;
    // 地面空间[a*b + c = Z]
    // constexpr float a[4] = {0, 0, 0.008, -0.008};
    // float c[4] = {c_stCfg_.m_attach.m_fGndRange[1],
    //               c_stCfg_.m_attach.m_fGndRange[0],
    //               c_stCfg_.m_attach.m_fGndRange[1] - a[3] * disT,
    //               c_stCfg_.m_attach.m_fGndRange[0] - a[3] * disT};

    // 当前区间值
    float zMax = c_stCfg_.m_attach.m_fGndRange[1];
    float zMin = c_stCfg_.m_attach.m_fGndRange[0];
    // 上次计算区间值时的xy距离
    float lastDist = -1.0f;

    // 在这些情况下不进行地面抽取
    if (p_stBlk.m_uiLen < c_stCfg_.m_class.m_uiMinBlkPoints)
        return state::FAIL;
    // 如果分段没有足够点在地面上,认为错误
    u_int32_t l_uiPointAnGndNum = 0;
    // 如果分类结果中没有地面分段，认为函数失败
    u_int32_t l_uiGndBlkNum = 0;
    // 初始化一个起止点为1，长为1的分段
    st_Block l_stTmpBlk(1);
    l_stTmpBlk.m_iStaType = shltTye::OTHER;
    l_stTmpBlk.m_iEndType = shltTye::OTHER;
    l_stTmpBlk.m_uiSta = p_stBlk.m_uiSta;
    l_stTmpBlk.m_uiEnd = p_stBlk.m_uiSta;
    // 分段判定
    bool lastIsGnd = false;
    bool thisIsGnd = false;
    // 一次跳跃多个点加速
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + 1; l_uiInd <= p_stBlk.m_uiEnd; l_uiInd += 3)
    {
        // 确定当前距离下的z区间,当距离变化较远时重新计算区间
        // if ( std::abs(p_pcL.m_pc.m_padd->at(l_uiInd).xydist - lastDist) > 0.5f)
        // {
        // zMax = c_stCfg_.m_attach.m_fGndRange[1];
        // zMin = c_stCfg_.m_attach.m_fGndRange[0];
        // if (p_pcL.m_pc.m_padd->at(l_uiInd).xydist < disT)
        // {
        // zMax = a[0] * p_pcL.m_pc.m_padd->at(l_uiInd).xydist + c[0];
        // zMin = a[1] * p_pcL.m_pc.m_padd->at(l_uiInd).xydist + c[1];
        // }
        // else if (p_pcL.m_pc.m_padd->at(l_uiInd).xydist < disMax)
        // {
        // zMax = a[2] * p_pcL.m_pc.m_padd->at(l_uiInd).xydist + c[2];
        // zMin = a[3] * p_pcL.m_pc.m_padd->at(l_uiInd).xydist + c[3];
        // }
        // else
        // {
        // 不提取地面
        // zMax = -1.0f;
        // zMin = +1.0f;
        // }
        // lastDist = p_pcL.m_pc.m_padd->at(l_uiInd).xydist;
        // }

        // 当前点是否属于地面
        thisIsGnd = (p_pcL.m_pc.m_praw->points[l_uiInd].z < zMax
                     && p_pcL.m_pc.m_praw->points[l_uiInd].z > zMin)
                        ? true
                        : false;
        // 连续地面点计数
        if (thisIsGnd)
            l_uiPointAnGndNum++;
        // 如果当前点是分段点
        if (thisIsGnd != lastIsGnd || l_uiInd + 3 > p_stBlk.m_uiEnd)
        {
            l_stTmpBlk.m_uiEnd = l_uiInd - 1;
            l_stTmpBlk.m_uiLen = l_stTmpBlk.m_uiEnd - l_stTmpBlk.m_uiSta + 1;
            // 如果连续地面计数至少2，认为是地面
            l_stTmpBlk.m_iType = (l_uiPointAnGndNum >= 2) ? blkTyp::GND_BLK : blkTyp::UNK_BLK;
            // 重置连续计数
            l_uiPointAnGndNum = 0;
            if (l_stTmpBlk.m_uiLen >= c_stCfg_.m_class.m_uiMinBlkPoints)
            {
                // 统计地面分类数
                if (l_stTmpBlk.m_iType == blkTyp::GND_BLK)
                    l_uiGndBlkNum++;
                p_vNewBlkList.emplace_back(l_stTmpBlk);
            }
            l_stTmpBlk.m_uiSta = l_uiInd;
        }

        // 当前状态转移
        lastIsGnd = thisIsGnd;
    }

    // 如果分段失败，或者没有地面分段出现，认为函数失败
    if (p_vNewBlkList.empty() || l_uiGndBlkNum < 1)
        return state::FAIL;
    return state::DONE;
}

int FeatureExtract::classificateBlock_(st_fCloud& p_pcL, st_Block& p_stBlk)
{
    // 是地面分段则直接返回
    if (p_stBlk.m_iType == blkTyp::GND_BLK)
    {
        return state::DONE;
    }
    else
    {
        p_stBlk.m_iType = blkTyp::UNK_BLK;
    }

    // 是否小物体 是否稀疏物体
    if (isBlockSmallOrSpare_(p_pcL, p_stBlk))
    {
        return state::DONE;
    }
    // 是否大物体
    else if (p_stBlk.m_uiLen >= EV_NUM)
    {
        // 是否平滑物体，杂乱物体不提取特征点
        if (isBlockSmooth_(p_pcL, p_stBlk))
        {
            // 是否凹圆弧物体
            if (c_stCfg_.m_use.m_bUseRND && isBlockArc_(p_pcL, p_stBlk))
                p_stBlk.m_iType = blkTyp::RND_BLK;
            // 没有通过上述判断认为是一般物体
            else
                p_stBlk.m_iType = blkTyp::NML_BLK;
            return state::DONE;
        }
        // 一些奇怪的点云
        else
        {
            p_stBlk.m_iType = blkTyp::UNK_BLK;
            return state::FAIL;
        }
    }
    return state::FAIL;
}

bool FeatureExtract::isBlockSmooth_(st_fCloud& p_pcL, st_Block& p_stBlk)
{
    // 如果开启强度变化检查则判断
    if (c_stCfg_.m_class.m_del.m_bIntensitySmoothCheck && !ifIntensitySmooth_(p_pcL, p_stBlk))
        return false;
    // 如果开启角度变化检查则判断
    if (c_stCfg_.m_class.m_del.m_bLineSmoothCheck && !ifLineSmooth_(p_pcL, p_stBlk))
        return false;
    else
        return true;
}

bool FeatureExtract::ifIntensitySmooth_(st_fCloud& p_pcL, st_Block& p_stBlk)
{
    // 分析至少需要3个点才能启动,至少4个点才有意义
    if (p_stBlk.m_uiLen < 3)
        return true;

    u_int32_t l_uiMaxOutlierPointNum =
        std::max((p_stBlk.m_uiLen - 2) * c_stCfg_.m_class.m_del.m_fIntenDiffPercent, 1.0f);
    u_int32_t l_uiIntensityDiff = 0;
    u_int32_t l_uiOutlierPointNum = 0;
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + 1; l_uiInd < p_stBlk.m_uiEnd; l_uiInd++)
    {
        l_uiIntensityDiff = ceil(std::fabs(p_pcL.m_pc.m_padd->at(l_uiInd + 1).intensity
                                           + p_pcL.m_pc.m_padd->at(l_uiInd - 1).intensity
                                           - 2 * p_pcL.m_pc.m_padd->at(l_uiInd).intensity));

        if (l_uiIntensityDiff > c_stCfg_.m_class.m_del.m_uiMaxIntensityDiff)
            l_uiOutlierPointNum++;
        if (l_uiOutlierPointNum > l_uiMaxOutlierPointNum)
            return false;
    }
    return true;
}

bool FeatureExtract::ifLineSmooth_(st_fCloud& p_pcL, st_Block& p_stBlk)
{
    // 分析至少需要3个点才能启动,至少4个点才有意义
    if (p_stBlk.m_uiLen < 3)
        return true;

    // 抽检率
    const u_int32_t l_uiSampleRate = 3;
    u_int32_t l_uiMaxOutlierPointNum =
        std::max(static_cast<u_int32_t>((p_stBlk.m_uiLen - 2) / l_uiSampleRate
                                        * c_stCfg_.m_class.m_del.m_fAngDiffPercent),
                 1u);
    u_int32_t l_uiOutlierPointNum = 0;

    // Eigen库比未化简的运算快20%左右。
    Eigen::Vector2f PA, PB;
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + 1; l_uiInd < p_stBlk.m_uiEnd;
         l_uiInd += l_uiSampleRate)
    {
        PA.x() = p_pcL.m_pc.m_praw->points[l_uiInd - 1].x - p_pcL.m_pc.m_praw->points[l_uiInd].x;
        PA.y() = p_pcL.m_pc.m_praw->points[l_uiInd - 1].y - p_pcL.m_pc.m_praw->points[l_uiInd].y;
        PB.x() = p_pcL.m_pc.m_praw->points[l_uiInd + 1].x - p_pcL.m_pc.m_praw->points[l_uiInd].x;
        PB.y() = p_pcL.m_pc.m_praw->points[l_uiInd + 1].y - p_pcL.m_pc.m_praw->points[l_uiInd].y;

        if (getAngleTwoVectors(PA, PB) > c_stCfg_.m_class.m_del.m_fMaxLineAngDiff)
            l_uiOutlierPointNum++;
        if (l_uiOutlierPointNum > l_uiMaxOutlierPointNum)
            return false;
    }
    return true;
}

bool FeatureExtract::isBlockSmallOrSpare_(st_fCloud& p_pcL, st_Block& p_stBlk)
{
    // 如果一个物体的宽度小于 m_fSmallScale[1],
    // 即使点数>EV_NUM存在中间角也会因为测距误差而导致找到的角点位置不断变化
    // 因此 只要是宽度小于 m_fSmallScale[1] 都认为是小物体

    //  方法1:投影宽度
    // 小物体的可能最大张角约3度
    // constexpr float l_fMaxAng = 0.06;
    // // 投射角度
    // float l_fAng = std::abs(p_pcL.m_pc.m_padd->at(p_stBlk.m_uiEnd).ang -
    // p_pcL.m_pc.m_padd->at(p_stBlk.m_uiSta).ang) * D2R; if (l_fAng > l_fMaxAng)
    //     return false;
    // // 投影距离
    // float l_fDepth = std::min(p_pcL.m_pc.m_padd->at(p_stBlk.m_uiEnd).depth,
    // p_pcL.m_pc.m_padd->at(p_stBlk.m_uiSta).depth);
    // // 投影宽度
    // float l_width = 2* l_fDepth * std::abs(std::tan(l_fAng * 0.5));

    // 方法2：使用实际宽度
    float l_width = calcXYDistP2P_point(p_pcL.m_pc.m_praw->points[p_stBlk.m_uiSta],
                                        p_pcL.m_pc.m_praw->points[p_stBlk.m_uiEnd]);

    if (l_width < c_stCfg_.m_class.m_fSmallScale[1] && l_width > c_stCfg_.m_class.m_fSmallScale[0])
    {
        if (p_stBlk.m_iStaType == shltTye::FRNT && p_stBlk.m_iEndType == shltTye::FRNT)
            p_stBlk.m_iType = blkTyp::FSM_BLK;
        // BSM 和 SPA分段需要额外的分类器（下次更新中）
        else
            p_stBlk.m_iType = blkTyp::BSM_BLK;
        return true;
    }
    if (p_stBlk.m_uiLen < EV_NUM && l_width >= c_stCfg_.m_class.m_fSmallScale[1])
    {
        p_stBlk.m_iType = blkTyp::SPA_BLK;
        return true;
    }
    // N种点会返回false
    // 1. l_width <= m_fSmallScale[0]的 各种点数（过于小的分段无法利用，抛弃）
    // 2. l_width > m_fSmallScale[1] && m_uiLen >= EV_NUM （进入大物体判定条件）
    return false;
}

bool FeatureExtract::isBlockArc_(st_fCloud& p_pcL, st_Block& p_stBlk)
{
    constexpr float l_fHarfRadio = 0.3;
    // 点段至少有7个点才可以判断是否圆弧（排除边缘2点还有5点组成2线）
    if (p_stBlk.m_uiLen < 7)
        return false;
    //如果点段的中间点距离比两侧远，他不可能是凹形^^__^^这种
    if (p_pcL.m_pc.m_padd->at(p_stBlk.m_uiSta + 1).depth
            < p_pcL.m_pc.m_padd->at(p_stBlk.m_uiSta + p_stBlk.m_uiLen / 2).depth
        || p_pcL.m_pc.m_padd->at(p_stBlk.m_uiEnd - 1).depth
               < p_pcL.m_pc.m_padd->at(p_stBlk.m_uiSta + p_stBlk.m_uiLen / 2).depth)
        return false;

    PRaw *l_pP1, *l_pP2, *l_pP3;
    PRaw l_center1, l_center2;

    l_pP1 = &p_pcL.m_pc.m_praw->points[p_stBlk.m_uiSta + 1];
    l_pP2 = &p_pcL.m_pc.m_praw->points[p_stBlk.m_uiSta + p_stBlk.m_uiLen / 4];
    l_pP3 = &p_pcL.m_pc.m_praw->points[p_stBlk.m_uiSta + p_stBlk.m_uiLen / 3];
    // 防止P1P2P3重叠
    l_pP2 = (l_pP2 == l_pP1) ? l_pP1 + 1 : l_pP2;
    l_pP3 = (l_pP3 == l_pP2) ? l_pP2 + 1 : l_pP3;
    float l_fR1 = calcCircle_(*l_pP1, *l_pP2, *l_pP3, l_center1);
    if (l_fR1 > c_stCfg_.m_class.m_fMaxRadiusRnd)
        return false;
    l_pP1 = &p_pcL.m_pc.m_praw->points[p_stBlk.m_uiEnd - 1];
    l_pP2 = &p_pcL.m_pc.m_praw->points[p_stBlk.m_uiEnd - p_stBlk.m_uiLen / 4];
    l_pP3 = &p_pcL.m_pc.m_praw->points[p_stBlk.m_uiEnd - p_stBlk.m_uiLen / 3];
    // 防止P1P2P3重叠
    l_pP2 = (l_pP2 == l_pP1) ? l_pP1 - 1 : l_pP2;
    l_pP3 = (l_pP3 == l_pP2) ? l_pP2 - 1 : l_pP3;
    float l_fR2 = calcCircle_(*l_pP1, *l_pP2, *l_pP3, l_center2);
    if (l_fR1 > c_stCfg_.m_class.m_fMaxRadiusRnd)
        return false;
    // 如果半径合理（排除超大尺寸物体）,半径差和圆心距离合理(排除直角)认为是凹圆弧形
    if (std::abs(l_fR1 - l_fR2) < l_fHarfRadio && std::abs(l_center1.x - l_center2.x) < l_fHarfRadio
        && std::abs(l_center1.y - l_center2.y) < l_fHarfRadio)
        return true;
    else
        return false;
}

template <typename T, typename T2>
float FeatureExtract::calcCircle_(T& p_pA, T& p_pB, T& p_pC, T2& p_pCenter)
{
    float l_fA = (p_pA.x * p_pA.x - p_pB.x * p_pB.x + p_pA.y * p_pA.y - p_pB.y * p_pB.y);
    float l_fB = (p_pA.x * p_pA.x - p_pC.x * p_pC.x + p_pA.y * p_pA.y - p_pC.y * p_pC.y);
    float l_fACX = (p_pA.x - p_pC.x);
    float l_fACY = (p_pA.y - p_pC.y);
    float l_fABY = (p_pA.y - p_pB.y);
    float l_fABX = (p_pA.x - p_pB.x);

    p_pCenter.x = (l_fA * l_fACY - l_fB * l_fABY) / (2 * l_fACY * l_fABX - 2 * l_fABY * l_fACX);
    p_pCenter.y = (l_fA * l_fACX - l_fB * l_fABX) / (2 * l_fABY * l_fACX - 2 * l_fACY * l_fABX);
    return sqrt((p_pCenter.x - p_pA.x) * (p_pCenter.x - p_pA.x)
                + (p_pCenter.y - p_pA.y) * (p_pCenter.y - p_pA.y));
}

#pragma endregion

#pragma region Filter

int FeatureExtract::segmentCloudFilter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList)
{
    sigmentCloudDownSize_(p_pcL, p_vBlockList);
    // SPA分析，提取合理的部分
    if (c_stCfg_.m_use.m_bUseSPA)
    {
        Spare_filter_(p_pcL, p_vBlockList);
    }
    // 对单一分段（参考旁边的点）进行分析
    if (c_stCfg_.m_use.m_bUseFSM)
    {
        FrontSmall_filter_(p_pcL, p_vBlockList);
    }
    // 对单一分段（参考旁边的点）进行分析
    if (c_stCfg_.m_use.m_bUseSHT)
        FrontShelter_filter_(p_pcL, p_vBlockList);
    return state::DONE;
}

int FeatureExtract::sigmentCloudDownSize_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList)
{
    //分段比小物体稍大？
    // float l_fLeafSize = (0.2 > c_stCfg_.m_class.m_fSmallScale[1])?
    // 0.2:(c_stCfg_.m_class.m_fSmallScale[1]+0.02);
    constexpr float l_fLeafSize = 0.2;

    // 如果点数不足以计算，返回
    if (p_pcL.m_pc.m_praw->points.size() < EV_NUM)
        return state::FAIL;

    // 采样点云
    pcDsPtr l_pcIDs(new pcDs());
    ////////////////////
    // 点云分段
    std::vector<std::array<u_int32_t, 3>> first_and_last_indices_vector;
    std::array<u_int32_t, 3> l_sub;

    // 临时距离
    float l_fTempDist = 0;
    // 采样尺度
    float l_fDisSq = l_fLeafSize * l_fLeafSize;
    int l_uiBlkNum = p_vBlockList.size();
    for (int l_uiInd = 0; l_uiInd < l_uiBlkNum; ++l_uiInd)
    {
        // 一般物体
        if (p_vBlockList[l_uiInd].m_iType == blkTyp::NML_BLK
            || p_vBlockList[l_uiInd].m_iType == blkTyp::GND_BLK
            || p_vBlockList[l_uiInd].m_iType == blkTyp::RND_BLK)  //
        {
            // second点序号
            u_int32_t ind_sta = p_vBlockList[l_uiInd].m_uiSta;
            u_int32_t ind_end = ind_sta + 1;

            for (; ind_end < p_vBlockList[l_uiInd].m_uiEnd + 1; ++ind_end)
            {
                l_fTempDist =
                    pow(p_pcL.m_pc.m_praw->points[ind_sta].x - p_pcL.m_pc.m_praw->points[ind_end].x,
                        2)
                    + pow(p_pcL.m_pc.m_praw->points[ind_sta].y
                              - p_pcL.m_pc.m_praw->points[ind_end].y,
                          2);
                // 如果在同一个分段内，继续移动到下一个
                // 反之生成一个采样点
                if (l_fTempDist > l_fDisSq)
                {
                    l_sub[0] = l_uiInd;
                    l_sub[1] = ind_sta;
                    l_sub[2] = ind_end - 1;
                    first_and_last_indices_vector.emplace_back(l_sub);
                    ind_sta = ind_end;
                }
                // ind_end-1可能是第n-2点
                if (ind_end == p_vBlockList[l_uiInd].m_uiEnd)
                {
                    // 包含两种情况：sta = end（独立分段）， sta != end（正常分段）
                    l_sub[0] = l_uiInd;
                    l_sub[1] = ind_sta;
                    l_sub[2] = ind_end;
                    // 如果点距离上一个分段终点不远，加入上个分段
                    if (l_fTempDist < 0.5 * l_fLeafSize)
                    {
                        // *异常：如果首个分段还没有出现，添加首个分段
                        if (first_and_last_indices_vector.empty())
                            first_and_last_indices_vector.emplace_back(l_sub);
                        else
                            (first_and_last_indices_vector.back())[2] = ind_end;
                    }
                    else
                    {
                        first_and_last_indices_vector.emplace_back(l_sub);
                    }
                }
            }
        }
        // 小型物体 (排除完全异常物体)
        else if (p_vBlockList[l_uiInd].m_iType != blkTyp::UNK_BLK)
        {
            l_sub[0] = l_uiInd;
            l_sub[1] = p_vBlockList[l_uiInd].m_uiSta;
            l_sub[2] = p_vBlockList[l_uiInd].m_uiEnd;
            first_and_last_indices_vector.emplace_back(l_sub);
        }
    }
    l_pcIDs->points.reserve(first_and_last_indices_vector.size());
    l_pcIDs->points.resize(first_and_last_indices_vector.size());

    u_int32_t index = 0;
    u_int32_t first_index, last_index;

    for (const auto& cp : first_and_last_indices_vector)
    {
        first_index = cp[1];
        last_index = cp[2] + 1;

        Eigen::Vector4f centroid(Eigen::Vector4f::Zero());
        for (u_int32_t li = first_index; li < last_index; ++li)
            centroid += p_pcL.m_pc.m_praw->points[li].getVector4fMap();

        centroid /= static_cast<float>(last_index - first_index);
        l_pcIDs->points[index].getVector4fMap() = centroid;

        ++index;
    }
    l_pcIDs->width = static_cast<u_int32_t>(l_pcIDs->points.size());
    ////////////////////

    // 如果点数不足以计算，返回
    if (l_pcIDs->points.size() < 3)
        return state::FAIL;
    //  采样点云过滤
    downSizeCloudFilter_(p_pcL, p_vBlockList, l_pcIDs, first_and_last_indices_vector);
    // debug 拷贝点云
    if (c_bDebug_)
    {
        *c_pcDsCloud_ += *l_pcIDs;
    }

    return state::DONE;
}

int FeatureExtract::downSizeCloudFilter_(st_fCloud& p_pcL,
                                         std::vector<st_Block>& p_vBlockList,
                                         pcDsPtr p_dsC,
                                         std::vector<std::array<u_int32_t, 3>>& p_vSub)
{
    // 角点判定曲率阈值，专指下采样使用
    const float l_fMinCornEvalue = cos(155.0 * D2R);
    // 最大连续高曲率点数，不可小于3
    constexpr u_int l_uiMaxContinueCorn = 4;
    // 连续中断数(-1)
    constexpr u_int l_uiAcptBreakNum = 1;
    // 储存焦点区域(采样点首尾)
    std::vector<u_int32_t> l_vDsCornPointBuff;
    // 计算特征值
    u_int32_t l_uiMax = p_dsC->points.size();
    u_int32_t l_uiLeft = 0;
    u_int32_t l_uiRigh = 0;
    Eigen::Vector2f PA, PB;
    // 没有统计到的两个分段置零
    p_dsC->points[0].intensity = 0;
    p_dsC->points[p_dsC->width - 1].intensity = 0;

    for (u_int32_t l_uiInd = 1; l_uiInd < l_uiMax - 2; ++l_uiInd)
    {
        l_uiLeft = l_uiInd - 1;
        l_uiRigh = l_uiInd + 1;
        //使用每个点的前后三点计算曲率
        PA.x() = p_dsC->points[l_uiLeft].x - p_dsC->points[l_uiInd].x;
        PA.y() = p_dsC->points[l_uiLeft].y - p_dsC->points[l_uiInd].y;
        PB.x() = p_dsC->points[l_uiRigh].x - p_dsC->points[l_uiInd].x;
        PB.y() = p_dsC->points[l_uiRigh].y - p_dsC->points[l_uiInd].y;
        p_dsC->points[l_uiInd].intensity = getAngleTwoVectors(PA, PB);
        if (p_dsC->points[l_uiInd].intensity > l_fMinCornEvalue)
        {
            l_vDsCornPointBuff.emplace_back(l_uiInd);
        }
    }

    // 根据连续性分段
    u_int32_t first_index, last_index;
    std::vector<u_int32_t> l_vDsSub;
    if (!l_vDsCornPointBuff.empty())
        l_vDsSub.emplace_back(l_vDsCornPointBuff[0]);
    else
        return state::DONE;
    for (u_int i = 1; i < l_vDsCornPointBuff.size(); i++)
    {
        // 如果连续(间隔l_uiAcptBreakNum-1也算连续)
        if (l_vDsCornPointBuff[i] <= l_vDsSub.back() + l_uiAcptBreakNum)
        {
            l_vDsSub.emplace_back(l_vDsCornPointBuff[i]);
        }
        else
        {
            // 如果连续分段少，则直接通过
            // 如果连续高曲率分段但不是A或--形状，也可以通过
            if (l_vDsSub.size() < l_uiMaxContinueCorn || !isPointsAShape_(p_dsC, l_vDsSub))
            {
                for (auto l_Pt : l_vDsSub)
                {
                    first_index = p_vSub[l_Pt][1];
                    last_index = p_vSub[l_Pt][2] + 1;
                    // 获取范围内的原始点
                    for (u_int32_t k = first_index; k < last_index; ++k)
                    {
                        p_pcL.m_iCLab[k] = labTyp::MAY;
                        p_pcL.m_iSLab[k] = labTyp::NOT;
                    }
                }
            }
            l_vDsSub.clear();
            l_vDsSub.emplace_back(l_vDsCornPointBuff[i]);
        }
    }
    return state::DONE;
}

bool FeatureExtract::isPointsAShape_(pcDsPtr p_dsC, std::vector<u_int32_t>& p_vDsSub)
{
    constexpr float l_fMaxDiffAsFrontTwo = 0.36;
    u_int32_t l_uiPtMax = p_vDsSub.back() - 1;
    u_int32_t l_Pt = p_vDsSub[1];
    float l_fSqRT, l_fSqRL, l_fSqLT, minDist;
    l_fSqRT = calcXYDistP2P_point(p_dsC->points[l_Pt - 1], p_dsC->points[l_Pt]);
    for (l_Pt = p_vDsSub[1]; l_Pt < l_uiPtMax; ++l_Pt)
    {
        // 左移
        l_fSqLT = l_fSqRT;
        l_fSqRL = calcXYDistP2P_point(p_dsC->points[l_Pt + 1], p_dsC->points[l_Pt - 1]);
        l_fSqRT = calcXYDistP2P_point(p_dsC->points[l_Pt + 1], p_dsC->points[l_Pt]);
        // 只要有聚集情况就算是异常(三点中任意两点的间隔过小)
        minDist = std::min(l_fSqRL, std::min(l_fSqRT, l_fSqLT));
        if (minDist < l_fMaxDiffAsFrontTwo)
            return true;
    }
    return false;
}

int FeatureExtract::FrontSmall_filter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList)
{
    int l_uiBlkNum = p_vBlockList.size();
    int l_uiLInd, l_uiLLInd;
    int l_uiNInd, l_uiNNInd;
    int midNum = 0;
    bool isGoodBlk = true;
    // 循环每一个块
    for (int l_uiInd = 0; l_uiInd < l_uiBlkNum; ++l_uiInd)
    {
        // 出现前景小物体
        if (p_vBlockList[l_uiInd].m_iType == blkTyp::FSM_BLK
            && p_pcL.m_iCLab[p_vBlockList[l_uiInd].m_uiSta] != labTyp::NOT)
        {
            isGoodBlk = true;
            // 计算需要使用的点序号,若防止出界
            l_uiLInd = l_uiInd - 1;
            l_uiLLInd = (l_uiBlkNum + l_uiLInd - 1) % l_uiBlkNum;
            l_uiNInd = l_uiInd + 1;
            l_uiNNInd = (l_uiBlkNum + l_uiNInd + 1) % l_uiBlkNum;
            midNum = 0.5 * (p_vBlockList[l_uiInd].m_uiSta + p_vBlockList[l_uiInd].m_uiEnd);
            // 开始判定
            // 如果分段属于点云开头或者末尾，可能由于速度扭曲点云造成角点假象
            if (l_uiLInd < 0 || l_uiNInd > l_uiBlkNum - 1)
            {
                isGoodBlk = false;
            }
            // 自身基本判断
            else if (!isCornDepthValid_(p_pcL.m_pc.m_padd->at(midNum).depth)
                     || !isCornPosValid_(p_pcL.m_pc.m_praw->points[midNum]))
            {
                isGoodBlk = false;
            }
            // 距离旁边的4个物体足够远
            else if (isLessWidthBetween2P_(
                         p_pcL, p_vBlockList[l_uiInd].m_uiSta, p_vBlockList[l_uiLInd].m_uiEnd, 0.5)
                     || isLessWidthBetween2P_(p_pcL,
                                              p_vBlockList[l_uiInd].m_uiSta,
                                              p_vBlockList[l_uiLLInd].m_uiEnd,
                                              0.5)
                     || isLessWidthBetween2P_(p_pcL,
                                              p_vBlockList[l_uiInd].m_uiEnd,
                                              p_vBlockList[l_uiNInd].m_uiSta,
                                              0.5)
                     || isLessWidthBetween2P_(p_pcL,
                                              p_vBlockList[l_uiInd].m_uiEnd,
                                              p_vBlockList[l_uiNNInd].m_uiSta,
                                              0.5))
            {
                isGoodBlk = false;
            }
            // 如果这个物体和背景处于同一直线上，认为是共线物体
            else if (isPointsAnLine_(p_pcL,
                                     p_vBlockList[l_uiInd].m_uiSta,
                                     p_vBlockList[l_uiLInd].m_uiEnd,
                                     p_vBlockList[l_uiLInd].m_uiSta)
                     || isPointsAnLine_(p_pcL,
                                        p_vBlockList[l_uiInd].m_uiSta,
                                        p_vBlockList[l_uiLLInd].m_uiEnd,
                                        p_vBlockList[l_uiLLInd].m_uiSta)
                     || isPointsAnLine_(p_pcL,
                                        p_vBlockList[l_uiInd].m_uiEnd,
                                        p_vBlockList[l_uiNInd].m_uiSta,
                                        p_vBlockList[l_uiNInd].m_uiEnd)
                     || isPointsAnLine_(p_pcL,
                                        p_vBlockList[l_uiInd].m_uiEnd,
                                        p_vBlockList[l_uiNNInd].m_uiSta,
                                        p_vBlockList[l_uiNNInd].m_uiSta))
            {
                isGoodBlk = false;
            }

            // 标记
            if (!isGoodBlk)
            {
                p_pcL.m_iCLab[p_vBlockList[l_uiInd].m_uiSta] = labTyp::NOT;
            }
        }
    }

    return state::DONE;
}

int FeatureExtract::Spare_filter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList)
{
    int l_uiBlkNum = p_vBlockList.size();
    int midNum = 0;
    bool isGoodBlk = true;
    for (int l_uiInd = 0; l_uiInd < l_uiBlkNum; ++l_uiInd)
    {
        // 出现SPA物体
        if (p_vBlockList[l_uiInd].m_iType == blkTyp::SPA_BLK)
        {
            isGoodBlk = true;
            midNum = 0.5 * (p_vBlockList[l_uiInd].m_uiSta + p_vBlockList[l_uiInd].m_uiEnd);
            // 如果稀疏物体并非在一条直线上，转为BSM类
            if (!isPointsAnLine_(
                    p_pcL, p_vBlockList[l_uiInd].m_uiSta, midNum, p_vBlockList[l_uiInd].m_uiEnd))
                isGoodBlk = false;

            // 标记
            if (!isGoodBlk)
            {
                p_vBlockList[l_uiInd].m_iType = blkTyp::BSM_BLK;
            }
        }
    }
    return state::DONE;
}

int FeatureExtract::FrontShelter_filter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList)
{
    int l_uiBlkNum = p_vBlockList.size();
    int l_uiLInd, l_uiLLInd;
    int l_uiNInd, l_uiNNInd;
    bool isGoodSht = true;
    for (int l_uiInd = 0; l_uiInd < l_uiBlkNum; ++l_uiInd)
    {
        // 出现大物体
        if (p_vBlockList[l_uiInd].m_iType == blkTyp::NML_BLK)
        {
            // 计算需要使用的点序号,若防止出界
            l_uiLInd = l_uiInd - 1;
            l_uiLLInd = (l_uiBlkNum + l_uiLInd - 1) % l_uiBlkNum;
            l_uiNInd = l_uiInd + 1;
            l_uiNNInd = (l_uiBlkNum + l_uiNInd + 1) % l_uiBlkNum;
            // 对首点边缘进行分析
            if (p_vBlockList[l_uiInd].m_iStaType == shltTye::FRNT
                && p_pcL.m_iCLab[p_vBlockList[l_uiInd].m_uiSta] != labTyp::NOT)
            {
                isGoodSht = true;
                // 如果是首个分段，禁止采取角点
                if (l_uiLInd < 0)
                {
                    isGoodSht = false;
                }
                // 如果位置不正确
                else if (!isCornDepthValid_(
                             p_pcL.m_pc.m_padd->at(p_vBlockList[l_uiInd].m_uiSta).depth)
                         || !isCornPosValid_(
                                p_pcL.m_pc.m_praw->points[p_vBlockList[l_uiInd].m_uiSta])
                         || isFRNTEdgeNotClear_(p_pcL,
                                                p_vBlockList[l_uiInd].m_uiSta,
                                                p_vBlockList[l_uiInd].m_uiSta + 1,
                                                p_vBlockList[l_uiInd].m_uiSta + 2))
                {
                    isGoodSht = false;
                }
                // 距离旁边的两个物体足够远
                else if (isLessWidthBetween2P_(p_pcL,
                                               p_vBlockList[l_uiInd].m_uiSta,
                                               p_vBlockList[l_uiLInd].m_uiEnd,
                                               0.5)
                         || isLessWidthBetween2P_(p_pcL,
                                                  p_vBlockList[l_uiInd].m_uiSta,
                                                  p_vBlockList[l_uiLLInd].m_uiEnd,
                                                  0.5))
                {
                    isGoodSht = false;
                }
                // 如果这个物体和背景处于同一直线上，认为是共线物体
                else if (isPointsAnLine_(p_pcL,
                                         p_vBlockList[l_uiInd].m_uiSta + 1,
                                         p_vBlockList[l_uiInd].m_uiSta,
                                         p_vBlockList[l_uiLInd].m_uiEnd))
                {
                    isGoodSht = false;
                }

                // 标记
                if (!isGoodSht)
                {
                    p_vBlockList[l_uiInd].m_iStaType = shltTye::OTHER;
                }
            }
            // 对尾点边缘进行分析
            if (p_vBlockList[l_uiInd].m_iEndType == shltTye::FRNT
                && p_pcL.m_iCLab[p_vBlockList[l_uiInd].m_uiEnd] != labTyp::NOT)
            {
                isGoodSht = true;
                // 如果是末尾分段，禁止采取角点
                if (l_uiNInd > l_uiBlkNum - 1)
                {
                    isGoodSht = false;
                }
                else if (!isCornDepthValid_(
                             p_pcL.m_pc.m_padd->at(p_vBlockList[l_uiInd].m_uiEnd).depth)
                         || !isCornPosValid_(
                                p_pcL.m_pc.m_praw->points[p_vBlockList[l_uiInd].m_uiEnd])
                         || isFRNTEdgeNotClear_(p_pcL,
                                                p_vBlockList[l_uiInd].m_uiEnd,
                                                p_vBlockList[l_uiInd].m_uiEnd - 1,
                                                p_vBlockList[l_uiInd].m_uiEnd - 2))
                {
                    isGoodSht = false;
                }
                // 距离旁边的两个物体足够远
                else if (isLessWidthBetween2P_(p_pcL,
                                               p_vBlockList[l_uiInd].m_uiEnd,
                                               p_vBlockList[l_uiNInd].m_uiSta,
                                               0.5)
                         || isLessWidthBetween2P_(p_pcL,
                                                  p_vBlockList[l_uiInd].m_uiEnd,
                                                  p_vBlockList[l_uiNNInd].m_uiSta,
                                                  0.5))
                {
                    isGoodSht = false;
                }
                // 如果这个物体和背景处于同一直线上，认为是共线物体
                else if (isPointsAnLine_(p_pcL,
                                         p_vBlockList[l_uiInd].m_uiEnd - 1,
                                         p_vBlockList[l_uiInd].m_uiEnd,
                                         p_vBlockList[l_uiNInd].m_uiSta))
                {
                    isGoodSht = false;
                }

                // 标记
                if (!isGoodSht)
                {
                    p_vBlockList[l_uiInd].m_iEndType = shltTye::OTHER;
                }
            }
        }
    }
    return state::DONE;
}

bool FeatureExtract::isLessWidthBetweenP2Blk_(st_fCloud& p_pcL,
                                              u_int32_t p_uiFrontP,
                                              st_Block& p_stBack)
{
    if (isLessWidthBetween2P_(p_pcL, p_uiFrontP, p_stBack.m_uiSta))
        return true;
    if (isLessWidthBetween2P_(p_pcL, p_uiFrontP, p_stBack.m_uiEnd))
        return true;
    if (isLessWidthBetween2P_(
            p_pcL, p_uiFrontP, u_int32_t(0.5 * (p_stBack.m_uiSta + p_stBack.m_uiEnd))))
        return true;
    return false;
}

bool FeatureExtract::isLessWidthBetween2P_(st_fCloud& p_pcL,
                                           u_int32_t p_uiFrontP,
                                           u_int32_t p_uiBackP,
                                           float p_width)
{
    // 如果两点之间距离太近，则这两个Front点无法正确利用
    if (calcXYDistP2P_point(p_pcL.m_pc.m_praw->points[p_uiFrontP],
                            p_pcL.m_pc.m_praw->points[p_uiBackP])
        < p_width)
        return true;
    else
        return false;
}

bool FeatureExtract::isLessAngBetween2P_(st_fCloud& p_pcL,
                                         u_int32_t p_uiFrontP,
                                         u_int32_t p_uiBackP)
{
    // 夹角越小，cos越大
    const float MaxCosEValue = std::cos(5.0 * D2R);
    Eigen::Vector2f PA, PB;
    // 雷达到两点之间的夹角
    PA.x() = p_pcL.m_pc.m_praw->points[p_uiFrontP].x;
    PA.y() = p_pcL.m_pc.m_praw->points[p_uiFrontP].y;
    PB.x() = p_pcL.m_pc.m_praw->points[p_uiBackP].x;
    PB.y() = p_pcL.m_pc.m_praw->points[p_uiBackP].y;
    // cos(theta)
    float CosAng = getAngleTwoVectors(PA, PB);
    if (CosAng > MaxCosEValue)
        return true;
    return false;
}

bool FeatureExtract::isFRNTEdgeNotClear_(st_fCloud& p_pcL,
                                         u_int32_t p_uiThisP,
                                         u_int32_t p_uiNeib1P,
                                         u_int32_t p_uiNeib2P)
{
    if (calcXYDistP2P_point(p_pcL.m_pc.m_praw->points[p_uiThisP],
                            p_pcL.m_pc.m_praw->points[p_uiNeib1P])
        > 0.15)
        return true;

    // 判断是否当前分段边缘是否大角度入射
    if (isCornInPlane_(p_pcL, p_uiThisP, p_uiNeib1P, p_uiNeib2P))
        return true;

    return false;
}

bool FeatureExtract::isCornInPlane_(st_fCloud& p_pcL,
                                    u_int32_t p_uiP1,
                                    u_int32_t p_uiP2,
                                    u_int32_t p_uiP3)
{
    // 当前点水平深度-临近点的水平深度
    float l_fDiff1 = p_pcL.m_pc.m_padd->at(p_uiP2).depth - p_pcL.m_pc.m_padd->at(p_uiP1).depth;
    float l_fDiff2 = p_pcL.m_pc.m_padd->at(p_uiP2).depth - p_pcL.m_pc.m_padd->at(p_uiP3).depth;
    //  若当前3点所在直线与激光射线夹角过大，则diff大于阈值
    //  l_fDiff1 * l_fDiff2 < 0 不包含波动情况，只包含同向大夹角
    if (std::fabs(l_fDiff1)
            > c_stCfg_.m_featu.m_fPlaneNoCornMulti * p_pcL.m_pc.m_padd->at(p_uiP2).depth
        && std::fabs(l_fDiff2)
               > c_stCfg_.m_featu.m_fPlaneNoCornMulti * p_pcL.m_pc.m_padd->at(p_uiP2).depth)
        return true;
    return false;
}

bool FeatureExtract::isPointsAnLine_(st_fCloud& p_pcL,
                                     u_int32_t l_uiLeft,
                                     u_int32_t l_uiMid,
                                     u_int32_t l_uiRigh)
{
    const float l_fMinCornEvalue = cos(170.0f * D2R);
    Eigen::Vector2f PA, PB, AB;
    //使用三点计算曲率
    PA.x() = p_pcL.m_pc.m_praw->points[l_uiLeft].x - p_pcL.m_pc.m_praw->points[l_uiMid].x;
    PA.y() = p_pcL.m_pc.m_praw->points[l_uiLeft].y - p_pcL.m_pc.m_praw->points[l_uiMid].y;
    PB.x() = p_pcL.m_pc.m_praw->points[l_uiRigh].x - p_pcL.m_pc.m_praw->points[l_uiMid].x;
    PB.y() = p_pcL.m_pc.m_praw->points[l_uiRigh].y - p_pcL.m_pc.m_praw->points[l_uiMid].y;
    if (getAngleTwoVectors(PA, PB) < l_fMinCornEvalue)
        return true;
    else
        return false;
}

#pragma endregion

#pragma region extract feature

int FeatureExtract::extractFeature_(st_fCloud& p_pcL,
                                    std::vector<st_Block>& p_vBlockList,
                                    pcFeaturePtr p_pcLC,
                                    pcFeaturePtr p_pcLS)
{
    for (auto& l_stBlk : p_vBlockList)
    {
        if (l_stBlk.m_iType == blkTyp::NML_BLK && c_stCfg_.m_use.m_bUseNML)
            getFeature_BIGBLK_(p_pcL, l_stBlk, p_pcLC, p_pcLS);
        else if (l_stBlk.m_iType == blkTyp::RND_BLK && c_stCfg_.m_use.m_bUseRND)
            getFeature_BIGBLK_(p_pcL, l_stBlk, p_pcLC, p_pcLS);
        else if (l_stBlk.m_iType == blkTyp::FSM_BLK && c_stCfg_.m_use.m_bUseFSM)
            getFeature_FSMBLK_(p_pcL, l_stBlk, p_pcLC, p_pcLS);
        else if (l_stBlk.m_iType == blkTyp::BSM_BLK && c_stCfg_.m_use.m_bUseBSM)
            getFeature_BSMBLK_(p_pcL, l_stBlk, p_pcLC, p_pcLS);
        else if (l_stBlk.m_iType == blkTyp::SPA_BLK && c_stCfg_.m_use.m_bUseSPA)
            getFeature_SPABLK_(p_pcL, l_stBlk, p_pcLC, p_pcLS);
        else if (l_stBlk.m_iType == blkTyp::GND_BLK && c_stCfg_.m_use.m_bUseGND)
            getFeature_GNDBLK_(p_pcL, l_stBlk, p_pcLC, p_pcLS);
        else if (l_stBlk.m_iType == blkTyp::UNK_BLK && c_stCfg_.m_use.m_bUseUNK)
            getFeature_UNKBLK_(p_pcL, l_stBlk, p_pcLC, p_pcLS);
    }
    return state::DONE;
}

int FeatureExtract::getFeature_FSMBLK_(st_fCloud& p_pcL,
                                       st_Block& p_stBlk,
                                       pcFeaturePtr p_pcLC,
                                       pcFeaturePtr p_pcLS)
{
    u_int32_t l_uiMid = (p_stBlk.m_uiSta + p_stBlk.m_uiLen * 0.5);
    if (p_pcL.m_iCLab[p_stBlk.m_uiSta] == labTyp::MAY)
    {
        addFeature_(p_pcL, l_uiMid, p_pcLC, FuTyp::FSM_F);
    }
    return state::DONE;
}

int FeatureExtract::getFeature_BIGBLK_(st_fCloud& p_pcL,
                                       st_Block& p_stBlk,
                                       pcFeaturePtr p_pcLC,
                                       pcFeaturePtr p_pcLS)
{
    // 一般大物体可以抽取边缘的前景点做角点
    if (c_stCfg_.m_use.m_bUseSHT == true && p_stBlk.m_iType == blkTyp::NML_BLK)
    {
        if (p_stBlk.m_iStaType == shltTye::FRNT)
        {
            if (p_pcL.m_iCLab[p_stBlk.m_uiSta] == labTyp::MAY)
                addFeature_(p_pcL, p_stBlk.m_uiSta, p_pcLC, FuTyp::SHELT);
        }

        if (p_stBlk.m_iEndType == shltTye::FRNT)
        {
            if (p_pcL.m_iCLab[p_stBlk.m_uiEnd] == labTyp::MAY)
                addFeature_(p_pcL, p_stBlk.m_uiEnd, p_pcLC, FuTyp::SHELT);
        }
        // 物体边缘设置不可为角点，防止边缘特征值较高的点被误识别
        setNoFeature_(p_pcL, p_stBlk, p_stBlk.m_uiSta, p_pcL.m_iCLab, labTyp::NOT);
        setNoFeature_(p_pcL, p_stBlk, p_stBlk.m_uiEnd, p_pcL.m_iCLab, labTyp::NOT);
    }

    // 排序仅针对物体内部，例如11点内1点，100点内90点
    std::vector<st_EValue> l_eValueSortBuf;
    // 计算特征值
    setEvalue_(p_pcL, p_stBlk, l_eValueSortBuf);
    // 排序
    std::sort(l_eValueSortBuf.begin(), l_eValueSortBuf.end(), [](st_EValue& l, st_EValue& r) {
        return l.m_fValue < r.m_fValue;
    });

    FuTyp l_FuType = FuTyp::UNK_F;
    if (p_stBlk.m_iType == blkTyp::NML_BLK)
        l_FuType = FuTyp::NML_F;
    else if (p_stBlk.m_iType == blkTyp::RND_BLK)
        l_FuType = FuTyp::RND_F;

    // 逆序搜索，找角点
    if (p_stBlk.m_iType == blkTyp::NML_BLK)  // RND_F类型不找内部角点
        for (auto l_stEv = l_eValueSortBuf.rbegin(); l_stEv != l_eValueSortBuf.rend(); l_stEv++)
        {
            if (l_stEv->m_fValue < c_stCfg_.m_featu.m_fCornCurvRange[0])
                break;
            if (isCornEvalueValid_(l_stEv->m_fValue)
                && p_pcL.m_iCLab[l_stEv->m_uiInd] == labTyp::MAY
                && isCornDepthValid_(p_pcL.m_pc.m_padd->at(l_stEv->m_uiInd).depth)
                && isCornPosValid_(p_pcL.m_pc.m_praw->points[l_stEv->m_uiInd])
                && !isCornInPlane_(
                       p_pcL, l_stEv->m_uiInd - 1, l_stEv->m_uiInd, l_stEv->m_uiInd + 1))
            {
                addFeature_(p_pcL, l_stEv->m_uiInd, p_pcLC, l_FuType, l_stEv->m_fValue);
                setNoFeature_(p_pcL, p_stBlk, l_stEv->m_uiInd, p_pcL.m_iCLab, labTyp::NOT);
            }
        }
    // 面点序列
    std::vector<st_EValue> l_uiSurfPointNumber;
    // 正序搜索，找面点
    for (auto l_stEv = l_eValueSortBuf.begin(); l_stEv != l_eValueSortBuf.end(); l_stEv++)
    {
        if (l_stEv->m_fValue > c_stCfg_.m_featu.m_fMaxEvalueSurf)
            break;
        if (l_stEv->m_fValue < c_stCfg_.m_featu.m_fMaxEvalueSurf
            && p_pcL.m_iSLab[l_stEv->m_uiInd] == labTyp::MAY
            && isSurfPosValid_(p_pcL.m_pc.m_praw->points[l_stEv->m_uiInd]))
        {
            l_uiSurfPointNumber.emplace_back(*l_stEv);
            setNoFeature_(p_pcL, p_stBlk, l_stEv->m_uiInd, p_pcL.m_iSLab, labTyp::NOT, 3);
        }
    }
    // 排序
    std::sort(l_uiSurfPointNumber.begin(),
              l_uiSurfPointNumber.end(),
              [](st_EValue& l, st_EValue& r) { return l.m_uiInd < r.m_uiInd; });
    //   顺序加入特征点
    for (auto l_stEv = l_uiSurfPointNumber.begin(); l_stEv != l_uiSurfPointNumber.end(); ++l_stEv)
    {
        addFeature_(p_pcL, l_stEv->m_uiInd, p_pcLS, l_FuType, l_stEv->m_fValue);
    }
    return state::DONE;
}

int FeatureExtract::getFeature_BSMBLK_(st_fCloud& p_pcL,
                                       st_Block& p_stBlk,
                                       pcFeaturePtr p_pcLC,
                                       pcFeaturePtr p_pcLS)
{
    // 稀疏大物体无法排序，所有抽取面点后退出
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + 1; l_uiInd < p_stBlk.m_uiEnd; l_uiInd += 2)
    {
        if (p_pcL.m_iSLab[l_uiInd] == labTyp::MAY
            && isSurfPosValid_(p_pcL.m_pc.m_praw->points[l_uiInd]))
            addFeature_(p_pcL, l_uiInd, p_pcLS, FuTyp::BSM_F);
    }
    return state::DONE;
}

int FeatureExtract::getFeature_SPABLK_(st_fCloud& p_pcL,
                                       st_Block& p_stBlk,
                                       pcFeaturePtr p_pcLC,
                                       pcFeaturePtr p_pcLS)
{
    // 稀疏大物体无法排序，所有抽取面点后退出
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + 1; l_uiInd < p_stBlk.m_uiEnd; l_uiInd += 2)
    {
        if (p_pcL.m_iSLab[l_uiInd] == labTyp::MAY
            && isSurfPosValid_(p_pcL.m_pc.m_praw->points[l_uiInd]))
            addFeature_(p_pcL, l_uiInd, p_pcLS, FuTyp::SPA_F);
    }
    return state::DONE;
}

int FeatureExtract::getFeature_GNDBLK_(st_fCloud& p_pcL,
                                       st_Block& p_stBlk,
                                       pcFeaturePtr p_pcLC,
                                       pcFeaturePtr p_pcLS)
{
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + 1; l_uiInd < p_stBlk.m_uiEnd; l_uiInd += 5)
    {
        if (p_pcL.m_iSLab[l_uiInd] == labTyp::MAY
            && isSurfPosValid_(p_pcL.m_pc.m_praw->points[l_uiInd]))
        {
            addFeature_(p_pcL, l_uiInd, p_pcLS, FuTyp::GND_F, p_stBlk.m_uiLen);
        }
    }
    return state::DONE;
}

int FeatureExtract::getFeature_UNKBLK_(st_fCloud& p_pcL,
                                       st_Block& p_stBlk,
                                       pcFeaturePtr p_pcLC,
                                       pcFeaturePtr p_pcLS)
{
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + 1; l_uiInd < p_stBlk.m_uiEnd; l_uiInd += 2)
    {
        if (p_pcL.m_iSLab[l_uiInd] == labTyp::MAY
            && isSurfPosValid_(p_pcL.m_pc.m_praw->points[l_uiInd]))
        {
            addFeature_(p_pcL, l_uiInd, p_pcLS, FuTyp::UNK_F, p_stBlk.m_uiLen);
        }
    }
    return state::DONE;
}

void FeatureExtract::setEvalue_(st_fCloud& p_pcL,
                                st_Block& p_stBlk,
                                std::vector<st_EValue>& p_vEvBuf)
{
    Eigen::Vector2f PA, PB;
    float l_feValue = 0;

    u_int32_t l_offset = 0;
    p_vEvBuf.resize(p_stBlk.m_uiLen - 2 * EV_HARFNUM);

    for (u_int32_t l_uiInd = p_stBlk.m_uiSta + EV_HARFNUM; l_uiInd <= p_stBlk.m_uiEnd - EV_HARFNUM;
         l_uiInd++)
    {
        // 注意向量方向
        PA.x() = p_pcL.m_pc.m_praw->points[l_uiInd - EV_HARFNUM].x
                 - p_pcL.m_pc.m_praw->points[l_uiInd].x;
        PA.y() = p_pcL.m_pc.m_praw->points[l_uiInd - EV_HARFNUM].y
                 - p_pcL.m_pc.m_praw->points[l_uiInd].y;
        PB.x() = p_pcL.m_pc.m_praw->points[l_uiInd + EV_HARFNUM].x
                 - p_pcL.m_pc.m_praw->points[l_uiInd].x;
        PB.y() = p_pcL.m_pc.m_praw->points[l_uiInd + EV_HARFNUM].y
                 - p_pcL.m_pc.m_praw->points[l_uiInd].y;
        // 限制表达范围
        p_vEvBuf[l_offset].m_uiInd = l_uiInd;
        p_vEvBuf[l_offset].m_fValue = getAngleTwoVectors(PA, PB);
        // 指向下一位
        l_offset++;
    }
}

void FeatureExtract::addFeature_(st_fCloud& p_pcL,
                                 u_int32_t& p_uiInd,
                                 pcFeaturePtr p_pcLf,
                                 FuTyp p_iFType,
                                 float p_fEvalue)
{
    PFeature l_tempP;
    l_tempP.x = p_pcL.m_pc.m_praw->points[p_uiInd].x;
    l_tempP.y = p_pcL.m_pc.m_praw->points[p_uiInd].y;
    l_tempP.z = p_pcL.m_pc.m_praw->points[p_uiInd].z;
    l_tempP.h = p_iFType;
    l_tempP.s = p_pcL.m_pc.m_padd->at(p_uiInd).time;
    l_tempP.v = p_pcL.m_pc.m_padd->at(p_uiInd).depth;
    p_pcLf->points.emplace_back(l_tempP);
}

/**TODO: 遮挡范围控制方法*/
void FeatureExtract::setNoFeature_(st_fCloud& p_pcL,
                                   st_Block& p_stBlk,
                                   u_int32_t& p_uiInd,
                                   int8_t* p_vLabs,
                                   labTyp p_iFType,
                                   u_int p_uiShieldNum,
                                   float p_fShieldRange)
{
    // 遮挡计算过程
    // 遮挡半径过小认为没有遮挡
    if (p_fShieldRange < 0.03 && p_uiShieldNum < 1)
        return;

    // 遮挡点数,对于面点只考虑距离
    u_int l_uiShtPointMinNum = p_uiShieldNum;

    u_int32_t l_uiCnt = (p_uiInd == 0) ? 0 : p_uiInd - 1;
    u_int32_t l_uiMin = p_stBlk.m_uiSta + EV_HARFNUM;
    u_int32_t l_uiMax = p_stBlk.m_uiEnd - EV_HARFNUM;

    // 向序号缩小方向搜索这个角度范围内的点并标记
    // 至少标记EV_HARFNUM点
    while (l_uiCnt >= l_uiMin && (p_uiInd - l_uiCnt <= l_uiShtPointMinNum))
    {
        p_vLabs[l_uiCnt] = p_iFType;
        l_uiCnt--;
    }
    // 向序号增大方向搜索这个角度范围内的点并标记
    // 至少标记EV_HARFNUM点
    l_uiCnt = p_uiInd + 1;
    while (l_uiCnt <= l_uiMax && (l_uiCnt - p_uiInd <= l_uiShtPointMinNum))
    {
        p_vLabs[l_uiCnt] = p_iFType;
        l_uiCnt++;
    }
}

#pragma endregion

void FeatureExtract::debugSegmentCopy_(st_fCloud& p_pcL, int p_iLineId, st_Block& p_stBlk)
{
    PFeature l_pTemp;
    for (u_int32_t l_uiInd = p_stBlk.m_uiSta; l_uiInd <= p_stBlk.m_uiEnd; ++l_uiInd)
    {
        l_pTemp.getVector4fMap() = p_pcL.m_pc.m_praw->points[l_uiInd].getVector4fMap();
        l_pTemp.h = p_iLineId;
        l_pTemp.s = p_pcL.m_pc.m_padd->at(l_uiInd).time;
        l_pTemp.v = p_pcL.m_pc.m_padd->at(l_uiInd).intensity;
        c_pcSegment_->points.emplace_back(l_pTemp);
        if (p_iLineId == 8)
        {
            // 预分类加速靶标提取
            // l_pTemp.h = p_stBlk.m_iType;
            c_pcSegmentMiddleScan_->points.emplace_back(l_pTemp);
        }
        for (u_int32_t curbId = 0; curbId < c_viCurbLineId.size(); curbId++)
        {
            if (c_viCurbLineId[curbId] == p_iLineId)
            {
                if (fabs(l_pTemp.z) < 0.3)
                    c_pcSegmentCurbScan_->points.emplace_back(l_pTemp);
                break;
            }
        }
    }
}

#pragma scancontext
int FeatureExtract::lineProcessSC_(st_fCloud& p_pcL, Eigen::MatrixXd& p_sc, int p_iLineId)
{
    u_int32_t l_uiLineSize = p_pcL.m_pc.m_praw->points.size();
    // 如果是空线则直接退出
    if (l_uiLineSize < EV_NUM)
        return state::FAIL;
    //  像素位置
    int ring_idx, sctor_idx;
    // 点的参数:平距&角度&Z值
    float l_fDist, l_fAng, l_fZ;
    // Z的固定偏移,用于将所有点转移到高空
    float l_fZOffset = c_stCfg_.m_attach.m_fLidarHeight + 2;
    for (u_int32_t l_uiInd = 0; l_uiInd < l_uiLineSize; ++l_uiInd)
    {
        l_fDist = p_pcL.m_pc.m_padd->at(l_uiInd).xydist;
        l_fAng = p_pcL.m_pc.m_padd->at(l_uiInd).ang;
        l_fZ = p_pcL.m_pc.m_praw->points[l_uiInd].z + l_fZOffset;

        // if range is out of roi, pass
        if (l_fDist > c_stCfg_.m_sc.m_iRadius || l_fZ < EXP)
            continue;

        ring_idx = std::max(
            std::min(c_stCfg_.m_sc.m_iRing,
                     int(ceil((l_fDist / c_stCfg_.m_sc.m_iRadius) * c_stCfg_.m_sc.m_iRing))),
            1);
        sctor_idx = std::max(std::min(c_stCfg_.m_sc.m_iSector,
                                      int(ceil((l_fAng / 360.0) * c_stCfg_.m_sc.m_iSector))),
                             1);
        // taking maximum l_fZ
        if (p_sc(ring_idx - 1, sctor_idx - 1) < l_fZ)
            p_sc(ring_idx - 1, sctor_idx - 1) = l_fZ;
    }
    return state::DONE;
}

#pragma endregion

}  // namespace wj_FE