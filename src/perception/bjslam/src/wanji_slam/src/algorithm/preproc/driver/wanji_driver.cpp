// local
#include "algorithm/preproc/driver/wanji_driver.h"
namespace wanji_driver {
wanjiDriver::wanjiDriver(
    uint32_t p_uiLidarID,
    boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATAS>&, int, bool)> p_packCB_)
    : c_stSysParam_(wj_slam::SYSPARAM::getIn()), c_driverCB_(p_packCB_), c_pInput_(nullptr),
      c_uiLidarID_(p_uiLidarID), c_uiCurID_(0), c_iLastCycleNum_(0), c_sLaserType_("")
{
    c_sLaserType_ = c_stSysParam_->m_lidar[c_uiLidarID_].m_dev.m_sDevType;
    start();
}

wanjiDriver::~wanjiDriver() {}

void wanjiDriver::shutDown() {}

void wanjiDriver::start()
{
    // 是否某一种WLR720
    std::string l_sLaser = c_sLaserType_.substr(0, 6);
    if (l_sLaser == "WLR720")
    {
        auto cb = boost::bind(&wanjiDriver::scanDataProcessCb_, this, _1);
        c_pInput_.reset(new wanji_driver::Input720(c_uiLidarID_, cb));
        // 启动基本参数查询
        c_pInput_->requestBaseParam();
        // 如果查询成功
        if (!(c_pInput_->isStart()))
        {
            c_stSysParam_->m_fae.setErrorCode("C8");
            LOGFAE(WERROR,
                   "雷达 [{}] 在线初始化失败 | 网络情况异常，请检查雷达是否正确连接！",
                   c_stSysParam_->m_lidar[c_uiLidarID_].m_sLaserName);
        }
    }
    else
    {
        // c_stSysParam_->m_fae.setErrorCode("C10");
        LOGFAE(WERROR,
               "雷达 [{}] 在线初始化失败 |基础类型异常，请联系万集开发人员进行检查!",
               c_stSysParam_->m_lidar[c_uiLidarID_].m_sLaserName);
    }
}

// 扫描数据回调函数，用于处理从雷达中接收的一圈数据
void wanjiDriver::scanDataProcessCb_(st_ScanBuffer& p_stScanbuffer)
{
    if (c_iLastCycleNum_ == -1)
    {
        c_iLastCycleNum_ = p_stScanbuffer.m_iCircleNum;  // 记录起始圈号，用以记录是否丢圈
        c_uiCurID_ = c_iLastCycleNum_;
    }

    uint16_t l_uiCycleDiff = uint16_t(p_stScanbuffer.m_iCircleNum) - c_iLastCycleNum_;
    if (l_uiCycleDiff > 1)
    {
        c_stSysParam_->m_lidar[c_uiLidarID_].m_dev.setStatus(wj_slam::DevStatus::DATADATAERROR);
        LOGFAE(WERROR,
               "{} 雷达 [{}] 数据异常 | 丢{}圈 | Last圈号{} -> Now圈号{} 请检查网络连接!",
               WJLog::getWholeSysTime(),
               c_stSysParam_->m_lidar[c_uiLidarID_].m_sLaserName,
               l_uiCycleDiff - 1,
               c_iLastCycleNum_,
               p_stScanbuffer.m_iCircleNum);
    }
    c_uiCurID_ += l_uiCycleDiff;

    boost::shared_ptr<s_LIDAR_RAW_DATAS> scandata = nullptr;
    if (!p_stScanbuffer.m_bIsFull)
    {
        // 本圈数据中存在丢包
        c_stSysParam_->m_lidar[c_uiLidarID_].m_dev.setStatus(wj_slam::DevStatus::DATADATAERROR);
        LOGFAE(WWARN,
               "{} 雷达 [{}] 丢包 | 圈号 {}",
               WJLog::getWholeSysTime(),
               c_stSysParam_->m_lidar[c_uiLidarID_].m_sLaserName,
               p_stScanbuffer.m_iCircleNum);
        scandata.reset(new s_LIDAR_RAW_DATAS());
        scandata->m_vPackets.reserve(c_stSysParam_->m_lidar[c_uiLidarID_].m_uiFramePkgNum);
        for (size_t i = 0; i < p_stScanbuffer.m_data.m_vPackets.size(); i++)
        {
            if (p_stScanbuffer.m_data.m_vPackets[i].hasData())
                scandata->m_vPackets.emplace_back(p_stScanbuffer.m_data.m_vPackets[i]);
        }
    }
    else
    {
        scandata.reset(new s_LIDAR_RAW_DATAS(p_stScanbuffer.m_data));
    }

    LOGFAE(WTRACE,
           "{} 雷达[{}]数据 | 圈号 {} 包数 {} curID {}",
           WJLog::getWholeSysTime(),
           c_stSysParam_->m_lidar[c_uiLidarID_].m_sLaserName,
           p_stScanbuffer.m_iCircleNum,
           scandata->m_vPackets.size(),
           c_uiCurID_);

    c_driverCB_(c_uiLidarID_, scandata, c_uiCurID_, (l_uiCycleDiff > 1));
    c_iLastCycleNum_ = p_stScanbuffer.m_iCircleNum;
}
}  // namespace wanji_driver
