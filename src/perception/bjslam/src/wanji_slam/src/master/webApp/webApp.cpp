/*
 * @Author: lin<PERSON><PERSON>i
 * @Date: 2021-05-18 13:15:07
 * @LastEditTime: 2022-12-01 16:44:35
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_net/src/net_Message_Proc.cpp
 */

#include "master/webApp/webApp.h"
#include <fcntl.h>
#include <thread>
#include <unistd.h>
#include <vector>

namespace wj_slam {

WebApp::WebApp(ros::NodeHandle& p_nh, boost::function<int(int, int)> setCb) : c_node_(p_nh)
{
    c_pstWebMsg_ = new s_NetMsg;
    c_pstSlamMsg_ = new s_NetMsg;
    c_masterParamPtr = s_masterCfg::getIn();
    netInit_(c_myAddr_, c_remoteAddr_);

    c_ProtocolProc_.reset(new ProtocolProc(*c_pstWebMsg_,
                                           c_mtxLockWeb_,
                                           *c_pstSlamMsg_,
                                           c_mtxLockSlam_,
                                           boost::bind(&WebApp::sendRosMsg_, this, _1, _2),
                                           boost::bind(&WebApp::sendNetMsg_, this, _1, _2),
                                           setCb));

    connectWebServer();

    c_subWeb_ = c_node_.subscribe<std_msgs::Int32MultiArray>(
        "/web_from", 20, &WebApp::procRecvROSMsg, this);
    c_subRvizPose_ = c_node_.subscribe<geometry_msgs::PoseWithCovarianceStamped>(
        "/initialpose", 20, &WebApp::poseHandlerCb_, this);
    c_pubWeb_ = c_node_.advertise<std_msgs::Int32MultiArray>("/web_to", 1);
}

WebApp::~WebApp()
{
    shutdown();
}
/**
 * @description: WebMsg处理
 * @param {Int32MultiArrayConstPtr&} p_msg
 * @return {*}
 * @other:
 */
void WebApp::procRecvROSMsg(const std_msgs::Int32MultiArrayConstPtr& p_msg)
{
    if (!c_bRun_)
        return;
    int l_iDataNum = p_msg->data.size();
    if (l_iDataNum > 0 && l_iDataNum <= NET_LENGTH_MAX)
    {
        u_char* l_cTmp = (u_char*)&(p_msg->data[0]);
        if ((c_pstWebMsg_->m_uiDataLen + l_iDataNum) <= (NET_LENGTH_MAX))
        {
            for (int i = 0; i < l_iDataNum; ++i)
            {
                c_pstWebMsg_->m_aucBuf[i + c_pstWebMsg_->m_uiDataLen] = u_char(p_msg->data[i]);
            }
            {
                std::lock_guard<std::mutex> l_mtx(c_mtxLockWeb_);
                c_pstWebMsg_->m_uiDataLen += l_iDataNum;
                c_pstWebMsg_->m_uiDataLen %= NET_LENGTH_MAX;
            }
        }
        else
        {
            int offset = 0;
            for (uint32_t i = 0; i < (NET_LENGTH_MAX - c_pstWebMsg_->m_uiDataLen); ++i)
            {
                c_pstWebMsg_->m_aucBuf[i + c_pstWebMsg_->m_uiDataLen] = u_char(p_msg->data[i]);
            }

            offset = NET_LENGTH_MAX - c_pstWebMsg_->m_uiDataLen;
            for (int i = 0; i < (l_iDataNum - offset); ++i)
            {
                c_pstWebMsg_->m_aucBuf[i] = u_char(p_msg->data[i + offset]);
            }
            {
                std::lock_guard<std::mutex> l_mtx(c_mtxLockWeb_);
                c_pstWebMsg_->m_uiDataLen = l_iDataNum - offset;
            }
        }
    }
}

/**
 * @description: 判断TCP是否连接成功，接收数据压入数组
 * @param {*}
 * @return {*}
 * @other:
 */
void WebApp::procRecvSlamMsg()
{
    LOGFAE(WINFO,
           "[{}] Web客户端: 服务器已连接 | 服务器-IP: {} 服务器-Port: {}",
           WJLog::getWholeSysTime(),
           inet_ntoa(c_remoteAddr_.sin_addr),
           ntohs(c_remoteAddr_.sin_port));
    u_char l_acBufTmp_[4096];
    int l_iDataNum_ = 0;
    getNetCmd2Send();
    while (c_bRun_)
    {
        int l_res = getsockopt(
            c_sockfd_, IPPROTO_TCP, TCP_INFO, &c_stConnectInfo_, (socklen_t*)&c_iConnectInfoLen_);
        if (l_res == -1 || c_stConnectInfo_.tcpi_state != TCP_ESTABLISHED)
        {
            if (c_sockfd_ != -1)
                close(c_sockfd_);
            c_sockfd_ = -1;
            exitGetNetCmd2Send();
            LOGFAE(WWARN,
                   "[{}] Web客户端: 服务器已断开| 服务器-IP: {} 服务器-Port: {}",
                   WJLog::getWholeSysTime(),
                   inet_ntoa(c_remoteAddr_.sin_addr),
                   ntohs(c_remoteAddr_.sin_port));
            return;
        }

        if (c_sockfd_ != -1)
            l_iDataNum_ = recv(c_sockfd_, l_acBufTmp_, 4095, 0);
        else
            l_iDataNum_ = 0;

        if (l_iDataNum_ > 0 && l_iDataNum_ <= NET_LENGTH_MAX)
        {
            if ((c_pstSlamMsg_->m_uiDataLen + l_iDataNum_) <= (NET_LENGTH_MAX))
            {
                memcpy(
                    &c_pstSlamMsg_->m_aucBuf[c_pstSlamMsg_->m_uiDataLen], l_acBufTmp_, l_iDataNum_);
                {
                    std::lock_guard<std::mutex> l_mtx(c_mtxLockSlam_);
                    c_pstSlamMsg_->m_uiDataLen += l_iDataNum_;
                    c_pstSlamMsg_->m_uiDataLen %= NET_LENGTH_MAX;
                }
            }
            else
            {
                int offset = 0;
                memcpy(&c_pstSlamMsg_->m_aucBuf[c_pstSlamMsg_->m_uiDataLen],
                       l_acBufTmp_,
                       NET_LENGTH_MAX - c_pstSlamMsg_->m_uiDataLen);
                offset = NET_LENGTH_MAX - c_pstSlamMsg_->m_uiDataLen;
                memcpy(&c_pstSlamMsg_->m_aucBuf[0], &l_acBufTmp_[offset], l_iDataNum_ - offset);
                {
                    std::lock_guard<std::mutex> l_mtx(c_mtxLockSlam_);
                    c_pstSlamMsg_->m_uiDataLen = l_iDataNum_ - offset;
                }
            }
        }
        if (!c_bRun_)
            break;
        usleep(10000);
    }
}

/**
 * @description: 将字符串(按照协议打包后)通过ROS发布出去
 * @param {char*} p_pcBuf 字符串
 * @param {int} p_iLen 长度
 * @return {*}
 * @other:
 */
void WebApp::sendRosMsg_(char* p_pcBuf, int p_iLen)
{
    if (p_iLen <= 0)
        return;
    std_msgs::Int32MultiArray msg_array;
    msg_array.data.resize(p_iLen);
    for (int i = 0; i < (p_iLen); ++i)
        msg_array.data[i] = char(p_pcBuf[i]);
    c_pubWeb_.publish(msg_array);
}

/**
 * @description: 将字符串通过TCP发布出去
 * @param {u_char*} p_pcBuf 要发布的字符串
 * @param {int} p_iLen  字符串长度
 * @return {*}
 * @other:
 */
void WebApp::sendNetMsg_(char* p_pcBuf, int p_iLen)
{
    if (c_sockfd_ != -1 && p_iLen > 0)
    {
        int l_res = send(c_sockfd_, p_pcBuf, p_iLen, MSG_NOSIGNAL);
        if (l_res <= 0)
        {
            LOGWEB(WERROR,
                   "{} Net sendMsg fail->status: {} | value {} | buf {} | len {}",
                   WJLog::getWholeSysTime(),
                   strerror(l_res),
                   l_res,
                   p_pcBuf,
                   p_iLen);
        }
    }
}

void WebApp::netInit_(sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr)
{
    memset(&p_sRemoteAddr, 0, sizeof(p_sRemoteAddr));
    p_sRemoteAddr.sin_family = AF_INET;
    p_sRemoteAddr.sin_port = htons(c_masterParamPtr->m_slam->m_web.m_socket.m_uiLocalPort);
    p_sRemoteAddr.sin_addr.s_addr = inet_addr(c_masterParamPtr->m_sLocalIP.c_str());

    memset(&p_sMyAddr, 0, sizeof(p_sMyAddr));
    p_sMyAddr.sin_family = AF_INET;
    p_sMyAddr.sin_port = 0;  // htons(c_masterParamPtr->m_slam->m_web.m_socket.m_uiLocalPort + 1);
    p_sMyAddr.sin_addr.s_addr = htonl(INADDR_ANY);  // inet_addr("127.0.0.1");
}

/**
 * @description: 初始化 TCP客户端 连接TCP服务器 并接受数据
 * @param {int&} p_iFd 句柄
 * @param {sockaddr_in&} p_sMyAddr TCP客户端地址
 * @param {sockaddr_in&} p_sRemoteAddr TCP服务器地址
 * @return {*}
 * @other:
 */
void WebApp::netStart_()
{
    // TCP连接保持，连接中接收数据，中断后自动重新连接
    while (c_bRun_)
    {
        if (connectTcpServer_(c_sockfd_, c_myAddr_, c_remoteAddr_))
        {
            std::thread prcomsg(&WebApp::procRecvSlamMsg, this);
            prcomsg.join();
        }
        if (!c_bRun_)
            break;
        usleep(100000);
    }
    c_bRunOver_ = true;
}

/**
 * @description: 重置SLAM网络连接
 * @param {}
 * @return {*}
 * @other:
 */
void WebApp::quitWebServer()
{
    c_bRun_ = false;
    // 断开网络连接-停止TCP接收
    if (c_sockfd_ != -1)
    {
        close(c_sockfd_);
        c_sockfd_ = -1;
    }
    // 停止解析
    exitGetNetCmd2Send();

    while (1)
    {
        if (c_bRunOver_)
            break;
        sleep(1);
    }
}

void WebApp::connectWebServer()
{
    c_bRunOver_ = false;
    c_bRun_ = true;
    std::thread prcomsg(&WebApp::netStart_, this);
    prcomsg.detach();
}

/**
 * @description: 连接TCP服务器 3s未连接则认为失败
 * @param {int&} p_iFd 网络句柄 须已建立socket
 * @param {sockaddr_in&} p_sRemoteAddr 服务器地址
 * @return {bool} TCP服务器连接成功
 * @other:
 */
bool WebApp::connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr)
{
    if (p_iFd != -1)
    {
        close(p_iFd);
        p_iFd = -1;
    }
    // 建立套接字
    p_iFd = socket(PF_INET, SOCK_STREAM, 0);
    if (p_iFd == -1)
    {
        LOGWEB(WERROR, "{} open socket failed: {}", WJLog::getWholeSysTime(), strerror(errno));
        return false;
    }
    // 端口复用
    int l_iOpt2 = 1;
    if (setsockopt(p_iFd, SOL_SOCKET, SO_REUSEPORT, &l_iOpt2, sizeof(l_iOpt2)))
    {
        LOGWEB(WERROR, "{} reuse port error: {}", WJLog::getWholeSysTime(), strerror(errno));
        return false;
    }

    // 绑定端口
    if (bind(p_iFd, (sockaddr*)&p_sMyAddr, sizeof(sockaddr)) == -1)
    {
        LOGWEB(WERROR, "{} bind error: {}", WJLog::getWholeSysTime(), strerror(errno));
        return false;
    }

    int flags = fcntl(p_iFd, F_GETFL, 0);
    // 设置非阻塞方式
    if (fcntl(p_iFd, F_SETFL, flags | O_NONBLOCK) < 0)
    {
        LOGWEB(WERROR, "{} non-block: {}", WJLog::getWholeSysTime(), strerror(errno));
        return false;
    }

    int res = connect(p_iFd, (struct sockaddr*)&p_sRemoteAddr, sizeof(p_sRemoteAddr));

    // 连接成功(服务器和客户端在同一台机器上时就有可能发生这种情况)
    if (res == -1)
    {
        // 以非阻塞的方式来进行连接的时候，返回-1,不代表一定连接错误，如果返回EINPROGRESS，代表连接还在进行中
        // 可以通过poll或者select来判断socket是否可写，如果可以写，说明连接完成了
        if (errno == EINPROGRESS)
        {
            fd_set writefds;
            FD_ZERO(&writefds);
            FD_SET(p_iFd, &writefds);

            struct timeval timeout;
            timeout.tv_sec = 3;
            timeout.tv_usec = 0;

            // 调用select来等待连接建立成功完成
            res = select(p_iFd + 1, NULL, &writefds, NULL, &timeout);
            if (res < 0)
            {
                LOGWEB(WERROR, "{} TCP select: {}", WJLog::getWholeSysTime(), strerror(errno));
                close(p_iFd);
                p_iFd = -1;
                return false;
            }

            // 返回0,则表示建立连接超时;
            // 我们返回超时错误给用户，同时关闭连接，以防止三路握手操作继续进行
            if (res == 0)
            {
                LOGFAE(WWARN, "连接Web服务器超时 | 请确认网络正常且SLAM启动");
                close(p_iFd);
                p_iFd = -1;
                return false;
            }
            else
            {
                // 返回大于0的值,则需要检查套接口描述符是否可读或可写;
                if (!FD_ISSET(p_iFd, &writefds))
                {
                    LOGWEB(WERROR, "{} no events found!", WJLog::getWholeSysTime());
                    close(p_iFd);
                    p_iFd = -1;
                    return false;
                }
                else
                {
                    // 套接口描述符可读或可写,通过调用getsockopt得到套接口上待处理的错误(SO_ERROR)
                    // err 0-建立成功
                    int err = 0;
                    socklen_t elen = sizeof(err);
                    res = getsockopt(p_iFd, SOL_SOCKET, SO_ERROR, (char*)&err, &elen);
                    if (res < 0)
                    {
                        LOGWEB(WERROR,
                               "{} TCP getsockopt: {}",
                               WJLog::getWholeSysTime(),
                               strerror(errno));
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    if (err != 0)
                    {
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    else
                        return true;
                }
            }
        }
        else
        {
            LOGWEB(WERROR, "{} connec error: {}", WJLog::getWholeSysTime(), strerror(errno));
            return false;
        }
    }
    else
        return true;
}

void WebApp::sendSetPose(s_POSE6D& p_setPose)
{
    char l_acBuf[100] = {0};
    int l_iOffset = 26;
    memcpy(&l_acBuf[0], c_aucSetLidarPose, l_iOffset);
    l_acBuf[l_iOffset++] = 1;  //雷达位姿
    fillFloatTo32Bytes(p_setPose.x(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(p_setPose.y(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(p_setPose.z(), l_acBuf, l_iOffset);
    fillFloatTo32Bytes(p_setPose.yaw(), l_acBuf, l_iOffset);

    // 写入帧长、校验位、帧尾
    l_acBuf[2] = (l_iOffset & 0xff00) >> 8;
    l_acBuf[3] = l_iOffset & 0xff;
    l_acBuf[l_iOffset++] = 0;
    l_acBuf[l_iOffset++] = checkXOR(&l_acBuf[2], l_iOffset - 3);
    l_acBuf[l_iOffset++] = 0xEE;
    l_acBuf[l_iOffset++] = 0xEE;

    if (c_bRun_)
        sendNetMsg_(l_acBuf, l_iOffset);
}

}  // namespace wj_slam