/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-24 23:05:08
 * @LastEditors: shuangquan han
 * @LastEditTime: 2023-12-07 13:49:28
 */
#include "master/webApp/web_Protocol.h"

namespace wj_slam {
FRAMETYPE WebProtocol::getFrameType(int p_iFrameType)
{
    if (p_iFrameType == 1)
        return FRAMETYPE::SETQUERY;
    if (p_iFrameType == 2)
        return FRAMETYPE::REPLY;
    return FRAMETYPE::FRAMEERR;
}

void WebProtocol::calcWebProtocolHead_(u_char* p_pcBufCMD, char* p_pcBufResponse, int& p_iLen)
{
    p_iLen = 26;
    memcpy(&p_pcBufResponse[0], p_pcBufCMD, p_iLen);
    p_pcBufResponse[11] = 2;
}

void WebProtocol::calcWebProtocolTail_(char* p_pucBuf, int& p_iLen)
{
    p_pucBuf[2] = (p_iLen & 0xff00) >> 8;
    p_pucBuf[3] = p_iLen & 0xff;
    p_pucBuf[p_iLen++] = 0;
    p_pucBuf[p_iLen++] = checkXOR(&p_pucBuf[2], p_iLen - 3);
    p_pucBuf[p_iLen++] = 0xEE;
    p_pucBuf[p_iLen++] = 0xEE;
}

/**
 * @description: SLAM实时生效协议处理
 * @param {u_char*} p_pcBufCMD 协议内容 区别与wj协议 带帧头帧尾
 * @param {int} p_iBufLen 协议长度
 * @param {char*} p_pcBufResponse 发送内容
 * @return {*}
 * @other: 设置帧-解析保存至备份参数中 透传发送给SLAM端   回复后 确认成功后将备份参数同步至主结构体
 * 并将回复帧给Web 查询帧-透传 回复后更新结构体 并转发至Web端
 */
void WebProtocol::selectSlaverProtocol(u_char* p_pcBufCMD, int p_iBufLen, char* p_pcBufResponse)
{
    int l_iSta = 11;
    int l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    // 拷贝完整协议 用于转发
    memcpy(&p_pcBufResponse[0], p_pcBufCMD, p_iBufLen);
    switch (l_iSonCMDID)
    {
        case SLACMDID::SETCMD_WORKMODE:
        {
            std::cout << "WEB_SETCMD_WORKMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            if ((int)p_pcBufCMD[l_iSta] == 2)
            {
                // 设置回复后 判断工作模式 启停避障模块
                c_setActionCb_(19, 0);
            }
            break;
        }
        case SLACMDID::SETCMD_VIEWLIDAR:
        {
            std::cout << "WEB_SETCMD_VIEWLIDAR: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_VIEWMAP:
        {
            std::cout << "WEB_SETCMD_VIEWMAP: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_SLAMMAPINFO:
        {
            std::cout << "WEB_SETCMD_SLAMMAPINFO: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_SAVEMAP:
        {
            std::cout << "SLACMDID::WEB_SETCMD_SAVEMAP: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_CURRPOSE:
        {
            std::cout << "WEB_SETCMD_CURRPOSE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_WHOLEPOSE:
        {
            std::cout << "WEB_SETCMD_WHOLEPOSE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_MAPCORRECT:
        {
            std::cout << "WEB_SETCMD_MAPCORRECT: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_CARCORRECT:
        {
            std::cout << "WEB_SETCMD_CARCORRECT: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_VIEWMODE:
        {
            std::cout << "WEB_SETCMD_VIEWMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_MAPPINGMODE:
        {
            std::cout << "WEB_SETCMD_MAPPINGMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::SETCMD_MOTIONMODE:
        {
            std::cout << "WEB_SETCMD_MOTIONMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_WORKMODE:
        {
            std::cout << "WEB_QUERYCMD_WORKMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_VIEWLIDAR:
        {
            std::cout << "WEB_QUERYCMD_VIEWLIDAR: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_VIEWMAP:
        {
            std::cout << "WEB_QUERYCMD_VIEWMAP: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_SLAMMAPINFO:
        {
            std::cout << "WEB_QUERYCMD_SLAMMAPINFO: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_CURRLASERPOSE:
        {
            std::cout << "WEB_QUERYCMD_CURRPOSE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_MAPCORRECT:
        {
            std::cout << "WEB_QUERYCMD_MAPCORRECT: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_CARCORRECT:
        {
            std::cout << "WEB_QUERYCMD_CARCORRECT: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_CURRTIMESTAMP:
        {
            std::cout << "WEB_QUERYCMD_CURRTIMESTAM: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_PROCVERSION:
        {
            std::cout << "WEB_QUERYCMD_PROCVERSION: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_CURRAGVPOSE:
        {
            std::cout << "WEB_QUERYCMD_CURRAGVPOSE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_VIEWMODE:
        {
            std::cout << "WEB_QUERYCMD_VIEWMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_MAPPINGMODE:
        {
            std::cout << "WEB_QUERYCMD_MAPPINGMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case SLACMDID::QUERYCMD_MOTIONMODE:
        {
            std::cout << "WEB_QUERYCMD_MOTIONMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        default:
        {
            LOGFAE(WERROR,
                   "Slam协议异常: 未定义 {:#X} - {:#X}, 请联系万集人员进行检查!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            c_masterParamPtr->m_slam->m_fae.setErrorCode("B1");
            return;
        }
    }
    // 转发任何已知协议
    if (getFrameType(p_pcBufCMD[l_iSta]) == FRAMETYPE::SETQUERY)
        c_sendTcpCb_(p_pcBufResponse, p_iBufLen);
    else if (getFrameType(p_pcBufCMD[l_iSta]) == FRAMETYPE::REPLY)
        c_sendRosCb_(p_pcBufResponse, p_iBufLen);
}

/**
 * @description: 确定WEB协议后 解析协议指令并处理/回复
 * @param {char*} p_pcBufCMD WEB协议内容 区别与wj协议 带帧头帧尾
 * @return {*}
 * @other: 设置帧-解析保存至参数 并直接回复Web
 *                    查询帧-直接回复Web
 */
void WebProtocol::selectMasterProtocol(u_char* p_pcBufCMD, int p_iBufLen, char* p_pcBufResponse)
{
    int l_iSta = 11;  // 区别与wj协议
    int l_iResLen = 0;
    int curStatus = 0;
    int l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    int l_iSubscript = l_iSta + 15;
    //拷贝数组0-25 l_iLen从26开始
    calcWebProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iResLen);
    switch (l_iSonCMDID)
    {
        case WEBCMDID::SETCMD_DRIVERMODE:
        {
            std::cout << "SETCMD_DRIVERMODE" << std::endl;
            //备份配置文件，打开配置文件-修改AGV连接IP/端口 杀死程序然后重启
            if (p_pcBufCMD[l_iSubscript] == 0 || p_pcBufCMD[l_iSubscript] == 1)
            {
                // 确认只有0和1模式 其他模式无法切换, 模式相同时 不必切换
                // 切换成功后外部更新m_bIsOnlineMode
                if ((int)c_masterParamPtr->m_slam->m_bIsOnlineMode != p_pcBufCMD[l_iSubscript])
                    curStatus = c_setActionCb_(0, 0);
                else
                    curStatus = 1;
            }
            else
                curStatus = 0;
            p_pcBufResponse[l_iResLen++] = p_pcBufCMD[l_iSubscript];
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_DELETEMAP:
        {
            std::cout << "SETCMD_DELETEMAP" << std::endl;
            curStatus = deleteData_(
                p_pcBufCMD, l_iSubscript, c_masterParamPtr->m_slam->m_sPkgPath + "/data/Map/");
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_LASERPCAPNAME:
        {
            std::cout << "SETCMD_LASERPCAPNAME" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                // 解析
                p_pcBufResponse[l_iResLen++] = (int)asciiToString_(
                    p_pcBufCMD,
                    l_iSubscript,
                    c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sPcapName);
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case WEBCMDID::SETCMD_AGVPCAPNAME:
        {
            std::cout << "SETCMD_AGVPCAPNAME" << std::endl;
            p_pcBufResponse[l_iResLen++] = (int)asciiToString_(
                p_pcBufCMD, l_iSubscript, c_masterParamPtr->m_slam->m_agv.m_dev.m_sPcapName);
            break;
        }
        case WEBCMDID::SETCMD_DRIVERCONTROL:
        {
            std::cout << "SETCMD_DRIVERCONTROL" << std::endl;
            if (!c_masterParamPtr->m_slam->m_bIsOnlineMode)
            {
                // 离线模式 控制播放暂停 并返回执行状态
                c_masterParamPtr->m_bIsStart = p_pcBufCMD[l_iSubscript];
                curStatus = c_setActionCb_(1, 0);
                p_pcBufResponse[l_iResLen++] = curStatus;
            }
            else
            {
                //在线模式 将离线播放按钮置0
                p_pcBufResponse[l_iResLen++] = 0;
                c_masterParamPtr->m_bIsStart = 0;
            }
            break;
        }
        case WEBCMDID::SETCMD_DELETEPCAP:
        {
            std::cout << "SETCMD_DELETEPCAP" << std::endl;
            p_pcBufResponse[l_iResLen++] = (int)deleteData_(
                p_pcBufCMD, l_iSubscript, c_masterParamPtr->m_sOffLineDataPath, ".pcapng");
            break;
        }
        case WEBCMDID::SETCMD_RECORDDATA:
        {
            std::cout << "SETCMD_RECORDDATA" << std::endl;
            // 返回是否录制雷达 而非启动关闭录制动作的执行情况
            if (c_setActionCb_(2, (int)(p_pcBufCMD[l_iSubscript])))
                p_pcBufResponse[l_iResLen++] = p_pcBufCMD[l_iSubscript];
            else
                p_pcBufResponse[l_iResLen++] = !(int)(p_pcBufCMD[l_iSubscript]);
            break;
        }
        case WEBCMDID::SETCMD_TIMEINTERVAL:
        {
            std::cout << "SETCMD_TIMEINTERVAL" << std::endl;
            p_pcBufResponse[l_iResLen++] =
                bytes16ToInt(p_pcBufCMD, l_iSubscript, c_masterParamPtr->m_uiRecordTimeInterval);
            break;
        }
        case WEBCMDID::SETCMD_NETCFG:
        {
            std::cout << "SETCMD_NETCFG" << std::endl;
            // 拷贝 网卡名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            curStatus = 0;
            int l_netId = getNetNameToId_(p_pcBufCMD, l_iSubscript);
            if (l_netId != -1)
            {
                s_NetCfg l_stNetCfg = c_masterParamPtr->m_net[l_netId];
                curStatus = setNetInfo_(p_pcBufCMD, l_iSubscript, c_masterParamPtr->m_net[l_netId]);
                if (curStatus)
                    curStatus = c_setActionCb_(4, l_netId);
                if (!curStatus)
                    c_masterParamPtr->m_net[l_netId] = l_stNetCfg;
            }
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_NETRESET:
        {
            std::cout << "SETCMD_NETRESET" << std::endl;
            // 拷贝 网卡名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_netId = getNetNameToId_(p_pcBufCMD, l_iSubscript);
            if (l_netId != -1)
                curStatus = c_setActionCb_(5, l_netId);
            else
                curStatus = 0;
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_SLAMCONTROL:
        {
            std::cout << "SETCMD_SLAMCONTROL: " << std::endl;
            p_pcBufResponse[l_iResLen++] = p_pcBufCMD[l_iSubscript];
            sendRecvSuccCMD_(p_pcBufResponse, l_iResLen, 2);
            if (!c_bSlamControlFlag_)
            {
                c_bSlamControlFlag_ = true;
                std::thread l_setslamcontrol = std::thread(&WebProtocol::setSlamControl_,
                                                           this,
                                                           p_pcBufCMD[l_iSubscript],
                                                           p_pcBufResponse,
                                                           l_iResLen);
                l_setslamcontrol.detach();
            }
            return;
        }
        case WEBCMDID::SETCMD_SECRETKEY:
        {
            std::cout << "SETCMD_SECRETKEY" << std::endl;
            curStatus = asciiToString_(p_pcBufCMD, l_iSubscript, c_masterParamPtr->m_sMacNo);
            if (curStatus)
                curStatus = c_setActionCb_(9, 0);
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_DELETELASER:
        {
            std::cout << "SETCMD_DELETELASER" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            if (!c_masterParamPtr->m_slam->m_bIsOnlineMode)
            {
                p_pcBufResponse[l_iResLen++] = 0;
                LOGWEB(WERROR, "{} Delete Laser Fail: Not OnlineMode", WJLog::getWholeSysTime());
            }
            else
            {
                int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
                if (l_iLidarId != -1)
                {
                    curStatus = c_setActionCb_(15, l_iLidarId);
                    if (curStatus)
                    {
                        // 删除后不重新排id 原因：id只是标识符 内部使用id为vector的size
                        c_masterParamPtr->m_slam->m_lidar.erase(
                            c_masterParamPtr->m_slam->m_lidar.begin() + l_iLidarId);
                        p_pcBufResponse[l_iResLen++] = 1;
                    }
                    else
                        p_pcBufResponse[l_iResLen++] = 0;
                }
                else
                    p_pcBufResponse[l_iResLen++] = 0;
            }
            break;
        }
        case WEBCMDID::SETCMD_ADDLASER:
        {
            std::cout << "SETCMD_ADDLASER" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            curStatus = 0;
            s_LidarConfig l_addLidar;
            if (!c_masterParamPtr->m_slam->m_bIsOnlineMode)
                LOGWEB(WERROR, "{} Add Laser Fail: Not OnlineMode", WJLog::getWholeSysTime());
            else
            {
                if (getNewLidarName_(p_pcBufCMD, l_iSubscript, l_addLidar.m_sLaserName)
                    && asciiToString_(p_pcBufCMD, l_iSubscript, l_addLidar.m_dev.m_sDevType))
                {
                    curStatus =
                        (asciiToIP(p_pcBufCMD, l_iSubscript, l_addLidar.m_dev.m_sDevIP)
                         && bytes16ToInt(p_pcBufCMD, l_iSubscript, l_addLidar.m_dev.m_uiDevPort)
                         && asciiToIP(p_pcBufCMD, l_iSubscript, l_addLidar.m_dev.m_sLocalIP)
                         && bytes16ToInt(p_pcBufCMD, l_iSubscript, l_addLidar.m_dev.m_uiLocalPort));
                    l_addLidar.log();
                }
                else
                    LOGWEB(WERROR, "{} Add Laser Err", WJLog::getWholeSysTime());
                if (curStatus)
                {
                    c_masterParamPtr->m_slam->m_lidar.push_back(l_addLidar);
                    curStatus = c_setActionCb_(14, c_masterParamPtr->m_slam->m_lidar.size() - 1);
                    if (!curStatus)
                        c_masterParamPtr->m_slam->m_lidar.pop_back();
                    else
                        LOGWEB(WINFO, "{} Add Laser Succ", WJLog::getWholeSysTime());
                }
                else
                    LOGWEB(WINFO, "{} Add Laser Fail", WJLog::getWholeSysTime());
            }
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_SAVEPARAM:
        {
            std::cout << "SETCMD_SAVEPARAM" << std::endl;
            curStatus = c_setActionCb_(13, 0);
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_AGVNET:
        {
            std::cout << "SETCMD_AGVNET" << std::endl;
            std::string l_sNetName = "";
            if (asciiToString_(p_pcBufCMD, l_iSubscript, l_sNetName))
            {
                for (uint32_t i = 0; i < c_masterParamPtr->m_net.size(); i++)
                {
                    if (l_sNetName == c_masterParamPtr->m_net[i].m_sNetName)
                    {
                        c_masterParamPtr->m_slam->m_agv.m_dev.m_sNetName = l_sNetName;
                        p_pcBufResponse[l_iResLen++] = 1;
                        break;
                    }
                    else if (i == c_masterParamPtr->m_net.size() - 1)
                        p_pcBufResponse[l_iResLen++] = 0;
                }
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case WEBCMDID::SETCMD_LASERNET:
        {
            std::cout << "SETCMD_LASERNET" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                std::string l_sNetName = "";
                // 解析
                if (asciiToString_(p_pcBufCMD, l_iSubscript, l_sNetName))
                {
                    // 设置的雷达网卡和网卡List对比，满足之一则使用
                    for (uint32_t i = 0; i < c_masterParamPtr->m_net.size(); i++)
                    {
                        if (l_sNetName == c_masterParamPtr->m_net[i].m_sNetName)
                        {
                            c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sNetName =
                                l_sNetName;
                            p_pcBufResponse[l_iResLen++] = 1;
                            break;
                        }
                        else if (i == c_masterParamPtr->m_net.size() - 1)
                            p_pcBufResponse[l_iResLen++] = 0;
                    }
                }
                else
                    p_pcBufResponse[l_iResLen++] = 0;
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case WEBCMDID::SETCMD_LOGPATH:
        {
            std::cout << "SETCMD_LOGPATH" << std::endl;
            std::string l_sLogPath;
            curStatus = asciiToString_(p_pcBufCMD, l_iSubscript, l_sLogPath);
            if (curStatus)
                curStatus = isExistFileOrFolder(l_sLogPath);
            if (curStatus)
            {
                if (l_sLogPath[l_sLogPath.length() - 1] != '/')
                    l_sLogPath += "/";
                c_masterParamPtr->m_slam->m_fae.m_sLogPath = l_sLogPath;
            }
            p_pcBufResponse[l_iResLen++] = curStatus;
            break;
        }
        case WEBCMDID::SETCMD_RESETPOSEUI:
        {
            std::cout << "SETCMD_RESETPOSEUI" << std::endl;
            float l_fPoseX, l_fPoseY, l_fPoseZ, l_fPoseR, l_fPoseP, l_fPoseA;

            bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseX);
            bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseY);
            bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseZ);
            bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseR);
            bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseP);
            bytes32ToFloat(p_pcBufCMD, l_iSubscript, l_fPoseA);

            c_masterParamPtr->m_slam->m_pos.m_stUIPose.setX(l_fPoseX);
            c_masterParamPtr->m_slam->m_pos.m_stUIPose.setY(l_fPoseY);
            c_masterParamPtr->m_slam->m_pos.m_stUIPose.setZ(l_fPoseZ);
            c_masterParamPtr->m_slam->m_pos.m_stUIPose.setRPY(l_fPoseR, l_fPoseP, l_fPoseA);
            curStatus = c_setActionCb_(17, 0);
            p_pcBufResponse[l_iResLen++] = curStatus;
        }
        case WEBCMDID::QUERYCMD_DRIVERMODE:
        {
            std::cout << "QUERYCMD_DRIVERMODE" << std::endl;
            p_pcBufResponse[l_iResLen++] = c_masterParamPtr->m_slam->m_bIsOnlineMode;
            break;
        }
        case WEBCMDID::QUERYCMD_MAPLIST:
        {
            std::cout << "QUERYCMD_MAPLIST" << std::endl;
            fillMapList_(
                c_masterParamPtr->m_slam->m_sPkgPath + "/data/Map/", p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_LASERPCAPNAME:
        {
            std::cout << "WEB_QUERYCMD_LASERPCAPNAME" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                stringToAscii_(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sPcapName,
                               p_pcBufResponse,
                               l_iResLen);
                p_pcBufResponse[l_iResLen] = 1;
            }
            else
                p_pcBufResponse[l_iResLen] = 0;
            break;
        }
        case WEBCMDID::QUERYCMD_AGVPCAPNAME:
        {
            std::cout << "QUERYCMD_AGVPCAPNAME" << std::endl;
            stringToAscii_(
                c_masterParamPtr->m_slam->m_agv.m_dev.m_sPcapName, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_PLAYBAGSTATUS:
        {
            std::cout << "QUERYCMD_PLAYBAGSTATUS" << std::endl;
            if (c_masterParamPtr->m_slam->m_bIsOnlineMode)
                c_masterParamPtr->m_bIsStart = false;
            p_pcBufResponse[l_iResLen++] = c_masterParamPtr->m_bIsStart;
            break;
        }
        case WEBCMDID::QUERYCMD_DRIVERPLAYRATE:
        {
            std::cout << "QUERYCMD_DRIVERPLAYRATE" << std::endl;
            p_pcBufResponse[l_iResLen++] = c_masterParamPtr->m_uiPlayBagRate;
            break;
        }
        case WEBCMDID::QUERYCMD_PCAPLIST:
        {
            std::cout << "QUERYCMD_PCAPLIST" << std::endl;
            fillFileList_(
                c_masterParamPtr->m_sOffLineDataPath, "pcapng", p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_RECORDDATA:
        {
            std::cout << "QUERYCMD_RECORDDATA" << std::endl;
            p_pcBufResponse[l_iResLen++] = c_setActionCb_(6, 0);
            c_setActionCb_(3, 0);
            fillIntTo32Bytes(c_masterParamPtr->m_iRecordDuration, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_RECORDTIMEINTERVAL:
        {
            std::cout << "QUERYCMD_RECORDTIMEINTERVAL" << std::endl;
            fillIntTo16Bytes(c_masterParamPtr->m_uiRecordTimeInterval, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_NETLIST:
        {
            std::cout << "QUERYCMD_NETLIST" << std::endl;
            p_pcBufResponse[l_iResLen++] = c_masterParamPtr->m_net.size();
            for (uint32_t i = 0; i < c_masterParamPtr->m_net.size(); i++)
                stringToAscii_(c_masterParamPtr->m_net[i].m_sNetName, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_NETCFG:
        {
            std::cout << "QUERYCMD_NETCFG" << std::endl;
            // 拷贝 网卡名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_netId = getNetNameToId_(p_pcBufCMD, l_iSubscript);
            if (l_netId != -1)
                fillNetInfo_(p_pcBufResponse, l_iResLen, l_netId);
            else
            {
                // 查询失败
                p_pcBufCMD[l_iSubscript++] = 0;
            }
            break;
        }
        case WEBCMDID::QUERYCMD_SAVEPOSE:
        {
            std::cout << "QUERYCMD_SAVEPOSE" << std::endl;
            s_POSE6D l_readPose;
            bool l_bRead = false;
            l_bRead = readPoseInFile_(l_readPose,
                                      c_masterParamPtr->m_slam->m_sPkgPath + "/data/Pose/pose.csv");
            if (!l_bRead)
                l_bRead = readPoseInFile_(
                    l_readPose, c_masterParamPtr->m_slam->m_sPkgPath + "/data/Pose/pose_bak.csv");
            p_pcBufResponse[l_iResLen++] = l_bRead;
            if (l_bRead)
            {
                fillFloatTo32Bytes(l_readPose.x(), p_pcBufResponse, l_iResLen);
                fillFloatTo32Bytes(l_readPose.y(), p_pcBufResponse, l_iResLen);
                fillFloatTo32Bytes(l_readPose.z(), p_pcBufResponse, l_iResLen);
                fillFloatTo32Bytes(l_readPose.roll(), p_pcBufResponse, l_iResLen);
                fillFloatTo32Bytes(l_readPose.pitch(), p_pcBufResponse, l_iResLen);
                fillFloatTo32Bytes(l_readPose.yaw(), p_pcBufResponse, l_iResLen);
            }
            break;
        }
        case WEBCMDID::QUERYCMD_SLAMSTATE:
        {
            std::cout << "QUERYCMD_SLAMSTATE" << std::endl;
            p_pcBufResponse[l_iResLen++] = (int)c_masterParamPtr->m_slam->m_RunStatus;
            break;
        }
        case WEBCMDID::QUERYCMD_SECRETKEY:
        {
            std::cout << "QUERYCMD_SECRETKEY" << std::endl;
            curStatus = c_setActionCb_(10, 0);
            p_pcBufResponse[l_iResLen++] = curStatus;
            if (!curStatus)
                stringToAscii_(c_masterParamPtr->m_sSecretNo, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_MASTERSTATUS:
        {
            std::cout << "QUERYCMD_MASTERSTATUS" << std::endl;
            p_pcBufResponse[l_iResLen++] = 1;
            break;
        }
        case WEBCMDID::QUERYCMD_LASERNET:
        {
            std::cout << "QUERYCMD_LASERNET" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                stringToAscii_(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sNetName,
                               p_pcBufResponse,
                               l_iResLen);
                p_pcBufResponse[l_iResLen] = 1;
            }
            else
                p_pcBufResponse[l_iResLen] = 0;
            break;
        }
        case WEBCMDID::QUERYCMD_AGVNET:
        {
            std::cout << "QUERYCMD_AGVNET" << std::endl;
            stringToAscii_(
                c_masterParamPtr->m_slam->m_agv.m_dev.m_sNetName, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_LOGPATH:
        {
            std::cout << "QUERYCMD_LOGPATH" << std::endl;
            stringToAscii_(c_masterParamPtr->m_slam->m_fae.m_sLogPath, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_ERRORCODE:
        {
            std::cout << "QUERYCMD_ERRORCODE" << std::endl;
            stringToAscii_(
                c_masterParamPtr->m_slam->m_fae.m_sErrorCode, p_pcBufResponse, l_iResLen);
            break;
        }
        case WEBCMDID::QUERYCMD_LASERSTATE:
        {
            // std::cout << "QUERYCMD_LASERSTATE" << std::endl;
            int res = true;
            for (int i = 0; i < c_masterParamPtr->m_slam->m_iLidarNum; i++)
            {
                if (c_masterParamPtr->m_slam->m_lidar[i].m_bEnable == false)
                {
                    continue;
                }
                bool l_bDevS = false;
                if (c_masterParamPtr->m_slam->m_devList[i]->m_status == DevStatus::DEVCONNECT
                    || c_masterParamPtr->m_slam->m_devList[i]->m_status == DevStatus::PCAPSTOP
                    || c_masterParamPtr->m_slam->m_devList[i]->m_status == DevStatus::PCAPRUN)
                {
                    l_bDevS = true;
                }
                res = res && l_bDevS;
                p_pcBufResponse[l_iResLen++] = res;
            }
            break;
        }
        default:
            LOGFAE(WERROR,
                   "Master协议异常: 未定义 {:#X} - {:#X}, 请联系万集人员进行检查!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("B2");
            break;
    }
    if (l_iResLen > 26)
    {
        fillAskField(p_pcBufResponse, l_iResLen);
        calcWebProtocolTail_(p_pcBufResponse, l_iResLen);
        c_sendRosCb_(p_pcBufResponse, l_iResLen);
    }
    else
        LOGWEB(WERROR, "{} Master Prot Tail Len Err: {}", WJLog::getWholeSysTime(), l_iResLen);
}

/**
 * @description: 设备实时生效协议处理
 * @param {u_char*} p_pcBufCMD 协议内容 区别与wj协议 带帧头帧尾
 * @param {int} p_iBufLen 协议长度
 * @param {char*} p_pcBufResponse 发送内容
 * @return {*}
 * @other: 设置帧-解析保存至备份参数中 透传发送给SLAM端   回复后 确认成功后将备份参数同步至主结构体
 * 并将回复帧给Web 查询帧-透传 回复后更新结构体 并转发至Web端
 */
void WebProtocol::selectDeviceProtocol(u_char* p_pcBufCMD, int p_iBufLen, char* p_pcBufResponse)
{
    int l_iSta = 11;
    int l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    int l_iSubscript = l_iSta + 15;
    int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
    if (l_iLidarId == -1)
        return;

    // 拷贝完整协议用于转发
    memcpy(&p_pcBufResponse[0], p_pcBufCMD, p_iBufLen);
    switch (l_iSonCMDID)
    {
        case DEVCMDID::SETCMD_LASERINSTALLXYZ:
        {
            std::cout << "WEB_SETCMD_LASERINSTALLXYZ: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_LASERINSTALLANGLE:
        {
            std::cout << "WEB_SETCMD_LASERINSTALLANGLE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_LASERDISBLIND:
        {
            std::cout << "WEB_SETCMD_LASERDISBLIND: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_LASERANGBLIND:
        {
            std::cout << "WEB_SETCMD_LASERANGBLIND: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_USEFLOOR:
        {
            std::cout << "WEB_SETCMD_USEFLOOR: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_LASERHIGHT:
        {
            std::cout << "WEB_SETCMD_LASERHIGHT: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_MARKMODE:
        {
            std::cout << "WEB_SETCMD_MARKMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_HORIZONTALCALIBRATION:
        {
            std::cout << "WEB_SETCMD_HORIZONTALCALIBRATION: " << (int)p_pcBufCMD[l_iSta]
                      << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_MULLASERCALIBRATION:
        {
            std::cout << "WEB_SETCMD_MULLASERCALIBRATION: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_HORIZONTALCALIBNUM:
        {
            std::cout << "WEB_SETCMD_HORIZONTALCALIBNUM: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_MULLASERCALIBRATIONINI:
        {
            std::cout << "WEB_SETCMD_MULLASERCALIBRATIONINI: " << (int)p_pcBufCMD[l_iSta]
                      << std::endl;
            break;
        }
        case DEVCMDID::SETCMD_LASERENABLE:
        {
            std::cout << "WEB_SETCMD_LASERENABLE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERINSTALLXYZ:
        {
            std::cout << "WEB_QUERYCMD_LASERINSTALLXYZ: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERINSTALLANGLE:
        {
            std::cout << "WEB_QUERYCMD_LASERINSTALLANGLE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERDISBLIND:
        {
            std::cout << "WEB_QUERYCMD_LASERDISBLIND: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERANGBLIND:
        {
            std::cout << "WEB_QUERYCMD_LASERANGBLIND: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_USEFLOOR:
        {
            std::cout << "WEB_QUERYCMD_USEFLOOR: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERHIGHT:
        {
            std::cout << "WEB_QUERYCMD_LASERHIGHT: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_MARKMODE:
        {
            std::cout << "WEB_QUERYCMD_MARKMODE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_HORIZONTALCALIBRATION:
        {
            std::cout << "WEB_QUERYCMD_HORIZONTALCALIBRATION: " << (int)p_pcBufCMD[l_iSta]
                      << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_MULLASERCALIBRATION:
        {
            std::cout << "WEB_QUERYCMD_MULLASERCALIBRATION: " << (int)p_pcBufCMD[l_iSta]
                      << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_LIDARSTATUS:
        {
            std::cout << "WEB_QUERYCMD_LIDARSTATUS: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_HORIZONTALCALIBNUM:
        {
            std::cout << "WEB_QUERYCMD_HORIZONTALCALIBNUM: " << (int)p_pcBufCMD[l_iSta]
                      << std::endl;
            break;
        }
        case DEVCMDID::QUERYCMD_LASERENABLE:
        {
            std::cout << "WEB_QUERYCMD_LASERENABLE: " << (int)p_pcBufCMD[l_iSta] << std::endl;
            break;
        }
        default:
        {
            LOGFAE(WERROR,
                   "Dev协议异常: 未定义 {:#X} - {:#X}, 请联系万集人员进行检查!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("B3");
            return;
        }
    }
    if (getFrameType(p_pcBufCMD[l_iSta]) == FRAMETYPE::SETQUERY)
        c_sendTcpCb_(p_pcBufResponse, p_iBufLen);
    else if (getFrameType(p_pcBufCMD[l_iSta]) == FRAMETYPE::REPLY)
        c_sendRosCb_(p_pcBufResponse, p_iBufLen);
}

/**
 * @description: 参数协议处理 无法实时生效  且须上位机执行保存后重启生效
 * @param {u_char*} p_pcBufCMD 协议内容 区别与wj协议 带帧头帧尾
 * @param {int} p_iBufLen 协议长度
 * @param {char*} p_pcBufResponse 发送内容
 * @return {*}
 * @other: 设置帧-解析保存至参数 并直接回复Web
 *                    查询帧-直接回复Web
 */
void WebProtocol::selectParamProtocol(u_char* p_pcBufCMD, int p_iBufLen, char* p_pcBufResponse)
{
    int l_iSta = 11;  // 区别与wj协议
    int l_iResLen = 0;
    int curStatus = 0;
    int l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    int l_iSubscript = l_iSta + 15;
    std::vector<std::string> l_vsTmp;
    calcWebProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iResLen);
    switch (l_iSonCMDID)
    {
        case PARAMCMDID::SETCMD_CLOCKSOURCE:
        {
            std::cout << "WEB_SETCMD_CLOCKSOURCE" << std::endl;
            c_masterParamPtr->m_slam->m_time.setTimeSource(TimeSource(p_pcBufCMD[l_iSubscript]));
            c_masterParamPtr->m_slam->m_time.resetSensorBaseTime(false);
            p_pcBufResponse[l_iResLen++] = 1;
            break;
        }
        case PARAMCMDID::SETCMD_AGVNETPARAM:
        {
            std::cout << "WEB_SETCMD_AGVNETPARAM" << std::endl;
            if (!c_masterParamPtr->m_slam->m_bIsOnlineMode)
                p_pcBufResponse[l_iResLen++] = 0;
            else
                p_pcBufResponse[l_iResLen++] =
                    (asciiToIP(
                         p_pcBufCMD, l_iSubscript, c_masterParamPtr->m_slam->m_agv.m_dev.m_sLocalIP)
                     && bytes16ToInt(p_pcBufCMD,
                                     l_iSubscript,
                                     c_masterParamPtr->m_slam->m_agv.m_dev.m_uiLocalPort));
            break;
        }
        case PARAMCMDID::SETCMD_LASERNETPARAM:
        {
            std::cout << "WEB_SETCMD_LASERNETPARAM" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (c_masterParamPtr->m_slam->m_bIsOnlineMode && l_iLidarId != -1)
            {
                // 解析
                p_pcBufResponse[l_iResLen++] =
                    (asciiToIP(p_pcBufCMD,
                               l_iSubscript,
                               c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sDevIP)
                     && bytes16ToInt(
                            p_pcBufCMD,
                            l_iSubscript,
                            c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_uiDevPort)
                     && asciiToIP(p_pcBufCMD,
                                  l_iSubscript,
                                  c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sLocalIP)
                     && bytes16ToInt(
                            p_pcBufCMD,
                            l_iSubscript,
                            c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_uiLocalPort));
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case PARAMCMDID::SETCMD_LASERBASEPARAM:
        {
            std::cout << "WEB_SETCMD_LASERBASEPARAM" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                // 解析
                p_pcBufResponse[l_iResLen++] =
                    (asciiToString_(p_pcBufCMD,
                                    l_iSubscript,
                                    c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_sLaserSN)
                     && asciiToString_(
                            p_pcBufCMD,
                            l_iSubscript,
                            c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sDevType));
                // ToDoList 验证雷达类型是否支持
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case PARAMCMDID::SETCMD_LOADDEFAULTPARAM:
        {
            std::cout << "SETCMD_LOADDEFAULTPARAM" << std::endl;
            // 确保出厂配置存在且有效时删除用户配置
            if (c_setActionCb_(8, 0))
            {
                bool l_bDeleteSign = true;
                deleteFileOrDir(c_masterParamPtr->m_slam->m_sPkgPath + "/config/.user.yaml.bak");
                if (isExistFileOrFolder(c_masterParamPtr->m_slam->m_sPkgPath + "/config/user.yaml")
                    || isExistFileOrFolder(c_masterParamPtr->m_slam->m_sPkgPath
                                           + "/config/logCfg.yaml"))
                {
                    if (isExistFileOrFolder(c_masterParamPtr->m_slam->m_sPkgPath
                                            + "/config/user.yaml"))
                        l_bDeleteSign = deleteFileOrDir(c_masterParamPtr->m_slam->m_sPkgPath
                                                        + "/config/user.yaml");
                    if (isExistFileOrFolder(c_masterParamPtr->m_slam->m_sPkgPath
                                            + "/config/logCfg.yaml"))
                        l_bDeleteSign = l_bDeleteSign
                                        && deleteFileOrDir(c_masterParamPtr->m_slam->m_sPkgPath
                                                           + "/config/logCfg.yaml");
                    // 拷贝default
                    copyFileOrFolder(c_masterParamPtr->m_slam->m_sPkgPath + "/config/default.yaml",
                                     c_masterParamPtr->m_slam->m_sPkgPath + "/config/user.yaml");
                    p_pcBufResponse[l_iResLen++] = (int)l_bDeleteSign;
                }
                else
                    p_pcBufResponse[l_iResLen++] = 1;
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case PARAMCMDID::SETCMD_LASERISBASE:
        {
            std::cout << "WEB_SETCMD_LASERISBASE: " << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1 && !c_masterParamPtr->m_slam->m_calib.m_MLCalib.m_bIsStartCalib)
            {
                // 若设置为基准雷达，自动将其他雷达设置为否
                if (p_pcBufCMD[l_iSubscript])
                    for (uint32_t i = 0; i < c_masterParamPtr->m_slam->m_lidar.size(); i++)
                        c_masterParamPtr->m_slam->m_lidar[i].m_bIsBaseLaser = false;
                c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_bIsBaseLaser =
                    (bool)p_pcBufCMD[l_iSubscript];
                p_pcBufResponse[l_iResLen++] = 1;
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case PARAMCMDID::QUERYCMD_CLOCKSOURCE:
        {
            std::cout << "WEB_QUERYCMD_CLOCKSOURCE" << std::endl;
            p_pcBufResponse[l_iResLen++] = (int)c_masterParamPtr->m_slam->m_time.timeSource();
            break;
        }
        case PARAMCMDID::QUERYCMD_LASERNETPARAM:
        {
            std::cout << "WEB_QUERYCMD_LASERNETPARAM" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;

            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                p_pcBufResponse[l_iResLen++] = 1;
                ipToAscii(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sDevIP,
                          p_pcBufResponse,
                          l_iResLen);
                fillIntTo16Bytes(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_uiDevPort,
                                 p_pcBufResponse,
                                 l_iResLen);
                ipToAscii(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sLocalIP,
                          p_pcBufResponse,
                          l_iResLen);
                fillIntTo16Bytes(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_uiLocalPort,
                                 p_pcBufResponse,
                                 l_iResLen);
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case PARAMCMDID::QUERYCMD_LASERBASEPARAM:
        {
            std::cout << "WEB_QUERYCMD_LASERBASEPARAM" << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                p_pcBufResponse[l_iResLen++] = 1;
                stringToAscii_(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_sLaserSN,
                               p_pcBufResponse,
                               l_iResLen);
                stringToAscii_(c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_dev.m_sDevType,
                               p_pcBufResponse,
                               l_iResLen);
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        case PARAMCMDID::QUERYCMD_AGVNETPARAM:
        {
            std::cout << "QUERYCMD_AGVNETPARAM" << std::endl;
            ipToAscii(c_masterParamPtr->m_slam->m_agv.m_dev.m_sLocalIP, p_pcBufResponse, l_iResLen);
            fillIntTo16Bytes(
                c_masterParamPtr->m_slam->m_agv.m_dev.m_uiLocalPort, p_pcBufResponse, l_iResLen);
            break;
        }
        case PARAMCMDID::QUERYCMD_LIDARNUM:
        {
            std::cout << "QUERYCMD_LIDARNUM" << std::endl;
            p_pcBufResponse[l_iResLen++] = (int)(c_masterParamPtr->m_slam->m_lidar.size());
            for (int i = 0; i < (int)(c_masterParamPtr->m_slam->m_lidar.size()); i++)
            {
                stringToAscii_(
                    c_masterParamPtr->m_slam->m_lidar[i].m_sLaserName, p_pcBufResponse, l_iResLen);
            }
            break;
        }
        case PARAMCMDID::QUERYCMD_LASERISBASE:
        {
            std::cout << "WEB_QUERYCMD_LASERISBASE: " << std::endl;
            // 拷贝 雷达名称len+名称
            memcpy(&p_pcBufResponse[l_iResLen],
                   &p_pcBufCMD[l_iSubscript],
                   p_pcBufCMD[l_iSubscript] + 1);
            l_iResLen += p_pcBufCMD[l_iSubscript] + 1;
            int l_iLidarId = getLidarId_(p_pcBufCMD, l_iSubscript);
            if (l_iLidarId != -1)
            {
                p_pcBufResponse[l_iResLen++] = 1;
                p_pcBufResponse[l_iResLen++] =
                    (int)c_masterParamPtr->m_slam->m_lidar[l_iLidarId].m_bIsBaseLaser;
            }
            else
                p_pcBufResponse[l_iResLen++] = 0;
            break;
        }
        default:
            LOGFAE(WERROR,
                   "Param协议异常: 未定义 {:#X} - {:#X}, 请联系万集人员进行检查!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("B4");
            break;
    }
    if (l_iResLen > 26)
    {
        fillAskField(p_pcBufResponse, l_iResLen);
        calcWebProtocolTail_(p_pcBufResponse, l_iResLen);
        c_sendRosCb_(p_pcBufResponse, l_iResLen);
    }
    else
        LOGWEB(WERROR, "{} Param Prot Len Err: {}", WJLog::getWholeSysTime(), l_iResLen);
}

/**
 * @description: 参数协议处理 无法实时生效  且须上位机执行保存后重启生效
 * @param {u_char*} p_pcBufCMD 协议内容 区别与wj协议 带帧头帧尾
 * @param {int} p_iBufLen 协议长度
 * @param {char*} p_pcBufResponse 发送内容
 * @return {*}
 * @other: 设置帧-解析保存至参数 并直接回复Web
 *                    查询帧-直接回复Web
 */
void WebProtocol::selectPrivateProtocol(u_char* p_pcBufCMD, int p_iBufLen, char* p_pcBufResponse)
{
    int l_iSta = 11;  // 区别与wj协议
    int l_iResLen = 0;
    int l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
    int l_iSubscript = l_iSta + 15;
    calcWebProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iResLen);
    switch (l_iSonCMDID)
    {
        case PRVTCMDID::SETCMD_DEBUGMODE: { break;
        }
        case PRVTCMDID::SETCMD_RESETENABLELASER: { break;
        }
        case PRVTCMDID::QUERYCMD_DEBUGMODE: { break;
        }
        case PRVTCMDID::QUERYCMD_SLAMSTATUS:
        {
            std::cout << "WEB_QUERYCMD_SLAMSTATUS: " << (int)p_pcBufCMD[l_iSubscript] << std::endl;
            // 废旧代码
            if (getFrameType(p_pcBufCMD[l_iSta]) == FRAMETYPE::REPLY)
            {
                switch (p_pcBufCMD[l_iSubscript])
                {
                    case 0: c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::NOSTART; break;
                    case 1: c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STARTING; break;
                    case 2: c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::RUNNING; break;
                    case 3: c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STOPING; break;
                    case 4: c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STARTERR; break;
                    case 5: c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STOPERR; break;
                    default: break;
                }
            }
            break;
        }
        case PRVTCMDID::QUERYCMD_WEBPOSE:
        {
            // std::cout << "WEB_QUERYCMD_WEBPOSE: " <<  std::endl;
            if (getFrameType(p_pcBufCMD[l_iSta]) == FRAMETYPE::REPLY)
            {
                // 重新拷贝完整数据 发送Web
                memcpy(&p_pcBufResponse[0], p_pcBufCMD, p_iBufLen);
                c_sendRosCb_(p_pcBufResponse, p_iBufLen);
            }
            break;
        }
        default:
            LOGFAE(WERROR,
                   "Prvt协议异常: 未定义 {:#X} - {:#X}, 请联系万集人员进行检查!",
                   p_pcBufCMD[l_iSta + 11],
                   p_pcBufCMD[l_iSta + 12]);
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("B5");
            break;
    }
}

/**
 * @description: 根据协议删除某类型的数据
 * @param {u_char*&} p_pcBufCMD 完整协议
 * @param {int} p_iLen ascii偏移量
 * @param {std::string} p_sPath 文件/文件夹上一级目录
 * @param {std::string} p_sFileTail 文件后缀  eg .pcapng .bag
 * @return {bool} true 是否删除成功
 * @other:
 */
bool WebProtocol::deleteData_(u_char*& p_pucBufCMD,
                              int p_iLen,
                              std::string p_sPath,
                              std::string p_sFileTail)
{
    bool l_bStatus = false;
    std::string l_sDeleteFileName;
    if (asciiToString_(p_pucBufCMD, p_iLen, l_sDeleteFileName))
        l_bStatus = deleteFileOrDir(p_sPath + l_sDeleteFileName + p_sFileTail);
    return l_bStatus;
}

/**
 * @description: 填充某目录下 某后缀格式文件列表
 * @param {std::string } p_sFindPath 搜寻目录
 * @param {std::string } p_sFileTail 文件后缀标志 eg pcapng/bag 不带. ""意味着文件夹
 * @param {char*} p_pcBuf
 * @param {int&} p_iLen
 * @return {*}
 * @other:
 */
void WebProtocol::fillFileList_(std::string p_sFindPath,
                                std::string p_sFileTail,
                                char* p_pcBuf,
                                int& p_iLen)
{
    std::vector<std::string> l_vsFileList;
    // 确保路径正确 含/
    std::string l_sFindPath = p_sFindPath;
    if ('/' != l_sFindPath[l_sFindPath.length() - 1])
        l_sFindPath += "/";

    std::string l_sFileTail = "";
    int l_iFileNumOffset = p_iLen;
    int l_iFileNum = 0;
    p_iLen += 1;

    if (p_sFileTail != "")
        findFileInFolder_(l_sFindPath, p_sFileTail, l_vsFileList);
    else
        findFolderInFolder_(l_sFindPath, l_vsFileList);

    for (uint32_t i = 0; i < l_vsFileList.size(); i++)
    {
        if (l_iFileNum > 255)
            break;
        std::string l_sFileName = fileFilterTail_(l_vsFileList[i], "." + p_sFileTail);
        // 限制单个string长度和整条协议最大长度
        if (strlen(l_sFileName.c_str()) > 255)
            continue;
        // printf("file: %s| %d | %d\n",
        //        l_sFileName.c_str(),
        //        p_iLen,
        //        (int)strlen(l_sFileName.c_str()));
        if (p_iLen + (int)strlen(l_sFileName.c_str()) > m_iTCPDataMaxLen_)
            break;
        stringToAscii_(l_sFileName, p_pcBuf, p_iLen);
        l_iFileNum++;
    }
    // 填写真实文件数量
    p_pcBuf[l_iFileNumOffset] = l_iFileNum;
}

/**
 * @description: 填充某目录下Map文件列表 并添加地图分辨率 目前写死
 * @param {std::string } p_sFindPath 搜寻目录
 * @param {char*} p_pcBuf
 * @param {int&} p_iLen
 * @return {*}
 * @other: 注意：该函数针对Map做了特殊化处理
 */
void WebProtocol::fillMapList_(std::string p_sFindPath, char* p_pcBuf, int& p_iLen)
{
    std::vector<std::string> l_vsFileList;

    // 确保路径正确 含/
    std::string l_sFindPath = p_sFindPath;
    if ('/' != l_sFindPath[l_sFindPath.length() - 1])
        l_sFindPath += "/";

    std::string l_sFileTail = "";
    int l_iFileNumOffset = p_iLen;
    int l_iFileNum = 0;
    p_iLen += 1;

    findFolderInFolder_(l_sFindPath, l_vsFileList);

    for (uint32_t i = 0; i < l_vsFileList.size(); i++)
    {
        if (l_iFileNum > 255)
            break;
        // 限制单个string长度和整条协议最大长度
        if (strlen(l_vsFileList[i].c_str()) > 255)
            continue;
        if (p_iLen + (int)strlen(l_vsFileList[i].c_str()) > m_iTCPDataMaxLen_)
            break;
        // // 地图文件夹增加对内部文件的判断 .同名pcd + .同名.wj
        // std::string l_sFilePcd = l_sFindPath + l_vsFileList[i] + "/" + l_vsFileList[i] + ".pcd";
        // if (access(l_sFilePcd.c_str(), F_OK) == -1)
        //     continue;
        // l_sFilePcd = l_sFindPath + l_vsFileList[i] + "/" + l_vsFileList[i] + "_2d.pcd";
        // if (access(l_sFilePcd.c_str(), F_OK) == -1)
        //     continue;
        std::string l_sFileWj = l_sFindPath + l_vsFileList[i] + "/" + l_vsFileList[i] + ".wj";
        if (access(l_sFileWj.c_str(), F_OK) == -1)
            continue;

        stringToAscii_(l_vsFileList[i], p_pcBuf, p_iLen);
        fillIntTo16Bytes(100, p_pcBuf, p_iLen);
        l_iFileNum++;
    }
    // 填写真实文件数量
    p_pcBuf[l_iFileNumOffset] = l_iFileNum;
}

/**
 * @description: 提取网卡名转换id
 * @param {u_char*&} p_pucBufCMD
 * @param {int} p_iLen
 * @return {*}
 * @other:
 */
int WebProtocol::getNetNameToId_(u_char*& p_pucBufCMD, int& p_iLen)
{
    std::string l_sNetName = "";
    if (!asciiToString_(p_pucBufCMD, p_iLen, l_sNetName))
        return -1;
    for (int i = 0; i < (int)c_masterParamPtr->m_net.size(); i++)
    {
        if (c_masterParamPtr->m_net[i].m_sNetName == l_sNetName)
            return i;
    }
    return -1;
}

/**
 * @description: 设置某id网卡的网络配置
 * @param {u_char*&} p_pucBufCMD
 * @param {int&} p_iLen 偏移量 外部已提取完毕网卡名
 * @param {s_NetCfg&} p_sNetInfo 待填充结构体
 * @return {*}
 * @other:
 */
bool WebProtocol::setNetInfo_(u_char*& p_pucBufCMD, int& p_iLen, s_NetCfg& p_sNetInfo)
{
    // netNameLen natName dhcp isConnectNet ip
    //                          1          N                1          1                  4

    // 从协议提取信息
    bool l_bDHCP = p_pucBufCMD[p_iLen++];
    bool l_bIsNetwork = p_pucBufCMD[p_iLen++];
    // 设定IP 不需要IP则由setNetWork和setDHCP清空 IP错误由返回值控制是否保存
    bool l_bRes = asciiToIP(p_pucBufCMD, p_iLen, p_sNetInfo.m_sIpSet);
    // 设定IP若为0.0.0.0 则为异常
    if (l_bRes && p_sNetInfo.m_sIpSet == "0.0.0.0")
        l_bRes = false;
    // 联网模式下设定DNS 非联网模式下 会清除网关、DNS
    p_sNetInfo.setNetWork(l_bIsNetwork);
    // 联网时 设定网关
    if (l_bIsNetwork)
        p_sNetInfo.setGateway();
    // DHCP模式下 会清空IP、网关、DNS
    p_sNetInfo.setDHCP(l_bDHCP);
    // 须IP的时候 IP错误则false
    if (!l_bRes && !p_sNetInfo.isDHCP)
        return false;
    return true;
}

/**
 * @description: 填充某id网卡的网络配置
 * @param {u_char*} p_pucBuf
 * @param {int&} p_iLen
 * @param {int} p_iNetid 网卡id
 * @return {*}
 * @other: DHCP模式下填充IP为实时IP 静态模式下填充为设定IP
 */
void WebProtocol::fillNetInfo_(char* p_pucBuf, int& p_iLen, int p_iNetid)
{
    // isSet dhcp isConnectNet ip
    // 1         1         1                          4
    // 该网卡是否被设置
    p_pucBuf[p_iLen++] = c_masterParamPtr->m_net[p_iNetid].isSet;

    if (c_masterParamPtr->m_net[p_iNetid].isSet)
    {
        p_pucBuf[p_iLen++] = c_masterParamPtr->m_net[p_iNetid].isDHCP;
        p_pucBuf[p_iLen++] = c_masterParamPtr->m_net[p_iNetid].isNetwork;
        // DHCP模式下发送实时IP
        if (!c_masterParamPtr->m_net[p_iNetid].isDHCP)
            ipToAscii(c_masterParamPtr->m_net[p_iNetid].m_sIpSet, p_pucBuf, p_iLen);
        else
            ipToAscii(c_masterParamPtr->m_net[p_iNetid].m_sIpRead, p_pucBuf, p_iLen);
    }
}

bool WebProtocol::setMapCorrect_(u_char*& p_pcBufCMD, int p_iOffset)
{
    float l_f64MymapX, l_f64MymapY, l_f64MymapA, l_f64TomapX, l_f64TomapY, l_f64TomapA;
    if (bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64MymapX)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64MymapY)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64MymapA)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64TomapX)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64TomapY)
        && bytes32ToFloat(p_pcBufCMD, p_iOffset, l_f64TomapA))
    {
        // 设置
        c_masterParamPtr->m_slam->m_agv.m_fMymap[0] = l_f64MymapX;
        c_masterParamPtr->m_slam->m_agv.m_fMymap[1] = l_f64MymapY;
        c_masterParamPtr->m_slam->m_agv.m_fMymap[2] = l_f64MymapA;
        c_masterParamPtr->m_slam->m_agv.m_fTomap[0] = l_f64TomapX;
        c_masterParamPtr->m_slam->m_agv.m_fTomap[1] = l_f64TomapY;
        c_masterParamPtr->m_slam->m_agv.m_fTomap[2] = l_f64TomapA;
        LOGW(WINFO,
             "{} setMapCorrect_ {:.3f} | {:.3f} | {:.3f} | {:.3f} | {:.3f} | {:.3f}.",
             WJLog::getWholeSysTime(),
             c_masterParamPtr->m_slam->m_agv.m_fMymap[0],
             c_masterParamPtr->m_slam->m_agv.m_fMymap[1],
             c_masterParamPtr->m_slam->m_agv.m_fMymap[2],
             c_masterParamPtr->m_slam->m_agv.m_fTomap[0],
             c_masterParamPtr->m_slam->m_agv.m_fTomap[1],
             c_masterParamPtr->m_slam->m_agv.m_fTomap[2]);
        double l_f64YawAngleDiff =
            (c_masterParamPtr->m_slam->m_agv.m_fTomap[2] - c_masterParamPtr->m_slam->m_agv.m_fMymap[2]) * M_PI / 180.0;
        double l_f64x = c_masterParamPtr->m_slam->m_agv.m_fMymap[0] * cos(l_f64YawAngleDiff)
                        - c_masterParamPtr->m_slam->m_agv.m_fMymap[1] * sin(l_f64YawAngleDiff);
        double l_f64y = c_masterParamPtr->m_slam->m_agv.m_fMymap[0] * sin(l_f64YawAngleDiff)
                        + c_masterParamPtr->m_slam->m_agv.m_fMymap[1] * cos(l_f64YawAngleDiff);
        s_POSE6D l_stTrans;
        l_stTrans.setRPY(0.0, 0.0, c_masterParamPtr->m_slam->m_agv.m_fTomap[2] - c_masterParamPtr->m_slam->m_agv.m_fMymap[2]);
        l_stTrans.setX(c_masterParamPtr->m_slam->m_agv.m_fTomap[0] - l_f64x);
        l_stTrans.setY(c_masterParamPtr->m_slam->m_agv.m_fTomap[1] - l_f64y);
        c_masterParamPtr->m_slam->m_agv.m_stTrans = l_stTrans;
        LOGW(WINFO,
             "{} calMapCorrect {:.3f} | {:.3f} | {:.3f}.",
             WJLog::getWholeSysTime(),
             c_masterParamPtr->m_slam->m_agv.m_stTrans.x(),
             c_masterParamPtr->m_slam->m_agv.m_stTrans.y(),
             c_masterParamPtr->m_slam->m_agv.m_stTrans.yaw());
        return true;
    }
    return false;
}

/**
 * @description: 提取lidarName 获取对应ector下标
 * @param {u_char*} p_pcBufCMD
 * @param {int&} p_iOffset
 * @return {int} 返回vector下标id  而不是ldar.id
 * @other:
 */
int WebProtocol::getLidarId_(u_char*& p_pcBufCMD, int& p_iOffset)
{
    std::string l_lidarName = "";
    int l_iLidarId = -1;
    if (asciiToString_(p_pcBufCMD, p_iOffset, l_lidarName))
    {
        for (int i = 0; i < (int)(c_masterParamPtr->m_slam->m_lidar.size()); i++)
        {
            if (c_masterParamPtr->m_slam->m_lidar[i].m_sLaserName == l_lidarName)
            {
                l_iLidarId = i;
                break;
            }
        }
    }
    return l_iLidarId;
}

/**
 * @description: 增加雷达时提取雷达名称 并确保名称唯一
 * @param {u_char*} p_pcBufCMD
 * @param {int&} p_iOffset
 * @param {string} p_sLidarName 待填充雷达名称
 * @return {bool}  true提取成功且未重复
 * @other:
 */
int WebProtocol::getNewLidarName_(u_char*& p_pcBufCMD, int& p_iOffset, std::string& p_sLidarName)
{
    std::string l_lidarName = "";
    if (asciiToString_(p_pcBufCMD, p_iOffset, l_lidarName))
    {
        for (int i = 0; i < (int)(c_masterParamPtr->m_slam->m_lidar.size()); i++)
        {
            if (c_masterParamPtr->m_slam->m_lidar[i].m_sLaserName == l_lidarName)
                return 0;
        }
        p_sLidarName = l_lidarName;
        return 1;
    }
    else
        return 0;
}

uint32_t WebProtocol::getOnlyId_(std::vector<s_LidarConfig>& p_vLidar)
{
    // p_vLidar队列为空时 将返回0
    uint32_t l_id = 0;
    while (1)
    {
        uint32_t i = 0;
        for (i = 0; i < p_vLidar.size(); i++)
        {
            if (p_vLidar[i].id == l_id)
            {
                l_id++;
                break;
            }
        }
        // 认为id未重复
        if (i == p_vLidar.size())
            break;
    }
    return l_id;
}

/**
 * @description: 从文件读取上次定位存储位姿
 * @param {s_POSE6D} &
 * @param {string} p_sFilePath
 * @return {*}
 */
bool WebProtocol::readPoseInFile_(s_POSE6D& p_sPose, std::string p_sFilePath)
{
    bool l_bloadSucc = false;

    std::fstream l_filePoseR;
    l_filePoseR.open(p_sFilePath.c_str(), std::ios::in);
    if (l_filePoseR.is_open())
    {
        std::string l_strLine;
        while (!l_filePoseR.eof())
        {
            getline(l_filePoseR, l_strLine);
            float tx, ty, tz, tr, tp, ta;
            int l_writeSuc = 0;

            if (7 == sscanf(l_strLine.c_str(), "%f,%f,%f,%f,%f,%f,%d", &tx, &ty, &tz, &tr, &tp, &ta, &l_writeSuc))
            {
                if (l_writeSuc == 1)
                {
                    p_sPose.setX(tx);
                    p_sPose.setY(ty);
                    p_sPose.setZ(tz);
                    p_sPose.setRPY(tr, tp, ta);
                    p_sPose.m_bFlag = PoseStatus::SettingPose;
                    l_bloadSucc = true;
                    break;
                }
                else
                    l_bloadSucc = false;
            }
            else
                l_bloadSucc = false;
        }
    }
    l_filePoseR.close();
    return l_bloadSucc;
}

/**
 * @description: 删除文件后缀
 * @param {string} & p_sFileName
 * @param {string} & p_sSeparator 文件后缀
 * @return {string} 文件名
 */
std::string WebProtocol::fileFilterTail_(std::string& p_sFileName, const std::string& p_sSeparator)
{
    std::vector<std::string> l_vsTmp;
    splitString(p_sFileName, l_vsTmp, p_sSeparator);
    return l_vsTmp[0];
}

void WebProtocol::sendRecvSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes)
{
    // 多扩10个字节 + 27 28 29 | 校验2 帧尾2 +末尾\0
    char* l_acBuf = new char[p_iLen + 10];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);

    int l_iLen = p_iLen;
    l_acBuf[l_iLen++] = p_iRes;
    fillAskField(l_acBuf, l_iLen);
    calcWebProtocolTail_(l_acBuf, l_iLen);
    c_sendRosCb_(l_acBuf, l_iLen);

    delete[] l_acBuf;
}

void WebProtocol::setSlamControl_(int p_newSign, char* p_pcBufResponse, int p_iLen)
{
    char* l_acBuf = new char[p_iLen];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);
    sendRecvSuccCMD_(l_acBuf, p_iLen, c_setActionCb_(7, p_newSign));
    c_bSlamControlFlag_ = false;

    delete[] l_acBuf;
}

WebProtocol::WebProtocol(boost::function<void(char*, int)> p_sendTcpCb,
                         boost::function<void(char*, int)> p_sendRosCb,
                         boost::function<int(int, int)> p_setActionCb)
    : c_sendTcpCb_(p_sendTcpCb), c_sendRosCb_(p_sendRosCb), c_setActionCb_(p_setActionCb)
{
    c_masterParamPtr = s_masterCfg::getIn();
    // c_slamBak_ = *c_masterParamPtr->m_slam;
}

WebProtocol::~WebProtocol() {}

}  // namespace wj_slam