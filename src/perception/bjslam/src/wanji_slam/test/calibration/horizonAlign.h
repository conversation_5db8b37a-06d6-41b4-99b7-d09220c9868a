/**
 * @file horizonAlign.h
 * <AUTHOR> Li
 * @brief 地平校准接口类
 * @version 0.1
 * @date 2023-07-20
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "algorithm/calibration/horizonAlign/impl/continueAlign.hpp"
#include "common/common_ex.h"
#include <boost/bind.hpp>
#include <boost/function.hpp>
#include <fstream>
#include <thread>

namespace wj_slam {

template <typename P> class HorizonAlign {
  public:
    using Ptr = boost::shared_ptr<HorizonAlign>;

  private:
    using PointRaw = pcl::PointXYZ;
    using PCRAW_PTR = boost::shared_ptr<pcl::PointCloud<PointRaw>>;
    using PC_PTR = boost::shared_ptr<pcl::PointCloud<P>>;
    using FEATURE_PAIR_PTR = typename FEATURE_PAIR<P>::Ptr;
    using FE_INPUT_CB = boost::function<void(int, FEATURE_PAIR_PTR&)>;
    using FE_OUTPUT_CB = boost::function<void(FEATURE_PAIR_PTR&)>;
    using HRZALIGN_PTR = boost::shared_ptr<wj_cA::GroundContinueAlign>;

    HRZALIGN_PTR c_pCAlign_;             /**< 实现类*/
    FE_INPUT_CB c_fEInputCb_;            /**< 特征提取完毕回调-原始*/
    FE_OUTPUT_CB c_fEOutputCb_;          /**< 发布特征帧*/
    SYSPARAM& c_stSysParam_;             /**< 系统参数*/
    s_HorizonAlignConfig& c_stHAConfig_; /**< 必要参数*/
    std::mutex c_runLock_;               /**< 接受特征帧函数锁*/
    std::thread c_planAlign_;            /**< 内部线程*/
    int c_iWorkLidarID_;                 /**< 标定的工作中雷达号*/
    int c_iAlignNum_;                    /**< 标定统计次数*/
    int c_iAlignMin_;                    /**< 标定统计次数*/
    int c_iAlignState_;                  /**< 标定状态*/
    bool c_bRun_;                        /**< 运行开关*/

  public:
    HorizonAlign(SYSPARAM& p_stSysParam, int p_iWorkLidarID, int p_iAlignNum = 20);
    ~HorizonAlign();
    void start();
    void stop();
    void shutdown();

    /**
     * @brief 接受特征帧的回调函数
     *
     * @param p_iLaserId 雷达id
     * @param p_featurePair 特征帧指针
     */
    void inputFeatureCallBack(int p_iLaserId, FEATURE_PAIR_PTR& p_featurePair);

    /**
     * @brief 设置原始帧回调函数
     *
     * @param p_funFE 回调函数
     */
    void setRawFeatureCallBack(FE_INPUT_CB p_funFE)
    {
        c_fEInputCb_ = p_funFE;
    }
    /**
     * @brief 获取原有帧回调函数
     *
     * @return FE_INPUT_CB 返回回调函数
     */
    FE_INPUT_CB getRawFeatureCallBack()
    {
        return c_fEInputCb_;
    }

    /**
     * @brief 设置特征帧出口
     *
     * @param p_funFE 输入回调函数
     */
    void setPubFeatureCallBack(FE_OUTPUT_CB p_funFE)
    {
        c_fEOutputCb_ = p_funFE;
    }

  private:
    /**
     * @brief 实现对象初始化函数
     *
     */
    void paramInit_();
    /**
     * @brief 根据旋转四元数转移点云
     *
     * @param p_roat
     * @param p_pcInput
     */
    void testCorrectResult_(Eigen::Quaterniond& p_roat, PC_PTR& p_pcInput);
    /**
     * @brief 应用校准结果
     *
     */
    void acceptResult_();
};

}  // namespace wj_slam
#ifdef WJSLAM_NO_PRECOMPILE
#    include "impl/horizontalAlign.hpp"
#else
#    define WJSLAM_HorizonAlign(P) template class wj_slam::HorizonAlign<P>;
#endif