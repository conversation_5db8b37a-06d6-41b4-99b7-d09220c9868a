#include <boost/filesystem.hpp>
#include <boost/regex.hpp>
#include <boost/system/system_error.hpp>
#include <cstdio>  //C++
#include <iostream>
#include <pcap.h>
#include <queue>
#include <string>
#include <utility>
#include <vector>
using namespace std;
struct pcap_timeval
{
    bpf_int32 tv_sec;  /* seconds */
    bpf_int32 tv_usec; /* microseconds */
};
struct pcap_sf_pkthdr
{
    struct pcap_timeval ts; /* time stamp */
    bpf_u_int32 caplen;     /* length of portion present */
    bpf_u_int32 len;        /* length of this packet (off wire) */
};
class MergePcap {
    /**
     * @class
     * @brief 读取PCAP文件，按照文件名排序，并合并
     * <AUTHOR>
     */
  public:
    /**
     * @brief 合并自动录制的PCPA包
     *
     * @param {std::string} p_p pcap文件路径
     *
     */
    MergePcap(std::string p_p) : c_sFolderPath_(p_p)
    {
        if (c_sFolderPath_[c_sFolderPath_.length() - 1] != '/')
            c_sFolderPath_ += "/";
        fillPcapNameList_(c_vsPcapNameList_, c_sFolderPath_);
    }
    ~MergePcap()
    {
        std::vector<u_char>().swap(c_vPcapAllData);
    }

    void startMerge()
    {
        if (c_vsPcapNameList_.empty())
        {
            printf("合并失败 | 路径下不存在PCAP文件\n");
            return;
        }
        struct pcap_pkthdr* l_stPktHeader;
        const u_char* l_ucPktData;

        for (int i = 0; i < (int)c_vsPcapNameList_.size(); i++)
        {
            std::string l_path = c_sFolderPath_ + c_vsPcapNameList_[i];
            if ((c_pacp_ = pcap_open_offline(l_path.c_str(), errbuf_)) == NULL)
            {
                // PCAP打开失败
                printf("Pcap[%d]: 跳过 | 文件[%s]打开失败\n", i, c_vsPcapNameList_[i].c_str());
                continue;
            }
            // 遍历PCAP
            while (1)
            {
                if (pcap_next_ex(c_pacp_, &l_stPktHeader, &l_ucPktData) >= 0)
                {
                    // 逐个pkt压入容器
                    pushPktInfo_(l_stPktHeader, l_ucPktData, c_vPcapAllData);
                }
                else
                {
                    // 读取完毕，关闭后读取下一个
                    pcap_close(c_pacp_);
                    c_pacp_ = NULL;
                    break;
                }
            }
        }
        if (c_vPcapAllData.size())
        {
            std::string l_sSavePath = c_sFolderPath_ + "splice.pcapng";
            printf("合并成功 | 输出：[%s]\n", l_sSavePath.c_str());
            savePcap_(c_vPcapAllData, c_sFolderPath_ + "splice.pcapng");
        }
        else
            printf("合并失败 | 文件为空\n");
    }

  private:
    std::vector<std::string> c_vsPcapNameList_; /**< 文件夹下所有pcap文件名称+后缀*/
    std::string c_sFolderPath_;                 /**PCAP文件路径*/
    std::vector<u_char> c_vPcapAllData;         /**pcap合并后的所有数据*/
    pcap_t* c_pacp_ = NULL;                     /**PCAP句柄*/
    bpf_program c_pcapPktFilter_;               /**PCAP过滤器*/
    char errbuf_[PCAP_ERRBUF_SIZE];

    /**
     * @brief 遍历目录下所有文件，添加至l_vfiles中
     * @param {std::vector<std::string> &} l_vfiles 保存的所有文件
     * @param {boost::filesystem::path} p path路径
     * @param {int} num 最多保留的pcap文件数目
     */
    void fillPcapNameList_(std::vector<std::string>& l_vfiles, std::string l_sPath)
    {
        boost::filesystem::path p = boost::filesystem::path(l_sPath);
        if (!boost::filesystem::exists(p))
            return;
        for (auto i = boost::filesystem::directory_iterator(p);
             i != boost::filesystem::directory_iterator();
             i++)
        {
            // 非目录则添加
            if (!boost::filesystem::is_directory(i->path()))
            {
                // cout << i->path().filename().string() << endl;
                l_vfiles.push_back(i->path().filename().string());
            }
        }
        // 从小到大排序
        if (!l_vfiles.empty())
            sort(l_vfiles.begin(), l_vfiles.end());
        for (size_t i = 0; i < l_vfiles.size(); i++)
        {
            std::cout << "file[" << i << "]" << l_vfiles[i] << std::endl;
        }
    }

    void pushPktInfo_(struct pcap_pkthdr* p_header,
                      const u_char* p_uiPktData,
                      std::vector<u_char>& p_vPcapAllData)
    {
        struct pcap_sf_pkthdr sf_hdr;
        sf_hdr.ts.tv_sec = (bpf_int32)p_header->ts.tv_sec;
        sf_hdr.ts.tv_usec = (bpf_int32)p_header->ts.tv_usec;
        sf_hdr.caplen = p_header->caplen;
        sf_hdr.len = p_header->len;

        int len = sizeof(pcap_sf_pkthdr);
        char temp_head[len];
        memcpy(temp_head, &sf_hdr, len);
        std::vector<u_char> l_vPktHeader(temp_head, temp_head + len);
        std::vector<u_char> l_vPktData;
        l_vPktData.insert(l_vPktData.end(), p_uiPktData, p_uiPktData + p_header->caplen);
        p_vPcapAllData.insert(p_vPcapAllData.end(), l_vPktHeader.begin(), l_vPktHeader.end());
        p_vPcapAllData.insert(p_vPcapAllData.end(), l_vPktData.begin(), l_vPktData.end());
    }

    void savePcap_(std::vector<u_char>& p_vPcapAllData, std::string p_sSavePath)
    {
        FILE* f;
        struct pcap_file_header hdr;
        hdr.magic = 0xa1b2c3d4;  // 0xa1b2c3d4精确到较低
        hdr.version_major = PCAP_VERSION_MAJOR;
        hdr.version_minor = PCAP_VERSION_MINOR;
        hdr.thiszone = 0;
        hdr.sigfigs = 0;
        hdr.snaplen = 65535;
        hdr.linktype = 1;

        if ((f = fopen(p_sSavePath.c_str(), "wb")) == NULL)
        {
            printf("保存[%s]失败| 文件打开错误\n", p_sSavePath.c_str());
            std::vector<u_char>().swap(p_vPcapAllData);
            return;
        }

        fwrite((char*)&hdr, sizeof(hdr), 1, f);
        fwrite((char*)&p_vPcapAllData[0], p_vPcapAllData.size() * sizeof(char), 1, f);
        fclose(f);
        std::vector<u_char>().swap(p_vPcapAllData);
    }
};

int main(int argc, const char** argv)
{
    if (argc < 2)
    {
        std::cout << "输入 PCAP路径" << std::endl;
        return -1;
    }
    // PCAP文件路径
    std::string c_sFilePath = argv[1];
    if (c_sFilePath[c_sFilePath.length() - 1] != '/')
        c_sFilePath += "/";
    MergePcap c_slicePcap(c_sFilePath);
    printf("开始合并...\n");
    c_slicePcap.startMerge();
    printf("完成合并!\n");
}