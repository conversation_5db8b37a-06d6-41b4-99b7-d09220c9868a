/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-03-04 15:16:36
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-03-23 09:10:09
 */
#include <cmath>
#include <fstream>
#include <iostream>
#include <set>

#include "common/type/type_pose.h"
namespace wj_slam {

class MultiLidarCalib {
  private:
    typedef struct s_PoseT
    {
        s_POSE6D m_Pose;
        u_int64_t m_ulTimetamp;
        s_PoseT() : m_Pose()
        {
            m_ulTimetamp = 0;
        }
        s_PoseT(uint64_t time, double x, double y, double z, double roll, double pitch, double yaw)
        {
            m_Pose.setX(x);
            m_Pose.setY(y);
            m_Pose.setZ(z);
            m_Pose.setRPY(roll, pitch, yaw);
            m_ulTimetamp = time;
        }
        void reset()
        {
            m_Pose.reset();
            m_ulTimetamp = 0;
        }
        s_PoseT& operator=(const s_PoseT& p_pos)
        {
            this->m_Pose = p_pos.m_Pose;
            this->m_ulTimetamp = p_pos.m_ulTimetamp;
            return *this;
        }

        friend bool operator<(s_PoseT const& a, s_PoseT const& b)
        {
            return a.m_ulTimetamp < b.m_ulTimetamp;
        }
    } s_PoseT;

    int c_iMaxSyncDiff = 20;

  public:
    typedef std::vector<s_PoseT> v_PoseList;
    typedef std::set<s_PoseT> set_PoseList;
    typedef std::pair<s_PoseT, s_PoseT> poseSync;
    typedef std::vector<poseSync> v_PoseSync;

    MultiLidarCalib(/* args */) {}

    ~MultiLidarCalib() {}

    /**
     * @description: 读取csv
     * @param {string} p_sPath csv路径
     * @param {set_PoseList&} p_vPoseList 路径
     * @return {Bool} true:文件成功打开
     */
    bool loadPath(std::string p_sPath, set_PoseList& p_vPoseList)
    {
        std::fstream m_fileMarkMapWR;  // mark文件读取句柄
        m_fileMarkMapWR.open(p_sPath.c_str(), std::ios::in);

        if (!m_fileMarkMapWR.is_open())
        {
            std::cout << "read file:" << p_sPath << " error!" << std::endl;
            return false;
        }
        //读取
        std::string _line;
        int l_iId = 0;
        while (!m_fileMarkMapWR.eof())
        {
            getline(m_fileMarkMapWR, _line);
            uint64_t time;
            double x, y, z, roll, pitch, yaw;
            if (7
                == sscanf(_line.c_str(),
                          "%lu,%lf,%lf,%lf,%lf,%lf,%lf",
                          &time,
                          &x,
                          &y,
                          &z,
                          &roll,
                          &pitch,
                          &yaw))
            {
                s_PoseT l_p(time, x, y, z, roll, pitch, yaw);
                p_vPoseList.insert(l_p);
                l_iId++;
            }
        }
        m_fileMarkMapWR.close();
        std::cout << "Load path with [" << l_iId << "] poses from file [" << p_sPath << "]."
                  << std::endl;
        if (l_iId > 3)
            return true;
        return false;
    }

    void matchTwoPathSync(set_PoseList& p_vFL, set_PoseList& p_vSL, v_PoseSync& p_vPS)
    {
        set_PoseList::iterator l_plow, l_pup;
        int l_ulLowDiff, l_ulUpDiff;
        // 匹配两个路径
        for (auto p : p_vFL)
        {
            l_ulLowDiff = INT32_MAX;
            l_ulUpDiff = INT32_MAX;
            l_pup = p_vSL.lower_bound(p);
            l_plow = l_pup--;
            if (l_plow != p_vSL.end())
                l_ulLowDiff = std::abs((int)(l_plow->m_ulTimetamp - p.m_ulTimetamp));
            if (l_pup != p_vSL.end())
                l_ulUpDiff = std::abs((int)(l_pup->m_ulTimetamp - p.m_ulTimetamp));
            // 储存时间最相近的一组
            if (l_ulLowDiff < l_ulUpDiff && l_ulLowDiff < c_iMaxSyncDiff)
                p_vPS.push_back(std::make_pair(p, *l_plow));
            if (l_ulUpDiff < l_ulLowDiff && l_ulUpDiff < c_iMaxSyncDiff)
                p_vPS.push_back(std::make_pair(p, *l_pup));
        }
    }

    void calibration(v_PoseSync& p_vPS, s_POSE6D& p_mean, double (&p_dev)[6])
    {
        std::cout << "start calibration with " << p_vPS.size() << "synchronized poses" << std::endl;
        std::vector<double> l_pPos[6];
        for (auto posePair : p_vPS)
        {
            s_POSE6D p_sync = calib_(posePair);
            for (int i = 0; i < 3; ++i)
                l_pPos[i].push_back(p_sync.m_trans[i]);
            l_pPos[3].push_back(p_sync.roll());
            l_pPos[4].push_back(p_sync.pitch());
            l_pPos[5].push_back(p_sync.yaw());
            printf("pose transform: [%.4f,%.4f,%.4f,%.4f,%.4f,%.4f]\n",
                   p_sync.m_trans[0],
                   p_sync.m_trans[1],
                   p_sync.m_trans[2],
                   p_sync.roll(),
                   p_sync.pitch(),
                   p_sync.yaw());
        }
        Eigen::Vector3d l_ang;
        for (int i = 0; i < 3; i++)
        {
            avgPos_(l_pPos[i], p_mean.m_trans[i], p_dev[i]);
            avgAngle_(l_pPos[i + 3], l_ang[i], p_dev[i + 3]);
        }
        p_mean.setRPY(l_ang[0], l_ang[1], l_ang[2]);
    }

    void calibVerify(std::string p_sPath, set_PoseList& p_vPoseList, s_POSE6D p_trans)
    {
        std::fstream l_filePoseWR;
        s_POSE6D c_stCurrPose_;
        l_filePoseWR.open(p_sPath.c_str(), std::ios::out | std::ios::trunc);
        for (auto pose : p_vPoseList)
        {
            c_stCurrPose_ = pose.m_Pose * p_trans;
            l_filePoseWR << pose.m_ulTimetamp << "," << c_stCurrPose_.m_trans[0] << ","
                         << c_stCurrPose_.m_trans[1] << "," << c_stCurrPose_.m_trans[2] << ","
                         << c_stCurrPose_.roll() << "," << c_stCurrPose_.pitch() << ","
                         << c_stCurrPose_.yaw() << std::endl;
        }
    }

  private:
    s_POSE6D calib_(poseSync& p_FS)
    {
        s_POSE6D p_sync;
        s_PoseT l_sF = p_FS.first;
        s_PoseT l_sS = p_FS.second;
        Eigen::Quaterniond p_q = l_sF.m_Pose.m_quat.conjugate() * l_sS.m_Pose.m_quat;
        Eigen::Vector3d p_t =
            l_sF.m_Pose.m_quat.conjugate() * (l_sS.m_Pose.m_trans - l_sF.m_Pose.m_trans);
        p_sync.setQuat(p_q);
        p_sync.m_trans = p_t;
        return p_sync;
    }

    void avgAngle_(std::vector<double>& p_pA, double& p_ang, double& p_dev)
    {
        double l_as = 0;
        double l_ac = 0;
        int l_size = p_pA.size();
        for (auto l_a : p_pA)
        {
            l_as += sin(l_a * (M_PI / 180.0));
            l_ac += cos(l_a * (M_PI / 180.0));
        }
        l_as /= (double)l_size;
        l_ac /= (double)l_size;
        p_ang = atan2(l_as, l_ac) / M_PI * 180.0;
        p_dev = sqrt(-log(l_as * l_as + l_ac * l_ac)) / M_PI * 180.0;
    }

    void avgPos_(std::vector<double>& p_pP, double& p_Pos, double& p_dev)
    {
        int l_size = p_pP.size();
        for (auto l_p : p_pP)
        {
            p_Pos += l_p;
        }
        p_Pos /= (double)l_size;
        for (auto l_p : p_pP)
        {
            p_dev += pow(l_p - p_Pos, 2);
        }
        p_dev /= (double)l_size;
    }
};

}  // namespace wj_slam

using namespace wj_slam;
int main(int argc, char const* argv[])
{
    if (argc < 2)
    {
        std::cout << "输入 <文件名1> <文件名2>" << std::endl;
        return -1;
    }
    std::string path1 = argv[1];
    std::string path2 = argv[2];
    MultiLidarCalib mc;
    MultiLidarCalib::set_PoseList l_pl1, l_pl2;
    if (mc.loadPath(path1, l_pl1) && mc.loadPath(path2, l_pl2))
    {
        MultiLidarCalib::v_PoseSync l_ps;
        mc.matchTwoPathSync(l_pl1, l_pl2, l_ps);
        s_POSE6D l_mean;
        double l_dev[6];
        mc.calibration(l_ps, l_mean, l_dev);
        printf("\n****************************\n mean pose transform "
               ":[%.4f,%.4f,%.4f,%.4f,%.4f,%.4f]\n",
               l_mean.m_trans[0],
               l_mean.m_trans[1],
               l_mean.m_trans[2],
               l_mean.roll(),
               l_mean.pitch(),
               l_mean.yaw());
        printf("stdev_xyz:[%.4f,%.4f,%.4f][m]\n", l_dev[0], l_dev[1], l_dev[2]);
        printf("stdev_deg:[%.2f,%.2f,%.2f][deg]\n", l_dev[3], l_dev[4], l_dev[5]);
        mc.calibVerify(path2 + ".new", l_pl2, l_mean);
    }
    else
    {
        printf("error");
    }

    return 0;
}
