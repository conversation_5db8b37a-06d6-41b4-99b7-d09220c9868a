/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushu<PERSON>
 * @Date: 2021-12-02 13:08:34
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-06 08:36:05
 */
#include "../mapproc/map_proc.h"
#include "common/type/type_frame.h"
#include <pcl/io/pcd_io.h>
typedef pcl::PointXYZ PointXYZ;
typedef pcl::RGB RGB;
typedef pcl::PointCloud<RGB> RGBCloud;
typedef boost::shared_ptr<RGBCloud> RGBCloudPtr;

int main(int argc, char const* argv[])
{
    if (argc < 3)
    {
        std::cout << "input file!" << std::endl;
        std::cout << "format : path/ name" << std::endl;
        return -1;
    }
    std::string path = argv[1];
    std::string name = argv[2];

    float filterOutSize = atof(argv[4]);
    std::string file = path + name + ".pcd";
    std::string filesurf = path + ".se.pcd";
    std::string filecorn = path + ".fi.pcd";
    std::cout << "file: " << file << std::endl;
    wj_slam::KEYFRAME<PointXYZ>::Ptr mapframe, sampleMap;
    mapframe.reset(new wj_slam::KEYFRAME<PointXYZ>());
    sampleMap.reset(new wj_slam::KEYFRAME<PointXYZ>());
    pcl::io::loadPCDFile<PointXYZ>(file, *(mapframe->m_pFeature->allPC));
    pcl::io::loadPCDFile<PointXYZ>(filesurf, *(mapframe->m_pFeature->second));
    pcl::io::loadPCDFile<PointXYZ>(filecorn, *(mapframe->m_pFeature->first));
    wj_slam::MapProc<PointXYZ> map;
    RGBCloudPtr rgb(new RGBCloud());
    map.process(mapframe, *mapframe);
    // map.make2D();
    map.colour(mapframe->m_pFeature->fourth, *rgb);
    typename pcl::PointCloud<pcl::PointXYZRGB>::Ptr map2D(new pcl::PointCloud<pcl::PointXYZRGB>());
    pcl::copyPointCloud(*mapframe->m_pFeature->fourth, *map2D);
    for (size_t i = 0; i < map2D->points.size(); ++i)
        map2D->points[i].rgb = rgb->points[i].rgb;
    pcl::io::savePCDFile(path + name + "_sample.pcd", *(mapframe->m_pFeature->allPC));
    pcl::io::savePCDFile(path + "fi_sample.pcd", *(mapframe->m_pFeature->first));
    pcl::io::savePCDFile(path + "se_sample.pcd", *(mapframe->m_pFeature->second));
    pcl::io::savePCDFile(path + "2d_sample.pcd", *(mapframe->m_pFeature->fourth));
    pcl::io::savePCDFile(path + "2drgb_sample.pcd", *(map2D));
    return 0;
}