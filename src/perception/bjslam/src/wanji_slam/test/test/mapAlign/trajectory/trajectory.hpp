#pragma once

#include "../type_site.h"
#include "common/type/type_pose.h"
#include "tool/fileTool/fileTool.h"
#include <deque>
#include <fstream>
#include <iostream>
#include <string>
#include <vector>

#include <pcl/common/common.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/io/pcd_io.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/pcl_base.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
// #define DEBUGSAVE

using namespace std;

namespace wj_slam {
class Trajectory {
    typedef pcl::PointXYZI T;
    typedef pcl::PointCloud<T> pointCloudT;
    typedef boost::shared_ptr<pointCloudT> pointCloudT_Ptr;

  public:
    Trajectory(std::string p_sFilePath, std::string p_sPoseFileName) : c_sFilePath_(p_sFilePath)
    {
        if ('/' != c_sFilePath_[c_sFilePath_.length() - 1])
            c_sFilePath_ += "/";
        c_sSrcFileName_ = p_sPoseFileName + "_new";
        c_sDstFileName_ = p_sPoseFileName + "_old";

        if (loadTrajectoryFile())
        {
            c_bTrajectoryValid_ = checkCorrespondingPoint();
        }
    }
    ~Trajectory()
    {
        clearDeque_(c_dqSrcPoseBuf);
        clearDeque_(c_dqDstPoseBuf);
        clearVector_(c_vSrcIDBuf);
        clearVector_(c_vDstIDBuf);
    }

    bool isValidTrajectory()
    {
        return c_bTrajectoryValid_;
    }

    std::deque<st_Trajectory>& getSrcTrajectory()
    {
        return c_dqSrcPoseBuf;
    }

    std::deque<st_Trajectory>& getDstTrajectory()
    {
        return c_dqDstPoseBuf;
    }

    void setBigYawValue(float p_fValue)
    {
        c_fBigYawValue_ = p_fValue;
    }

  private:
    std::deque<st_Trajectory> c_dqSrcPoseBuf;  // 源轨迹
    std::deque<st_Trajectory> c_dqDstPoseBuf;  // 目标轨迹 即真值轨迹
    std::vector<int> c_vSrcIDBuf;
    std::vector<int> c_vDstIDBuf;

    std::string c_sFilePath_ = "";     // 文件路径
    std::string c_sSrcFileName_ = "";  // 源轨迹文件名
    std::string c_sDstFileName_ = "";  // 目标轨迹文件名

    bool c_bTrajectoryValid_ = false;
    float c_fBigYawValue_ = 100.0;

    bool loadTrajectoryFile()
    {
        std::string l_path = c_sFilePath_ + c_sSrcFileName_ + ".csv";
        if (!loadTrajectoryFile_(l_path, c_dqSrcPoseBuf, c_vSrcIDBuf))
            return false;
        // // 根据PCD 过滤CSV & 重新填充c_vSrcIDBuf
        // l_path = c_sFilePath_ + "path6_filter2"+"_new.pcd";
        // loadTrajectoryPcdFile_(l_path, c_dqSrcPoseBuf, c_vSrcIDBuf);

        l_path = c_sFilePath_ + c_sDstFileName_ + ".csv";
        if (!loadTrajectoryFile_(l_path, c_dqDstPoseBuf, c_vDstIDBuf))
            return false;
        // l_path = c_sFilePath_ + "path6_filter2"+"_old.pcd";
        // loadTrajectoryPcdFile_(l_path, c_dqDstPoseBuf, c_vDstIDBuf);
        return true;
    }

    /*检查同名点*/
    bool checkCorrespondingPoint()
    {
        std::vector<int> l_vEraseSrc = vectorDifference(c_vSrcIDBuf, c_vDstIDBuf);
        std::vector<int> l_vEraseDst = vectorDifference(c_vDstIDBuf, c_vSrcIDBuf);

        size_t l_commonSrcSize = c_vSrcIDBuf.size() - l_vEraseSrc.size();
        size_t l_commonDstSize = c_vDstIDBuf.size() - l_vEraseDst.size();
        if (l_commonSrcSize != l_commonDstSize)
        {
            printf("common Size not equal\n");
            return false;
        }
        eraseNoCommon(c_dqSrcPoseBuf, l_vEraseSrc);
        if (c_dqSrcPoseBuf.size() != l_commonSrcSize)
        {
            printf("src eraseCommon error\n");
            return false;
        }
        eraseNoCommon(c_dqDstPoseBuf, l_vEraseDst);
        if (c_dqDstPoseBuf.size() != l_commonDstSize)
        {
            printf("dst eraseCommon error\n");
            return false;
        }

        if (c_dqSrcPoseBuf.size() != c_dqDstPoseBuf.size())
        {
            printf("trajectory Size not equal\n");
            return false;
        }

        // 检查删除不相同帧ID 且记录角度较大点帧ID
        std::vector<int> l_vEraseBigYaw;
        for (size_t i = 0; i < c_dqSrcPoseBuf.size(); i++)
        {
            if (c_dqSrcPoseBuf[i].m_iFrameID != c_dqDstPoseBuf[i].m_iFrameID)
            {
                printf("erase common fail\n");
                return false;
            }

            float l_yaw1 = c_dqSrcPoseBuf[i].m_trajectory.m_Pose.yaw();
            float l_yaw2 = c_dqDstPoseBuf[i].m_trajectory.m_Pose.yaw();
            float l_yawDiff = 0;
            // 一正一负 则跨180 or 0
            if (l_yaw1 * l_yaw2 < 0.0)
            {
                if ((fabs(l_yaw1) > 90) && (fabs(l_yaw2) > 90))
                {
                    // 跨180
                    l_yawDiff = 360 - fabs(l_yaw1 - l_yaw2);
                }
                else if ((fabs(l_yaw1) < 90) && (fabs(l_yaw2) < 90))
                {
                    // 跨0
                    l_yawDiff = fabs(l_yaw1) + fabs(l_yaw2);
                }
                else
                    printf("common point[%d] no default judge\n", (int)i);
            }
            else
                l_yawDiff = fabs(l_yaw1 - l_yaw2);
            if (l_yawDiff > c_fBigYawValue_)
            {
                l_vEraseBigYaw.emplace_back(i);
                printf("common point[%d] yaw too big | frameID: %d | %f %f | res: %f\n",
                       (int)i,
                       c_dqSrcPoseBuf[i].m_iFrameID,
                       c_dqSrcPoseBuf[i].m_trajectory.m_Pose.yaw(),
                       c_dqDstPoseBuf[i].m_trajectory.m_Pose.yaw(),
                       c_fBigYawValue_);
            }

            // // 新的角度 = 新角度 - 老角度
            // float l_fNew_Old = c_dqSrcPoseBuf[i].m_trajectory.m_Pose.yaw() -
            // c_dqDstPoseBuf[i].m_trajectory.m_Pose.yaw();
            // c_dqSrcPoseBuf[i].m_trajectory.m_Pose.setRPY(0 ,0, l_fNew_Old);
            // // 老角度清空
            // c_dqDstPoseBuf[i].m_trajectory.m_Pose.setRPY(0,0,0);
        }

        // 过滤角度差值过大的同名点
        if (!l_vEraseBigYaw.empty())
        {
            std::deque<st_Trajectory> l_dqDstFilter_;
            std::deque<st_Trajectory> l_dqSrcFilter_;
            for (size_t i = 0; i < c_dqSrcPoseBuf.size(); i++)
            {
                bool l_bIsBigYaw = false;
                for (size_t j = 0; j < l_vEraseBigYaw.size(); j++)
                {
                    if ((int)i == l_vEraseBigYaw[j])
                    {
                        l_bIsBigYaw = true;
                        break;
                    }
                }
                if (!l_bIsBigYaw)
                {
                    l_dqDstFilter_.emplace_back(c_dqDstPoseBuf[i]);
                    l_dqSrcFilter_.emplace_back(c_dqSrcPoseBuf[i]);
                }
            }
            clearDeque_(c_dqDstPoseBuf);
            clearDeque_(c_dqSrcPoseBuf);
            c_dqDstPoseBuf.assign(l_dqDstFilter_.begin(), l_dqDstFilter_.end());
            c_dqSrcPoseBuf.assign(l_dqSrcFilter_.begin(), l_dqSrcFilter_.end());
        }

#ifdef DEBUGSAVE
        std::string l_path = c_sFilePath_ + c_sDstFileName_ + "_common.csv";
        saveTrajectory_(l_path, c_dqDstPoseBuf);
        l_path = c_sFilePath_ + c_sSrcFileName_ + "_common.csv";
        saveTrajectory_(l_path, c_dqSrcPoseBuf);
        l_path = c_sFilePath_ + c_sSrcFileName_ + "_common.pcd";
        saveTrajectoryPcd_(l_path, c_dqDstPoseBuf);
        l_path = c_sFilePath_ + c_sSrcFileName_ + "_common.pcd";
        saveTrajectoryPcd_(l_path, c_dqSrcPoseBuf);
#endif  // DEBUGSAVE

        return true;
    }

    bool saveTrajectory_(std::string p_stPath, std::deque<st_Trajectory>& p_dqSrcPoseBuf)
    {
        std::fstream l_filePoseWR;
        l_filePoseWR.open(p_stPath.c_str(), std::ios::out | std::ios::trunc);
        if (!l_filePoseWR.is_open())
        {
            printf(" save trajectory fail | open [%s] fail\n", p_stPath.c_str());
            return false;
        }
        for (size_t i = 0; i < p_dqSrcPoseBuf.size(); i++)
        {
            l_filePoseWR << p_dqSrcPoseBuf[i].m_iFrameID << ","
                         << p_dqSrcPoseBuf[i].m_trajectory.m_Pose.x() << ","
                         << p_dqSrcPoseBuf[i].m_trajectory.m_Pose.y() << ","
                         << p_dqSrcPoseBuf[i].m_trajectory.m_Pose.z() << ","
                         << p_dqSrcPoseBuf[i].m_trajectory.m_Pose.roll() << ","
                         << p_dqSrcPoseBuf[i].m_trajectory.m_Pose.pitch() << ","
                         << p_dqSrcPoseBuf[i].m_trajectory.m_Pose.yaw() << std::endl;
        }
        l_filePoseWR.close();
        return true;
    }

    void saveTrajectoryPcd_(std::string p_stPath, std::deque<st_Trajectory>& p_dqSrcPoseBuf)
    {
        pointCloudT_Ptr l_savePath(new pointCloudT());
        T l_transPose;
        for (uint32_t i = 0; i < p_dqSrcPoseBuf.size(); i++)
        {
            l_transPose.x = p_dqSrcPoseBuf[i].m_trajectory.m_Pose.x();
            l_transPose.y = p_dqSrcPoseBuf[i].m_trajectory.m_Pose.y();
            l_transPose.z = p_dqSrcPoseBuf[i].m_trajectory.m_Pose.z();
            l_transPose.intensity = p_dqSrcPoseBuf[i].m_iFrameID;
            l_savePath->points.push_back(l_transPose);
        }
        pcl::PCDWriter writer;
        writer.writeBinary(p_stPath, *l_savePath);
    }

    void eraseNoCommon(std::deque<st_Trajectory>& p_vPoseBuf, std::vector<int>& p_vEraceIDBuf)
    {
        if (p_vEraceIDBuf.empty())
            return;
        for (size_t i = 0; i < p_vEraceIDBuf.size(); i++)
        {
            bool l_bRes = false;
            std::deque<st_Trajectory>::iterator iter;
            for (iter = p_vPoseBuf.begin(); iter != p_vPoseBuf.end();)
            {
                if (p_vEraceIDBuf[i] == (*iter).m_iFrameID)
                {
                    iter = p_vPoseBuf.erase(iter);
                    l_bRes = true;
                    break;
                }
                else
                    ++iter;
            }
            if (!l_bRes)
                printf("取非重复异常: frameId: %d\n", p_vEraceIDBuf[i]);
        }
    }

    bool loadTrajectoryFile_(std::string p_sFilePath,
                             std::deque<st_Trajectory>& p_vPoseBuf,
                             std::vector<int>& p_vFrameIDBuf)
    {
        if (!isExistFileOrFolder(p_sFilePath))
        {
            printf("轨迹地图不存在: [%s]\n", p_sFilePath.c_str());
            return false;
        }
        std::vector<std::vector<std::string>> l_fileInfo;
        st_Trajectory l_pose;
        readFile_(p_sFilePath, l_fileInfo, ",");
        for (size_t i = 0; i < l_fileInfo.size(); i++)
        {
            // frameID x y z r p y
            if (l_fileInfo[i].size() == 7)
            {
                l_pose.m_iFrameID = stoi(l_fileInfo[i][0]);
                p_vFrameIDBuf.emplace_back(l_pose.m_iFrameID);
                // l_pose.m_trajectory.m_Pose.setXYZ(
                //     stod(l_fileInfo[i][1]), stod(l_fileInfo[i][2]), stod(l_fileInfo[i][3]));
                // l_pose.m_trajectory.m_Pose.setRPY(
                //     stod(l_fileInfo[i][4]), stod(l_fileInfo[i][5]), stod(l_fileInfo[i][6]));
                l_pose.m_trajectory.m_Pose.setXYZ(
                    stod(l_fileInfo[i][1]), stod(l_fileInfo[i][2]), 0);
                l_pose.m_trajectory.m_Pose.setRPY(0, 0, stod(l_fileInfo[i][6]));
                p_vPoseBuf.emplace_back(l_pose);
            }
            else
                std::cout << "读取轨迹地图异常: 列数不符 | [" << i << "]: " << l_fileInfo[i].size()
                          << std::endl;
        }
        if (p_vPoseBuf.empty())
        {
            printf("读取轨迹地图异常: 文件有效点为空\n");
            return false;
        }
        return true;
    }

    /*加载PCD 根据I通道的FrameID 反向过滤掉csv读取中不存在的frameID*/
    bool loadTrajectoryPcdFile_(std::string p_sFilePath,
                                std::deque<st_Trajectory>& p_vPoseBuf,
                                std::vector<int>& p_vFrameIDBuf)
    {
        if (!isExistFileOrFolder(p_sFilePath))
        {
            printf("轨迹PCD地图不存在: [%s]\n", p_sFilePath.c_str());
            return false;
        }
        pointCloudT_Ptr l_pointPtr(new pointCloudT());
        pcl::io::loadPCDFile(p_sFilePath, *(l_pointPtr));
        std::vector<int> l_existFrameID;
        for (size_t i = 0; i < l_pointPtr->points.size(); i++)
        {
            int l_iFrameID = (int)(l_pointPtr->points[i].intensity);
            l_existFrameID.emplace_back(l_iFrameID);
        }

        std::deque<st_Trajectory>::iterator iter;
        for (iter = p_vPoseBuf.begin(); iter != p_vPoseBuf.end();)
        {
            bool l_bErase = true;
            for (size_t i = 0; i < l_existFrameID.size(); i++)
            {
                // 相同的不需要删除
                if (l_existFrameID[i] == (*iter).m_iFrameID)
                {
                    l_bErase = false;
                    break;
                }
            }
            if (l_bErase)
                iter = p_vPoseBuf.erase(iter);
            else
                ++iter;
        }
        clearVector_(p_vFrameIDBuf);
        for (size_t i = 0; i < p_vPoseBuf.size(); i++)
        {
            p_vFrameIDBuf.emplace_back(p_vPoseBuf[i].m_iFrameID);
        }
    }

    void readFile_(string p_sPathName, vector<vector<string>>& p_vsInfo, const string& c)
    {
        p_vsInfo.clear();
        ifstream InFile(p_sPathName);
        string l_sLine;                                //存储读取的每行数据
        vector<string> l_vSimpleStr;                   //存储每行数据
        while (!InFile.eof() && InFile.peek() != EOF)  //使用
        {
            getline(InFile, l_sLine);
            splitString_(l_sLine, l_vSimpleStr, c);
            p_vsInfo.push_back(l_vSimpleStr);
            l_vSimpleStr.clear();
        }
        InFile.close();
    }

    void splitString_(string& p_sStr, vector<string>& p_vsSimpleStr, const string& c)
    {
        // string::size_type它是一个无符号类型的值，而且能够存放下任何string对象的大小。
        string::size_type l_Pos1, l_Pos2;
        l_Pos2 = p_sStr.find(c);
        l_Pos1 = 0;
        while (string::npos != l_Pos2)  // npos意味着一直到结尾
        {
            p_vsSimpleStr.push_back(p_sStr.substr(l_Pos1, l_Pos2 - l_Pos1));
            l_Pos1 = l_Pos2 + c.size();
            l_Pos2 = p_sStr.find(c, l_Pos1);
        }
        if (l_Pos1 != p_sStr.length())
        {
            p_vsSimpleStr.push_back(p_sStr.substr(l_Pos1));
        }
    }

    /**
     * @description:
     * 两个vector求交集，存在空集 返回空集 ，都不空 去交集
     * @param {vector<int> } v1
     * @param {vector<int> } v2
     * @return {*}
     */
    vector<int> vectorIntersection_(vector<int> v1, vector<int> v2)
    {
        vector<int> v;
        if (v1.empty() || v2.empty())
        {
            clearVector_(v);
            return v;
        }
        sort(v1.begin(), v1.end());
        sort(v2.begin(), v2.end());
        set_intersection(v1.begin(), v1.end(), v2.begin(), v2.end(),
                         back_inserter(v));  //求交集
        return v;
    }

    /**
     * @description: v1  去除 v2 和 v1 重复元素
     * @param {vector<int> v1, vector<int>} v2 |  v1 雷达扫描框 更大  v2附近框
     * @return {*}
     */
    vector<int> vectorDifference(vector<int> v1, vector<int> v2)
    {
        vector<int> v;
        if (v1.empty())
            return v;
        if (v2.empty())
            return v1;

        sort(v1.begin(), v1.end());
        sort(v2.begin(), v2.end());

        // std::set_symmetric_difference(std::begin(v1), std::end(v1),
        // std::begin(v2),
        //                               std::end(v2), std::back_inserter(v));

        // v1  去除 v2 和 v1 重复元素
        std::set_difference(
            std::begin(v1), std::end(v1), std::begin(v2), std::end(v2), std::back_inserter(v));
        return v;
    }

    /**
     * @description:
     * 两个deque求交集，存在空集 返回空集 ，都不空 去交集
     * @param {std::deque<st_Trajectory>} dq1
     * @param {std::deque<st_Trajectory>} dq2
     * @return {*}
     */
    std::deque<st_Trajectory> dequeIntersection(std::deque<st_Trajectory> dq1,
                                                std::deque<st_Trajectory> dq2)
    {
        std::deque<st_Trajectory> dq;
        return dq;
        // if (dq1.empty() || dq2.empty())
        // {
        //     clearDeque_(dq);
        //     return dq;
        // }
        // stable_sort(dq1.begin(), dq1.end(), [](const st_Trajectory& a, const st_Trajectory& b) {
        //     return a.m_iFrameID < b.m_iFrameID;
        // });
        // stable_sort(dq2.begin(), dq2.end(), [](const st_Trajectory& a, const st_Trajectory& b) {
        //     return a.m_iFrameID < b.m_iFrameID;
        // });
        // set_intersection(dq1.begin(),
        //                  dq1.end(),
        //                  dq2.begin(),
        //                  dq2.end(),
        //                  back_inserter(dq));  //求交集
        // return dq;
    }

    template <typename D> inline void clearVector_(std::vector<D>& vec)
    {
        std::vector<D>().swap(vec);
    }

    template <typename D> inline void clearDeque_(std::deque<D>& vec)
    {
        std::deque<D>().swap(vec);
    }
};
}  // namespace wj_slam