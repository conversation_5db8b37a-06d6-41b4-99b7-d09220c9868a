#include "dispatchMap.h"

namespace wj_slam {

#pragma region 基类
DispatchMap::DispatchMap(std::string p_sFilePath, std::string p_sDispatchFileName)
    : c_sFilePath(p_sFilePath), c_sDispatchFileName(p_sDispatchFileName)
{
    if ('/' != c_sFilePath[c_sFilePath.length() - 1])
        c_sFilePath += "/";
    c_vSiteBuf.clear();
}

bool DispatchMap::loadSiteMapFile()
{
    return false;
}

bool DispatchMap::saveSiteMapFile()
{
    return false;
}

DispatchMap::~DispatchMap()
{
    c_vSiteBuf.clear();
}

std::vector<st_Site>& DispatchMap::getSiteXYInfoBuf()
{
    return c_vSiteBuf;
}

std::vector<st_SiteLineInfo>& DispatchMap::getSiteYawInfoBuf()
{
    return c_SiteInfoBuf;
}

void DispatchMap::readFile(string p_sPathName, vector<vector<string>>& p_vsInfo, const string& c)
{
    p_vsInfo.clear();
    ifstream InFile(p_sPathName);
    string l_sLine;                                //存储读取的每行数据
    vector<string> l_vSimpleStr;                   //存储每行数据
    while (!InFile.eof() && InFile.peek() != EOF)  //使用
    {
        getline(InFile, l_sLine);
        splitString(l_sLine, l_vSimpleStr, c);
        p_vsInfo.push_back(l_vSimpleStr);
        l_vSimpleStr.clear();
    }
    InFile.close();
}

void DispatchMap::splitString(string& p_sStr, vector<string>& p_vsSimpleStr, const string& c)
{
    // string::size_type它是一个无符号类型的值，而且能够存放下任何string对象的大小。
    string::size_type l_Pos1, l_Pos2;
    l_Pos2 = p_sStr.find(c);
    l_Pos1 = 0;
    while (string::npos != l_Pos2)  // npos意味着一直到结尾
    {
        p_vsSimpleStr.push_back(p_sStr.substr(l_Pos1, l_Pos2 - l_Pos1));
        l_Pos1 = l_Pos2 + c.size();
        l_Pos2 = p_sStr.find(c, l_Pos1);
    }
    if (l_Pos1 != p_sStr.length())
    {
        p_vsSimpleStr.push_back(p_sStr.substr(l_Pos1));
    }
}

#pragma endregion

#pragma region 哈工调度
DispatchMapHG::DispatchMapHG(std::string p_sFilePath, std::string p_sDispatchFileName)
    : DispatchMap(p_sFilePath, p_sDispatchFileName)
{
    c_SiteInfoBuf.clear();
    if (!loadSiteMapFile())
        printf("加载调度地图失败\n");
}

DispatchMapHG::~DispatchMapHG()
{
    c_SiteInfoBuf.clear();
}

bool DispatchMapHG::loadSiteMapFile()
{
    std::string l_path = c_sFilePath + c_sDispatchFileName + "_xy.csv";
    if (!loadSiteMapXYInfo_(l_path, c_vSiteBuf))
        return false;
    l_path = c_sFilePath + c_sDispatchFileName + "_yaw.csv";
    if (!loadSiteMapYawInfo_(l_path, c_SiteInfoBuf))
        return false;
#ifdef MANUALTRANS
    // 拷贝pcd同名站点，用于通过cc手动移动站点
    l_path = c_sFilePath + c_sDispatchFileName + "_xy_manualTrans.pcd";
    getPcdToSite_(l_path, c_vSiteBuf);
#endif  // MANUALTRANS
    return true;
}

bool DispatchMapHG::saveSiteMapFile()
{
    std::string l_path = c_sFilePath + c_sDispatchFileName + "_xy_correct.csv";
    if (!saveiteMapXYInfo_(l_path, c_vSiteBuf))
        return false;
    l_path = c_sFilePath + c_sDispatchFileName + "_yaw_correct.csv";
    if (!saveSiteMapYawInfo_(l_path, c_SiteInfoBuf))
        return false;
#ifdef MANUALTRANS
    // 保存修正后的xy信息，用于cc手动移动
    l_path = c_sFilePath + c_sDispatchFileName + "_xy_correct_copy.pcd";
    siteXYInfoToPcd_(l_path, c_vSiteBuf);
#endif  // MANUALTRANS
    return true;
}

/**
 * @function: loadSiteMapXYInfo_
 * @description: 加载XY信息
 * @param {std::string& p_stFilePath} XY信息文件路径
 * @param {std::vector<st_Site>&} p_SiteBuf_ 待填充站点数据
 * @return {bool}
 * @others: {*}
 */
bool DispatchMapHG::loadSiteMapXYInfo_(std::string& p_stPath, std::vector<st_Site>& p_SiteBuf)
{
    if (!isExistFileOrFolder(p_stPath))
    {
        printf("调度地图不存在: [%s]\n", p_stPath.c_str());
        return false;
    }
    std::vector<std::vector<std::string>> l_fileInfo;
    st_Site l_site;
    readFile(p_stPath, l_fileInfo, ",");
    for (size_t i = 0; i < l_fileInfo.size(); i++)
    {
        if (l_fileInfo[i].size() == 3)
        {
            l_site.m_iSiteID = stoi(l_fileInfo[i][0]);
            double l_x = stod(l_fileInfo[i][1]);
            double l_y = stod(l_fileInfo[i][2]);
            l_site.m_SitePose.setXYZ(l_x, l_y, 0);
            p_SiteBuf.emplace_back(l_site);
        }
        else
            std::cout << "load xy info size error | [" << i << "]: " << l_fileInfo[i].size()
                      << std::endl;
    }
    if (p_SiteBuf.empty())
    {
        printf("load xy info fail | file empty\n");
        return false;
    }
    return true;
}

bool DispatchMapHG::saveiteMapXYInfo_(std::string& p_stPath, std::vector<st_Site>& p_SiteBuf)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_stPath.c_str(), std::ios::out | std::ios::trunc);
    if (!l_filePoseWR.is_open())
    {
        printf(" save xy info fail | open [%s] fail\n", p_stPath.c_str());
        return false;
    }
    for (size_t i = 0; i < p_SiteBuf.size(); i++)
    {
        l_filePoseWR << p_SiteBuf[i].m_iSiteID << "," << p_SiteBuf[i].m_SitePose.x() << ","
                     << p_SiteBuf[i].m_SitePose.y() << std::endl;
    }
    l_filePoseWR.close();
    return true;
}

/**
 * @function: loadSiteMapYawInfo_
 * @description: 加载Yaw及线段信息
 * @param {std::string&} p_stPath Yaw及线段文件路径
 * @param {std::vector<st_SiteLineInfo>&} p_SiteBuf_ 待填充站点线段信息数据
 * @return {bool}
 * @others: {*}
 */
bool DispatchMapHG::loadSiteMapYawInfo_(std::string& p_stPath,
                                        std::vector<st_SiteLineInfo>& p_SiteInfoBuf)
{
    if (!isExistFileOrFolder(p_stPath))
    {
        printf("调度地图不存在: [%s]\n", p_stPath.c_str());
        return false;
    }
    std::vector<std::vector<std::string>> l_fileInfo;
    std::vector<std::string> l_splitInfo;
    readFile(p_stPath, l_fileInfo, ",");
    for (size_t i = 0; i < l_fileInfo.size(); i++)
    {
        // 是否3列
        if (l_fileInfo[i].size() != 3)
        {
            std::cout << "load yaw info size != 3 | [" << i << "]: " << l_fileInfo[i].size()
                      << std::endl;
            continue;
        }
        st_SiteLineInfo l_siteInfo;
        for (size_t j = 0; j < l_fileInfo[i].size(); j++)
        {
            // 第一列分割，是否为线
            if (j == 0)
            {
                l_splitInfo.clear();
                splitString(l_fileInfo[i][j], l_splitInfo, "-");
                if (l_splitInfo.size() == 2)
                    l_siteInfo.m_bIsLine = true;
                else if (l_splitInfo.size() == 1)
                    l_siteInfo.m_bIsLine = false;
                else
                {
                    // 分割失败
                    printf("split yaw info size error | [%d][%d]: %s\n",
                           (int)i,
                           (int)j,
                           l_fileInfo[i][j].c_str());
                    // break;
                }
                for (size_t k = 0; k < l_splitInfo.size(); k++)
                    l_siteInfo.m_viSiteID.emplace_back(stoi(l_splitInfo[k]));
            }
            else
            {
                // 角度约束[0.360] 为(-180，180]
                float l_fYaw = stod(l_fileInfo[i][j]);
                if (l_fYaw > 180.0)
                    l_fYaw -= 360.0;
                l_siteInfo.m_vfSiteAng.emplace_back(l_fYaw);
            }

            // 每行读取完毕后存储
            if ((j + 1) == l_fileInfo[i].size())
                p_SiteInfoBuf.emplace_back(l_siteInfo);
        }
    }
    return true;
}

bool DispatchMapHG::saveSiteMapYawInfo_(std::string& p_stPath,
                                        std::vector<st_SiteLineInfo>& p_SiteInfoBuf)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_stPath.c_str(), std::ios::out | std::ios::trunc);
    if (!l_filePoseWR.is_open())
    {
        printf(" save yaw info fail | open [%s] fail\n", p_stPath.c_str());
        return false;
    }
    for (size_t i = 0; i < p_SiteInfoBuf.size(); i++)
    {
        std::string l_sSiteIdInfo = "";
        for (size_t j = 0; j < p_SiteInfoBuf[i].m_viSiteID.size(); j++)
        {
            l_sSiteIdInfo += std::to_string(p_SiteInfoBuf[i].m_viSiteID[j]);
            if ((j + 1) != p_SiteInfoBuf[i].m_viSiteID.size())
                l_sSiteIdInfo += "-";
        }

        std::string l_sAngInfo = "";
        for (size_t j = 0; j < p_SiteInfoBuf[i].m_vfSiteAng.size(); j++)
        {
            // 保存时 改变约束(0 360]
            if (p_SiteInfoBuf[i].m_vfSiteAng[j] < 0.0)
                p_SiteInfoBuf[i].m_vfSiteAng[j] += 360.0;
            l_sAngInfo += std::to_string((int)p_SiteInfoBuf[i].m_vfSiteAng[j]);
            if ((j + 1) != p_SiteInfoBuf[i].m_vfSiteAng.size())
                l_sAngInfo += ",";
        }
        l_filePoseWR << l_sSiteIdInfo << "," << l_sAngInfo << std::endl;
    }
    l_filePoseWR.close();
    return true;
}

/**
 * @function: fillAngInfo_
 * @description: 从p_SiteInfoBuf提取角度信息至p_SiteBuf
 * @param {std::vector<st_Site>&} p_SiteBuf_ 待填充站点数据
 * @param {std::vector<st_SiteLineInfo>&} p_SiteInfoBuf 站点线段信息数据
 * @return {bool}
 * @others: {*}
 */
void DispatchMapHG::fillAngInfo_(std::vector<st_Site>& p_SiteBuf,
                                 std::vector<st_SiteLineInfo>& p_SiteInfoBuf)
{
    //     pointCloudT_Ptr l_savePath(new pointCloudT());
    //     T l_pose;
    //     for (size_t i = 0; i < p_SiteBuf.size(); i++)
    //     {
    //         bool l_res = false;
    //         for (size_t j = 0; j < p_SiteInfoBuf.size(); j++)
    //         {
    //             for (size_t k = 0; k < p_SiteInfoBuf[j].m_viSiteID.size(); k++)
    //             {
    //                 if (p_SiteBuf[i].m_iSiteID == p_SiteInfoBuf[j].m_viSiteID[k])
    //                 {
    //                     p_SiteBuf[i].m_SitePose.setRPY(0, 0, p_SiteInfoBuf[j].m_vfSiteAng[k]);
    //                     l_res = true;

    //                     p_SiteBuf[i].m_SitePose.nomalize();
    //                     // 调试 放入 xy yaw
    //                     l_pose.x = p_SiteBuf[i].m_SitePose.x();
    //                     l_pose.y = p_SiteBuf[i].m_SitePose.y();
    //                     l_pose.z = p_SiteBuf[i].m_SitePose.z();
    //                     l_pose.intensity = p_SiteInfoBuf[j].m_vfSiteAng[k];
    //                     l_savePath->points.push_back(l_pose);
    //                     break;
    //                 }
    //             }
    //             if (l_res)
    //                 break;
    //         }
    //         if (!l_res)
    //             printf("no find ID[%d] ang\n", p_SiteBuf[i].m_iSiteID);
    //     }

    // #ifdef DEBUGSAVE
    //     pcl::PCDWriter writer;
    //     std::string l_path = c_sFilePath + c_sDispatchFileName + "_rawSite.pcd";
    //     writer.writeBinary(l_path, *l_savePath);
    // #endif  // DEBUGSAVE
}

/**
 * @function: correctAngInfo_
 * @description: 从p_SiteBufNoAng提取角度补偿值信息至p_SiteBuf &
 * @param {std::vector<st_Site>&} p_SiteBuf_ 待填充站点数据
 * @param {std::vector<st_SiteLineInfo>&} p_SiteInfoBuf 站点线段信息数据
 * @return {bool}
 * @others: {*}
 */
void DispatchMapHG::correctAngInfo_(std::vector<st_Site>& p_SiteBuf,
                                    std::vector<st_SiteLineInfo>& p_SiteInfoBuf)
{
    //     pointCloudT_Ptr l_savePath(new pointCloudT());
    //     T l_pose;

    //     for (size_t i = 0; i < p_SiteInfoBuf.size(); i++)
    //     {
    //         for (size_t j = 0; j < p_SiteInfoBuf[i].m_viSiteID.size(); j++)
    //         {
    //             bool l_bRes = false;
    //             for (size_t k = 0; k < p_SiteBuf.size(); k++)
    //             {
    //                 if (p_SiteBuf[k].m_iSiteID == p_SiteInfoBuf[i].m_viSiteID[j])
    //                 {
    //                     float l_fRaw = p_SiteInfoBuf[i].m_vfSiteAng[j];
    //                     if (p_SiteBuf[k].m_bIsTrans)
    //                         p_SiteInfoBuf[i].m_vfSiteAng[j] = p_SiteBuf[k].m_SitePose.yaw();

    //                     // 约束(-180 180]
    //                     if (p_SiteInfoBuf[i].m_vfSiteAng[j] > 180.0)
    //                         p_SiteInfoBuf[i].m_vfSiteAng[j] -= 360.0;
    //                     if (p_SiteInfoBuf[i].m_vfSiteAng[j] < -180.0)
    //                         p_SiteInfoBuf[i].m_vfSiteAng[j] += 360.0;

    //                     if (fabs(l_fRaw - p_SiteInfoBuf[i].m_vfSiteAng[j]) >= 100.0)
    //                     {
    //                         printf("ang too big change: yawFile[%d]: id-[%d]: isTrans %d | %f ->
    //                         %f | "
    //                                "%f\n",
    //                                (int)i,
    //                                p_SiteInfoBuf[i].m_viSiteID[j],
    //                                p_SiteBuf[k].m_bIsTrans,
    //                                l_fRaw,
    //                                p_SiteInfoBuf[i].m_vfSiteAng[j],
    //                                p_SiteBuf[k].m_SitePose.yaw());
    //                     }
    //                     l_bRes = true;

    //                     // 调试 放入 xy yaw
    //                     l_pose.x = p_SiteBuf[k].m_SitePose.x();
    //                     l_pose.y = p_SiteBuf[k].m_SitePose.y();
    //                     l_pose.z = p_SiteBuf[k].m_SitePose.z();
    //                     l_pose.intensity = p_SiteInfoBuf[i].m_vfSiteAng[j];
    //                     l_savePath->points.push_back(l_pose);

    //                     // 保存时 改变约束(0 360]
    //                     if (p_SiteInfoBuf[i].m_vfSiteAng[j] < 0.0)
    //                         p_SiteInfoBuf[i].m_vfSiteAng[j] += 360.0;
    //                     break;
    //                 }
    //             }
    //             if (!l_bRes)
    //                 printf("correct no find common ID [%d][%d]: %d\n",
    //                        (int)i,
    //                        (int)j,
    //                        p_SiteInfoBuf[i].m_viSiteID[j]);
    //         }
    //     }
    // #ifdef DEBUGSAVE
    //     pcl::PCDWriter writer;
    //     std::string l_path = c_sFilePath + c_sDispatchFileName + "_transSite.pcd";
    //     writer.writeBinary(l_path, *l_savePath);
    // #endif  // DEBUGSAVE
}

void DispatchMapHG::getPcdToSite_(std::string& p_sFilePath, std::vector<st_Site>& p_SiteBuf)
{
    std::vector<st_Site> l_SiteBuf;
    l_SiteBuf.assign(p_SiteBuf.begin(), p_SiteBuf.end());
    if (!isExistFileOrFolder(p_sFilePath))
    {
        printf("调度PCD地图不存在: [%s]\n", p_sFilePath.c_str());
        return;
    }
    pointCloudT_Ptr l_pointPtr(new pointCloudT());
    pcl::io::loadPCDFile(p_sFilePath, *(l_pointPtr));
    for (size_t i = 0; i < l_pointPtr->points.size(); i++)
    {
        for (size_t j = 0; j < l_SiteBuf.size(); j++)
        {
            if (l_SiteBuf[j].m_iSiteID == l_pointPtr->points[i].intensity)
            {
                l_SiteBuf[j].m_SitePose.setXYZ(
                    l_pointPtr->points[i].x, l_pointPtr->points[i].y, 0.0);
                break;
            }
        }
    }
    std::string l_path = c_sFilePath + c_sDispatchFileName + "_xy_manualTransPcd.csv";
    saveiteMapXYInfo_(l_path, l_SiteBuf);
}

void DispatchMapHG::siteXYInfoToPcd_(std::string& p_sFilePath, std::vector<st_Site>& p_SiteBuf)
{
    pointCloudT_Ptr l_pointPtr(new pointCloudT());
    T l_Pose;
    for (uint32_t i = 0; i < p_SiteBuf.size(); i++)
    {
        l_Pose.intensity = p_SiteBuf[i].m_iSiteID;
        l_Pose.x = p_SiteBuf[i].m_SitePose.x();
        l_Pose.y = p_SiteBuf[i].m_SitePose.y();
        l_Pose.z = 0;
        l_pointPtr->points.push_back(l_Pose);
    }
    pcl::PCDWriter writer;
    writer.writeBinary(p_sFilePath, *l_pointPtr);
}

#pragma endregion
}  // namespace wj_slam