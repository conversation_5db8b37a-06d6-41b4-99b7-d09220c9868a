#pragma once

#include "../type_site.h"
#include "common/type/type_pose.h"
#include <pcl/common/common.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/io/pcd_io.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/pcl_base.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <stdio.h>
#include <stdlib.h>

#include <Eigen/Core>
#include <algorithm>
#include <fstream>
#include <iostream>
#include <mutex>
#include <string>
#include <thread>
#include <vector>
#define DEBUGSAVE

using namespace std;

namespace wj_slam {

class TrajectoryAlign {
    typedef pcl::PointXYZ T;
    typedef pcl::PointCloud<T> pointCloudT;
    typedef boost::shared_ptr<pointCloudT> pointCloudT_Ptr;
    typedef boost::shared_ptr<pcl::KdTreeFLANN<T>> kdtreeT_Ptr;

    typedef pcl::PointXYZI P;
    typedef pcl::PointCloud<P> pointCloudP;
    typedef boost::shared_ptr<pointCloudP> pointCloudP_Ptr;

  public:
    TrajectoryAlign(std::vector<st_Site>& p_vSiteBuf,
                    std::vector<st_SiteLineInfo>& p_vSiteLineBuf,
                    std::deque<st_Trajectory>& p_vSrcPoseBuf,
                    std::deque<st_Trajectory>& p_vDstPoseBuf);
    ~TrajectoryAlign();

    /*设置地图对齐*/
    void setMapTrans(float p_fMyMap[3], float p_fToMap[3]);

    /*设置文件路径*/
    void setFilePath(std::string a, std::string b, std::string c);

    void setInterPolationSize(float p_value);
    void setSiteSearchSize(float p_value);
    void setPathDownSize(float p_value);
    void setPathCutLineYaw(float p_value);
    void setAlignRotateAng(float p_value);
    void setIgnoreCorrectAng(float p_value);

    /*启动对齐*/
    void startAlign();

    /*输入轨迹、调度是否有效*/
    bool isValidData();

  private:
    std::vector<st_Site>& c_vSiteBuf_;             // 调度站点地图
    std::vector<st_SiteLineInfo>& c_vSiteLineBuf;  // 调度站点线段信息
    std::deque<st_Trajectory>& c_vSrcPoseBuf;      // 源轨迹
    std::deque<st_Trajectory>& c_vDstPoseBuf;      // 目标轨迹
    std::deque<st_Trajectory> c_vSrcPoseDSBuf;     // 源轨迹 采样后
    std::deque<st_Trajectory> c_vDstPoseDsBuf;     // 目标轨迹 即真值轨迹 采样后
    std::deque<st_Trajectory> c_vDstPoseAddPoseBuf;  // 插值后目标轨迹 含源轨迹->目标轨迹转换关系
    s_POSE6D c_stTrans;                              // 地图校正参数

    float c_fTurnYawDiffRes_ = 5.0;      // 转弯点角度变化值
    float c_fPathDownSize_ = 0.3;        // 轨迹下采样参数
    float c_finterpolation_Size_ = 0.5;  // 插值距离参数
    float c_fKdSearchSize_ = 10.0;       // 站点地图搜索轨迹半径
    float c_fRotateYawDiff_ = 0.2;       // 直线旋转角度差
    float c_fIgnoreAngDiff_ = 0.5;       // 角度低于此阈值 不作修正

    std::string c_sFilePath_ = "";            // 文件路径
    std::string c_sDispatchFileName_ = "";    // 调度地图文件名
    std::string c_sTrajectoryFileName_ = "";  //轨迹文件名

    bool c_bIsValid_ = true;  // 输入轨迹、调度是否有效

    pcl::VoxelGrid<T> c_downSizeFilter;

    /*轨迹下采样*/
    void makeTrajectoryDS_(std::deque<st_Trajectory>& p_vSrcPoseBuf,
                           std::deque<st_Trajectory>& p_vDstPoseBuf,
                           std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                           std::deque<st_Trajectory>& p_vDstPoseBufDs);

    /*下采样轨迹*/
    void downSizeWholeTrajectory(std::deque<st_Trajectory>& p_vSrcPoseBuf,
                                 std::deque<st_Trajectory>& p_vDstPoseBuf,
                                 pcl::VoxelGrid<T>& p_downSizeFilter);

    void downSizeLineTrajectory(std::deque<st_Trajectory>& p_vSrcPoseBuf,
                                std::deque<st_Trajectory>& p_vDstPoseBuf,
                                pcl::VoxelGrid<T>& p_downSizeFilter);

    void downSizeLine_(pointCloudT_Ptr& p_filterIn,
                       pointCloudT_Ptr& p_wholeBuf,
                       pcl::VoxelGrid<T>& p_downSizeFilter,
                       std::vector<int>& p_viFilterId);

    /*下采样后搜索最近点id*/
    void findNearestPointId_(pointCloudT_Ptr& p_in1,
                             pointCloudT_Ptr& p_in2,
                             std::vector<int>& p_vSearchId);

    /*轨迹对齐*/
    void align_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                std::deque<st_Trajectory>& p_vDstPoseBufDs);

    void alignICP_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                   std::deque<st_Trajectory>& p_vDstPoseBufDs);

    /*搜索p_vSearchId对应的点 附近最近的点ID*/
    void findNearestId_(std::deque<st_Trajectory>& p_vPoseBuf,
                        std::vector<int>& p_vSearchId,
                        std::vector<int>& p_vNearbyId);

    /*转换点云 可用于kd*/
    void toPointCloud_(std::deque<st_Trajectory>& p_vInputBuf, pointCloudT_Ptr& l_outPointPtr);
    void toPointCloud_I_(std::vector<st_Site>& p_vInputBuf, pointCloudP_Ptr& l_outPointPtr);

    void alignSrcToDst_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                        std::deque<st_Trajectory>& p_vDstPoseBufDs);

    void alignDstToSrc_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                        std::deque<st_Trajectory>& p_vDstPoseBufDs);

    void saveTrajectoryPcd_(std::string p_stPath, std::deque<st_Trajectory>& p_dqSrcPoseBuf);
    /*插值*/
    void interpolation_(std::deque<st_Trajectory>& p_vRawPoseBuf,
                        std::deque<st_Trajectory>& p_vNewPoseBuf);

    bool isAngTooBig_(st_Trajectory& a, st_Trajectory& b);

    s_POSE6D getIncrease_(st_Trajectory& a, st_Trajectory& b);

    bool ableInsert_(st_Trajectory& a, st_Trajectory& b, float& p_fMinLength);

    st_Trajectory makeInsertPoint_(st_Trajectory& a, st_Trajectory& b, float& p_fMinLength);

    s_TWIST transformSlerp_(s_TWIST& a, s_TWIST& b, float& p_fScale);

    void transTrajtoryToSiteMap_(std::deque<st_Trajectory>& p_vPoseBuf,
                                 s_POSE6D& p_MapTrans,
                                 pointCloudT_Ptr& p_NewPosePtr);

    void correctSiteMap_(std::deque<st_Trajectory>& p_vPoseBuf,
                         pointCloudT_Ptr& p_NewPosePtr,
                         std::vector<st_Site>& p_vSiteBuf,
                         float& p_fRadius);

    void correctAng_(int p_siteID, float& p_fAng, s_TWIST p_Trans, float p_fIgnoreAngDiff);

    void
    correctSiteLine_(int p_iSiteID, s_TWIST p_Trans, std::vector<st_SiteLineInfo>& p_vSiteLineBuf);

    template <typename D> inline void clearDeque_(std::deque<D>& vec)
    {
        std::deque<D>().swap(vec);
    }
};
}  // namespace wj_slam