#pragma once

#include "../type_site.h"
#include "common/type/type_pose.h"
#include "tool/fileTool/fileTool.h"
#include <fstream>
#include <iostream>
#include <string>
#include <vector>

#include <pcl/common/common.h>
#include <pcl/io/pcd_io.h>
#include <pcl/pcl_base.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#define DEBUGSAVE
#define MANUALTRANS
using namespace std;

namespace wj_slam {
class DispatchMap {
  public:
    DispatchMap(std::string p_sFilePath, std::string p_sDispatchFileName);
    ~DispatchMap();

    /**
     * @function: loadSiteMapFile
     * @description: 根据不同调度地图保存方式 加载调度地图
     * @return {bool} 加载成功
     * @others: {*}
     */
    virtual bool loadSiteMapFile();

    /**
     * @function: saveSiteMapFile
     * @description: 根据不同调度地图保存方式 保存调度地图
     * @return {bool} 保存成功
     * @others: {*}
     */
    virtual bool saveSiteMapFile();

    /**
     * @function: getSiteXYInfoBuf
     * @description: 获取调度站点XY坐标地图
     * @param {*}
     * @return {std::vector<st_Site>&} 站点地图容器
     * @others: {*}
     */
    std::vector<st_Site>& getSiteXYInfoBuf();

    /**
     * @function: getSiteXYInfoBuf
     * @description: 获取调度站点Yaw地图
     * @param {*}
     * @return {std::vector<st_Site>&} 站点地图角度容器
     * @others: {*}
     */
    std::vector<st_SiteLineInfo>& getSiteYawInfoBuf();

    /**
     * @function: readFile
     * @description: 读取csv文件
     * @param {*}
     * @return {*}
     * @others: {*}
     */
    void readFile(string p_sPathName, vector<vector<string>>& p_vsInfo, const string& c);

    /**
     * @function: splitString
     * @description: 分割str
     * @param {*}
     * @return {*}
     * @others: {*}
     */
    void splitString(string& p_sStr, vector<string>& p_vsSimpleStr, const string& c);

  protected:
    typedef pcl::PointXYZI T;
    typedef pcl::PointCloud<T> pointCloudT;
    typedef boost::shared_ptr<pointCloudT> pointCloudT_Ptr;
    std::string c_sFilePath = "";                // 文件路径
    std::string c_sDispatchFileName = "";        // 调度文件名
    std::vector<st_Site> c_vSiteBuf;             // 站点坐标容器
    std::vector<st_SiteLineInfo> c_SiteInfoBuf;  // 站点线段容器
};

#pragma region 哈工调度
class DispatchMapHG : public DispatchMap {
  public:
    DispatchMapHG(std::string p_sFilePath, std::string p_sDispatchFileName);
    virtual ~DispatchMapHG();

    /**
     * @function: loadSiteMapFile
     * @description: 根据不同调度地图保存方式 加载调度地图
     * @return {bool} 加载成功
     * @others: {*}
     */
    virtual bool loadSiteMapFile();

    /**
     * @function: saveSiteMapFile
     * @description: 根据不同调度地图保存方式 保存调度地图
     * @return {bool} 保存成功
     * @others: {*}
     */
    virtual bool saveSiteMapFile();

  private:
    /**
     * @function: loadSiteMapXYInfo_
     * @description: 加载XY信息
     * @param {std::string& p_stFilePath} XY信息文件路径
     * @param {std::vector<st_Site>&} p_SiteBuf_ 待填充站点数据
     * @return {bool}
     * @others: {*}
     */
    bool loadSiteMapXYInfo_(std::string& p_stPath, std::vector<st_Site>& p_SiteBuf);
    bool saveiteMapXYInfo_(std::string& p_stPath, std::vector<st_Site>& p_SiteBuf);

    /**
     * @function: loadSiteMapYawInfo_
     * @description: 加载Yaw及线段信息
     * @param {std::string& p_stFilePath} Yaw及线段文件路径
     * @param {std::vector<st_SiteLineInfo>&} p_SiteBuf_ 待填充站点线段信息数据
     * @return {bool}
     * @others: {*}
     */
    bool loadSiteMapYawInfo_(std::string& p_stPath, std::vector<st_SiteLineInfo>& p_SiteInfoBuf);
    bool saveSiteMapYawInfo_(std::string& p_stPath, std::vector<st_SiteLineInfo>& p_SiteInfoBuf);

    /**
     * @function: fillAngInfo_
     * @description: 从p_SiteInfoBuf提取角度信息至p_SiteBuf
     * @param {std::vector<st_Site>&} p_SiteBuf_ 待填充站点数据
     * @param {std::vector<st_SiteLineInfo>&} p_SiteInfoBuf 站点线段信息数据
     * @return {bool}
     * @others: {*}
     */
    void fillAngInfo_(std::vector<st_Site>& p_SiteBuf, std::vector<st_SiteLineInfo>& p_SiteInfoBuf);
    void correctAngInfo_(std::vector<st_Site>& p_SiteBuf,
                         std::vector<st_SiteLineInfo>& p_SiteInfoBuf);
    void getPcdToSite_(std::string& p_sFilePath, std::vector<st_Site>& p_SiteBuf);
    void siteXYInfoToPcd_(std::string& p_sFilePath, std::vector<st_Site>& p_SiteBuf);
};
#pragma endregion

}  // namespace wj_slam