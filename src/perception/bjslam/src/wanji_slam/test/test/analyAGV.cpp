/*
 * @Description: 解析AGV-PCAP 保存定位结果 pcd
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-30 12:21:00
 * @LastEditors: wen <EMAIL>
 * @LastEditTime: 2023-04-18 13:56:34
 */

#pragma once
#include <ctime>
#include <iostream>
#include <mutex>
#include <pcap.h>
#include <poll.h>
#include <ros/ros.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <sys/time.h>
#include <thread>
#include <time.h>
#include <unistd.h>

#include "common/common_ex.h"
#include "tool/fileTool/fileTool.h"
#include <boost/function.hpp>
#include <boost/shared_ptr.hpp>
#include <errno.h>
#include <fcntl.h>
#include <netdb.h>
#include <pcl/io/pcd_io.h>
#include <sys/socket.h>
#include <sys/types.h>

typedef unsigned char u8;
typedef char s8;
typedef unsigned short u16;
typedef short s16;
typedef unsigned int u32;
typedef int s32;
typedef long u64;
typedef long unsigned int size_t;

typedef struct NetMsg
{
    u_char m_aucBuf[4096];
    u_int m_uiDataLen = 0;
    sTimeval m_time;
} s_NetMsg;
typedef struct AscProtocol
{
    char m_acCmdType[10];
    char m_acCmd[20];
    char m_acParam[14][20];
} s_AscProtocol;

typedef struct s_ProtocalInfo
{
    s_ProtocalInfo()
    {
        this->m_TimeNetLast.tv_sec = 0;
        this->m_TimeNetLast.tv_usec = 0;
        m_Num = 0;
        m_TimeNetMax = 0;
        m_TimeNetInterval = 0;
        m_TimeMax = 0;
        m_TimeInterval = 0;
        m_TimeNetTransMax = 0;
        m_NetErrorNum = 0;
        m_TimeLast = 0;
    }
    int m_Num;               // 数量
    int m_TimeNetMax;        // 网络时间最长间隔
    int m_TimeNetInterval;   // 网络时间频率
    int m_TimeMax;           // 自带时间最长间隔
    int m_TimeInterval;      // 网络时间频率
    int m_TimeNetTransMax;   // 网络传输时间最长间隔
    int m_NetErrorNum;       // 网络传输时间过长 超过10ms
    sTimeval m_TimeNetLast;  //上次网卡时间
    int m_TimeLast;          // 上次自带时间
} s_ProtocalInfo;

class UnpackAgv {
  public:
    UnpackAgv(std::string p_sPcapPath, std::string p_sPcapName)
    {
        c_sPcapPath = p_sPcapPath;
        c_sAgvPacpPath_ = setAgvPcapFile_(p_sPcapPath, p_sPcapName);
        c_sSavePCDPath_ = setAgvPcapFile_(p_sPcapPath, p_sPcapName, "_Path.pcd");
        c_sSaveSpeedPath_ = setAgvPcapFile_(p_sPcapPath, p_sPcapName, "_speed.csv");
        deleteFileOrDir(c_sSaveSpeedPath_);
    }
    ~UnpackAgv()
    {
        std::lock_guard<std::mutex> l_mutex(c_mutex_);
        if (c_pacp_)
            pcap_close(c_pacp_);
        c_pacp_ = NULL;

        delete c_pstPcapMsg_;
        c_pstPcapMsg_ = NULL;
    }

    void setTrans(double p_transX, double p_transY, double p_transYaw)
    {
        c_transMap_t.x() = p_transX;
        c_transMap_t.y() = p_transY;
        c_transMap_t.z() = 0;
        c_transMap_q = YAW2Quat(p_transYaw);
    }

    void makePathPcd()
    {
        // 通过agvPacp文件是否存在
        if (paramInit_())
        {
            c_bRun_ = agvPcapStart_();
        }

        u_char* l_pucBufTmp = NULL;
        while (c_bRun_)
        {
            if (getPktPacp_(c_pstPcapMsg_))
            {
                c_iCurProcOffset_ = 0;
                while (c_iCurProcOffset_ != c_pstPcapMsg_->m_uiDataLen)
                {
                    l_pucBufTmp = getSICKCMD(c_iCurProcOffset_);
                    if (!l_pucBufTmp)
                        continue;

                    int l_iDataProcLen = strlen((char*)l_pucBufTmp);
                    seprateSICKCmd(l_pucBufTmp, l_iDataProcLen);
                    selectSICKProtocol(c_pstPcapMsg_->m_time);
                }
            }
            else
                break;
        }

        std::cout << std::endl;
        std::cout << "***********"
                  << "数据包统计"
                  << "***********" << std::endl;

        std::cout << "***********"
                  << "接收速度"
                  << "***********" << std::endl;
        std::cout << "**  包数             : " << c_recvSpeed.m_Num << std::endl;
        std::cout << "**  网卡时间平均间隔   : " << c_recvSpeed.m_TimeNetInterval << std::endl;
        std::cout << "**  网卡时间最长间隔   : " << c_recvSpeed.m_TimeNetMax << std::endl;
        std::cout << "**  自带时间最长间隔   : " << c_recvSpeed.m_TimeMax << std::endl;
        std::cout << "**  自带时间平均间隔   : " << c_recvSpeed.m_TimeInterval << std::endl;
        std::cout << "**  网络传输最长耗时   : " << c_recvSpeed.m_TimeNetTransMax << std::endl;
        std::cout << "**  网络传输超时包数量 : " << c_recvSpeed.m_NetErrorNum << std::endl;
        std::cout << std::endl;

        std::cout << "***********"
                  << "请求位置"
                  << "***********" << std::endl;
        std::cout << "**  包数             : " << c_recvGetPose.m_Num << std::endl;
        std::cout << "**  网卡时间平均间隔   : " << c_recvGetPose.m_TimeNetInterval << std::endl;
        std::cout << "**  网卡时间最长间隔   : " << c_recvGetPose.m_TimeNetMax << std::endl;
        std::cout << "**  自带时间最长间隔   : " << c_recvGetPose.m_TimeMax << std::endl;
        std::cout << "**  自带时间平均间隔   : " << c_recvGetPose.m_TimeInterval << std::endl;
        std::cout << "**  网络传输最长耗时   : " << c_recvGetPose.m_TimeNetTransMax << std::endl;
        std::cout << "**  网络传输超时包数量 : " << c_recvGetPose.m_NetErrorNum << std::endl;
        std::cout << std::endl;

        std::cout << "***********"
                  << "发送位置"
                  << "***********" << std::endl;
        std::cout << "**  包数             : " << c_sendPose.m_Num << std::endl;
        std::cout << "**  网卡时间平均间隔   : " << c_sendPose.m_TimeNetInterval << std::endl;
        std::cout << "**  网卡时间最长间隔   : " << c_sendPose.m_TimeNetMax << std::endl;
        std::cout << "**  自带时间最长间隔   : " << c_sendPose.m_TimeMax << std::endl;
        std::cout << "**  自带时间平均间隔   : " << c_sendPose.m_TimeInterval << std::endl;
        std::cout << "**  网络传输最长耗时   : " << c_sendPose.m_TimeNetTransMax << std::endl;
        std::cout << "**  网络传输超时包数量 : " << c_sendPose.m_NetErrorNum << std::endl;
        std::cout << std::endl;

        std::cout << "***********"
                  << "send1610"
                  << "***********" << std::endl;
        std::cout << "**  包数             : " << c_send1610.m_Num << std::endl;
        std::cout << "**  网卡时间最长间隔   : " << c_send1610.m_TimeNetMax << std::endl;
        std::cout << "**  自带时间最长间隔   : " << c_send1610.m_TimeMax << std::endl;
        std::cout << "**  网络传输最长耗时   : " << c_send1610.m_TimeNetTransMax << std::endl;
        std::cout << "**  网络传输超时包数量 : " << c_send1610.m_NetErrorNum << std::endl;
        std::cout << std::endl;

        if (c_pathPtr_->size())
        {
            // transformFromSlamMap(c_transMap_t, c_transMap_q);
            pcl::PCDWriter writer;
            writer.writeBinary(c_sSavePCDPath_.c_str(), *c_pathPtr_);
            std::cout << "***********"
                      << "生成Path"
                      << "***********" << std::endl;
            std::cout << "**  生成Path路径  : " << c_sSavePCDPath_.c_str() << std::endl;
        }
        std::cout << "********************************" << std::endl;
        std::cout << std::endl;
    }

  private:
#pragma region 变量
    std::string c_sAgvPacpPath_ = "";
    std::string c_sSavePCDPath_ = "";
    std::string c_sSaveSpeedPath_ = "";
    std::string c_sPcapPath = "";

    bool c_bRun_;
    pcap_t* c_pacp_;
    bpf_program c_pcapPktFilter_;
    std::mutex c_mutex_;

    char errbuf_[PCAP_ERRBUF_SIZE];
    int c_iSleepTime = 1000;

    s_NetMsg* c_pstPcapMsg_ = NULL;
    int c_iCurProcOffset_ = 0;
    int c_uiAllPktNum_ = 0;
    s_AscProtocol askProtocol_;

    pcl::PointCloud<pcl::PointXYZHSV>::Ptr c_pathPtr_;
    Eigen::Vector3d c_transMap_t = Eigen::Vector3d::Zero();
    Eigen::Quaterniond c_transMap_q = Eigen::Quaterniond::Identity();

    int c_cTimeList[6] = {0, 0, 0, 0, 0, 0};

    s_ProtocalInfo c_recvSpeed;
    s_ProtocalInfo c_recvGetPose;
    s_ProtocalInfo c_sendPose;
    s_ProtocalInfo c_send1610;
    sTimeval c_firstSpeedNetT;

#pragma endregion

#pragma region 函数
    std::string setAgvPcapFile_(std::string p_sDataFilesPath,
                                std::string p_sPcapName,
                                std::string p_sTail = ".pcapng")
    {
        std::string l_sPcapPath = "";
        std::string l_sDataFilesPath = p_sDataFilesPath;
        if ('/' != l_sDataFilesPath[l_sDataFilesPath.length() - 1])
            l_sDataFilesPath += "/";
        l_sPcapPath = l_sDataFilesPath + p_sPcapName + p_sTail;
        return l_sPcapPath;
    }

    bool paramInit_()
    {
        c_pacp_ = NULL;
        c_pathPtr_.reset(new pcl::PointCloud<pcl::PointXYZHSV>());
        c_pstPcapMsg_ = new s_NetMsg();
        if (!isExistFileOrFolder(c_sAgvPacpPath_))
        {
            fprintf(stderr, "agv pcap file: %s is non-existent\n", c_sAgvPacpPath_.c_str());
            return false;
        }

        if ((c_pacp_ = pcap_open_offline(c_sAgvPacpPath_.c_str(), errbuf_)) == NULL)
        {
            fprintf(stderr, "Error opening agvPcap file \n");
            return false;
        }
        return true;
    }

    bool agvPcapStart_()
    {
        bool l_res = false;
        /*
         * pcap模式要求pcap文件必须存在
         * 1web切换工作模式调用 调用输入的pcap肯定存在
         * 2初始化调用前已经确认pcap文件存在
         */
        std::lock_guard<std::mutex> l_mutex(c_mutex_);
        if (c_pacp_)
            pcap_close(c_pacp_);

        std::cout << std::endl;
        std::cout << "**  打开数据包    : " << c_sAgvPacpPath_.c_str() << std::endl;

        if ((c_pacp_ = pcap_open_offline(c_sAgvPacpPath_.c_str(), errbuf_)) == NULL)
        {
            fprintf(stderr, "Error opening wjlidar socket dump file\n");
            l_res = false;
        }
        else
            l_res = true;

        if (l_res)
        {
            std::string l_filter = "port " + std::to_string(2112);
            std::stringstream filter;
            pcap_compile(c_pacp_, &c_pcapPktFilter_, l_filter.c_str(), 1, 0);
            pcap_setfilter(c_pacp_, &c_pcapPktFilter_);
        }

        c_uiAllPktNum_ = 0;

        return l_res;
    }

    bool getPktPacp_(s_NetMsg* p_agvdata)
    {
        static int l_iVaildPkgNum = 0;
        struct pcap_pkthdr* l_stHeader;
        const u_char* l_ucPktData;
        bool res = false;
        int l_iJumpPkgNum = 1;
        while (true)
        {
            if (!c_pacp_)
                return false;

            {
                std::lock_guard<std::mutex> l_mutex(c_mutex_);
                while (l_iJumpPkgNum)
                {
                    if (pcap_next_ex(c_pacp_, &l_stHeader, &l_ucPktData) >= 0)
                    {
                        c_uiAllPktNum_++;
                        if (0 == pcap_offline_filter(&c_pcapPktFilter_, l_stHeader, l_ucPktData))
                            continue;
                        // 空包跳过
                        if (l_stHeader->len <= 66)
                            continue;

                        int l_iDataNum = l_stHeader->len - 66;
                        if (l_iDataNum < 4096)
                        {
                            memcpy(&p_agvdata->m_aucBuf[0], l_ucPktData + 66, l_iDataNum);
                            p_agvdata->m_uiDataLen = l_iDataNum;
                            p_agvdata->m_time = l_stHeader->ts;
                        }
                        l_iVaildPkgNum++;
                        res = true;
                    }
                    l_iJumpPkgNum--;
                }
            }

            if (!res)
            {
                std::cout << "**  关闭数据包 " << std::endl;
                pcap_close(c_pacp_);
                c_pacp_ = NULL;
            }

            return res;
        }
    }

    u_char* getSICKCMD(int& p_iOffset)
    {
        int l_iSta = -1, l_iEnd = -1;
        while (p_iOffset != c_pstPcapMsg_->m_uiDataLen)
        {
            if (c_pstPcapMsg_->m_aucBuf[p_iOffset] == 0x02)
            {
                l_iSta = p_iOffset;
                p_iOffset = (p_iOffset + 1) % 4096;
            }
            else if (c_pstPcapMsg_->m_aucBuf[p_iOffset] == 0x03 && l_iSta != -1)
            {
                l_iEnd = p_iOffset;
                p_iOffset = (p_iOffset + 1) % 4096;
            }
            else
            {
                p_iOffset = (p_iOffset + 1) % 4096;
            }
            if (l_iSta != -1 && l_iEnd != -1)
            {
                int dataProcLen = (l_iEnd - l_iSta + 1 + 4096) % 4096;

                u_char* buftmp = new u_char[dataProcLen - 1];
                if (l_iSta > l_iEnd)
                {
                    memcpy(buftmp, &c_pstPcapMsg_->m_aucBuf[l_iSta + 1], 4096 - l_iSta - 1);
                    if (l_iEnd > 0)
                        memcpy(&buftmp[4096 - l_iSta - 1], c_pstPcapMsg_->m_aucBuf, l_iEnd);
                }
                else
                    memcpy(buftmp, &c_pstPcapMsg_->m_aucBuf[l_iSta + 1], dataProcLen - 2);
                buftmp[dataProcLen - 2] = '\0';

                if (1)
                {
                    char l_buf[4096];
                    std::string l_sBuf = "";
                    memcpy(&l_buf, &buftmp[0], dataProcLen);
                    l_sBuf = l_buf;
                }

                return buftmp;
            }
        }
        return NULL;
    }

    void seprateSICKCmd(u_char* p_pucBuf, int p_iLen)
    {
        int l_iOffset = 0;
        int l_iSta = 0, l_iEnd = 0;
        int l_iCMDCnt = 0;
        memset(&askProtocol_, 0, sizeof(s_AscProtocol));
        while (l_iOffset < p_iLen)
        {
            if (p_pucBuf[l_iOffset] == 0x20)
            {
                l_iEnd = l_iOffset;
                if (l_iCMDCnt == 0)
                {
                    memcpy(askProtocol_.m_acCmdType, &p_pucBuf[l_iSta], l_iEnd - l_iSta);
                    if ((l_iEnd - l_iSta > 10) || (l_iEnd - l_iSta < 0))
                    {
                        std::cout << "wrong 1!!" << std::endl;
                        break;
                    }
                }
                else if (l_iCMDCnt == 1)
                {
                    memcpy(askProtocol_.m_acCmd, &p_pucBuf[l_iSta], l_iEnd - l_iSta);
                    if ((l_iEnd - l_iSta > 20) || (l_iEnd - l_iSta < 0))
                    {
                        std::cout << "wrong 2!!" << std::endl;
                        break;
                    }
                }
                else
                {
                    memcpy(&askProtocol_.m_acParam[l_iCMDCnt - 2][0],
                           &p_pucBuf[l_iSta],
                           l_iEnd - l_iSta);
                }
                l_iSta = l_iOffset + 1;
                if (l_iCMDCnt > 14)
                {
                    printf("buf too long: %d", l_iCMDCnt);
                    break;
                }
                l_iCMDCnt++;

                // l_iSta = l_iOffset + 1;
            }

            l_iOffset++;
        }
        memcpy(&askProtocol_.m_acParam[l_iCMDCnt - 2][0], &p_pucBuf[l_iSta], p_iLen - l_iSta);
    }

    int selectSICKProtocol(sTimeval p_stNetTime)
    {
        int l_iLen = 0;
        int l_time = 0;
        if ((!strcmp("sAN", askProtocol_.m_acCmdType))
            && (!strcmp("mNPOSSetSpeed", askProtocol_.m_acCmd)))
        {
            return l_iLen;
        }
        else if ((!strcmp("sMA", askProtocol_.m_acCmdType))
                 && (!strcmp("mNPOSGetPose", askProtocol_.m_acCmd)))
        {
            calculationTime(p_stNetTime, 0, &c_recvGetPose, "recvGetPose", 150);
            return l_iLen;
        }
        else if ((!strcmp("sAN", askProtocol_.m_acCmdType))
                 && (!strcmp("mNPOSGetPose", askProtocol_.m_acCmd)))
        {
            if (std::atoi(askProtocol_.m_acParam[0]) == 1
                && std::atoi(askProtocol_.m_acParam[1]) == 0
                && std::atoi(askProtocol_.m_acParam[3]) == 1)
            {
                int l_poseX = (s32)HexStr2U32(&askProtocol_.m_acParam[4][0]);
                int l_poseY = (s32)HexStr2U32(&askProtocol_.m_acParam[5][0]);
                int l_poseZ = (u32)HexStr2U32(&askProtocol_.m_acParam[6][0]);
                l_time = (u32)HexStr2U32(&askProtocol_.m_acParam[9][0]);
                int flag = (u32)HexStr2U32(&askProtocol_.m_acParam[11][0]);
                pcl::PointXYZHSV l_point;
                l_point.x = l_poseX * 0.001;
                l_point.y = l_poseY * 0.001;
                l_point.z = 0;
                l_point.h = flag;             // 传入角度
                l_point.s = l_poseZ * 0.001;  // 传入网络时间 s
                l_point.v = l_time;           // 传入网络时间 ms
                c_pathPtr_->push_back(l_point);

                calculationTime(p_stNetTime, l_time, &c_sendPose, "sendPose", 150);
            }
            else if (std::atoi(askProtocol_.m_acParam[0]) == 1
                     && std::atoi(askProtocol_.m_acParam[1]) == 6
                     && std::atoi(askProtocol_.m_acParam[2]) == 1
                     && std::atoi(askProtocol_.m_acParam[3]) == 0)
            {
                calculationTime(p_stNetTime, 0, &c_send1610, "send1610");
            }
            else
            {
                printf("mNPOSGetPose ERROR\n");
            }

            return l_iLen;
        }
        else if ((!strcmp("sMN", askProtocol_.m_acCmdType))
                 && (!strcmp("mNPOSSetSpeed", askProtocol_.m_acCmd)))
        {
            l_time = (u32)HexStr2U32(&askProtocol_.m_acParam[3][0]);
            s16 l_s16speedx = (s16)HexStr2U32(&askProtocol_.m_acParam[0][0]);
            s16 l_s16speedy = (s16)HexStr2U32(&askProtocol_.m_acParam[1][0]);
            //这里的角度
            s16 l_s32spdang = (s32)HexStr2U32(&askProtocol_.m_acParam[2][0]);
            calculationTime(p_stNetTime, l_time, &c_recvSpeed, "recvSpeed", 50);
            saveSpeedInFile(p_stNetTime,
                            l_time,
                            l_s16speedx / 10000.0,
                            l_s16speedy / 10000.0,
                            l_s32spdang / 10000.0,
                            c_sSaveSpeedPath_);
            return l_iLen;
        }
        else if ((!strcmp("sMN", askProtocol_.m_acCmdType))
                 && (!strcmp("mNPOSGetPose", askProtocol_.m_acCmd)))
        {
            return l_iLen;
        }
        else
        {
            printf("protocol not set\n");
            return 0;
        }
    }

    u32 HexStr2U32(void* Str)
    {
        unsigned int tmp = 0;
        char* p = (char*)Str;
        while (1)
        {
            if (*p >= '0' && *p <= '9')
            {
                tmp *= 16;
                tmp += *p - '0';
            }
            else if (*p >= 'a' && *p <= 'f')
            {
                tmp *= 16;
                tmp += *p - 'a' + 10;
            }
            else if (*p >= 'A' && *p <= 'F')
            {
                tmp *= 16;
                tmp += *p - 'A' + 10;
            }
            else
            {
                break;
            }
            p++;
        }
        return tmp;
    }

    void transformFromSlamMap(Eigen::Vector3d l_tTrans, Eigen::Quaterniond l_qTrans)
    {
        pcl::PointXYZHSV l_point;
        Eigen::Vector3d l_transMap_t = Eigen::Vector3d::Zero();
        Eigen::Quaterniond l_transMap_q = Eigen::Quaterniond::Identity();
        Eigen::Vector3d l_raw_t = Eigen::Vector3d::Zero();
        Eigen::Quaterniond l_raw_q = Eigen::Quaterniond::Identity();
        for (int i = 0; i < c_pathPtr_->points.size(); i++)
        {
            l_point = c_pathPtr_->points[i];
            l_transMap_t.x() = l_point.x;
            l_transMap_t.y() = l_point.y;
            l_transMap_t.z() = 0;

            l_raw_t = l_qTrans.conjugate() * (l_transMap_t - l_tTrans);

            double l_raw_yawDeg = l_point.z - toYawIn2PI(l_qTrans);
            l_raw_q = YAW2Quat(l_raw_yawDeg);

            l_point.x = l_raw_t.x();
            l_point.y = l_raw_t.y();
            l_point.z = 0;

            c_pathPtr_->points[i] = l_point;
        }
    }

    Eigen::Quaterniond YAW2Quat(double Yaw)
    {
        const Eigen::AngleAxisd roll_angle(0.0, Eigen::Vector3d::UnitX());
        const Eigen::AngleAxisd pitch_angle(0.0, Eigen::Vector3d::UnitY());
        const Eigen::AngleAxisd yaw_angle(Yaw, Eigen::Vector3d::UnitZ());
        return yaw_angle * pitch_angle * roll_angle;
    }

    double toYawIn2PI(const Eigen::Quaterniond& q)
    {
        // yaw (z-axis rotation)
        double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
        double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
        double l_yaw = atan2(siny_cosp, cosy_cosp);

        if (l_yaw < 0.0)
            return l_yaw + 2 * M_PI;
        else if (l_yaw > 2 * M_PI)
            return l_yaw - 2 * M_PI;
        else
            return l_yaw;
    }
    void saveSpeedInFile(sTimeval p_timeNet,
                         int p_time,
                         float p_x,
                         float p_y,
                         float p_yaw,
                         std::string p_sFilePath)
    {
        static int l_iLastTime = 0;
        if (l_iLastTime == 0)
        {
            l_iLastTime = p_time;
            c_firstSpeedNetT = p_timeNet;
        }
        int l_iInterval = wj_slam::getTimeDiffMs(p_timeNet, c_firstSpeedNetT);
        std::fstream l_filePoseWR;
        l_filePoseWR.open(p_sFilePath.c_str(), std::ios::app);
        if (l_filePoseWR.is_open())
        {
            l_filePoseWR << l_iInterval << "," << p_time << "," << p_time - l_iLastTime << ","
                         << p_x << "," << p_y << "," << p_yaw << std::endl;
            l_filePoseWR.close();
        }
        c_firstSpeedNetT = p_timeNet;
        l_iLastTime = p_time;
    }

    void calculationTime(sTimeval p_timeNet,
                         int p_time,
                         s_ProtocalInfo* p_info,
                         std::string p_cmd,
                         int p_iT = 0)
    {
        int l_timeNetDiff = 0, l_timeDiff = 0;
        if (p_time)
        {
            // 统计协议自带time 最长间隔
            if (!p_info->m_TimeLast)
                p_info->m_TimeLast = p_time;
            l_timeDiff = p_time - p_info->m_TimeLast;

            p_info->m_TimeMax = (l_timeDiff > p_info->m_TimeMax) ? l_timeDiff : p_info->m_TimeMax;

            // if(l_timeDiff < p_iT-10)
            //     printf("[%s][%d] %d | %d - %d \n",p_cmd.c_str(), c_uiAllPktNum_, l_timeDiff,
            //     p_time, p_info->m_TimeLast);
            if (p_info->m_TimeInterval)
                p_info->m_TimeInterval =
                    (p_info->m_TimeInterval * (p_info->m_Num) + l_timeDiff) / (p_info->m_Num + 1);
            else
                p_info->m_TimeInterval = p_iT;
        }

        {
            if (!p_info->m_TimeNetLast.tv_sec)
                p_info->m_TimeNetLast = p_timeNet;

            // 统计网络时间 最长间隔
            l_timeNetDiff = wj_slam::getTimeDiffMs(p_timeNet, p_info->m_TimeNetLast);

            p_info->m_TimeNetLast = p_timeNet;
            p_info->m_TimeNetMax =
                (l_timeNetDiff > p_info->m_TimeNetMax) ? l_timeNetDiff : p_info->m_TimeNetMax;

            if (p_info->m_TimeNetInterval)
                p_info->m_TimeNetInterval =
                    (p_info->m_TimeNetInterval * p_info->m_Num + l_timeNetDiff)
                    / (p_info->m_Num + 1);
            else
                p_info->m_TimeNetInterval = p_iT;
        }

        // 统计网络传输超时情况 但需要协议自带时间
        if (l_timeDiff)
        {
            int l_timeTransDiff = fabs(l_timeNetDiff - l_timeDiff);
            p_info->m_TimeNetTransMax = (l_timeTransDiff > p_info->m_TimeNetTransMax)
                                            ? l_timeTransDiff
                                            : p_info->m_TimeNetTransMax;

            //超时次数
            if (l_timeTransDiff > 10)
                p_info->m_NetErrorNum++;
        }

        if (l_timeDiff - p_iT > 20 || l_timeNetDiff - p_iT > 20)
            saveLog_(p_info,
                     c_uiAllPktNum_,
                     l_timeNetDiff,
                     l_timeDiff,
                     p_timeNet,
                     p_time,
                     setAgvPcapFile_(c_sPcapPath, p_cmd, ".csv"));

        p_info->m_TimeNetLast = p_timeNet;
        p_info->m_TimeLast = p_time;
        p_info->m_Num++;
    }

    void saveLog_(s_ProtocalInfo* p_info,
                  int p_iPktNum,
                  int p_timeNetDiff,
                  int p_timeDiff,
                  sTimeval p_timeNetNow,
                  int p_timeNow,
                  std::string p_sFilePath)
    {
        std::fstream l_filePoseWR;
        l_filePoseWR.open(p_sFilePath.c_str(), std::ios::app);
        if (l_filePoseWR.is_open())
        {
            l_filePoseWR << p_iPktNum << "," << getStrTime(&p_timeNetNow) << "," << p_timeNetDiff
                         << "," << p_timeDiff << "," << p_timeNow << "," << p_info->m_TimeLast
                         << std::endl;
            l_filePoseWR.close();
        }
    }

    std::string getStrTime(sTimeval* p_timeNet)
    {
        std::string l_str = "";
        tm* ltm = localtime(&p_timeNet->tv_sec);
        l_str = std::to_string(ltm->tm_hour) + "_" + std::to_string(ltm->tm_min) + "_"
                + std::to_string(ltm->tm_sec);
        return l_str;
    }

#pragma endregion
};

int main(int argc, char const* argv[])
{
    float l_trans[6];
    if (argc < 3)
    {
        std::cout << "输入  agvPcap文件地址  文件名 是否平移地图(y/n，忽略则为否)" << std::endl;
        return -1;
    }
    std::string path = argv[1];
    std::string filename = argv[2];
    std::string isTrans = "";
    UnpackAgv c_unpackAgv(path, filename);

    if (argc == 4)
    {
        isTrans = argv[3];
        if (isTrans == "y")
        {
            std::cout << "myMap_x:" << std::endl;
            std::cin >> l_trans[0];
            std::cout << "myMap_y:" << std::endl;
            std::cin >> l_trans[1];
            std::cout << "myMap_yaw:" << std::endl;
            std::cin >> l_trans[2];
            std::cout << "toMap_x:" << std::endl;
            std::cin >> l_trans[3];
            std::cout << "toMap_y:" << std::endl;
            std::cin >> l_trans[4];
            std::cout << "toMap_yaw:" << std::endl;
            std::cin >> l_trans[5];
        }
        else if (isTrans == "cd")
        {
            l_trans[0] = 0;
            l_trans[1] = 0;
            l_trans[2] = 0;
            l_trans[3] = 250.0;
            l_trans[4] = 400.0;
            l_trans[5] = 0;
        }
    }
    double l_f64YawAngleDiff = (l_trans[5] - l_trans[2]) * M_PI / 180.0;
    // 历史遗留问题: 无法改动
    if (isTrans == "cd")
        l_f64YawAngleDiff = (l_trans[5] - l_trans[2]);

    double l_f64x = l_trans[0] * cos(l_f64YawAngleDiff) - l_trans[1] * sin(l_f64YawAngleDiff);
    double l_f64y = l_trans[0] * sin(l_f64YawAngleDiff) + l_trans[1] * cos(l_f64YawAngleDiff);

    c_unpackAgv.setTrans(l_trans[3] - l_f64x, l_trans[4] - l_f64y, (l_trans[5] - l_trans[2]));
    c_unpackAgv.makePathPcd();
    return 0;
}