/**
 * @file transformData.cpp
 * <AUTHOR> name
 * @brief 对建图过程数据保存的文件进行解密
 * @version 1.0
 * @date 2023-10-08
 * @copyright Copyright (c)2023 Vanjee
 */
#include "algorithm/map/secret_map/laserIO.h"
#include "common/common_ex.h"
#include "tool/fileTool/fileTool.h"
#include "algorithm/map/sub_map/KeyFrameMap.h"
#include <pcl/io/pcd_io.h>
#include "algorithm/loop/mappingStorage.hpp"
typedef pcl ::PointXYZHSV PointType;

using namespace wj_slam;

int main(int argc, char const* argv[])
{
    if (argc < 2)
    {
        std::cout << "输入  文件地址" << std::endl;
        return -1;
    }
    using Frame = KEYFRAME<PointType>;                          /**< 输入帧-特征组类型 */
    using FramePtr = typename Frame::Ptr;               /**< 输入帧-特征组指针类型 */
    // std::vector<FramePtr> c_vMaps_;          /**< 地图队列 */
    boost::shared_ptr<KfMapPair> c_pMapPair_ = nullptr; /**< 关键帧队列管理对象*/
    std::string path = argv[1];
    boost::shared_ptr<MappingStorage<PointType>> c_pMapStor_;
    c_pMapStor_.reset(new MappingStorage<PointType>(c_pMapPair_));
    c_pMapStor_->decryptBinary(path+"/mapdata.wj",path+"/mapdata.txt");
    c_pMapStor_->decryptBinary(path+"/Graphdata.wj",path+"/Graphdata.txt",true);
    c_pMapStor_->readKFBinary(path+"/KF.gz",path+"/KF_");
    return 0;
}