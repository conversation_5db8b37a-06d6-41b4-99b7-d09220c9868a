/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-10-18 13:38:32
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-01-06 18:29:55
 */
#include "algorithm/map/secret_map/laserIO.h"
#include "common/common_ex.h"
#include "tool/fileTool/fileTool.h"
#include <pcl/io/pcd_io.h>
typedef pcl ::PointXYZ BaseType;
typedef pcl ::PointXYZHSV PointType;
typedef pcl ::PointXYZI PathType;

using namespace wj_slam;

int main(int argc, char const* argv[])
{
    if (argc < 3)
    {
        std::cout << "输入  文件地址  文件名" << std::endl;
        return -1;
    }
    KEYFRAME<BaseType>::Ptr l_mapV1(new KEYFRAME<BaseType>());
    KEYFRAME<PointType>::Ptr l_mapV2(new KEYFRAME<PointType>());
    pcl::PointCloud<BaseType>::Ptr c_pcKeyFramesPoseV1_(new pcl::PointCloud<BaseType>());
    pcl::PointCloud<PathType>::Ptr c_pcKeyFramesPoseV2_(new pcl::PointCloud<PathType>());
    std::string path = argv[1];
    std::string filename = argv[2];
    int turnFlag = 0;
    std::cout << "This App Version is : V1<->V2 & xyz<->xyzhsv" << std::endl;
    std::cout << "choose turn mode:" << std::endl;
    std::cout << "1: V1-->V2" << std::endl;
    std::cout << "2: V2-->V1" << std::endl;
    std::cin >> turnFlag;
    boost::shared_ptr<LaserIO<BaseType, BaseType>> c_pReadWriteMapV1_;
    boost::shared_ptr<LaserIO<PointType, PathType>> c_pReadWriteMapV2_;
    c_pReadWriteMapV1_.reset(new LaserIO<BaseType, BaseType>());
    c_pReadWriteMapV2_.reset(new LaserIO<PointType, PathType>());
    pcl::PCDWriter writer;
    std::vector<typename pcl::PointCloud<BaseType>::Ptr> l_featureV1;
    std::vector<typename pcl::PointCloud<PointType>::Ptr> l_featureV2;
    l_featureV1.push_back(l_mapV1->m_pFeature->first);
    l_featureV1.push_back(l_mapV1->m_pFeature->second);
    l_featureV1.push_back(l_mapV1->m_pFeature->fourth);
    l_featureV2.push_back(l_mapV2->m_pFeature->first);
    l_featureV2.push_back(l_mapV2->m_pFeature->second);
    l_featureV2.push_back(l_mapV2->m_pFeature->fourth);

    std::vector<typename pcl::PointCloud<BaseType>::Ptr> l_pcVisibleV1;
    std::vector<typename pcl::PointCloud<PointType>::Ptr> l_pcVisibleV2;
    l_pcVisibleV2.push_back(l_mapV2->m_pFeature->allPC);
    pcl::PointCloud<BaseType>::Ptr c_pMap2DV1(new pcl::PointCloud<BaseType>());
    pcl::PointCloud<PointType>::Ptr c_pMap2DV2(new pcl::PointCloud<PointType>());
    l_pcVisibleV2.push_back(c_pMap2DV2);

    switch (turnFlag)
    {
        case 1:
        {
            if (isExistFileOrFolder(path + "/" + filename + ".pcd"))
                pcl::io::loadPCDFile<BaseType>(path + "/" + filename + ".pcd",
                                               *(l_mapV1->m_pFeature->allPC));
            else
            {
                printf("加密异常 |未找到%s.pcd\n", filename.c_str());
                break;
            }
            if (isExistFileOrFolder(path + "/" + filename + "_2d.pcd"))
                pcl::io::loadPCDFile<BaseType>(path + "/" + filename + "_2d.pcd", *(c_pMap2DV1));
            else
            {
                printf("加密异常 |未找到%s_2d.pcd\n", filename.c_str());
                break;
            }

            if (!isExistFileOrFolder(path + "/." + filename + ".wj"))
            {
                printf("[error] no find %s/.%s.wj\n", path.c_str(), filename.c_str());
                break;
            }
            c_pReadWriteMapV1_->setVersion(1);
            if (!c_pReadWriteMapV1_->readBinary(path + "/." + filename + ".wj",
                                                c_pcKeyFramesPoseV1_,
                                                l_featureV1,
                                                l_pcVisibleV1))
            {
                printf("读取V1地图失败\n");
                break;
            }

            // path
            pcl::copyPointCloud<BaseType, PathType>(*c_pcKeyFramesPoseV1_, *c_pcKeyFramesPoseV2_);
            // feature
            for (int i = 0; i < (int)l_featureV1.size(); i++)
                pcl::copyPointCloud<BaseType, PointType>(*l_featureV1[i], *l_featureV2[i]);
            // 可视化地图
            pcl::copyPointCloud<BaseType, PointType>(*(l_mapV1->m_pFeature->allPC),
                                                     *(l_mapV2->m_pFeature->allPC));
            pcl::copyPointCloud<BaseType, PointType>(*c_pMap2DV1, *c_pMap2DV2);

            std::string l_sPath = path + "/" + filename + ".wj";
            c_pReadWriteMapV2_->setVersion(2);
            if (!c_pReadWriteMapV2_->writeBinary(path + "/" + filename + ".wj",
                                                 c_pcKeyFramesPoseV2_,
                                                 l_featureV2,
                                                 l_pcVisibleV2))
                printf("转换失败，生成V2加密文件：%s错误\n", l_sPath.c_str());
            printf("转换V2地图成功，生成文件：%s\n", l_sPath.c_str());
            break;
        }
        case 2:
        {
            if (!isExistFileOrFolder(path + "/" + filename + ".wj"))
            {
                printf("[error] no find %s/%s.wj\n", path.c_str(), filename.c_str());
                break;
            }

            c_pReadWriteMapV2_->setVersion(2);
            if (!c_pReadWriteMapV2_->readBinary(path + "/" + filename + ".wj",
                                                c_pcKeyFramesPoseV2_,
                                                l_featureV2,
                                                l_pcVisibleV2))
            {
                printf("读取V2地图失败\n");
                break;
            }

            // path
            pcl::copyPointCloud<PathType, BaseType>(*c_pcKeyFramesPoseV2_, *c_pcKeyFramesPoseV1_);
            // feature
            for (int i = 0; i < (int)l_featureV1.size(); i++)
                pcl::copyPointCloud<PointType, BaseType>(*l_featureV2[i], *l_featureV1[i]);
            // 可视化地图
            // pcl::copyPointCloud<PointType, BaseType>(*(l_mapV2->m_pFeature->allPC),
            // *(l_mapV1->m_pFeature->allPC));
            pcl::copyPointCloud<PointType, BaseType>(*c_pMap2DV2, *c_pMap2DV1);

            std::string l_sPath = path + "/." + filename + ".wj";
            c_pReadWriteMapV1_->setVersion(1);
            if (!c_pReadWriteMapV1_->writeBinary(path + "/." + filename + ".wj",
                                                 c_pcKeyFramesPoseV1_,
                                                 l_featureV1,
                                                 l_pcVisibleV1))
            {
                printf("转换失败，生成V1加密隐藏文件：%s错误\n", l_sPath.c_str());
            }

            pcl::copyPointCloud<PointType, BaseType>(*(l_mapV2->m_pFeature->allPC),
                                                     *(l_mapV1->m_pFeature->allPC));
            if (l_mapV1->m_pFeature->allPC->size())
                writer.writeBinary(path + "/" + filename + ".pcd", *l_mapV1->m_pFeature->allPC);
            else
                printf("解密异常 | 3D 为空\n");

            if (c_pMap2DV1->size())
                writer.writeBinary(path + "/" + filename + "_2d.pcd", *c_pMap2DV1);
            else
                printf("解密异常 | 2D 为空\n");

            printf("转换V1地图成功，生成隐藏文件：%s\n", l_sPath.c_str());
            break;
        }
        default: break;
    }
    return 0;
}