/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-10-18 13:38:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 19:11:27
 */
#include "algorithm/preproc/driver/input.h"
#include "tool/fileTool/fileTool.h"
#include <fstream>
#include <pcap.h>
#include <time.h>
constexpr static const int RAW_SCAN_SIZE = 4;     // 占用字节 （距离2 脉宽1 置信度1）
constexpr static const int SCANS_PER_BLOCK = 19;  // 发光通道19线
constexpr static const int BLOCK_DATA_SIZE =
    (SCANS_PER_BLOCK * RAW_SCAN_SIZE);              //  19*4 每块数据长度
constexpr static const int BLOCKS_PER_PACKET = 15;  // 120包每包15块 每块80字节
struct s_pcapInfo
{
    uint32_t m_uiSrcPort;
    uint32_t m_uiDstPort;
    std::string m_sSrcIP;
    std::string m_sDstIP;
    std::string m_sProtocalMode;  // 0x11[17] UDP  | 0x06[6] TCP
};
typedef struct RawBlock
{
    uint16_t header;
    uint16_t rotation;
    uint8_t data[BLOCK_DATA_SIZE];  // data[19*4]
} s_RawBlock;

typedef struct RawAddtionMsg
{
    uint16_t header;
    uint8_t rads[2];
    uint8_t time[6];
    uint8_t nsec[4];
    uint8_t angVel[6];
    uint8_t accel[6];
} s_RawAddtionMsg;

typedef struct RawPacket
{
    s_RawBlock blocks[BLOCKS_PER_PACKET];  // blocks[15]
    s_RawAddtionMsg addmsg;
} s_RawPacket;
std::string c_ErrorInfo[4] = {"雷达异常", "网络连接异常", "网络阻塞", "未定义"};
using namespace wj_slam;
pcap_t* c_pacp = NULL;
s_pcapInfo c_stPcapInfo;
bpf_program c_pcapPktFilter_;
char errbuf_[4096];
uint32_t c_uiOffset_ = 0;       // 读取数据偏移
uint32_t c_uiDiscardNum_ = 30;  // 丢弃字节数

bool pcapInit_(std::string p_sDataFilePath);
bool pcapFilter(std::string l_sFileName_);
bool analysisPcapHeader(const u_char* p_ucData, uint32_t p_iLen, s_pcapInfo& p_stPcapInfo);
bool checkScanErr(const boost::array<uint8_t, 1600>& p_rawData, int& p_iErrScanPointNum);

bool pcapInit_(std::string p_sDataFilePath)
{
    if (!isExistFileOrFolder(p_sDataFilePath))
    {
        fprintf(stderr, "pcap file: %s is non-existent \n", p_sDataFilePath.c_str());
        return false;
    }

    if ((c_pacp = pcap_open_offline(p_sDataFilePath.c_str(), errbuf_)) == NULL)
    {
        fprintf(stderr, "Error opening pcap file\n");
        return false;
    }

    return true;
}

bool pcapFilter(std::string l_sFileName_)
{
    struct pcap_pkthdr* l_stHeader_;
    const u_char* l_ucPktData_;
    int l_tryPktNum = 100;
    while (l_tryPktNum)
    {
        if (pcap_next_ex(c_pacp, &l_stHeader_, &l_ucPktData_) >= 0)
        {
            // 解析第一包数据 获取源IP 目标端口等
            if (analysisPcapHeader(l_ucPktData_, l_stHeader_->len, c_stPcapInfo))
            {
                std::cout << std::endl;
                std::cout << "***********"
                          << "PCAP_Info"
                          << "***********" << std::endl;
                std::cout << "**  ProtocalMode : " << c_stPcapInfo.m_sProtocalMode << std::endl;
                std::cout << "**  srcIP        : " << c_stPcapInfo.m_sSrcIP << std::endl;
                std::cout << "**  srcPort      : " << c_stPcapInfo.m_uiSrcPort << std::endl;
                std::cout << "**  dstIP        : " << c_stPcapInfo.m_sDstIP << std::endl;
                std::cout << "**  dstPort      : " << c_stPcapInfo.m_uiDstPort << std::endl;
                std::cout << std::endl;
                std::cout << "*****************************" << std::endl;
                std::cout << std::endl;
                break;
            }
        }
        else
            printf("离线数据包 [%s] 自动过滤失败 | 内部有效包数不足\n", l_sFileName_.c_str());
        l_tryPktNum--;
    }

    // 关闭后再次打开并过滤
    pcap_close(c_pacp);
    c_pacp = pcap_open_offline(l_sFileName_.c_str(), errbuf_);

    if (l_tryPktNum)
    {
        // 通过源IP过滤数据包
        std::stringstream filter;
        filter << "len>1230 ";
        filter << "&& udp dst port " << c_stPcapInfo.m_uiDstPort;
        // 生成过滤器
        pcap_compile(c_pacp, &c_pcapPktFilter_, filter.str().c_str(), 1, PCAP_NETMASK_UNKNOWN);
        // 设置过滤器
        pcap_setfilter(c_pacp, &c_pcapPktFilter_);
        printf("自动过滤成功,过滤规则: 数据长度>1230 目标-端口: %d\n", c_stPcapInfo.m_uiDstPort);
        return true;
    }
    else
    {
        pcap_compile(c_pacp, &c_pcapPktFilter_, NULL, 1, PCAP_NETMASK_UNKNOWN);
        pcap_setfilter(c_pacp, &c_pcapPktFilter_);
        printf("自动过滤失败 | 解析过滤规则异常,请检查数据包!\n");
        return false;
    }
}

bool analysisPcapHeader(const u_char* p_ucData, uint32_t p_iLen, s_pcapInfo& p_stPcapInfo)
{
    if (!p_iLen)
        return false;
    int l_offset = 0;
    while (l_offset < (int)(p_iLen - 1))
    {
        // 找IP Header
        if (*(p_ucData + l_offset) == 0x45 && *(p_ucData + l_offset + 1) == 0)
        {
            int l_totalLen = *(p_ucData + l_offset + 2) << 8 | *(p_ucData + l_offset + 3);
            if (l_totalLen == (int)(p_iLen - l_offset))
            {
                if (*(p_ucData + l_offset + 9) == 0x11)
                    p_stPcapInfo.m_sProtocalMode = "UDP";
                else if (*(p_ucData + l_offset + 9) == 0x06)
                    p_stPcapInfo.m_sProtocalMode = "TCP";
                else
                    p_stPcapInfo.m_sProtocalMode = "ERROR";

                p_stPcapInfo.m_sSrcIP = std::to_string(*(p_ucData + l_offset + 12)) + "."
                                        + std::to_string(*(p_ucData + l_offset + 13)) + "."
                                        + std::to_string(*(p_ucData + l_offset + 14)) + "."
                                        + std::to_string(*(p_ucData + l_offset + 15));
                p_stPcapInfo.m_sDstIP = std::to_string(*(p_ucData + l_offset + 16)) + "."
                                        + std::to_string(*(p_ucData + l_offset + 17)) + "."
                                        + std::to_string(*(p_ucData + l_offset + 18)) + "."
                                        + std::to_string(*(p_ucData + l_offset + 19));
                p_stPcapInfo.m_uiSrcPort =
                    *(p_ucData + l_offset + 20) << 8 | *(p_ucData + l_offset + 21);
                p_stPcapInfo.m_uiDstPort =
                    *(p_ucData + l_offset + 22) << 8 | *(p_ucData + l_offset + 23);
                return true;
            }
            else
                printf("error ip header totalLength %d - %d - %d \n", p_iLen, l_totalLen, l_offset);
        }
        l_offset++;
    }
    return false;
}

bool setOffset_(const u_char* p_ucData, uint32_t p_iLen, uint32_t& p_uiOffset)
{
    int l_numFFEE = 0;
    int l_iLastIdFFEE = 0;

#ifndef DEBUGPCAP
    if (p_uiOffset)
        return true;
    for (uint32_t i = 0; i < p_iLen - 1; i++)
    {
        // 找ff ee
        if (*(p_ucData + i) == 255 && *(p_ucData + i + 1) == 238)
        {
            p_uiOffset = i;
            break;
        }
    }
#else
    for (uint32_t i = 0; i < p_iLen - 1; i++)
    {
        // 找ff ee
        if (*(p_ucData + i) == 255 && *(p_ucData + i + 1) == 238)
        {
            if (!p_uiOffset)
            {
                p_uiOffset = i;
            }

            {
                if (!l_iLastIdFFEE)
                    l_iLastIdFFEE = i - 80;
                if (i - l_iLastIdFFEE == 80)
                    l_numFFEE++;
            }
            l_iLastIdFFEE = i;
        }
    }
    if (l_numFFEE != 15)
        printf("ffee num[%d] <15 %d | %d\n", c_uiAllPktNum, p_iLen, l_numFFEE);

    // debug
    if (p_uiOffset && l_numFFEE != 15)
    {
        if (p_iLen > p_uiOffset)
        {
            for (uint32_t i = 0; i < p_iLen - 1 - p_uiOffset; i++)
            {
                if (i % 80 == 0)
                    printf("\n");
                printf("%x ", *(p_ucData + p_uiOffset + i));
            }
            printf("\n");
        }
    }
#endif  // DEBUGPCAP

    if (p_uiOffset)
        return true;
    return false;
}

// 检查雷达扫描是否异常 距离 10 00 00 10
bool checkScanErr(const boost::array<uint8_t, 1600>& p_rawData, int& p_iErrScanPointNum)
{
    p_iErrScanPointNum = 0;
    const s_RawPacket* raw = (const s_RawPacket*)&p_rawData[0];
    // 15块
    for (int i = 0; i < BLOCKS_PER_PACKET; i++)
    {
        // 19块
        for (int j = 0, k = 0; j < SCANS_PER_BLOCK; j++, k += RAW_SCAN_SIZE)
        {
            if (raw->blocks[i].data[k] == 0x10 && raw->blocks[i].data[k + 1] == 0x00
                && raw->blocks[i].data[k + 2] == 0x00 && raw->blocks[i].data[k + 3] == 0x10)
                p_iErrScanPointNum++;
        }
    }
    // 点数15*19  若大于60%的点  则异常
    if (p_iErrScanPointNum == BLOCKS_PER_PACKET * SCANS_PER_BLOCK)
        return false;
    return true;
}

int main(int argc, char const* argv[])
{
    if (argc < 2)
    {
        std::cout << "输入 文件路径 是否输出文件(y/n，default：n)" << std::endl;
        return -1;
    }

    std::fstream l_filePoseWR;
    struct pcap_pkthdr* l_stHeader;
    const u_char* l_ucPktData;
    int l_lineNum = 0;
    int l_uiAllPktNum = 0;
    int l_lidarPktNumLast = 0;
    int l_angPktLast = 0;
    struct timeval l_stLastT;
    l_stLastT.tv_sec = 0;
    l_stLastT.tv_usec = 0;
    int l_iTimeDiffMs = 0;
    boost::array<uint8_t, 1600> c_data;
    std::string c_DirInput = argv[1];
    std::string c_DirOuput(getcwd(NULL, 0));
    c_DirOuput = c_DirOuput + "/lidarAnaly.csv";
    bool c_bCalcuTime = false;
    timeval l_DataLast, l_HeadLast, l_CircleLast, l_CircleHeadLast;
    int c_LastCircle = 0;

    if (!pcapInit_(c_DirInput))
    {
        if (c_pacp)
            pcap_close(c_pacp);
        return -1;
    }
    if (!pcapFilter(c_DirInput))
    {
        if (c_pacp)
            pcap_close(c_pacp);
        return -1;
    }

    bool c_bOutFile = false;
    int c_iFirstSendOffset = 0;
    if (argc > 2)
    {
        // std::string l_sign = argv[2];
        // if (l_sign == "y")
        //     c_bOutFile = true;
        c_iFirstSendOffset = std::atoi(argv[2]);
    }

    if (c_bOutFile)
    {
        l_filePoseWR.open(c_DirOuput.c_str(), std::ios::out | std::ios::trunc);
        if (!l_filePoseWR.is_open())
        {
            printf("输出文件< %s> 打开失败\n", c_DirOuput.c_str());
            return -1;
        }
    }

    printf("离线分析中...\n");
    int l_iTimeErrNum = 0, l_iScanErrNum = 0;
    while (true)
    {
        if (!c_pacp)
            return -1;
        if (pcap_next_ex(c_pacp, &l_stHeader, &l_ucPktData) >= 0)
        {
            l_uiAllPktNum++;

            int ress = pcap_offline_filter(&c_pcapPktFilter_, l_stHeader, l_ucPktData);

            if ((0 == ress) || l_stHeader->len < 1230)
            {
                printf("Pkt[%d] 长度:%d < 1230\n", l_uiAllPktNum, l_stHeader->len);
                continue;
            }

            if (!setOffset_(l_ucPktData, l_stHeader->len, c_uiOffset_))
            {
                printf("Pkt[%d] 获取偏移失败 | len %d\n", l_uiAllPktNum, l_stHeader->len);
                continue;
            }

            if ((l_stHeader->len != (1230 + c_uiOffset_ + c_uiDiscardNum_)))
            {
                printf("Pkt[%d] 长度异常 | %d != %d\n",
                       l_uiAllPktNum,
                       l_stHeader->len,
                       (1230 + c_uiOffset_ + c_uiDiscardNum_));
                continue;
            }

            memcpy(&c_data[0], l_ucPktData + c_uiOffset_, 1260);

            l_lineNum++;
            int l_angPkt = (c_data[3] << 8 | c_data[2]);
            int l_lidarPktNum = (c_data[1256] << 8 | c_data[1257]);
            int l_lidarCycleNum = (c_data[1202] << 8 | c_data[1203]);
            tm l_time = {
                .tm_sec = c_data[1204],         //秒
                .tm_min = c_data[1205],         //分
                .tm_hour = c_data[1206],        //时
                .tm_mday = c_data[1207],        //日
                .tm_mon = c_data[1208] - 1,     //月
                .tm_year = c_data[1209] + 100,  //年
            };
            timeval l_DataTime;
            l_DataTime.tv_sec = mktime(&l_time);
            l_DataTime.tv_usec = (((c_data[1213] & 0x0F) << 24) + (c_data[1212] << 16)
                                  + (c_data[1211] << 8) + c_data[1210])
                                 / 100;
            if (!c_bCalcuTime)
            {
                l_DataLast = l_DataTime;
            }
            printf("pcap time %ld.%ld\n", l_DataTime.tv_sec, l_DataTime.tv_usec);
            float timeDiffMs = (l_DataTime.tv_sec - l_DataLast.tv_sec) * 1000 + (l_DataTime.tv_usec - l_DataLast.tv_usec) * 0.001;
            printf("pcap timediff %f %d %d\n", timeDiffMs, l_lidarCycleNum, l_lidarPktNum);
            l_DataLast = l_DataTime;
            if (c_LastCircle != l_lidarCycleNum)
            {
                float circleDiffMs = (l_DataTime.tv_sec - l_CircleLast.tv_sec) * 1000 + (l_DataTime.tv_usec - l_CircleLast.tv_usec) * 0.001;
                printf("circle %f\n", circleDiffMs);
                float l_headDiffMs = (l_stHeader->ts.tv_sec - l_CircleHeadLast.tv_sec) * 1000 + (l_stHeader->ts.tv_usec - l_CircleHeadLast.tv_usec) * 0.001;
                printf("headcircle %f\n", l_headDiffMs);
                l_CircleHeadLast = l_stHeader->ts;
                c_LastCircle = l_lidarCycleNum;
                l_CircleLast = l_DataTime;
            }
            // if (c_iFirstSendOffset && l_lineNum >= c_iFirstSendOffset
            //     && l_lineNum < c_iFirstSendOffset + 12)
            // {
            //     std::cout << "id : " << l_lineNum << "    角度  :  " << l_angPkt
            //               << "    计数 : " << l_lidarPktNum << " 圈号: " << l_lidarCycleNum
            //               << std::endl;
            // }
            // if (l_lineNum == c_iFirstSendOffset + 12)
            // {
            //     c_iFirstSendOffset += 120;
            //     printf("----------------------------------------------------------\n");
            // }

            // 检查雷达状态
            bool l_bStatus = true;
            if (l_angPktLast && l_angPkt != l_angPktLast + 300)
            {
                if (l_angPktLast == 36000 && l_angPkt == 300)
                    ;
                else
                    l_bStatus = false;
            }

            if (l_lidarPktNumLast && l_lidarPktNum != l_lidarPktNumLast + 1)
            {
                if (l_lidarPktNumLast == 65535 && l_lidarPktNum == 0)
                    ;
                else
                    l_bStatus = false;
            }
            // 检查包间耗时 理论值 12包1发，发晚后休眠等待满足10ms
            if (!c_bCalcuTime)
            {
                l_HeadLast = l_stHeader->ts;
            }
            printf("head time %ld.%ld\n", l_stHeader->ts.tv_sec, l_stHeader->ts.tv_usec);
            float l_timeDiffMs = (l_stHeader->ts.tv_sec - l_HeadLast.tv_sec) * 1000 + (l_stHeader->ts.tv_usec - l_HeadLast.tv_usec) * 0.001;
            printf("Head timediff %f\n", l_timeDiffMs);
            l_HeadLast = l_stHeader->ts;
            c_bCalcuTime = true;
            if (l_stLastT.tv_sec == 0)
                l_stLastT = l_stHeader->ts;
            l_iTimeDiffMs = ((l_stHeader->ts.tv_sec - l_stLastT.tv_sec) * 1e+6
                             + (l_stHeader->ts.tv_usec - l_stLastT.tv_usec))
                            * 1e-3;
            if (l_iTimeDiffMs > 20)
                l_bStatus = false;
            l_stLastT = l_stHeader->ts;
            // 打印雷达异常状态
            if (!l_bStatus)
            {
                l_iTimeErrNum++;
                char l_time[40] = {0};
                strftime(l_time,
                         sizeof(l_time),
                         "%Y-%m-%d %H-%M-%S",
                         localtime(&(l_stHeader->ts.tv_sec)));
                std::string l_analy = c_ErrorInfo[3];
                int l_angDiff = (l_angPkt - l_angPktLast) > 0 ? (l_angPkt - l_angPktLast)
                                                              : (l_angPkt - l_angPktLast + 36000);
                if (l_angDiff / 300 == (l_lidarPktNum - l_lidarPktNumLast) % 120)
                {
                    if (l_angDiff / 300 == 1)
                        l_analy = c_ErrorInfo[2];
                    else
                        l_analy = c_ErrorInfo[1];
                }

                printf("耗时异常 | Pkt[%-7d] | 耗时 [%-5d] Ms 角度变化: %-5d -> %-5d 理论包号变化: "
                       "%-5d | "
                       "内部包号变化: %-10d -> %-10d 理论包号变化: %-10d |  分析结果: %s %s\n",
                       l_uiAllPktNum,
                       l_iTimeDiffMs,
                       l_angPktLast,
                       l_angPkt,
                       (l_angPkt - l_angPktLast) / 300,
                       l_lidarPktNumLast,
                       l_lidarPktNum,
                       (l_lidarPktNum - l_lidarPktNumLast) % 120,
                       l_time,
                       l_analy.c_str());
            }
            int l_iErrPointNum = 0;
            if (!checkScanErr(c_data, l_iErrPointNum))
            {
                char l_time[40] = {0};
                strftime(l_time,
                         sizeof(l_time),
                         "%Y-%m-%d %H-%M-%S",
                         localtime(&(l_stHeader->ts.tv_sec)));
                printf("扫描异常 | Pkt[%-7d] 异常点数 [%-3d/%-3d] | 分析结果: %s %s\n",
                       l_uiAllPktNum,
                       l_iErrPointNum,
                       BLOCKS_PER_PACKET * SCANS_PER_BLOCK,
                       l_time,
                       c_ErrorInfo[0].c_str());
                l_iScanErrNum++;
            }
            l_lidarPktNumLast = l_lidarPktNum;
            l_angPktLast = l_angPkt;
            // 保存74 8b所在pcap信息
            if (c_bOutFile && l_filePoseWR.is_open())
            {
                if (l_angPkt == 35700)
                    l_filePoseWR << l_uiAllPktNum << "," << l_lidarPktNum << "," << l_angPkt << ","
                                 << l_stHeader->ts.tv_sec << "," << l_stHeader->ts.tv_usec
                                 << std::endl;
            }
        }
        else
            break;
    }

    l_filePoseWR.close();
    pcap_close(c_pacp);
    c_pacp = NULL;
    if (l_iTimeErrNum == 0 && l_iScanErrNum == 0)
        printf("离线分析结束: 数据无异常\n");
    else
        printf("离线分析结束\n");
    printf("\n");
    if (c_bOutFile)
    {
        if (l_lineNum)
            printf("输出文件: %s\n", c_DirOuput.c_str());
        else
            printf("离线分析失败,无符合数据包\n");
    }

    return 0;
}