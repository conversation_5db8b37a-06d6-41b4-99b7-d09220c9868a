/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-10-18 13:38:32
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-05-19 17:03:44
 */
#include "algorithm/map/secret_map/laserIO.h"
#include "common/common_ex.h"
#include "tool/fileTool/fileTool.h"
#include <pcl/io/pcd_io.h>
typedef pcl ::PointXYZHSV PointType;
typedef pcl ::PointXYZI PathType;

using namespace wj_slam;

int main(int argc, char const* argv[])
{
    if (argc < 3)
    {
        std::cout << "输入  文件地址  文件名" << std::endl;
        return -1;
    }
    KEYFRAME<PointType>::Ptr l_map(new KEYFRAME<PointType>());
    pcl::PointCloud<PathType>::Ptr c_pcKeyFramesPose_(new pcl::PointCloud<PathType>());
    std::string path = argv[1];
    std::string filename = argv[2];
    int turnFlag = 0;
    std::cout << "This App Version is : V2 & xyzhsv" << std::endl;
    std::cout << "choose turn mode:" << std::endl;
    std::cout << "1: secret-->pcl" << std::endl;
    std::cout << "2: pcl-->secret" << std::endl;
    std::cin >> turnFlag;
    boost::shared_ptr<LaserIO<PointType, PathType>> c_pReadWriteMap_;
    c_pReadWriteMap_.reset(new LaserIO<PointType, PathType>());
    pcl::PCDWriter writer;
    std::vector<typename pcl::PointCloud<PointType>::Ptr> l_feature;
    l_feature.push_back(l_map->m_pFeature->first);
    l_feature.push_back(l_map->m_pFeature->second);
    l_feature.push_back(l_map->m_pFeature->fourth);
    l_feature.push_back(l_map->m_pFeature->third);

    std::vector<typename pcl::PointCloud<PointType>::Ptr> l_pcVisible;
    l_pcVisible.push_back(l_map->m_pFeature->allPC);
    pcl::PointCloud<PointType>::Ptr c_pMap2D(new pcl::PointCloud<PointType>());
    l_pcVisible.push_back(c_pMap2D);

    switch (turnFlag)
    {
        case 1:
            if (!isExistFileOrFolder(path + "/" + filename + ".wj"))
            {
                printf("[error] no find %s/%s.wj\n", path.c_str(), filename.c_str());
                break;
            }
            if (!c_pReadWriteMap_->readBinary(
                    path + "/" + filename + ".wj", c_pcKeyFramesPose_, l_feature, l_pcVisible))
            {
                printf("解密异常 | 读取失败\n");
                return -1;
            }

            if (l_map->m_pFeature->cornerSize())
                writer.writeBinary(path + "/fi.pcd", *l_map->m_pFeature->first);
            else
                printf("解密异常 | fi 为空\n");

            if (l_map->m_pFeature->surfSize())
                writer.writeBinary(path + "/se.pcd", *l_map->m_pFeature->second);
            else
                printf("解密异常 | se 为空\n");
            if (l_map->m_pFeature->markSize())
                writer.writeBinary(path + "/mark.pcd", *l_map->m_pFeature->third);
            else
                printf("解密异常 | mark 为空\n");

            if (c_pcKeyFramesPose_->size())
                writer.writeBinary(path + "/p.pcd", *c_pcKeyFramesPose_);
            else
                printf("解密异常 | p 为空\n");

            if (l_map->m_pFeature->curbSize())
                writer.writeBinary(path + "/curb.pcd", *l_map->m_pFeature->fourth);
            else
                printf("解密异常 | curb 为空\n");

            if (l_map->m_pFeature->allPC->size())
                writer.writeBinary(path + "/3D.pcd", *l_map->m_pFeature->allPC);
            else
                printf("解密异常 | 3D 为空\n");

            if (c_pMap2D->size())
                writer.writeBinary(path + "/2D.pcd", *c_pMap2D);
            else
                printf("解密异常 | 2D 为空\n");

            break;
        case 2:
        {
            if (isExistFileOrFolder(path + "/fi.pcd"))
                pcl::io::loadPCDFile<PointType>(path + "/fi.pcd", *(l_map->m_pFeature->first));
            else
            {
                printf("加密异常 |未找到fi.pcd\n");
                break;
            }
            if (isExistFileOrFolder(path + "/se.pcd"))
                pcl::io::loadPCDFile<PointType>(path + "/se.pcd", *(l_map->m_pFeature->second));
            else
            {
                printf("加密异常 |未找到se.pcd\n");
                break;
            }

            if (isExistFileOrFolder(path + "/mark.pcd"))
                pcl::io::loadPCDFile<PointType>(path + "/mark.pcd", *(l_map->m_pFeature->third));
            else
            {
                printf("加密异常 |未找到mark.pcd\n");
                break;
            }

            if (isExistFileOrFolder(path + "/p.pcd"))
                pcl::io::loadPCDFile<PathType>(path + "/p.pcd", *c_pcKeyFramesPose_);
            else
            {
                printf("加密异常 |未找到p.pcd\n");
                break;
            }
            if (isExistFileOrFolder(path + "/curb.pcd"))
                pcl::io::loadPCDFile<PointType>(path + "/curb.pcd", *(l_map->m_pFeature->fourth));
            else
            {
                printf("加密异常 |未找到curb.pcd\n");
                // break;
            }

            if (isExistFileOrFolder(path + "/3D.pcd"))
                pcl::io::loadPCDFile<PointType>(path + "/3D.pcd", *(l_map->m_pFeature->allPC));
            else
            {
                printf("加密异常 |未找到3D.pcd\n");
                break;
            }

            if (isExistFileOrFolder(path + "/2D.pcd"))
                pcl::io::loadPCDFile<PointType>(path + "/2D.pcd", *(c_pMap2D));
            else
            {
                printf("加密异常 |未找到2D.pcd\n");
                break;
            }

            c_pReadWriteMap_->writeBinary(
                path + "/" + filename + ".wj", c_pcKeyFramesPose_, l_feature, l_pcVisible);
            std::string l_sPath = path + "/" + filename + ".wj";
            printf("加密成功，生成隐藏文件：%s\n", l_sPath.c_str());
            printf("注意：下发客户请确认删除 fi.pcd | se.pcd | p.pcd | curb.pcd\n");
            break;
        }
        default: break;
    }
    return 0;
}