/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushuang
 * @Date: 2021-07-14 17:14:39
 * @LastEditors: shuangquan han
 * @LastEditTime: 2023-12-06 15:56:57
 */
#pragma once
#ifndef _PREPROC_HPP_
#    define _PREPROC_HPP_
#    include "../pre_proc.h"
#    include "../pre_proc_config.h"
namespace wj_slam {
template <typename P> void PreProcess<P>::setStatus_()
{
     printf("set lidarMode\n");
    //  cout<<"hsq: c_stSysParam_->m_iLidarNum = " << c_stSysParam_->m_iLidarNum << endl;
    for (int l_iLaserId = 0; l_iLaserId < c_stSysParam_->m_iLidarNum; l_iLaserId++)
    {
        // 统一shutdown
        if (c_vpDriver_[l_iLaserId])
        {
            c_vpDriver_[l_iLaserId]->shutDown();
            c_vpDriver_[l_iLaserId] = nullptr;
        }

        if (c_vpConv_[l_iLaserId])
        {
            c_vpConv_[l_iLaserId]->shutdown();
            c_vpConv_[l_iLaserId] = nullptr;
        }
    }

    for (int l_iLaserId = 0; l_iLaserId < c_stSysParam_->m_iLidarNum; l_iLaserId++)
    {
        c_vpDriver_[l_iLaserId].reset(new wanji_driver::wanjiDriver(
            l_iLaserId, boost::bind(&PreProcess<P>::processScanCb, this, _1, _2, _3, _4)));

        bool l_KnewLidarType = true;
        // 统一重新实例化
        if (c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720A"
            || c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720F"
            || c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720FCW")
        {
            c_vpConv_[l_iLaserId].reset(new wanji_driver::Convert720(
                l_iLaserId,
                boost::bind(&PreProcess<P>::fECallback, this, _1, _2, _3, _4, _5, _6, _7)));
            if (0 == l_iLaserId)
                c_vpConv_[l_iLaserId]->setIMUCallback(
                    boost::bind(&PreProcess<P>::imuCallback, this, _1, _2));
        }
        else if (c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720F_NP"
                 || c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720FCW_NP")
        {
            c_vpConv_[l_iLaserId].reset(new wanji_driver::Convert720F(
                l_iLaserId,
                boost::bind(&PreProcess<P>::fECallback, this, _1, _2, _3, _4, _5, _6, _7)));
        }
        else
        {
            l_KnewLidarType = false;
        }
        if (!l_KnewLidarType)
        {
            LOGFAE(WERROR,
                   "雷达驱动启动失败 | 不支持雷达类型[{}]，请检查雷达类型是否正确!",
                   c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType);
            // c_stSysParam_->m_fae.setErrorCode("C51");
        }
    }
    // cout<<"hsq: setStatus_ finished" << endl;
}

template <typename P>
bool PreProcess<P>::getIMUData(const uint32_t& p_iLaserId,
                               const int& p_iScanId,
                               IMUPerScan& p_getData)
{
    std::lock_guard<std::mutex> l_mtx(c_imuMtx_);
    /**< 0一定为主雷达,外部做了排序*/
    if (0 != p_iLaserId)
        return false;
    std::map<int, IMUPerScan>::iterator iter;
    iter = m_qIMURawData_.find(p_iScanId);
    if (iter != m_qIMURawData_.end())
    {
        p_getData = iter->second;
        m_qIMURawData_.erase(iter);
        return true;
    }
    else
    {
        int id = p_iScanId;
        LOGFAE(WERROR,
               "[{}] Get Lidar [{}] RawIMU Data Error, scanId: {}",
               WJLog::getWholeSysTime(),
               c_stSysParam_->m_lidar[p_iLaserId].m_sLaserName,
               p_iScanId);
        return false;
    }
}
template <typename P>
void PreProcess<P>::imuCallback(uint32_t p_iLaserId, const wj_slam::IMUPerScan& p_imuData)
{
    std::lock_guard<std::mutex> l_mtx(c_imuMtx_);
    m_qIMURawData_.insert(std::make_pair(p_imuData.scanId(), p_imuData));
}

template <typename P>
void PreProcess<P>::runFECallback(uint32_t p_iLaserId,
                   s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                   s_PCloud& pm,
                   PointCloudPtr& allPC,
                   timeMs timestamp,
                   timeMs recvTimestamp,
                   u_int32_t scanFrameID){
    fECallback(p_iLaserId, pc, pm, allPC, timestamp, recvTimestamp, scanFrameID);
}

template <typename P>
void PreProcess<P>::fECallback(uint32_t p_iLaserId,
                               s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                               s_PCloud& pm,
                               PointCloudPtr& allPC,
                               timeMs timestamp,
                               timeMs recvTimestamp,
                               u_int32_t scanFrameID)
{
    // // cout << "hsq fECallback: p_iLaserId = " << p_iLaserId << endl;
    // 防止多线程同时触发
    std::lock_guard<std::mutex> l_mtx(c_FECBlock_);
    if (c_stSysParam_->m_fae.m_bPrintfTimeLog)
        LOGP(WINFO,
             "{} 雷达 [{}] 帧 {} fE at {} recv {}",
             WJLog::getWholeSysTime(),
             c_stSysParam_->m_lidar[p_iLaserId].m_sLaserName,
             scanFrameID,
             timestamp,
             recvTimestamp);
    if (0 == c_pSync_->c_iEnableSize)
        LOGFAE(WERROR, "雷达驱动运行中断 | 全部雷达关闭使能，请检查使能状态！");
    // 使能关闭状态,不进行后续步骤
    if (TimeSync::SYNC_SET::DISABLE == c_pSync_->c_SyncSet[p_iLaserId])
        return;
    // // cout << "hsq fECallback: scanFrameID = " << scanFrameID << endl;
    FeaturePairPtr featurePair(new FEATURE_PAIR<P>());
    FeaturePtr corner(new Feature());
    FeaturePtr surf(new Feature());
    FeaturePtr curb(new Feature());
    FeaturePtr mark(new Feature());

    TicToc tt;
    c_vpFExter[p_iLaserId]->setInputCloud(pc);
    c_vpFExter[p_iLaserId]->extractFeature(corner, surf);
    c_vpFExter[p_iLaserId]->extractScancontext(*featurePair->sc);
    featurePair->m_bHasContext = true;
    // 获取采样点数
    featurePair->m_iSample2ndSize = c_vpFExter[p_iLaserId]->getUniSampleSurfSize();
    // cout << "hsq fECallback: m_iSample2ndSize =  " << featurePair->m_iSample2ndSize << endl;
    // 切换使用过滤点云
    // 目前模式1与马路牙子提取冲突
    // cout << "hsq fECallback: c_stSysParam_->m_prep.m_iVisualCloud =  " << endl;
    // cout << c_stSysParam_->m_prep.m_iVisualCloud << endl;
    switch (c_stSysParam_->m_prep.m_iVisualCloud)
    {
        case 0: *allPC = *c_vpFExter[p_iLaserId]->getAllCloud(); break;
        case 1: *allPC = *c_vpFExter[p_iLaserId]->getSegmentCloud(); break;
        default: *allPC = *c_vpFExter[p_iLaserId]->getSegmentCloud(); break;
    }

    // 靶标提取
    // cout << "hsq fECallback: c_stSysParam_->m_bIsUseMark =  " << c_stSysParam_->m_bIsUseMark
        //  << endl;
    // if (c_stSysParam_->m_bIsUseMark)
    // {
    //     c_vpMarkExter_[p_iLaserId]->extractMark(
    //         pm, 0, mark, c_stSysParam_->m_sPkgPath, c_stSysParam_->m_fMarkSize);
    // }

    // 目前禁用车道线和马路牙子识别
    // if (0)
    // {
    //     c_vpCurbDector_[p_iLaserId]->setInputCloud(allPC);
    //     c_vpCurbDector_[p_iLaserId]->curbDetector(*curb);
    // }
    *curb += *c_vpFExter[p_iLaserId]->getSegmentCurbScan();
    

    COST_TIME_FEATURE(tt.toc());
    LOGCONV(WDEBUG,
            "{} [FEATRUE] 帧[{}] Feature Cost Time {:.3f}",
            WJLog::getWholeSysTime(),
            scanFrameID,
            tt.toc());

    IMUPerScan l_imuDataScan;
    TicToc tic;
    if (c_stSysParam_->m_bIsUseIMU)
    {
        bool imuRes = getIMUData(p_iLaserId, scanFrameID, l_imuDataScan);
        if (p_iLaserId == 0)
        {
            if (imuRes)
            {
                for (int i = 0; i < l_imuDataScan.size(); ++i)
                {
                    c_imuFilter_->inputRawData(l_imuDataScan.at(i));
                }
            }
        }
    }
    // cout << "hsq fECallback: c_stSysParam_->m_bIsUseIMU =  " << c_stSysParam_->m_bIsUseIMU << endl;
    featurePair->first = corner;
    featurePair->second = surf;
    featurePair->third = mark;
    featurePair->fourth = curb;
    featurePair->allPC = allPC;
    featurePair->m_tsWallTime = recvTimestamp;
    featurePair->m_tsSyncTime = timestamp;
    featurePair->m_dTimespan = SCAN_TIME_MS;
    featurePair->m_uiScanFrame = scanFrameID;
#    ifdef PRINT
    wjPrint(WJCOL_GREEN, "lidarId", p_iLaserId);
    wjPrint(WJCOL_GREEN, "corn size", featurePair->cornerSize());
    wjPrint(WJCOL_GREEN, "surf size", featurePair->surfSize());
    wjPrint(WJCOL_GREEN, "curb size", featurePair->curbSize());
    wjPrint(WJCOL_GREEN, "all size", featurePair->pcSize());
    wjPrint(WJCOL_GREEN, "timestamp", featurePair->time());
#    endif
    if (c_stSysParam_->m_fae.m_bPrintfTimeLog)
        LOGP(WINFO,
             "{} 雷达 [{}] 帧 {} fEOut at {} recv {}",
             WJLog::getWholeSysTime(),
             c_stSysParam_->m_lidar[p_iLaserId].m_sLaserName,
             scanFrameID,
             timestamp,
             recvTimestamp);
    // cout << "hsq fECallback: c_stSysParam_->m_fae.m_bPrintfTimeLog =  "
        //  << c_stSysParam_->m_fae.m_bPrintfTimeLog << endl;
    c_fEOutputCb_((int)p_iLaserId, featurePair);
}

template <typename P>
void PreProcess<P>::processScanCb(uint32_t p_uiLidarID,
                                  RawDataPtr& p_pScanMsg,
                                  int p_iFrameId,
                                  bool isReNewConnect)
{
    RawDataPtr l_pScanMsg = p_pScanMsg;
    // 处理帧数据,确保c_vpConv_已实例化且正常运行
    if (c_vpConv_[p_uiLidarID] && !c_vpConv_[p_uiLidarID]->isShutdown())
        c_vpConv_[p_uiLidarID]->processScan(p_pScanMsg, p_iFrameId, isReNewConnect);
    c_wjOdCallback_(p_uiLidarID, p_pScanMsg, p_iFrameId, isReNewConnect);
}

template <typename P> void PreProcess<P>::resetLidarEnableStatus()
{
    std::lock_guard<std::mutex> l_mtx(c_FECBlock_);
    // 使能设定
    c_pSync_->setLidarEnableStatus();
}

template <typename P> void PreProcess<P>::resetTransToBase()
{
    std::lock_guard<std::mutex> l_mtx(c_FECBlock_);
    for (int i = 0; i < c_stSysParam_->m_iLidarNum; i++)
    {
        s_POSE6D l_DipQT, l_CalQT;
        s_LidarConfig& l_lidar = c_stSysParam_->m_lidar[i];
        // 计算倾角校准
        l_DipQT.setRPY(l_lidar.m_fDipAngle[0], l_lidar.m_fDipAngle[1], 0);
        l_CalQT.setX(l_lidar.m_fCalibration[0]);
        l_CalQT.setY(l_lidar.m_fCalibration[1]);
        l_CalQT.setZ(l_lidar.m_fCalibration[2]);
        l_CalQT.setRPY(
            l_lidar.m_fCalibration[3], l_lidar.m_fCalibration[4], l_lidar.m_fCalibration[5]);
        // 计算标定校准
        l_lidar.m_transToBase = l_CalQT * l_DipQT;
        // 设置下发
        if (c_vpConv_[i])
            c_vpConv_[i]->setTransToBase();
    }
    // cout<<"hsq: resetTransToBase finished" << endl;
}
template <typename P> bool PreProcess<P>::setModeConfig(int p_WorkMode, int p_ScenMode)
{
    std::lock_guard<std::mutex> l_mtx(c_FECBlock_);
    // 如果发生变化则更新mode
    if (p_WorkMode != (int)c_sConfig_.m_workMode || p_ScenMode != (int)c_sConfig_.m_ScenMode)
    {
        c_sConfig_.m_workMode = WorkMode(p_WorkMode);
        c_sConfig_.m_ScenMode = ScenMode(p_ScenMode);
        for (int l_ilidar = 0; l_ilidar < c_stSysParam_->m_iLidarNum; ++l_ilidar)
        {
            // 特征提取设置
            configFromScen_FExt_(l_ilidar);
            configFromFile_FExt_(l_ilidar);
            configPrintf_FExt_(l_ilidar);

            // 靶标提取设置

            // 车道提取设置
        }
    }
    return true;
}

template <typename P> void PreProcess<P>::start()
{
    // setStatus_();// hsq
    // resetTransToBase();
}
template <typename P> void PreProcess<P>::syncDataCallback_(FeaturePairPtr& p_fEPair)
{
    IMUData l_imuData;
    if (c_sConfig_.m_bIsUseImu)
    {
        Eigen::Vector3d l_angle;
        if (c_imuFilter_->getIMUData(l_imuData, p_fEPair->m_tsSyncTime, 10))
        {
            //此处增加90,由于一帧点云中仅保存了约10个imu数据,目的是获取最新的imu数据
            bool imustatus;
            imustatus = c_imuFilter_->getAngularTwist(l_angle, p_fEPair->m_tsSyncTime, 5);
            l_imuData.angularVelocity() = l_angle;
            if (imustatus)
                l_imuData.setStatus(IMUData::IMUStatus::AllValid);
            else
                l_imuData.setStatus(IMUData::IMUStatus::QuatValid);
            if (c_imuFilter_->bias().status() != IMUData::IMUStatus::InitDone)
                c_imuFilter_->bias().setStatus(IMUData::IMUStatus::InitDone);
        }
        else
        {
            if ((c_imuFilter_->bias().status() == IMUData::IMUStatus::InitDone))
            {
                // bias标定完成且第一帧已经初始化完成,没有找到同步imu数据
                LOGW(WERROR,
                     "[{}] Get IMU Data Error, syncTime: {:.3f}ms, scanId: {}, syncPrecs: {}ms",
                     WJLog::getWholeSysTime(),
                     p_fEPair->m_tsSyncTime,
                     p_fEPair->m_uiScanFrame,
                     10);
            }
            l_imuData.setStatus(c_imuFilter_->bias().status());
        }
    }
    p_fEPair->m_imuData = l_imuData;
    c_SyncOutputCb_(p_fEPair);
}
template <typename P>
PreProcess<P>::PreProcess(boost::function<void(FeaturePairPtr&)> p_callback,
                          boost::function<void(uint32_t, RawDataPtr&, int, bool)> p_wjOdCallback)
    : c_wjOdCallback_(p_wjOdCallback), c_SyncOutputCb_(p_callback)
{
    c_stSysParam_ = SYSPARAM::getIn();
    // 更新mode
    c_sConfig_.m_workMode = WorkMode(c_stSysParam_->m_iWorkMode);
    c_sConfig_.m_ScenMode = ScenMode(c_stSysParam_->m_nParam_mode);
    c_sConfig_.m_TimeSource = TimeSource(c_stSysParam_->m_time.timeSource());
    c_sConfig_.m_bIsUseImu = c_stSysParam_->m_bIsUseIMU;
    c_vpMarkExter_.resize(c_stSysParam_->m_iLidarNum);
    c_vpFExter.resize(c_stSysParam_->m_iLidarNum);
    // c_vpCurbDector_.resize(c_stSysParam_->m_iLidarNum);
    c_vpDriver_.resize(c_stSysParam_->m_iLidarNum);
    c_vpConv_.resize(c_stSysParam_->m_iLidarNum);
    // 设置同步完成后调用预处理输出
    c_pSync_.reset(new TimeSync(boost::bind(&PreProcess::syncDataCallback_, this, _1)));
    // 设置特征提取完成后进入同步序列类的数据输入
    setFEOutputCallback(boost::bind(&TimeSync::syncInputCallBack, c_pSync_, _1, _2));

    for (int l_ilidar = 0; l_ilidar < c_stSysParam_->m_iLidarNum; ++l_ilidar)
    {
        // 特征提取设置
        c_vpFExter[l_ilidar].reset(new wj_FE::FeatureExtract(false));
        c_vpFExter[l_ilidar]->setCurbLineId(c_stSysParam_->m_lidar[l_ilidar].m_vCurbLine);
        configFromScen_FExt_(l_ilidar);
        configFromFile_FExt_(l_ilidar);
        configPrintf_FExt_(l_ilidar);

        // 靶标提取设置
        c_vpMarkExter_[l_ilidar].reset(new Mark_Rec());

        // 车道提取设置
        // c_vpCurbDector_[l_ilidar].reset(new CurbDetector<P>());
        // c_vpCurbDector_[l_ilidar]->setHighValue(c_stSysParam_->m_prep.m_curb.m_fCurbHeight);
        // c_vpCurbDector_[l_ilidar]->setAngleNum(c_stSysParam_->m_prep.m_curb.m_fAxisAngleDiff);
        // c_vpCurbDector_[l_ilidar]->setOffset( offsetof(P, H) );//线号偏移
        // c_vpCurbDector_[l_ilidar]->setLidarHigh(c_stSysParam_->m_prep.m_curb.m_fGroundClearance);
    }
    c_imuFilter_.reset(new imuFilter());
    c_imuFilter_->setAngularVelNoise(c_stSysParam_->m_prep.m_imu.m_vAngularVelNoise);
}
template <typename P> void PreProcess<P>::shutDown()
{
    // 关闭子类循环
    for (int i = 0; i < c_stSysParam_->m_iLidarNum; i++)
    {
    //     c_vpDriver_[i]->shutDown();
    //     c_vpConv_[i]->shutdown();
        // c_vpFExter[i] 没有内部循环需要关闭
    }
    // 关闭类中循环
}
template <typename P> PreProcess<P>::~PreProcess()
{
    std::cout << "exit pre" << std::endl;
    for (int i = 0; i < c_stSysParam_->m_iLidarNum; i++)
    {
        c_vpDriver_[i] = nullptr;
        c_vpConv_[i] = nullptr;
        c_vpFExter[i] = nullptr;
        c_vpMarkExter_[i] = nullptr;
        // c_vpCurbDector_[i] = nullptr;
    }
    c_pSync_ = nullptr;
};
}  // namespace wj_slam
#    define WJSLAM_PREPROC(P) template class wj_slam::PreProcess<P>;
#endif