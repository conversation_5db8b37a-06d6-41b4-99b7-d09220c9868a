/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-11-15 14:43:25
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-06-25 10:29:37
 */
#pragma once
// #include "../../../gtest/gtest_test.h"
#include "algorithm/optimize/laserTransform.h"
#include "common/common_ex.h"
#include <deque>
#include <queue>
#include <vector>

#define SENDID
namespace wj_slam {

class TimeSync {
  private:
    typedef typename pcl::PointXYZHSV P;
    typedef typename FEATURE_PAIR<P>::Ptr FeaturePairPtr;
    typedef pcl::PointCloud<P> Feature;
    typedef boost::shared_ptr<Feature> FeaturePtr;
    typedef std::vector<FeaturePairPtr> F_QUEUE;

  public:
    /**
     * An enum type
     */
    enum SYNC_SET {
        ENABLE = 0, /**< 使能 */
        DISABLE = 1 /**< 未使能 */
    };

    std::vector<SYNC_SET> c_SyncSet; /**< 使能设置列表(内部,防止误触发) */
    int c_iEnableSize;               /**< 使能雷达数量 */

  private:
    /**
     * An enum type
     *
     */
    enum SYNC {
        UNSYNC = 0, /**< 未同步 */
        SYNC = 1    /**< 同步 */
    };
    /**
     * An struct type 传感器同步数据
     *
     */
    typedef struct st_SyncDatas
    {
        FeaturePairPtr m_lidarFrame; /**< 特征点云数据 */
    } st_SyncDatas;
    /**
     * An struct type 传感器阵列
     *
     */
    typedef struct st_MultiSensorQueue
    {
        std::vector<int> m_SyncStat;
        std::vector<F_QUEUE> m_lidars;
        void reset(const int p_iSize)
        {
            m_SyncStat = std::vector<int>(p_iSize, SYNC);
            m_lidars.resize(p_iSize);
            for (size_t i = 0; i < p_iSize; i++)
            {
                m_lidars[i].resize(0);
            }
        }
        int getSyncStatus()
        {
            for (int i = 0; i < (int)m_SyncStat.size(); ++i)
                if (m_SyncStat[i] == UNSYNC)
                    return UNSYNC;
            return SYNC;
        }
    } st_MultiSensorQueue;

    st_MultiSensorQueue c_stQues_;                          /**< 传感器数据集合 */
    int c_iMaxTimeBehind_;                                  /**< 最大允许落后时间 */
    int c_iMaxTimeSync_;                                    /**< 最大允许同步时间 */
    int c_iMaxTimeDelay_;                                   /**< 最大允许发送延时时间 */
    int c_iLastSendTime_;                                   /**< 上次发送时间 */
    int c_iFrameNo_;                                        /**< 累计帧号 */
    int TIME_FIELDS_OFFSET;                                 /**< 点的时间字段偏移 */
    int ID_FIELDS_OFFSET;                                   /**< 点的雷达ID字段偏移 */
    SYSPARAM* c_stSysParam_;                                /**< 系统参数 */
    std::mutex syncLock;                                    /**< 同步锁 */
    boost::function<void(FeaturePairPtr&)> c_SyncOutputCb_; /**< 同步完毕回调 */

  public:
    /**
     * @brief Construct a new Time Sync object
     *
     * @param p_SyncOutputCb 数据同步回调
     *
     */
    TimeSync(boost::function<void(FeaturePairPtr&)> p_SyncOutputCb)
        : c_SyncOutputCb_(p_SyncOutputCb)
    {
        c_stSysParam_ = SYSPARAM::getIn();
        c_iMaxTimeBehind_ = c_stSysParam_->m_prep.m_sync.m_iMaxTimeBehind;
        c_iMaxTimeSync_ = c_stSysParam_->m_prep.m_sync.m_iMaxTimeSync;
        c_iMaxTimeDelay_ = c_stSysParam_->m_prep.m_sync.m_iMaxTimeDelay;
        c_iFrameNo_ = 0;
        c_iLastSendTime_ = -1;
        TIME_FIELDS_OFFSET = (offsetof(P, s) / 4UL);
        ID_FIELDS_OFFSET = (offsetof(P, h) / 4UL);
        c_stQues_.reset(c_stSysParam_->m_iLidarNum);
        // 使能初始设定
        setLidarEnableStatus();
    }
    /**
     * @brief Destroy the Time Sync object
     *
     *
     */
    ~TimeSync()
    {
        c_iFrameNo_ = 0;
        c_iLastSendTime_ = -1;
        for (int i = 0; i < (int)c_stQues_.m_lidars.size(); ++i)
        {
            c_stQues_.m_lidars[i].resize(0);
        }
        c_stQues_.reset(0);
    }
    /**
     * @brief 设置雷达使能状态
     *
     *
     */
    void setLidarEnableStatus()
    {
        c_SyncSet.resize(c_stSysParam_->m_iLidarNum);
        c_iEnableSize = 0;
        // 使能设定刷新
        for (int i = 0; i < c_stSysParam_->m_iLidarNum; i++)
        {
            if (c_stSysParam_->m_lidar[i].m_bEnable)
            {
                c_iEnableSize++;
                c_SyncSet[i] = SYNC_SET::ENABLE;
            }
            else
                c_SyncSet[i] = SYNC_SET::DISABLE;
        }
    }
    /**
     * @brief 设置时间偏移量在点索引中的位置（float*）
     *
     * @param p_iTimeOffset 偏移N个float数
     *
     */
    void setTimeOffset(int p_iTimeOffset)
    {
        TIME_FIELDS_OFFSET = p_iTimeOffset;
    }
    /**
     * @brief 设置时间偏移量在点索引中的位置（float*）
     *
     * @param p_iIDOffset 偏移N个float数
     *
     */
    void setIDOffset(int p_iIDOffset)
    {
        ID_FIELDS_OFFSET = p_iIDOffset;
    }
    /**
     * @brief 设置允许的最大同步等待时间，超过后会立即发送最新点云
     *
     * @param p_iMaxWaitMs 同步等待时间
     *
     */
    void setTimeWaitForSync(int p_iMaxWaitMs)
    {
        c_iMaxTimeDelay_ = p_iMaxWaitMs;
    }
    /**
     * @brief 将雷达数据放入同步队列
     *
     * @param p_iLaserId 雷达ID
     * @param featurePair 雷达帧
     *
     */
    void syncInputCallBack(int p_iLaserId, FeaturePairPtr& featurePair)
    {
        syncLock.lock();
        setNewLidarFrame(p_iLaserId, featurePair);
        st_SyncDatas l_tempData;
        if (getSyncData(l_tempData))
        {
            c_SyncOutputCb_(l_tempData.m_lidarFrame);
        }
        syncLock.unlock();
    }
    /**
     * @brief 向队列中添加新的雷达帧
     *
     * @param p_uiID 雷达序号
     * @param p_pc 雷达帧
     *
     */
    void setNewLidarFrame(u_int p_uiID, FeaturePairPtr& p_pc)
    {
        // LOGP(WDEBUG, "get [{}] at {}ms recvT {} ms.", p_uiID, p_pc->m_dTimestamp,
        // p_pc->m_iRecvTimestamp);
        if (c_stQues_.m_SyncStat[p_uiID] == UNSYNC)
            c_stQues_.m_SyncStat[p_uiID] = SYNC;
        c_stQues_.m_lidars[p_uiID].push_back(p_pc);
        // 当队列中存在过多数据，进行释放
        if (c_stQues_.m_lidars[p_uiID].size() > 4)
        {
            LOGP(WDEBUG,
                 "{} pop [{}] at {}ms.",
                 WJLog::getWholeSysTime(),
                 p_uiID,
                 c_stQues_.m_lidars[p_uiID].front()->m_tsSyncTime);
            c_stQues_.m_lidars[p_uiID].erase(c_stQues_.m_lidars[p_uiID].begin());
        }
    }
    /**
     * @brief 获取已同步的合成帧
     *
     * @param p_SyncDatas 合成数据结构
     * @code
     *
     * @endcode
     * @return [true] \n
     * 同步成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 同步失败
     *
     */
    bool getSyncData(st_SyncDatas& p_SyncDatas)
    {
        static sTimeval l_timeNow(0);
        std::vector<FeaturePairPtr> l_lidarDatas(c_stQues_.m_lidars.size(), nullptr);
        if (anyLidarQueueNotEmpty(c_stQues_.m_lidars))
        {
            if (getLidarFrames(l_lidarDatas))
            {
                p_SyncDatas.m_lidarFrame.reset(new FEATURE_PAIR<P>());
                timeMs l_tsSyncTime = 0, l_tsWallTime = 0, l_tsTimeSpan = 0;
                mergLidars(l_lidarDatas,
                           l_tsSyncTime,
                           l_tsWallTime,
                           l_tsTimeSpan,
                           p_SyncDatas.m_lidarFrame);
                // LOGP(WDEBUG,"release one Merg-Frame.");
                // 写入时间戳
                p_SyncDatas.m_lidarFrame->m_tsWallTime = l_tsWallTime;
                p_SyncDatas.m_lidarFrame->m_tsSyncTime = l_tsSyncTime;
                c_iLastSendTime_ = l_tsSyncTime;
                p_SyncDatas.m_lidarFrame->m_dTimespan = l_tsTimeSpan;
                // 写入帧号
                // p_SyncDatas.m_lidarFrame->m_uiScanFrame = c_iFrameNo_;
                LOGP(WDEBUG,
                     "{} release [{}]-s{} at {}ms with {}ms | lidarRecvT: {} ms ",
                     WJLog::getWholeSysTime(),
                     p_SyncDatas.m_lidarFrame->m_uiScanFrame,
                     c_stQues_.getSyncStatus(),
                     p_SyncDatas.m_lidarFrame->m_tsSyncTime,
                     p_SyncDatas.m_lidarFrame->m_dTimespan,
                     p_SyncDatas.m_lidarFrame->m_tsWallTime);
                c_iFrameNo_++;
                return true;
            }
            else
            {
                sTimeval l_time(0);
                double l_dTimediff = l_time.data() - l_timeNow.data();
                if (-1 == c_iLastSendTime_ && l_dTimediff > 10.0)
                {
                    LOGP(WWARN, "双雷达同步未成功，请注意双雷达时间源");
                    l_timeNow.now();
                }
            }
        }
        return false;
    }

  private:
    /**
     * @description: 判断所有雷达队列中是否存在空队列
     * @param {std::vector<F_QUEUE>&} p_lidarQue 雷达队列的序列
     * @return {bool} 是否存在空队列
     * @other:
     */
    bool anyLidarQueueEmpty(std::vector<F_QUEUE>& p_lidarQue)
    {
        for (int j = 0; j < (int)p_lidarQue.size(); ++j)
        {
            if (p_lidarQue[j].empty())
            {
                return true;
            }
        }
        return false;
    }
    /**
     * @brief 判断所有雷达队列中是否存在非空队列
     *
     * @param p_lidarQue 多雷达数据队列
     * @code
     *
     * @endcode
     * @return [true] \n
     * 队列不为空
     * @code
     *
     * @endcode
     * @return [false] \n
     * 队列为空
     *
     */
    bool anyLidarQueueNotEmpty(std::vector<F_QUEUE>& p_lidarQue)
    {
        for (int j = 0; j < (int)p_lidarQue.size(); ++j)
        {
            if (!p_lidarQue[j].empty())
            {
                return true;
            }
        }
        return false;
    }
    /**
     * @brief 获取全部雷达队列最新帧中时间戳最新者
     *
     * @param p_lidarQue 雷达队列的序列
     * @param p_iBaseQue 最大时间戳所在雷达队列的序号
     * @code
     *
     * @endcode
     * @return [int] \n
     * 最大时间戳（最大即最新）
     *
     */
    int getLatestTimeOfLidarQueFront(std::vector<F_QUEUE>& p_lidarQue, int& p_iBaseQue)
    {
        int l_iLatestTime = INT_MIN;
        // 最新帧中时间戳最新者
        for (int i = 0; i < (int)p_lidarQue.size(); ++i)
        {
            if (!p_lidarQue[i].empty())
            {
                if (p_lidarQue[i].back()->m_tsSyncTime > l_iLatestTime)
                {
                    l_iLatestTime = p_lidarQue[i].back()->m_tsSyncTime;
                    p_iBaseQue = i;
                }
            }
        }
        // LOGP(WDEBUG,"queue-{} is front-latest at {} ms.", p_iBaseQue, l_iLatestTime);
        return l_iLatestTime;
    }
    /**
     * @brief 使用基准时间戳搜索附近时间范围内每个队列中的帧，若帧的时间戳过旧则抛弃
     *
     * @param p_lidarQue 雷达队列的序列
     * @param p_iTimeStamp 基准时间戳
     * @param p_iBaseQue 基准时间戳所在雷达队列序号
     * @param p_frames 输出帧序列
     * @code
     *
     * @endcode
     * @return [true] \n
     * 基准时间戳远小于某一队列的最旧时间戳
     *
     */
    bool getFramesAtTime(std::vector<F_QUEUE>& p_lidarQue,
                         int p_iTimeStamp,
                         int p_iBaseQue,
                         std::vector<FeaturePairPtr>& p_frames)
    {
        // 其他队列的帧和时间差
        FeaturePairPtr l_fp = nullptr;
        int l_iTimeDiff = 0;
        // 基准自己无需重新判定
        p_frames[p_iBaseQue] = p_lidarQue[p_iBaseQue].back();
        // 对每个雷达队列
        for (int i = 0; i < (int)p_lidarQue.size(); ++i)
        {
            if (i == p_iBaseQue)
                continue;
            // 从较新的开始
            for (int j = p_lidarQue[i].size() - 1; j >= 0; j--)
            {
                l_fp = p_lidarQue[i][j];
                l_iTimeDiff = l_fp->m_tsSyncTime - p_iTimeStamp;
                // 如果时间符合区间限制则加入缓存
                if (l_iTimeDiff > c_iMaxTimeBehind_)
                {
                    p_frames[i] = l_fp;
                    // 终止这一队列内循环
                    break;
                }
            }
        }
        return true;
    }
    /**
     * @brief 判断是否可以进行同步
     *
     * @param p_lidarQue 雷达队列的序列
     * @param p_iTimeStamp 基准时间戳
     * @param p_iBaseQue 基准时间戳所在雷达队列序号
     * @param p_frames 输出帧序列
     * @code
     *
     * @endcode
     * @return [true] \n
     * 可以进行同步
     * @code
     *
     * @endcode
     * @return [false] \n
     * 不可以进行同步
     *
     */
    bool SyncSendAndRecvBuff(std::vector<F_QUEUE>& p_lidarQue,
                             int p_iTimeStamp,
                             int p_iBaseQue,
                             std::vector<FeaturePairPtr>& p_frames)
    {
        // 如果某个为空则表示同步不成功
        std::vector<int> l_iSyncLidar;
        std::vector<int> l_iUnSyncLidar;
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            // 不存在
            if (p_frames[i] == nullptr)
            {
                l_iUnSyncLidar.push_back(i);
            }
            else
            {
                l_iSyncLidar.push_back(i);
            }
        }
        // 计算时间损失
        int l_iTimeLost = p_iTimeStamp - c_iLastSendTime_;
        // 使能雷达数,只有使能雷达会进入同步队列,所以可以直接用数量检查是否全部同步完成
        // 如果全部使能雷达同步成功，发送
        if ((int)l_iSyncLidar.size() == c_iEnableSize)
        {
            // 更新同步状态
            for (int i = 0; i < (int)l_iSyncLidar.size(); ++i)
                c_stQues_.m_SyncStat[l_iSyncLidar[i]] = SYNC;
            // 清空接收缓存
            for (int i = 0; i < (int)p_lidarQue.size(); ++i)
                p_lidarQue[i].resize(0);
            return true;
        }
        // 如果不是第一帧
        // 如果未发送时间超过允许的跳跃时间限制，直接发送
        // 如果上次丢失同步状态的雷达仍未恢复,且已超过单次同步等待时间，直接发送
        else if (c_iLastSendTime_ > 0
                 && ((l_iTimeLost > c_iMaxTimeDelay_)
                     || (c_stQues_.getSyncStatus() == UNSYNC)))
        {
            LOGP(WWARN, "{} sync jump {}ms", WJLog::getWholeSysTime(), l_iTimeLost);
            // 更新同步状态
            for (int i = 0; i < (int)l_iUnSyncLidar.size(); ++i)
                c_stQues_.m_SyncStat[l_iUnSyncLidar[i]] = UNSYNC;
            // 清空接收缓存
            for (int i = 0; i < (int)p_lidarQue.size(); ++i)
                p_lidarQue[i].resize(0);
            return true;
        }
        return false;
    }
    /**
     * @brief 尝试从雷达序列中捕获一组同步数据
     *
     * @param p_frames 输出帧序列
     * @code
     *
     * @endcode
     * @return [true] \n
     * 获取一组可同步数据成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 获取一组可同步数据失败
     *
     */
    bool getLidarFrames(std::vector<FeaturePairPtr>& p_frames)
    {
        // 基准时间戳,基准所在雷达队列序号
        int l_iTimeStamp = -1;
        int l_iBaseQueue = -1;
        // 获取雷达队列每队最新帧中最新的时间
        l_iTimeStamp = getLatestTimeOfLidarQueFront(c_stQues_.m_lidars, l_iBaseQueue);
        // 获取这个时间戳范围内的帧，抛弃更旧时间范围的帧
        // 尝试获取时间范围内的帧
        if ((-1 != l_iBaseQueue)
            && getFramesAtTime(c_stQues_.m_lidars, l_iTimeStamp, l_iBaseQueue, p_frames))
        {
            return SyncSendAndRecvBuff(c_stQues_.m_lidars, l_iTimeStamp, l_iBaseQueue, p_frames);
        }
        return false;
    }
    /**
     * @brief 已一组可同步数据中最旧时间戳作为同步时间戳
     *
     * @param p_frames 帧序列
     * @param p_iTimeStamp 待填写的基准时间戳
     * @param p_iRecvTimeStamp 待填写的基准时间对应的雷达接收时间
     * @param p_iTimeSpan 待填写的合成时间跨度
     * @param p_mergFe 待填写的合成帧
     *
     */
    void mergLidars(std::vector<FeaturePairPtr>& p_frames,
                    timeMs& p_tsSyncTime,
                    timeMs& p_tsWallTime,
                    timeMs& p_tsTimeSpan,
                    FeaturePairPtr& p_mergFe)
    {
        // 基准时间戳所在雷达序号
        int l_iBaseQueue = -1;
        // 设置时间基准为序列最旧时间戳
        lidarsTimeReBase(p_frames, p_tsSyncTime, p_tsWallTime, p_tsTimeSpan, l_iBaseQueue);
        // 以时间基准起修改每个点的时间
        pointsTimeReBase(p_frames, p_tsSyncTime, l_iBaseQueue);
        // 转移每个雷达帧到统一坐标系
        // lidarsCoorRebase(p_frames);
        // 合成其他点云
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            *p_mergFe->first += *p_frames[i]->first;
            *p_mergFe->third += *p_frames[i]->third;
            *p_mergFe->fourth += *p_frames[i]->fourth;
            *p_mergFe->allPC += *p_frames[i]->allPC;
            // 帧ID
            p_mergFe->m_uiScanFrame = p_frames[i]->m_uiScanFrame;
        }
        // 重新排列面点和采样offset
        sample2ndArrangeMerg(p_frames, p_mergFe);
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            p_mergFe->m_bHasContext = p_frames[i]->m_bHasContext;
            if (p_mergFe->m_bHasContext)
            {
                *p_mergFe->sc = *p_frames[i]->sc;
                break;
            }
        }
    }
    /**
     * @brief 寻找最旧时间戳并计算帧间时间跨度
     *
     * @param p_frames 帧序列
     * @param p_iTimeStamp 待填写的基准时间戳
     * @param p_iRecvTimeStamp 待填写的基准时间对应的雷达接收时间
     * @param p_iTimeSpan 待填写的合成时间跨度
     * @param p_iBaseQueue 待填写的基准时间戳所在序列号
     *
     */
    void lidarsTimeReBase(std::vector<FeaturePairPtr>& p_frames,
                          timeMs& p_tsSyncTime,
                          timeMs& p_tsWallTime,
                          timeMs& p_iTimeSpan,
                          int& p_iBaseQueue)
    {
        // 初始化时间戳为最大值
        p_tsSyncTime = FLT_MAX;
        timeMs l_iSyncTimeEnd = FLT_MIN;
        // 计算同步帧的最旧时间
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            if (p_frames[i]->m_tsSyncTime <= p_tsSyncTime)
            {
                p_tsSyncTime = p_frames[i]->m_tsSyncTime;
                p_tsWallTime = p_frames[i]->m_tsWallTime;
                p_iBaseQueue = i;
            }
            if (p_frames[i]->m_tsSyncTime >= l_iSyncTimeEnd)
            {
                l_iSyncTimeEnd = p_frames[i]->m_tsSyncTime;
            }
        }
        // 计算最大时间间隔(首帧起点到最后一帧终点)
        p_iTimeSpan = l_iSyncTimeEnd + SCAN_TIME_MS - p_tsSyncTime;
        // LOGP(WDEBUG,"rebase to lidar-{}'s time.", p_iBaseQueue);
    }
    /**
     * @brief 修正全部雷达帧的时间偏移到基准时间戳
     *
     * @param p_frames 帧序列
     * @param p_iTimeStamp 基准时间戳
     * @param p_iBaseQueue 基准时间戳所在序列号
     *
     */
    void
    pointsTimeReBase(std::vector<FeaturePairPtr>& p_frames, timeMs p_tsSyncTime, int p_iBaseQueue)
    {
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
#ifdef SENDID
            // 转移ID号用于畸变矫正
            resetFPairID(p_frames[i], i);
#endif
            // 时间基准帧不需要计算偏移
            if (i == p_iBaseQueue)
                continue;
            // 计算毫秒单位下的时间偏移量
            timeMs l_fTimeDiff = p_frames[i]->m_tsSyncTime - p_tsSyncTime;
            // 刷新时间偏移量
            rebaseFPairTime(p_frames[i], l_fTimeDiff);
        }
    }
    /**
     * @brief 获取点的时间字段指针
     *
     * @param p_Pnt 点
     * @code
     *
     * @endcode
     * @return [float*] \n
     * 点云时间字段指针
     *
     */
    inline float* ptime(P& p_Pnt)
    {
        return reinterpret_cast<float*>(&p_Pnt) + TIME_FIELDS_OFFSET;
    }
    /**
     * @brief 获取点的雷达ID字段指针
     *
     * @param p_Pnt 点
     * @code
     *
     * @endcode
     * @return [float*] \n
     * 点云ID字段指针
     *
     */
    inline float* pID(P& p_Pnt)
    {
        return reinterpret_cast<float*>(&p_Pnt) + ID_FIELDS_OFFSET;
    }
    /**
     * @brief 修正帧的时间偏移到基准时间戳
     *
     * @param p_pPc 帧
     * @param p_fTimeDiff 时间偏移
     *
     */
    void rebaseFPairTime(FeaturePairPtr p_pPc, timeMs p_fTimeDiff)
    {
        rebaseCloudTime(p_pPc->first, p_fTimeDiff);
        rebaseCloudTime(p_pPc->second, p_fTimeDiff);
        rebaseCloudTime(p_pPc->fourth, p_fTimeDiff);
        rebaseCloudTime(p_pPc->allPC, p_fTimeDiff);
    }
    /**
     * @brief 修正点云的时间偏移到基准时间戳
     *
     * @param p_pPc 点云
     * @param p_fTimeDiff 时间偏移
     *
     */
    void rebaseCloudTime(FeaturePtr p_pPc, timeMs p_fTimeDiff)
    {
        u_int l_iCldSize = p_pPc->size();
        for (u_int i = 0; i < l_iCldSize; ++i)
            *(ptime(p_pPc->points[i])) += p_fTimeDiff;
    }
    /**
     * @brief 记录点云的雷达ID
     *
     * @param p_pPc 帧
     * @param p_fID 雷达ID
     *
     */
    void resetFPairID(FeaturePairPtr p_pPc, float p_fID)
    {
        resetCloudID(p_pPc->allPC, p_fID);
    }
    /**
     * @brief 记录点云的雷达ID
     *
     * @param p_pPc 点云
     * @param p_fID 雷达ID
     *
     */
    void resetCloudID(FeaturePtr p_pPc, float p_fID)
    {
        u_int l_iCldSize = p_pPc->size();
        for (u_int i = 0; i < l_iCldSize; ++i)
            *(pID(p_pPc->points[i])) = p_fID;
    }
    /**
     * @brief 合成帧中的面点由于采样排序，需要将每个帧的采样点移到非采样点前
     *
     * @param p_frames 待合成帧序列
     * @param p_mergFe 合成帧
     *
     */
    void sample2ndArrangeMerg(std::vector<FeaturePairPtr>& p_frames, FeaturePairPtr& p_mergFe)
    {
        FeaturePtr l_pPcS, l_pMergS;
        l_pMergS = p_mergFe->second;
        int l_iSampleSize = 0;
        int l_iMergSampleSize = 0;
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            l_pPcS = p_frames[i]->second;
            l_iSampleSize = p_frames[i]->m_iSample2ndSize;
            // 在上个采样点序列后插入新采样点
            l_pMergS->insert(l_pMergS->begin() + l_iMergSampleSize,
                             l_pPcS->begin(),
                             l_pPcS->begin() + l_iSampleSize);
            // 更新采样点序列长度
            l_iMergSampleSize += l_iSampleSize;
            // 在尾部插入非采样点
            l_pMergS->insert(l_pMergS->end(), l_pPcS->begin() + l_iSampleSize, l_pPcS->end());
            // LOGP(WDEBUG,"merg lidar-{}'s 2ndPC to offset {}.", i, l_iMergSampleSize);
        }
        p_mergFe->m_iSample2ndSize = l_iMergSampleSize;
    }

};  // class TimeSync
}  // namespace wj_slam