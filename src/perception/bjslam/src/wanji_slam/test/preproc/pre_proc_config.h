/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-03 13:43:02
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2022-12-01 16:23:13
 */
#pragma once
#include "pre_proc.h"
namespace wj_slam {

template <typename P> void PreProcess<P>::configFromScen_FExt_(uint32_t p_Id)
{
    // 设置场景参数
    c_vpFExter[p_Id]->setParamScenario(c_sConfig_.m_ScenMode);
    //根据工作模式补充参数
    switch (c_sConfig_.m_workMode)
    {
        case WorkMode::StandByMode: setPp_StandBy_(); break;

        case WorkMode::InitMapMode: setPp_InitMap_(); break;

        case WorkMode::ContMapMode: setPp_ContMap_(); break;

        case WorkMode::LocatMode: setPp_Locat_(); break;

        case WorkMode::UpdateMapMode: setPp_UpdateMap_(); break;

        default: setPp_StandBy_(); break;
    }
}

template <typename P> void PreProcess<P>::setPp_StandBy_()
{
    switch (c_sConfig_.m_ScenMode)
    {
        case ScenMode::INDOOR: break;

        case ScenMode::OUTDOOR: break;

        case ScenMode::VACROUS: break;

        case ScenMode::LABOR: break;

        default: break;
    }
}
template <typename P> void PreProcess<P>::setPp_InitMap_()
{
    switch (c_sConfig_.m_ScenMode)
    {
        case ScenMode::INDOOR: break;

        case ScenMode::OUTDOOR: break;

        case ScenMode::VACROUS: break;

        case ScenMode::LABOR: break;

        default: break;
    }
}
template <typename P> void PreProcess<P>::setPp_ContMap_()
{
    switch (c_sConfig_.m_ScenMode)
    {
        case ScenMode::INDOOR: break;

        case ScenMode::OUTDOOR: break;

        case ScenMode::VACROUS: break;

        case ScenMode::LABOR: break;

        default: break;
    }
}
template <typename P> void PreProcess<P>::setPp_Locat_()
{
    switch (c_sConfig_.m_ScenMode)
    {
        case ScenMode::INDOOR: break;

        case ScenMode::OUTDOOR: break;

        case ScenMode::VACROUS: break;

        case ScenMode::LABOR: break;

        default: break;
    }
}
template <typename P> void PreProcess<P>::setPp_UpdateMap_()
{
    switch (c_sConfig_.m_ScenMode)
    {
        case ScenMode::INDOOR: break;

        case ScenMode::OUTDOOR: break;

        case ScenMode::VACROUS: break;

        case ScenMode::LABOR: break;

        default: break;
    }
}

template <typename P> void PreProcess<P>::configFromFile_FExt_(uint32_t p_Id)
{
    CurvFeatureConfig& l_sys = c_stSysParam_->m_prep.m_curv;
    if (l_sys.m_vUseStat[0] != -1)
        c_vpFExter[p_Id]->setFeatureUse(l_sys.m_vUseStat);

    std::vector<bool> l_setBasic = l_sys.m_basic.hasSet();
    std::vector<bool> l_setScale = l_sys.m_scale.hasSet();
    std::vector<bool> l_setSmooth = l_sys.m_smooth.hasSet();
    std::vector<bool> l_setFilter = l_sys.m_filter.hasSet();

    if (l_setBasic[0])
        c_vpFExter[p_Id]->setBondaryAngle(l_sys.m_basic.m_fMaxBondaryAng);
    if (l_setBasic[1])
        c_vpFExter[p_Id]->setCornCurvRange(l_sys.m_basic.m_fCornAngRange[0],
                                           l_sys.m_basic.m_fCornAngRange[1]);
    if (l_setBasic[2])
        c_vpFExter[p_Id]->setSurfCurvRange(l_sys.m_basic.m_fMinSurfAng);
    if (l_setScale[0])
        c_vpFExter[p_Id]->setSmallThingScaleRange(l_sys.m_scale.m_fSmallScale[0],
                                                  l_sys.m_scale.m_fSmallScale[1]);
    if (l_setScale[1])
        c_vpFExter[p_Id]->setFeatureZSpace(l_sys.m_scale.m_fFeatHighRange[0],
                                           l_sys.m_scale.m_fFeatHighRange[1]);
    if (l_setScale[2])
        c_vpFExter[p_Id]->setCornDistRange(l_sys.m_scale.m_fCornDistRange[0],
                                           l_sys.m_scale.m_fCornDistRange[1]);
    if (l_setScale[3])
        c_vpFExter[p_Id]->setLidarHeight(c_stSysParam_->m_lidar[p_Id].m_fFeatureHeight,
                                         l_sys.m_scale.m_fGroundZSpace);
    else
        c_vpFExter[p_Id]->setLidarHeight(c_stSysParam_->m_lidar[p_Id].m_fFeatureHeight);

    if (l_setSmooth[0])
        c_vpFExter[p_Id]->setIntensitySmoothCheck(l_sys.m_smooth.m_bIntensitySmoothCheck,
                                                  l_sys.m_smooth.m_uiMaxIntensityDiff,
                                                  l_sys.m_smooth.m_fIntenDiffPercent);
    else
        c_vpFExter[p_Id]->setIntensitySmoothCheck(l_sys.m_smooth.m_bIntensitySmoothCheck);

    if (l_setSmooth[1])
        c_vpFExter[p_Id]->setLineSmoothCheck(l_sys.m_smooth.m_bLineSmoothCheck,
                                             l_sys.m_smooth.m_fMaxLineAng,
                                             l_sys.m_smooth.m_fAngDiffPercent);
    else
        c_vpFExter[p_Id]->setLineSmoothCheck(l_sys.m_smooth.m_bLineSmoothCheck);

    if (l_setFilter[0] && l_setFilter[1])
        c_vpFExter[p_Id]->setUniSampleSurfFilter(l_sys.m_filter.m_bUniSampleSurfFilter,
                                                 l_sys.m_filter.m_uiMaxSurfPntNum,
                                                 &l_sys.m_filter.m_fSampleRange,
                                                 &l_sys.m_filter.m_fSampleScale);
    else if (l_setFilter[0])
        c_vpFExter[p_Id]->setUniSampleSurfFilter(l_sys.m_filter.m_bUniSampleSurfFilter,
                                                 l_sys.m_filter.m_uiMaxSurfPntNum);
    else
        c_vpFExter[p_Id]->setUniSampleSurfFilter(l_sys.m_filter.m_bUniSampleSurfFilter);

    if (l_setFilter[2])
        c_vpFExter[p_Id]->setVerticalCornFilter(l_sys.m_filter.m_bVerticalCornFilter,
                                                l_sys.m_filter.m_uiCornCyldMinPoints,
                                                l_sys.m_filter.m_fCornCyldRadius,
                                                l_sys.m_filter.m_fCornCyldMinHeight);
    else
        c_vpFExter[p_Id]->setVerticalCornFilter(l_sys.m_filter.m_bVerticalCornFilter);

    // sc参数
    c_vpFExter[p_Id]->setSCDistRange(c_stSysParam_->m_loop.m_dRadius);
    c_vpFExter[p_Id]->setSCRingSector(c_stSysParam_->m_loop.m_iRing,
                                      c_stSysParam_->m_loop.m_iSector);
}

template <typename P> void PreProcess<P>::configPrintf_FExt_(uint32_t p_Id)
{
    if (c_stSysParam_->m_bDebugModel)
    {
        LOGF(WDEBUG, "{} [FE Param] ****set FE-{} ****", WJLog::getWholeSysTime(), p_Id);
        c_vpFExter[p_Id]->debugPrintf();
    }
}
}  // namespace wj_slam