/*
 * @Descripttion:
 * @version:
 * @Author: zu<PERSON><PERSON>
 * @Date: 2021-04-28 19:56:24
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-07-12 11:20:12
 */
#pragma once
#ifndef _ODOMETRY_H_
#    define _ODOMETRY_H_
// #    include "../../gtest/gtest_test.h"
#    include "algorithm/optimize/laserRegistration.h"
#    include "common/common_ex.h"
#    include "common/type/type_imu.h"
#    include "common/type/type_queue.h"
#    include "common/type/type_thread.h"
#    include "tic_toc.h"
#    include <Eigen/Dense>
#    include <boost/shared_ptr.hpp>
#    include <map>
#    include <queue>
#    include <thread>
#    include <vector>

namespace wj_slam {
class IsKeyFrame {
  public:
    typedef boost::shared_ptr<IsKeyFrame> Ptr;
    typedef s_POSE6D MoveSpeed;
    typedef s_POSE6D PoseDev;

  private:
    PoseDev& c_lastCurPose_; /**< 距离上一次关键帧运动偏差 */
    PoseDev c_lastPose_;     /**< 距离上一次关键帧运动偏差 */
    MoveSpeed& c_curSpeed_;  /**< 当前速度 */
    MoveSpeed c_lastSpeed_;  /**< 上次速度 */

    double c_dLastTSDev_;            /**< 时间间隔 */
    double c_dLastTS_;               /**< 上一次关键帧时间 */
    u_int32_t c_uiLastFrame_;        /**< 上一次关键帧时间戳 */
    u_int32_t c_uiLastKeyFrame_;     /**< 上次的建图帧时间戳 */
    u_int32_t c_uiLastFrameDev_;     /**< 关键帧时间戳间隔 */
    bool c_bIsFirst_ = true;         /**< 初始化 */
    u_int32_t c_uiKeyFrameTimeDiff_; /**< 允许建图定位的时间差 */
    float c_fRealKeyFrameDistDev_;   /**< 允许建图的关键帧距离差 */
    float c_fRealKeyFrameAngDev_;    /**< 允许建图的关键帧角度差 */

    /**
     * @brief 判断是否匀速
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 匀速
     * @code
     *
     * @endcode
     * @return [false] \n
     * 非匀速
     *
     */
    bool isUniformVelocity()
    {
        double maxspeed =
            (c_lastSpeed_.norm() >= c_curSpeed_.norm()) ? c_lastSpeed_.norm() : c_curSpeed_.norm();
        // double l_dLinerAcceleration = (c_lastSpeed_.inverse() * c_curSpeed_).norm() / maxspeed;
        double l_dLinerAcceleration = (c_lastSpeed_.inverse() * c_curSpeed_).norm();
        // double speedcur = c_curSpeed_.norm();
        // double speedlast = c_lastSpeed_.norm();
        // if (speedcur < 0.005 || speedlast < 0.005)
        // {
        //     // std::cout<<"speed low"<<std::endl;
        //     wjPrint(COLOR_RED, "speed low", 0);
        //     c_curSpeed_.printf("cur speed");
        //     c_lastSpeed_.printf("last speed");
        //     return false;
        // }
        float l_roll = std::fabs(c_lastSpeed_.roll() - c_curSpeed_.roll());
        float l_pitch = std::fabs(c_lastSpeed_.pitch() - c_curSpeed_.pitch());
        float l_yaw = std::fabs(c_lastSpeed_.yaw() - c_curSpeed_.yaw());
        // float cosarg = std::fabs(c_lastSpeed_.m_quat.dot(c_curSpeed_.m_quat));
        if (l_yaw > 0.2)
        {
            std::cout << "yaw diff is big " << l_yaw << std::endl;
            return false;
        }
        // 速度允许误差以米/100毫秒速度单位判定
        if (l_dLinerAcceleration <= 0.03)
            return true;
        std::cout << "l_dLinerAcceleration diff is big " << l_dLinerAcceleration << std::endl;
        return false;
    }

  public:
    /**
     * @brief 设置允许建图定位的时间差
     *
     * @param timeDev 时间差
     *
     */
    void setKFRenewTime(int timeDev)
    {
        c_uiKeyFrameTimeDiff_ = timeDev;
    }
    /**
     * @brief 设置允许建图的关键帧距离差
     *
     * @param distDev 距离差
     *
     */
    void setKFRenewDist(double distDev)
    {
        c_fRealKeyFrameDistDev_ = distDev;
    }
    /**
     * @brief 设置允许建图的关键帧角度差
     *
     * @param angDev 角度差
     *
     */
    void setKFRenewAng(double angDev)
    {
        c_fRealKeyFrameAngDev_ = angDev;
    }
    /**
     * @brief 判定是否属于关键帧
     *
     * @param isEnoughMark 靶标标志位
     * @param p_uiCurFrame 雷达首包时间戳
     * @param isRealKF 关键帧标志位
     * @code
     *
     * @endcode
     * @return [true] \n
     * 属于关键帧
     * @code
     *
     * @endcode
     * @return [false] \n
     * 不属于关键帧
     *
     */
    bool isKeyFrame(bool isEnoughMark, int p_uiCurFrame, bool& isRealKF)
    {
        bool res = false;
        PoseDev l_poseDev = c_lastPose_.inverse() * c_lastCurPose_;
        double l_dPosDis = l_poseDev.normXY();
        double l_dAngDev = std::fabs(l_poseDev.yaw());
        bool l_bUniformVelocity = isUniformVelocity();
        double l_fKFAngDev = c_fRealKeyFrameAngDev_ > 170.0 ? 170.0 : c_fRealKeyFrameAngDev_;
        double l_fKFAngDevLarge =
            3.0 * c_fRealKeyFrameAngDev_ > 170.0 ? 170.0 : 3.0 * c_fRealKeyFrameAngDev_;

        if (c_bIsFirst_)
        {
            c_bIsFirst_ = false;
            isRealKF = true;
            res = true;
        }
        // else if (isEnoughMark)
        // {
        //     isRealKF = true;
        //     res = true;
        // }
        else
        {
            c_uiLastFrameDev_ = p_uiCurFrame - c_uiLastFrame_;
            // 如果1s没有定位/建图，必须进行其一
            std::cout << "c_uiLastFrameDev_ " << c_uiLastFrameDev_ << " = p_uiCurFrame " << p_uiCurFrame
                << " - c_uiLastFrame_ " << c_uiLastFrame_ << std::endl;
            if (c_uiLastFrameDev_ > c_uiKeyFrameTimeDiff_)
            {
                res = true;
                // 前提条件：距离上次定位/建图已经1s以上
                // 如果距离差或角度差足够,且匀速运动，进行建图
                if ((l_dPosDis > c_fRealKeyFrameDistDev_ || l_dAngDev > l_fKFAngDev)
                    && l_bUniformVelocity)
                {
                    isRealKF = true;
                    // printf("\033[32mAdd KF [PosDev & Stable].\n\033[0m");
                }
                // 如果距离差或角度差非常大，进行建图
                else if (l_dPosDis > 3.0 * c_fRealKeyFrameDistDev_ || l_dAngDev > l_fKFAngDevLarge)
                {
                    isRealKF = false;
                    // printf("\033[32mAdd KF [PosDev much].\n\033[0m");
                }
                else
                {
                    std::cout << "l_dPosDis : " << l_dPosDis << " l_dAngDev: " << l_dAngDev
                              << " l_bUniformVelocity: " << l_bUniformVelocity << std::endl;
                }
                // 如果距离上次建图已经5s以上，进行建图
                // else if (p_uiCurFrame - c_uiLastKeyFrame_ > 49)
                // {
                //     isRealKF = true;
                //     // printf("\033[32mAdd KF [FrameDev much].\n\033[0m");
                // }
            }
        }

        if (res)
        {
            if (isRealKF)
            {
                std::cout << "hsq: 确认关键帧， isRealKF = true " << std::endl;
                c_uiLastKeyFrame_ = p_uiCurFrame;
                c_uiLastFrame_ = p_uiCurFrame;
                c_lastPose_ = c_lastCurPose_;
            }
            else
            {
                std::cout << "hsq: 确认关键帧， res = true, isRealKF = false" << std::endl;
                c_uiLastFrame_ = p_uiCurFrame;
            }
        }
        else{
            std::cout << "hsq: 非关键帧， res = false, isRealKF = " << isRealKF << std::endl;
        }
        c_lastSpeed_ = c_curSpeed_;
        return res;
    }
    /**
     * @brief Construct a new Is Key Frame object
     *
     * @param p_poseDev 位姿
     * @param p_curSpeed 速度
     *
     */
    IsKeyFrame(PoseDev& p_poseDev, MoveSpeed& p_curSpeed)
        : c_lastCurPose_(p_poseDev), c_curSpeed_(p_curSpeed)
    {
        c_bIsFirst_ = true;
        c_dLastTSDev_ = 0;
        c_uiKeyFrameTimeDiff_ = 900;
        c_fRealKeyFrameDistDev_ = 1.0;
        c_fRealKeyFrameAngDev_ = 170.0;
    }
    /**
     * @brief Destroy the Is Key Frame object
     *
     *
     */
    ~IsKeyFrame() {}
};
template <typename P> class Odometry {
  public:
    typedef boost::shared_ptr<Odometry<P>> Ptr;

  private:
    typedef typename KEYFRAME<P>::Ptr KEYFRAME_PTR;
    typedef typename FEATURE_PAIR<P>::Ptr FEATURE_PAIR_PTR;
    typedef s_POSE6D MoveSpeed;
    typedef s_POSE6D PoseDev;
    typedef s_POSE6D Pose;
    typedef timeMs Time;
    typedef u_int32_t ScanId;
#    define MinMarkSize 3
    SYSPARAM* c_stSysParam_;
    /**
     * An enum type
     *
     */
    enum OptAlgoType {
        useMark = 0, /**< 使用靶标计算 */
        useFeature   /**< 使用slam计算 */
    };

  private:
    std::mutex c_mtxOdomLock_; /**< 位姿回调锁 */

#    pragma region "程序开关控制"
    bool c_bOdomRun_;          /**< odom模块开始工作 */
    bool c_bOdomRunOver_;      /**< 一次odom完成 */
    bool c_bSysHasInit;        /**< 第一帧初始化 */
    bool c_bShutDown_;         /**< odom模块关闭 */
    bool c_bShutDownOver_;     /**< odom模块关闭完成 */
    bool c_bOnlyLocationMode_; /**< 只开定位功能,即无增量里程计更新 */
    bool c_bUseIMU_;           /**< IMU应用开关 */
#    pragma endregion

    boost::function<void(KEYFRAME_PTR)> c_isKeyFrameCb_;    /**< 关键帧回调 */
    Queue<FEATURE_PAIR_PTR>& c_featureBuf_;                 /**< 点云队列 */
    boost::shared_ptr<LaserRegistration<P, P>> c_pMatcher_; /**< 匹配器 */
    IsKeyFrame::Ptr c_pCheckKeyFrame_;                      /**< 关键帧判定 */

    /*实际增量
     * 上帧到当前帧的位姿变化
     * K位姿相对于K-N的坐标系
     */
    PoseDev c_stIncrease_;       /**< 帧间增量,用于畸变矫正 */
    Pose c_stIncreaseOdom_;      /**< 增量里程计 */
    Pose c_stIncreaseOdomLast_;  /**< 上一帧增量里程计 */
    Pose c_stHPrecsOdomCur_;     /**< 当前帧高精度里程计 */
    Pose c_stHPrecsOdomLast_;    /**< 上一帧高精度里程计 */
    Pose c_stHPrecsOdomPredict_; /**< 预测当前帧位姿（即当前帧终点位姿） */
    /*优化增量
     * 优化源点云相对于优化目标点云
     * 源点云 = 畸变矫正+修正到当前起始时间的K点云
     * 目标点云 = 畸变矫正+修正到当前起始时间的K-1点云
     */
    PoseDev c_stIncreaseOpt_;   /**< 优化增量 */
    Pose c_stHPrecsPose_;       /**< mapping返回的高精度位姿 */
    PoseDev c_stOdomDev_;       /**< 里程计与mapping位姿间偏差 */
    PoseDev c_stIncreaseHpDev_; /**< 当前增量里程计与上一次精确全局位姿偏差 */
    /*速度
     * 0.1秒标准时间的位姿变化
     * K位姿相对于预估的K-1的坐标系
     */
    MoveSpeed c_stCurSpeed_; /**< 当前增量里程计与上一次精确全局位姿偏差 */
    FEATURE_PAIR_PTR c_pCurrFeature_; /**< 当前帧特征 */
    FEATURE_PAIR_PTR c_pFeatureMap_;  /**< Target地图 */
    Time c_timeCurr_;                 /**< 当前帧时间戳 */
    Time c_timeCurrLidarRecv_;        /**< 对应c_timeCurr_时间下的接收雷达时间 */
    Time c_timeLast_;                 /**< 上一帧时间戳 */
    ScanId c_scanIDCurr_;             /**< 当前帧ID */
    ScanId c_scanIDLast_;             /**< 上一帧ID */
    bool c_bHasLocationDone_;         /**< 回调更新位姿完成标志位 */
    boost::function<void(std::vector<Pose>)> c_bSendPos = NULL;        /**< 发送位姿回调 */
    boost::function<void(std::vector<KEYFRAME_PTR>)> c_bSendPC = NULL; /**< 发送点云回调 */
    float c_fJumpNum_;                                                 /**< 跳帧 */
    std::vector<KEYFRAME_PTR> c_vDisplay_;                             /**< 显示点云和位姿 */
    std::queue<std::pair<int, Pose>> c_stOdomQueue_;                   /**< 位姿队列 */
    IMUData c_stCurIMUData_;                                           /**< 当前IMU数据 */
    IMUData c_stLastIMUData_; /**< 上一次更新的IMU数据 */
    float c_fImuQuatPrec_;    /**<  IMU四元数与lidarOdom允许偏差 */

    //关键帧发送控制组
    Queue<KEYFRAME_PTR> c_KFBuf_;             /**< 里程计预估关键帧队列*/
    typename wj_slam::thread::Ptr c_pThread_; /**< 发送关键帧线程线程控制*/
    bool c_bAddKeyFrame_;                     /**< 输出关键帧标志*/
    bool c_bRun_;                             /**< 输出关键帧线程控制*/
    /**
     * @brief 参数重置
     *
     *
     */
    void paramReset_();
    /**
     * @brief 参数初始化
     *
     *
     */
    void paramInit_();
    /**
     * @brief 畸变修正
     *
     * @param p_stPose 修正位姿
     * @param p_pcInput 待修正点云
     *
     */
    void corretPC_(s_POSE6D p_stPose, FEATURE_PAIR_PTR& p_pcInput);
    /**
     * @brief 非特征点畸变修正
     *
     * @param p_pcInput 当前帧点云
     *
     */
    void finishCorretPC_(FEATURE_PAIR_PTR& p_pcInput);
    /**
     * @brief 设置匹配输入点云
     *
     * @param p_pcInput 当前帧点云
     *
     */
    void setInput_(FEATURE_PAIR_PTR& p_pcInput);
    /**
     * @brief 更新位姿与匹配地图
     *
     * @param p_pcInput 当前帧点云
     * @param p_stTrans 当前帧地图
     *
     */
    void renewMap_(PoseDev p_stTrans);
    /**
     * @brief 设置匹配输入地图
     *
     * @param p_pcInput 匹配地图
     *
     */
    void setTarget_(FEATURE_PAIR_PTR& p_pcInput);
    /**
     * @brief 更新
     *
     *
     */
    void update_();
    /**
     * @brief 更新位姿
     *
     *
     */
    void renewOdom_();
    /**
     * @brief 打包关键帧信息
     *
     *
     */
    void addKeyFrame_();
    /**
     * @brief 优化函数
     *
     *
     */
    void optimiz_();
    /**
     * @brief 获取最新一帧点云
     *
     * @code
     *
     * @endcode
     * @return [FEATURE_PAIR_PTR] \n
     * 点云
     *
     */
    FEATURE_PAIR_PTR getCurrFeature_(void)
    {
        FEATURE_PAIR_PTR l_pFeatrue;
        l_pFeatrue = c_featureBuf_.front();
        c_featureBuf_.pop();
        c_bHasLocationDone_ = false;
        return l_pFeatrue;
    }
    /**
     * @brief 只取最新帧点云数据,定位算法使用
     *
     *
     */
    void clearFeatureBuf2Lastest_(void);
    /**
     * @brief 清空所有帧
     *
     *
     */
    void clearFeatureBuf_(void);
    /**
     * @brief 判定是否与待处理点云帧
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 存在待处理帧
     * @code
     *
     * @endcode
     * @return [false] \n
     * 不存在待处理帧
     *
     */
    bool hasPointCloud_();
    /**
     * @brief 选择匹配优化方式
     *
     * @param p_pCurrFeature 当前帧点云
     * @param p_Map 当前帧地图
     * @code
     *
     * @endcode
     * @return [OptAlgoType] \n
     * 匹配优化方式
     *
     */
    OptAlgoType selectAlgoType_(FEATURE_PAIR_PTR& p_pCurrFeature, FEATURE_PAIR_PTR& p_Map)
    {
        // if (!c_stSysParam_->m_bIsUseMark)
        //     return OptAlgoType::useFeature;
        // if (hasEnoughMark_(p_pCurrFeature) && hasEnoughMark_(p_Map))
        // {
        //     return OptAlgoType::useMark;
        // }
        return OptAlgoType::useFeature;
    }
    /**
     * @brief 匹配优化模块
     *
     * @param p_pCurrFeature 当前帧点云
     * @param p_Map 当前帧地图
     * @code
     *
     * @endcode
     * @return [true] \n
     * 匹配优化成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 匹配优化失败
     *
     */
    bool location_(FEATURE_PAIR_PTR& p_pCurrFeature, FEATURE_PAIR_PTR& p_Map);
    /**
     * @brief 靶标数量时候满足
     *
     * @param p_pFeature 当前帧点云
     * @code
     *
     * @endcode
     * @return [true] \n
     * 靶标数量满足
     * @code
     *
     * @endcode
     * @return [false] \n
     * 靶标数量不满足
     *
     */
    bool hasEnoughMark_(FEATURE_PAIR_PTR& p_pFeature);
    // int getJumpNum_()
    // {
    //     return (c_timeCurr_ - c_timeLast_ + 50) / 100;
    // }
    /**
     * @brief 获取跳帧数
     *
     * @code
     *
     * @endcode
     * @return [float] \n
     * 帧间隔
     *
     */
    float getJumpNum_();
    /**
     * @brief 速度平均
     *
     * @param p_stNewSpeed 最新速度
     * @param p_stLastSpeed 上次速度
     *
     */
    void speedSmooth(MoveSpeed& p_stNewSpeed, MoveSpeed& p_stLastSpeed);
    void selectSpeed_();
    Pose findLidarOdomById_(const int p_scanId);
    void clearLidarOdom_();

    /**
     * @brief 里程计关键帧输出线程函数
     *
     */
    void outputKeyFrame_();

  public:
    /**
     * @brief 更新位姿回调
     *
     * @param p_stPoseGlobal location最后定位位姿与速度
     *
     */
    void renewPrecisionPose(s_PoseWithTwist& p_stPoseGlobal);
    /**
     * @brief 设置odom速度
     *
     * @param p_speed 速度
     *
     */
    void setSpeed(MoveSpeed p_speed);
    /**
     * @brief 使能定位模式标志位
     *
     *
     */
    void enableOnlyLocationMode();
    /**
     * @brief 不使能定位模式标志位
     *
     *
     */
    void disableOnlyLocationMode();
    /**
     * @brief 退出odom函数
     *
     *
     */
    void shutDown();
    /**
     * @brief odom开始函数
     *
     *
     */
    void start();
    /**
     * @brief 匹配工作模式
     *
     * @param p_workMode 工作模式
     * @code
     *
     * @endcode
     * @return [true] \n
     * 匹配工作模式成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 匹配工作模式失败
     *
     */
    bool setWorkMode(int p_workMode);
    /**
     * @brief odom运行函数
     *
     *
     */
    void run();
    /**
     * @brief odom暂停运行函数
     *
     *
     */
    void stop();
    /**
     * @brief 判断里程计线程是否暂停运行
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 暂停运行
     * @code
     *
     * @endcode
     * @return [false] \n
     * 未暂停运行
     *
     */
    bool isStop();
    /**
     * @brief Construct a new Odometry object
     *
     * @param p_feature 点云帧队列
     * @param p_keyFrameCb 关键帧回调
     * @param timeOffset 时间偏移
     * @param ringOffset 线号偏移
     * @param sendpos 发送位姿回调
     * @param sendPc 发送点云回调
     *
     */
    Odometry(Queue<FEATURE_PAIR_PTR>& p_feature,
             boost::function<void(KEYFRAME_PTR)> p_keyFrameCb,
             int timeOffset = -1,
             int ringOffset = -1,
             boost::function<void(std::vector<Pose>)> sendpos = NULL,
             boost::function<void(std::vector<KEYFRAME_PTR>)> sendPc = NULL);
    /**
     * @brief Destroy the Odometry object
     *
     *
     */
    ~Odometry();
};

}  // namespace wj_slam
#    ifdef WJSLAM_NO_PRECOMPILE
#        include "impl/odometry.hpp"
#    else
#        define WJSLAM_Odometry(P) template class wj_slam::Odometry<P>;
#    endif
#endif
