/**
 * @file wheelOdomerty.h
 * <AUTHOR> (wenz<PERSON><EMAIL>)
 * @brief
 * @version 1.0
 * @date 2022-11-23
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once
#ifndef _WHEELODOMERTY_H_
#    define _WHEELODOMERTY_H_
#    include "algorithm/odometry/impl/kalmanVelocity.hpp"
#    include "common/common_ex.h"
#    include "tic_toc.h"
#    include <Eigen/Dense>
#    include <boost/function.hpp>
#    include <boost/shared_ptr.hpp>
#    include <deque>
#    include <fstream>
#    include <thread>
#    include <vector>

namespace wj_slam {
/**
 * \defgroup odomtery
 * @brief 轮式里程计
 * \ingroup
 */
/**
 * @class WheelOdometry WheelOdometry.h "WheelOdometry.h"
 * @brief 位姿优化模组
 * \ingroup Odomerty
 * @details 接收轮式速度进行位姿预估
 * @attention
 */
class WheelOdometry {
  public:
    typedef timeMs Time;
    typedef boost::shared_ptr<WheelOdometry> Ptr; /**< 类指针别名 */
    typedef s_POSE6D MoveSpeed;                   /**< 速度（帧间局部） */
    typedef s_POSE6D PoseDev;                     /**< 位姿偏差 */
    typedef s_POSE6D Pose;
    using FUN_SENDPOS = boost::function<void(std::vector<Pose>, std::vector<Pose>)>;

  private:
    SYSPARAM* c_stSysParam_;
    std::mutex c_mtxOdomFuseLock_;
    std::vector<Pose> c_vPredPoseBuf_;
    std::deque<s_PoseWithTwist> c_HPredPoseBuf_; /**< 轮式里程计buf */
    std::deque<s_PoseWithTwist> c_outPoseBuf_; /**< 输出至slam对应时刻的未修正位姿buf */
    FUN_SENDPOS c_bSendPos = NULL;

    s_PoseWithTwist c_stHPredPoseTwist; /**< 高频低精度预估里程计 */
    s_PoseWithTwist c_stLastPrecPoseTwist;
    s_fuseTwist c_stLastFuseTwist; /**< 上次速度 */
    Pose c_LastPredPose;           /**< 上一次时间完全匹配的里程计预估pose */
    Pose c_LastHPrecPose;    /**< 上一次时间完全匹配的高精度里程计预估Pose */
    PoseStatus c_HPredFlag_; /**< 高频pose标志位 */
    KalmanFilter6DV c_kf_;   /**< 滤波器 */
#    pragma region "程序开关控制"
    bool c_bSysHasInit = false; /**< 第一帧初始化 */
    bool c_bShutDown_ = false;  /**< odomfuse模块关闭 */
    bool c_bRun_ = false;       /**< odomfuse模块开始工作 */
    bool c_bRunOver_ = true;    /**< 一次odomfuse完成 */
#    pragma endregion

#    pragma region "其他"
    bool c_bInfinitiveVitual_ = false; /**< 允许无效虚拟预估 */
    int c_iScanID_ = 0;                /**< 快速估计次数 */
    int c_iLastTwistOwnTime_ = -1;     /**< 上次速度内部时间 */
    int c_iLastOutPredRecvTime_ = 0;   /**< 上次输出的有效预估位姿时间戳*/
    float c_fMaxLineTwist = 0.2;       /**< 最大线速度限制 m/100ms*/
#    pragma endregion

    /*******************************
     * @function: paramInit_
     * @description: 参数初始化
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void paramInit_();

    /*******************************
     * @function: paramReset_
     * @description: 参数复位
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void paramReset_();

    /*******************************
     * @function: twistToLocal_
     * @description: 判断外部融合速度为绝对坐标系or局部坐标系，决定是否需转局部坐标
     * @param {Pose} p_stHPose 最新高精度位姿
     * @param {s_fuseTwist&} p_stFuseTwist 外部速度 修改为局部速度
     * @return {boo} 是否为局部速度
     * @others: null
     *******************************/
    bool twistToLocal_(Pose p_stHPose, s_fuseTwist& p_stFuseTwist);

    /*******************************
     * @function: getCorrTimePrecPose_
     * @description: 根据时间差值 对齐precPose
     * @param {s_PoseWithTwist &} p_stPosePrec 上一时刻预估位姿
     * @param {int} p_iTimeDiff 目标位姿时间-当前位姿时间差
     * @return {*}
     * @others: null
     *******************************/
    void getCorrTimePrecPose_(s_PoseWithTwist& p_stPosePrec, int p_iTimeDiff);

    /*******************************
     * @function: saveHPredPose_
     * @description: 保存precPose 及 twist
     * @param {s_PoseWithTwist&} p_sCurrPose: 最终使用位姿
     * @param {string} p_sFilePath: 路径
     * @return {*}
     * @others: null
     *******************************/
    void saveHPredPose_(s_PoseWithTwist& p_sUsePose, std::string p_sFilePath);

    void saveTwistInfo_(MoveSpeed& p_sOutSpeed, std::string p_sFilePath);

    void viewPose_(std::vector<Pose>& p_predPoseBuf, std::vector<Pose>& p_precPoseBuf);

    void correctPredPose_(s_PoseWithTwist& p_stPoseGlobal);

    bool updateOdomdev(s_PoseWithTwist& p_stPoseGlobal, PoseDev& p_stOdomFuseDev_);

    /*******************************
     * @function: fillViewPrecPoseBuf_
     * @description: 填充可视化高精度Pose
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void fillViewPrecPoseBuf_(std::vector<Pose>& p_precPoseBuf);

    /*******************************
     * @function: checkTimeDiff_
     * @description: 判断预估Pose时间戳与基准时间戳差值是否符合阈值
     * @param {int&} p_iOutputTime 预估Pose时间戳
     * @param {int&} p_iBaseTime 基准时间戳
     * @return {bool} 时间差是否满足
     * @others: null
     *******************************/
    bool checkTimeDiff_(Time& p_iOutputTime, Time& p_iBaseTime);

    /*******************************
     * @function: popWheelPredPose_
     * @description: 输入时间戳 清空该时间前的所有buf中的预估位姿
     * @param {int} p_iOldRecvTime 过时数据的时间戳
     * @return {*}
     * @others: nill
     *******************************/
    void popWheelPredPose_(s_PoseWithTwist& p_stRecvTwist);

    /*******************************
     * @function: renewFuseTwist_
     * @description: 填充最新外部融合速度
     * @param {s_fuseTwist&} p_stFuseTwist 外部融合速度
     * @return {*}
     * @others: null
     *******************************/
    void renewFuseTwist_(s_fuseTwist& p_stFuseTwist);

    /*******************************
     * @function: getClosestTimePose_
     * @description: 获取时间戳最接近的位姿
     * @param {std::queue<s_PoseWithTwist>&} p_buf 待遍历队列
     * @param {int} p_iTime 设置时间
     * @param {s_PoseWithTwist&} p_stFindPose 待填充位姿
     * @return {是否找到}
     * @others:
     *******************************/
    bool getClosestTimePose_(std::deque<s_PoseWithTwist>& p_buf,
                             int p_iTime,
                             s_PoseWithTwist& p_stFindPose);

  public:
    /*******************************
     * @function: renewPrecisionPose
     * @description: 填充最新slam速度
     * @param {s_PoseWithTwist&} p_stPoseGlobal 最新帧slam位姿
     * @return {*}
     * @others: null
     *******************************/
    void renewPrecisionPose(s_PoseWithTwist& p_stPoseGlobal);

    /*******************************
     * @function: getWheelPredPose
     * @description: 输入时间戳 获取最近时间的预估位姿
     * @param {s_PoseWithTwist} p_stPosePrec 融合外部速度高频预估位姿(待填充)
     * @param {int} p_iRecvTimeCurrPos 当前slam位姿对应雷达的系统时间戳
     * @return {bool} true 找到距离当前雷达最近的prePose
     * @others: null
     *******************************/
    bool getWheelPredPose(s_PoseWithTwist& p_stPosePrec, Time p_iRecvTimeCurrPos);

    /*******************************
     * @function: run
     * @description: 主循环
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void run(void);

    /*******************************
     * @function: wheelTwistCb
     * @description: 回调接口,接收外部传入的局部速度
     * @param {s_PoseWithTwist&} p_stTwist 局部速度
     * @return {*}
     * @others: 注意此回调要求输入必须为局部速度
     *******************************/
    void wheelTwistCb(s_PoseWithTwist& p_stTwist);

    /*******************************
     * @function: shutDown
     * @description: 关闭预估
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void shutDown();

    /*******************************
     * @function: start
     * @description: 重新开始预估
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void start();

    /*******************************
     * @function: stop
     * @description: 停止预估,并清空buf
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void stop();

    /*******************************
     * @function: isStop
     * @description: 是否 停止预估,并清空buf
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    bool isStop(void);

    /*******************************
     * @function: playPause
     * @description: pcap模式下控制暂停，暂停后slamPose/twist照常接收 HP速度暂停
     * @param {*}
     * @return {*}
     * @others: null
     *******************************/
    void playPause(bool p_isPause);

    /**
     * @brief Set the Enable Infinitive Vitual
     *
     * @param p_enable
     */
    void setEnableInfinitiveVitual(bool p_enable)
    {
        c_bInfinitiveVitual_ = p_enable;
    }

    WheelOdometry(FUN_SENDPOS sendpos = FUN_SENDPOS());
    ~WheelOdometry();
};

}  // namespace wj_slam
#    include "./wheelOdomerty.hpp"
#endif
