/**
 * @file wheelOdomerty.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 轮式里程计
 * @version 1.0
 * @date 2022-11-23
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once
#ifndef _WHEELODOMERTY_HPP_
#    define _WHEELODOMERTY_HPP_
#    include "./wheelOdomerty.h"
namespace wj_slam {

/*******************************
 * @function: renewPrecisionPose
 * @description: 接收高精度Pose 修正里程计
 * @param {s_PoseWithTwist&} p_stPoseGlobal 最新帧有效位姿
 * @return {*}
 * @others: null
 *******************************/
void WheelOdometry::renewPrecisionPose(s_PoseWithTwist& p_stPoseGlobal)
{
    LOGOF(WTRACE, "recv slamSpeed");
    std::lock_guard<std::mutex> l_mtx(c_mtxOdomFuseLock_);
    c_stLastPrecPoseTwist = p_stPoseGlobal;
    if (!c_bSysHasInit)
    {
        LOGOF(WINFO, "[WHL] init");
        // 首帧对齐至扫描帧
        c_stHPredPoseTwist = p_stPoseGlobal;
        c_stHPredPoseTwist.timeAlign(p_stPoseGlobal.m_tsSyncTime); /** @todo 确认公式*/
        c_stHPredPoseTwist.m_Pose.m_bFlag = c_HPredFlag_;
        c_stHPredPoseTwist.m_bFlag = c_HPredFlag_;
        // c_stHPredPoseTwist.m_iScanId = c_iScanID_;
        // 首次压入队列，避免无速度无预估Pose问题
        c_HPredPoseBuf_.emplace_back(c_stHPredPoseTwist);
        c_bSysHasInit = true;
        return;
    }

    // 仍掉过旧的Pose
    popWheelPredPose_(p_stPoseGlobal);

    // 重传播，修正队列现有Pose
    correctPredPose_(p_stPoseGlobal);
}

/*******************************
 * @function: popWheelPredPose_
 * @description: 输入时间戳 清空该时间前的所有buf中的预估位姿
 * @param {int} p_iOldRecvTime 过时数据的时间戳
 * @return {*}
 * @others: nill
 *******************************/
void WheelOdometry::popWheelPredPose_(s_PoseWithTwist& p_stRecvTwist)
{
    std::deque<s_PoseWithTwist>::iterator iter;
    for (iter = c_HPredPoseBuf_.begin(); iter != c_HPredPoseBuf_.end();)
    {
        if (p_stRecvTwist.m_tsSyncTime >= (*iter).m_tsSyncTime)
            iter = c_HPredPoseBuf_.erase(iter);
        else
            ++iter;
    }
}

/*******************************
 * @function: renewFuseTwist_
 * @description: 接收最新外部融合速度 预估位姿填充队列
 * @param {s_fuseTwist&} p_stFuseTwist 外部融合速度
 * @return {*}
 * @others:
 *******************************/
void WheelOdometry::renewFuseTwist_(s_fuseTwist& p_stFuseTwist)
{
    LOGOF(WTRACE,
          "recv agvSpeed | flag: {} T: {} recvT: {}",
          p_stFuseTwist.m_iStatus,
          p_stFuseTwist.m_tsSyncTime,
          p_stFuseTwist.m_tsWallTime);
    if (p_stFuseTwist.m_iStatus == TwistStatus::Unknown)
        return;

    if (c_bRun_ && c_bSysHasInit)
    {
        // 如果队列完全清空，无法进行推演，加入当前的初始量作为initial
        if (c_HPredPoseBuf_.empty())
        {
            int l_iTimeNow = c_stSysParam_->m_time.getTimeNowMs();
            c_stHPredPoseTwist = c_stLastPrecPoseTwist;
            c_stHPredPoseTwist.recvTimeAlign(l_iTimeNow);
            c_stHPredPoseTwist.m_Pose.m_bFlag = c_HPredFlag_;
            c_stHPredPoseTwist.m_bFlag = c_HPredFlag_;
            c_HPredPoseBuf_.emplace_back(c_stHPredPoseTwist);
        }

        s_fuseTwist l_stFuseTwist = p_stFuseTwist;
        // 速度滤波
        if (c_stSysParam_->m_vel.m_bWheelFilter)
        {
            c_kf_.update(p_stFuseTwist.m_Twist.coeffs_6());
            l_stFuseTwist.m_Twist.setVector6d(c_kf_.state());
            LOGSPEED(WTRACE,
                     "{} filtTwist: {} | {} {} NULL | {:.3} {:.3} {:.3}",
                     WJLog::getWholeSysTime(),
                     (int)l_stFuseTwist.m_iStatus,
                     l_stFuseTwist.m_tsSyncTime,
                     l_stFuseTwist.m_tsWallTime,
                     l_stFuseTwist.m_Twist.x(),
                     l_stFuseTwist.m_Twist.y(),
                     l_stFuseTwist.m_Twist.yaw());
            if (l_stFuseTwist.m_Twist.normXY() > c_fMaxLineTwist)
            {
                LOGW(WWARN,
                     "{} agv too big {:.3f} {} {} {:.3f} {:.3f} {:.3f} {:.3f} {:.3f} {:.3f} {:.3f} "
                     "{:.3f} {:.3f}",
                     WJLog::getWholeSysTime(),
                     c_fMaxLineTwist,
                     p_stFuseTwist.m_tsWallTime,
                     p_stFuseTwist.m_tsSyncTime,
                     p_stFuseTwist.m_Twist.x(),
                     p_stFuseTwist.m_Twist.y(),
                     p_stFuseTwist.m_Twist.yaw(),
                     l_stFuseTwist.m_Twist.x(),
                     l_stFuseTwist.m_Twist.y(),
                     l_stFuseTwist.m_Twist.yaw(),
                     c_stLastFuseTwist.m_Twist.x(),
                     c_stLastFuseTwist.m_Twist.y(),
                     c_stLastFuseTwist.m_Twist.yaw());
                return;
            }
            c_stLastFuseTwist = l_stFuseTwist;
        }
        if (l_stFuseTwist.m_tsWallTime <= c_stHPredPoseTwist.m_tsWallTime)
        {
            LOGOF(WTRACE,
                  "{} agvSpeed time lag {} < {}",
                  WJLog::getSystemTime(),
                  l_stFuseTwist.m_tsWallTime,
                  c_stHPredPoseTwist.m_tsWallTime);
            return;
        }

        if (twistToLocal_(c_LastHPrecPose, l_stFuseTwist))
        {
            // 转局部速度后开始预估
            c_stHPredPoseTwist.m_Twist = l_stFuseTwist.m_Twist;
            // 首个速度 预估deta(recvT) 之后预估deta(T) 但是recvT仍为 Twist的recvT
            if (c_iLastTwistOwnTime_ == -1)
                c_stHPredPoseTwist.recvTimeAlign(l_stFuseTwist.m_tsWallTime);
            else
                c_stHPredPoseTwist.recvTimeAlign(c_stHPredPoseTwist.m_tsWallTime
                                                 + l_stFuseTwist.m_tsSyncTime
                                                 - c_iLastTwistOwnTime_);
            c_stHPredPoseTwist.m_tsWallTime = l_stFuseTwist.m_tsWallTime;
            // 记录上次速度内部时间戳
            c_iLastTwistOwnTime_ = l_stFuseTwist.m_tsSyncTime;
            // c_stHPredPoseTwist.m_iScanId = c_iScanID_;
            c_HPredPoseBuf_.emplace_back(c_stHPredPoseTwist);
        }
    }
}

/*******************************
 * @function: getClosestTimePose_
 * @description: 获取时间戳最接近的位姿
 * @param {std::deque<s_PoseWithTwist>&} p_buf 待遍历队列
 * @param {int} p_iTime 设置系统时间
 * @param {s_PoseWithTwist&} p_stFindPose 待填充位姿
 * @return {是否找到}
 * @others: 队列为空 则返回最新预估 由外部须校验时间差值是否满足
 *******************************/
bool WheelOdometry::getClosestTimePose_(std::deque<s_PoseWithTwist>& p_buf,
                                        int p_iTime,
                                        s_PoseWithTwist& p_stFindPose)
{
    if (p_buf.empty())
    {
        p_stFindPose = c_stHPredPoseTwist;
        return true;
    }

    int l_timeDiff = 0;
    // 获取预估队列中最接近的位姿
    for (size_t i = 0; i < p_buf.size(); i++)
    {
        p_stFindPose = p_buf.at(i);
        l_timeDiff = abs(p_stFindPose.m_tsWallTime - p_iTime);
        if ((i + 1) < p_buf.size())
        {
            int l_timeDiff_next = abs(p_buf.at(i + 1).m_tsWallTime - p_iTime);
            if (l_timeDiff_next > l_timeDiff)
                break;
        }
    }
    return true;
}

/*******************************
 * @function: getWheelPredPose
 * @description: 输入时间戳 获取最近时间的预估位姿
 * @param {s_PoseWithTwist} p_stPosePrec 融合外部速度预估位姿(待填充) == 上次预估位姿
 * @param {int} p_iRecvTimeCurrPos 当前slam位姿对应雷达的系统时间戳
 * @return {bool} true 找到距离当前雷达最近的prePose
 * @others: 要求p_stPosePrec=上次预估位姿 不可重置 当队列为空 将基于此位姿预估
 * 注意：更新后的p_stPosePrec的 m_tsSyncTime 同样被更改  此值将无意义
 *******************************/
bool WheelOdometry::getWheelPredPose(s_PoseWithTwist& p_stPosePrec, Time p_iRecvTimeCurrPos)
{
    if (!c_bSysHasInit)
    {
        LOGOF(WINFO, "{} [WHL] wait init...", WJLog::getWholeSysTime());
        return false;
    }
    std::lock_guard<std::mutex> l_mtx(c_mtxOdomFuseLock_);
    bool l_bRes = false;
    s_PoseWithTwist l_stPosePrecBak = p_stPosePrec;
    getClosestTimePose_(c_HPredPoseBuf_, p_iRecvTimeCurrPos, p_stPosePrec);
    if (checkTimeDiff_(p_stPosePrec.m_tsWallTime, p_iRecvTimeCurrPos))
    {
        std::vector<Pose> l_predPoseBuf_;
        std::vector<Pose> l_precPoseBuf_;
        l_predPoseBuf_.emplace_back(p_stPosePrec.m_Pose);
        //对齐到雷达时间戳
        p_stPosePrec.recvTimeAlign(p_iRecvTimeCurrPos);
        l_precPoseBuf_.emplace_back(p_stPosePrec.m_Pose);
        // 可视化Pose
        viewPose_(l_predPoseBuf_, l_precPoseBuf_);

        // 用于速度转换
        c_LastHPrecPose = p_stPosePrec.m_Pose;
        c_iLastOutPredRecvTime_ = p_iRecvTimeCurrPos;
        l_bRes = true;
    }
    // 允许无限递推,且时间顺序
    else if (c_bInfinitiveVitual_ && p_stPosePrec.m_tsWallTime <= p_iRecvTimeCurrPos)
    {
        LOGOF(WWARN,
              "[WHL] update timeout getT: {} nowT: {} lastPredT: {}",
              p_iRecvTimeCurrPos,
              l_stPosePrecBak.m_tsWallTime,
              c_iLastOutPredRecvTime_);
        // 匀速假设
        p_stPosePrec.recvTimeAlign(p_iRecvTimeCurrPos);
        c_iLastOutPredRecvTime_ = p_iRecvTimeCurrPos;
    }
    else
    {
        if (!c_bSysHasInit)
            LOGOF(WERROR, "[WHL] not init | wait first local");
        // else if (!c_HPredPoseBuf_.empty())
        //     LOGOF(WERROR,
        //           "[WHL] update timeout getT: {} nowT: {} lastPredT: {}",
        //           WJLog::getWholeSysTime(),
        //           p_iRecvTimeCurrPos,
        //           p_stPosePrec.m_tsWallTime,
        //           c_iLastOutPredRecvTime_);
        // else
        //     LOGOF(WWARN,
        //           "{} wheelOd buf is empty | please check input speed | getT: {}",
        //           WJLog::getWholeSysTime(),
        //           p_iRecvTimeCurrPos);
        // 若预估Buf非空 则p_stPosePrec已被更改 若不满足时间阈值须还原
        p_stPosePrec = l_stPosePrecBak;
    }
    return l_bRes;
}

/*******************************
 * @function: run
 * @description: 主循环，判断是否存在新速度
 * @param {*}
 * @return {*}
 * @others: null
 *******************************/
void WheelOdometry::run(void)
{
    s_fuseTwist l_NewstTwist;
    c_bRunOver_ = false;
    while (1)
    {
        // 若状态非空 则拷贝并清空数据
        if (c_stSysParam_->m_vel.m_stWheelTwist.cutData(l_NewstTwist))
        {
            std::lock_guard<std::mutex> l_mtx(c_mtxOdomFuseLock_);
            renewFuseTwist_(l_NewstTwist);
        }
        if (c_bRun_)
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        if (c_bShutDown_)
            break;
    }
    c_bRunOver_ = true;
}

/*******************************
 * @function: wheelTwistCb
 * @description: 回调接口,接收外部传入的局部速度
 * @param {s_PoseWithTwist&} p_stTwist 局部速度
 * @return {*}
 * @others: 注意此回调要求输入必须为局部速度
 *******************************/
void WheelOdometry::wheelTwistCb(s_PoseWithTwist& p_stTwist)
{
    c_bRunOver_ = false;
    if (c_bRun_ && c_bSysHasInit)
    {
        s_fuseTwist l_NewstTwist;
        l_NewstTwist.m_tsSyncTime = p_stTwist.m_tsSyncTime;
        l_NewstTwist.m_tsWallTime = p_stTwist.m_tsWallTime;
        l_NewstTwist.m_Twist = p_stTwist.m_Twist;
        l_NewstTwist.m_iStatus = TwistStatus::Relative;
        std::lock_guard<std::mutex> l_mtx(c_mtxOdomFuseLock_);
        renewFuseTwist_(l_NewstTwist);
    }
    c_bRunOver_ = true;
}

/*******************************
 * @function: twistToLocal_
 * @description: 判断外部融合速度为绝对坐标系or局部坐标系，决定是否需转局部坐标
 * @param {Pose} p_stHPose 最新高精度位姿
 * @param {s_fuseTwist&} p_stFuseTwist 外部速度 修改为局部速度
 * @return {boo} 是否为局部速度
 * @others: null
 *******************************/
bool WheelOdometry::twistToLocal_(Pose p_stHPose, s_fuseTwist& p_stFuseTwist)
{
    // LOGOF(WTRACE, "{} speedTrans", WJLog::getSystemTime());

    if (p_stFuseTwist.m_iStatus == TwistStatus::Relative)
    {
        //  局部速度 直接输出
        return true;
    }
    else if (p_stFuseTwist.m_iStatus == TwistStatus::Absolute)
    {
        // 全局转局部t‘ = q-1 * t | q‘ = q
        p_stFuseTwist.m_Twist.m_trans =
            p_stHPose.m_quat.conjugate() * p_stFuseTwist.m_Twist.m_trans;
        // p_stFuseTwist.m_Twist.printf("Relative speed: ");
        return true;
    }
    else
    {
        LOGOF(WERROR, "{} fuseTwist status error", WJLog::getSystemTime());
        return false;
    }
}

/*******************************
 * @function: saveHPredPose_
 * @description: 保存precPose 及 twist
 * @param {s_PoseWithTwist&} p_sCurrPose: 最终使用位姿
 * @param {string} p_sFilePath: 路径
 * @return {*}
 * @others: null
 *******************************/
void WheelOdometry::saveHPredPose_(s_PoseWithTwist& p_sUsePose, std::string p_sFilePath)
{
    LOGOF(WTRACE,
          "{} {} {} {} | {:.3} {:.3} {:.3} {} {:.3} {:.3} {:.3}",
          WJLog::getSystemTime(),
          p_sUsePose.m_iScanId,
          p_sUsePose.m_bFlag,
          p_sUsePose.m_tsSyncTime,
          p_sUsePose.m_Pose.x(),
          p_sUsePose.m_Pose.y(),
          p_sUsePose.m_Pose.yaw(),
          p_sUsePose.m_Pose.m_bFlag,
          p_sUsePose.m_Twist.x(),
          p_sUsePose.m_Twist.y(),
          p_sUsePose.m_Twist.yaw());
}

void WheelOdometry::saveTwistInfo_(MoveSpeed& p_sOutSpeed, std::string p_sFilePath)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::app);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_sOutSpeed.x() << "," << p_sOutSpeed.y() << "," << p_sOutSpeed.z() << ","
                     << p_sOutSpeed.yaw() << std::endl;
        l_filePoseWR.close();
    }
}

/*******************************
 * @function: fillViewPrecPoseBuf_
 * @description: 填充可视化高精度Pose
 * @param {*}
 * @return {*}
 * @others: null
 *******************************/
void WheelOdometry::fillViewPrecPoseBuf_(std::vector<Pose>& p_precPoseBuf)
{
    p_precPoseBuf.resize(c_HPredPoseBuf_.size());
    for (uint32_t i = 0; i < c_HPredPoseBuf_.size(); i++)
        p_precPoseBuf[i] = c_HPredPoseBuf_[i].m_Pose;
}

/*******************************
 * @function: viewPose_
 * @description: 位姿可视化 SLAM获取-轮式预估
 * @param {*}
 * @return {*}
 * @others: null
 *******************************/
void WheelOdometry::viewPose_(std::vector<Pose>& p_predPoseBuf, std::vector<Pose>& p_precPoseBuf)
{
    if (c_bSendPos)
    {
        // 更新可视化高精度PrecBuf
        // std::vector<Pose> l_vPrecPoseBuf_;
        // fillViewPrecPoseBuf_(l_vPrecPoseBuf_);
        c_bSendPos(p_predPoseBuf, p_precPoseBuf);
    }
}
/** @todo 公式适配,不需要使用walltime*/
void WheelOdometry::correctPredPose_(s_PoseWithTwist& p_stPoseGlobal)
{
    // 基于最新的高精度Pose重新传播
    s_PoseWithTwist l_tmpPose;
    for (size_t i = 0; i < c_HPredPoseBuf_.size(); i++)
    {
        if (i == 0)
        {
            l_tmpPose = p_stPoseGlobal;
            l_tmpPose.m_Pose.m_bFlag = c_HPredFlag_;
            l_tmpPose.m_bFlag = c_HPredFlag_;
            l_tmpPose.m_iScanId += 1;
        }
        else
            l_tmpPose = c_HPredPoseBuf_[i - 1];
        l_tmpPose.m_Twist = c_HPredPoseBuf_[i].m_Twist;
        l_tmpPose.recvTimeAlign(c_HPredPoseBuf_[i].m_tsWallTime);
        c_HPredPoseBuf_[i] = l_tmpPose;
    }
    if (!c_HPredPoseBuf_.empty())
        c_stHPredPoseTwist = c_HPredPoseBuf_.at(c_HPredPoseBuf_.size() - 1);
}

/*******************************
 * @function: checkTimeDiff_
 * @description: 判断预估Pose时间戳与基准时间戳差值是否符合阈值
 * @param {int&} p_iOutputTime 预估Pose时间戳
 * @param {int&} p_iBaseTime 基准时间戳
 * @return {bool} 时间差是否满足
 * @others: null
 *******************************/
bool WheelOdometry::checkTimeDiff_(Time& p_iOutputTime, Time& p_iBaseTime)
{
    Time l_timeDiff = abs(p_iOutputTime - p_iBaseTime);

    if (l_timeDiff < c_stSysParam_->m_posCheck.m_iPredValidTime)
        return true;
    return false;
}

/*******************************
 * @function: paramInit_
 * @description: 参数初始化
 * @param {*}
 * @return {*}
 * @others: null
 *******************************/
void WheelOdometry::paramInit_()
{
    c_LastHPrecPose.reset();  // 用于速度坐标系转换

    c_HPredFlag_ = PoseStatus::VirtualPose;

    // 用于起始位姿/起始速度策略
    c_bSysHasInit = false;

    // 清空队列
    c_HPredPoseBuf_.clear();
    c_vPredPoseBuf_.clear();

    c_iLastOutPredRecvTime_ = 0;
    c_bShutDown_ = false;
    c_bRunOver_ = true;
    c_bRun_ = true;
}

void WheelOdometry::paramReset_()
{
    paramInit_();
}

void WheelOdometry::start(void)
{
    // 先停止 重置参数再启动
    shutDown();
    paramInit_();
    std::thread wheelOdomThr = std::thread(&WheelOdometry::run, this);
    wheelOdomThr.detach();
}

void WheelOdometry::shutDown(void)
{
    c_bShutDown_ = true;
    stop();
    while (1)
    {
        if (isStop())
            break;
        else
            usleep(1);
    }
}

void WheelOdometry::stop(void)
{
    c_bRun_ = false;
}

bool WheelOdometry::isStop(void)
{
    if (!c_bRun_ && c_bRunOver_)
        return true;
    return false;
}

WheelOdometry::WheelOdometry(FUN_SENDPOS sendpos)
    : c_stSysParam_(SYSPARAM::getIn()), c_bSendPos(sendpos),
      c_kf_(c_stSysParam_->m_vel.m_fWheelProcPN,
            c_stSysParam_->m_vel.m_fWheelProcRN,
            c_stSysParam_->m_vel.m_fWheelMeasPN,
            c_stSysParam_->m_vel.m_fWheelMeasRN),
      c_fMaxLineTwist(c_stSysParam_->m_vel.m_stWheelTwistMax.x())
{
    paramInit_();
}

WheelOdometry::~WheelOdometry()
{
    shutDown();
}

}  // namespace wj_slam
#    define INSTANTIATE_Odomfuse class wj_slam::WheelOdometry;
#endif