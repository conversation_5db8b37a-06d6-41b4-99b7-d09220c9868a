/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-07-14 18:57:09
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-03-23 09:37:43
 */
#pragma once
#ifndef _LOOP_HPP_
#    define _LOOP_HPP_
#    include "../loop.h"
namespace wj_slam {
template <typename P> void LoopGraph<P>::checkLoopThread_()
{
    c_bIsCheckLoop_ = true;
    MapFramePtr l_KFCur;
    PointCloudMapPtr l_pcMapCorn(new PointCloudMap());
    PointCloudMapPtr l_pcMapSurf(new PointCloudMap());

    s_POSE6D l_stMapPose;
    {
        std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
        c_bFindLoop_ = false;
    }
    {
        std::lock_guard<std::mutex> l_mtx(c_mMutexPC_);
        // std::cout << "c_vKFMap_.size : " << c_vKFMap_.size() << std::endl;
        // std::cout << "c_viMayLoopIndex_.size : " << c_viMayLoopIndex_.size() << std::endl;
        l_KFCur = c_vKFMap_.at(c_iCurrKFIndex_);
        std::vector<int>::iterator index = c_viMayLoopIndex_.begin();

        // for (int i = 0; i < c_viMayLoopIndex_.size(); i++)
        // {
        //     std::cout << "c_viMayLoopIndex_[" << i << "]:" << c_viMayLoopIndex_[i] << std::endl;
        // }
        // std::cout << "index_pose : " << *index << std::endl;
        l_stMapPose = c_vKFMap_.at(*index)->m_Pose;
        int lastindex = *index;
        for (; index != c_viMayLoopIndex_.end(); index++)
        {
            if ((*index - lastindex) > 1)
                break;
            lastindex = *index;
            // std::cout << "index : " << *index << std::endl;
            *l_pcMapCorn += *(c_vKFMap_.at(*index)->m_pFeature->first);
            *l_pcMapSurf += *(c_vKFMap_.at(*index)->m_pFeature->second);
        }
    }
    c_pLoop_->setInputSource(c_iCurrKFIndex_,
                             l_KFCur->m_Pose.m_quat,
                             l_KFCur->m_Pose.m_trans,
                             l_KFCur->m_pFeature->first,
                             l_KFCur->m_pFeature->second);
    c_pLoop_->setInputTarget(
        c_viMayLoopIndex_[0], l_stMapPose.m_quat, l_stMapPose.m_trans, l_pcMapCorn, l_pcMapSurf);
    bool l_bLoopSucess = c_pLoop_->align();  //找到回环
    if (l_bLoopSucess)
    {
        std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
        c_bFindLoop_ = true;
        c_iLoopKFIndex_ = c_iCurrKFIndex_;
    }
    PointCloudMap().swap(*l_pcMapCorn);
    PointCloudMap().swap(*l_pcMapSurf);
    c_bIsCheckLoop_ = false;
}
template <typename P> bool LoopGraph<P>::isStartLoop_()
{
    if (c_iLoopKFIndex_ == -1)
        return false;
    if (!c_bLoopRun_ || c_bShutDown_)
        return false;
    std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
    bool res = c_bFindLoop_ && c_bLoopOptDone_ && c_bRenewAllPc_;
    if (res && c_bCheckIndex_)
    {
        bool resIndex = c_iLoopKFIndex_ < c_iCurrUseKFIndex_ ? true : false;
        wjPrint(WJCOL_RED, "c_iLoopKFIndex_ < c_iCurrUseKFIndex_ ", resIndex);
        //回环修正帧不会影响当前定位
        res = res && resIndex;
    }
    return res;
}
template <typename P> void LoopGraph<P>::paramRenew_()
{
    c_pLoop_.reset(new clousureLoop<P>());
    std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
    c_bShutDown_ = false;
    c_bLoopRun_ = false;
    c_bFindLoop_ = false;
    c_bLoopOptDone_ = true;
    c_bNotInit = true;
    c_bIsCheckLoop_ = false;
    c_bCheckIndex_ = true;
    c_bRenewAllPc_ = true;
    c_iCurrUseKFIndex_ = -1;
    c_iCurrKFIndex_ = -1;
    c_iLoopKFIndex_ = -1;
    c_iLastLoopIndex_ = 0;
    std::vector<int>().swap(c_viMayLoopIndex_);
}

template <typename P>
LoopGraph<P>::LoopGraph(std::vector<MapFramePtr>& p_vKFMaps,
                        std::mutex& p_mutex,
                        boost::function<void(bool)> p_loopCb)
    : c_mMutexPC_(p_mutex), c_vKFMap_(p_vKFMaps), c_loopCb_(p_loopCb)
{
    c_pLoop_.reset(new clousureLoop<P>());
    c_pTrans_.reset(new LaserTransform<P>());
}
template <typename P> LoopGraph<P>::~LoopGraph()
{
    std::cout << "exit loop" << std::endl;
    this->shutDowm();
    c_pLoop_ = nullptr;
    c_pTrans_ = nullptr;
}
template <typename P> void LoopGraph<P>::start()
{
    paramRenew_();
    c_bLoopRun_ = true;
}
template <typename P> void LoopGraph<P>::shutDowm()
{
    c_bShutDown_ = true;
}
template <typename P> void LoopGraph<P>::stop()
{
    c_bLoopRun_ = false;
}
template <typename P> bool LoopGraph<P>::isStop()
{
    // if ((!c_bLoopRun_) && c_bLoopOptDone_)
    //     return true;
    return ((!c_bLoopRun_) && c_bLoopOptDone_);
}
template <typename P> bool LoopGraph<P>::isLoopOpt(void)
{
    return !c_bLoopOptDone_;
}
template <typename P> void LoopGraph<P>::renewPoseGraph()
{
    s_POSE6D l_stPose;

    {
        std::lock_guard<std::mutex> l_mtx(c_mMutexPC_);

        c_iCurrKFIndex_ = c_vKFMap_.size() - 1;
        l_stPose = c_vKFMap_.at(c_iCurrKFIndex_)->m_Pose;
    }
    // memset(c_afVariances_, 1e-6, sizeof(c_afVariances_));
    c_pLoop_->renewPoseGraph(c_iCurrKFIndex_, l_stPose.m_quat, l_stPose.m_trans, c_afVariances_);
    if (c_bNotInit)
    {
        c_stLastCheckPose_ = l_stPose;
        c_bNotInit = false;
    }
}
template <typename P> void LoopGraph<P>::renewPoseGraphWhole()
{
    s_POSE6D l_stPose;

    {
        std::lock_guard<std::mutex> l_mtx(c_mMutexPC_);
        for (int i = 0; i < (int)c_vKFMap_.size(); i++)
        {
            c_iCurrKFIndex_ = i;
            l_stPose = c_vKFMap_.at(i)->m_Pose;
            c_pLoop_->renewPoseGraph(
                c_iCurrKFIndex_, l_stPose.m_quat, l_stPose.m_trans, c_afVariances_);
            if (c_bNotInit)
            {
                c_stLastCheckPose_ = l_stPose;
                c_bNotInit = false;
            }
        }
    }
}
template <typename P> void LoopGraph<P>::setLastestMapIndex(int p_iCurrUseKFIndex_)
{
    c_iCurrUseKFIndex_ = p_iCurrUseKFIndex_;
}
template <typename P> bool LoopGraph<P>::checkLoop(std::vector<int> p_viMayLoopIndex)
{
    if (c_bFindLoop_)
        return true;
    if (p_viMayLoopIndex.empty())
        return false;
    c_viMayLoopIndex_ = p_viMayLoopIndex;
    int indexDev = c_iCurrKFIndex_ - c_iLastLoopIndex_;
    if (indexDev < 10)  //太近不进行回环
        return false;
    if (c_bIsCheckLoop_)  //正在检测回环
        return false;
    // if (!c_bLoopOptDone_ || c_bFindLoop_)  //正在进行回环检测或者回环
    //     return true;

    std::thread l_checkLoop(&LoopGraph<P>::checkLoopThread_, this);
    l_checkLoop.detach();
    return false;
}
template <typename P> bool LoopGraph<P>::checkLoop(void)
{
    return c_bFindLoop_;
}
template <typename P> void LoopGraph<P>::renewAllKeyFrame()
{
    std::lock_guard<std::mutex> l_mtx(c_mMutexPC_);
    std::cout << "renew other map: start.at[" << c_iLoopKFIndex_ << "]  end.at["
              << c_vKFMap_.size() - 1 << "]" << std::endl;

    s_POSE6D l_stPoseLoop = c_vKFMap_.at(c_iLoopKFIndex_ - 1)->m_Pose;
    s_POSE6D l_stPoseOpt = l_stPoseLoop * c_stPoseLastest_.inverse();
    for (int ind = c_iLoopKFIndex_; ind < (int)c_vKFMap_.size(); ind++)
    {
        s_POSE6D l_stPose = l_stPoseOpt * c_vKFMap_.at(ind)->m_Pose;  //求增量
        l_stPose.printf("allpc");
        c_vKFMap_.at(ind)->m_Pose = l_stPose;
        MapFramePtr& l_pMapFrame = c_vKFMap_.at(ind);
        c_pTrans_->transformCloudPoints(l_stPoseOpt.m_quat,
                                        l_stPoseOpt.m_trans,
                                        l_pMapFrame->m_pFeature->first,
                                        l_pMapFrame->m_pFeature->first);
        c_pTrans_->transformCloudPoints(l_stPoseOpt.m_quat,
                                        l_stPoseOpt.m_trans,
                                        l_pMapFrame->m_pFeature->second,
                                        l_pMapFrame->m_pFeature->second);
    }

    {
        std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
        c_bRenewAllPc_ = true;
        c_bFindLoop_ = false;
    }
}
template <typename P> void LoopGraph<P>::optForce()
{
    std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
    c_bCheckIndex_ = false;
}
template <typename P> void LoopGraph<P>::run()
{
    while (1)
    {
        //程序运行 & 发现回环 & 上一次回环结束
        if (isStartLoop_())
        {
            {
                std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
                c_bLoopOptDone_ = false;
                c_bRenewAllPc_ = false;
            }
            std::cout << "renew loop map: start.at[" << 0 << "]  end.at[" << c_iLoopKFIndex_ - 1
                      << "]" << std::endl;

            c_stPoseLastest_ =
                c_vKFMap_.at(c_iLoopKFIndex_ - 1)->m_Pose;  //保存优化前位姿，为后续kf转换提供基准
            for (int ind = 0; ind < c_iLoopKFIndex_; ind++)
            {
                s_POSE6D l_stPose = c_vKFMap_.at(ind)->m_Pose;
                MapFramePtr l_mapFrame = c_vKFMap_.at(ind);
                c_pLoop_->setOriKeyFrame(ind,
                                         l_stPose.m_quat,
                                         l_stPose.m_trans,
                                         l_mapFrame->m_pFeature->first,
                                         l_mapFrame->m_pFeature->second);
                c_pLoop_->getRenewKeyFrame(l_stPose.m_quat,
                                           l_stPose.m_trans,
                                           l_mapFrame->m_pFeature->first,
                                           l_mapFrame->m_pFeature->second);
            }
            c_loopCb_(true);
            {
                std::lock_guard<std::mutex> l_mtx(c_mMutexFlag_);
                c_bLoopOptDone_ = true;
            }
        }
        if (c_bShutDown_)
            break;
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}
}  // namespace wj_slam
#    define WJSLAM_Loop(P) template class wj_slam::LoopGraph<P>;
#endif