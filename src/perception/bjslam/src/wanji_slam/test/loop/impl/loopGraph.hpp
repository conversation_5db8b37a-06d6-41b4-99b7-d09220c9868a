/**
 * @file loopGraph.hpp
 * <AUTHOR> Li
 * @brief 回环检测模块对外接口
 * @version 1.0
 * @date 2023-08-15
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */
#pragma once
#include "../loopGraph.h"

namespace wj_slam {
_T_M_ LoopGraph<M>::LoopGraph(boost::shared_ptr<KfMapPair> p_pMapPair)
    : c_pMapPair_(p_pMapPair), c_bGpsTrigger_(false), c_bMarkTrigger_(false),
      c_bCorrectedFlag_(false)  
{
    //回环检测类
    c_pLoopCheck_.reset(new LoopCheck<M>(c_pMapPair_));
    c_pMapStor_.reset(new MappingStorage<M>(c_pMapPair_));
    //连续建图模式下,先将旧地图添加至因子图
    for (int i = 0; i < c_pMapPair_->getKfSize(); i++)
    {
        s_POSE6D l_stPose;
        MapPosePair l_mapPair = c_pMapPair_->getKfPairByInd(i);
        l_stPose.setXYZ(l_mapPair.pose.x, l_mapPair.pose.y, l_mapPair.pose.z);
        l_mapPair.map->m_pFeature->m_uiScanFrame = i;
        l_mapPair.map->m_Pose = l_stPose;
        c_pMapPair_->setKfMapByInd(i, l_mapPair.map);
        addPoseNodeFactor(i, l_stPose);
    }
}

_T_M_ LoopGraph<M>::~LoopGraph()
{
    // c_pLoopCheck_ = nullptr;
}

_T_M_ void LoopGraph<M>::setGPSTrigger(){
    c_bGpsTrigger_ = true;
}


_T_M_ void LoopGraph<M>::addPoseNodeFactor(int p_iCurrIndex, Pose& p_CurrPose)
{
    if (0 == p_iCurrIndex)
    {
        // 添加初始位姿因子
        c_pFactorGraph_.setInitPoseFactor(p_iCurrIndex,
                                          FactorGraph::NodeType::Pose,
                                          p_CurrPose.m_quat,
                                          p_CurrPose.m_trans,
                                          c_vPriorPoseVar);
        // 添加位姿节点初始值
        c_pFactorGraph_.setIndexPose(
            p_iCurrIndex, FactorGraph::NodeType::Pose, p_CurrPose.m_quat, p_CurrPose.m_trans);
        std::cout <<"addPoseNodeFactor： " << p_CurrPose.m_trans[0] <<", " << p_CurrPose.m_trans[1] << std::endl;
        c_pMapStor_->setInitPose(p_iCurrIndex, p_CurrPose, c_vPriorPoseVar);       
    }
    else
    {
        // 获取上次位姿点（可能未更新因子图，不存在getIndexPose）

        FramePtr l_pLastKF = c_pMapPair_->getKfMapByInd(p_iCurrIndex - 1);
        Pose& l_LastPose = l_pLastKF->m_Pose;
        // 添加相对关系因子
        c_pFactorGraph_.addGraphConstraint(p_iCurrIndex - 1,
                                           FactorGraph::NodeType::Pose,
                                           l_LastPose.m_quat,
                                           l_LastPose.m_trans,
                                           p_iCurrIndex,
                                           FactorGraph::NodeType::Pose,
                                           p_CurrPose.m_quat,
                                           p_CurrPose.m_trans,
                                           c_vBetweenPoseVar);
        // 添加位姿节点初始值
        c_pFactorGraph_.setIndexPose(
            p_iCurrIndex, FactorGraph::NodeType::Pose, p_CurrPose.m_quat, p_CurrPose.m_trans);

        float roll, pitch, yaw;
        // roll (x-axis rotation)
        double sinr_cosp = +2.0 * (p_CurrPose.m_quat.w() * p_CurrPose.m_quat.x() + p_CurrPose.m_quat.y() * p_CurrPose.m_quat.z());
        double cosr_cosp = +1.0 - 2.0 * (p_CurrPose.m_quat.x() * p_CurrPose.m_quat.x() + p_CurrPose.m_quat.y() * p_CurrPose.m_quat.y());
        roll = atan2(sinr_cosp, cosr_cosp);

        // pitch (y-axis rotation)
        double sinp = +2.0 * (p_CurrPose.m_quat.w() * p_CurrPose.m_quat.y() - p_CurrPose.m_quat.z() * p_CurrPose.m_quat.x());
        if (fabs(sinp) >= 1)
            pitch = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
        else
            pitch = asin(sinp);

        // yaw (z-axis rotation)
        double siny_cosp = +2.0 * (p_CurrPose.m_quat.w() * p_CurrPose.m_quat.z() + p_CurrPose.m_quat.x() * p_CurrPose.m_quat.y());
        double cosy_cosp = +1.0 - 2.0 * (p_CurrPose.m_quat.y() * p_CurrPose.m_quat.y() + p_CurrPose.m_quat.z() * p_CurrPose.m_quat.z());
        yaw = atan2(siny_cosp, cosy_cosp);
        std::cout <<"addPoseNodeFactor： " << p_CurrPose.m_trans[0] <<", " << p_CurrPose.m_trans[1] 
            <<", heading = " << yaw * 180 / M_PI << std::endl;
        Pose l_between = l_LastPose.inverse() * p_CurrPose;
        c_pMapStor_->setGraphConstraint(
            p_iCurrIndex - 1, p_iCurrIndex, l_between, c_vBetweenPoseVar);
    }
    c_pMapStor_->setIndexPose(p_iCurrIndex, p_CurrPose);
}

void toEuler(const Eigen::Quaterniond& q, float& roll, float& pitch, float& yaw)
{
    // roll (x-axis rotation)
    double sinr_cosp = +2.0 * (q.w() * q.x() + q.y() * q.z());
    double cosr_cosp = +1.0 - 2.0 * (q.x() * q.x() + q.y() * q.y());
    roll = atan2(sinr_cosp, cosr_cosp);
    // pitch (y-axis rotation)
    double sinp = +2.0 * (q.w() * q.y() - q.z() * q.x());
    if (fabs(sinp) >= 1)
        pitch = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
    else
        pitch = asin(sinp);
    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    yaw = atan2(siny_cosp, cosy_cosp);
}

_T_M_ void LoopGraph<M>::addGpsNodeFactor(int p_iCurrIndex, Pose& p_CurrPose)
{
    std::cout<< "loopGraph.hpp addGpsNodeFactor： p_iCurrIndex = " << p_iCurrIndex << std::endl;
    if (0 == p_iCurrIndex){
         // 添加GPS因子（m_trans有效）
        c_pFactorGraph_.setInitPoseFactor(p_iCurrIndex,
                                        FactorGraph::NodeType::GPS,
                                        p_CurrPose.m_quat,
                                        p_CurrPose.m_trans,
                                        c_vGPSPositionVar);
        std::cout <<"addGpsNodeFactor: gps pose = " << p_CurrPose.m_trans[0] <<", " << p_CurrPose.m_trans[1] << std::endl;
        std::cout<< "loopGraph.hpp setInitPoseFactor finished--------------" << std::endl;
        //    添加GPS节点初始值
        // c_pFactorGraph_.setIndexPose(
        //     p_iCurrIndex, FactorGraph::NodeType::GPS, Eigen::Quaterniond(), p_CurrPose.m_trans);
        std::cout<< "loopGraph.hpp setIndexPose finished--------------" << std::endl;
        c_pMapStor_->setGpsNode(p_iCurrIndex, p_CurrPose, c_vGPSPositionVar);
        std::cout<< "loopGraph.hpp setGpsNode finished--------------" << std::endl;
        c_bGpsTrigger_ = true;
    }
    else{
        float roll, pitch, yaw;
        toEuler(p_CurrPose.m_quat, roll, pitch, yaw);
        // 如果前后差距过大，暂不添加GPS因子 TODO
        FramePtr l_pLastKF = c_pMapPair_->getKfMapByInd(p_iCurrIndex - 1);
        Pose& l_LastPose = l_pLastKF->m_Pose;
        float deltaX = l_LastPose.x() - p_CurrPose.m_trans[0];
        float deltaY = l_LastPose.y() - p_CurrPose.m_trans[1];
        float deltaDistance = sqrt(deltaX * deltaX + deltaY * deltaY);

        float rolllast, pitchlast, yawlast;
        toEuler(l_LastPose.m_quat, rolllast, pitchlast, yawlast);
        std::cout <<"addGpsNodeFactor: gps pose =  " << p_CurrPose.m_trans[0] <<", " << p_CurrPose.m_trans[1] 
            <<", heading = " << yaw * 180 / M_PI 
            <<", last heading = " << yawlast * 180 / M_PI << std::endl;
        float headingGap = (yaw - yawlast) * 180 / M_PI;
        if(abs(headingGap) > 0.5 || deltaDistance > 1.5){
            std::cout<< "deltaDistance = "<< deltaDistance <<" > 1m, 不加入GPS优化， c_bGpsTrigger_ = "<<c_bGpsTrigger_ 
                <<", headingGap = "<< headingGap << std::endl;
            return;
        }

        std::cout<< "deltaDistance = "<< deltaDistance <<", 加入GPS优化， c_bGpsTrigger_ = "<<c_bGpsTrigger_ 
                <<", headingGap = "<< headingGap << std::endl;
        c_pFactorGraph_.setIndexPose(
            p_iCurrIndex, FactorGraph::NodeType::GPS, p_CurrPose.m_quat, p_CurrPose.m_trans);
        c_pMapStor_->setGpsNode(p_iCurrIndex, p_CurrPose, c_vGPSPositionVar);
        c_bGpsTrigger_ = true;
    }
   
    // 通知
    
    std::cout<< "loopGraph.hpp addGpsNodeFactor finished--------------" << std::endl;
}

_T_M_ void LoopGraph<M>::addMarksNodeFactor(int p_iCurrIndex, PCPtr p_CurrMarks)
{
    // 获取当前位姿点（未更新因子图，不存在getIndexPose）
    Pose& l_NeighberPose = (c_pMapPair_->getKfMapByInd(p_iCurrIndex)->m_Pose);
    for (auto mark : p_CurrMarks->points)
    {
        // 靶标序列号
        int l_iMarkIndex = mark.h;
        int markTimes = addMarkSet_(l_iMarkIndex);
        // 靶标出现次数太少，不加入因子
        if (markTimes < 2)
            continue;
        // 靶标位置
        Eigen::Vector3d markPosition = mark.getVector3fMap().template cast<double>();
        // 添加相对关系因子
        c_pFactorGraph_.addGraphConstraint(p_iCurrIndex,
                                           FactorGraph::NodeType::Pose,
                                           l_NeighberPose.m_quat,
                                           l_NeighberPose.m_trans,
                                           l_iMarkIndex,
                                           FactorGraph::NodeType::Mark,
                                           Eigen::Quaterniond(),
                                           markPosition,
                                           c_vMarkPositionVar);
        // 靶标首次满足条件
        if (2 == markTimes)
        {
            // 添加位姿节点初始值
            c_pFactorGraph_.setIndexPose(
                l_iMarkIndex, FactorGraph::NodeType::Mark, Eigen::Quaterniond(), markPosition);
        }
    }
}

_T_M_ void LoopGraph<M>::addLoopCheckPair(int p_iCurrIndex, std::vector<int>& p_viMayIndex)
{
    if (!p_viMayIndex.empty())
    {
        c_pLoopCheck_->setTryPair(p_iCurrIndex, p_viMayIndex);
    }
}

_T_M_ void LoopGraph<M>::correct()
{
    //点云和位姿变化标志
    c_bCorrectedFlag_ = false;
    std::cout << "hsq LoopGraph<M>::correct() initial set c_bCorrectedFlag_ = false" << std::endl;
    //添加回环因子（若有）
    addLoopCheckFactor_();
    //更新因子图
    c_pFactorGraph_.update();
    //更新地图和路径
    renewMapAndPath_(0, c_pMapPair_->getKfSize() - 1);
    // 完成修正，重置标志(GPS标志需要LG类控制，LC标志自动调整)
    c_bGpsTrigger_ = false;
    c_bMarkTrigger_ = false;
}

_T_M_ bool LoopGraph<M>::getIfUpdate()
{
    //todo:get和set,读取标志和修改标志分开
    bool l_bRes = c_bCorrectedFlag_;
    std::cout << "hsq LoopGraph<M>::getIfUpdate() c_bCorrectedFlag_ = " << c_bCorrectedFlag_ << std::endl;
    if (c_bCorrectedFlag_)
        c_bCorrectedFlag_ = false;
    return l_bRes;
}

_T_M_ void LoopGraph<M>::addLoopCheckFactor_()
{
    if (readyLoopCorrect())
    {
        // 提取回环配准结果（触发LC线程自动结束）
        auto l_LCRes = c_pLoopCheck_->getLoopCheckResult();
        // 添加回环相对关系因子
        c_pFactorGraph_.addGraphConstraint(l_LCRes.m_uiLoopFrameID,
                                           FactorGraph::NodeType::Pose,
                                           l_LCRes.m_uiLoopFramePose.m_quat,
                                           l_LCRes.m_uiLoopFramePose.m_trans,
                                           l_LCRes.m_uiCurrFrameID,
                                           FactorGraph::NodeType::Pose,
                                           l_LCRes.m_sEstimatedPose.m_quat,
                                           l_LCRes.m_sEstimatedPose.m_trans,
                                           c_vLoopPoseVar);
        Pose l_between = l_LCRes.m_uiLoopFramePose.inverse() * l_LCRes.m_sEstimatedPose;
        c_pMapStor_->setGraphConstraint(
            l_LCRes.m_uiLoopFrameID, l_LCRes.m_uiCurrFrameID, l_between, c_vLoopPoseVar);
        LOGM(WINFO,
             "{} [PG] LoopCorrect X{} --> X{}",
             WJLog::getWholeSysTime(),
             l_LCRes.m_uiCurrFrameID,
             l_LCRes.m_uiLoopFrameID);
    }
}

_T_M_ void LoopGraph<M>::renewMapAndPath_(int p_iFromIdx, int p_iToIdx)
{
    std::cout <<"hsq:LoopGraph<M>::renewMapAndPath_ use gps correct: p_iFromIdx = " << p_iFromIdx <<", p_iToIdx = " << p_iToIdx << std::endl;
    for (int i = p_iToIdx; i >= p_iFromIdx; --i)
    {
        
        Pose l_NewPose;
        // 注意只能在update后调用，否则存在部分序列未加入ISAM
        c_pFactorGraph_.getIndexPose(
            i, FactorGraph::NodeType::Pose, l_NewPose.m_quat, l_NewPose.m_trans);
        MapPosePair l_mapPair = c_pMapPair_->getKfPairByInd(i);
        Pose l_Correct = l_NewPose * l_mapPair.map->m_Pose.inverse();
        if (c_trans_.isEffectiveTransform(l_Correct.m_quat, l_Correct.m_trans))
        {
            LOGM(WDEBUG,
                 "{} [PG] LoopCorrect X{}, trans:[{:1f}m,{:.1f}m,{:.1f}deg]",
                 WJLog::getWholeSysTime(),
                 i,
                 l_Correct.x(),
                 l_Correct.y(),
                 l_Correct.yaw());

            // std::cout <<"位姿有效: l_Correct: " << l_Correct.x() <<"," << l_Correct.y()<<"," <<
            //      l_Correct.yaw() << std::endl;
            // 位姿更新
            l_mapPair.map->m_Pose.m_quat = l_NewPose.m_quat;
            l_mapPair.map->m_Pose.m_trans = l_NewPose.m_trans;
            // 点云更新
            c_trans_.transformCloudPoints(l_Correct.m_quat,
                                          l_Correct.m_trans,
                                          l_mapPair.map->m_pFeature->first,
                                          l_mapPair.map->m_pFeature->first);
            c_trans_.transformCloudPoints(l_Correct.m_quat,
                                          l_Correct.m_trans,
                                          l_mapPair.map->m_pFeature->second,
                                          l_mapPair.map->m_pFeature->second);
            c_trans_.transformCloudPoints(l_Correct.m_quat,
                                          l_Correct.m_trans,
                                          l_mapPair.map->m_pFeature->fourth,
                                          l_mapPair.map->m_pFeature->fourth);
            // c_trans_.transformCloudPoints(l_Correct.m_quat,
            //                               l_Correct.m_trans,
            //                               l_mapPair.map->m_pFeature->allPC,
            //                               l_mapPair.map->m_pFeature->allPC);
            // 路径点更新

            l_mapPair.pose.x = l_NewPose.x();
            l_mapPair.pose.y = l_NewPose.y();
            l_mapPair.pose.z = l_NewPose.z();
            c_pMapPair_->setKfMapByInd(i, l_mapPair.map);
            c_pMapPair_->setKfPoseByInd(i, l_mapPair.pose);
            c_bCorrectedFlag_ = true;
        }
        else{
            // std::cout <<"位姿无效: p_iFromIdx = " << p_iFromIdx <<", p_iToIdx = " << p_iToIdx <<", l_Correct: " << l_Correct.x() <<"," << l_Correct.y()<<"," <<
            //      l_Correct.yaw() << std::endl;
        }
    }
}

_T_M_ int LoopGraph<M>::addMarkSet_(int p_iMarkIdx)
{
    // 首次添加
    if (c_mMarkSet_.find(p_iMarkIdx) == c_mMarkSet_.end())
    {
        c_mMarkSet_[p_iMarkIdx] = 1;
        return 1;
    }
    return ++c_mMarkSet_[p_iMarkIdx];
}
}  // namespace wj_slam
#define WJSLAM_LoopGraph(M) template class wj_slam::LoopGraph<M>;