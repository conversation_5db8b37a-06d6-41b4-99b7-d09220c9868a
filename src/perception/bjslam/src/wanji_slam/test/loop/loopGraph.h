/**
 * @file loopGraph.h
 * <AUTHOR> Li
 * @brief 回环检测对外接口
 * @version 1.0
 * @date 2023-08-15
 * @copyright Copyright (c)2023 Van<PERSON>
 */
#pragma once
#include "algorithm/loop/factorGraph/impl/factorGraph.hpp"
#include "algorithm/loop/loopCheck/loopCheck.hpp"
#include "algorithm/loop/mappingStorage.hpp"
#include "algorithm/map/sub_map/KeyFrameMap.h"
#include "algorithm/optimize/laserTransform.h"
#include <unordered_map>

namespace wj_slam {
template <typename M = pcl::PointXYZHSV> class LoopGraph {
  private:
    using FrameFeature = FEATURE_PAIR<M>;               /**< 输入帧-特征组类型*/
    using FrameFeaturePtr = typename FrameFeature::Ptr; /**< 输入帧-特征组指针类型*/
    using Frame = KEYFRAME<M>;                          /**< 输入帧-特征组类型*/
    using FramePtr = typename Frame::Ptr;               /**< 输入帧-特征组指针类型*/
    using PC = pcl::PointCloud<M>;                      /**< 输入帧-点云类型*/
    using PCPtr = typename PC::Ptr;                     /**< 输入帧-点云指针类型*/
    using PosePoint = pcl::PointXYZI;                   /**< 输入帧-路径点类型*/
    using PoseCloud = pcl::PointCloud<PosePoint>;       /**< 输入帧-路径点云类型*/
    using PoseCloudPtr = typename PoseCloud::Ptr;       /**< 输入帧-路径点云指针类型*/
    using Pose = wj_slam::s_POSE6D;                     /**< 6D位姿别名*/

    boost::shared_ptr<MappingStorage<M>> c_pMapStor_;
    boost::shared_ptr<LoopCheck<M>> c_pLoopCheck_;      /**< 回环检测流程*/
    FactorGraph c_pFactorGraph_;                        /**< 位姿图管理器*/
    LaserTransform<M> c_trans_;                         /**< 点云转移方法*/
    boost::shared_ptr<KfMapPair> c_pMapPair_ = nullptr; /**< 关键帧队列管理对象*/
    bool c_bGpsTrigger_;                                /**< 加入新GPS因子标志 */
    bool c_bMarkTrigger_;                               /**< 加入靶标因子标志 */
    bool c_bCorrectedFlag_;                             /**< 因子图修正标志*/
    std::unordered_map<int, int> c_mMarkSet_;           /**< Mark因子缓冲 */
    float c_vPriorPoseVar[6] = {1e-5, 1e-5, 1e-5, 1e-5, 1e-5, 1e-5}; /**< 初始位姿噪声 */
    float c_vBetweenPoseVar[6] = {0.0004, 0.0004, 0.0004, 0.0025, 0.0025, 0.0025}; /**<
                                                                                      连续约束噪声*/
    float c_vLoopPoseVar[6] = {1e-5, 1e-5, 1e-5, 1e-6, 1e-6, 1e-6};     /**< 回环约束噪声 */
    float c_vGPSPositionVar[6] = {0.05, 0.05, 0.1, 0, 0, 0};                    /**< GPS位置噪声 */
    float c_vMarkPositionVar[6] = {0.03, 0.03, 0.05, 0.00, 0.00, 0.00}; /**< 靶标约束噪声 */
  public:
    /*** 设置GPS触发用于GPS优化，在location.cpp中设置*/
    void setGPSTrigger();
    /**
     * @brief 保存建图过程数据
     * @param filename
     *
     */
    void saveMapDate(const string& filename)
    {
        c_pMapStor_->saveMapDate(filename);
    }
    /**
     * @brief 保存gtsam的Graph数据
     * @param filename 路径
     *
     */
    void saveGraphDate(const string& filename)
    {
        gtsam::NonlinearFactorGraph graph;
        gtsam::Values estimate;
        c_pFactorGraph_.getGraph(graph, estimate);
        c_pMapStor_->saveGraphDate(graph, estimate, filename);
    }
    /**
     * @brief 保存加密关键帧数据
     * @param filename 路径
     *
     */
    void saveKFBinary(const string& filename)
    {
        c_pMapStor_->saveKFBinary(filename);
    }
    /**
     * @brief Construct a new Loop Graph object
     * @param p_vMaps 当前地图帧顺序保存容器
     * @param p_pPath 对应路径点点云存储
     * @param p_Mutex 地图帧容器锁
     *
     */
    LoopGraph(boost::shared_ptr<KfMapPair> p_pMapPair);
    ~LoopGraph();
    /**
     * @brief 接口1：添加新的关键帧时调用，添加新节点和因子到位姿图
     *
     * @param p_iCurrIndex 当前帧序号
     * @param p_CurrPose 当前帧位姿
     */
    void addPoseNodeFactor(int p_iCurrIndex, Pose& p_CurrPose);

    /**
     * @brief 接口2：添加新的GPS位置时调用，添加新节点和因子到位姿图
     *
     * @param p_iCurrIndex 当前GPS点序号（同步帧序号）
     * @param p_CurrPose 当前GPS点位姿
     */
    void addGpsNodeFactor(int p_iCurrIndex, Pose& p_CurrPose);

    /**
     * @brief 接口3：添加新的靶标位置时调用，添加新节点和因子到位姿图
     *
     * @param p_iCurrIndex 当前帧序号（同步帧序号）
     * @param p_CurrMarks 当前靶标点序列
     */
    void addMarksNodeFactor(int p_iCurrIndex, PCPtr p_CurrMarks);

    /**
     * @brief 接口4:添加一对待匹配帧-地图帧
     *
     * @param p_iCurrIndex 当前帧
     * @param p_viMayIndex 可能回环的地图帧
     */
    void addLoopCheckPair(int p_iCurrIndex, std::vector<int>& p_viMayIndex);

    /**
     * @brief 表征可以执行地图修正
     *
     */
    bool readyCorrect()
    {
        if (readyLoopCorrect() || readyGpsCorrect() || readyMarkCorrect())
            return true;
        return false;
    }

    /**
     * @brief 表征回环检测有成果
     *
     */
    bool readyLoopCorrect()
    {
        return thread::State::Paused == c_pLoopCheck_->getLoopCheckState();
    }

    /**
     * @brief 表征GPS添加到因子图，待更新
     *
     * @return [true] [details]
     * @return [false] [details]
     */
    bool readyGpsCorrect()
    {
        return c_bGpsTrigger_;
    }
    /**
     * @brief 表征mark添加到因子图，待更新
     *
     * @return [true] [details]
     * @return [false] [details]
     */
    bool readyMarkCorrect()
    {
        return c_bMarkTrigger_;
    }

    /**
     * @brief 修正！阻塞性质修正全部数据
     *
     * @note 待添加分段式修正
     */
    void correct();
    /**
     * @brief 获取是否发生关键帧队列点云变化状态
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool getIfUpdate();
    /**
     * @brief 保存因子图文件
     * @param filename
     *
     */
    void saveGraph(std::string filename)
    {
        c_pFactorGraph_.saveGraph(filename);
    }

  private:
    /**
     * @brief 添加回环因子
     *
     */
    void addLoopCheckFactor_();

    /**
     * @brief 修正一段地图和路径
     *
     * @param p_iFromIdx 循环起点
     * @param p_iToIdx 循环终点
     */
    void renewMapAndPath_(int p_iFromIdx, int p_iToIdx);

    /**
     * @brief 将靶标序列号添加到缓冲
     *
     * @param p_iMarkIdx
     * @return [int] [details]
     */
    int addMarkSet_(int p_iMarkIdx);
};
}  // namespace wj_slam
#define WJSLAM_LoopGraph(M) template class wj_slam::LoopGraph<M>;