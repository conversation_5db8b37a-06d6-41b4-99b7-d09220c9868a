#pragma once

/*
 * @Descripttion: 定位模块各位姿容器类
 * @version: v1.0
 * @Author: x<PERSON><PERSON>
 * @Date: 2023-04-14 10：19
 * @LastEditors:
 * @LastEditTime:
 */

// local
#include "common/type/type_pose.h"

namespace wj_slam {

class LocationPoseTwistContainer {
  public:
    LocationPoseTwistContainer();
    ~LocationPoseTwistContainer();

    inline void setSlamOptPoseTwist(const s_PoseWithTwist& p_stPoseTwist)
    {
        c_stSlamOptPoseTwist_ = p_stPoseTwist;
    }
    inline void setOdomPredPoseTwist(const s_PoseWithTwist& p_stPoseTwist)
    {
        c_stOdomPredPoseTwist_ = p_stPoseTwist;
    }
    inline void setFusePoseTwist(const s_PoseWithTwist& p_stPoseTwist)
    {
        c_stFusePoseTwist_ = p_stPoseTwist;
    }
    inline void setLastSlamPrecPoseTwist(const s_PoseWithTwist& p_stPoseTwist)
    {
        c_stLastSlamPrecPoseTwist_ = p_stPoseTwist;
    }
    inline void setLastAddMapPoseTwist(const s_PoseWithTwist& p_stPoseTwist)
    {
        c_stLastAddMapPoseTwist_ = p_stPoseTwist;
    }

    inline void setSlamOptPoseFlag(wj_slam::PoseStatus p_bFlag)
    {
        c_stSlamOptPoseTwist_.m_Pose.m_bFlag = p_bFlag;
    }
    inline void setOdomPredPoseFlag(wj_slam::PoseStatus p_bFlag)
    {
        c_stOdomPredPoseTwist_.m_Pose.m_bFlag = p_bFlag;
    }
    inline void setFusePoseFlag(wj_slam::PoseStatus p_bFlag)
    {
        c_stFusePoseTwist_.m_Pose.m_bFlag = p_bFlag;
    }
    inline void setLastSlamPrecPoseFlag(wj_slam::PoseStatus p_bFlag)
    {
        c_stLastSlamPrecPoseTwist_.m_Pose.m_bFlag = p_bFlag;
    }
    inline void setLastAddMapPoseFlag(wj_slam::PoseStatus p_bFlag)
    {
        c_stLastAddMapPoseTwist_.m_Pose.m_bFlag = p_bFlag;
    }

    inline s_PoseWithTwist getSlamOptPoseTwist() const
    {
        return c_stSlamOptPoseTwist_;
    }
    inline s_PoseWithTwist getOdomPredPoseTwist() const
    {
        return c_stOdomPredPoseTwist_;
    }
    inline s_PoseWithTwist getFusePoseTwist() const
    {
        return c_stFusePoseTwist_;
    }
    inline s_PoseWithTwist getLastSlamPrecPoseTwist() const
    {
        return c_stLastSlamPrecPoseTwist_;
    }
    inline s_PoseWithTwist getLastAddMapPoseTwist() const
    {
        return c_stLastAddMapPoseTwist_;
    }

    inline s_POSE6D getSlamOptPose() const
    {
        return c_stSlamOptPoseTwist_.m_Pose;
    }
    inline s_POSE6D getOdomPredPose() const
    {
        return c_stOdomPredPoseTwist_.m_Pose;
    }
    inline s_POSE6D getFusePose() const
    {
        return c_stFusePoseTwist_.m_Pose;
    }
    inline s_POSE6D getLastSlamPrecPose() const
    {
        return c_stLastSlamPrecPoseTwist_.m_Pose;
    }
    inline s_POSE6D getLastAddMapPose() const
    {
        return c_stLastAddMapPoseTwist_.m_Pose;
    }

    void reset();

  private:
    s_PoseWithTwist c_stSlamOptPoseTwist_;       // SLAM优化位姿
    s_PoseWithTwist c_stOdomPredPoseTwist_;      // 轮式里程计预估位姿
    s_PoseWithTwist c_stFusePoseTwist_;          // 融合位姿
    s_PoseWithTwist c_stLastSlamPrecPoseTwist_;  // 上次SLAM高精位姿及速度
    s_PoseWithTwist c_stLastAddMapPoseTwist_;    // 上次更新地图位姿
};

LocationPoseTwistContainer::LocationPoseTwistContainer() {}

LocationPoseTwistContainer::~LocationPoseTwistContainer() {}

void LocationPoseTwistContainer::reset()
{
    c_stSlamOptPoseTwist_.reset();
    c_stOdomPredPoseTwist_.reset();
    c_stFusePoseTwist_.reset();
    c_stLastSlamPrecPoseTwist_.reset();
    c_stLastAddMapPoseTwist_.reset();
}

}  // namespace wj_slam