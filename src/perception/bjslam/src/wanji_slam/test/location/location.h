/*
 * @Descripttion:
 * @version:
 * @Author: zu<PERSON><PERSON>
 * @Date: 2021-04-28 19:56:24
 * @LastEditors: Tao<PERSON><NAME_EMAIL>
 * @LastEditTime: 2023-07-12 09:41:42
 */
#pragma once
#ifndef _LOCATION_H_
#    define _LOCATION_H_
// #    include "../loop/loop.h"
#    include "../loop/loopGraph.h"
// #    include "../../gtest/gtest_test.h"
#    include "../mapproc/map_proc.h"
// #    include "algorithm/location/initial_location/amcl.h"
// #    include "algorithm/location/mark_location/markMatch.h"
#    include "./impl/debugSave.hpp"
#    include "algorithm/map/secret_map/laserIO.h"
#    include "algorithm/map/sub_map/Submap.h"
#    include "algorithm/optimize/impl/curbRegistration.hpp"
#    include "algorithm/optimize/impl/laserRegistration.hpp"
#    include "algorithm/poseCheck/poseCheck.hpp"
#    include "algorithm/remove_moving/removeMoving.h"
#    include "algorithm/verify/impl/laserVerifyScore.hpp"
#    include "common/common_ex.h"
#    include "common/type/type_queue.h"
#    include "impl/LocationPoseTwistContainer.hpp"
#    include "tic_toc.h"
#    include "tool/fileTool/fileTool.h"
#    include <Eigen/Dense>
#    include <dirent.h>
#    include <mutex>
#    include <pcl/common/common.h>
#    include <pcl/filters/voxel_grid.h>
#    include <pcl/io/file_io.h>
#    include <pcl/search/impl/kdtree.hpp>
#    include <sys/stat.h>
#    include <sys/types.h>
#    include "../test/param.hpp"
#include "ros/package.h"
#include <pcl/common/transforms.h>
#include "common_msgs/sensorgps.h"

#    define USESUBMAP
#    define ZEROOPT

namespace wj_slam {
struct GPSInfo{
    common_msgs::sensorgps s_sensorgps;
    std::vector<double> s_vCurUTM;

};

template <typename C, typename M> class Location {
  public:
    typedef boost::shared_ptr<Location<C, M>> Ptr;

  private:
    typedef pcl::PointXYZI POSE;

    typedef KEYFRAME<C> KeyFrame;
    typedef typename KeyFrame::Ptr KeyFramePtr;

    typedef KEYFRAME<M> MapFrame;
    typedef typename MapFrame::Ptr MapFramePtr;

    typedef FEATURE_PAIR<C> KeyFrameFeature;
    typedef typename KeyFrameFeature::Ptr KeyFrameFeaturePtr;

    typedef FEATURE_PAIR<M> MapFrameFeature;
    typedef typename MapFrameFeature::Ptr MapFrameFeaturePtr;

    typedef pcl::PointCloud<M> PointCloudMap;
    typedef typename PointCloudMap::Ptr PointCloudMapPtr;

    typedef pcl::search::KdTree<M> KdTree;
    typedef typename pcl::search::KdTree<M>::Ptr KdTreePtr;

    typedef boost::function<void(s_PoseWithTwist&)> LT_OUTPUT_CB;
    typedef boost::function<bool(s_PoseWithTwist&, int)> WHEELOdom_CB;

    using PoseCheckPtr = typename PoseCheck::Ptr; /**< 位姿校验器指针别名 */
    /**
     * An enum type
     * 定位状态标志位
     */
    enum LocationStatus {
        STOP = -1,         /**< 定位错误或初始化 */
        CONTINUS_SLAM = 0, /**< SLAM连续定位 */
        INITITAL_SLAM,     /**< SLAM初始定位 */
        CONTINUS_MARK,     /**< 靶标连续定位 */
        INITITAL_MARK      /**< 靶标初始定位 */
    };
    // location模块自用结构体,用来模块内控制,此结构体一般保存Location模块初始化后不再改变参数
    /**
     * An struct type
     * 定位模块参数
     */
    struct Config
    {
      private:
        std::string Bool[2] = {"false", "true"};
        std::string PoseStatusStr[6] =
            {"Default", "InitialPose", "ContinuePose", "VirtualPose", "StopPose", "SettingPose"};
        std::string WorkModeStr[5] = {"StandByMode",
                                      "InitMapMode",
                                      "ContMapMode",
                                      "LocatMode",
                                      "UpdateMapMode"};

      public:
        WorkMode m_workMode;        /**< 当前工作模式 */
        bool m_bIsEnLoop;           /**< 是否使用回环检测及优化 */
        bool m_bOnlyLocatMode;      /**< 是否仅为定位模式 */
        bool m_bCopyLatestKeyFrame; /**< 是否丢弃帧至最新 */
        bool m_bIsEnAmcl;           /**< 是否使能Amcl功能 */
        bool m_bIsEnRM;             /**< 是否使能动态过滤功能 */
        bool m_bIsSendPC;           /**< 是否发送点云 */
        int m_iOldMapNum;           /**< 老地图个数,保存地图其实帧 */
        Config()
        {
            this->reset();
        }
        void reset()
        {
            m_workMode = WorkMode::StandByMode;
            m_bIsEnLoop = false;
            m_bOnlyLocatMode = false;
            m_bCopyLatestKeyFrame = false;
            m_bIsEnAmcl = false;
            m_bIsEnRM = false;
            m_bIsSendPC = false;
            m_iOldMapNum = 0;
        }
        void printf()
        {
            wjPrint(WJCOL_GREEN, "[Loc Param] WorkMode", WorkModeStr[int(m_workMode)]);
            wjPrint(WJCOL_GREEN, "[Loc Param] isEnLoop", Bool[m_bIsEnLoop]);
            wjPrint(WJCOL_GREEN, "[Loc Param] isLocatMode", Bool[m_bOnlyLocatMode]);
            wjPrint(WJCOL_GREEN, "[Loc Param] isLatestKeyFrame", Bool[m_bCopyLatestKeyFrame]);
            wjPrint(WJCOL_GREEN, "[Loc Param] isEnAmcl", Bool[m_bIsEnAmcl]);
            wjPrint(WJCOL_GREEN, "[Loc Param] isEnRM", Bool[m_bIsEnRM]);
            wjPrint(WJCOL_GREEN, "[Loc Param] isSendPC", Bool[m_bIsSendPC]);
            wjPrint(WJCOL_GREEN, "[Loc Param] OldMapNum", m_iOldMapNum);
        }
    };

  private:
    std::mutex c_mutexCumulativeMap_; /**< keyFrame锁 */
    SYSPARAM* c_stSysParam_;          /**< 参数结构体 */

#    pragma region "程序开关控制"
    bool c_bLocationRun_;     /**< location_模块开始工作 */
    bool c_bLocationRunOver_; /**< 一次location完成 */
    bool c_bHasInit;          /**< 第一帧初始化完成 */
    bool c_bShutDown_;        /**< location模块关闭 */
    bool c_bShutDownOver_;    /**< location模块关闭完成 */
    bool c_bManualPoseModel;  /**< 手动位姿模式  匹配参数放大 */
#    pragma endregion

    PointCloudMapPtr c_pcMergeMapCloud_;                     /**< Cumulative map */
    pcl::PointCloud<pcl::PointXYZ>::Ptr c_pcPredPoseBox_;    /**< 预测位姿允许框 */
    Queue<KeyFramePtr>& c_qKeyFrameOptToBe_;                 /**< 待匹配关键帧队列 */
    KeyFramePtr c_pKeyFrameOptToBe_ = nullptr;               /**< 待匹配关键帧 */
    pcl::PointCloud<POSE>::Ptr c_pcKeyPoseSearch_ = nullptr; /**< 用于搜索概率路径 */
    MapFramePtr c_pstLocalMap_ = nullptr;                    /**< 当前地图 */
    MapFramePtr c_pstLocaliVoxMap_ = nullptr;                /**< 当前iVox栅格地图 */
    KdTreePtr c_pKdtKeyFrame_ = nullptr;  /**< 关键帧kdtree地图，用于标签回退 */
    MapFramePtr c_pstWholeMap_ = nullptr; /**< 全部地图 */

    LT_OUTPUT_CB c_highPrecsPoseCb_;                 /**< 里程计位姿更新回调 */
    WHEELOdom_CB c_getWheelOdomCb_;                  /**< 获取轮式里程计预估位姿回调 */
    boost::function<void(int, int)> c_bakFeatureCb_; /**< 异常数据保存回调 */
    boost::function<void(KeyFramePtr, MapFramePtr)> c_vSendPC_; /**< 显示点云回调 */
    boost::function<void(std::vector<s_POSE6D>)> c_vSendPose_;  /**< 显示位姿回调 */
    boost::function<void(pcl::PointCloud<pcl::PointXYZ>::Ptr)> c_vSendBox_; /**< 显示预估框回调 */
    boost::function<std::vector<double>(const Eigen::Vector3d& bodyAxisPosition)> c_vEnu2UTM; /**<点云ENU坐标转换*/

    s_POSE6D c_stIncreaseOpt_;                        /**< slam匹配优化增量 */
    LocationPoseTwistContainer c_poseTwistContainer_; /**< 定位各位姿容器 */

    boost::shared_ptr<LaserRegistration<C, M>> c_pMatcher_ = nullptr; /**< 匹配器 */
    boost::shared_ptr<KfMapPair> c_pKfMapPairNewAdd_ = nullptr;       /**< KfMapPair */
    boost::shared_ptr<KeyFrameMap> c_pKeyFrameMap_ = nullptr;         /**< KeyFrameMap */
    boost::shared_ptr<Submap> c_pSubmap_ = nullptr;                   /**< Submap */
    // boost::shared_ptr<MarkMatch> c_pMarkMatcher_ = nullptr;            /**< 靶标匹配 */
    boost::shared_ptr<RemoveMoving<M>> c_pRMoving_; /**< 动态过滤模块 */
    // boost::shared_ptr<RemoveMoving<M>> c_pRemoveMover_ = nullptr; /**< 动态过滤 */
    // boost::shared_ptr<wanjilocal::Amcl2D<M, C>> c_pAMCLer_ = nullptr; /**< AMCL2D过滤 */
    // boost::shared_ptr<LoopGraph<M>> c_pLooper_ = nullptr;/**< 回环过滤 */
    boost::shared_ptr<LoopGraph<M>> c_pGraph_ = nullptr; /**< 回环因子图模块 */
    // boost::shared_ptr<wanjilocal::GlobalAmcl<M, C>> c_pGlobalAmcl_ = nullptr;/**< 全局AMCL过滤 */
    boost::shared_ptr<LaserIO<M, POSE>> c_pReadWriteMap_ = nullptr;      /**< 地图加解密 */
    boost::shared_ptr<CurbRegistration<C, M>> c_pCurbMatcher_ = nullptr; /**< 车道线\路沿优化 */
    boost::shared_ptr<MapProc<M>> c_pMapProcer_ = nullptr;               /**< 地图后处理 */
    boost::shared_ptr<IVox<3, M>> c_pIvoxGrid_ = nullptr;                /**< ivox栅格地图 */
    boost::shared_ptr<LaserVerifyScore<C, M>> c_pLaserVerify_ = nullptr; /**< 概率校验 */
    PoseCheckPtr c_pPoseCheck_;                                          /**< 位姿校验器 */

    vector<int> c_vCallBackIndex_;   /**< 子图ID队列 */
    int c_nManualPoseModelNum;       /**< 手动设定位姿后运行帧数 */
    u_int32_t c_uiScanIDCur_;        /**< 当前帧ID */
    u_int32_t c_uiScanIDLast_;       /**< 上一帧ID */
    timeMs c_nScanTimeCurr_;         /**< 当前帧时间戳 */
    timeMs c_nScanTimeLast_;         /**< 上一帧时间戳 */
    timeMs c_tsLastestLocatSysTime_; /**< 最新一次点云定位接收时间 */
    timeMs c_tsLasDataRecvTime_;     /**< 最新一次数据接收时间 */
    LocationStatus c_CurrLocStatus_; /**< 定位状态 */
    // STRUCT_FILTER_TARGET c_stCurrMark_;
    // STRUCT_FILTER_TARGET_LITE c_stMarkMatchInfo_;
    // MARK_SETS c_stMarkSet_;

    bool c_bIsKeyFrame_;               /**< 关键帧标志位 */
    bool c_bLoopHasDone_;              /**< 回环完成标志位 */
    float c_fJumpNum_;                 /**< 跳帧帧数 */
    Config c_sConfig_;                 /**< Location模块参数体 */
    float c_fAlignScore_;              /**< 配准匹配点数比例 */
    PoseCheckStatus c_PoseCheckStatus; /**< 位姿校验状态 */
    PoseCheckStatus c_PoseCheckNoClear; /**< 位姿校验状态不自动复位，须人为设定Pose */
    bool c_bMatchCurb_;                 /**< curb匹配结果 */
    bool c_bOccupyVerify_;              /**< 概率校验结果 */
    u_char c_ucMoveStatus;              /**< 移动状态 */
    GPSInfo c_sGPSInfo; /**<GPS位姿*/
    GPSInfo c_sGPSInfoLast; /**<GPS位姿*/
    GPSInfo c_sGPSInfoFirst;
    s_POSE6D c_GPSPose6D; /**<GPS转换的POSE*/
    bool c_isGPSUseful; /**GPS 可用状态位*/

    std::mutex c_mutexSavemap_; /**< 保存地图状态锁 */

    /**
     * @brief 参数重置
     *
     *
     */
    void paramReset_();
    /**
     * @brief 回环回调
     *
     * @param b_loopDone
     *
     */
    void loopCallback_(bool b_loopDone);
    /**
     * @brief 参数初始化
     *
     *
     */
    void paramInit_();
    /**
     * @brief 设置匹配输入点云
     *
     * @param p_pcIn 当前帧点云
     *
     */
    void setInput_(KeyFramePtr& p_pcIn);
    /**
     * @brief 设定手动标志位
     *
     *
     */
    void matchValueInit();
    /**
     * @brief 子图设定位姿
     *
     * @param p_poseCurr 当前帧位姿
     *
     */
    void submapSetPose_(s_POSE6D& p_poseCurr);
    /**
     * @brief 更新当前定位地图
     *
     * @param p_poseCurr 当前帧位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * 更新当前定位地图成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 更新当前定位地图失败
     *
     */
    bool renewLocalMap_(s_POSE6D& p_poseCurr);
    /**
     * @brief 设定定位地图
     *
     * @param p_poseCurr 当前帧位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * 设定定位地图成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 设定定位地图失败
     *
     */
    bool updateLocalMap_(s_POSE6D& p_poseCurr);
    /**
     * @brief 判断靶标是否足够
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 靶标足够
     * @code
     *
     * @endcode
     * @return [false] \n
     * 靶标不够
     *
     */
    bool hasEnoughMark_(void);
    /**
     * @brief 设置定位子图
     *
     * @param p_pcIn 定位子图
     *
     */
    void setTarget_(MapFramePtr& p_pcIn);
    /**
     * @brief 添加KF帧到地图容器
     *
     * @param p_pKeyFrame KF帧
     *
     */
    void addMapFrames_(KeyFramePtr& p_pKeyFrame);
    /**
     * @brief 更新定位位姿，更新位姿回调，更新状态
     *
     * @param p_pKeyFrame 当前帧点云
     *
     */
    void update_(KeyFramePtr& p_pKeyFrame);
    /**
     * @brief 更新定位位姿及状态
     *
     *
     */
    void renewPose_();
    /**
     * @brief SLAM位姿优化
     *
     * @param p_pKeyFrame 当前帧
     * @param p_stPoseOpt 优化位姿增量
     * @code
     *
     * @endcode
     * @return [true] \n
     * 优化成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 优化失败
     *
     */
    bool optimiz_(KeyFramePtr& p_pKeyFrame, s_POSE6D& p_stPoseOpt);
    /**
     * @brief 检查是否有新帧
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 有新帧
     * @code
     *
     * @endcode
     * @return [false] \n
     * 无新帧
     *
     */
    bool checkNewKeyFrames_();
    /**
     * @brief 清空关键帧容器
     *
     *
     */
    void clearKeyFramesAll_();
    /**
     * @brief 清理帧容器，只保留最新帧
     *
     *
     */
    void clearKeyFramesBuf_(void);
    /**
     * @brief 获取当前待匹配最新关键帧
     *
     * @code
     *
     * @endcode
     * @return [KeyFramePtr] \n
     * 关键帧
     *
     */
    KeyFramePtr getKeyFrame_(void)
    {
        if (c_sConfig_.m_bCopyLatestKeyFrame)
        {
            clearKeyFramesBuf_();
        }
        KeyFramePtr l_pKeyFrame;
        l_pKeyFrame = c_qKeyFrameOptToBe_.front();
        c_qKeyFrameOptToBe_.pop();
        return l_pKeyFrame;
    }
    /**
     * @brief 判断slam是否match成功
     *
     * @param p_pKeyFrame 当前帧点云
     * @code
     *
     * @endcode
     * @return [true] \n
     * 匹配成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 匹配失败
     *
     */
    bool matchSlam_(KeyFramePtr& p_pKeyFrame);
    /**
     * @brief slam连续定位
     *
     * @param p_pKeyFrame 当前帧点云
     * @code
     *
     * @endcode
     * @return [true] \n
     * 定位成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 定位失败
     *
     */
    bool continueLocationSlam_(KeyFramePtr& p_pKeyFrame);
    /**
     * @brief slam初始定位
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 定位成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 定位失败
     *
     */
    bool initialLocationSlam_(void);
    /**
     * @brief 更新定位位姿以及位姿状态
     *
     * @param p_stCurrPoseTwistRaw_ 当前帧定位位姿
     *
     */
    void updatePoseStatus_(s_PoseWithTwist& p_stCurrPoseTwistRaw_);
    /**
     * @brief 位姿融合
     *
     * @param p_stOdomPredPoseTwist 预估位姿
     * @param p_stSlamPoseTwist     当前帧定位位姿
     *
     */
    void poseFuse_(s_PoseWithTwist& p_stOdomPredPoseTwist, s_PoseWithTwist& p_stSlamPoseTwist);
    /**
     * @brief 位姿融合初始化
     *
     *
     */
    void initPoseFuse_();
    /**
     * @brief 分析位姿状态
     *
     * @param p_stPoseCurrSlam 当前帧定位位姿
     * @param p_stPosePrecOdom 当前帧预估位姿
     * @param p_iLastPostCheckStatus 定位状态
     * @param p_bHasPredPose 是否有预估位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * 分析成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 分析失败
     *
     */
    bool quickAnalyPoseStatus(s_PoseWithTwist& p_stPoseCurrSlam,
                              s_PoseWithTwist& p_stPosePrecOdom,
                              PoseCheckStatus& p_iLastPostCheckStatus,
                              bool p_bHasPredPose);
    /**
     * @brief 位姿校验状态
     *
     * @param p_stPoseCurrSlam 当前帧定位位姿
     * @param p_stPosePrecOdom 当前帧预估位姿
     * @param p_iLastPostCheckStatus 定位状态
     *
     */
    void getPoseCheckStatus_(s_PoseWithTwist& p_stPoseCurrSlam,
                             s_PoseWithTwist& p_stPosePrecOdom,
                             PoseCheckStatus& p_iLastPostCheckStatus);
    /**
     * @brief 是否处于不校验区域
     *
     * @param p_sCurrPose 当前帧定位位姿
     * @param p_vAreaList 非校验区域
     * @code
     *
     * @endcode
     * @return [true] \n
     * 处于不校验区域
     * @code
     *
     * @endcode
     * @return [false] \n
     * 不处于不校验区域
     *
     */
    bool belongNoCheckArea(s_POSE6D p_sCurrPose, std::vector<std::vector<float>>& p_vAreaList);
    /**
     * @brief 当前位姿是否处于设置区域
     *
     * @param p_sCurrPose 当前帧定位位姿
     * @param p_vArea 区域
     * @code
     *
     * @endcode
     * @return [true] \n
     * 当前位姿属于区域
     * @code
     *
     * @endcode
     * @return [false] \n
     * 当前位姿不属于区域
     *
     */
    bool belongArea(s_POSE6D p_sCurrPose, std::vector<float> p_vArea);
    /**
     * @brief 虚拟位姿行进距离时间检查
     *
     * @param p_stPose_Start 虚拟起始位姿
     * @param p_stPose_Now 虚拟当前位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * 虚拟位姿超过可行驶范围
     * @code
     *
     * @endcode
     * @return [false] \n
     * 虚拟位姿未超过可行驶范围
     *
     */
    bool checkVirtualMoveOverFar_(s_PoseWithTwist& p_stPose_Start, s_PoseWithTwist& p_stPose_Now);
    /**
     * @brief 保存pose信息
     *
     * @param p_sUsePose 当前使用位姿
     * @param p_sSlamPose 当前帧定位位姿
     * @param p_sPrePose 当前帧预估位姿
     * @param p_sCurbPose 当前帧curb位姿
     * @param p_sFilePath 文件路径
     *
     */
    void savePoseCheckInfo_(s_PoseWithTwist& p_sUsePose,
                            s_PoseWithTwist& p_sSlamPose,
                            s_PoseWithTwist& p_sPrePose,
                            s_PoseWithTwist& p_sCurbPose,
                            std::string p_sFilePath);
    /**
     * @brief 获取预估位姿+允许阈值 框点
     *
     * @param p_stPredPose 当前帧预估位姿
     * @param p_stPoseRes 允许误差
     * @param p_stBoxRes 位姿框允许误差
     * @param p_stBox 位姿框顶点
     *
     */
    void getPredValidPoseBox_(s_PoseWithTwist& p_stPredPose,
                              s_POSE6D& p_stPoseRes,
                              s_TWIST& p_stBoxRes,
                              pcl::PointCloud<pcl::PointXYZ>::Ptr& p_stBox);
    /**
     * @brief 速度朝向是否满足
     *
     * @param p_stNewTwist 当前帧定位速度
     * @param p_stLastTwist 当前帧预估速度
     * @param p_fThr 角度允许误差
     * @code
     *
     * @endcode
     * @return [true] \n
     * 速度朝向满足阈值
     * @code
     *
     * @endcode
     * @return [false] \n
     * 速度朝向不满足阈值
     *
     */
    bool twistDirectCheck(s_TWIST& p_stNewTwist, s_TWIST& p_stLastTwist, float p_fThr);
    /**
     * @brief 获取当前点云边界
     *
     * @param p_stBox 点云
     * @param l_pcMin 最小边界
     * @param l_pcMax 最大边界
     *
     */
    void getMinMax3D_(pcl::PointCloud<pcl::PointXYZ>::Ptr p_stBox,
                      pcl::PointXYZ& l_pcMin,
                      pcl::PointXYZ& l_pcMax);
    /**
     * @brief 位置是否满足允许误差
     *
     * @param p_stPoseNow 当前帧定位位姿
     * @param p_stPredPose 当前帧预估位姿
     * @param p_stPoseRes 速度允许偏差倍数
     * @param p_stBoxRes 预估位姿速度
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool poseCheck_(s_PoseWithTwist& p_stPoseNow,
                    s_PoseWithTwist& p_stPredPose,
                    s_POSE6D& p_stPoseRes,
                    s_TWIST& p_stBoxRes);
    /**
     * @brief 校验位姿变化是否满足阈值
     *
     * @param p_stPoseNow 当前帧定位位姿
     * @param p_stPosePred 当前帧预估位姿
     * @param p_stPoseDiff 位姿允许误差
     * @param p_stPoseMoveStatus 当前定位状态
     * @param p_strPrintf
     * @code
     *
     * @endcode
     * @return [true] \n
     * 变化满足阈值
     * @code
     *
     * @endcode
     * @return [false] \n
     * 变化不满足阈值
     *
     */
    bool poseTwistCheck(s_PoseWithTwist& p_stPoseNow,
                        s_PoseWithTwist& p_stPosePred,
                        s_PoseWithTwist& p_stPoseDiff,
                        s_PoseWithTwist& p_stPoseMoveStatus,
                        std::string p_strPrintf = "");
    /**
     * @brief 获取运动状态
     *
     * @param p_poseCurr 当前帧预估位姿
     * @param p_stPoseRes 运动状态阈值
     * @code
     *
     * @endcode
     * @return [u_char] \n
     * 运动状态
     *
     */
    u_char getMoveStatus(s_PoseWithTwist& p_poseCurr, s_PoseWithTwist& p_stPoseRes);
    /**
     * @brief 马路伢子车道线匹配
     *
     * @param p_pKeyFrame 当前帧点云
     * @param p_stPosePrec 当前帧预估位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * 匹配成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 匹配失败
     *
     */
    bool matchCurb_(KeyFramePtr& p_pKeyFrame, s_PoseWithTwist& p_stPosePrec);
    /**
     * @brief 雷达断开机制
     *
     * @param p_iDisConNum 持续断开次数
     *
     */
    void lidarDisconFunc(int& p_iDisConNum);
    /**
     * @brief 是否触发雷达断开机制
     *
     * @param p_iNowMs 当前时间
     * @param p_iLastLocalOver 上次定位结束时间
     * @code
     *
     * @endcode
     * @return [true] \n
     * 雷达断开
     * @code
     *
     * @endcode
     * @return [false] \n
     * 雷达未断开
     *
     */
    bool isLidarDisCon_(timeMs p_iNowMs, timeMs& p_iLastLocalOver);
    /**
     * @brief 获取某时刻下预估位姿 根据级别选择不同的预估
     *
     * @param p_outPred 当前帧预估位姿
     * @param p_iNowTimeMs 需要预估时间
     * @code
     *
     * @endcode
     * @return [true] \n
     * 预估成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 预估失败
     *
     */
    bool getPredPose_(s_PoseWithTwist& p_outPred, timeMs p_iNowTimeMs);
    /**
     * @brief 获取某时刻下SLAM预估位姿
     *
     * @param p_outPred 待填充预估位姿
     * @param p_iNowTimeMs 预估时间
     * @param p_lastSlamPrec 上次定位位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * 预估成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 预估失败
     *
     */
    bool getSlamPredPose(s_PoseWithTwist& p_outPred,
                         timeMs& p_iNowTimeMs,
                         s_PoseWithTwist& p_lastSlamPrec);
    /**
     * @brief 更新AGV位姿: lidar对齐至当前时刻并转移至AGV坐标系
     *
     * @param p_outLidarPos 输出位姿
     *
     */
    void updateAGVPose(s_PoseWithTwist& p_outLidarPos);
    /**
     * @brief 设置stop-flag 强制 AGV 停车
     *
     *
     */
    void stopAGVPose();
    /**
     * @brief 可视化PoseList
     *
     * @param p_lidarPos 当前帧定位位姿
     * @param p_agvPose AGV使用位姿
     * @param p_predPose 当前帧预估位姿
     * @param p_curbPose 当前帧curb位姿
     *
     */
    void
    viewPose(s_POSE6D p_lidarPos, s_POSE6D p_agvPose, s_POSE6D p_predPose, s_POSE6D p_curbPose);
    /**
     * @brief
     *
     * @param p_mark
     *
     */
    void changeToMarkSt_(boost::shared_ptr<pcl::PointCloud<C>> p_mark);
    /**
     * @brief 定位函数
     *
     * @param p_pKeyFrame 当前帧点云
     * @param p_locate 定位状态
     * @code
     *
     * @endcode
     * @return [true] \n
     * 定位成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 定位失败
     *
     */
    bool location_(KeyFramePtr& p_pKeyFrame, LocationStatus& p_locate);
    /**
     * @brief
     *
     *
     */
    void buildMarkMap_(void);
    /**
     * @brief 保存地图
     *
     *
     */
    void saveMap_();
    /**
     * @brief 保存位姿
     *
     * @param p_sCurrPose 当前帧位姿
     * @param p_sFilePath 路径
     *
     */
    void savePoseThread_(s_POSE6D& p_sCurrPose, std::string p_sFilePath);
    /**
     * @brief 保存位姿
     *
     * @param p_sCurrPose 当前帧位姿
     * @param p_sFilePath 路径
     *
     */
    void savePoseInFile_(s_POSE6D p_sCurrPose, std::string p_sFilePath);
    /**
     * @brief 保存位姿
     *
     * @param p_sCurrPose 当前帧位姿
     * @param p_iTimetamp 时间戳
     *
     */
    void savePoseInFile2_(s_POSE6D& p_sCurrPose, timeMs p_iTimetamp);
    /**
     * @brief 判断是否将关键帧添加至地图
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 更新地图
     * @code
     *
     * @endcode
     * @return [false] \n
     * 不更新地图
     *
     */
    bool isUpdateMap_();
    /**
     * @brief 根据工作模式,参数配置控制Location模块工作状态
     *
     *
     */
    void configUpdate_();
    /**
     * @brief 子图参数初始化
     *
     *
     */
    void submapUpdate_();
    /**
     * @brief 检测当前定位与早期位姿转移状态(带速度)
     *
     * @param p_poseBefore 上一帧位姿
     * @param p_poseCurr 当前帧位姿
     * @code
     *
     * @endcode
     * @return [u_char] \n
     * 移动状态
     *
     */
    u_char checkMoveStatus_(s_PoseWithTwist& p_poseBefore, s_PoseWithTwist& p_poseCurr);
    /**
     * @brief 根据工作模式对系统参数更新
     *
     *
     */
    void paramUpdate_(void);
    /**
     * @brief 加载地图后的参数更新
     *
     *
     */
    void mapParamUpdate_();
    /**
     * @brief 设置匹配参数
     *
     * @param p_stParam
     *
     */
    void setMarchParam_(s_MatcherConfig& p_stParam);
    /**
     * @brief 转移点云
     *
     * @param p_stPose 转移位姿
     * @param p_pKeyFrame 待转移点云
     *
     */
    void transPointCloud_(s_POSE6D& p_stPose, KeyFramePtr& p_pKeyFrame);
    /**
     * @brief 根据位姿状态/是否初始化等判断是否清空速度
     *
     * @param p_stLastPose 上一帧定位位姿
     * @param p_stCurrPose 当前帧定位位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * 清空
     * @code
     *
     * @endcode
     * @return [false] \n
     * 不清空
     *
     */
    bool isResetTwist_(const s_POSE6D& p_stLastPose, const s_POSE6D& p_stCurrPose);
    /**
     * @brief 计算概率校验是否通过
     *
     * @param p_fMatchNumPercent 当前帧匹配点数
     * @param p_fMatchOccupyScore 当前帧匹配概率
     * @param p_fPath 路径概率信息
     * @code
     *
     * @endcode
     * @return [true] \n
     * 校验成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 校验失败
     *
     */
    bool occupyVerifyResult_(float& p_fMatchNumPercent, float& p_fMatchOccupyScore, float& p_fPath);

    /**
     * @brief 获取概率校验结果
     *
     * @param p_keyFramePtr 当前帧点云
     * @param p_fMatchNumPercent 当前帧匹配点数
     * @param p_fMatchOccupyScore 当前帧匹配概率
     * @code
     *
     * @endcode
     * @return [true] \n
     * 校验通过
     * @code
     *
     * @endcode
     * @return [false] \n
     * 校验失败
     *
     */
    bool getOccupyVerify_(KeyFramePtr& p_keyFramePtr,
                          float& p_fMatchNumPercent,
                          float& p_fMatchOccupyScore);
    /**
     * @brief 概率校验
     *
     * @param p_keyFramePtr 当前帧
     * @param p_bOccupyVerify_ 校验标志位
     * @code
     *
     * @endcode
     * @return [true] \n
     * 校验成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 校验失败
     *
     */
    bool checkOccupyVerify_(KeyFramePtr& p_keyFramePtr, bool& p_bOccupyVerify_);
    /**
     * @brief 保存加密地图
     *
     * @tparam <PointT>
     * @param file_name 地图文件
     * @param cloud 点云
     * @code
     *
     * @endcode
     * @return [true] \n
     * 保存成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 保存失败
     *
     */
    template <typename PointT>
    bool writeBinary_(const std::string& file_name, const pcl::PointCloud<PointT>& cloud)
    {
        if (!cloud.size())
        {
            LOGFAE(WWARN, "地图保存异常 | 点云为空,未保存 [{}]", file_name);
            return false;
        }
        pcl::PCDWriter writer;
        writer.writeBinary(file_name, cloud);
        return true;
    };
    /**
     * @brief 建图模式下，初始化第一帧点云标签
     *
     * @param p_pKeyFrame 当前帧点云
     *
     */
    void initFrameLabel(KeyFramePtr& p_pKeyFrame);
    /**
     * @brief 建图中当前帧将0标签再次计算
     *
     * @param p_pKeyFrame 当前帧点云
     *
     */
    void backoutFrameLabel(KeyFramePtr& p_pKeyFrame);
    /**
     * @brief 去掉iVox标签为0、3的点
     *
     * @param p_pKeyFrame 当前帧点云
     *
     */
    void removeErrorLabelPoint(KeyFramePtr& p_pKeyFrame);
    /**
     * @brief 添加节点和因子到因子图
     * @note 此函数可能会导致地图和位姿修正
     * @return [true] [地图修正]
     * @return [false] [地图不变]
     */
    bool renewFactorGraph_();
    /**
     * @brief 动态过滤关键帧
     *
     * @param p_pFrame
     */
    void reMovingFrame_(MapFramePtr& p_pFrame);

  public:
    /***设置GPS位姿，用于添加GPS约束*/
    void setGPSInfo(const GPSInfo& gpsInfo);
    void setFirstGPSInfo(const GPSInfo& gpsInfo);
    /**
     * @brief 关闭location模块
     *
     *
     */
    void shutDown();
    /**
     * @brief start只负责启动各种模块，内部不负责初始化
     *
     *
     */
    void start();
    /**
     * @brief 设置靶标地图
     *
     * @param p_path 路径
     *
     */
    void setMarkMap(std::string p_path);
    /**
     * @brief 使能连续定位
     *
     *
     */
    void enableOnlyLocationMode();
    /**
     * @brief 关闭连续定位
     *
     *
     */
    void disableOnlyLocationMode();
    /**
     * @brief 设置工作模式
     *
     * @param p_workMode
     * @code
     *
     * @endcode
     * @return [true] \n
     * 设置模式成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 设置模式失败
     *
     */
    bool setWorkMode(int p_workMode);
    /**
     * @brief location模块运行函数
     *
     *
     */
    void run();
    /**
     * @brief 暂时停止location模块
     *
     *
     */
    void stop();
    /**
     * @brief 判断location是否停止成功
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 停止成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 停止失败
     *
     */
    bool isStop();
    /**
     * @brief 保存地图
     *
     *
     */
    void saveMap();
    /**
     * @brief Set the New Cumulative Scan object
     *
     * @param p_map
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool setNewCumulativeScan(PointCloudMapPtr p_map);
    /**
     * @brief Get the Cumulative Scan Cloud object
     *
     * @code
     *
     * @endcode
     * @return [boost::shared_ptr<pcl::PointCloud<M>>] \n
     * [details wirte here]
     *
     */
    boost::shared_ptr<pcl::PointCloud<M>> getCumulativeScanCloud();
    /**
     * @brief 定位模式下给子图设置地图与路径
     *
     * @param p_pose
     * @param p_map
     *
     */
    void setMap(pcl::PointCloud<POSE>::Ptr p_pose, MapFramePtr p_map);
    /**
     * @brief 加载地图
     *
     * @param l_sFilePath 文件路径
     * @param l_sMapHeader 文件名
     * @code
     *
     * @endcode
     * @return [true] \n
     * 加载成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 加载失败
     *
     */
    bool loadMap(std::string l_sFilePath, std::string l_sMapHeader);
    /**
     * @brief 检查地图文件
     *
     * @param p_sFilePath 文件路径
     * @param p_sMapHeader 文件头
     * @param p_feature 地图特征点云
     * @param p_pcVisible 地图allpc
     * @param p_Pose 路径
     * @code
     *
     * @endcode
     * @return [true] \n
     * 地图正常
     * @code
     *
     * @endcode
     * @return [false] \n
     * 地图异常
     *
     */
    bool checkReadMap(std::string p_sFilePath,
                      std::string p_sMapHeader,
                      std::vector<typename pcl::PointCloud<M>::Ptr>& p_feature,
                      std::vector<typename pcl::PointCloud<M>::Ptr>& p_pcVisible,
                      pcl::PointCloud<POSE>::Ptr p_Pose);

    /**
     * @brief 停止动态过滤
     *
     *
     */
    void stopRemoveMoving();
    /**
     * @brief 获取当前地图,在非initialMap模式时,为了显示已经建好的图,需要调用
     *
     * @code
     *
     * @endcode
     * @return [PointCloudMapPtr] \n
     * 地图指针
     *
     */
    PointCloudMapPtr getMap()
    {
        if (c_pstWholeMap_)
            return c_pstWholeMap_->m_pFeature->allPC;
        return nullptr;
    }
    /**
     * @brief 回调函数
     *
     * @tparam <T>
     * @param p_LTOutputCb
     *
     */
    template <typename T> void setLocationOutputCb(T p_LTOutputCb)
    {
        c_highPrecsPoseCb_ = LT_OUTPUT_CB(p_LTOutputCb);
    }
    /**
     * @brief 回调函数
     *
     * @code
     *
     * @endcode
     * @return [LT_OUTPUT_CB] \n
     * [details wirte here]
     *
     */
    LT_OUTPUT_CB getLocationOutputCb()
    {
        return c_highPrecsPoseCb_;
    }

    /*******************************
     * @function:
     * @description:
     * @param {p_keyframe} 关键帧queue
     * @param {p_mutex} 关键帧queue
     * @param {p_sendPos} 是否sendpos
     * @return {*}
     * @others: null
     *******************************/

    /**
     * @brief Construct a new Location object
     *
     * @param p_keyframe 关键帧队列
     * @param p_mutex 锁
     * @param p_highPrecsPoseCb 位姿回调
     * @param p_wheelOdomCb 轮式里程计回调
     * @param p_bakFeature 保存异常数据回调
     * @param p_sendPos 发送位姿显示回调
     * @param p_sendPC 发送点云显示回调
     * @param p_sendBox 发送预估框显示回调
     *
     */
    Location(Queue<KeyFramePtr>& p_keyframe,
             LT_OUTPUT_CB p_highPrecsPoseCb,
             WHEELOdom_CB p_wheelOdomCb,
             boost::function<void(int, int)> p_bakFeature = NULL,
             boost::function<void(std::vector<s_POSE6D>)> p_sendPos = NULL,
             boost::function<void(KeyFramePtr, MapFramePtr)> p_sendPC = NULL,
             boost::function<void(pcl::PointCloud<pcl::PointXYZ>::Ptr)> p_sendBox = NULL,
             boost::function<std::vector<double>(const Eigen::Vector3d& bodyAxisPosition)> p_enu2UTM = NULL);
    /**
     * @brief Destroy the Location object
     *
     *
     */
    ~Location();
};

}  // namespace wj_slam
#    ifdef WJSLAM_NO_PRECOMPILE
#        include "impl/location.hpp"
#    else
#        define WJSLAM_Location(T, P) template class wj_slam::Location<T, P>;
#    endif
#endif
