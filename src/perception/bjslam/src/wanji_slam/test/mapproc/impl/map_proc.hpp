/**
 * @file map_proc.hpp
 * <AUTHOR> Li
 * @brief 地图后处理类接口实现
 * @version 0.1
 * @date 2023-07-07
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "../map_proc.h"
namespace wj_slam {

template <typename P> void MapProc<P>::init_()
{
    // 平面为主点云的处理方法
    c_pAllCloudProcer_.reset(new CloudPostProc<P>());
    // 采样栅格大小默认为0.2m
    c_pAllCloudProcer_->setLeafSize(c_fAllCloudSize_);
    c_pAllCloudProcer_->setProcMethod(wj_cpostproc::PostProcMethod::Plane);

    // 平面点云的处理方法
    c_pPlaneCloudProcer_.reset(new CloudPostProc<P>());
    // 采样栅格大小默认为0.2m
    c_pPlaneCloudProcer_->setLeafSize(c_fPlaneCloudSize_);
    c_pPlaneCloudProcer_->setProcMethod(wj_cpostproc::PostProcMethod::Plane);

    // 边角点云处理方法
    c_pLineCloudProcer_.reset(new CloudPostProc<P>());
    // 采样栅格大小默认为0.1m
    c_pLineCloudProcer_->setLeafSize(c_fLineCLoudSize_);
    c_pLineCloudProcer_->setProcMethod(wj_cpostproc::PostProcMethod::VerticalLine);
}
template <typename P>
void MapProc<P>::process_(CloudPostProcPtr& p_postProcer,
                          PointCloudPtr& dataIn,
                          PointCloud& dataOut)
{
    p_postProcer->setInputCloud(dataIn);
    p_postProcer->filter(dataOut);
}
template <typename P> void MapProc<P>::gen2D_(CloudPostProcPtr& p_postProcer, PointCloud& dataOut)
{
    p_postProcer->filter2D(dataOut);
}

template <typename P> void MapProc<P>::make2D(PointCloudPtr& dataOut)
{
    gen2D_(c_pAllCloudProcer_, *dataOut);
}

template <typename P>
void MapProc<P>::colour(boost::shared_ptr<pcl::PointCloud<P>>& dataIn,
                        pcl::PointCloud<pcl::RGB>& dataOut)
{
    // 2D点云颜色处理类
    c_pColor2D_.reset(new CloudColor_PCA<P>());
    c_pColor2D_->setLeafSize(c_fPlaneCloudSize_ * 1.5);
    c_pColor2D_->setInputCloud(dataIn);
    c_pColor2D_->color(dataOut);
    // c_pColor2D_->image("2drgb_sample.png");
}

template <typename P> void MapProc<P>::setSampleSize(float p_fAllpc, float p_fPlane, float p_fLize)
{
    //栅格尺寸参数必须大于0
    if (p_fAllpc > 0.0f && p_fPlane > 0.0f && p_fLize > 0.0f)
    {
        c_fAllCloudSize_ = p_fAllpc;
        c_fPlaneCloudSize_ = p_fPlane;
        c_fLineCLoudSize_ = p_fLize;
    }
    else
    {
        std::cout << "[MapProc]:输入下采样参数为负数,已忽略" << std::endl;
    }
    // 平面为主点云的处理方法
    if (nullptr == c_pAllCloudProcer_)
        c_pAllCloudProcer_.reset(new CloudPostProc<P>());
    c_pAllCloudProcer_->setLeafSize(c_fAllCloudSize_);
    // 平面点云的处理方法
    if (nullptr == c_pPlaneCloudProcer_)
        c_pPlaneCloudProcer_.reset(new CloudPostProc<P>());
    c_pPlaneCloudProcer_->setLeafSize(c_fPlaneCloudSize_);

    // 边角点云处理方法
    if (nullptr == c_pLineCloudProcer_)
        c_pLineCloudProcer_.reset(new CloudPostProc<P>());
    c_pLineCloudProcer_->setLeafSize(c_fLineCLoudSize_);
}

template <typename P>
void MapProc<P>::process(boost::shared_ptr<KeyFrameFeature>& dataIn, KeyFrameFeature& dataOut)
{
    if (dataIn)
    {  // 处理三种点云
        process_(c_pAllCloudProcer_, dataIn->allPC, *(dataOut.allPC));
        process_(c_pPlaneCloudProcer_, dataIn->second, *(dataOut.second));
        process_(c_pLineCloudProcer_, dataIn->first, *(dataOut.first));
    }
    else
    {
        std::cout << "[MapProc]:输入待处理点云指针为空,请检查!" << std::endl;
    }
}
template <typename P>
void MapProc<P>::process(boost::shared_ptr<KeyFrame>& dataIn, KeyFrame& dataOut)
{
    if (dataIn)
    {
        process_(c_pAllCloudProcer_, dataIn->m_pFeature->allPC, *(dataOut.m_pFeature->allPC));
        process_(c_pPlaneCloudProcer_, dataIn->m_pFeature->second, *(dataOut.m_pFeature->second));
        process_(c_pLineCloudProcer_, dataIn->m_pFeature->first, *(dataOut.m_pFeature->first));
    }
    else
    {
        std::cout << "[MapProc]:输入待处理点云指针为空,请检查!" << std::endl;
    }
}
template <typename P>
void MapProc<P>::process(boost::shared_ptr<KeyFrame>& dataIn, KeyFrameFeature& dataOut)
{
    if (dataIn)
    {
        process_(c_pAllCloudProcer_, dataIn->m_pFeature->allPC, *(dataOut.allPC));
        process_(c_pPlaneCloudProcer_, dataIn->m_pFeature->second, *(dataOut.second));
        process_(c_pLineCloudProcer_, dataIn->m_pFeature->first, *(dataOut.first));
    }
    else
    {
        std::cout << "[MapProc]:输入待处理点云指针为空,请检查!" << std::endl;
    }
}
template <typename P>
void MapProc<P>::process(boost::shared_ptr<KeyFrameFeature>& dataIn, KeyFrame& dataOut)
{
    if (dataIn)
    {
        process_(c_pAllCloudProcer_, dataIn->allPC, *(dataOut.m_pFeature->allPC));
        process_(c_pPlaneCloudProcer_, dataIn->second, *(dataOut.m_pFeature->second));
        process_(c_pLineCloudProcer_, dataIn->first, *(dataOut.m_pFeature->first));
    }
    else
    {
        std::cout << "[MapProc]:输入待处理点云指针为空,请检查!" << std::endl;
    }
}
}  // namespace wj_slam
#define WJSLAM_MapProc(P) template class wj_slam::MapProc<P>;
