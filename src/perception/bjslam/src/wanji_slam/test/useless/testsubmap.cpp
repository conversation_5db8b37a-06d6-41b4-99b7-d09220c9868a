/*
 * @Author: your name
 * @Date: 2021-06-17 13:44:57
 * @LastEditTime: 2021-12-01 15:15:16
 * @LastEditors: <PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/test/testsubmap.cpp
 */
#include "algorithm/map/sub_map/submap.hpp"
#include "common/common_ex.h"
#include <cstring>
#include <mutex>
#include <pcl/io/pcd_io.h>
using namespace wj_slam;
int main(int argc, char const* argv[])
{
    std::vector<KEYFRAME<pcl::PointXYZ>::Ptr> c_vMapFrames_;  //已存关键帧地图
    // std::vector<KeyFrameFeaturePtr> c_vpcKeyFrames11_;               //已存关键帧地图
    pcl::PointCloud<pcl::PointXYZ>::Ptr c_pcKeyFramesPose_ = nullptr;  //已存关键帧地图
    c_pcKeyFramesPose_.reset(new pcl::PointCloud<pcl::PointXYZ>());
    std::mutex c_mutexKeyFrame_;                                                     // keyFrame锁
    boost::shared_ptr<SubMap<pcl::PointXYZ, pcl::PointXYZ>> c_pSubMaper_ = nullptr;  // subMap
    KEYFRAME<pcl::PointXYZ>::Ptr c_pstLocalMap_ = nullptr;                           //当前地图
    c_pstLocalMap_.reset(new KEYFRAME<pcl::PointXYZ>());
    c_pSubMaper_.reset(new SubMap<pcl::PointXYZ, pcl::PointXYZ>(
        c_pcKeyFramesPose_, c_vMapFrames_, c_mutexKeyFrame_, 15, 5, 0.1, 0.3));
    KEYFRAME<pcl::PointXYZ>::Ptr l_map(new KEYFRAME<pcl::PointXYZ>());
    // pcl::PCDReader reader;
    std::string path = "/home/<USER>/.ros";
    pcl::io::loadPCDFile<pcl::PointXYZ>(path + "/corner.pcd", *(l_map->m_pFeature->first));
    pcl::io::loadPCDFile<pcl::PointXYZ>(path + "/surf.pcd", *(l_map->m_pFeature->second));
    pcl::io::loadPCDFile<pcl::PointXYZ>(path + "/pose.pcd", *c_pcKeyFramesPose_);

    // reader.read<PointTypeMap>(path+"/corner.pcd",*(l_map->m_pFeature->first));
    // reader.read<PointTypeMap>(path+"/surf.pcd",*(l_map->m_pFeature->second));
    // reader.read<POSE>(path+"/pose.pcd",*c_pcKeyFramesPose_);
    c_pSubMaper_->wholeMapToKFMap(c_pcKeyFramesPose_, l_map, 10);
    int i = 0;
    while (1)
    {
        i++;
        // for(int i=0;i<c_pcKeyFramesPose_->size())
        {
            s_POSE6D pose;
            pose.setX(c_pcKeyFramesPose_->points[i].x);
            pose.setY(c_pcKeyFramesPose_->points[i].y);
            pose.setZ(0);
            c_pSubMaper_->setPose(pose.m_trans, Eigen::Quaterniond::Identity());
            std::cout << "pose x: " << c_pcKeyFramesPose_->points[i].x << std::endl;
            std::cout << "pose y: " << c_pcKeyFramesPose_->points[i].y << std::endl;
            c_pSubMaper_->transformPointer(c_pstLocalMap_);
            sleepMs(100);
        }
    }
    return 0;
}
