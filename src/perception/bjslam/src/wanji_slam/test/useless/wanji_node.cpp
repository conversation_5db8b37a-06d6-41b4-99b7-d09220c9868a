/*
 * @Author: your name
 * @Date: 2021-04-27 19:17:34
 * @LastEditTime: 2021-07-15 16:29:50
 * @LastEditors: zushuang
 * @Description: In User Settings Edit
 * @FilePath: /wanji_16_laser/src/wanji_slam/test/wanjislam.cpp
 */
/*
 * This file is part of lslidar_n301 driver.
 *
 * The driver is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * The driver is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with the driver.  If not, see <http://www.gnu.org/licenses/>.
 */

#include "algorithm/optimize/laserTransform.h"
#include "location/location.h"
#include "net_app/netApp.h"
#include "net_app/net_Message_Proc.h"
#include "odomerty/odometry.h"
#include "param.hpp"
#include "preproc/pre_proc.h"
#include "wanji_slam.h"

SYSPARAM c_sysParam_;
wj_unpack::s_Config c_LaserConfig_;

#pragma region "ros"

ros::Publisher outputCorn_;
ros::Publisher outputSurf_;
ros::Publisher outputPath_;
ros::Publisher outputPose_;
ros::Publisher outputPathOdom_;
ros::Publisher outputPathHPOdom_;
ros::Subscriber wanji_scan_;
ros::Subscriber workMode_;
#pragma endregion

std::queue<FEATURE_PAIR_PTR> c_featureQueue_;
std::queue<KEYFRAME_PTR> c_keyFramesQueue_;
PreProcess<PCurXYZHSV>::Ptr c_preProcess_;
Odom_Ptr c_odom_;
Location_Ptr c_location_;
int timenow;
double t1, t2;
std::mutex lockKeyframe_;
nav_msgs::Path c_pathMsg_;
nav_msgs::Path c_pathOdomMsg_;
nav_msgs::Path c_pathHPOdomMsg_;
LaserTransform<PCurXYZHSV> c_stCurPCTranser_;
void odomCallback(FEATURE_PAIR_PTR& p_fEPair)
{
    static bool init = false;
    if (!init)
    {
        timenow = p_fEPair->m_dTimestamp;
        init = true;
    }
    // wjPrint(COLOR_BLUE, "time : ", p_fEPair->m_dTimestamp - timenow);
    c_featureQueue_.push(p_fEPair);
    // std::thread showth(& show, this);
    // showth.detach();
}
void locationCallback(KEYFRAME_PTR p_keyFrame)
{
    c_keyFramesQueue_.push(p_keyFrame);
    // std::thread showth(& show, this);
    // showth.detach();
}
void locationOutputCb(s_PoseWithTwist& p_pose)
{
    c_odom_->renewPrecisionPose(p_pose);
}
template <typename PcType, typename MapType>
void showPC(boost::shared_ptr<KEYFRAME<PcType>> pc,
            boost::shared_ptr<KEYFRAME<MapType>> map = nullptr)
{
    // static bool hasSend = false;
    // std::cout << "getSub corn : " << outputCorn_.getNumSubscribers() << std::endl;
    // if (outputCorn_.getNumSubscribers())
    // {
    //     sensor_msgs::PointCloud2 msgPC;
    //     typename pcl::PointCloud<PcType>::Ptr outPC(new pcl::PointCloud<PcType>());
    //     int offset = 0;
    //     s_POSE6D pose = pc->m_Pose;
    //     // if (c_sysParam_.m_iWorkMode == 3)
    //         c_stCurPCTranser_.transformCloudPoints(
    //             pose.m_quat, pose.m_trans, pc->m_pFeature->allPC, outPC);
    //     // else
    //     //     *outPC = *pc->m_pFeature->allPC;

    //     pcl::toROSMsg(*outPC, msgPC);
    //     msgPC.header.frame_id = "world";
    //     msgPC.header.stamp = ros::Time::now();
    //     outputCorn_.publish(msgPC);
    //     pcl::PointCloud<PcType>().swap(*outPC);
    // }
    // std::cout << "getSub surf : " << outputSurf_.getNumSubscribers() << std::endl;
    // if (map && (outputSurf_.getNumSubscribers()))
    // {
    //     if (hasSend && (c_sysParam_.m_iWorkMode == 3))
    //         return;
    //     hasSend = true;
    //     sensor_msgs::PointCloud2 msgMap;
    //     typename pcl::PointCloud<MapType>::Ptr outMap(new pcl::PointCloud<MapType>());
    //     if (c_sysParam_.m_iWorkMode == 3)
    //         pcl::toROSMsg(*map->m_pFeature->allPC, msgMap);
    //     else
    //         pcl::toROSMsg(*map->m_pFeature->second, msgMap);
    //     msgMap.header.frame_id = "world";
    //     msgMap.header.stamp = ros::Time::now();
    //     outputSurf_.publish(msgMap);
    //     pcl::PointCloud<MapType>().swap(*outMap);
    // }
    // else
    // {
    //     hasSend = false;
    // }
    sensor_msgs::PointCloud2 msgPC;
    typename pcl::PointCloud<PcType>::Ptr outPC(new pcl::PointCloud<PcType>());
    pcl::PointCloud<pcl::PointXYZI>::Ptr outPCXYZI(new pcl::PointCloud<pcl::PointXYZI>());
    int offset = 0;
    *outPC = *pc->m_pFeature->first;
    offset = outPC->points.size();
    *outPC += *pc->m_pFeature->second;
    pcl::copyPointCloud(*outPC, *outPCXYZI);
    for (int i = 0; i < offset; i++)
    {
        outPCXYZI->points[i].intensity = 1;
    }
    pcl::toROSMsg(*outPCXYZI, msgPC);
    msgPC.header.frame_id = "world";
    msgPC.header.stamp = ros::Time::now();
    outputCorn_.publish(msgPC);
    if (map && outputSurf_.getNumSubscribers())
    {
        pcl::PointCloud<pcl::PointXYZI>().swap(*outPCXYZI);
        sensor_msgs::PointCloud2 msgMap;
        typename pcl::PointCloud<MapType>::Ptr outMap(new pcl::PointCloud<MapType>());
        // *outMap = *map->m_pFeature->first;
        // *outMap += *map->m_pFeature->second;
        *outMap = *map->m_pFeature->first;
        offset = outMap->points.size();
        *outMap += *map->m_pFeature->second;
        pcl::copyPointCloud(*outMap, *outPCXYZI);
        for (int i = 0; i < offset; i++)
        {
            outPCXYZI->points[i].intensity = 1;
        }
        pcl::toROSMsg(*outPCXYZI, msgMap);
        msgMap.header.frame_id = "world";
        msgMap.header.stamp = ros::Time::now();
        outputSurf_.publish(msgMap);
        pcl::PointCloud<MapType>().swap(*outMap);
    }
    pcl::PointCloud<PcType>().swap(*outPC);
    pcl::PointCloud<pcl::PointXYZI>().swap(*outPCXYZI);
}
template <typename PcType, typename MapType>
void pcCallback(boost::shared_ptr<KEYFRAME<PcType>> pc,
                boost::shared_ptr<KEYFRAME<MapType>> map = nullptr)
{
    std::thread showpc(&showPC<PcType, MapType>, pc, map);
    showpc.detach();
}

void poseCallback(std::vector<s_POSE6D> pose)
{
    geometry_msgs::PoseStamped lpose;
    lpose.header.frame_id = "world";
    lpose.header.stamp = ros::Time::now();
    lpose.pose.orientation.w = pose.begin()->m_quat.w();
    lpose.pose.orientation.x = pose.begin()->m_quat.x();
    lpose.pose.orientation.y = pose.begin()->m_quat.y();
    lpose.pose.orientation.z = pose.begin()->m_quat.z();
    lpose.pose.position.x = pose.begin()->m_trans.x();
    lpose.pose.position.y = pose.begin()->m_trans.y();
    lpose.pose.position.z = pose.begin()->m_trans.z();
    if (outputPose_.getNumSubscribers() > 0)
    {
        outputPose_.publish(lpose);
    }
    if (outputPath_.getNumSubscribers() > 0)
    {
        c_pathMsg_.header.frame_id = "world";
        c_pathMsg_.header.stamp = ros::Time::now();
        c_pathMsg_.poses.push_back(lpose);
        outputPath_.publish(c_pathMsg_);
    }
}
void poseCallbackOdom(std::vector<s_POSE6D> pose)
{
    geometry_msgs::PoseStamped lpose;
    lpose.header.frame_id = "world";
    lpose.header.stamp = ros::Time::now();
    lpose.pose.orientation.w = pose.begin()->m_quat.w();
    lpose.pose.orientation.x = pose.begin()->m_quat.x();
    lpose.pose.orientation.y = pose.begin()->m_quat.y();
    lpose.pose.orientation.z = pose.begin()->m_quat.z();
    lpose.pose.position.x = pose.begin()->m_trans.x();
    lpose.pose.position.y = pose.begin()->m_trans.y();
    lpose.pose.position.z = pose.begin()->m_trans.z();
    if (outputPathOdom_.getNumSubscribers() > 0)
    {
        c_pathOdomMsg_.header.frame_id = "world";
        c_pathOdomMsg_.header.stamp = ros::Time::now();
        c_pathOdomMsg_.poses.push_back(lpose);
        outputPathOdom_.publish(c_pathOdomMsg_);
    }
    lpose.header.frame_id = "world";
    lpose.header.stamp = ros::Time::now();
    lpose.pose.orientation.w = pose[1].m_quat.w();
    lpose.pose.orientation.x = pose[1].m_quat.x();
    lpose.pose.orientation.y = pose[1].m_quat.y();
    lpose.pose.orientation.z = pose[1].m_quat.z();
    lpose.pose.position.x = pose[1].m_trans.x();
    lpose.pose.position.y = pose[1].m_trans.y();
    lpose.pose.position.z = pose[1].m_trans.z();
    if (outputPathHPOdom_.getNumSubscribers() > 0)
    {
        c_pathHPOdomMsg_.header.frame_id = "world";
        c_pathHPOdomMsg_.header.stamp = ros::Time::now();
        c_pathHPOdomMsg_.poses.push_back(lpose);
        outputPathHPOdom_.publish(c_pathHPOdomMsg_);
    }
}

bool setWorkMode(void)
{
    bool res = c_odom_->setWorkMode(c_sysParam_.m_iWorkMode);
    res &= c_location_->setWorkMode(c_sysParam_.m_iWorkMode);
    return res;
}
void processScanRos(const wanji_msgs::WanjiScan::ConstPtr& p_pScanMsg)
// void processScanRos(const std_msgs::String::ConstPtr& p_pScanMsg)
{
    std::cout << "1" << std::endl;
    // conv_->processScan(p_pScanMsg);
}
int main(int argc, char** argv)
{
    ros::init(argc, argv, "wj_slam");
    ros::NodeHandle c_node_;

    // wj_slam::WanJiSLAM wjslam(node);
    Param l_param;
    std::string path = ros::package::getPath("wj_slam");
    l_param.loadSysParam(path);

    c_LaserConfig_ = l_param.getLaserConfig();
    c_sysParam_ = l_param.getSLAMConfig();
#pragma region "preProcess"
    c_preProcess_.reset(
        new PreProcess<PCurXYZHSV>(&c_LaserConfig_, c_sysParam_, boost::bind(&odomCallback, _1)));
#pragma endregion

#pragma region "odom"
    c_odom_.reset(new Odometry<PCurXYZHSV>(c_featureQueue_,
                                           boost::bind(&locationCallback, _1),
                                           c_sysParam_,
                                           offsetof(PCurXYZHSV, s),
                                           -1,
                                           boost::bind(&poseCallbackOdom, _1),
                                           NULL));
    // boost::bind(& poseCallback,   _1)
    // boost::bind(& pcCallback,   _1)
    std::thread odom(&Odometry<PCurXYZHSV>::run, c_odom_);
#pragma endregion

#pragma region "location"

    c_location_.reset(
        new Location<PCurXYZHSV, PMapXYZ>(c_keyFramesQueue_,
                                          lockKeyframe_,
                                          boost::bind(&locationOutputCb, _1),
                                          c_sysParam_,
                                          boost::bind(&poseCallback, _1),
                                          boost::bind(&pcCallback<PCurXYZHSV, PMapXYZ>, _1, _2)));
    // boost::bind(& poseCallback,   _1),
    // boost::bind(& pcCallback<PCurXYZHSV, PMapXYZ>,   _1, _2)

    std::thread location(&Location<PCurXYZHSV, PMapXYZ>::run, c_location_);

    // #pragma endregion

    // #pragma region "show"
    //     std::thread show(& show, this);
    // #pragma endregion

#pragma region "netApp"
    WJNetApp netApp(
        /* c_odom_, c_location_, */ boost::bind(&setWorkMode), (void*)&c_sysParam_);
    std::thread netAppTh(&WJNetApp::start, netApp);
#pragma endregion

#pragma region "webNet"
    NetMessagePro webNet(
        /* c_odom_, c_location_, */ boost::bind(&setWorkMode), c_node_, (void*)&c_sysParam_);
#pragma endregion

#pragma region "ros"
    outputCorn_ = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_corn", 10);
    outputSurf_ = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_surf", 10);
    outputPath_ = c_node_.advertise<nav_msgs::Path>("wanji_path", 10);
    outputPathOdom_ = c_node_.advertise<nav_msgs::Path>("wanji_odom", 10);
    outputPathHPOdom_ = c_node_.advertise<nav_msgs::Path>("wanji_HPodom", 10);
    outputPose_ = c_node_.advertise<geometry_msgs::PoseStamped>("wanji_pose", 10);
#ifdef PLAYROSBAG
    wanji_scan_ = c_node_.subscribe<wanji_msgs::WanjiScan>(
        "wanji_packets", 10, &PreProcess<PCurXYZHSV>::processScanRos, c_preProcess_);
    // wanji_scan_ = c_node_.subscribe<wanji_msgs::WanjiScan>(
    //     "wanji_packets", 10, &processScanRos);

    // wanji_scan_ = c_node_.subscribe<wanji_msgs::WanjiScan>(
    //     "wanji_packets", 10, &processScanRos);
#endif

    // #pragma endregion
    ros::spin();
    netAppTh.join();
    odom.join();
    location.join();
    return 0;
}
