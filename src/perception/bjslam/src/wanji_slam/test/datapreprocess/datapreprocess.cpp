#include "datapreprocess.h"


using namespace std;

namespace wj_slam { 

    DataPreprocess::DataPreprocess(ros::NodeHandle& node, const std::string& topicName, const std::string& laserName
        , typename DataPreprocess::FeatureCb feCallBack
        , boost::function<bool (const double& syncFrameStamp)> gpspreprocess,
        std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque
                   )
        :m_node(node), m_topicSubscribedName(topicName),m_laserName(laserName)
            ,c_feCallBack(feCallBack), c_gpspreprocess(gpspreprocess)
            // , c_gpsDeque(gpsDeque)
        {
         
         c_sysParam_ = SYSPARAM::getIn();
        
        for (int i = 0; i < 64; i++)
            c_pcRawOut[i].reset();
        l_pcMidOut.reset();

        m_pointsMatrix.resize(64);
        for (int i = 0; i < 64; i++)
        {
            m_pointsMatrix[i].resize(1800);
        }

        m_pointCloudPtr = boost::make_shared<sensor_msgs::PointCloud2>();
         // 点云回调函数
        m_topicSubscriber = m_node.subscribe<sensor_msgs::PointCloud2>(
            m_topicSubscribedName, 100, &DataPreprocess::callbackPointCloud, this);

    }

    DataPreprocess::~DataPreprocess(){
        m_pointCloudPtr = nullptr;
    }


    void DataPreprocess::callbackPointCloud(const sensor_msgs::PointCloud2ConstPtr& msgPtr){
        std::lock_guard<std::mutex> lock(m_dataMutex);
        m_pointCloudPtrQueue.push(msgPtr);
        std::cerr <<"INFO: push data in m_pointCloudPtrQueue, size = " << m_pointCloudPtrQueue.size() << std::endl;
    }

    void DataPreprocess::processPointCloud(std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque){
       
        while (ros::ok()) {  // 保持循环直到 ROS 关闭
            // std::this_thread::sleep_for(std::chrono::milliseconds(100));  // 如果队列为空，稍微延迟一下
            std::lock_guard<std::mutex> lock(m_dataMutex);
            if(m_pointCloudPtrQueue.empty()) {
                // std::cerr <<"WARN: m_pointCloudPtrQueue is empty1 " << std::endl;
                continue;
            }

            std::cout<<"gpsDeque.size = " << gpsDeque.size()<<", c_gpsDeque.size = " << c_gpsDeque.size()  << std::endl;
            std::cerr << "INFO: Entered processPointCloud...................." << std::endl;

            std::cout <<"INFO: m_pointCloudPtrQueue size = " << m_pointCloudPtrQueue.size() << std::endl;
            m_pointCloudPtr = m_pointCloudPtrQueue.front();
            

            if(m_laserName == "WLR720FCW"){
                processWanjiPointCloud(m_pointCloudPtr, gpsDeque);
            }
            else if(m_laserName == "VLP16"){
                processVLPPointCloud(m_pointCloudPtr, gpsDeque);
            }
            else{
                std::cerr <<"laser name error, laserName = " << m_laserName << std::endl;
            }

            m_pointCloudPtrQueue.pop();
            
        }
    }

    void DataPreprocess::processWanjiPointCloud(const sensor_msgs::PointCloud2ConstPtr& msgPtr, std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque){
        if (!gpsDeque.empty()){
            c_isProcessedGPS = c_gpspreprocess(msgPtr->header.stamp.toSec());
        }
        else{
            std::cout <<"INFO: gpsDeque is empty " << std::endl;
        }

        // 没有同步GPS数据，返回
        if(!c_isProcessedGPS){
            std::cout <<"INFO: c_isProcessedGPS is failed " << std::endl;
            return;
        }

        // std::cout <<"INFO: start processWanjiPointCloud " << std::endl;
        pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud_raw(new pcl::PointCloud<pcl::PointXYZI>);
        pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud(new pcl::PointCloud<pcl::PointXYZI>);
        std::vector<int> indexs;
        pcl::fromROSMsg(*msgPtr, *in_cloud_raw);
        pcl::removeNaNFromPointCloud(*in_cloud_raw, *in_cloud, indexs);
        memset(&m_pointsSetMatrix[0][0], false, sizeof(bool) * 64 * 1800); //TODO 根据lidar类型填充线数
        m_frameCountTemp++;
        for (int i = 0; i < in_cloud->points.size(); ++i)
        {
            auto& point = in_cloud->points[i];
            if ((point.x == 0 && point.y == 0 && point.z == 0))
                continue;
            if(abs(point.x) < 1.0 && abs(point.y) < 1.0)//去除牵引车附近的点
                continue;
            float distanceXY = sqrt(point.x * point.x + point.y * point.y); // 主lidar为原点，FLU坐标系
            float pitchDegree = atan2(point.z, distanceXY) * 180.0 / M_PI;
            float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI; // X轴负方向逆时针0~360度
            yawDegree += 90.0;
            if (yawDegree < 0)
                yawDegree += 360;
            if (std::isnan(pitchDegree) || angleToChannel.count(int(round(pitchDegree * 10))) == 0)
            {  // pitchDegree不存在于map中
                std::cout << " no pitch " << pitchDegree << ", " << int(round(pitchDegree * 10))
                          << ", " << angleToChannel.count(int(round(pitchDegree * 10)))
                          << std::endl;
                continue;
            }

            int lineblock = round(yawDegree / 0.2);
            int channel = angleToChannel[int(round(pitchDegree * 10))];  // 查找对应的通道序号
            m_pointsSetMatrix[64 - channel][lineblock] = true;
            m_pointsMatrix[64 - channel][lineblock] = point;
        }

        boost::shared_ptr<pcl::PointCloud<pcl::PointXYZHSV>> l_pcAll(
            new pcl::PointCloud<pcl::PointXYZHSV>());  // 参数5
        boost::shared_ptr<pcl::PointCloud<pcl::PointXYZI>> l_pcAllSend(
            new pcl::PointCloud<pcl::PointXYZI>());  // 参数5
        sTimeval l_PktFirstTime;
        l_PktFirstTime.second() = msgPtr->header.stamp.sec;
        l_PktFirstTime.subsecond() = msgPtr->header.stamp.nsec / 1000.0;

        if (!c_sysParam_->m_time.isSensorTimeSet())
        {
            c_sysParam_->m_time.setSensorBaseTime(l_PktFirstTime);
            LOGFAE(WINFO,
                   "{} 雷达 [{}] 帧 {} 授时, syncTime {}",
                   WJLog::getWholeSysTime(),
                   c_sysParam_->m_lidar[0].m_sLaserName,
                   m_frameCountTemp,
                   c_sysParam_->m_time.getSensorBaseTime().data());
        }
        double receiveTimestamp = c_sysParam_->m_time.getTimeNowMs();
        double timestamp = l_PktFirstTime.getDiffMs(c_sysParam_->m_time.getSensorBaseTime());
        // std::cout << "m_pointsMatrix.size() " << m_pointsMatrix.size() << std::endl;
        for (int i = 0; i < m_pointsMatrix.size(); i++)
        {
            auto& linePoints = m_pointsMatrix[i];
            // std::cout << "linePoints.size() " << linePoints.size() << std::endl;
            for (int j = 0; j < linePoints.size(); j++)
            {
                if (m_pointsSetMatrix[i][j] == false)
                    continue;
                auto& point = linePoints[j];
                float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI;
                yawDegree += 90.0;
                if (yawDegree < 0)
                    yawDegree += 360;
                // 将点放在正确的位置上
                PRaw pointRaw;
                pointRaw.x = point.x;
                pointRaw.y = point.y;
                pointRaw.z = point.z;

                PAdd pointAdd;
                pointAdd.intensity = point.intensity;  // TODO 修改驱动添加强度
                pointAdd.xydist = sqrt(point.x * point.x + point.y * point.y);
                pointAdd.depth = sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
                pointAdd.ang = yawDegree;
                pointAdd.time = 100 * yawDegree / 360.0;
                point.intensity = pointAdd.time;
                // std::cout << " i = " << i << ", k = " << j
                //           << ", x = " << point.x
                //           << ", y = " << point.y
                //           << ", z = " << point.z
                //           << ", intensity = " << point.intensity
                //           << ", pointAdd.xydist = " << pointAdd.xydist
                //           << ", pointAdd.depth = " << pointAdd.depth
                //           << ", pointAdd.ang = " << pointAdd.ang
                //           << ", pointAdd.time = " << pointAdd.time
                //           <<", c_pcRawOut[i].m_praw size = " << c_pcRawOut[i].m_praw->points.size()
                //           <<", c_pcRawOut[i].m_praw capacity = " << c_pcRawOut[i].m_praw->points.capacity()
                //           << std::endl;
                          
                l_pcAllSend->push_back(point);
                c_pcRawOut[i].m_praw->points.push_back(pointRaw);
                c_pcRawOut[i].m_padd->push_back(pointAdd);
            }
        }


        cout << "hsq start runFECallback: m_frameCountTemp = " << m_frameCountTemp << ", timestamp = " << std::to_string(timestamp)
             << endl;
        c_feCallBack(static_cast<uint32_t>(0),
                                     c_pcRawOut,
                                     l_pcMidOut,
                                     l_pcAll,
                                     timestamp,  // 每个雷达数据包内部所带的时间戳
                                     receiveTimestamp,  // wireshark获取到每个雷达数据包系统时间戳
                                     m_frameCountTemp);

        // std::cout << " runFECallback end " << std::endl;
        for (int i = 0; i < 64; i++)
            c_pcRawOut[i].reset();
        // showPointCloud<pcl::PointXYZI>(outputCurrTrans_, l_pcAllSend);
    }

    void DataPreprocess::processVLPPointCloud(const sensor_msgs::PointCloud2ConstPtr& msgPtr, std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque){
        // TODO
    }
    


}// namespace wj_slam

