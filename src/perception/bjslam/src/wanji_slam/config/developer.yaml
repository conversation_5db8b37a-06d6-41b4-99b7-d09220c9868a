#研发版参数配置文件
LaserConfig:
    ## 模式相关 ##
    work_mode: 0                  #工作模式 0:standby 1:initialmap 2:contiunemap 3:location
    param_mode: 0                 #建图参数模式 0:indoor 1:outdoor 2:vacrous 3:labor
    debugModel: true              #调试模式（改变输出）[true/false]
    XYMovingModel: false

    ## master相关 ##
    master:
        online_mode: true         #是否雷达在线模式
        default_save_pcap: true   #是否默认录制PCAP
        record_time: 180          #录制时间间隔3min
        maxNum_pcap: 30           #录制Lidar-PCAP最大个数
        maxNum_agvpcap: 30        #录制AGV-PCAP最大个数
        net_cfg_path: /etc/netplan/01-netcfg.yaml
        lidar_data_path: NOSET    #离线数据存放路径
        use_od: false             #是否启用避障功能

    ## 其他相关 ##
    send_map: true                #可视化地图
    send_point: true              #可视化雷达
    view_mode: 0                  #地图可视化模式

    ## Web相关 ##
    web_port: 10088               #Web端口

    ## 靶标相关 ##
    use_mark: false               #是否使用mark
    use_imu: true                 #是否使用imu
    enableLoop: true             #是否启动回环
    slam_model: 0                 #设置模式 0 slam 1 mark 2 mark+slam 3 mark+slam(未使用slam的角点)
    markWightMax: 20              #靶标权重最大值
    markWightMin: 3               #靶标权重最小值
    markRec_model: 0              #
    mark_size: 0.04               #靶标半径

    ## 雷达相关 ##
    lidarInfo:
        lidar_0:                            #主雷达
            laser_type: WLR720F             #雷达型号
            calibration_online: true        #是否在线读取表
            is_baseLaser: true              #是否为基础雷达
            laser_sn: NOSET                 #雷达SN码
            net_name: NOSET                 #网卡名
            calibration_file: NOSET         #存放偏心|垂直表 文件夹名(不同类型雷达可不同)
            laser_enable: true              #雷达使能
            use_multicast: false            #雷达组播功能
            pc_ip: ************             #目标IP
            pc_port: 3001                   #目标端口
            laser_ip: ************          #雷达IP
            laser_port: 3333                #雷达端口
            multicast_ip: **********        #雷达组播IP
            min_num: 1000                   #最小点数 低于此阈值 则认为包数不足
            min_pkt: 24                     #每帧点云pcap包允许解析的最小数量
            frame_sec: 109                  #雷达频率ms 超时检测
            maxFrame_sec: 300               #雷达中断数据有效阈值
            laser_rpm: 600                  #雷达转速
            laser_height: 1.0               #雷达安装高度
            dist_res: 0.0                   #雷达测距补偿值
            use_floor: true                 #是否使用z<0高度以下点云
            min_distance: 1.0               #雷达点云最近距离
            max_distance: 70.0              #雷达点云最远距离
            pcap_name: NOSET                #pcap包名称 不带后缀
            bag_name: NOSET                 #bag包名称
            curbLine: [0, 0, 0, 0, 0, 0]    #curb线号信息
            blindInfo:                      #盲区角度信息 逆时针0-360 
                blind0: [0,0]               #盲区1 [起始角, 终止角]
                blind1: [0,0]               #盲区2
            transToBase: [0,0,0,0,0,0]      #相对主雷达 位置信息x-y-z-roll-pithch-yaw
            DipAngle: [0, 0]                #地平校准结果
            Calibration: [0, 0, 0, 0, 0, 0] #标定结果
        lidar_1:                            #辅雷达
            laser_type: WLR720F             #雷达型号
            calibration_online: true        #是否在线读取表
            is_baseLaser: false             #是否为基础雷达
            laser_sn: NOSET                 #雷达SN码
            net_name: NOSET                 #网卡名
            calibration_file: NOSET         #存放偏心|垂直表 文件夹名(不同类型雷达可不同)
            laser_enable: true              #雷达使能
            use_multicast: false            #雷达组播功能
            pc_ip: ************             #目标IP
            pc_port: 3002                   #目标端口
            laser_ip: ************          #雷达IP
            laser_port: 3333                #雷达端口
            multicast_ip: **********        #雷达组播IP
            min_num: 1000                   #最小点数 低于此阈值 则认为包数不足
            min_pkt: 24                     #每帧点云pcap包允许解析的最小数量
            frame_sec: 109                  #雷达频率ms 超时检测
            maxFrame_sec: 300               #雷达中断数据有效阈值
            laser_rpm: 600                  #雷达转速
            laser_height: 1.0               #雷达安装高度
            dist_res: 0.0                   #雷达测距补偿值
            use_floor: true                 #是否使用地面
            min_distance: 1.0               #雷达最近距离
            max_distance: 70.0              #雷达最远距离
            pcap_name: NOSET                #pcap包名称 不带后缀
            bag_name: NOSET                 #bag包名称
            curbLine: [0, 0, 0, 0, 0, 0]    #curb线号信息
            blindInfo:                      #盲区信息
                blind0: [0,0]               #盲区1
                blind1: [0,0]               #盲区2
            transToBase: [0,0,0,0,0,0]      #相对主雷达 位置信息x-y-z-roll-pithch-yaw
            DipAngle: [0, 0]                #地平校准结果
            Calibration: [0, 0, 0, 0, 0, 0] #标定结果

    ## 标定相关 ##
    LidarCalib: 
        init_Time: 10000                    #初始化的最短时间限制[ms]
        init_ReInitTime: 1000               #初始化的最大抖动容忍时间限制[ms]
        init_MaxVelocity: 0.05              #判为静止的最大速度[m/100ms]
        init_MaxStdDev: [0.05, 0.5]         #判为静止的最大位置标准差[m, deg]  
        calib_MaxVelocity: 0.1              #静态标定容忍抖动速度阈值(m/s)
        calib_MaxRotSpeed: 5                #静态标定容忍转速最大阈值(deg/s)
        calib_MaxAccel: 0.5                 #用于匀速判定的速度差异(m/s^2)
        calib_MinChangeDis: 1               #距离上次静态标定时的最小移动距离(m)
        calib_MinChangeAng: 30              #距离上次静态标定位置的最小变化角度
    ## agv相关 ##
    agv:
        server_ip: ************             #服务器IP
        server_port: 2112                   #服务器端口
        pose_valid_time: 205.0              #位姿有效时间阈值
        pcap_name: NOSET                    #agv数据包名称
        net_name: NOSET                     #网卡名称
        poseAlign: true                     #预估位姿是否对齐至发送时刻
        lidarToAgv: [0, 0, 0, 0, 0, 0]      #车体校正参数
        my_map: [0, 0, 0]
        to_map: [0, 0, 0]
        wheelToLidar: [0, 0, 0, 0, 0, 0]    #车体校正参数 
        wheelTwistMax: [0.2, 0, 0, 0, 0, 0] #轮式最大速度
        wheelTwistMin: [0, 0, 0, 0, 0, 0]   #轮式最小速度
        wheelFilter: true                   #轮式里程计滤波器开关
        wheelFilter_ProcessPosN: 0.1        #轮式里程计过程噪声 [m/100ms]
        wheelFilter_ProcessRotN: 0.1        #轮式里程计过程噪声 [deg/100ms]
        wheelFilter_MeasurePosN: 0.02       #轮式里程计测量噪声 [m/100ms]
        wheelFilter_MeasureRotN: 0.05       #轮式里程计测量噪声 [deg/100ms]

    ## Pos校验相关 ##
    posVerify:
        maxNum_bak: 20                          #设置备份雷达文件夹数量
        frameBak: 100                           #设置备份雷达帧数
        pred_valid_time: 300                    #设置预估Pose有效时间
        poseCheck: false                        #设置是否启动位姿校验 即 是否使用轮式里程计等外部融合速度
        testWheel: false                        #设置纯里程计模式
        useFuse: false                          #设置使用融合位姿
        safeModel: false                        #设置安全模式 - slam定位失败后是否可无限使用里程计模式
        useCurb: false                          #设置是否使用车道线+马路伢子+中间线
        useOdom: false                          #设置里程计校验
        useLoct: false                          #设置定位校验
        testCurb: false                         #设置是否启动测试车道线+马路伢子+中间线匹配情况
        usePoseCheck: false                     #设置使用位姿对比
        wheelFarDist: 30.0                      #设置预估Pos下AGV最远行进距离
        wheelFarTime: 30.0                      #设置预估Pos下AGV最长运行时间
        matchKdNumPer: 0.1                      #匹配点数比例阈值
        openOccupyVerify: false                 #是否开启概率校验
        useOccupyVerify: false                  #是否使用概率恢复
        occVerifyBypassThd: 10.0                #概率校验恢复帧数
        matchNumPer: 0.3                        #概率校验匹配点数比例
        matchOccupyScore: 0.3                   #概率校验得分
        verifyGauss: 0.2                        #概率校验高斯分布系数
        move_res: [0.05, 0.03, 0, 0, 0, 1.0]    #运动状态阈值
        pos_vel: [0.5, 0.3, 0, 0, 0, 0.999]     #slamPose校验阈值 twist
        pos_res: [0.03, 0.03, 0, 0, 0, 2]       #slamPose校验阈值 pose
        cbpos_res: [0.1, 0, 0, 0, 0, 0]         #马路伢子优化位姿校验 pose
        cbpos_vel: [0, 0, 0, 0, 0, 0]           #马路伢子优化位姿校验 pose

    ## 时间相关 ##
    time:
        TimeSource: 0                           #设置使用何种时钟源

    ## 地图相关 ##
    map:
        map_name: NOSET                         #地图名 SLAM保存 / Locate 加载
        map_grid: 0.1                           #可视化地图下采样栅格（暂时无效参数）[m]
        map_size: 10.0                          #建图：子图加载的路径点范围半径[m]
        map_EuclideanDis: 10.0                  #子图中生成path欧式距离[m]
        map_pathGrid: 8.0                       #建图&定位：子图路径点的采样栅格[m]
        map_maxRange: 30.0                      #建图&定位：子图加载的路径点最大延伸距离[m]
        map_pathRange: 30.0                     #定位：每个子图路径点的加载点云半径[m]
        map_TimeStep: 9                         #建图：建图触发频率（距离步长）[100ms]
        map_DistStep: 0.5                       #建图：建图触发频率（距离步长）[m]
        map_AnglStep: 180                       #建图：建图触发频率（角度步长）[deg]
        grid_outResolu: 0.5                     #iVox大栅格尺寸
        grid_inResolu: 0.1                      #iVox大栅格内部小栅格尺寸 & 地图后处理的面点下采样尺寸
        grid_searchNum: 10                      #iVox搜索栅格内部点数
        grid_matchType: 3                       #栅格周围环绕类型 0:中心一个栅格 1:周围环绕6个栅格 2:周围环绕18个栅格 3:周围环绕26个栅格
        grid_Size: 1000000                      #栅格预先开辟数量
        grid_groundHigh: 0.4                    #iVox双层墙计算中判定地面点的高度阈值
        grid_roofHigh: 1.2                      #iVox双层墙计算中判定棚顶点的高度阈值 示例：墙面-lidar距离地面高度
        grid_optimizeModel: 1                   #匹配模块使用Kdtree还是iVox 0:Kdtree 1:iVox
        grid_filterZValue: 0.5                  #双层墙计算中用于过滤搜到的墙点阈值
        grid_corSamplSize: 0.8                  #iVox模式下submap角点下采样尺寸
        grid_curbSamplSize: 0.8                 #iVox模式下submapcurb点下采样尺寸
        grid_surSamplSize: 0.2                  #iVox模式下submap面点下采样尺寸
        grid_pathSamplSize: 0.9                 #iVox模式下submap路径点下采样尺寸
        map_allPcSize: 0.2                      #地图后处理中所有点云的下采样尺寸
        map_linePcSize: 0.1                     #地图后处理中角点点云的下采样尺寸
        map_surfPcSize: 0.2                     #地图后处理中面点点云的下采样尺寸
        map_proc: true                          #地图后处理开关
        ifSaveKF: false                         #逐帧保存KF开关

    ## 位置相关 ##
    laserPose_x: 0.0                            #初始位姿X 单位m
    laserPose_y: 0.0                            #初始位姿Y 单位m
    laserPose_z: 0.0                            #初始位姿Z 单位m
    laserPose_r: 0.0                            #初始位姿roll 单位deg
    laserPose_p: 0.0                            #初始位姿pitch 单位deg
    laserPose_a: 0.0                            #初始位姿yaw 单位deg
    savePath: false                             #是否保存路径

    ## 预处理相关 ##
    prep:
        FEXT:
            using_type: [-1, 0, 0, 0, 0, 0, 0, 0]             #启用特征类型[一般~，小~，边沿~，圆弧~，稀疏~、地面~、背景~、杂乱~]
            bondary_Angle: 10                                 #判为边界拖尾点最大角度[deg]
            L_Angle: [45, 135]                                #判为角点的角度范围[min,max][deg]
            P_Angle: 165                                      #判为面点的角度范围[min,180][deg]
            smallThing_Scale: [0.05, 0.20]                    #判为小物体的物体尺寸范围[min,max][m]
            feature_HeightRange: [-999, 999]                  #允许的特征点提取Z-axis边界[min,max][m]
            L_DistRange: [0.5, 42.0]                          #角点的允许距离范围[min,max][m]
            ground_SpaceHeight: 0.3                           #基于雷达高度判定的低空区间
            intenSmooth_check: true                           #是否检查强度平滑
            LineSmooth_check: true                            #是否检查角度平滑
            intenSmooth_diff: 6                               #强度检查的阈值[0~255]
            lineSmooth_diff: 100.0                            #角度检查的阈值[deg]
            intenSmooth_perct: 0.5                            #强度检查的阈值[比例]
            lineSmooth_perct: 0.5                             #角度检查的阈值[比例]
            L_verticFilter: true                              #是否检查角点的垂直圆柱性质
            P_uniSample: true                                 #是否对面点进行均匀采样
            P_uniSample_maxPoints: 3000                       #面点数量预期
            P_uniSample_range: [8.0, 16.0, 24.0]              #面点采样区间[区间1，区间2，区间3][m]
            P_uniSample_scale: [0.16, 0.09, 0.04, 0.01, 0.36] #面点采样区间内的采样尺度[区间1，区间2，区间3，更远区间，地面][m^2]
            L_verticFilter_radius: 0.3                        #角点垂直圆柱半径
            L_verticFilter_height: 0.3                        #角点垂直圆柱最小高度
            L_verticFilter_miPoints: 2                        #角点垂直圆柱最少点数
            curb_height: 0.15                                 #马路伢子高度
            axis_ang: 90.0                                    #轴角差-车前进方向与轴角(单雷达为X轴，多雷达视点云拼接情况而定)的夹角,逆时针为正,0-360
            ground_height: 99.9                               #离地高度-单雷达为雷达安装高度，多雷达视点云拼接情况而定
        Sync:
            MaxTimeBehind_ms: -55                             #最大允许落后时间
            MaxTimeSync_ms: 100.0                             #最大允许同步时间
            MaxTimeDelay_ms: 200000000                        #最大允许发送延时时间
        VisualCloudMode: 1                                    #使用全部点云-0 滤波点云-1 采样点云-2

    ## 里程计相关 ##
    odom:
        XYMoveMode: false
        imuQuatPrec: 0.99
        match:
            PMaxPoints: 10                  #设置面点k近邻搜索
            PMaxRadius: 3.5                 #设置面点搜索到的N点的最大距离差[m]
            PMeanDiff: 0.15                 #设置面点平均误差阈值
            LMinPoints: 3                   #设置角点2D匹配最小点数
            LMaxRadius: 0.3                 #设置角点2D搜索半径[m]
            LMaxZDiff: 2.0                  #设置角点2D匹配有效高差范围[m]
            maxDist: 0.3                    #设置点到线、面距离阈值[m]
            onlySample: true                #设置稀疏化
            Pvalid: [2, 0.995]              #设置面点是否平面阈值

    ## 定位相关 ##
    locate:
        XYMoveMode: false
        match:
            PMaxPoints: 10                  #设置面点k近邻搜索
            PMaxRadius: 3.0                 #设置面点搜索到的N点的最大距离差[m]
            PMeanDiff: 0.15                 #设置面点平均误差阈值
            Pvalid: [2, 0.995]              #设置面点是否平面阈值
            LMinPoints: 4                   #设置角点2D匹配最小点数
            LMaxRadius: 0.4                 #设置角点2D搜索半径[m]
            LMaxZDiff: 1.0                  #设置角点2D匹配有效高差范围[m]
            maxDist: 0.8                    #设置点到线、面距离阈值[m]
            onlySample: false               #设置稀疏化
        manual:
            PMaxPoints: 15                  #设置面点k近邻搜索
            PMaxRadius: 10.0                #设置面点搜索到的N点的最大距离差[m]
            PMeanDiff: 0.15                 #设置面点平均误差阈值
            Pvalid: [2, 0.995]              #设置面点是否平面阈值
            LMinPoints: 3                   #设置角点2D匹配最小点数
            LMaxRadius: 0.6                 #设置角点2D搜索半径[m]
            LMaxZDiff: 10.0                 #设置角点2D匹配有效高差范围[m]
            maxDist: 1.0                    #设置点到线、面距离阈值[m]
            onlySample: false               #设置稀疏化

    ## 建图相关 ##
    mapping:
        XYMoveMode: false                   #优化xy平面 不优化z r p
        match_0:
            PMaxPoints: 12                  #设置面点k近邻搜索
            PMaxRadius: 3.5                 #设置面点搜索到的N点的最大距离差[m]
            PMeanDiff: 0.15                 #设置面点平均误差阈值
            Pvalid: [2, 0.995]              #设置面点是否平面阈值
            LMinPoints: 4                   #设置角点2D匹配最小点数
            LMaxRadius: 0.2                 #设置角点2D搜索半径[m]
            LMaxZDiff: 2.0                  #设置角点2D匹配有效高差范围[m]
            maxDist: 0.8                    #设置点到线、面距离阈值[m]
            onlySample: true                #设置稀疏化
        SCSector: 60                        #SC提取sector
        SCRing: 20                          #SC提取ring
        SCRadius: 50                        #SC提取半径
        SCRecongnizeThr: 0.75               #回环粗匹配SC阈值
        LoopOptTimes: 15                    #回环匹配次数
        LoopMaxAveDistance: 0.065           #回环检测面点匹配平均距离最大阈值[m],超出阈值，不认为回环
        LoopMinMatchPercent: 0.35           #回环检测匹配最小比例
        LoopVerifyScoreThr: 0.7             #回环检测概率校验匹配最小得分比例
        LoopVerifyNumThr: 0.65               #回环检测概率校验匹配最小匹配比例
        FilterRangeX: 1.5                   #局部AMCLX方向范围[m]
        FilterRangeY: 1.5                   #局部AMCLY方向范围[m]
        FilterRangeZ: 0.02                  #局部AMCLZ方向范围[m]
        FilterRangeA: 8                     #局部AMCL角度范围[m度]
        ParticleNums: 300                  #局部AMCL粒子数量
        AMCLFilterTimes: 0                  #局部AMCL粒子滤波次数
    
    ## 日志相关 ##
    log:
        log_level: 0                        #手动日志等级 0:自动 1~5:ERROR-WARN-INFO-DEBUG-TRACE
        log_printf: false                   #是否打印详细信息
        log_printf_time: false              #是否打印时间流日志
        log_printf_check: false             #是否打印校验日志
        save_error_local: false             #是否保存异常定位雷达数据
        log_size: 10                        #日志文件大小，超过则新建[MB]
        log_path: NOSET                     #日志路径