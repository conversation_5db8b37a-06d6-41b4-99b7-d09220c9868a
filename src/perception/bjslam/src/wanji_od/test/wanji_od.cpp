/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-04-27 19:17:34
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 16:47:15
 */

#include "obstacleDetect.h"
#include <ros/ros.h>
int main(int argc, char** argv)
{
    ros::init(argc, argv, "wj_od");
    ros::NodeHandle node;
    ros::Rate loop_rate(1000);
    if (ros::ok())
    {
        openWanjiOD(node);
        while (ros::ok())
        {
            ros::spinOnce();
            loop_rate.sleep();
        }
        std::cout << "ros no ok!" << std::endl;
    }
    return 0;
}