/*
 * @Author: senlin <EMAIL>
 * @Date: 2022-08-15 13:25:05
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 08:32:04
 * @FilePath: /src/wanji_od/test/wanji_od.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "obstacleDetect.h"
#include "param.hpp"
#include "tic_toc.h"
#include "wanji_od.h"
#include <pcl/io/pcd_io.h>
#include <signal.h>
#include <termio.h>

namespace wj_od {
// 日志头

class WanJiOD {
  private:
    wj_slam::SYSPARAMOD* c_sysParam_;
    ros::NodeHandle& c_node_;

#pragma region "ros"
    ros::NodeHandle node;
    ros::Publisher outputCurr_;     /**< 当帧原始点云*/
    ros::Publisher outputObstacle_; /**< 当帧障碍点云*/
    ros::Publisher outputArea1_;    /**< 区域1的防护距离点*/
    ros::Publisher outputArea2_;    /**< 区域2的防护距离点*/
    ros::Publisher outputArea3_;    /**< 区域3的防护距离点*/
    ros::Subscriber wanji_scan_;
#pragma endregion

#pragma region "net"
    uint32_t c_iServerPort_ = 2112; /**< 当前服务器端口，用于和上位机交互*/
#pragma endregion

#pragma region "coordSolution"
    int c_iAreaNum_ = 0; /**< 该组区域数量 */
    int c_iGroupID_ = 5; /**< 当前区域主ID 默认是从区域组1开始 如果当前组不存在会报错 */
    int c_iAreaID_ = 0;                         /**< 区域副ID */
    bool c_bAreaAddFlag_;                       /**< 区域新增标志 新增以组为单位*/
    bool c_bAreaChangeFlag_;                    /**< 区域切换标志  切换以组为单位*/
    bool c_bStopMultiDetect_ = false;           /**< 停止多帧检测*/
    bool c_bMultiDetectOver_ = true;            /**< 多帧检测完成*/
    std::vector<int> c_vAreaChangeId_;          /**< 区域切换ID*/
    std::vector<int> c_vAreaAddId_;             /**< 区域新增ID*/
    GroupScaleMap c_sGroupScaleMap_;            /**< 16组区域的刻度表Map*/
    std::vector<PcXYZIPtr> c_allProtectVertex_; /**< 防护距离端点*/

#pragma endregion

#pragma region "roiDetect"
    std::mutex c_lock_;
    std::mutex c_roiLock_;
    bool c_warnCallback_ = false;
    bool c_bAreaDisplayInit_ = false;
    std::vector<bool> c_areaDetectFlag_;   /**< 区域检测标志位*/
    std::vector<int> c_areaWarnFlag_;      /**< 区域报警标志位*/
    areaPcPtr c_allAreaLastPc_;            /**<各区域上一帧障碍点矩阵 */
    std::vector<int> c_areaDet_;           /**<各区域当前多帧检测开启时间*/
    std::queue<areaPcPtr> c_allAreaCurPc_; /**< 各区域当帧障碍点矩阵队列*/
    boolVector c_areaMultiFrameSwitch_;    /**< 各区域多帧检测开关*/
    std::string c_areaName_[3] = {"Danger", "Warning1", "Warning2"}; /**< 检测区域等级*/
#pragma endregion

  public:
    WanJiOD(ros::NodeHandle& node,
            std::vector<wj_slam::s_LidarConfig>& p_mLidarCfg,
            sTimeval& p_sTimeProcStart);
    ~WanJiOD()
    {
        c_wjNetApp_ = nullptr;
        c_wjNetAppWarn_ = nullptr;
        c_roiDetect_ = nullptr;
        c_preProcess_ = nullptr;
        c_coordSolution_ = nullptr;
    }

    WJNetAppOD::Ptr c_wjNetApp_;             /**< 网络模块（与上位机交互）*/
    WJNetAppOD::Ptr c_wjNetAppWarn_;         /**< 网络模块（与小车交互）*/
    RoiDetect::Ptr c_roiDetect_;             /**< 区域检测模块*/
    CoordSolution::Ptr c_coordSolution_;     /**< 区域解算模块*/
    PreProcess<PCurXYZI>::Ptr c_preProcess_; /**< 点云解析模块*/

    /**
     * @brief 参数初始化
     */
    void paramInit()
    {
        c_allAreaLastPc_.reset(new allAreaPC());
    }

    /**
     * @brief rviz显示点云
     * @tparam P PXYZI
     * @param pub
     * @param pc
     */
    template <typename P>
    void showPointCloud(ros::Publisher& pub, typename pcl::PointCloud<P>::Ptr& pc)
    {
        if (pub.getNumSubscribers() < 1)
            return;
        sensor_msgs::PointCloud2 msgPC;
        pcl::toROSMsg(*pc, msgPC);
        msgPC.header.frame_id = "world";
        msgPC.header.stamp = ros::Time::now();
        pub.publish(msgPC);
    }

    /**
     * @brief 点云解析回调函数，返回变量传入检测模块使用
     * @param p_curPC 当帧原始点云 在rviz中显示
     * @param p_curObstacle 当帧障碍点云 在rviz中显示
     * @param p_allPcMatrix 各区域当帧障碍点矩阵
     */
    void roiCallback(PcXYZIPtr& p_curPC, PcXYZIPtr& p_curObstacle, areaPcPtr& p_allPcMatrix)
    {
        {
            std::lock_guard<std::mutex> l_lock(c_roiLock_);
            c_allAreaCurPc_.push(p_allPcMatrix);
        }

        showPointCloud<PCurXYZI>(outputCurr_, p_curPC);
        showPointCloud<PCurXYZI>(outputObstacle_, p_curObstacle);
        sendProtectPoints();  // 发送防护区域边界点
    }

    void sendProtectPoints()
    {
        // 防护区域rviz可视化
        for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
        {
            switch (i)
            {
                case 0: {
                    if (c_bAreaDisplayInit_ || c_allProtectVertex_[i]->size() > 0)
                        showPointCloud<PCurXYZI>(outputArea1_, c_allProtectVertex_[i]);
                    break;
                }
                case 1: {
                    if (c_bAreaDisplayInit_ || c_allProtectVertex_[i]->size() > 0)
                        showPointCloud<PCurXYZI>(outputArea2_, c_allProtectVertex_[i]);
                    break;
                }
                case 2: {
                    if (c_bAreaDisplayInit_ || c_allProtectVertex_[i]->size() > 0)
                        showPointCloud<PCurXYZI>(outputArea3_, c_allProtectVertex_[i]);
                    break;
                }
                default: break;
            }
        }

        if (c_bAreaDisplayInit_)
            c_bAreaDisplayInit_ = false;
    }

    /**
     * @brief 执行区域切换操作
     * @param p_vAreaChangeId
     */
    void setChangeAction(std::vector<int>& p_vAreaChangeId)
    {
        int l_iGroupID = c_iGroupID_;     // 当前检测的区域组ID
        c_iGroupID_ = p_vAreaChangeId[0]; /**<需要切换的区域组ID*/
        LOGA(WINFO, "正在切换区域组[{}] ", c_iGroupID_ + 1);
        if (c_sGroupScaleMap_.find(c_iGroupID_)
            != c_sGroupScaleMap_.end())  // 如果当前map中有该组区域信息
        {
            TicToc l_time;        // 计算区域切换耗时
            changeAreaCb_(true);  // 开始切换 停止解析 检测等操作

            AreaTablePtr l_sNewGroupTable(new AreaScaleTable());
            for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
            {
                // 将map中对应组别的区域加入到当前m_sGroupScaleTable_中
                if (c_sGroupScaleMap_[c_iGroupID_].find(i) != c_sGroupScaleMap_[c_iGroupID_].end())

                {
                    l_sNewGroupTable->push_back(*c_sGroupScaleMap_[c_iGroupID_][i]);
                }
            }
            c_sysParam_->m_sGroupScaleTable_ = l_sNewGroupTable;  // 改变当前区域组的刻度表
            c_iAreaNum_ = c_sysParam_->m_sGroupScaleTable_->size();  // 当前区域数量
            roiParamInit();        // 检测模块参数基于当前区域数量重新初始化
            changeAreaCb_(false);  // 停止切换 重新开启解析和检测

            LOGA(WINFO,
                 "\n切换区域组[{}], 区域个数: {}",
                 c_iGroupID_ + 1,
                 c_sysParam_->m_sGroupScaleTable_->size());
            wjPrint(WJCOL_GREEN, "change T: %f\n", l_time.toc());
        }
        else
        {
            LOGA(WWARN,
                 "无法切换区域组[{}], 保持检测当前区域组[{}]",
                 c_iGroupID_ + 1,
                 l_iGroupID + 1);
            c_iGroupID_ = l_iGroupID;
        }
    }

    void startDetect(int p_iGroupID)
    {
        LOGA(WINFO, "开始检测区域组[{}]", p_iGroupID + 1);
        if (c_sGroupScaleMap_.find(p_iGroupID)
            != c_sGroupScaleMap_.end())  // 如果当前map中有该组区域信息
        {
            AreaTablePtr l_sNewGroupTable(new AreaScaleTable());
            for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
            {
                // 将map中对应组别的区域加入到当前m_sGroupScaleTable_中
                if (c_sGroupScaleMap_[p_iGroupID].find(i) != c_sGroupScaleMap_[p_iGroupID].end())
                    l_sNewGroupTable->push_back(*c_sGroupScaleMap_[p_iGroupID][i]);
            }
            c_sysParam_->m_sGroupScaleTable_ = l_sNewGroupTable;  // 改变当前区域组的刻度表
            c_iAreaNum_ = c_sysParam_->m_sGroupScaleTable_->size();  // 当前区域数量
            roiParamInit();  // 检测模块参数基于当前区域数量重新初始化
            LOGA(WINFO,
                 "初始区域组[{}], 区域个数: {}",
                 p_iGroupID + 1,
                 c_sysParam_->m_sGroupScaleTable_->size());
        }
        else
        {
            printf("区域组%d不存在, 无法开启检测\n", p_iGroupID + 1);
            // changeAreaCb_(true);  // 不解析 不检测
        }
    }

    /**
     * @brief 切换区域时暂停检测
     * @param isStartChange
     */
    void changeAreaCb_(bool isStartChange)
    {
        // 停止检测
        if (isStartChange)
        {
            // 停止解析模块
            if (c_preProcess_)
                c_preProcess_->stop();

            // 停止检测模块
            if (c_roiDetect_)
                c_roiDetect_->stopRoiDetect();

            // 停止多帧检测
            stopMultiDetect();
        }
        else
        {
            // 运行解析模块
            if (c_preProcess_)
                c_preProcess_->reStart();

            // 运行检测模块
            if (c_roiDetect_)
                c_roiDetect_->runRoiDetect();

            // 运行多帧检测
            runMultiDetect();
        }
    }

    /**
     * @brief 上位机下发新的区域时，解算该区域
     * @param p_vAreaAddId
     */
    void setAddAction(std::vector<int>& p_vAreaAddId)
    {
        c_coordSolution_->add(p_vAreaAddId);
    }

    /**
     * @brief 响应上位机下发指令
     * @param p_iAcitonCMD 具体指令
     * @param p_vAreaId 区域ID
     */
    void areaActionCMD(int p_iAcitonCMD, std::vector<int>& p_vAreaId)
    {
        switch (p_iAcitonCMD)
        {
            case ADD: {
                LOGA(WINFO, "区域新增指令");
                setAddAction(p_vAreaId);  // 区域新增
                break;
            }
            case CHANGE: {
                LOGA(WINFO, "区域切换指令");
                if (p_vAreaId[2] == 1)  // 区域组文件存在才会切换区域
                    setChangeAction(p_vAreaId);
                break;
            }
            case QUERY: {
                LOGA(WINFO, "区域查询指令");
                break;
            }
            default: {
                LOGA(WWARN, "The CMD is Error!\n");
                break;
            }
        }
    }

    /**
     * @brief 计算解算得到的防护边界xyz坐标，在rviz中显示
     * @param p_iAreaIdx
     * @param p_sTable
     */
    void getProtectRangeCoord(int p_iAreaIdx, scaleTablePtr p_sTable)
    {
        int scanID = ZERO_SCAN;  // 只画0度水平线
        for (uint32_t j = 0; j < HORIZON_SCAN_NUM; j++)
        {
            if (!(*p_sTable)[scanID][j].state)
                continue;
            float minDis = (*p_sTable)[scanID][j].minDis;
            float maxDis = (*p_sTable)[scanID][j].maxDis;
            double curHorAngleRad = (j + 1) * 0.2 * M_PI / 180;

            PXYZI point;
            float xy = minDis * cos(0);
            point.z = minDis * sin(0);
            point.x = xy * sin(curHorAngleRad);
            point.y = xy * cos(curHorAngleRad);
            c_allProtectVertex_[p_iAreaIdx]->push_back(point);

            xy = maxDis * cos(0);
            point.z = maxDis * sin(0);
            point.x = xy * sin(curHorAngleRad);
            point.y = xy * cos(curHorAngleRad);
            c_allProtectVertex_[p_iAreaIdx]->push_back(point);
        }
    }

    /**
     * @brief 防护区域rviz显示
     */
    void displayDetectArea()
    {
        for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
        {
            c_allProtectVertex_[i].reset(new PcXYZI());
            for (size_t j = 0; j < c_sysParam_->m_sGroupScaleTable_->size(); j++)
            {
                if (c_sysParam_->m_sGroupScaleTable_->at(j).areaID == i)
                {
                    scaleTablePtr table = c_sysParam_->m_sGroupScaleTable_->at(j).table;
                    getProtectRangeCoord(i, table);
                    break;
                }
            }
        }
    }

    /**
     * @brief ROI多帧检测参数reset
     * @param p_iIdx 区域ID
     */
    void roiParamReset(int p_iIdx)
    {
        c_areaDet_[p_iIdx] = 0;
        c_areaMultiFrameSwitch_[p_iIdx] = false;
        wjPrint(WJCOL_GREEN, "关闭多帧检测", c_areaName_[p_iIdx].c_str());
    }

    /**
     * @brief 停止多帧检测
     */
    void stopMultiDetect()
    {
        c_bStopMultiDetect_ = true;
        while (1)
        {
            if (c_bMultiDetectOver_)
                break;
            else
                usleep(1000);
        }
    }

    /**
     * @brief 运行多帧检测
     */
    void runMultiDetect()
    {
        c_bStopMultiDetect_ = false;
    }

    /**
     * @brief 多帧检测功能
     * @param p_iIdx
     * @param p_iDetectState
     * @param p_curAreaPc
     */
    void multiFrameDetect(int p_iIdx, int p_iDetectState, s_AreaPC& p_curAreaPc)
    {
        int areaID = p_curAreaPc.areaID; /**< 区域ID*/
        int groupID = p_curAreaPc.groupID;
        int detFrame = p_curAreaPc.detFrame; /**< 区域响应帧数*/

        switch (p_iDetectState)
        {
            // 没有点的时候不报警
            case NoPoint: {
                if (c_areaMultiFrameSwitch_[p_iIdx])  // 如果开启过多帧检测 关闭
                    roiParamReset(p_iIdx);
                // c_areaDetectFlag_[p_iIdx] = true;   // 该区域检测完成
                break;
            }
            case LargeObstacle: {
                detectPrint(areaID, "Large Obstacles");  // 直接报警
                c_warnCallback_ = true;
                c_areaWarnFlag_[p_iIdx] = 1;          // 报警标志位=true
                if (c_areaMultiFrameSwitch_[p_iIdx])  // 如果开启过多帧检测 关闭
                    roiParamReset(p_iIdx);
                // c_areaDetectFlag_[p_iIdx] = true;   // 该区域检测完成
                break;
            }
            case SmallObstacle: {
                //  当是小物体时，如果当前没有开启多帧检测，则开启
                if (!c_areaMultiFrameSwitch_[p_iIdx])
                {
                    c_areaMultiFrameSwitch_[p_iIdx] = true;
                    wjPrint(WJCOL_GREEN, "开启多帧检测", c_areaName_[areaID].c_str());
                }

                c_areaDet_[p_iIdx]++;  // 各区域多帧检测开启后响应帧数+1
                // std::cout << " 当前检测帧数： " <<c_areaDet_[p_iIdx] << " 区域检测帧数: " <<
                // detFrame << std::endl;
                (*c_allAreaLastPc_)[p_iIdx] = p_curAreaPc;  // 记录当帧障碍点矩阵

                if (c_areaDet_[p_iIdx] >= detFrame)
                {
                    detectPrint(areaID, "Small Obstacles");
                    // c_areaDetectFlag_[p_iIdx] = true;   // 该区域检测完成
                    c_warnCallback_ = true;
                    c_areaWarnFlag_[p_iIdx] = 1;  // 报警标志位=true
                    roiParamReset(p_iIdx);
                    break;
                }
            }
            default: break;
        }
        isGroupDetectComplete(p_iIdx, groupID, c_warnCallback_);  // 判断该组区域是否检测完成
    }

    void isGroupDetectComplete(int p_iIdx, int p_iGroupID, bool p_bWarnCallback)
    {
        // 该组区域检测完成
        if (p_iIdx == (c_iAreaNum_ - 1))
        {
            int l_iGroupID = p_iGroupID;
            bool l_bWarnCallback_ = p_bWarnCallback;
            std::vector<int> l_vbWarnFlag_ = c_areaWarnFlag_;

            c_warnCallback_ = false;
            std::vector<int>().swap(c_areaWarnFlag_);
            c_areaWarnFlag_.resize(c_iAreaNum_, 0);

            // 如果三个区域中至少一个有障碍物 报警
            if (l_bWarnCallback_)
            {
                c_wjNetAppWarn_->fillInWarnCfg(l_iGroupID, l_vbWarnFlag_);
            }
        }
    }

    void invalidDataWarn()
    {
        int l_iGroupID = c_iGroupID_;
        c_wjNetAppWarn_->sendInvalidDataFlag(c_iGroupID_);  // 没有雷达数据时发送报警协议
    }

    void detectPrint(int p_iAreaID, std::string p_sPrintInfo)
    {
        std::string printInfo = p_sPrintInfo + " in Area";
        switch (p_iAreaID)
        {
            case DANGER: {
                wjPrint(WJCOL_GREEN, printInfo, c_areaName_[p_iAreaID].c_str());
                break;
            }
            case WARNING1: {
                wjPrint(WJCOL_YELLOW, printInfo, c_areaName_[p_iAreaID].c_str());
                break;
            }
            case WARNING2: {
                wjPrint(WJCOL_BLUE, printInfo, c_areaName_[p_iAreaID].c_str());
                break;
            }
            default: break;
        }
    }

    /**
     * @brief ROI检测回调模块，传入变量用于多帧检测
     * @param p_iIdx
     * @param p_iDetectState
     * @param p_curAreaPc
     */
    void multiFrameCallback(int p_iIdx, int p_iDetectState, s_AreaPC& p_curAreaPc)
    {
        if (!c_bStopMultiDetect_)
        {
            c_bMultiDetectOver_ = false;
            multiFrameDetect(p_iIdx, p_iDetectState, p_curAreaPc);
            c_bMultiDetectOver_ = true;
        }
    }

    /**
     * @brief 程序上电时刻度表初始化
     */
    void scaleTableInit()
    {
        int l_iGroupID = c_sysParam_->m_iInitGroupID - 1;  // 初始化区域组
        c_iGroupID_ = l_iGroupID;
        LOGA(WINFO, "初始区域组[{}]", l_iGroupID + 1);
        if (c_sGroupScaleMap_.find(l_iGroupID) != c_sGroupScaleMap_.end())
        {
            startDetect(l_iGroupID);  // 初始化时切换至默认的刻度表
        }
    }

    /**
     * @brief 区域解算模块回调函数，返回新的Map
     * @param p_sGroupScaleMap
     */
    void areaSolutionCallback(GroupScaleMap& p_sGroupScaleMap)
    {
        c_sGroupScaleMap_ = p_sGroupScaleMap;  // 所有区域的刻度表
    }

    /**
     * @brief ROI参数初始化
     */
    void roiParamInit()
    {
        std::vector<bool>().swap(c_areaMultiFrameSwitch_);  // 多帧检测开关数组
        std::vector<int>().swap(c_areaDet_);          // 各区域多帧检测开启后当前帧数
        std::vector<bool>().swap(c_areaDetectFlag_);  // 各区域检测完成标志位
        std::vector<int>().swap(c_areaWarnFlag_);     // 各区域报警标志位
        std::vector<s_AreaPC>().swap(*c_allAreaLastPc_);  // 各区域多帧检测开启后前一帧点云
        std::vector<PcXYZIPtr>().swap(c_allProtectVertex_);  // 各区域防护距离端点

        c_areaDet_.resize(c_iAreaNum_, 0);
        c_areaDetectFlag_.resize(c_iAreaNum_, false);
        c_areaWarnFlag_.resize(c_iAreaNum_, 0);
        c_allAreaLastPc_->resize(c_iAreaNum_);
        c_areaMultiFrameSwitch_.resize(c_iAreaNum_, false);
        c_allProtectVertex_.resize(EVERY_GROUP_AREA_NUM);

        c_bAreaDisplayInit_ = true;
        displayDetectArea();  // 解算防护边界的点
    }

    void saveParam()
    {
        wj_slam::ParamOD l_param;
        std::string path = ros::package::getPath("wanji_od");
        c_sysParam_->m_iInitGroupID = c_iGroupID_ + 1;  // 退出之前保存当前检测区域组号
        if (!l_param.saveSysParam(path))
            LOGA(WERROR, "退出保存参数失败!");
    }

    void stop()
    {
        saveParam();
        std::cout << "\n避障程序正在退出...\n";
        c_wjNetApp_->shutDown();
        c_wjNetAppWarn_->shutDown();
        c_preProcess_->shutDown();
        c_roiDetect_->shutDown();
    }

    void setLidarData(uint32_t p_uiLidarID,
                      boost::shared_ptr<s_LIDAR_RAW_DATAS>& p_pScanMsg,
                      int p_iFrameId,
                      bool isReNewConnect)
    {
        if (c_preProcess_)
            c_preProcess_->processScanCb(p_uiLidarID, p_pScanMsg, p_iFrameId, isReNewConnect);
    }
};
WanJiOD::WanJiOD(ros::NodeHandle& p_node,
                 std::vector<wj_slam::s_LidarConfig>& p_mLidarCfg,
                 sTimeval& p_sTimeProcStart)
    : c_node_(node)
{
    wj_slam::s_masterOdCfg* c_masterParamPtr;
    c_masterParamPtr = wj_slam::s_masterOdCfg::getIn();
    wj_slam::ParamOD l_param(false);
    std::string path = ros::package::getPath("wanji_od");
    l_param.loadSysParam(path);  // user.yaml读取
    c_sysParam_ = wj_slam::SYSPARAMOD::getIn();

    c_sysParam_->m_lidar = p_mLidarCfg;
    c_sysParam_->m_lidar.resize(1);
    c_sysParam_->m_time.m_sTimeProcStart = wj_slam::getTimeNow();
    c_sysParam_->m_time.m_sTimeSetSync = c_sysParam_->m_time.m_sTimeProcStart;
    c_sysParam_->m_time.m_sTimeSyncStart = c_sysParam_->m_time.m_sTimeProcStart;

    LOGA(WINFO, "****** OD_VERSION: {} ******", c_sysParam_->m_sOdVersion);

    paramInit();  // 参数初始化

#pragma region "coordSolution"
    c_coordSolution_.reset(
        new CoordSolution(boost::bind(&WanJiOD::areaSolutionCallback, this, _1)));
    // 计算区域初始化耗时
    TicToc l_time;
    c_coordSolution_->start();
    scaleTableInit();  // 初始化检测刻度表
    LOGA(WINFO, "区域初始化完成！");
    wjPrint(WJCOL_GREEN, "Area init T", l_time.toc());
#pragma endregions

#pragma region "netApp"
    c_wjNetApp_.reset(new WJNetAppOD(c_sysParam_->m_iServerPort,
                                     boost::bind(&WanJiOD::areaActionCMD, this, _1, _2)));
#pragma endregion

#pragma region "netAppWarn"
    c_wjNetAppWarn_.reset(new WJNetAppOD(c_sysParam_->m_iWarnPort,
                                         boost::bind(&WanJiOD::areaActionCMD, this, _1, _2)));
#pragma endregion

#pragma region "preproc"
    c_preProcess_.reset(
        new PreProcess<PCurXYZI>(boost::bind(&WanJiOD::roiCallback, this, _1, _2, _3)));
    c_preProcess_->start();
#pragma endregion

#pragma region "roiDetect"
    c_roiDetect_.reset(new RoiDetect(c_allAreaCurPc_,
                                     c_allAreaLastPc_,
                                     c_areaMultiFrameSwitch_,
                                     c_roiLock_,
                                     boost::bind(&WanJiOD::multiFrameCallback, this, _1, _2, _3),
                                     boost::bind(&WanJiOD::invalidDataWarn, this)));
    std::thread roi = std::thread(&RoiDetect::run, c_roiDetect_);
    roi.detach();
#pragma endregion

#pragma region "ros"
    outputCurr_ = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_lidarOD", 1);
    outputObstacle_ = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_detect", 1);
    outputArea1_ = c_node_.advertise<sensor_msgs::PointCloud2>("danger_area", 1);
    outputArea2_ = c_node_.advertise<sensor_msgs::PointCloud2>("warning_area1", 1);
    outputArea3_ = c_node_.advertise<sensor_msgs::PointCloud2>("warning_area2", 1);
#pragma endregion

#ifdef PLAYROSBAG
    wanji_scan_ = c_node_.subscribe<wanji_msgs::WanjiScan>(
        "wanji_packets", 10, &PreProcess<PCurXYZI>::processScanRos, c_preProcess_);
#endif
}

}  // namespace wj_od
generalOdLog* glog = nullptr;
#ifdef LOG_DETECT
// 区域避障日志 avoid obstacles
WJODLog* generalOdLog::aolog = nullptr;
#endif

boost::shared_ptr<wj_od::WanJiOD> g_wjOd = nullptr;

bool openWanjiOD(ros::NodeHandle& p_node,
                 std::vector<wj_slam::s_LidarConfig>& p_mLidarCfg,
                 sTimeval& p_sTimeProcStart)
{
    if (g_wjOd)
        return true;

    if (!g_wjOd)
    {
        glog = new generalOdLog(ros::package::getPath("wanji_od") + "/data/Log/Logfiles/",
                            "wj",
                            FileMode::DAILY_ROTATE,
                            LogMode::TRIAL_MODE,
                            1024 * 1024);
        glog->changeMode(0);  // 重设日志等级
        LOGA(WINFO, "\n****** Creat OD Log File at Time: {} ******", WJODLog::getSystemTime());
        LOGA(WINFO, "正在打开避障程序");
        g_wjOd.reset(new wj_od::WanJiOD(p_node, p_mLidarCfg, p_sTimeProcStart));
        return true;
    }
    return false;
}

void closeWanjiOD()
{
    if (!g_wjOd)
        return;
    else
    {
        g_wjOd->stop();
        g_wjOd = nullptr;
        LOGA(WINFO, "避障程序退出");
    }
}

void setLidarData(uint32_t p_uiLidarID,
                  boost::shared_ptr<s_LIDAR_RAW_DATAS>& p_pScanMsg,
                  int p_iFrameId,
                  bool isReNewConnect)
{
    if (g_wjOd)
    {
        g_wjOd->setLidarData(p_uiLidarID, p_pScanMsg, p_iFrameId, isReNewConnect);
    }
}
