/*******************************
 * @Descripttion:
 * @version:
 * @Author: zushu<PERSON>
 * @Date: 2021-04-28 14:42:39
 * @LastEditors: zushuang
 * @LastEditTime: 2021-05-16 16:26:01
 *******************************/
#pragma once
#ifndef _PREPROC_H_
#    define _PREPROC_H_
#    include "./common/common_ex.h"
#    include "algorithm/coord_solution/coordSolution.h"
#    include "algorithm/preproc/driver/wanji_driver.h"
#    include "algorithm/preproc/feature_extract/featureExtract.h"
#    include "algorithm/preproc/unpack/convert.h"
#    include "algorithm/preproc/unpack/convert_720NP.h"
#    include "tic_toc.h"
#    include <boost/function.hpp>
#    include <thread>
// #define PRINT

// typedef std::pair<FeaturePtr, FeaturePtr> FEATURE_PAIR<P>;
namespace wj_od {
template <typename P> class PreProcess {
  public:
    typedef boost::shared_ptr<PreProcess> Ptr;

  private:
    typedef typename wj_slam::FEATURE_PAIR<P>::Ptr FeaturePairPtr;
    typedef typename s_LIDAR_RAW_DATAS::Ptr RawDataPtr;
    typedef pcl::PointCloud<P> Feature;
    typedef boost::shared_ptr<Feature> FeaturePtr;
    typedef boost::shared_ptr<Feature> PointCloudPtr;
    typedef std::pair<uint32_t, RawDataPtr> RawDataPair;
    boost::function<void(PointCloudPtr&, PointCloudPtr&, areaPcPtr&)>
        c_roiExtractCb_;  // 点云检测回调

    std::vector<boost::shared_ptr<wanji_driverOD::wanjiDriver>> c_vpDriver_;
    std::vector<boost::shared_ptr<wanji_driverOD::Convert>> c_vpConv_;
    wj_slam::SYSPARAMOD* c_stSysParam_;
    std::mutex c_FECBlock_;

    void setStatus_();
    void netErrorCb_(uint32_t p_iLaserId);

    public:
    void stop(); 
    void reStart(); 

    /**
     * @function: fECallback
     * @description: 点云解析后回调
     * @param {s_PCloud}pc: 点云存储位置 {double} timestamp : 点云时间戳
     * @input: null
     * @output: pc,timestamp
     * @return {*}
     * @others: null
     */
    void fECallback(uint32_t p_iLaserId,
                    areaPcPtr& allPcMatrix,
                    PointCloudPtr& allPC,
                    PointCloudPtr& pcObstacleOut,
                    int timestamp,
                    int recvTimestamp,
                    u_int32_t scanFrameID);

  public:
    void
    processScanCb(uint p_uiLidarID, RawDataPtr& p_pScanMsg, int p_iFrameId, bool isReNewConnect);

    /**
     * @brief 设置雷达转移和盲区
     *
     */
    void resetTransToBase();
    void start();
    void shutDown();
    PreProcess(boost::function<void(PointCloudPtr&, PointCloudPtr&, areaPcPtr&)> p_callback);
    ~PreProcess();
};
}  // namespace wj_od
#    ifdef WJOD_NO_PRECOMPILE
#        include "impl/pre_proc.hpp"
#    else
#        define WJOD_PREPROC(P) template class wj_od::PreProcess<P>;
#    endif
#endif