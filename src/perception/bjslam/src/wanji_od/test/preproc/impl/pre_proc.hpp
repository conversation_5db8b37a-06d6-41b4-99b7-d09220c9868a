/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-07-14 17:14:39
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:32:05
 */
#pragma once
#ifndef _WJOD_PREPROC_HPP_
#    define _WJOD_PREPROC_HPP_
#    include "../pre_proc.h"
namespace wj_od {
template <typename P> void PreProcess<P>::setStatus_()
{
    // printf("set lidarMode\n");
    for (int l_iLaserId = 0; l_iLaserId < c_stSysParam_->m_iLidarNum; l_iLaserId++)
    {
        // 统一shutdown
        if (c_vpDriver_[l_iLaserId])
        {
            c_vpDriver_[l_iLaserId]->shutDown();
            c_vpDriver_[l_iLaserId] = nullptr;
        }

        if (c_vpConv_[l_iLaserId])
        {
            c_vpConv_[l_iLaserId]->shutdown();
            c_vpConv_[l_iLaserId] = nullptr;
        }
    }

    for (int l_iLaserId = 0; l_iLaserId < c_stSysParam_->m_iLidarNum; l_iLaserId++)
    {
#    ifdef OpenOdDriver
        c_vpDriver_[l_iLaserId].reset(new wanji_driver::wanjiDriver(
            l_iLaserId, boost::bind(&PreProcess<P>::processScanCb, this, _1, _2, _3, _4)));
#    endif

        bool l_KnewLidarType = true;
        // 统一重新实例化
        if (c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720A"
            || c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720F"
            || c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720FCW")
        {
            c_vpConv_[l_iLaserId].reset(new wanji_driverOD::Convert720(
                l_iLaserId,
                boost::bind(&PreProcess<P>::fECallback, this, _1, _2, _3, _4, _5, _6, _7)));
        }
        else if (c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720F_NP"
                 || c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType == "WLR720FCW_NP")
        {
            c_vpConv_[l_iLaserId].reset(new wanji_driverOD::Convert720F(
                l_iLaserId,
                boost::bind(&PreProcess<P>::fECallback, this, _1, _2, _3, _4, _5, _6, _7)));
        }
        else
        {
            l_KnewLidarType = false;
        }
        if (!l_KnewLidarType)
        {
            LOGA(WERROR,
                 "雷达驱动启动失败 | 不支持雷达类型[{}]，请检查雷达类型是否正确!",
                 c_stSysParam_->m_lidar[l_iLaserId].m_dev.m_sDevType);
        }
    }
}

template <typename P> void PreProcess<P>::stop()
{
    for (int l_iLaserId = 0; l_iLaserId < c_stSysParam_->m_iLidarNum; l_iLaserId++)
    {
        if (c_vpConv_[l_iLaserId])
            c_vpConv_[l_iLaserId]->stop();
    }
}

template <typename P> void PreProcess<P>::reStart()
{
    for (int l_iLaserId = 0; l_iLaserId < c_stSysParam_->m_iLidarNum; l_iLaserId++)
    {
        if (c_vpConv_[l_iLaserId])
            c_vpConv_[l_iLaserId]->reStart();
    }
}

template <typename P>
void PreProcess<P>::fECallback(uint32_t p_iLaserId,
                               areaPcPtr& allPcMatrix,
                               PointCloudPtr& allPC,
                               PointCloudPtr& pcObstacleOut,
                               int timestamp,
                               int recvTimestamp,
                               u_int32_t scanFrameID)
{
    c_roiExtractCb_(allPC, pcObstacleOut, allPcMatrix);
}

template <typename P>
void PreProcess<P>::processScanCb(uint32_t p_uiLidarID,
                                  RawDataPtr& p_pScanMsg,
                                  int p_iFrameId,
                                  bool isReNewConnect)
{
    RawDataPtr l_pScanMsg = p_pScanMsg;
    // 处理帧数据,确保c_vpConv_已实例化且正常运行
    if (c_vpConv_[p_uiLidarID] && !c_vpConv_[p_uiLidarID]->isShutdown())
        c_vpConv_[p_uiLidarID]->processScan(p_pScanMsg, p_iFrameId, isReNewConnect);
}

template <typename P> void PreProcess<P>::resetTransToBase()
{
    std::lock_guard<std::mutex> l_mtx(c_FECBlock_);
    for (int i = 0; i < c_stSysParam_->m_iLidarNum; i++)
    {
        wj_slam::s_POSE6D l_DipQT, l_CalQT;
        wj_slam::s_LidarConfig& l_lidar = c_stSysParam_->m_lidar[i];
        // 计算倾角校准
        l_DipQT.setRPY(l_lidar.m_fDipAngle[0], l_lidar.m_fDipAngle[1], 0);
        l_CalQT.setX(l_lidar.m_fCalibration[0]);
        l_CalQT.setY(l_lidar.m_fCalibration[1]);
        l_CalQT.setZ(l_lidar.m_fCalibration[2]);
        l_CalQT.setRPY(
            l_lidar.m_fCalibration[3], l_lidar.m_fCalibration[4], l_lidar.m_fCalibration[5]);
        // 计算标定校准
        l_lidar.m_transToBase = l_CalQT * l_DipQT;
        // 设置下发
        if (c_vpConv_[i])
            c_vpConv_[i]->setTransToBase();
    }
}

template <typename P> void PreProcess<P>::start()
{
    setStatus_();
    resetTransToBase();
}
template <typename P>
PreProcess<P>::PreProcess(
    boost::function<void(PointCloudPtr&, PointCloudPtr&, areaPcPtr&)> p_callback)
{
    c_stSysParam_ = wj_slam::SYSPARAMOD::getIn();
    c_roiExtractCb_ = p_callback;
    c_vpDriver_.resize(c_stSysParam_->m_iLidarNum);
    c_vpConv_.resize(c_stSysParam_->m_iLidarNum);
}
template <typename P> void PreProcess<P>::shutDown()
{
    // 关闭子类循环
    for (int i = 0; i < c_stSysParam_->m_iLidarNum; i++)
    {
        // c_vpDriver_[i]->shutDown();
        c_vpConv_[i]->shutdown();
    }
    // 关闭类中循环
}
template <typename P> PreProcess<P>::~PreProcess()
{
    std::cout << "exit pre" << std::endl;
    for (int i = 0; i < c_stSysParam_->m_iLidarNum; i++)
    {
        c_vpDriver_[i] = nullptr;
        c_vpConv_[i] = nullptr;
    }
};
}  // namespace wj_od
#    define WJOD_PREPROC(P) template class wj_od::PreProcess<P>;
#endif