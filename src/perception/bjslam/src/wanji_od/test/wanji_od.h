/*
 * @Author: senlin <EMAIL>
 * @Date: 2022-08-15 13:27:17
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 14:03:01
 * @FilePath: /src/wanji_od/test/wanji_od.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#include "./algorithm/coord_solution/coordSolution.h"
#include "./algorithm/roi_detect/roiDetect.h"
#include "./common/common_ex.h"
#include "net_app/netApp.h"
#include "preproc/pre_proc.h"
#include <mutex>
#include <queue>

typedef pcl::PointXYZ PXYZ;
typedef pcl::PointXYZHSV PCurXYZHSV;
typedef pcl::PointXYZI PCurXYZI;
typedef pcl::PointCloud<PCurXYZI> PcXYZI;
typedef boost::shared_ptr<PcXYZI> PcXYZIPtr;
typedef boost::shared_ptr<s_LIDAR_RAW_DATAS> RawDataPtr;
