#include "common/config/conf_lidar.h"
#include "common/config/conf_timer.h"
#include <ros/ros.h>

bool openWanjiOD(ros::NodeHandle& p_node,
                 std::vector<wj_slam::s_LidarConfig>& p_mLidarCfg,
                 struct timeval& p_sTimeProcStart);
void setLidarData(uint32_t p_uiLidarID,
                  boost::shared_ptr<s_LIDAR_RAW_DATAS>& p_pScanMsg,
                  int p_iFrameId,
                  bool isReNewConnect){}
void closeWanjiOD();