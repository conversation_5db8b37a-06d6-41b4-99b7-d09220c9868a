/*
 * @Author: your name
 * @Date: 2021-05-29 16:18:55
 * @LastEditTime: 2022-10-24 14:43:23
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/include/net_app/common.h
 */
#pragma once
#include <Eigen/Dense>
#include <boost/bind.hpp>
#include <boost/function.hpp>
// #include <eigen3/Eigen/Core>
#include <eigen3/Eigen/Dense>
// #include <eigen3/Eigen/Geometry>
#include <iostream>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <unistd.h>

#define NET_LENGTH_MAX 4096
#define TOUINT16(a, b) (((a << 8) & 0xFF00 | b) & 0xFFFF)
#define TOINT32(a) ((a[0] << 24 & 0xFF000000 | a[1] << 16 & 0xff0000 | a[2] << 8 & 0xff00 | a[3]))

typedef struct NetMsg
{
    u_char m_aucBuf[NET_LENGTH_MAX];
    u_int m_uiDataLen = 0;
} s_NetMsg;

/**
 * NOMATCHPROTOCOL<默认状态/无效协议>;
 * CMD_SLAVER<SLAM实时生效协议>;
 * CMD_MASTER<Master协议>;
 * CMD_DEVICE<雷达协议>
 * CMD_PARAM<参数协议>
 * CMD_PRVT<私有协议>
 */
enum WJCMDTYPE {
    NOMATCHPROTOCOL = 0,
    CMD_SLAVER = 0x06,  // 下位机指令,算法相关
};

enum SLACMDID {        // SLAM实时生效指令ID
    SENDCMD_AREA = 5,  // 上位机发送区域信息
    SETCMD_AREA = 7,   //  上位机配置/切换区域

    QUERYCMD_AREA = 6,  // 上位机查询区域信息

};