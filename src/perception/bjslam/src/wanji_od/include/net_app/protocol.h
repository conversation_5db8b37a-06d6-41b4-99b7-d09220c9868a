/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 10:43:56
 * @LastEditTime: 2022-10-28 12:14:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/include/net_app/protocol.h
 */

#pragma once
// #include "cmdPro.h"
#include "common.h"
#include "wj_Protocol.h"
#include <boost/function.hpp>
#include <ctime>
#include <mutex>
#include <netinet/tcp.h>
#include <semaphore.h>
#include <sys/time.h>
#include <thread>
#include <time.h>
#include <unistd.h>

#define WJPROTOCOL 1
#define SICKPROTOCOL 2

class ProtocolOD {
  private:
    struct timeval time_tv;
    struct timezone time_tz;
    struct tm* p;
    WJProtocolOD* c_pWJProtocol_;
    s_NetMsg& c_stNetMsg_;
    boost::function<void(char*, int)> sendCallBack_;
    boost::function<void(int, std::vector<int>&)> c_actionCallback_;
    int c_iCurProcOffset_ = 0;
    bool c_bProcThrRun_ = false;
    bool c_bProcThrRunOver_ = false;
    std::mutex c_mtxLock_;

  public:
    void exitProcThread(void)
    {
        // 如果解析线程未运行中不操作
        if (!c_bProcThrRun_)
            return;
        c_bProcThrRun_ = false;
        {
            std::lock_guard<std::mutex> l_mtx(c_mtxLock_);
            c_iCurProcOffset_ = c_stNetMsg_.m_uiDataLen;
        }
        while (1)
        {
            if (c_bProcThrRunOver_)
                break;
            else
                usleep(500);
        }
        c_iCurProcOffset_ = 0;
        c_stNetMsg_.m_uiDataLen = 0;
    }
    void shutDown()
    {
        printf("ProtocolOD shutdown\n");
        exitProcThread();
        if (c_pWJProtocol_)
            c_pWJProtocol_->shutDown();
        printf("ProtocolOD shutdown over\n");
    }
    ProtocolOD(s_NetMsg& netMsg,
             boost::function<void(char*, int)> sendCallBack,
             boost::function<void(int, std::vector<int>&)> p_actionCallback);
    void procCmdInThread();
    int recognizeProtocolType(int&);
    void procCmd();
    ~ProtocolOD()
    {
        if (c_pWJProtocol_)
            delete c_pWJProtocol_;

        c_pWJProtocol_ = NULL;
    };
};
// # endif