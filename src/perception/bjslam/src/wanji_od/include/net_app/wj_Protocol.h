/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-05-28 08:53:45
 * @LastEditTime: 2022-12-28 15:47:55
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /wanji_ws/src/wanji_net/include/wj_Protocol.h
 */

#pragma once
// #include "cmdPro.h"
#include "./common/common_ex.h"
#include "common.h"
#include "tool/fileTool/fileTool.h"
#include "tool/protocolTool/protocolTool.h"
#include <dirent.h>
#include <fstream>
#include <mutex>
#include <ros/package.h>
#include <ros/ros.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <unordered_map>
// #include "../algorithm/coord_solution/coordSolution.h"
// using namespace wj_od;

class WJProtocolOD {
  public:
    u_char* getWJCMD(int&, std::mutex&);
    int selectWJProtocol(u_char*, char*);
    int selectSlaverProtocol(u_char*, char*);
    int selectDeviceProtocol(u_char*, char*);
    int selectParamProtocol(u_char*, char*);
    int selectPrivateProtocol(u_char*, char*);

  private:
    typedef std::vector<u_char*> onePkgData;
    typedef struct oneAreaData
    {
        u_char* areaData;  // 存放区域原始数据，完整接收一个区域后再写入文件
        boolVector recieveFlag;  // 存放各包接收标志
        bool recveCompleteFlag;  // 完整接收标志
    } s_oneAreaData;

    typedef std::unordered_map<int, s_oneAreaData> areaRawData;

    boost::function<void(char*, int)> sendCallback_;
    boost::function<void(int, std::vector<int>&)> c_actionCallback_;
    s_NetMsg& c_stNetMsg_;
    areaRawData c_sAreaCfg_;

    bool c_bRun_ = true;
    int c_iAreaNum_ = 0;
    bool c_bRunOver_ = true;
    bool c_bHasRecvFlag_ = false;

    bool c_bAreaChangeFlag_ = false;  //
    std::string c_sAreaDir_;
    std::vector<int> c_vAreaIdNum_ = std::vector<int>(3, 0);

    u_char c_acWJResponseHead_[26];

    void calcWJProtocolHead_(u_char*, char*, int&);
    void calcWJProtocolTail_(char*, int&);
    void sendRecvSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes);
    void sendQuerySuccCMD_(std::vector<bool> p_vbAreaExistFlag,
                           char* p_pcBufResponse,
                           int p_iLen,
                           int p_iRes);
    void sendQueryFailedCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes);
    void sendChangeSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes);
    // 解析收到的每包区域信息
    void analysisRecieveAreaInfo(u_char* p_pcBufCMD);
    // 将当前区域信息写入文件
    void saveNewArea(int p_iGroupID, int p_iAreaID, int p_iAreaNumID);

    bool checkAreaCfgFile(u_char* p_pcBufCMD, std::vector<bool>& p_vbAreaExistFlag);
    // 查询该区域文件是否存在
    bool isAreaExists(int p_iGroupID, int p_iAreaID);
    // 删除旧文件
    void deleteOldAreaCfg(int p_iGroupID);

    void
    fillInAreaCfg(int p_iLineIdx, std::vector<std::string> p_vLineArray, char* p_pcBufResponse);

    char* readAreaCfgFile(int p_iGroupID, int p_iAreaID, char* p_pcBufResponse, int& p_iLen);

  public:
    WJProtocolOD(s_NetMsg& netMsg,
               boost::function<void(char*, int)> sendCallback,
               boost::function<void(int, std::vector<int>&)> p_actionCallback)
        : sendCallback_(sendCallback), c_actionCallback_(p_actionCallback), c_stNetMsg_(netMsg)
    {
        c_sAreaDir_ = ros::package::getPath("wanji_od");
    };
    ~WJProtocolOD(){};
    void shutDown()
    {
        c_bRun_ = false;
        while (1)
        {
            // printf("wj_protocol shutdown\n");
            if (c_bRunOver_)
                break;
            usleep(1000);
        }
    }
};
// #endif