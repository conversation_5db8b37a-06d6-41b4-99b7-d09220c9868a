

#ifndef COORD_SOLUTION
#define COORD_SOLUTION

#include "./common/common_ex.h"

namespace wj_od {
class CoordSolution {
  public:
    typedef boost::shared_ptr<CoordSolution> Ptr;

  private:
    u_int c_iGroupNum_; /**<区域组个数 */
    u_int c_iAreaNum_;

    /**
     * @brief 多边形区域，任何形状到最后都会转化多边形来解算
     *
     *
     */
    typedef struct Polygon
    {
        u_int vNum;
        pcVerPtr vertex;
        Polygon()
        {
            vNum = 0;
            vertex.reset(new pcVer());
        }
        void reset()
        {
            vNum = 0;
            vertex.reset(new pcVer());
        }
    } s_Polygon;
    /**
     * @brief 区域信息
     *
     *
     */
    typedef struct Area
    {
        u_int groupID;       /**< 区域主ID（属于哪组区域）*/
        u_int areaID;        /**< 区域副ID（一组中属于哪个防护等级的区域）*/
        u_int resTime;       /**< 响应时间 */
        u_int shape;         /**< 区域形状 */
        float minHeight;     /**< 最小防护高度 */
        float maxHeight;     /**< 最大防护高度*/
        bool state;          /**< 状态值 0即用户没有设置该区域 */
        bool originInRegion; /**< 原点是否在区域内 */
        double startAngle;
        double endAngle;
        s_Polygon polygon; /**< 端点信息 */
        Area()
        {
            groupID = 0;
            areaID = 0;
            minHeight = -1.8;
            maxHeight = 0.5;
            resTime = 200;
            shape = 0;  // 0:多边形 1:圆形 2:扇形 3:长方形
            state = false;
            originInRegion = false;
            startAngle = 0;
            endAngle = 0;
            polygon.reset();
        }
    } s_Area;

  private:
    wj_slam::SYSPARAMOD* c_stSysParam_;
    GroupScaleMap c_sGroupTableMap_;   /**< 16组区域刻度表Map*/
    std::vector<int> c_vAreaAddId_;    /**< 区域新增ID */
    std::vector<int> c_vAreaChangeId_; /**< 区域切换ID */

  public:
    float c_fLidarHeight_;                                   /**< 雷达安装高度 */
    float c_fLidarPitch_;                                    /**< 雷达安装时的俯仰角 */
    float c_fGroudHeight_;                                   /**< 地面高度 默认地面10cm厚*/
    std::string c_sAreaDir_;                                 /**< 区域文件默认目录*/
    boost::function<void(GroupScaleMap&)> c_preproCallback_; /**< 回调函数 返回新的map*/

    double c_afVertAngle_[WLR720_SCANS_PER_FIRING] =
        {-16, -14, -12, -10, -8, -6, -4, -2, 0, 2, 4, 6, 8, 10, 12, 14}; /**< 雷达垂直角度 */
    double c_afVsinRotTable_[WLR720_SCANS_PER_FIRING];
    double c_afVcosRotTable_[WLR720_SCANS_PER_FIRING];

    CoordSolution(boost::function<void(GroupScaleMap&)> p_callback);
    ~CoordSolution();

    /**
     * @brief 读雷达的安装高度、俯仰角
     *
     * @param p_fLidarHeight
     * @param p_fLidarPitch
     *
     */
    void getLidarParam(float p_fLidarHeight, double p_fLidarPitch);
    /**
     * @brief 根据安装高度和区域信息过滤无效雷达扫描线束
     *
     * @param p_area
     *
     */
    void getValidScan(s_Area& p_area, ScaleTablePtr& p_scaleTable);
    /**
     * @brief 角度转弧度
     *
     * @param p_fDeg 单位角度
     * @code
     *
     * @endcode
     * @return [float] \n
     * [details wirte here]
     *
     */
    double degTransRad(double p_fDeg);
    /**
     * @brief 弧度转角度
     *
     * @param p_fRad 单位弧度
     * @code
     *
     * @endcode
     * @return [float] \n
     * [details wirte here]
     *
     */
    double radTransDeg(double p_fRad);
    /**
     * @brief 设定雷达水平起始扫描角度时，适配水平角度分辨率
     *
     * @param p_area
     *
     */
    double matchLidarResolution(PXYZI* p_ptrCoordA);
    /**
     * @brief 将防护距离写入16*1800的刻度表
     *
     * @param p_iScaleId 当前水平扫描角度对应的刻度ID 0-1799
     * @param p_dProtectRange 最大最小防护距离
     * @param p_sNewArea 某区域的信息
     * @param p_sNewTable 某区域的刻度表
     *
     */
    void
    setTable(int p_iScaleId, float p_dProtectRange, s_Area& p_sNewArea, ScaleTablePtr& p_sNewTable);
    /**
     * @brief 参数初始化
     */
    void paramInit();
    /**
     * @brief 当上位机下发某组区域信息后，对该组区域进行解析，并添加到当前map中
     * @param p_vAreaAddId 内含区域ID
     */
    void add(std::vector<int>& p_vAreaAddId);
    /**
     * @brief 区域初始化，解算设定路径下所有的area文件，添加到map中
     *
     *
     */
    void start();
    /**
     * @brief 解算的区域刻度表一一写成.csv文件，用于测试时验证解算
     * @param p_iGroupID
     * @param p_iAreaID
     * @param p_scaleTable
     */
    void writeTableCsv(int p_iGroupID, int p_iAreaID, scaleTablePtr& p_scaleTable);
    /**
     * @brief 计算cos表，方便cos函数查表
     *
     *
     */
    void calculateCosTable();

    bool isHeightBelowGround(float& p_fHeight, int p_iScanIdx, ScaleTablePtr& p_scaleTable);

    bool isHeightUpArea(float& p_fHeight,
                        float& p_fAareHeight,
                        int p_iScanIdx,
                        ScaleTablePtr& p_scaleTable);
    /**
     * @brief 单个区域解算
     * @param p_sNewArea 某区域信息
     * @param p_sNewTable 该区域对应的刻度表
     */
    void singleAreaSolution(s_Area& p_sNewArea, ScaleTablePtr& p_sNewTable);
    /**
     * @brief 判断一条直线两个顶点的起始和终止角度，经过计算后，起始角度<终止角度
     * @param p_ptrCoordA A点指针
     * @param p_ptrCoordB B点指针
     * @param p_pCoordA A点
     * @param p_pCoordB B点
     */
    void
    handleTwoVertexLine(PXYZI* p_ptrCoordA, PXYZI* p_ptrCoordB, PXYZI p_pCoordA, PXYZI p_pCoordB);
    /**
     * @brief 计算一个顶点距离原点的距离及水平角度
     * @param p_pCoordA
     */
    void calculateVertexInfo(PXYZI& p_pCoordA);
    /**
     * @brief 计算某点的水平角度
     * @param x 坐标x值
     * @param y 坐标x值
     * @return 返回一个单位为角度的值
     */
    double getHorAngleDegree(float x, float y);
    /**
     * @brief 计算两点之间的距离
     * @param p_pCoordA
     * @param p_pCoordB
     * @return 两点之间水平距离
     */
    double getDistanceOfTwoPoints(PXYZI* p_pCoordA, PXYZI* p_pCoordB);
    /**
     * @brief 计算正弦定理 弃用
     * @param p_pCoordA
     * @param p_pCoordB
     * @param p_dAngleOfAngleAOB
     * @return 返回某个角度值
     */
    double SineTheoryGetSineB(PXYZI* p_pCoordA, PXYZI* p_pCoordB, double p_dAngleOfAngleAOB);
    /**
     * @brief 计算余弦定理
     * @param p_pCoordA
     * @param p_pCoordB
     * @param p_dAngleOfAngleAOB
     * @return 返回某个角度值 单位为角度
     */
    double CosTheoryGetCosB(PXYZI* p_pCoordA, PXYZI* p_pCoordB, double p_dAngleOfAngleAOB);
    /**
     * @brief 两个浮点数的比较
     * @param x
     * @param y
     * @return 相等时返回true，反之false
     */
    bool floatCompare(double x, double y);
    /**
     * @brief 根据正弦定理计算保护距离
     * @param p_dLidarHAngle 雷达水平扫描角度
     * @param p_dSinAngleOAB
     * @param p_ptrCoordA
     * @return 返回防护距离
     */
    double getProtectRange(double p_dLidarHAngle, double p_dSinAngleOAB, PXYZI* p_ptrCoordA);
    /**
     * @brief 更新最大防护距离
     * @param p_iScaleId
     * @param p_dProtectRange
     * @param p_sNewTable
     */
    void updateMaxProtectRange(int p_iScaleId, float p_dProtectRange, ScaleTablePtr& p_sNewTable);
    /**
     * @brief 更新最小防护距离
     * @param p_iScaleId
     * @param p_dProtectRange
     * @param p_sNewTable
     */
    void updateMinProtectRange(int p_iScaleId, float p_dProtectRange, ScaleTablePtr& p_sNewTable);
    /**
     * @brief 防护距离初始化
     * @param p_iScaleId
     * @param p_dProtectRange
     * @param p_sNewArea
     * @param p_sNewTable
     */
    void initProtectRange(int p_iScaleId,
                          float p_dProtectRange,
                          s_Area& p_sNewArea,
                          ScaleTablePtr& p_sNewTable);
    /**
     * @brief 计算雷达原点是否在区域内
     * @param p_sNewArea 某区域信息
     * @return 雷达原点在区域内返回true，反之false
     */
    bool isOriginInArea(s_Area& p_sNewArea);
    /**
     * @brief 查询某区域文件是否在设定路径中
     * @param p_iGroupID
     * @param p_iAreaID
     * @return 如果存在返回true，反之false
     */
    bool isAreaExists(int p_iGroupID, int p_iAreaID);
    /**
     * @brief 根据读取的area文件，填写area结构体
     * @param p_iLineIdx
     * @param p_vLineArray
     * @param p_sNewArea
     * @param p_sNewTable
     *
     */
    void fillInAreaCfg(int p_iLineIdx,
                       std::vector<std::string> p_vLineArray,
                       s_Area& p_sNewArea,
                       ScaleTablePtr& p_sNewTable);
    /**
     * @brief 读取对应区域文件信息
     * @param p_iGroupID
     * @param p_iAreaID
     * @param p_sNewArea
     * @param p_sNewTable
     */
    void
    readAreaCfgFile(int p_iGroupID, int p_iAreaID, s_Area& p_sNewArea, ScaleTablePtr& p_sNewTable);
    /**
     * @brief 根据角度sin值计算对应角度
     * @param p_dSinValue 角度sin值
     * @return [double] 返回单位为角度的一个值
     */
    double getAsinDegree(double p_dSinValue);
    /**
     * @brief 根据角度的cos值计算角度
     * @param p_dCosValue
     * @return [double]
     */
    double getCosDegree(double p_dCosValue);
    /**
     * @brief 一组区域的解算
     * @param p_iGroupID
     */
    void groupSolution(int p_iGroupID);
    /**
     * @brief 圆形或扇形的离散，目的为最终转为多边形来解算
     * @param p_sArea
     */
    void pointDiscretize(s_Area& p_sArea);
    /**
     * @brief 求解扇形离散时的起始与终止角度
     * @param p_dStartAngle
     * @param p_dEndAngle
     * @param p_sVerArray
     */
    void getFanScanScope(double& p_dStartAngle, double& p_dEndAngle, pcVerPtr& p_sVerArray);
    /**
     * @brief 处理小于0度的角度
     * @param p_dAngle
     */
    void calculateEdgeDeg(double& p_dAngle);
    /**
     * @brief 判断雷达扫描角度是否在两个顶点的角度范围内，不是则返回false
     * @param p_dLidarHAngle 雷达扫描角度
     * @param p_ptrCoordA
     * @param p_ptrCoordB
     * @return
     */
    bool lidarHAngleInLine(double p_dLidarHAngle, PXYZI* p_ptrCoordA, PXYZI* p_ptrCoordB);
    /**
     * @brief 计算两个顶点的角度差
     * @param p_dAngleOfAngleAOB
     * @return 如果角度差等于0或等于180，说明原点在坐标轴上，返回true
     */
    bool diffDegreeOfTwoVetex(double p_dAngleOfAngleAOB);
    /**
     * @brief 判断两条射线与区域的交点个数
     * @param p_viCrossPointNum 交点个数
     * @return 交点个数全为奇数时返回true
     */
    bool judgeCrossPointNum(std::vector<int> p_viCrossPointNum);
};
}  // namespace wj_od

#endif