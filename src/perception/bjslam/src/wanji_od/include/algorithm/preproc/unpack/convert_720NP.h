/*
 * @Description: 接收 deta_X * 1600 网络原始数据 判断数据是否异常 解算点云
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-13 09:11:05
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:27:12
 */
#ifndef _WANJI_CONVERT720F_H_
#define _WANJI_CONVERT720F_H_

#include "./convert.h"

namespace wanji_driverOD {

#pragma region 720F类
class Convert720F : public Convert720 {
  protected:
#pragma region 常量 / 结构体定义
    /***禁止使用uchar以外类型***/
    typedef struct RawHead
    {
        uint8_t header[2];  /// 0xffdd
        uint8_t scan_per_Block;
        uint8_t returnWaveNum;
        uint8_t block_per_Packet;
    } s_RawHead;

    typedef struct RawBlock
    {
        uint8_t rotation[2];
        uint8_t data[BLOCK_DATA_SIZE];
    } s_RawBlock;

    typedef struct RawAddtionMsg
    {
        uint8_t header[2];
        uint8_t rads[2];
        uint8_t time[6];
        uint8_t nsec[4];
        uint8_t angVel[6];
        uint8_t accel[6];
    } s_RawAddtionMsg;

    typedef struct RawPacket
    {
        s_RawHead head;
        s_RawBlock blocks[BLOCKS_PER_PACKET];
        s_RawAddtionMsg addmsg;
    } s_RawPacket;

#pragma endregion

  public:
#pragma region 公有函数
    Convert720F(
        uint32_t p_uiLidarID,
        boost::function<void(uint32_t, areaPcPtr&, PCOutPtr&, PCOutPtr&, int, int, u_int32_t)>
            p_fECb);

    ~Convert720F();

#pragma endregion

  protected:
    /**
     * @function: checkDataHeader_
     * @description: 检查原始数据头角度范围是否异常
     * @param {st_TWithTS&} p_pScanMsg 原始数据
     * @param {int&} p_iCurId 帧id
     * @return {*}
     * @others: {*}
     */
    virtual bool checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId);

    /**
     * @function: unpack_
     * @description: 逐包解算雷达数据
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 雷达原始网络数据
     * @param {double&} p_f64Time 每个点的时间
     * @param {s_PCloud} 原始数据
     * @param {s_PCloud&} pmid 中间线点云
     * @param {PCOutPtr&} pcOut 16*1800点云
     * @param {int&} p_iCnt 点号
     * @return {*}
     * @others: {*}
     */
    void unpack_(const s_LIDAR_RAW_DATA::DArray& p_rawData,
                 double& p_f64Time,
                 s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                 s_PCloud& pmid,
                 PCOutPtr& pcOut);
    /**
     * @function: unpackAddMsg_
     * @description: 逐包解算雷达数据中的附加数据
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 雷达原始网络数据
     * @param {AddtionMsgPtr&} padd 附加消息 GNSS+IMU
     * @return {*}
     * @others: {*}
     */
    void unpackAddMsg_(const s_LIDAR_RAW_DATA::DArray& p_rawData, AddtionMsgPtr& padd);
#pragma endregion
};
#pragma endregion

}  // namespace wanji_driverOD
#include "impl/convert_720NP.hpp"
#endif