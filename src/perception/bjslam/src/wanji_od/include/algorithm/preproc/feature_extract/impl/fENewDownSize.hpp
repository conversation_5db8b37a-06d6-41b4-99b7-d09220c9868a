#include <Eigen/Dense>
#include <pcl/point_types.h>

namespace wj_FE {

class fEDownSize {
  private:
    Eigen::Vector2f leaf_size_;

    std::vector<std::pair<u_int32_t, u_int32_t>> first_and_last_indices_vector;

  public:
    void setLeafSize(float lx, float ly)
    {
        leaf_size_[0] = lx;
        leaf_size_[1] = ly;
    }

    std::vector<std::pair<u_int32_t, u_int32_t>>* getPointSub()
    {
        return &first_and_last_indices_vector;
    }

    template <typename T>
    void applyFilter(boost::shared_ptr<pcl::PointCloud<T>> input_,
                     pcl::PointCloud<pcl::PointXYZI>& output)
    {
        // Has the input dataset been set already?
        if (!input_)
        {
            output.width = output.height = 0;
            output.points.clear();
            return;
        }

        // Copy the header (and thus the frame_id) + allocate enough space for points
        output.height = 1;       // downsampling breaks the organized structure
        output.is_dense = true;  // we filter out invalid points

        // 点云大小
        u_int32_t l_uRawSize = input_->points.size();
        // 点云分段
        // std::vector<std::pair<u_int32_t, u_int32_t> > first_and_last_indices_vector;
        first_and_last_indices_vector.clear();

        // 采样尺度
        float l_fDisSq = leaf_size_[0] * leaf_size_[0];
        // second点序号
        u_int32_t ind_s = 0;
        for (u_int32_t ind = 1; ind < l_uRawSize - 2;)
        {
            // 下一个点
            ind_s = ind + 1;
            // 如果在同一个分段内，继续移动到下一个
            while (pow(input_->points[ind].x - input_->points[ind_s].x, 2)
                       + pow(input_->points[ind].y - input_->points[ind_s].y, 2)
                   < l_fDisSq)
            {
                ++ind_s;
            }
            first_and_last_indices_vector.emplace_back(ind, ind_s - 1);
            ind = ind_s;
        }

        output.points.reserve(first_and_last_indices_vector.size());
        output.points.resize(first_and_last_indices_vector.size());

        u_int32_t index = 0;
        u_int32_t first_index, last_index;

        for (const auto& cp : first_and_last_indices_vector)
        {
            first_index = cp.first;
            last_index = cp.second + 1;

            Eigen::Vector4f centroid(Eigen::Vector4f::Zero());
            for (u_int32_t li = first_index; li < last_index; ++li)
                centroid += input_->points[li].getVector4fMap();

            centroid /= static_cast<float>(last_index - first_index);
            output.points[index].getVector4fMap() = centroid;

            ++index;
        }
        output.width = static_cast<u_int32_t>(output.points.size());
    }
};

class fESigmentCloudDownSize {
  private:
    Eigen::Vector2f leaf_size_;

    std::vector<std::pair<u_int32_t, u_int32_t>> first_and_last_indices_vector;

  public:
    void setLeafSize(float lx, float ly)
    {
        leaf_size_[0] = lx;
        leaf_size_[1] = ly;
    }

    std::vector<std::pair<u_int32_t, u_int32_t>>* getPointSub()
    {
        return &first_and_last_indices_vector;
    }

    template <typename T, typename BlkList>
    void applyFilter(boost::shared_ptr<pcl::PointCloud<T>> input_,
                     BlkList& p_vBlockList,
                     pcl::PointCloud<pcl::PointXYZI>& output)
    {
        // Has the input dataset been set already?
        if (!input_)
        {
            output.width = output.height = 0;
            output.points.clear();
            return;
        }

        // Copy the header (and thus the frame_id) + allocate enough space for points
        output.height = 1;       // downsampling breaks the organized structure
        output.is_dense = true;  // we filter out invalid points

        // 点云大小
        u_int32_t l_uRawSize = input_->points.size();
        // 点云分段
        // std::vector<std::pair<u_int32_t, u_int32_t> > first_and_last_indices_vector;
        first_and_last_indices_vector.clear();

        // 采样尺度
        float l_fDisSq = leaf_size_[0] * leaf_size_[0];
        // second点序号
        u_int32_t ind_s = 0;
        for (u_int32_t ind = 1; ind < l_uRawSize - 2;)
        {
            // 下一个点
            ind_s = ind + 1;
            // 如果在同一个分段内，继续移动到下一个
            while (pow(input_->points[ind].x - input_->points[ind_s].x, 2)
                       + pow(input_->points[ind].y - input_->points[ind_s].y, 2)
                   < l_fDisSq)
            {
                ++ind_s;
            }
            first_and_last_indices_vector.emplace_back(ind, ind_s - 1);
            ind = ind_s;
        }

        output.points.reserve(first_and_last_indices_vector.size());
        output.points.resize(first_and_last_indices_vector.size());

        u_int32_t index = 0;
        u_int32_t first_index, last_index;

        for (const auto& cp : first_and_last_indices_vector)
        {
            first_index = cp.first;
            last_index = cp.second + 1;

            Eigen::Vector4f centroid(Eigen::Vector4f::Zero());
            for (u_int32_t li = first_index; li < last_index; ++li)
                centroid += input_->points[li].getVector4fMap();

            centroid /= static_cast<float>(last_index - first_index);
            output.points[index].getVector4fMap() = centroid;

            ++index;
        }
        output.width = static_cast<u_int32_t>(output.points.size());
    }
};

}  // namespace wj_FE
