/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2021-12-01 15:58:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 18:28:28
 */
#pragma once
#include "../type/type_device.h"
#include "../type/type_pose.h"
#include <mutex>
#include <string>
#include <wjod_log.h>

namespace wj_slam {

/** 外置速度模板 eg wheelVel imuVel ... */
typedef struct s_VelConfig
{
    s_VelConfig()
    {
        m_stWheelTwist.reset();
    }
    // 打印
    void log() {}

    s_fuseTwist m_stWheelTwist;  // 轮式速度
} s_VelConfig;

/** 建图/定位-位置配置参数模板 */
typedef struct s_PoseConfig
{
    s_PoseConfig()
    {
        m_stSetPose.reset();
        m_stCurrPose.reset();
        m_stLastPose.reset();
        m_stCurrPoseWJ.reset();
        m_stLastPoseWJ.reset();
        m_sPoseFilePath = "";
        m_bSaveWholePath = false;
        m_mtRequestSend.reset(new std::mutex());
        m_mtRequestSendWJ.reset(new std::mutex());
    }
    // 打印
    void log()
    {
        LOGA(WINFO, "[param] PoseFilePath", m_sPoseFilePath);
        LOGA(WINFO,
             "[param] Set Pos: XYA=[{:.3f},{:.3f},{:.3f}]",
             m_stSetPose.x(),
             m_stSetPose.y(),
             m_stSetPose.yaw());
    }

    s_POSE6D m_stSetPose;                           // 设定位姿势
    s_PoseWithTwist m_stCurrPose;                   // 用于SICK协议发送
    s_PoseWithTwist m_stLastPose;                   // 用于SICK协议发送
    s_PoseWithTwist m_stCurrPoseWJ;                 // 用于WJ协议发送
    s_PoseWithTwist m_stLastPoseWJ;                 // 用于WJ协议发送
    std::string m_sPoseFilePath;                    // 位姿保存读取路径
    bool m_bSaveWholePath;                          // 记录定位路径
    std::shared_ptr<std::mutex> m_mtRequestSend;    // 是否需要发送:SICK要数
    std::shared_ptr<std::mutex> m_mtRequestSendWJ;  // 是否需要发送:WJ要数
} s_PoseConfig;

/** 对外通信参数 */
typedef struct s_CommunicateConfig
{
    s_CommunicateConfig()
    {
        m_dev.m_sLocalIP = "0.0.0.0";  // 任意IP
        m_dev.m_uiLocalPort = 2112;
        m_dev.m_sPcapName = "NOSET";
        m_dev.m_sDevType = "WheelSick";
        m_dev.m_sNetName = "NET2";  // 以WLC-703为默认值
        m_stTrans.reset();
        m_fMymap[0] = 0.0;
        m_fMymap[1] = 0.0;
        m_fMymap[2] = 0.0;
        m_fTomap[0] = 0.0;
        m_fTomap[1] = 0.0;
        m_fTomap[2] = 0.0;
    }
    // 打印
    void log()
    {
        LOGA(WINFO, "[param] AGV IP: {}", m_dev.m_sLocalIP);
        LOGA(WINFO, "[param] AGV Port: {}", m_dev.m_uiLocalPort);
        LOGA(WINFO,
             "[param] Map Trans: XYA=[{:.3f},{:.3f},{:.3f}]",
             m_stTrans.x(),
             m_stTrans.y(),
             m_stTrans.yaw());
    }

    s_DevCfg m_dev;
    s_POSE6D m_stLidarToAgv;  // 车体校正参数
    s_POSE6D m_stTrans;       // 地图校正参数
    float m_fMymap[3];
    float m_fTomap[3];
} s_CommunicateConfig;

/** 位姿校验参数 配置模板 */
typedef struct s_PoseCheckConfig
{
    s_PoseCheckConfig()
    {
        m_bOpenPoseCheck = false;
        m_bOnlyWheelOdom = false;
        m_bSafeModel = false;
        m_bUseCurb = false;
        m_bTestCurb = false;
        m_fPrecPosFarDist = 30.0;
        m_vNoCheckAreaList.clear();

        m_stPoseCheck.reset();
        m_stPoseCheck.m_Pose.setX(0.05);
        m_stPoseCheck.m_Pose.setY(0.05);
        m_stPoseCheck.m_Pose.setZ(0.0);
        m_stPoseCheck.m_Pose.setRPY(10.0, 10.0, 2.0);
        m_stPoseCheck.m_Twist.setX(0.05);
        m_stPoseCheck.m_Twist.setY(0.05);
        m_stPoseCheck.m_Twist.setZ(0.0);
        m_stPoseCheck.m_Twist.setRPY(1.0, 1.0, 1.0);

        m_stCurbCheck.reset();
        m_stCurbCheck.m_Pose.setX(0.05);
        m_stCurbCheck.m_Pose.setY(0.05);
        m_stCurbCheck.m_Pose.setZ(0.0);
        m_stCurbCheck.m_Pose.setRPY(10.0, 10.0, 2.0);
        m_stCurbCheck.m_Twist.setX(0.05);
        m_stCurbCheck.m_Twist.setY(0.05);
        m_stCurbCheck.m_Twist.setZ(0.0);
        m_stCurbCheck.m_Twist.setRPY(1.0, 1.0, 1.0);
    }
    // 打印
    void log()
    {
        LOGA(WINFO, "[param] openPoseCheck: {}", m_bOpenPoseCheck);
        LOGA(WINFO, "[param] onlyWheelOdom: {}", m_bOnlyWheelOdom);
        LOGA(WINFO, "[param] safeModel: {}", m_bSafeModel);
        LOGA(WINFO, "[param] useCurb: {}", m_bUseCurb);
        LOGA(WINFO, "[param] testCurb: {}", m_bTestCurb);
        LOGA(WINFO, "[param] wheelOdomFarDist: {}", m_fPrecPosFarDist);

        for (size_t i = 0; m_vNoCheckAreaList.size(); i++)
        {
            if (m_vNoCheckAreaList[i].size() == 4)
                LOGA(WINFO,
                     "[param] noCheckArea[{}]: xmin {} | xmax {} | ymin {} | ymax {} ",
                     i,
                     m_vNoCheckAreaList[i][0],
                     m_vNoCheckAreaList[i][1],
                     m_vNoCheckAreaList[i][2],
                     m_vNoCheckAreaList[i][3]);
            else
                LOGA(WINFO,
                     "[param] error noCheckArea[{}] size: {} ",
                     i,
                     m_vNoCheckAreaList[i].size());
        }

        LOGA(WINFO,
             "[param] Set slamPosCheck Pos Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             m_stPoseCheck.m_Pose.x(),
             m_stPoseCheck.m_Pose.y(),
             m_stPoseCheck.m_Pose.roll(),
             m_stPoseCheck.m_Pose.pitch(),
             m_stPoseCheck.m_Pose.yaw());
        LOGA(WINFO,
             "[param] Set slamPosCheck Vel Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             m_stPoseCheck.m_Twist.x(),
             m_stPoseCheck.m_Twist.y(),
             m_stPoseCheck.m_Twist.roll(),
             m_stPoseCheck.m_Twist.pitch(),
             m_stPoseCheck.m_Twist.yaw());

        LOGA(WINFO,
             "[param] Set CurbPosCheck Pos Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             m_stCurbCheck.m_Pose.x(),
             m_stCurbCheck.m_Pose.y(),
             m_stCurbCheck.m_Pose.roll(),
             m_stCurbCheck.m_Pose.pitch(),
             m_stCurbCheck.m_Pose.yaw());
        LOGA(WINFO,
             "[param] Set CurbPosCheck Vel Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             m_stCurbCheck.m_Twist.x(),
             m_stCurbCheck.m_Twist.y(),
             m_stCurbCheck.m_Twist.roll(),
             m_stCurbCheck.m_Twist.pitch(),
             m_stCurbCheck.m_Twist.yaw());
    }

    bool m_bOpenPoseCheck;  // 设置是否启动位姿校验 即 是否使用轮式里程计等外部融合速度
    bool m_bOnlyWheelOdom;  // 设置纯里程计模式
    bool m_bSafeModel;  // 设置安全模式 - slam定位失败后是否可无限使用里程计模式
    bool m_bUseCurb;   // 设置是否使用车道线+马路伢子+中间线
    bool m_bTestCurb;  // 设置是否启动测试车道线+马路伢子+中间线匹配情况
    float m_fPrecPosFarDist;                             // 设置预估Pos下AGV最远行进距离
    std::vector<std::vector<float>> m_vNoCheckAreaList;  // 设置不校验区域List xmin xmax ymin ymax

    s_PoseWithTwist m_stPoseCheck;  // slamPose校验阈值
    s_PoseWithTwist m_stCurbCheck;  // 马路伢子优化位姿校验 m_Pose为阈值 m_Twist为速度阈值
                                    // 用于选择m_Pose阈值
} s_PoseCheckConfig;
}  // namespace wj_slam