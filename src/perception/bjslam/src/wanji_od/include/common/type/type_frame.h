/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:38:01
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-02-09 14:57:43
 */
#pragma once
#include "type_feature.h"
#include "type_pose.h"

namespace wj_slam {

template <typename T> class KEYFRAME {
  private:
    typedef pcl::PointCloud<T> PC_;
    typedef typename FEATURE_PAIR<T>::Ptr FEATURE_PAIR_PTR;

  public:
    FEATURE_PAIR_PTR m_pFeature;
    s_POSE6D m_Pose;
    bool m_bIsKeyFrame = false;
    KEYFRAME()
    {
        m_pFeature.reset(new FEATURE_PAIR<T>());
        m_bIsKeyFrame = false;
    }
    void free()
    {
        if (m_pFeature)
            m_pFeature->free();
        return;
    }

    /**
     * @function: =
     * @description: 深拷贝
     * @param {*}
     * @return {*}
     * @others: null
     */
    KEYFRAME& operator=(const KEYFRAME& p_in)
    {
        // wjPrint(COLOR_RED, "deep copy KEYFRAME", NULL);
        *(this->m_pFeature) = *p_in.m_pFeature;
        this->m_Pose = p_in.m_Pose;
        return *this;
    }
    double x()
    {
        return this->m_Pose.x();
    }
    double y()
    {
        return this->m_Pose.y();
    }
    double z()
    {
        return this->m_Pose.z();
    }
    double yaw()
    {
        return this->m_Pose.yaw();
    }
    double pitch()
    {
        return this->m_Pose.pitch();
    }
    double roll()
    {
        return this->m_Pose.roll();
    }

    // /**
    //  * @function:
    //  * @description: 浅拷贝
    //  * @param {*}
    //  * @return {*}
    //  * @others: null
    //  */
    // KEYFRAME* operator=(const KEYFRAME* p_in)
    // {
    //     wjPrint(COLOR_RED, "low copy KEYFRAME", NULL);
    //     this->m_pFeature = p_in->m_pFeature;
    //     this->m_Pose = p_in->m_Pose;
    //     return this;
    // }

    ~KEYFRAME()
    {
        // this->free();
        // std::cout << "~KEYFRAME : " << this << std::endl;
    }
    typedef boost::shared_ptr<KEYFRAME<T>> Ptr;
};

}  // namespace wj_slam