/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2022-02-09 14:12:38
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-25 14:54:20
 */
#pragma once

namespace wj_slam {

/**
 * StandByMode<默认状态/无工作模式>;
 * InitMapMode<初始建图模式>;
 * ContMapMode<连续建图模式>;
 * LocatMode<定位模式>;
 * UpdateMapMode<更新地图模式>
 */
enum WorkMode { StandByMode = 0, InitMapMode, ContMapMode, LocatMode, UpdateMapMode };

/**
 * INDOOR<室内场景>;
 * OUTDOOR<室外场景>;
 * VACROUS<杂乱场景>;
 * LABOR<调试场景>
 */
enum ScenMode { INDOOR = 0, OUTDOOR, VACROUS, LABOR };

/**
 * Local<本机时间源（SC、NTP)>;
 * Lidar<雷达时间源（RTC、Satellite）>;
 * Other<预留>;
 */
enum TimeSource { Local, Lidar, Other };

/**
 * Noise<噪声状态/异常状态/初始状态>;
 * Uniform<匀速状态>;
 * Length_Enough<移动长度足够>;
 * Ang_Enough<转角足够>;
 * Time_Enough<时间足够>;
 */
enum MoveStatus {
    Noise = 0,
    Uniform = 1 << 0,
    Length_Enough = 1 << 1,
    Ang_Enough = 1 << 2,
    Time_Enough = 1 << 3
};

/**
 * Default<默认状态/无定位结果/正在定位>;
 * InitialPose<初始定位位姿>;
 * ContinuePose<连续定位位姿>;
 * VirtualPose<虚拟定位>;
 * StopPose<无效定位>;
 * SettingPose<外部设置位姿>
 */
enum PoseStatus { Default = 0, InitialPose, ContinuePose, VirtualPose, StopPose, SettingPose };

/**
 * InitialSICK<默认状态/初始定位位姿>;
 * ContinueSICK<连续定位位姿>;
 * VirtualSICK<虚拟定位>;
 * StopSICK<停止定位>;
 * InvalidSICK<无效位姿>
 */
enum SICKPoseStatus { InitialSICK = 0, ContinueSICK, VirtualSICK, StopSICK, InvalidSICK };

/**
 * InvalidWJ<默认状态/无效定位位姿>;
 * ContinueWJ<连续定位位姿>;
 * VirtualWJ<虚拟定位>;
 * InitialWJ<初始定位>;
 */
enum WJPoseStatus { InvalidWJ = 0, ContinueWJ, VirtualWJ, InitialWJ };

/**
 * Unknown<默认状态/未设置>;
 * Relative<相对坐标系 - 基于传感器>;
 * Absolute<绝对坐标系 -基于地图>;
 */
enum TwistStatus { Unknown = 0, Relative, Absolute };

/**
 * Valid<定位有效>;
 * Novalid<定位无效>;
 */
enum PoseCheckStatus { Valid = 0, Novalid };

/**
 * DEFAULTSTATUS<默认最低级别>
 * WAITCONNECT<等待连接>
 * VIRTUALDEV<虚拟设备>
 *
 * DATADATAERROR<设备数据错误：超时/扫描异常>
 * PCAPRUN<正在播放>
 * PCAPSTOP<暂停播放>
 * DEVCONNECT<识别设备: 网络无误且连接>
 *
 * LOADPARAMERROR<设备错误：雷达超时/垂直偏心加载异常>
 * GETPARAMERROR<获取参数错误：eg: SN/雷达类型/数据表>
 * NOFINDDEV<设备未识别：PING未识别 或 未连接>
 * NETNOALIVE<网卡未活跃>
 * NETNOIP<网卡未活跃且未配置IP>
 * NETIPERROR<网卡IP错误: PC-IP和网卡实时IP/配置IP不符>
 * NETSETERROR<网卡设置错误: 未设置>
 * PCAPERROR<PCPA错误：不存在或异常>
 * PCAPOVER<播放完毕>
 * IPINFOERROR<网络配置错误: PC-IP 和 雷达IP 非同网段>
 */
enum DevStatus {
    DEFAULTSTATUS, /*默认最低级别*/
    WAITCONNECT,   /*等待连接*/
    VIRTUALDEV,    /*虚拟设备*/

    DATAWAIT,      /*等待雷达数据，5者允许跨级显示*/
    DATADATAERROR, /*设备数据错误：超时/扫描异常，5者允许跨级显示*/
    PCAPRUN,       /*正在播放，5者允许跨级显示*/
    PCAPSTOP,      /*暂停播放，5者允许跨级显示*/
    DEVCONNECT,    /*识别设备: 网络无误且连接，5者允许跨级显示*/

    LOADPARAMERROR, /*垂直偏心获取/加载异常*/
    GETPARAMERROR,  /*获取参数错误：eg: SN/雷达类型/数据表*/
    NOFINDDEV,      /*设备未识别：PING未识别 或 未连接*/
    NETNOALIVE,     /*网卡未活跃*/
    NETNOIP,        /*网卡未活跃且未配置IP*/
    NETIPERROR,     /*网卡IP错误: PC-IP和网卡实时IP/配置IP不符*/
    NETSETERROR,    /*网卡设置错误: 未设置*/
    PCAPOVER,       /*播放完毕*/
    PCAPERROR,      /*PCPA错误：不存在或异常*/
    IPINFOERROR,    /*网络配置错误: PC-IP 和 雷达IP 非同网段*/
};
}  // namespace wj_slam