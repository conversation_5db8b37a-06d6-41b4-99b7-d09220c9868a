#pragma once

#include <vector>
// boost
#include <boost/thread.hpp>
// 点云
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
// 获取参数
// #include <ros/package.h>
#include <yaml-cpp/yaml.h>
// 计算
#include <Eigen/Dense>

#pragma region lock
typedef boost::shared_mutex WR_Mutex;
typedef boost::unique_lock<WR_Mutex> writeLock;
typedef boost::shared_lock<WR_Mutex> readLock;
#pragma endregion

static const double M_2PI = 6.2831853071796;

// 1/180.0*M_PI
static const double D2R = 0.017453292519943;

// 1*180.0/M_PI
static const double R2D = 57.295779513082;

#pragma region yamlcpp
/**
 * @description: 返回统一的配置文件路径
 * @return 路径
 */
// std::string getConfigFile()
// {
//     return ros::package::getPath("wslam_pretreat") + "/cfg/config.yaml";
// }

/**
 * @description: 获取参数
 * @param config yaml-node对象
 * @param name 参数名
 * @param param 返回到对象
 * @param default_value 默认值
 * @return {*}
 */
template <typename T>
void getParam(const YAML::Node& config, const std::string& name, T& param, const T& default_value)
{
    if (config[name.c_str()])
    {
        param = config[name.c_str()].as<T>();
    }
    else
    {
        param = default_value;
    }
}

/**
 * @description: 获取参数
 * @param config yaml-node对象
 * @param type 参数类
 * @param name 参数名
 * @param param 返回到对象
 * @param default_value 默认值
 * @return {*}
 */
template <typename T>
void getParam(const YAML::Node& config,
              const std::string& type,
              const std::string& name,
              T& param,
              const T& default_value)
{
    if (config[type.c_str()][name.c_str()])
    {
        param = config[type.c_str()][name.c_str()].as<T>();
    }
    else
    {
        param = default_value;
    }
}
#pragma endregion

inline Eigen::Vector3d quatToEuler(Eigen::Quaterniond& p_quat)
{
    Eigen::Vector3d l_vEuler = p_quat.matrix().eulerAngles(0, 1, 2);
    return l_vEuler;
}

// 返回360度内的角度
inline void degI360(float& angle)
{
    while (angle > 360.0f)
        angle -= 360.0f;
    while (angle < 0.0f)
        angle += 360.0f;
}

// 点到点的距离pcl
template <typename T1, typename T2> inline double calcDistP2P_point(T1& pcur, T2& ptra)
{
    return sqrt(pow(pcur.x - ptra.x, 2) + pow(pcur.y - ptra.y, 2) + pow(pcur.z - ptra.z, 2));
}

// 点到点的距离pcl
template <typename T1, typename T2> inline double calcXYDistP2P_point(T1& pcur, T2& ptra)
{
    return sqrt(pow(pcur.x - ptra.x, 2) + pow(pcur.y - ptra.y, 2));
}

// 清理点云的内存占用
template <typename T> inline void clearPointCloud(pcl::PointCloud<T>& pc)
{
    pcl::PointCloud<T>().swap(pc);
}
