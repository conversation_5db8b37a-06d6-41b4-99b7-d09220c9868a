/*
 * @Descripttion:
 * @version:
 * @Author: sueRimn
 * @Date: 2021-04-28 10:38:21
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-04-14 18:46:15
 */
#pragma once

#include <iostream>

#define WJCOL_RESET "\033[0m"
#define WJCOL_BLACK "\033[30m"              /* Black */
#define WJCOL_RED "\033[31m"                /* Red */
#define WJCOL_GREEN "\033[32m"              /* Green */
#define WJCOL_YELLOW "\033[33m"             /* Yellow */
#define WJCOL_BLUE "\033[34m"               /* Blue */
#define WJCOL_MAGENTA "\033[35m"            /* Magenta */
#define WJCOL_CYAN "\033[36m"               /* Cyan */
#define WJCOL_WHITE "\033[37m"              /* White */
#define WJCOL_BOLDBLACK "\033[1m\033[30m"   /* Bold Black */
#define WJCOL_BOLDRED "\033[1m\033[31m"     /* Bold Red */
#define WJCOL_BOLDGREEN "\033[1m\033[32m"   /* Bold Green */
#define WJCOL_BOLDYELLOW "\033[1m\033[33m"  /* Bold Yellow */
#define WJCOL_BOLDBLUE "\033[1m\033[34m"    /* Bold Blue */
#define WJCOL_BOLDMAGENTA "\033[1m\033[35m" /* Bold Magenta */
#define WJCOL_BOLDCYAN "\033[1m\033[36m"    /* Bold Cyan */
#define WJCOL_BOLDWHITE "\033[1m\033[37m"   /* Bold White */
typedef std::string COLOR;
#define IS_WJCOL_GROUP(COLOR)                                                                      \
    (((COLOR) == WJCOL_RESET) || ((COLOR) == WJCOL_BLACK) || ((COLOR) == WJCOL_RED)                \
     || ((COLOR) == WJCOL_GREEN) || ((COLOR) == WJCOL_YELLOW) || ((COLOR) == WJCOL_BLUE)           \
     || ((COLOR) == WJCOL_MAGENTA) || ((COLOR) == WJCOL_CYAN) || ((COLOR) == WJCOL_WHITE)          \
     || ((COLOR) == WJCOL_BOLDBLACK) || ((COLOR) == WJCOL_BOLDRED) || ((COLOR) == WJCOL_BOLDGREEN) \
     || ((COLOR) == WJCOL_BOLDYELLOW) || ((COLOR) == WJCOL_BOLDBLUE)                               \
     || ((COLOR) == WJCOL_BOLDGREEN) || ((COLOR) == WJCOL_BOLDMAGENTA)                             \
     || ((COLOR) == WJCOL_BOLDCYAN) || ((COLOR) == WJCOL_BOLDWHITE))

template <typename T>
void wjPrint(COLOR p_Color = WJCOL_RESET, std::string p_sStr = "", T p_Data = NULL)
{
    if (IS_WJCOL_GROUP(p_Color))
    {
        std::cout << p_Color << p_sStr << " : " << p_Data << WJCOL_RESET << std::endl;
        return;
    }
    std::cout << p_sStr << " : " << p_Data << std::endl;
}
template <typename T> void wjPrintError(std::string p_sStr, T p_Data)
{
    std::cerr << WJCOL_RED << "[ ERROR ] " << p_sStr << " : " << p_Data << WJCOL_RESET << std::endl;
}