/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-05-16 16:15:05
 * @LastEditTime: 2022-10-28 12:22:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/src/net_app/protocol.cpp
 */
#include "net_app/protocol.h"

ProtocolOD::ProtocolOD(s_NetMsg& p_stNetMsg,
                   boost::function<void(char*, int)> p_sendCallback,
                   boost::function<void(int, std::vector<int>&)> p_actionCallback)
    : c_stNetMsg_(p_stNetMsg), sendCallBack_(p_sendCallback), c_actionCallback_(p_actionCallback)
{
    c_pWJProtocol_ = new WJProtocolOD(c_stNetMsg_, sendCallBack_, c_actionCallback_);
};

void ProtocolOD::procCmdInThread()
{
    std::thread t(&ProtocolOD::procCmd, this);
    t.detach();
}
#pragma region "判断协议类型并指定到帧头位置"
int ProtocolOD::recognizeProtocolType(int& p_iOffset)
{
    int l_iOffsetTmp = p_iOffset;
    int l_iCMDLen = 0;
    while (l_iOffsetTmp != (int)c_stNetMsg_.m_uiDataLen)
    {
        if ((u_char)(c_stNetMsg_.m_aucBuf[l_iOffsetTmp]) == 0xFF
            && (u_char)(c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 1) % NET_LENGTH_MAX]) == 0xAA)
        {
            l_iCMDLen = c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 2) % NET_LENGTH_MAX] << 8
                        | c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 3) % NET_LENGTH_MAX];
            if (c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + l_iCMDLen + 2) % NET_LENGTH_MAX] == 0xEE
                && c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + l_iCMDLen + 3) % NET_LENGTH_MAX] == 0xEE)
            {
                std::lock_guard<std::mutex> l_mtx(c_mtxLock_);
                if (p_iOffset == (int)c_stNetMsg_.m_uiDataLen)
                    return 0;
                p_iOffset = l_iOffsetTmp;
                return WJPROTOCOL;
            }
            else
            {
                printf("length fail %d %d | %d %d\n",
                       c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 2) % NET_LENGTH_MAX],
                       c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 3) % NET_LENGTH_MAX],
                       c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 22) % NET_LENGTH_MAX],
                       c_stNetMsg_.m_aucBuf[(l_iOffsetTmp + 23) % NET_LENGTH_MAX]);
                l_iOffsetTmp++;
                l_iOffsetTmp %= NET_LENGTH_MAX;
            }
        }
        else
        {
            l_iOffsetTmp++;
            l_iOffsetTmp %= NET_LENGTH_MAX;
        }
    }
    return 0;
}
#pragma endregion

void ProtocolOD::procCmd()
{
    int l_iSendNum = 0;
    int l_iSta = -1, l_iEnd = -1;
    int l_iDataProcLen = 0;
    char l_acBuf[100] = {0};
    int l_iProctocolType = 0;
    u_char* l_pucBufTmp = NULL;
    c_bProcThrRun_ = true;
    c_bProcThrRunOver_ = false;
    while (c_bProcThrRun_)
    {
        while (c_iCurProcOffset_ != (int)c_stNetMsg_.m_uiDataLen)
        {
            if (!c_bProcThrRun_)
            {
                c_bProcThrRunOver_ = true;
                return;
            }
            l_iProctocolType = recognizeProtocolType(c_iCurProcOffset_);
            if (WJPROTOCOL == l_iProctocolType)
            {
                l_pucBufTmp = c_pWJProtocol_->getWJCMD(c_iCurProcOffset_, c_mtxLock_);
                if (l_pucBufTmp)
                {
                    // std::cout << "send by WJ \n" << std::endl;
                    l_iSendNum = c_pWJProtocol_->selectWJProtocol(l_pucBufTmp, l_acBuf);
                    // std::cout << "send data[" << l_iSendNum << "]" << std::endl;
                    sendCallBack_(l_acBuf, l_iSendNum);
                }
                else
                    LOGA(WERROR, "{} 服务器: 协议获取异常/校验未通过, 请检查!");
            }

            if (l_pucBufTmp)
            {
                delete[] l_pucBufTmp;
                l_pucBufTmp = NULL;
            }

            l_iSta = -1;
            l_iEnd = -1;
        }
        usleep(1000);
    }
    c_bProcThrRunOver_ = true;
    return;
}
