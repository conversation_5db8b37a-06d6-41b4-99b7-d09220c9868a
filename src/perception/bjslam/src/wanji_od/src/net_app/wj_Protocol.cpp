/*
 * @Description:
 * @Version: 1.0
 * @Autor: senlin
 * @Date: 2022-10-31 16:08:03
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:36:15
 */
#include "net_app/wj_Protocol.h"
#include <stdio.h>
#include <unistd.h>

u_char* WJProtocolOD::getWJCMD(int& p_iOffset, std::mutex& p_lock)
{
    int l_iSta = -1, l_iEnd = -1;
    int l_iCmdLen = 0;
    l_iCmdLen = TOUINT16(c_stNetMsg_.m_aucBuf[(p_iOffset + 2) % NET_LENGTH_MAX],
                         c_stNetMsg_.m_aucBuf[(p_iOffset + 3) % NET_LENGTH_MAX]);
    if (c_stNetMsg_.m_aucBuf[p_iOffset] == 0xFF
        && c_stNetMsg_.m_aucBuf[(p_iOffset + 1) % NET_LENGTH_MAX] == 0xAA
        && c_stNetMsg_.m_aucBuf[(p_iOffset + l_iCmdLen + 2) % NET_LENGTH_MAX] == 0xEE
        && c_stNetMsg_.m_aucBuf[(p_iOffset + l_iCmdLen + 3) % NET_LENGTH_MAX] == 0xEE)
    {
        // 区别于wj/sick 这里须加入帧头帧尾
        l_iSta = p_iOffset % NET_LENGTH_MAX;
        l_iEnd = (l_iSta + l_iCmdLen + 4) % NET_LENGTH_MAX;
    }

    if (l_iSta != -1 && l_iEnd != -1)
    {
        int l_iDataProcLen = l_iCmdLen + 4;

        u_char* l_pcBufTmp = new u_char[l_iDataProcLen];
        if (l_iSta > l_iEnd)
        {
            memcpy(l_pcBufTmp, &c_stNetMsg_.m_aucBuf[l_iSta], NET_LENGTH_MAX - l_iSta);
            if (l_iEnd > 0)
                memcpy(&l_pcBufTmp[NET_LENGTH_MAX - l_iSta], c_stNetMsg_.m_aucBuf, l_iEnd);
        }
        else
            memcpy(l_pcBufTmp, &c_stNetMsg_.m_aucBuf[l_iSta], l_iDataProcLen);

        {
            std::lock_guard<std::mutex> l_mtx(p_lock);
            if (p_iOffset == (int)c_stNetMsg_.m_uiDataLen)
            {
                delete l_pcBufTmp;
                return NULL;
            }
            p_iOffset += l_iDataProcLen;
            p_iOffset %= NET_LENGTH_MAX;
        }

        if (l_pcBufTmp[l_iDataProcLen - 3] == checkXOR(&l_pcBufTmp[2], l_iDataProcLen - 6))  //
        {
            return l_pcBufTmp;
        }
        else
        {
            delete l_pcBufTmp;
            return NULL;
        }
    }

    return NULL;
}

void WJProtocolOD::calcWJProtocolHead_(u_char* p_pcBufCMD, char* p_pcBufResponse, int& p_iLen)
{
    p_iLen = 26;
    memcpy(&p_pcBufResponse[0], p_pcBufCMD, p_iLen);
    p_pcBufResponse[11] = 2;
}

int WJProtocolOD::selectWJProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse)
{
    int l_iSta = 11;
    int l_iSubscript = 26;
    int l_iFatherCMDID;
    int l_iSonCMDID;
    int l_iLen = 0;

    // 720避障的设备类型改成020B 719改成030B
    if (p_pcBufCMD[l_iSta] == 0x01 && p_pcBufCMD[l_iSta + 1] == 0x02
        && p_pcBufCMD[l_iSta + 2] == 0x0B)
    {
        l_iFatherCMDID = p_pcBufCMD[l_iSta + 11];
        // 选择协议种类
        switch (l_iFatherCMDID)
        {
            case WJCMDTYPE::CMD_SLAVER:
                return selectSlaverProtocol(p_pcBufCMD, p_pcBufResponse);
                break;
            default: return 0; break;
        }

        l_iSonCMDID = p_pcBufCMD[l_iSta + 12];
        // 拷贝数组0-25 l_iLen从26开始
        calcWJProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iLen);
        switch (l_iSonCMDID)
        {
            default: return 0; break;
        }
    }
    printf("SLAM协议异常: 未定义 %d - %d, 请检查!\n",
           (int)p_pcBufCMD[l_iSta + 11],
           (int)p_pcBufCMD[l_iSta + 12]);
    return 0;
}

void WJProtocolOD::calcWJProtocolTail_(char* p_pcBuf, int& p_iLen)
{
    p_pcBuf[2] = (p_iLen & 0xff00) >> 8;
    p_pcBuf[3] = p_iLen & 0xff;
    p_pcBuf[p_iLen++] = 0;
    p_pcBuf[p_iLen++] = checkXOR((u_char*)&p_pcBuf[2], p_iLen - 3);
    p_pcBuf[p_iLen++] = 0xEE;
    p_pcBuf[p_iLen++] = 0xEE;
}

void WJProtocolOD::sendRecvSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes)
{
    // 多扩10个字节 + 27 28 29 | 校验2 帧尾2 +末尾\0
    char* l_acBuf = new char[p_iLen + 10];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);

    int l_iLen = p_iLen;
    l_acBuf[l_iLen++] = p_iRes;  // 从第27位开始，0代表失败， 1代表成功
    // l_acBuf[l_iLen++] = p_iRes;
    fillAskField(l_acBuf, l_iLen, 28);
    calcWJProtocolTail_(l_acBuf, l_iLen);
    sendCallback_(l_acBuf, l_iLen);
    delete l_acBuf;
}

void WJProtocolOD::fillInAreaCfg(int p_iLineIdx,
                                 std::vector<std::string> p_vLineArray,
                                 char* p_pcBufResponse)
{
    switch (p_iLineIdx)
    {
        case 1: fillStringTo8Bytes(26, p_vLineArray[1], p_pcBufResponse); break;
        case 2: fillStringTo8Bytes(27, p_vLineArray[1], p_pcBufResponse); break;
        case 3:
            fillStringTo8Bytes(35, p_vLineArray[1], p_pcBufResponse);  // 区域形状
            // fillStringTo16Bytes(36, "1", p_pcBufResponse);  // 本区域总包数  暂时没用到这个值
            // fillStringTo16Bytes(37, "1", p_pcBufResponse);  // 当前包数 暂时没用到这个值
            break;
        case 4:
            fillStringTo16Bytes(30, p_vLineArray[1], p_pcBufResponse);  // 区域最小高度
            break;
        case 5:
            fillStringTo16Bytes(32, p_vLineArray[1], p_pcBufResponse);  // 区域最大高度
            break;
        case 6:
            fillStringTo8Bytes(34, p_vLineArray[1], p_pcBufResponse);  // 响应帧数
            break;
        case 7:
            fillStringTo16Bytes(38, p_vLineArray[1], p_pcBufResponse);  // 本区域总点数
            break;
        default: break;
    }
}

char* WJProtocolOD::readAreaCfgFile(int p_iGroupID,
                                    int p_iAreaID,
                                    char* p_pcBufResponse,
                                    int& p_iLen)
{
    std::string l_sFilePath = c_sAreaDir_ + "/area_cfg/" + std::to_string(p_iGroupID + 1) + "_"
                              + std::to_string(p_iAreaID + 1) + ".csv";
    std::ifstream read_file(l_sFilePath);
    if (!read_file.is_open())
    {
        // 区域文件读取不到时 有可能引发崩溃
        LOGA(
            WERROR, "Group [{}], area [{}], cannot open area file.", p_iGroupID + 1, p_iAreaID + 1);
    }

    std::string line;
    int index = 0;
    int l_iVerNum = 0;
    int l_iVerIdx = 0;
    char* l_acBuf = nullptr;
    try
    {
        while (std::getline(read_file, line))
        {
            std::string cell;
            std::stringstream lineStream(line);  // 字符串流
            std::vector<std::string> lineArray;
            while (std::getline(lineStream, cell, ','))
            {
                lineArray.push_back(cell);
            }
            if (index < 6)
                fillInAreaCfg(index + 1, lineArray, p_pcBufResponse);
            else if (index == 6)
            {
                fillInAreaCfg(index + 1, lineArray, p_pcBufResponse);
                l_iVerNum = atoi(lineArray[1].c_str());  // 顶点数量
            }
            else
            {
                if(!l_acBuf)
                {
                    int l_iByteNum = p_iLen + l_iVerNum * 4 + 4;
                    l_acBuf = new char[l_iByteNum];  // 40+校验 帧尾4字节+顶点数*4字节
                    memcpy(&l_acBuf[0], p_pcBufResponse, 40);
                }
                int xLen = 40 + l_iVerIdx * 4;
                int yLen = 40 + l_iVerIdx * 4 + 2;
                fillStringTo16Bytes(xLen, lineArray[1], l_acBuf);
                fillStringTo16Bytes(yLen, lineArray[2], l_acBuf);

                l_iVerIdx++;  // 顶点个数+1
                p_iLen += 4;
            }
            index++;
        }
        return l_acBuf;
    }
    catch (const std::exception& e)
    {
        std::cout << "readAreaCfgError" << std::endl;
        std::cerr << e.what() << '\n';
    }
    return l_acBuf;
}

void WJProtocolOD::sendQuerySuccCMD_(std::vector<bool> p_vbAreaExistFlag,
                                     char* p_pcBufResponse,
                                     int p_iLen,
                                     int p_iRes)
{
    int l_iAreaNum = c_iAreaNum_;
    int index = 0;
    int l_iGroupID = c_vAreaIdNum_[0];

    char* l_acBuf = new char[p_iLen + 14];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);

    for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
    {
        if (p_vbAreaExistFlag[i])
        {
            int l_iLen = p_iLen;
            l_acBuf[l_iLen++] = c_vAreaIdNum_[0] & 0xFF;  // 区域主ID
            l_acBuf[l_iLen++] = c_vAreaIdNum_[1] & 0xFF;  // 区域副ID
            l_iLen++;
            l_acBuf[36] = l_iAreaNum & 0xFF;  // 本区域总包数
            l_acBuf[37] = index & 0xFF;
            l_acBuf[l_iLen++] = p_iRes;  // 从第29位开始，0代表失败， 1代表成功

            char* l_acNewBuf;
            l_iLen = 40;
            l_acNewBuf = readAreaCfgFile(l_iGroupID, i, l_acBuf, l_iLen);
            calcWJProtocolTail_(l_acNewBuf, l_iLen);
            sendCallback_(l_acNewBuf, l_iLen);
            index++;
            delete[] l_acNewBuf;
        }
    }
    delete l_acBuf;
}

void WJProtocolOD::sendQueryFailedCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes)
{
    int l_iGroupID = c_vAreaIdNum_[0];

    char* l_acBuf = new char[p_iLen + 10];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);

    int l_iLen = p_iLen;
    l_acBuf[l_iLen++] = (c_vAreaIdNum_[0] & 0xFF00) >> 8;  // 区域主ID
    l_acBuf[l_iLen++] = (c_vAreaIdNum_[1] & 0xFF00) >> 8;  // 区域副ID
    l_iLen++;
    l_acBuf[l_iLen++] = p_iRes;  // 从第29位开始，0代表失败， 1代表成功

    fillAskField(l_acBuf, l_iLen, 30);
    calcWJProtocolTail_(l_acBuf, l_iLen);
    sendCallback_(l_acBuf, l_iLen);

    delete l_acBuf;
}

void WJProtocolOD::sendChangeSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes)
{
    // 多扩10个字节 + 27 28 29 | 校验2 帧尾2 +末尾\0
    char* l_acBuf = new char[p_iLen + 10];
    memcpy(&l_acBuf[0], p_pcBufResponse, p_iLen);

    int l_iLen = p_iLen;
    l_acBuf[l_iLen++] = (c_vAreaIdNum_[0] & 0xFF00) >> 8;  // 区域主ID
    l_acBuf[l_iLen++] = (c_vAreaIdNum_[1] & 0xFF00) >> 8;  // 区域副ID
    l_iLen++;
    l_acBuf[l_iLen++] = p_iRes;  // 从第29位开始，0代表失败， 1代表成功
    fillAskField(l_acBuf, l_iLen, 30);
    calcWJProtocolTail_(l_acBuf, l_iLen);
    sendCallback_(l_acBuf, l_iLen);
    delete l_acBuf;
}

void WJProtocolOD::saveNewArea(int p_iGroupID, int p_iAreaID, int p_iAreaNumID)
{
    std::ofstream g_out_file;
    std::string l_sOutFilePath = c_sAreaDir_ + "/area_cfg/" + std::to_string(p_iGroupID + 1) + "_"
                                 + std::to_string(p_iAreaID + 1) + ".csv";
    LOGA(WINFO, "save group [{}] area [{}] csv!", p_iGroupID + 1, p_iAreaID + 1);

    int l_iLen = 28;
    u_char* l_sNewAreaData = c_sAreaCfg_[p_iAreaNumID].areaData;

    int minHeight = intFrom16Bytes(l_iLen, l_sNewAreaData);  // 区域防护高度，28 29
    int maxHeight = intFrom16Bytes(l_iLen, l_sNewAreaData);  // 区域防护高度，30 31
    int resFrame = l_sNewAreaData[l_iLen++] & 0xFF;  // 32，区域响应时间，范围：1-16
    int shape = l_sNewAreaData[l_iLen] & 0xFF;  // 区域形状 0:多边形 1:圆形 2:扇形 3:长方形 33
    l_iLen += 3;                                // 移到36位
    int vertexNum = intFrom16Bytes(l_iLen, l_sNewAreaData);  // 本区域总点数，36 37

    g_out_file.open(l_sOutFilePath, std::ios::out);
    g_out_file << "groupID," << p_iGroupID << std::endl;
    g_out_file << "areaID," << p_iAreaID << std::endl;
    g_out_file << "shape," << shape << std::endl;
    g_out_file << "minHeight," << minHeight << std::endl;
    g_out_file << "maxHeight," << maxHeight << std::endl;
    g_out_file << "resFrame," << resFrame << std::endl;
    g_out_file << "vertex_num," << vertexNum << std::endl;

    l_iLen = 38;
    int curPkgVerNum = vertexNum;
    for (int j = 0; j < curPkgVerNum; j++)
    {
        int index = l_iLen + j * 4;                     // xy的值各占2字节，共4个字节
        int x = intFrom16Bytes(index, l_sNewAreaData);  // x坐标 单位 毫米
        int y = intFrom16Bytes(index, l_sNewAreaData);  // y坐标 单位 毫米
        std::string vertexID = "vertex" + std::to_string(j);
        g_out_file << vertexID << "," << x << "," << y << std::endl;
    }
    g_out_file.close();
}

void WJProtocolOD::analysisRecieveAreaInfo(u_char* p_pcBufCMD)
{
    int l_iAreaPkgCnt = p_pcBufCMD[34] & 0xFF;  // 本区域总包数
    int l_iPkgSeqNum = p_pcBufCMD[35] & 0xFF;   // 当前包数，第一包下标为1

    int l_iAreaGroupID = p_pcBufCMD[26] & 0xFF;  // 区域主ID
    int l_iAreaID = p_pcBufCMD[27] & 0xFF;       // 区域副ID

    c_vAreaIdNum_[0] = l_iAreaGroupID;

    int l_iAreaNumID = l_iAreaGroupID * EVERY_GROUP_AREA_NUM + l_iAreaID;

    wjPrint(WJCOL_GREEN, "AreaPkgCnt", l_iAreaPkgCnt);
    wjPrint(WJCOL_GREEN, "PkgSeqNum", l_iPkgSeqNum);

    c_sAreaCfg_[l_iAreaNumID].areaData = p_pcBufCMD;
    saveNewArea(l_iAreaGroupID, l_iAreaID, l_iAreaNumID);  // 收发完之后将当前区域信息存入yaml文件

    // 当前包数==总包数，收发完比后才触发coord类解算该组区域
    if (l_iAreaPkgCnt == l_iPkgSeqNum)
    {
        // 如果一组区域没有发全，比如只发了保护区，则删除警告区1 、警告区2的旧文件
        if (l_iAreaPkgCnt < EVERY_GROUP_AREA_NUM)
            deleteOldAreaCfg(l_iAreaGroupID);
        c_sAreaCfg_.clear();
        // std::cout << "c_sAreaCfg_: " << c_sAreaCfg_.size() << std::endl;
        c_actionCallback_(0, c_vAreaIdNum_);
    }
}

void WJProtocolOD::deleteOldAreaCfg(int p_iGroupID)
{
    for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
    {
        int l_iAreaNumID = p_iGroupID * EVERY_GROUP_AREA_NUM + i;
        if (c_sAreaCfg_.find(l_iAreaNumID) == c_sAreaCfg_.end())
        {
            if (!isAreaExists(p_iGroupID, i))
                continue;

            std::string l_sAreaCfgPath = c_sAreaDir_ + "/area_cfg/" + std::to_string(p_iGroupID + 1)
                                         + "_" + std::to_string(i + 1) + ".csv";
            if (deleteFileOrDir(l_sAreaCfgPath))
                LOGA(WINFO, "Delete old group [{}] area [{}] csv", p_iGroupID + 1, i + 1);
        }
    }
}

bool WJProtocolOD::checkAreaCfgFile(u_char* p_pcBufCMD, std::vector<bool>& p_vbAreaExistFlag)
{
    c_vAreaIdNum_[0] = p_pcBufCMD[26] & 0xFF;  // 区域主ID
    c_vAreaIdNum_[1] = p_pcBufCMD[27] & 0xFF;  // 区域副ID

    // 如果该区域存在 则对其进行检测/切换区域
    c_iAreaNum_ = 0;
    for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
    {
        if (isAreaExists(c_vAreaIdNum_[0], i))
        {
            c_iAreaNum_++;
            p_vbAreaExistFlag[i] = true;  // 如果该文件存在 置为true
        }
    }

    if (c_iAreaNum_ > 0)
    {
        c_vAreaIdNum_[2] = 1;  // 用于切换的标志
        return true;
    }
    else
    {
        LOGA(WINFO, "Don's find any group [{}] csv", c_vAreaIdNum_[0] + 1);
        return false;
    }
}

bool WJProtocolOD::isAreaExists(int p_iGroupID, int p_iAreaID)
{
    std::string l_sOutFilePath = c_sAreaDir_ + "/area_cfg/" + std::to_string(p_iGroupID + 1) + "_"
                                 + std::to_string(p_iAreaID + 1) + ".csv";

    // 该文件是否存在
    if (access(l_sOutFilePath.c_str(), F_OK) == 0)
    {
        // printf("%s\n", l_sOutFilePath.c_str());
        return true;
    }
    else
        printf("该区域没有对应的文件可读取\n");
    return false;
}

int WJProtocolOD::selectSlaverProtocol(u_char* p_pcBufCMD, char* p_pcBufResponse)
{
    int l_iSta = 11;
    int l_iSubscript = 26;  // 有效内容起始位
    int l_iFatherCMDID;     // 主命令号
    int l_iSonCMDID;        // 子命令号
    int l_iLen = 0;

    l_iSonCMDID = p_pcBufCMD[l_iSta + 12];  // 第23位为子命令号
    // 拷贝数组0-25 l_iLen从26开始
    calcWJProtocolHead_(p_pcBufCMD, p_pcBufResponse, l_iLen);
    switch (l_iSonCMDID)
    {
        // 上位机发送区域信息
        case SLACMDID::SENDCMD_AREA: {
            LOGA(WINFO, "SENDCMD_AREA");
            p_pcBufResponse[l_iLen++] = p_pcBufCMD[l_iSubscript];
            sendRecvSuccCMD_(p_pcBufResponse, l_iLen, 1);
            analysisRecieveAreaInfo(p_pcBufCMD);
            return 0;
        }

        // 上位机查询区域信息
        case SLACMDID::QUERYCMD_AREA: {
            LOGA(WINFO, "QUERYCMD_AREA");
            std::vector<bool> l_vbAreaExistFlag(3, false);
            if (checkAreaCfgFile(p_pcBufCMD, l_vbAreaExistFlag))
                sendQuerySuccCMD_(l_vbAreaExistFlag, p_pcBufResponse, l_iLen, 1);
            else
                sendQueryFailedCMD_(p_pcBufResponse, l_iLen, 0);
            return 0;
        }

        // 上位机设置/切换区域
        case SLACMDID::SETCMD_AREA: {
            LOGA(WINFO, "SETCMD_AREA");
            std::vector<bool> l_vbAreaExistFlag(3, false);
            if (checkAreaCfgFile(p_pcBufCMD, l_vbAreaExistFlag))
            {
                c_actionCallback_(2, c_vAreaIdNum_);
                sendChangeSuccCMD_(p_pcBufResponse, l_iLen, 1);
            }
            else
                sendChangeSuccCMD_(p_pcBufResponse, l_iLen, 0);
            return 0;
        }

        default: {
            LOGA(WERROR,
                 "SLAM协议异常: 未定义 {:#X} - {:#X}, 请检查!",
                 p_pcBufCMD[l_iSta + 11],
                 p_pcBufCMD[l_iSta + 12]);
            break;
        }
    }
    if (l_iLen > 26)
    {
        fillAskField(p_pcBufResponse, l_iLen, 28);
        calcWJProtocolTail_(p_pcBufResponse, l_iLen);
        return l_iLen;
    }
    return 0;
}
