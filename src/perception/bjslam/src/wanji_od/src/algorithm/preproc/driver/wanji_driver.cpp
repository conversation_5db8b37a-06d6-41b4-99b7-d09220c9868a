/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-07-05 20:25:51
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 18:48:36
 */

#include "algorithm/preproc/driver/wanji_driver.h"
#include <thread>

namespace wanji_driverOD {
wanjiDriver::wanjiDriver(
    uint32_t p_uiLidarID,
    boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATAS>&, int, bool)> p_packCB_)
    : c_uiLidarID(p_uiLidarID), c_driverCB_(p_packCB_)
{
    c_stSysParam_ = wj_slam::SYSPARAMOD::getIn();
    c_sLaserType_ = c_stSysParam_->m_lidar[c_uiLidarID].m_dev.m_sDevType;
    start();
}

wanjiDriver::~wanjiDriver()
{
    printf("exit driver[%d] \n", c_uiLidarID);
}

void wanjiDriver::start()
{
    // 是否某一种WLR720
    std::string l_sLaser = c_sLaserType_.substr(0, 6);
    if (l_sLaser == "WLR720")
    {
        msop_input_.reset(new wanji_driverOD::Input720(
            c_uiLidarID, boost::bind(&wanjiDriver::scanDataProcessCb, this, _1)));
        // 启动基本参数查询
        std::thread l_getPrarmThread(&wanji_driverOD::Input::requestBaseParam, msop_input_);
        l_getPrarmThread.join();

        // 如果查询成功
        if (!(msop_input_->isStart()))
        {
            msop_input_ = nullptr;
            c_stSysParam_->m_fae.setErrorCode("C8");
            LOGA(WERROR,
                 "雷达 [{}] 在线初始化失败 | 网络情况异常，请检查雷达是否正确连接！",
                 c_stSysParam_->m_lidar[c_uiLidarID].m_sLaserName);
        }
    }
    else
    {
        // c_stSysParam_->m_fae.setErrorCode("C10");
        LOGA(WERROR,
             "雷达 [{}] 在线初始化失败 |基础类型异常，请联系万集开发人员进行检查!",
             c_stSysParam_->m_lidar[c_uiLidarID].m_sLaserName);
    }
}

// 扫描数据回调函数，用于处理从雷达中接收的一圈数据
void wanjiDriver::scanDataProcessCb(ScanBuffer scanbuffer)
{
    if (c_iLastCycleNum == -1)
        c_iLastCycleNum = scanbuffer.m_iCircleNum;  // 记录起始圈号，用以记录是否丢圈

    uint16_t l_uiCycleDiff = uint16_t(scanbuffer.m_iCircleNum) - c_iLastCycleNum;
    if (l_uiCycleDiff > 1)
    {
        LOGA(WERROR,
             "{} 雷达 [{}] 数据异常 | 丢{}圈 | Last圈号{} -> Now圈号{} 请检查网络连接!",
             WJODLog::getWholeSysTime(),
             c_stSysParam_->m_lidar[c_uiLidarID].m_sLaserName,
             l_uiCycleDiff,
             c_iLastCycleNum,
             scanbuffer.m_iCircleNum);
    }
    c_uiCurID += l_uiCycleDiff;

    boost::shared_ptr<s_LIDAR_RAW_DATAS> scandata = nullptr;
    if (!scanbuffer.m_bIsFull)
    {
        // 本圈数据中存在丢包
        LOGA(WWARN,
             "{} 雷达 [{}] 丢包 | 圈号 {}",
             WJODLog::getWholeSysTime(),
             c_stSysParam_->m_lidar[c_uiLidarID].m_sLaserName,
             scanbuffer.m_iCircleNum);
        scandata.reset(new s_LIDAR_RAW_DATAS());
        scandata->m_vPackets.reserve(c_stSysParam_->m_lidar[c_uiLidarID].m_uiFramePkgNum);
        for (size_t i = 0; i < scanbuffer.m_data.m_vPackets.size(); i++)
        {
            if (scanbuffer.m_data.m_vPackets[i].hasData())
                scandata->m_vPackets.emplace_back(scanbuffer.m_data.m_vPackets[i]);
        }
    }
    else
    {
        scandata.reset(new s_LIDAR_RAW_DATAS(scanbuffer.m_data));
    }

    LOGA(WTRACE,
         "{} 雷达[{}]数据 | 圈号 {} 包数 {} curID {}",
         WJODLog::getWholeSysTime(),
         c_stSysParam_->m_lidar[c_uiLidarID].m_sLaserName,
         scanbuffer.m_iCircleNum,
         scandata->m_vPackets.size(),
         c_uiCurID);

    c_driverCB_(c_uiLidarID, scandata, c_uiCurID, (l_uiCycleDiff > 1));
    c_iLastCycleNum = scanbuffer.m_iCircleNum;
}

}  // namespace wanji_driverOD
