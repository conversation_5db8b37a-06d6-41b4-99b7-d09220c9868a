/*
 * @Description:
 * @Version: 1.0
 * @Autor: senlin
 * @Date: 2022-08-16 16:48:28
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 08:30:47
 * @FilePath: /src/wanji_od/src/algorithm/preproc/RoiDetect/RoiDetect.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "algorithm/roi_detect/roiDetect.h"

namespace wj_od {

RoiDetect::RoiDetect(std::queue<areaPcPtr>& p_allAreaCurPC,
                     areaPcPtr& p_allAreaLastPC,
                     boolVector& p_multiFrameSwitch,
                     std::mutex& p_roiLock,
                     boost::function<void(int, int, s_AreaPC&)> p_callback,
                     boost::function<void()> p_invalidCallback)
    : c_allAreaLastPc_(p_allAreaLastPC), c_areaMultiFrameSwitch_(p_multiFrameSwitch),
      c_allAreaCurPcQue_(p_allAreaCurPC), c_mutexDetectOpt_(p_roiLock), c_roiOutputCb_(p_callback),
      c_roiInvalidDataCb_(p_invalidCallback)
{
    paramInit();
}

RoiDetect::~RoiDetect()
{
    // c_allAreaLastPc_ = nullptr;
}

void RoiDetect::paramInit()
{
    c_iRowNum_ = 5;
    c_iColNum_ = 5;
    c_iValidNum_ = 2;
    c_iContinousNum_ = 6;  // 后期可以写在文件里配置
    c_iMaxDetectNum_ = 100;
    c_allAreaCurPc_.reset(new allAreaPC());
}

bool RoiDetect::checkNewFrame()
{
    if (c_allAreaCurPcQue_.empty())
    {
        return false;
    }
    return true;
}

bool RoiDetect::isPointExist(int p_iPointNum)
{
    return p_iPointNum >= c_iValidNum_;
}

int RoiDetect::getPointsNum(PcMatrix& p_pcMat)
{
    int l_iPointNum = p_pcMat.sum();
    return l_iPointNum;
}

bool RoiDetect::horizonDetect(int p_iRowNum)
{
    return p_iRowNum >= c_iRowNum_;
}

bool RoiDetect::verticalDetect(int p_iColNum)
{
    return p_iColNum >= c_iColNum_;
}

bool RoiDetect::twoRowThreeColDetect(int row, int col, PcMatrix& p_pcMat)
{
    Eigen::Matrix<int, 2, 3> pcBlock = p_pcMat.block<2, 3>(row - 1, col - 2);
    int l_iPointNum = pcBlock.sum();
    return l_iPointNum >= c_iContinousNum_;
}

bool RoiDetect::threeRowTwoColDetect(int row, int col, PcMatrix& p_pcMat)
{
    Eigen::Matrix<int, 3, 2> pcBlock = p_pcMat.block<3, 2>(row - 2, col - 1);
    int l_iPointNum = pcBlock.sum();
    return l_iPointNum >= c_iContinousNum_;
}

// 后期的矩阵会缩小一点，暂时是16*1800的矩阵
bool RoiDetect::isLargeObstacle(PcMatrix& p_pcMat)
{
    int l_iColMaxNum = 0;
    std::vector<int> l_viColNum(HORIZON_SCAN_NUM, 0);

    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        int l_iRowNum = 0;
        for (size_t j = 0; j < HORIZON_SCAN_NUM; j++)
        {
            if (p_pcMat(i, j) == 1)
            {
                l_iRowNum++;
                l_viColNum[j]++;
                l_iColMaxNum = fmax(l_iColMaxNum, l_viColNum[j]);
            }
            else  // 当前刻度没有点，归0
            {
                l_iRowNum = 0;
                l_viColNum[j] = 0;
            }

            if (j >= 4 && horizonDetect(l_iRowNum))
                return true;

            if (i >= 4 && verticalDetect(l_iColMaxNum))
                return true;

            if (i > 1 && j > 2 && twoRowThreeColDetect(i, j, p_pcMat))
                return true;

            if (i > 2 && j > 1 && threeRowTwoColDetect(i, j, p_pcMat))
                return true;
        }
    }
    return false;
}

bool RoiDetect::twoFrameAnalysis(PcMatrix& p_pcLastMat, PcMatrix& p_pcCurMat)
{
    Eigen::Map<Eigen::RowVectorXi> pcCurVector(p_pcCurMat.data(), p_pcCurMat.size());
    Eigen::Map<Eigen::RowVectorXi> pcLastVector(p_pcLastMat.data(), p_pcLastMat.size());

    // 两帧障碍点按位与 有重叠点时dotResult大于0 没有则等于0
    int dotResult = pcCurVector.dot(pcLastVector);
    return dotResult > 0;
}

void RoiDetect::singleAreaDetect(int p_iAreaID, s_AreaPC& p_pcCurArea)
{
    PcMatrix l_pcCurMat = *p_pcCurArea.pcMat;
    int l_iPointNum = getPointsNum(l_pcCurMat);  // 计算当帧矩阵障碍点的数量
    
    // wjPrint(WJCOL_GREEN, "obstacle points", l_iPointNum);
    // 如果少于有效点数，输出“没有点”
    if (!isPointExist(l_iPointNum))
        c_roiOutputCb_(p_iAreaID, 0, p_pcCurArea);

    // 大于等于100个点 直接报警
    else if (l_iPointNum >= c_iMaxDetectNum_)
        c_roiOutputCb_(p_iAreaID, 1, p_pcCurArea);

    // 如果总共没有5个点，说明也不可能存在连续的5个点
    else if (l_iPointNum >= c_iRowNum_ && isLargeObstacle(l_pcCurMat))
        c_roiOutputCb_(p_iAreaID, 1, p_pcCurArea);

    else
    {
        if (c_areaMultiFrameSwitch_[p_iAreaID])  // 如果多帧检测已开启，则比较前后两帧
        {
            pcMatPtr l_pcLastMatPtr = (*c_allAreaLastPc_)[p_iAreaID].pcMat;
            if (twoFrameAnalysis(l_pcCurMat, *l_pcLastMatPtr))  // 如果前后两帧有重叠
                c_roiOutputCb_(p_iAreaID, 2, p_pcCurArea);
            else
                c_roiOutputCb_(
                    p_iAreaID, 0, p_pcCurArea);  // 如果前后两帧没有重叠点，则返回结果“没有点”
        }
        else
            c_roiOutputCb_(p_iAreaID, 2, p_pcCurArea);
    }
}

void RoiDetect::allAreaDetect(areaPcPtr p_allAreaCurPc)
{
    int l_iAreaNum = p_allAreaCurPc->size();
    for (int i = 0; i < l_iAreaNum; i++)
    {
        if (c_bStopRoiDetect_)
            return;
        singleAreaDetect(i, p_allAreaCurPc->at(i));
    }
}

void RoiDetect::shutDown()
{
    // LOGA(WINFO, "RoiDetect shutdown...\n");
    c_bShutDown_ = true;
    while (1)
    {
        if (c_bShutDownOver_)
            break;
        usleep(1000);
    }
    // LOGA(WINFO, "RoiDetect shutdown Succ!\n");
}

void RoiDetect::run()
{
    c_bRoiInit_ = false;
    c_bShutDown_ = false;
    c_bShutDownOver_ = false;

    int l_iWarnNum = 0;
    TicToc l_tic;
    static float l_fLastFrameTime_ = l_tic.toc();
    while (1)
    {
        if (!c_bShutDown_ && !c_bStopRoiDetect_ && checkNewFrame())
        {
            if (!c_bRoiInit_)
            {
                LOGA(WINFO, "检测模块初始化完成");
                c_bRoiInit_ = true;
            }

            c_bDetectOver_ = false;
            c_allAreaCurPc_ = getAllAreaPC();
            // if (c_allAreaCurPc_->size())
            // {
            //     wjPrint(WJCOL_GREEN, "OD CurID", c_allAreaCurPc_->at(0).scanFrame);
            // }

            allAreaDetect(c_allAreaCurPc_);
            c_bDetectOver_ = true;
            l_fLastFrameTime_ = l_tic.toc();
            l_iWarnNum = 0;
        }

        else
        {
            if (!c_bShutDown_ && l_tic.toc() - l_fLastFrameTime_ > 500)
            {
                if (l_iWarnNum % 1000 == 0)  // 1s钟发送一次
                {
                    LOGA(WWARN,
                     "避障异常,雷达数据超过 {:.3} s未收到,请检查!",
                     (l_tic.toc() - l_fLastFrameTime_) * 1e-3);
                    c_roiInvalidDataCb_();
                    l_iWarnNum++;
                }
            }
        }

        if (c_bShutDown_)
            break;
        else
            usleep(1000);
    }
    c_bShutDownOver_ = true;
}

}  // namespace wj_od
