# comm_tcp: transfer vanjee pose (from TCP) to localization msg (in ROS)
=================================================

## 1.build
```
cd ~/catkin_tcp
catkin_make
```

## 2.run
```
source devel/setup.bash
roslaunch comm_tcp client.launch 
```
Params in client.launch file are  adaptable
```
[ip] 
# TCP server ip

[port] 
# TCP server port

[request_delay]
# 0 or 1;
# 0 means request pose data with no delay, if the calculation of cur frame pose is not finished, TCP server replies the estimated pose
# 1 means that TCP server replies pose data after calculation is down

[request_interval] 
# time interval (us) between two requests 

[pose_topic]
# where the localization msgs were published
```

## 3.illustrarion
This procedure askes TCP server for pose messages. The replied pose msgs are transerred to localiztion.msg format and pulished on `pose_topic`.
