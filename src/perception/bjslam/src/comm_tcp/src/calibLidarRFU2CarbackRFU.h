/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-02-28 16:41:36
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-03-07 11:11:26
 * @FilePath     : calibLidarRFU2CarbackRFU.h
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-02-28 16:41:36
 */

#ifndef _CALIBLIDARRFU2CARBACKRFU_H_
#define _CALIBLIDARRFU2CARBACKRFU_H_
#include <iostream>
#include <vector>

#include <ceres/ceres.h>
#include <ceres/rotation.h>
#include "../../../../commonlibrary/src/coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h"



// 定义残差的结构体，表示 Lidar 和 GPS 的 UTM 坐标差值
struct LidarGpsResidual{
    LidarGpsResidual(const Eigen::Vector3d& lidarRFU,
        const Eigen::Vector3d& curGPSUTM, 
        const Eigen::Vector3d& curGPSRPY, 
        const std::vector<double>& vStartingPoint_UTM,
        const Eigen::Vector3d& startingPoint_EulerDegrees,
        SensorAxisTransformation* psSensorAxisTransformation)
            :c_lidarRFU(lidarRFU),c_curGPSUTM(curGPSUTM),c_curGPSRPY(curGPSRPY),
            c_vStartingPoint_UTM(vStartingPoint_UTM),
            c_startingPoint_EulerDegrees(startingPoint_EulerDegrees),
            c_psSensorAxisTransformation(psSensorAxisTransformation)
        {}
    

    template <typename T>
    bool operator()(const T *q, const T *t, T* residual) const{
        // cout <<"hsq: residual lidar2Carback.x = " << lidar2Carback[0] << ", lidar2Carback.y = " << lidar2Carback[1] << endl;
        // 坐标转换
        Eigen::Quaternion<T> q_calibration{q[3], q[0], q[1], q[2]};
        q_calibration.normalize();
		Eigen::Matrix<T, 3, 1> t_calibration{t[0], t[1], t[2]};
		Eigen::Matrix<T, 3, 1> cp{T(c_lidarRFU[0]), T(c_lidarRFU[1]), T(c_lidarRFU[2])};
		Eigen::Matrix<T, 3, 1> ENUPosition;
		ENUPosition = q_calibration * cp + t_calibration;

        // cout <<"hsq: residual pointInCarBackRFU.x = " << ENUPosition[0] << ", pointInCarBackRFU.y = " << ENUPosition[1] << endl;
        // ENU坐标转到UTM坐标
        // std::vector<double> l_centerPointUTM = c_psSensorAxisTransformation->ENU2UTM(ENUPosition);  
        Eigen::Matrix<T, 3, 3> rotationZ2;
        rotationZ2 << T(cos(T(c_startingPoint_EulerDegrees[2] * M_PI / 180.0))), T(-sin(T(c_startingPoint_EulerDegrees[2] * M_PI / 180.0))), T(0),
                    T(sin(T(c_startingPoint_EulerDegrees[2] * M_PI / 180.0))), T(cos(T(c_startingPoint_EulerDegrees[2] * M_PI / 180.0))), T(0),
                    T(0),T(0),T(1);
        Eigen::Matrix<T, 3, 3> rotationY2;
        rotationY2 << T(cos(T(c_startingPoint_EulerDegrees[1] * M_PI / 180.0))), T(0), T(sin(T(c_startingPoint_EulerDegrees[1] * M_PI / 180.0))),
                        T(0), T(1), T(0),
                        T(-sin(T(c_startingPoint_EulerDegrees[1] * M_PI / 180.0))),T(0),T(cos(T(c_startingPoint_EulerDegrees[1] * M_PI / 180.0)));

        Eigen::Matrix<T, 3, 3> rotationX2;
        rotationX2 << T(1),T(0),T(0),
                    T(0),T(cos(T(c_startingPoint_EulerDegrees[0] * M_PI / 180.0))), T(-sin(T(c_startingPoint_EulerDegrees[0] * M_PI / 180.0))),
                    T(0),T(sin(T(c_startingPoint_EulerDegrees[0] * M_PI / 180.0))), T(cos(T(c_startingPoint_EulerDegrees[0] * M_PI / 180.0)));

        Eigen::Matrix<T, 3, 3> rotationFrom2 = rotationX2 * rotationY2 * rotationZ2;
        Eigen::Quaternion<T> quationCarbackRFU2UTM = Eigen::Quaternion<T>(rotationFrom2);
        quationCarbackRFU2UTM.normalize();
        Eigen::Matrix<T, 3, 1> translation2{T(c_vStartingPoint_UTM[0]),T(c_vStartingPoint_UTM[1]),T(0)};
        Eigen::Matrix<T, 3, 1> l_centerPointUTM = quationCarbackRFU2UTM * ENUPosition + translation2;                   
        // cout <<"hsq: residual l_centerPointUTM.x = " << l_centerPointUTM[0] << ", l_centerPointUTM.y = " << l_centerPointUTM[1] << endl;
        
        // 计算残差
        residual[0] = T(l_centerPointUTM[0]) - T(c_curGPSUTM[0]);
        residual[1] = T(l_centerPointUTM[1]) - T(c_curGPSUTM[1]);
        residual[2] = T(l_centerPointUTM[2]) - T(c_curGPSUTM[2]);
        // 使用ceres::AngleDistance计算两个角度之间的残差
        Eigen::AngleAxisd rotationVectorx = Eigen::AngleAxisd(c_curGPSRPY[0] * M_PI / 180.0, Eigen::Vector3d::UnitX());
        Eigen::AngleAxisd rotationVectory = Eigen::AngleAxisd(c_curGPSRPY[1] * M_PI / 180.0, Eigen::Vector3d::UnitY());
        Eigen::AngleAxisd rotationVectorz = Eigen::AngleAxisd(c_curGPSRPY[2] * M_PI / 180.0, Eigen::Vector3d::UnitZ());
        Eigen::Quaterniond rotationQuat = Eigen::Quaterniond(rotationVectorx * rotationVectory * rotationVectorz);
        rotationQuat.normalize();
        residual[3] = q[0] - T(rotationQuat.x());
        residual[4] = q[1] - T(rotationQuat.y());
        residual[5] = q[2] - T(rotationQuat.z());
        residual[6] = q[3] - T(rotationQuat.w());
        return true;
    }

    static ceres::CostFunction *Create(const Eigen::Vector3d& lidarRFU,
        const Eigen::Vector3d& curGPSUTM, 
        const Eigen::Vector3d& curGPSRPY, 
        const std::vector<double>& vStartingPoint_UTM,
        const Eigen::Vector3d& startingPoint_EulerDegrees,
        SensorAxisTransformation* psSensorAxisTransformation){
            return (new ceres::AutoDiffCostFunction<LidarGpsResidual, 7, 4,3>(
                            new LidarGpsResidual(lidarRFU, curGPSUTM, curGPSRPY, vStartingPoint_UTM, startingPoint_EulerDegrees, psSensorAxisTransformation)
                        ));
            
        }
    const Eigen::Vector3d c_lidarRFU;
    const Eigen::Vector3d c_curGPSUTM;
    const Eigen::Vector3d c_curGPSRPY;
    const std::vector<double> c_vStartingPoint_UTM;
    const Eigen::Vector3d c_startingPoint_EulerDegrees;
    SensorAxisTransformation* c_psSensorAxisTransformation;
};


#endif // _CALIBLIDARRFU2CARBACKRFU_H_